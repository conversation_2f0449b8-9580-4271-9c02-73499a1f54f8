# WFH Chatbot Error Handling Demo

## Before Fix (Error)
```
[log] [HR_ACTION_EXECUTOR] WFH API response status: 400
[log] [HR_ACTION_EXECUTOR] Error in WFH application: type '_Map<String, dynamic>' is not a subtype of type 'String'
[log] [HR_ACTION_EXECUTOR] WFH API response data: {status: false, message: {non_field_errors: [You've already submitted a WFH request.]}}
```

**Result**: App crashed with type casting error, user saw generic error message.

## After Fix (Proper Handling)

### API Response:
```json
{
  "status": false,
  "message": {
    "non_field_errors": ["You've already submitted a WFH request."]
  }
}
```

### User Experience:
```
User: "I want to work from home tomorrow"
AI: "❌ **WFH Request Issue**

You've already submitted a WFH request.

💡 **What you can do:**
• Check your existing WFH requests
• Contact your HR administrator if needed
• Verify your WFH permissions

🤖 **Try asking me:**
• "What's my leave balance?"
• "Show my leave history"
• "What are the upcoming holidays?""
```

## Error Handling Examples

### 1. Duplicate WFH Request
**API Response:**
```json
{
  "status": false,
  "message": {
    "non_field_errors": ["You've already submitted a WFH request."]
  }
}
```
**User Message:** "You've already submitted a WFH request."

### 2. Simple Error Message
**API Response:**
```json
{
  "status": false,
  "message": "WFH is not enabled for your account"
}
```
**User Message:** "WFH is not enabled for your account"

### 3. Validation Errors
**API Response:**
```json
{
  "status": false,
  "errors": {
    "start_date": ["This field is required."],
    "reason": ["This field cannot be blank."]
  }
}
```
**User Message:** "This field is required., This field cannot be blank."

### 4. Permission Errors
**API Response:**
```json
{
  "status": false,
  "error": "You don't have permission to apply for WFH"
}
```
**User Message:** "You don't have permission to apply for WFH"

### 5. Server Errors
**API Response:**
```json
{
  "status": false
}
```
**User Message:** "WFH request failed. Please try again or contact support."

## Technical Implementation

### `_extractErrorMessage()` Method Logic:
1. **Check if response data exists and is a Map**
2. **Handle nested message structure:**
   - If `message` is a String → return directly
   - If `message` is a Map → extract all error arrays and join them
3. **Handle other error fields:**
   - Check `error` field
   - Check `errors` field (validation errors)
4. **Fallback to generic message if no specific error found**

### Code Structure:
```dart
String _extractErrorMessage(dynamic responseData) {
  // Null/type checks
  if (responseData == null || responseData is! Map<String, dynamic>) {
    return 'Failed to submit WFH request. Please try again.';
  }

  Map<String, dynamic> data = responseData;

  // Handle nested message structure
  if (data.containsKey('message')) {
    var message = data['message'];
    
    if (message is String) {
      return message;
    } else if (message is Map<String, dynamic>) {
      // Extract all error messages from nested structure
      List<String> errorMessages = [];
      message.forEach((key, value) {
        if (value is List) {
          errorMessages.addAll(value.cast<String>());
        } else {
          errorMessages.add(value.toString());
        }
      });
      
      if (errorMessages.isNotEmpty) {
        return errorMessages.join(', ');
      }
    }
  }

  // Handle other error formats...
  // Return fallback message
}
```

## Benefits of the Fix

1. **No More Crashes**: Properly handles all error response formats
2. **User-Friendly Messages**: Clear, actionable error messages
3. **Comprehensive Coverage**: Supports multiple API error formats
4. **Debugging Support**: Detailed logging for developers
5. **Graceful Degradation**: Fallback messages when parsing fails

## Testing Coverage

✅ **Unit Tests Added:**
- Nested error structure parsing
- Various error response formats
- Authentication error handling
- Parameter validation

✅ **Integration Tests:**
- End-to-end error handling
- User experience validation
- Error message formatting

The WFH chatbot now handles all known error formats gracefully and provides users with clear, actionable feedback instead of crashing.
