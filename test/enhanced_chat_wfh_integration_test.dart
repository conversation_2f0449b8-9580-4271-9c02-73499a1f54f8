import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:e8_hr_portal/provider/enhanced_chat_provider.dart';

void main() {
  group('Enhanced Chat WFH Integration Tests', () {
    late EnhancedChatProvider provider;

    setUp(() {
      provider = EnhancedChatProvider();
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({
        'access_token': 'test_token_123',
        'user_id': 'test_user',
      });
    });

    tearDown(() {
      provider.dispose();
    });

    test('should include WFH in quick actions', () {
      List<String> quickActions = provider.getQuickActions();
      
      expect(quickActions.isNotEmpty, true);
      expect(
        quickActions.any((action) => 
          action.toLowerCase().contains('work from home') || 
          action.toLowerCase().contains('wfh')
        ), 
        true,
        reason: 'Quick actions should include work from home option'
      );
    });

    test('should handle WFH quick action selection', () async {
      // Initialize chat first
      await provider.initializeChat();
      
      // Select a WFH quick action
      String wfhAction = "Request work from home for next week";
      
      // This will attempt to send the message through the AI service
      // In a real test environment, we would mock the AI service
      try {
        await provider.selectQuickAction(wfhAction);
        
        // Verify that the message was added to the conversation
        expect(provider.messages.isNotEmpty, true);
        
        // The last user message should be the WFH request
        var userMessages = provider.messages.where((msg) => msg.isUser).toList();
        expect(userMessages.isNotEmpty, true);
        expect(userMessages.last.content, wfhAction);
        
      } catch (e) {
        // Expected to fail due to network calls in test environment
        // But we can verify the message structure
        expect(e.toString(), isNotEmpty);
      }
    });

    test('should handle various WFH message formats', () async {
      await provider.initializeChat();
      
      List<String> wfhMessages = [
        "I want to work from home tomorrow",
        "Apply for WFH next Monday",
        "Request work from home for personal reasons",
        "WFH application for this Friday",
        "Can I work from home next week?",
      ];

      for (String message in wfhMessages) {
        try {
          await provider.sendMessage(message);
          
          // Verify message was added
          var userMessages = provider.messages.where((msg) => msg.isUser).toList();
          expect(userMessages.any((msg) => msg.content == message), true);
          
        } catch (e) {
          // Expected to fail due to network calls
          expect(e.toString(), isNotEmpty);
        }
      }
    });

    test('should maintain conversation context for WFH requests', () async {
      await provider.initializeChat();
      
      // Simulate a conversation about WFH
      try {
        await provider.sendMessage("I need to work from home");
        await provider.sendMessage("Tomorrow would be good");
        await provider.sendMessage("The reason is personal work");
        
        // Verify all messages are in the conversation
        var userMessages = provider.messages.where((msg) => msg.isUser).toList();
        expect(userMessages.length, greaterThanOrEqualTo(3));
        
      } catch (e) {
        // Expected to fail due to network calls
        expect(e.toString(), isNotEmpty);
      }
    });

    test('should handle WFH-related information queries', () async {
      await provider.initializeChat();
      
      List<String> infoQueries = [
        "What is the WFH policy?",
        "How many WFH days do I have?",
        "Check my WFH status",
        "Show my WFH history",
      ];

      for (String query in infoQueries) {
        try {
          await provider.sendMessage(query);
          
          // Verify message was processed
          var userMessages = provider.messages.where((msg) => msg.isUser).toList();
          expect(userMessages.any((msg) => msg.content == query), true);
          
        } catch (e) {
          // Expected to fail due to network calls
          expect(e.toString(), isNotEmpty);
        }
      }
    });

    test('should clear WFH conversation properly', () async {
      await provider.initializeChat();
      
      try {
        await provider.sendMessage("I want to work from home tomorrow");
        
        // Verify message was added
        expect(provider.messages.isNotEmpty, true);
        
        // Clear the chat
        provider.clearChat();
        
        // Verify chat is cleared
        expect(provider.messages.isEmpty, true);
        
      } catch (e) {
        // Clear should work regardless of network errors
        provider.clearChat();
        expect(provider.messages.isEmpty, true);
      }
    });

    test('should handle WFH session management', () async {
      await provider.initializeChat();
      
      // Verify session is initialized
      expect(provider.context, isNotNull);
      
      try {
        await provider.sendMessage("WFH request for tomorrow");
        
        // Session should still be active
        expect(provider.context, isNotNull);
        
      } catch (e) {
        // Session should remain valid even if network calls fail
        expect(provider.context, isNotNull);
      }
    });
  });
}
