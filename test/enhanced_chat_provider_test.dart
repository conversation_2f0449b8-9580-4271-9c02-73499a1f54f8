import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:e8_hr_portal/provider/enhanced_chat_provider.dart';

void main() {
  group('EnhancedChatProvider Greeting Tests', () {
    late EnhancedChatProvider provider;

    setUp(() {
      provider = EnhancedChatProvider();
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({});
    });

    tearDown(() {
      provider.dispose();
    });

    test('should show greeting message on first initialization', () async {
      // Initialize chat for the first time
      await provider.initializeChat();

      // Should have one welcome message
      expect(provider.messages.length, 1);
      expect(provider.messages.first.messageType, ChatMessageType.welcome);
      expect(provider.messages.first.content,
          contains("Hello! I'm your HR assistant"));
    });

    test(
        'should not show greeting message on subsequent initializations same day',
        () async {
      // Initialize chat for the first time
      await provider.initializeChat();
      expect(provider.messages.length, 1);

      // Clear messages and initialize again (simulating reopening the screen)
      provider.clearChat();
      await provider.initializeChat();

      // Should not show greeting message again
      expect(provider.messages.length, 0);
    });

    test('should show greeting message on new day', () async {
      // Mock SharedPreferences with yesterday's date
      String yesterday = DateTime.now()
          .subtract(const Duration(days: 1))
          .toIso8601String()
          .split('T')[0];

      SharedPreferences.setMockInitialValues({
        'last_greeting_date_unknown_user': yesterday,
      });

      // Initialize chat
      await provider.initializeChat();

      // Should show greeting message for new day
      expect(provider.messages.length, 1);
      expect(provider.messages.first.messageType, ChatMessageType.welcome);
    });

    test(
        'should reset greeting status when clearChat is called with resetGreeting=true',
        () async {
      // Initialize chat first time
      await provider.initializeChat();
      expect(provider.messages.length, 1);

      // Clear chat with reset greeting
      provider.clearChat(resetGreeting: true);

      // Initialize again
      await provider.initializeChat();

      // Should show greeting message again
      expect(provider.messages.length, 1);
      expect(provider.messages.first.messageType, ChatMessageType.welcome);
    });

    test('should handle different users separately', () async {
      // Mock SharedPreferences with greeting shown for user1 but not user2
      SharedPreferences.setMockInitialValues({
        'user_id': 'user1',
        'last_greeting_date_user1':
            DateTime.now().toIso8601String().split('T')[0],
      });

      // Initialize for user1
      await provider.initializeChat();
      expect(provider.messages.length, 0); // No greeting for user1

      // Switch to user2
      SharedPreferences.setMockInitialValues({
        'user_id': 'user2',
        'last_greeting_date_user1':
            DateTime.now().toIso8601String().split('T')[0],
      });

      provider.clearChat();
      await provider.initializeChat();
      expect(provider.messages.length, 1); // Greeting for user2
    });

    test('should provide quick actions consistently', () async {
      // Initialize chat
      await provider.initializeChat();

      // Get quick actions
      List<String> actions = provider.getQuickActions();

      // Should have quick actions available
      expect(actions.isNotEmpty, true);
      expect(actions.length, greaterThan(0));

      // Quick actions should include common HR tasks
      expect(actions.any((action) => action.toLowerCase().contains('leave')), true);
      expect(actions.any((action) => action.toLowerCase().contains('work from home')), true);
    });
  });
}
