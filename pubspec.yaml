name: e8_hr_portal
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.2.2+1
environment:
  sdk: ">=3.5.0 <4.0.0"
  flutter: ">=3.32.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  badges: ^3.1.2
  bot_toast: ^4.1.3
  cached_network_image: ^3.4.1
  carousel_slider: ^5.0.0
  cloud_firestore: ^5.6.9
  crypto: ^3.0.6
  cupertino_icons: ^1.0.8
  dotted_border: ^2.1.0
  firebase_auth: ^5.6.0
  firebase_core: ^3.14.0
  flutter_easyloading: ^3.0.5
  firebase_messaging: ^15.2.7
    # flutter_facebook_auth: ^5.0.6
  flutter_linkify: ^6.0.0
  flutter_slidable: ^4.0.0
  flutter_typeahead: ^5.2.0
  fluttertoast: ^8.2.12
  google_fonts: ^6.2.1
  google_sign_in: ^6.2.2
  http: ^1.4.0
  image_cropper: ^9.1.0
  image_picker: ^1.1.2

  provider: ^6.1.2
  path_provider: ^2.1.5
  shared_preferences: ^2.3.3
  # sign_in_with_apple: ^4.2.0
  timeago: ^3.7.0
  # time_picker_widget: ^1.0.0+10
  url_launcher: ^6.3.1
  infinite_scroll_pagination: ^4.1.0
  device_info_plus: ^11.3.0
  pin_code_fields: ^8.0.1
  file_picker: ^8.1.4
  flutter_local_notifications: ^19.3.0
  flutter_downloader: ^1.12.0
  photo_view: ^0.15.0
  flutter_pdfview: ^1.4.0+1
  easy_image_viewer: ^1.5.1
  # fleather: ^1.9.0
  flutter_html: ^3.0.0
  emoji_picker_flutter: ^4.3.0
  video_player: ^2.10.0
  syncfusion_flutter_pdfviewer: ^29.2.11
  chewie: ^1.11.3
  dio: ^5.8.0+1
  permission_handler: ^12.0.0+1
  # video_thumbnail: ^0.5.3
  pull_to_refresh: ^2.0.0
  month_picker_dialog: ^5.1.3
  connectivity_plus: ^6.1.4
  upgrader: ^11.3.0
  firebase_crashlytics: ^4.1.5
  encrypt: ^5.0.3
  app_links: ^6.3.2
  # flutter_share: ^2.0.0
  flutter_svg: ^2.0.14
  flutter_quill: ^11.4.1
  lottie: ^3.1.3
  html: ^0.15.4
  share_plus: ^10.1.2
  file_selector: ^1.0.3
  screenshot: ^3.0.0
  flutter_blue_plus: ^1.34.4
  geolocator: ^13.0.2
  flutter_reactive_ble: ^5.3.1
  sign_in_with_apple: ^6.1.3
  equatable: ^2.0.5
  firebase_analytics: ^11.3.5
  easy_stepper: ^0.8.5+1
  webview_flutter: ^4.9.0
  open_file: ^3.5.9
  camera: ^0.11.0+2
  month_year_picker: ^0.5.0+1
  flutter_localizations:
    sdk: flutter
  package_info_plus: ^8.3.0
  skeletonizer: ^2.0.1
  intl: ^0.20.2
  flutter_secure_storage: ^9.2.2




  

 
 


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.1
  mockito: ^5.4.4


flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/launcher_icon.png"
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/
    - assets/icons/recent/
    - assets/lottie/

  fonts:
    - family: SFProDisplay
      fonts:
        - asset: assets/fonts/SFPRODISPLAYULTRALIGHTITALIC.otf
          weight: 100
        - asset: assets/fonts/SFPRODISPLAYTHINITALIC.otf
          weight: 200
        - asset: assets/fonts/SFPRODISPLAYLIGHTITALIC.otf
          weight: 300
        - asset: assets/fonts/SFPRODISPLAYREGULAR.otf
          weight: 400
        - asset: assets/fonts/SFPRODISPLAYMEDIUM.otf
          weight: 500
        - asset: assets/fonts/SFPRODISPLAYSEMIBOLDITALIC.otf
          weight: 600
        - asset: assets/fonts/SFPRODISPLAYBOLD.otf
          weight: 700
        - asset: assets/fonts/SFPRODISPLAYHEAVYITALIC.otf
          weight: 800
        - asset: assets/fonts/SFPRODISPLAYBLACKITALIC.otf
          weight: 900
