# Enhanced AI Chatbot for HR Portal

## 🎯 Overview

This enhanced AI chatbot integrates your local Qwen-3B-Coder model with your existing HR Portal APIs to provide actionable conversations. Users can perform HR tasks through natural language interactions.

## ✨ Features

### **Actionable Conversations**
- **Leave Management**: Apply for leave, check balance, view history
- **Work From Home**: Request WFH, check status
- **Information Queries**: Holidays, policies, employee info
- **Expense Tracking**: Submit and track expenses (coming soon)
- **Meeting Rooms**: Book rooms, check availability (coming soon)

### **Smart AI Integration**
- Natural language understanding with Qwen-3B-Coder
- Intent classification and parameter extraction
- Context-aware multi-turn conversations
- Confirmation workflows before executing actions

### **Modern UI/UX**
- Beautiful chat interface with message types
- Quick action buttons for common tasks
- Loading states and typing indicators
- Error handling with retry options

## 🚀 Getting Started

### Step 1: Configure AI Settings

1. Open the HR Portal app
2. Navigate to **Menu → AI Assistant**
3. Click **"Setup AI Configuration"**
4. Enter your AI API credentials:
   - **API Token**: Your bearer token for the AI service
   - **Base URL**: `https://ai.nuox.io/v1` (default)
   - **Model**: `qwen-3b-coder` (default)
5. Click **"Test Connection"** to verify
6. Click **"Save Configuration"**

### Step 2: Start Using the AI Assistant

1. From the AI Assistant screen, click **"Start AI Chat"**
2. Try these example conversations:
   - "Hello, how can you help me?"
   - "Apply for sick leave tomorrow"
   - "Check my leave balance"
   - "Request work from home for next week"
   - "Show upcoming holidays"

## 💬 Example Conversations

### **Leave Application**
```
User: "I need to apply for sick leave tomorrow"
AI: "I'll help you apply for sick leave. Let me process your request for tomorrow."
AI: "✅ Your sick leave application for [date] has been submitted successfully!"
```

### **Work From Home Request**
```
User: "Can I work from home next Monday and Tuesday?"
AI: "I'll submit a work from home request for Monday and Tuesday next week."
AI: "✅ Your WFH request has been submitted successfully!"
```

### **Information Query**
```
User: "What's my leave balance?"
AI: "Here's your current leave balance:
• Annual Leave: 15 days
• Sick Leave: 8 days
• Emergency Leave: 3 days"
```

## 🔧 Technical Architecture

### **Core Components**

1. **AIChatbotService** (`lib/services/ai_chatbot_service.dart`)
   - Communicates with Qwen-3B-Coder model
   - Handles intent classification and parameter extraction

2. **HRActionExecutor** (`lib/services/hr_action_executor.dart`)
   - Executes HR actions through existing APIs
   - Validates parameters and handles responses

3. **EnhancedChatProvider** (`lib/provider/enhanced_chat_provider.dart`)
   - Manages conversation state and context
   - Coordinates between AI service and action executor

4. **AIConfigService** (`lib/services/ai_config_service.dart`)
   - Manages AI API configuration and credentials

### **API Integration**

The chatbot integrates with your existing HR Portal APIs:
- **Leave APIs**: `leaveApplyUrl`, `leaveBalanceUrl`, `leaveHistoryUrl`
- **WFH APIs**: `wfhUpdateUrl`, `wfhRequestsListUrl`
- **Info APIs**: `upcomingHolidaysURL`, `holidaysURL`

### **AI Model Communication**

```json
{
  "model": "qwen-3b-coder",
  "messages": [
    {
      "role": "system",
      "content": "You are an AI assistant for an HR Portal system..."
    },
    {
      "role": "user",
      "content": "Apply for sick leave tomorrow"
    }
  ],
  "max_tokens": 500,
  "temperature": 0.7
}
```

## 🛠️ Configuration

### **Environment Setup**

Add your AI API token to the app configuration:

```dart
// Initialize AI configuration
await AIConfigService.initializeConfig(
  apiToken: 'your-ai-api-token',
  baseUrl: 'https://ai.nuox.io/v1',
  model: 'qwen-3b-coder',
);
```

### **Customization**

You can customize the AI system prompt in `AIChatbotService._getSystemPrompt()` to:
- Add new HR actions
- Modify response formats
- Include company-specific information

## 🧪 Testing

### **Quick Tests**

1. Navigate to **AI Assistant → Run Quick Tests**
2. Select a test message
3. Click **"Run Test"** to validate functionality

### **Manual Testing**

Try these conversation patterns:
- **Action requests**: "Apply for...", "Request...", "Submit..."
- **Information queries**: "Check...", "Show...", "What is..."
- **Status checks**: "Status of...", "When is..."

## 🔒 Security

- AI API tokens are stored securely using SharedPreferences
- All HR API calls use existing Bearer token authentication
- No sensitive data is sent to the AI model
- Actions require confirmation before execution

## 🚨 Troubleshooting

### **Common Issues**

1. **"AI configuration is incomplete"**
   - Ensure API token, base URL, and model are configured
   - Test the connection in AI Settings

2. **"Connection test failed"**
   - Verify your AI API token is valid
   - Check network connectivity
   - Ensure the base URL is correct

3. **"Failed to submit leave application"**
   - Check if you're logged into the HR Portal
   - Verify leave types and dates are valid
   - Ensure you have sufficient leave balance

### **Debug Mode**

Enable debug logging to see detailed AI interactions:
```dart
// In main.dart or app initialization
if (kDebugMode) {
  // Debug logs will show AI requests/responses
}
```

## 📱 Menu Integration

The AI Assistant is accessible from the main menu:
**Menu Drawer → AI Assistant**

## 🔄 Future Enhancements

- **Expense Management**: Full expense submission and tracking
- **Meeting Room Booking**: Complete meeting room management
- **Document Requests**: Certificate and document requests
- **Approval Workflows**: Manager approval actions
- **Voice Integration**: Voice-to-text input
- **Multi-language Support**: Support for multiple languages

## 📞 Support

For technical support or feature requests, contact your development team or refer to the HR Portal documentation.

---

**Note**: This AI chatbot enhances your existing HR Portal functionality and requires proper configuration of your Qwen-3B-Coder model to work effectively.
