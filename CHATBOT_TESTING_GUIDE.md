# 🤖 HR Portal AI Chatbot Testing Guide

## 🎯 Current Status
- ✅ **Technical Integration**: Working perfectly
- ✅ **Authentication**: Bearer token working
- ✅ **API Calls**: Successfully reaching server
- ⚠️ **Leave Applications**: Restricted by business rules
- ✅ **Other Functions**: Available for testing

## 🧪 Test Commands

### **✅ Working Functions (Test These)**

#### **1. Leave Balance Inquiry**
```
"What's my leave balance?"
"Show my leave balance"
"How many leave days do I have?"
```
**Expected**: Display current leave balance by type

#### **2. Leave History/Status**
```
"Show my leave history"
"What's my leave status?"
"Check my leave requests"
```
**Expected**: Display recent leave applications and their status

#### **3. Holiday Information**
```
"What are the upcoming holidays?"
"Show holidays"
"When is the next holiday?"
```
**Expected**: Display upcoming company holidays

#### **4. Work From Home Requests (FIXED)**
```
"I want to work from home tomorrow"
"Apply for WFH next Monday"
"Request work from home for next week"
"Temporary WFH for tomorrow"
"Regular work from home"
```
**Expected**: Submit WFH request with correct API format (Temporary WFH by default)

#### **5. Leave Types & Staff Information (NEW)**
```
"Show available leave types"
"What leave types can I choose?"
"Show staff list"
"Who can I select as staff in charge?"
```
**Expected**: Display available leave types and staff members with IDs

#### **6. Leave Diagnostic (NEW)**
```
"Check my leave settings"
"Why can't I apply for leave?"
"Leave diagnostic"
```
**Expected**: Show diagnostic information about leave eligibility

### **🔄 Guided Leave Applications (NEW FLOW)**

#### **Step 1: Initial Request**
```
"Apply for sick leave tomorrow"
"I need annual leave next week"
```
**Expected**: Guided setup message asking for leave type, day type, and staff in charge

#### **Step 2: Get Required Information**
```
"Show available leave types"
"Show staff list"
```
**Expected**: Display available options with IDs

#### **Step 3: Complete Application (Future Enhancement)**
```
"Apply sick leave with John as staff in charge for full day"
"Annual leave, half day, Sarah in charge"
```
**Expected**: Process complete application with all required fields

### **⚠️ Current Limitation**
- Leave applications may still be restricted by server-side business rules
- Users will get guided setup but final submission depends on admin configuration

## 🔍 Debug Information

### **What to Look For in Logs**
1. **Successful API Calls**:
   ```
   [HR_ACTION_EXECUTOR] ========== CHATBOT LEAVE APPLICATION DEBUG ==========
   [HR_ACTION_EXECUTOR] TYPE CASTING FIX: Successfully converted int to string
   ```

2. **Business Logic Restrictions**:
   ```
   [HR_ACTION_EXECUTOR] BUSINESS LOGIC RESTRICTION: Leave application blocked by server
   [HR_ACTION_EXECUTOR] This is not a technical error - user needs admin assistance
   ```

3. **Authentication Status**:
   ```
   [HR_ACTION_EXECUTOR] Access token available: true
   [HR_ACTION_EXECUTOR] User ID available: true
   ```

## 🛠️ Troubleshooting

### **If Chatbot Doesn't Respond**
1. Check internet connection
2. Verify AI service is running at https://ai.nuox.io/v1
3. Check bearer token configuration

### **If API Calls Fail**
1. Check authentication logs
2. Verify user is logged in
3. Check server connectivity

### **If Leave Applications Fail**
- This is expected due to business rules
- Contact HR administrator for leave settings configuration

## 📞 Next Steps for Leave Applications

### **For HR Administrators**
1. **Check User Profile**:
   - Verify employee profile is complete
   - Ensure leave settings are configured
   - Check leave balance allocation

2. **Review Leave Policies**:
   - Confirm user meets eligibility criteria
   - Check probation period restrictions
   - Verify leave type permissions

3. **System Configuration**:
   - Update leave settings in admin panel
   - Configure leave approval workflows
   - Set up leave balance allocations

### **For Users**
1. **Contact HR Team**:
   - Request leave settings configuration
   - Verify employee profile completion
   - Ask about leave eligibility requirements

2. **Use Alternative Functions**:
   - Check leave balance
   - View leave history
   - Apply for work from home
   - Check upcoming holidays

## 🎯 Success Criteria

### **Technical Success** ✅
- [x] API calls reach server
- [x] Authentication works
- [x] Type casting fixed
- [x] Error handling improved

### **Business Logic** ⚠️
- [ ] Leave settings configured by admin
- [ ] User profile completed
- [ ] Leave eligibility verified

## 📊 Expected Results

| Function | Status | Expected Outcome |
|----------|--------|------------------|
| Leave Balance | ✅ Working | Shows current balance |
| Leave History | ✅ Working | Shows past requests |
| Holidays | ✅ Working | Shows upcoming holidays |
| WFH Requests | ✅ Working | Submits WFH request |
| Leave Applications | ⚠️ Restricted | User-friendly error |
| Diagnostic | ✅ Working | Shows eligibility info |

---

**Note**: The chatbot's technical implementation is working perfectly. The leave application restriction is a server-side business rule that requires HR administrator intervention to resolve.
