{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a9af2d49dfa371b6d752cf584dc61a95", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/TOCropViewController/TOCropViewController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/TOCropViewController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TOCropViewController/TOCropViewController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "TOCropViewController", "PRODUCT_NAME": "TOCropViewController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98605c35452afc21c9738f0c6828c71d26", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f25a2a2f4f6eed3ab64a16a6aa4ac7ce", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/TOCropViewController/TOCropViewController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/TOCropViewController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TOCropViewController/TOCropViewController.modulemap", "PRODUCT_MODULE_NAME": "TOCropViewController", "PRODUCT_NAME": "TOCropViewController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988c614f91f9b72052667099c32cbe6637", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f25a2a2f4f6eed3ab64a16a6aa4ac7ce", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/TOCropViewController/TOCropViewController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/TOCropViewController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TOCropViewController/TOCropViewController.modulemap", "PRODUCT_MODULE_NAME": "TOCropViewController", "PRODUCT_NAME": "TOCropViewController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a86be8a3d3fc9de309d9529a5e706f38", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98259c8b27262564f626cbddcc4ad79289", "guid": "bfdfe7dc352907fc980b868725387e980a4dfaa604a5c973bd4cb5802c9855ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa9c01c5a13501c349431c445166eef2", "guid": "bfdfe7dc352907fc980b868725387e9863499551374c1c9ce8b5b4772d95cd02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e250a9b8bc6390cf721ff967f8090004", "guid": "bfdfe7dc352907fc980b868725387e98729b04462a09cdeae6d1711d1cca87d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a59fbd69c58a78ce24c6c9f8899042a", "guid": "bfdfe7dc352907fc980b868725387e98d7cc822e6ff7acf3dda3e6f4e7a05fe2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98227450b4abc9bda863825e2753417354", "guid": "bfdfe7dc352907fc980b868725387e9869aa9e264a1406d02e21ec0e12782b90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddeb9102c3c85c809708100788457915", "guid": "bfdfe7dc352907fc980b868725387e988c61a099ce5bda3b8c423bda9a71d3fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f6a4f05744b62a61479d252d2705387", "guid": "bfdfe7dc352907fc980b868725387e98969de66fdf5f5ba640fa0b4fd2b1983d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eacc8f02beb17bb923f6db50984eacf", "guid": "bfdfe7dc352907fc980b868725387e9892aac1b38c1207e05c8972ff02b5577d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c30f1d3ab162159a4f2265e710bb92f5", "guid": "bfdfe7dc352907fc980b868725387e98a105d635d93b75cace7bbd27b86b8e73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7aa11a527483826af61ee2dcf68e547", "guid": "bfdfe7dc352907fc980b868725387e98fe2d3380e9a9e674f15a2ea4eea9bba1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985baede3b57872f8d3a4e6b5ed5da7262", "guid": "bfdfe7dc352907fc980b868725387e98311b7b43caef2eb83b6f84d293bffd52", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989823e64e9e00d6500db27cd56bb64766", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9e26a141ea32567da82273fb76663c", "guid": "bfdfe7dc352907fc980b868725387e98f2854e2e6c4b0efdadd510b4c7df08e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f10df69f2cbcd0f71dad35ef496c37e", "guid": "bfdfe7dc352907fc980b868725387e98862f204c2217ec1fd0f6c2541cb1d7d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886362a2eb7633366d5a3c85b9dc12b48", "guid": "bfdfe7dc352907fc980b868725387e98c4192ed237fd06694e27828b8f6eaa20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c98450f8b5cbdc3677a4a8fc12d0e02", "guid": "bfdfe7dc352907fc980b868725387e98314f445b68467d09bc944f6977d6a1e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98007280d1906aa97a1195c6e59d871893", "guid": "bfdfe7dc352907fc980b868725387e9899e94270ee0a9fc2990fe6311d9e930c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeb239857c65ea1fdd8fd0a6a288cccd", "guid": "bfdfe7dc352907fc980b868725387e9874865e6e3e9d40fd69956302ea6a16b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f7d72d3059dbbbf122fa9205e566ed5", "guid": "bfdfe7dc352907fc980b868725387e9830df155f1ccf9cf1ff06feda787776e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880bbff6470f6a378c458bf0231146e0e", "guid": "bfdfe7dc352907fc980b868725387e98a9f944ecbfbd6276045c1acd677fdc87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d71cdaca5e3f80d32d2af00cb60b20fe", "guid": "bfdfe7dc352907fc980b868725387e9834639a5bdb4f50681ddcc792d8385313"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a63d9f8ef847bcabc287463fe625764f", "guid": "bfdfe7dc352907fc980b868725387e983da8a6c162fb34e1a67bb7ab1d4b8cc7"}], "guid": "bfdfe7dc352907fc980b868725387e98b973c71a5c8d9944a2711af778a2c3b6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e98269f2208e6af9ec57440cdb8e9a1fdb7"}], "guid": "bfdfe7dc352907fc980b868725387e988710db881c4cd7a5ab2e4ff5e3783e93", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981ebf3826f47dbb7fd582b4ad06af832b", "targetReference": "bfdfe7dc352907fc980b868725387e986dbfa2df59ddcae0f992dedaee8f3553"}], "guid": "bfdfe7dc352907fc980b868725387e98561889aae53f203fe8d20bd552b4212b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e986dbfa2df59ddcae0f992dedaee8f3553", "name": "TOCropViewController-TOCropViewControllerBundle"}], "guid": "bfdfe7dc352907fc980b868725387e987a4af56e2729cecfad7b13d62a9a5fa4", "name": "TOCropViewController", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9874cf314b58ac20a75c1512ceb8885e00", "name": "TOCropViewController.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}