{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9871b3dd60ec6e64d934ae4ae1c54ae5d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98760f5ae909ed73f27c26858f5102cd23", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aca8bff4d49f90cb0e88215ca3b76c43", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984c85668983fda9e6d78dc4d7a396bb6e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aca8bff4d49f90cb0e88215ca3b76c43", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9867d39cf8488516625d07cc67ee9cb940", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983cbd654ff8f3225c62083a064ff1c292", "guid": "bfdfe7dc352907fc980b868725387e9865d92f441c04c1f56d988165b521b820", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801e375365290a79dd923e594f6abec57", "guid": "bfdfe7dc352907fc980b868725387e987d494ac33163b82098a5193e20383b2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98030a1e7babbdacf1023c5f16ed3c1211", "guid": "bfdfe7dc352907fc980b868725387e9828aaece2e4a44dd762660adb280fd6a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dde40d7dd5a822b8fc8d27b514d34643", "guid": "bfdfe7dc352907fc980b868725387e98ceb401b8b563e83a5c4ce13950e9ad1c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4d120c597d17e04bc61c1060f614ca9", "guid": "bfdfe7dc352907fc980b868725387e988dd1887fca2698c3c7f1af71f9c6b928", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98843c4847dc928080aa694e9cebcc8997", "guid": "bfdfe7dc352907fc980b868725387e989407154c9748d9009a6ad8221033501e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f6250fd97e3ce1953755cfb5cc0b152", "guid": "bfdfe7dc352907fc980b868725387e981d787b6fb3fee205de6a069b1e0037fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df57fa321049d339a26f7ce12bbd0ec6", "guid": "bfdfe7dc352907fc980b868725387e984f5b11c795d0017f8f1fca66b0883376"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850b8085fd625005fad74b5534f44851d", "guid": "bfdfe7dc352907fc980b868725387e986e991910a47e8bc6e7cf9ca0794b1222", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9811c5b0424a0c59b01818e7953ec942d8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f4643dd508c5f67d0399db21de8b3970", "guid": "bfdfe7dc352907fc980b868725387e98ed32b3374aeadf588e5aaec99b5688bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846f922b6a414ad840208aa18153226eb", "guid": "bfdfe7dc352907fc980b868725387e989b75acd7bf9619b74a10289a280c0c73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e9fd6a2482de376e7c3e27ea48fa05d", "guid": "bfdfe7dc352907fc980b868725387e98150403e332f02ceeca8cfa71d0f5a57c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810e4002b78c05e8b405e131f7fc346a4", "guid": "bfdfe7dc352907fc980b868725387e98c05807f354bb018d0d9d9ec295f0b6c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af70ab0e7a36cba8c80ae0733bd6fd74", "guid": "bfdfe7dc352907fc980b868725387e98324083c8a32a29ca6c3908c80347d5a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a92fee2eaa14731ed83f0920aafbed67", "guid": "bfdfe7dc352907fc980b868725387e9851f86b875c61d7cc0f961cc5e734b2e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c380befcf04466e7a7d740ca55e5091d", "guid": "bfdfe7dc352907fc980b868725387e98c391191051772de38fb5b04141fb7f97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb01f232d3325f3769149141db3eedd9", "guid": "bfdfe7dc352907fc980b868725387e989cd5599e5e229b77e900f2d70af11a15"}], "guid": "bfdfe7dc352907fc980b868725387e98df41d3ef7d5482eaa95d46b31986b4af", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e984b1dfca6354ca0779fc2a07f06739d23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98580093888bf617f96216b6e919905ca7", "guid": "bfdfe7dc352907fc980b868725387e983dd3d275f90b6f20ee99cd5854fac8b0"}], "guid": "bfdfe7dc352907fc980b868725387e98755816bcd65718466ec7de18e0f19146", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98978a440f24c0232819bf1ac5c4133611", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1f2bd4f39928f5718c72984ec9fa405", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e987a0976f8644d4fb3e7648b7d7e34f99f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}