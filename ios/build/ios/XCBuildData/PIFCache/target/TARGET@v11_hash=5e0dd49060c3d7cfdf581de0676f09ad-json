{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986236fe8e3871e277091834f897e389cb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "reactive_ble_mobile", "PRODUCT_NAME": "reactive_ble_mobile", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985fb0531c81b6e9d308e3b183b01d5d6f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825f944fa531671e612150347cd9a6fe2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "reactive_ble_mobile", "PRODUCT_NAME": "reactive_ble_mobile", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98da7d9c51499ca7a1c71021548de920d0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825f944fa531671e612150347cd9a6fe2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "reactive_ble_mobile", "PRODUCT_NAME": "reactive_ble_mobile", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d1d6d14974458eba4a6a83a91f0961d9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d0eedd5b7a37acfa1c68ab01ade9db24", "guid": "bfdfe7dc352907fc980b868725387e9876baa28368b6eb9089ef74d04af4510f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f5d8d93d0204861291f39b319e62547", "guid": "bfdfe7dc352907fc980b868725387e985f4e5292d1bd2ba4a84edccd2b991cf6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98aa40c1acc98c1bc9c20ef1b6d1f402af", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851ab8c6ec553af4137a2270394ee80ef", "guid": "bfdfe7dc352907fc980b868725387e98467ec331e6abdb5786a6d752f132d634"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980db49ed021febd14630aab9b0e959647", "guid": "bfdfe7dc352907fc980b868725387e982132f3ac3339c1119f0bc09af98b1c96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988efb98b71042af4db7706fdbbb977cd9", "guid": "bfdfe7dc352907fc980b868725387e98188d6bbde20ccd9bee5b7524f3c8dbac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f03aa7fd70e6ad43e1460dccbbb854c5", "guid": "bfdfe7dc352907fc980b868725387e986b4c9ad1e1937b2d35c0d3a3c324daa0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895b6ffd33881a2bfbefb48792ca97f3d", "guid": "bfdfe7dc352907fc980b868725387e9896b1e2524a0b32683358fc4c209db1ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c07861d6b371507a17425d136e5bd1d", "guid": "bfdfe7dc352907fc980b868725387e98b84a7ae9dd2692956ff4466722bf9580"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccaaf8c8224a92b921ea9554432c9e56", "guid": "bfdfe7dc352907fc980b868725387e98d9565320e326c3881812d3eca8ac1a79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98507dd96627249b5deaac20a52e5ac7f4", "guid": "bfdfe7dc352907fc980b868725387e989f0efc9563bc2ca19daa2ed41986f795"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bd1d43d2cd59d619f3d7dfb6e04d2d6", "guid": "bfdfe7dc352907fc980b868725387e9820012c5fe4a78777dcff4ae26604afd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcd6113740b59b4b62eaaaad3dfbec97", "guid": "bfdfe7dc352907fc980b868725387e981a66cdae8514dd291f84204f47c6bead"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4e0bc137b350a7ec27a62e1554deed8", "guid": "bfdfe7dc352907fc980b868725387e9807a88bbef55cc5d9d3b1977d3cb20cac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885f49e6e6d0d91af410890fb38eb7620", "guid": "bfdfe7dc352907fc980b868725387e981f938210c5ad81fcffa0f3010ea512ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98369dbe173680ca8e138b0807aeebc709", "guid": "bfdfe7dc352907fc980b868725387e9889afa807cecb7337c3b751a78f998555"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c01a300184da1d207522ce8926bcbee", "guid": "bfdfe7dc352907fc980b868725387e9886a5b580eae067037b665a45b6c312d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864049c9e4b7b62cb9d969dd841042740", "guid": "bfdfe7dc352907fc980b868725387e98fe1f06ae8a0f73320b11cbe75268c285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98525d79060e6d7c65d79fd61ed66646f1", "guid": "bfdfe7dc352907fc980b868725387e98cfbc981a46cb88a23a8111c20eb4b8ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a2a294a41faf99c7a28f8f2aa0d9c54", "guid": "bfdfe7dc352907fc980b868725387e98a58b28c2c20a556fabc5d5b88f745250"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d21f5c94594086abf5b0f93d29226d4", "guid": "bfdfe7dc352907fc980b868725387e9827aa027b7d7585b779b9c2820ed62c71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98571959b87df96bc0c6ae0886b5d01ddd", "guid": "bfdfe7dc352907fc980b868725387e983827e13ec2b43d029e865276473e188a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adbe4896febdae5e951a0fab10a62f32", "guid": "bfdfe7dc352907fc980b868725387e98f20a7f2bfba17137706825ca7b048cdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc76662f5f3dabcf550fbc8c79867c05", "guid": "bfdfe7dc352907fc980b868725387e98af6fe06686678d7a65491518d93797db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce9db823cd1c2e4ff4fc767a182edfb6", "guid": "bfdfe7dc352907fc980b868725387e988f26e5761cae2b417b8349d59f175a3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807359310a498df01757d9139a3e41243", "guid": "bfdfe7dc352907fc980b868725387e98ab72d2395ca46ae74e33294c15ac7a57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98753d9d988bc6b0056d8aa94828a75ae2", "guid": "bfdfe7dc352907fc980b868725387e98614f388c9de1b927bdf66350aa675c09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dac5ea7949b98c39b0720706e1cd03a", "guid": "bfdfe7dc352907fc980b868725387e9820852bb50d93f2445c86225705468341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813c225dba741a74247a8d92278d5bf3f", "guid": "bfdfe7dc352907fc980b868725387e9876fda15da8ac3eac6247215bc78876f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f6363da5dab4806cd10dae4e0ddb556", "guid": "bfdfe7dc352907fc980b868725387e9848480a2b2ca1dd4ee5ed4671c373cb2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f77bebc361e553c7f4aefdd2522d68cf", "guid": "bfdfe7dc352907fc980b868725387e98c7ddefe2a64639b57f7818faf7eee220"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6d068500f8d34c4547233124d8902e9", "guid": "bfdfe7dc352907fc980b868725387e98386903d8a8bf23b55679bb79ff03e9d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e1054c7316b23243a81e9e6698ce17a", "guid": "bfdfe7dc352907fc980b868725387e9804f6eb24a739c85a2ac9bf5eb1bdb2eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff4f8ffa5c87b0ca81fd3915f574ab4e", "guid": "bfdfe7dc352907fc980b868725387e98dfa1f44784a28a318c01e3250eacad55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98154c70af2bd014fd67f7218baf6d8a38", "guid": "bfdfe7dc352907fc980b868725387e989017652fe09b2c9542d4ba47b52b267b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984585efc2ce0296e8843a848ca06db9ca", "guid": "bfdfe7dc352907fc980b868725387e98c4dc1b5efa700278f35dfc85cb48c56c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ca17298e3fb488251995df1109b1ac0", "guid": "bfdfe7dc352907fc980b868725387e98df3b417a5e3c8d5eacfbb8eb4a5da297"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c627180645d4f55b12882c039656738", "guid": "bfdfe7dc352907fc980b868725387e98d6128b69920271730418f1833a2c6b63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d346a6ba6c089fd8d14c316bd71f831b", "guid": "bfdfe7dc352907fc980b868725387e98bc206038850a20b163684c63bf9358ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f63b8d91e162580d6a6a87a167e0f1a3", "guid": "bfdfe7dc352907fc980b868725387e98b037af8dc321f9cf2818ddf72976130f"}], "guid": "bfdfe7dc352907fc980b868725387e982ba31f417545fa571bbc70c971bb7041", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e982f384b5d7d3e4668014e92b971f4d47b"}], "guid": "bfdfe7dc352907fc980b868725387e985f5bae573cc561b47fb74f0e2ee3152c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9883054ce7ccf69448e11547b18ae233d5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98ea730784e531c7b29a4d3807c4f260df", "name": "Protobuf"}, {"guid": "bfdfe7dc352907fc980b868725387e9840fa72b1389229bc82a786c7bb54bc7b", "name": "SwiftProtobuf"}], "guid": "bfdfe7dc352907fc980b868725387e9882f8d386d4480ff95a26a30c940edf12", "name": "reactive_ble_mobile", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ad15dc253cf5cce54073e74a8e492f3f", "name": "reactive_ble_mobile.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}