{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98905be5fd8b9fe13bfd19910997610b60", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ae6d24a8d814690841a21166ed7492fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989384f3444c0b68486e0f641020a3144e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983d600b111debf4bc085e187cc3e2f69c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989384f3444c0b68486e0f641020a3144e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9817b866c20a697d99c1e3bdfde74ba5d2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7b13106198a05ab0deb3806e1da3b03", "guid": "bfdfe7dc352907fc980b868725387e98df3ba65856aa63b1c254dc762685a504", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e27bfc4d0bfbb86a2d60cf8edacc46fa", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e4b00644f05d41bc9c7340060951fb64", "guid": "bfdfe7dc352907fc980b868725387e9878a25b86d677f7d1de1957c0322882d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819f75483f5020eb83712b3d819f2fd7e", "guid": "bfdfe7dc352907fc980b868725387e9829263a8ca563416d23a4eadc8a816146"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98566d25143865e234e1e959f8d3f7996b", "guid": "bfdfe7dc352907fc980b868725387e98b052700b721629b6e4727418f3f61462"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98930c4ee20575b4317296f7fe7378121e", "guid": "bfdfe7dc352907fc980b868725387e98bcf112ca042fabbefbd292680462f969"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ed036c4a4662c747e2d7917174bd800", "guid": "bfdfe7dc352907fc980b868725387e987da9d9152b4f6e436dfdc1c6684cd7d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcb97647d75d22bc6d9fdb85afada3ad", "guid": "bfdfe7dc352907fc980b868725387e98795a9c28bdf55e1938ba77c741d825f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2e9bfb9c2aa40e89b1b745772f5a0b7", "guid": "bfdfe7dc352907fc980b868725387e989978fd5e40569101046b3bf17ca72fb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0e890bfcef59f1d7fbd1a916f188da6", "guid": "bfdfe7dc352907fc980b868725387e984d80bb18ff20a16279e45caebbffd9c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef6112f695019555aa55b78e5539937d", "guid": "bfdfe7dc352907fc980b868725387e98b6c59ec1446a78b56aa429c780f2128c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f67c67a908ce8dc366edbdc9201d1f34", "guid": "bfdfe7dc352907fc980b868725387e9847062b4b351ec86ac51eeb488cadab12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c63e7d024a40fb0bd9d33f1780a21339", "guid": "bfdfe7dc352907fc980b868725387e98e80ad3772dc64cbddbed70c4f7935d0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f454db57938b2a248b57d13365092434", "guid": "bfdfe7dc352907fc980b868725387e9888cd1a8dd21640e456c8ccd1fcaef7dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e94120b1441849fb90856c9a225861f", "guid": "bfdfe7dc352907fc980b868725387e98b5dba82471c459dcf7e36b4cbdc31b4c"}], "guid": "bfdfe7dc352907fc980b868725387e981fba60d0f3bf5b9582afada68c4d531b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e986dae06fda35f02d689866262cff0b345"}], "guid": "bfdfe7dc352907fc980b868725387e98e344fff71a610185ee10830e3955454e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984509954de35ed877bced12bac9571096", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e9890250f45c65ce6b82e23010a70c6a4e7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}