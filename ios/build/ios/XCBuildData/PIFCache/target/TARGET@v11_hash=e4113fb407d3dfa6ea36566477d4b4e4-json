{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986974ab8fb6db398498875d1fad1d27a4", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d36cce6c2706236f30ab4e223acfa95a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980c4c7450ec316565525d59e1963714aa", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9897b1f4ec071cc524e9aa7a539b0bb5e0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980c4c7450ec316565525d59e1963714aa", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989407719299bba9bcd9bc15bc23180df8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c0925c94ccb08a7ccf09915c8e92a7ac", "guid": "bfdfe7dc352907fc980b868725387e98b41f7daf060ab320e79602d54bb1e30c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a7f43fa3a1db1d3fad0b846b26429fd", "guid": "bfdfe7dc352907fc980b868725387e98c9582ad3c24ac20c2f891593644921bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860d774f5ba3660617a5b143374a63ddd", "guid": "bfdfe7dc352907fc980b868725387e98f9b81bff80c853211fff35ed778c9a49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815905295213ef4bdbd2283e6f4e3e359", "guid": "bfdfe7dc352907fc980b868725387e98b9be4b7bb21ac4871e44ffa847f12a0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98533cba57b0f17f949d67066bbb7062da", "guid": "bfdfe7dc352907fc980b868725387e98b3d9bb2850740ffc1469fb7d48540eeb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822480ac78a95759e32ec0bf3cebc00cf", "guid": "bfdfe7dc352907fc980b868725387e987fc4d71f96591f4bb0ff15a5d8b82c5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7a5b4c311955e39c049e2a90166ec2e", "guid": "bfdfe7dc352907fc980b868725387e987db8aaa0d5dd3d68666d4ac70511a6aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98799bb357a7575d3c040d41006a6f6a71", "guid": "bfdfe7dc352907fc980b868725387e986e59f0f085a053018ba800cb737aad86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bc29f213ae734fb98ba6082fb93c997", "guid": "bfdfe7dc352907fc980b868725387e98d093885d163beec2032997921e9275ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a073624fd387ad96e3383aacd9d621d3", "guid": "bfdfe7dc352907fc980b868725387e984b806475a258c6ed32bb2d1df08e7926", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b04021162d531e96df568c089433f92", "guid": "bfdfe7dc352907fc980b868725387e98a81735ea4b0c674b17fd397a79a780f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848c126ada2aea47a4d5d74a68a5c8a64", "guid": "bfdfe7dc352907fc980b868725387e98a8256dce68f2ff83288393e17152cbe0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dce9e744432dcf8d141a39dabe532c39", "guid": "bfdfe7dc352907fc980b868725387e98ef32ff2271eb2a1c3f1649c27dfad1d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8de641c0e3f0f393d379d68371f42ba", "guid": "bfdfe7dc352907fc980b868725387e989d71b247beea88dbd61abac251e8cc11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984213fda23380b35c68a58d7643800115", "guid": "bfdfe7dc352907fc980b868725387e98be5e8877bd88a935d43337b952d7c97c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98935f83e7c6a63543a8522b334854e999", "guid": "bfdfe7dc352907fc980b868725387e985d129b9d9209036dadeb45c66af24bb4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877b591895bee105835602ef04f7a7cc5", "guid": "bfdfe7dc352907fc980b868725387e98ff0ec56f8ad75ab4ccfee9a28fdbb9c2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98daf90cfcd0b3429440f11d422f98df04", "guid": "bfdfe7dc352907fc980b868725387e984239656e75cf5e0afbda9158aef0b01f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c14a82c38911849e3de47b8b9625eaa1", "guid": "bfdfe7dc352907fc980b868725387e98365e4f74bc25d60128e17459f1ccf6a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b06557ded62833b6d7dc69d711962b3b", "guid": "bfdfe7dc352907fc980b868725387e98c1a3cd1dcf22b27c0c52e7ce4113d684"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b718162cd3b8fc03ddd53e3b1b8d1b6c", "guid": "bfdfe7dc352907fc980b868725387e98c102e1b81fded2e92d95769a174a1d01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345d2d1d0bd361a3f5d72d6f11bb9498", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aea84f2abdef6dec579e73516dccc7a", "guid": "bfdfe7dc352907fc980b868725387e98c1890f27cc6c993bfa4a81fb6df050c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988588989c2dc3b4d1dac69dc1b9824a42", "guid": "bfdfe7dc352907fc980b868725387e980f13a01a9beb5a66c5949584782d5170"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98520b87cf98bead5cfdd47670044f7ff7", "guid": "bfdfe7dc352907fc980b868725387e986fd11db4204af9ce2881f3992615bfd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a5a4e234f813fb013346b454cd1da7f", "guid": "bfdfe7dc352907fc980b868725387e98ac4daf2c3ff40733db2606df2611370c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982501d14586577b1a69afbf744192d6ba", "guid": "bfdfe7dc352907fc980b868725387e98753799ea8eb80bf7aa3902835d463d84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a1a2a05eb47679f4d2d38b03c466fd1", "guid": "bfdfe7dc352907fc980b868725387e982f6b118d06a21e8dd090656e96388727"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988711725ad24308f364019f7b7d4aa18e", "guid": "bfdfe7dc352907fc980b868725387e989fc3c5714d1cbbdc20c3961fbaa570f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881267892ab13659b6416af70b58224c7", "guid": "bfdfe7dc352907fc980b868725387e98e9921b07a26c7375097dd24bff5b185f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891f17e4858aa77c6bdce831ea525698c", "guid": "bfdfe7dc352907fc980b868725387e985fd18ca2f7419267e8901a3a90c868c8"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}