{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c8dd7ceadf673fc82b0ee568783aa8a7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980d996f0fe52c8017c40142527e05b5c4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b7e179778e1cd78230cb2af971c743d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f78c55ec73b4f538e16397a350b515b7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b7e179778e1cd78230cb2af971c743d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d3220a077f980509ef6af032493bd9dd", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981518e5941cdd2bb17d474668c7200c7c", "guid": "bfdfe7dc352907fc980b868725387e98f6ed6f6b74e68659d6394c7b0d908127"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896804e6b8597efa653d40d6b681007fb", "guid": "bfdfe7dc352907fc980b868725387e98e6ac812af5d8af7cc65bf1e767dfab98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c40ed0ae35db0df3bda2dc8fcc0a9ec9", "guid": "bfdfe7dc352907fc980b868725387e988215847c98738593e38a2c63e8d50a67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c84044a0ebf5df6f7742e9481891b23c", "guid": "bfdfe7dc352907fc980b868725387e98c48a31eb30032fc2559b888e7907c503"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98658c026f4d5c6e3cefd2351e0f30cb5d", "guid": "bfdfe7dc352907fc980b868725387e989c39d8278f2ad9a3ce07e25b5d2cacfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a41f878bc431576c0514b84f1b1eb0ad", "guid": "bfdfe7dc352907fc980b868725387e98ca46a3d5c951cfdf5d80387810f4e212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988359b4a92b28286941a43f32c17d97c6", "guid": "bfdfe7dc352907fc980b868725387e983280756e9fe9e49475c553d27bdafa2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98958cd23149add8110416162073345f07", "guid": "bfdfe7dc352907fc980b868725387e9854978d69499084aa67057e6c1240a99f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d67fd25c1267378dece12c66139c33b", "guid": "bfdfe7dc352907fc980b868725387e986b1b17aa82d93652f6b4229512c0c0c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac578e0e544a0d05bba8c519459bd7c1", "guid": "bfdfe7dc352907fc980b868725387e986c81538ccd2a02ae0c4edaeccec39f7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a54aa837e60a8738b0bbc3301962ea0", "guid": "bfdfe7dc352907fc980b868725387e9870088f0953422fafd1d3519ae0b8a80d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4b6cf0c2d693f5146dcb6ce1fae4648", "guid": "bfdfe7dc352907fc980b868725387e98c00b4a72b4a50bc66db5edbc31a199ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e0bc749ed5315887c48ba5dbdea98a2", "guid": "bfdfe7dc352907fc980b868725387e98ae90caaa42269f9a110133ce7d4eb4cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98268b5568193c7946a8d6d648a1af3366", "guid": "bfdfe7dc352907fc980b868725387e98fd84be14448e6d92f75712d597f6a257"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a012c9e1f82b8c9c6fb8a11f93e001c8", "guid": "bfdfe7dc352907fc980b868725387e98536102971c926e9f5cf1ddc65cd0344b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986737c4250a592d3405b1b7d26a1152af", "guid": "bfdfe7dc352907fc980b868725387e98856d58d3b6bbc840a27998f37d344436", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e24798a1893de03ffbe43fe4d233e26e", "guid": "bfdfe7dc352907fc980b868725387e989d31dcee68005325f14e368a407c82b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4a647a967fe7b10c8068ddebb94eb3e", "guid": "bfdfe7dc352907fc980b868725387e986a3a80c25053a3893f03b6327912e6c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98415ed7e3ed0ec32569cd028d893c0368", "guid": "bfdfe7dc352907fc980b868725387e985b3c60c71264b942677cf7de7728df43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc8cc2a493460f7cebdb6ca4b227c864", "guid": "bfdfe7dc352907fc980b868725387e98bcdee0b355383d8382d1f1f638fa54c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b90cf97405b71af17f2dc847e577bedb", "guid": "bfdfe7dc352907fc980b868725387e983f8b674dab77416b1570aeebf1dd3988"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2eaaabf69662e2721b9f13020bc9576", "guid": "bfdfe7dc352907fc980b868725387e9845c976facca1bf4db6846bf81f72131d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3ab8d0aaada76991e635f187b4ccda0", "guid": "bfdfe7dc352907fc980b868725387e982da9bf3e9d76593bf265d0d92ed2c460"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98461edc0c0537f6bf4530a934033d5dc9", "guid": "bfdfe7dc352907fc980b868725387e981135f43299d72b9b1aec63de833a1b77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f667fd107a9492f654d1417b6d00b51", "guid": "bfdfe7dc352907fc980b868725387e986aa269d8be06c4841c1d057941abd1ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f9ada15fe206e4373ef3eba0f919199", "guid": "bfdfe7dc352907fc980b868725387e9858edb8379d25ffa8d810c668d1d56463"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98210718ad54e10a315998d754c29fe55f", "guid": "bfdfe7dc352907fc980b868725387e98ee6a5b0ea22f8331d4e7ae95dad5afa3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836a5fd053f602eb45f1e32bc059fed9f", "guid": "bfdfe7dc352907fc980b868725387e988ef153ceaff95182391a70400102be80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb46183a759634394ca4a55445e37c93", "guid": "bfdfe7dc352907fc980b868725387e982c2f8acfa82c3ba3d38e9810f2ce495e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986140b1fde182f04701d14b39a1cba1fc", "guid": "bfdfe7dc352907fc980b868725387e9887e9b0be8de2f7811e1a7c168b212d39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e4e60511b1e5b4b13ad2d669b823800", "guid": "bfdfe7dc352907fc980b868725387e987903faac00970c84f97bd8f4b32d07fb"}], "guid": "bfdfe7dc352907fc980b868725387e986fa5ffd6e790bb920eef7145a1f13661", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7de6ca4c322f49fd4cac4ecb63b2830", "guid": "bfdfe7dc352907fc980b868725387e98cf1dcadd5d88e675c0b893167a8f8fe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98314e52819da101674f6bad4d5b2faba9", "guid": "bfdfe7dc352907fc980b868725387e9866d14fd58bd6c4ceb786816bba4750f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4b414b4d5197be807ede57022a6fecb", "guid": "bfdfe7dc352907fc980b868725387e98f7e848d48e52a7b9a6d3e7458ac93cda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981044f97e766a4c9128277e748b1aca93", "guid": "bfdfe7dc352907fc980b868725387e982361c6b909f542f734eb5aa6c37cc846"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d977fb9f68354a860ff9c0a3f33c5cd7", "guid": "bfdfe7dc352907fc980b868725387e98de9698d8473ca9438e169e9d32fe8714"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98446e12788930d71bf1db8f3824476e5f", "guid": "bfdfe7dc352907fc980b868725387e984f20d5fbbc5cc7634dad7dde49a61cd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2944ad52e686df53b7d257df988b88e", "guid": "bfdfe7dc352907fc980b868725387e981ac4f7a782fc622740400c382d2d0843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c249d207a21c481fa4d8a796f71d87e5", "guid": "bfdfe7dc352907fc980b868725387e980f7afcddf3e8dba5437b4f34ffce5338"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980765a295a244b954fd580284d92348a9", "guid": "bfdfe7dc352907fc980b868725387e984b28a9ac0871e2f689976c0450563e26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851f711bbd2a2a650151de4dd681c88cd", "guid": "bfdfe7dc352907fc980b868725387e98fed39937d7276fe64bb24302d8ebccb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c2594034d1e80c159156d337953b413", "guid": "bfdfe7dc352907fc980b868725387e98026f49ee0b20f06e20871967b7cc7829"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d47913622f8a91c1a14dce5af1c4258", "guid": "bfdfe7dc352907fc980b868725387e9890eeb63648db2f7d0df9a94eb6365422"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dfcaf473ab8b1625ac854c65bcab4a0", "guid": "bfdfe7dc352907fc980b868725387e988f571679cd381723b6ae9fb6f69710b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2fec6dc4ce44e92aa5d7adb2783092a", "guid": "bfdfe7dc352907fc980b868725387e98548f41b4b06287d42a1cc792a64aaaff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893e29aea0e9dcfdb6990b2fa58ee4a38", "guid": "bfdfe7dc352907fc980b868725387e9873c1f7bf7af9eefc275f8c89a99168db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d752d1186298f852b378780065db20e8", "guid": "bfdfe7dc352907fc980b868725387e9850c57afdc14965a964be92b1e56859c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a89e9c37b18be7ac833ba680d133674", "guid": "bfdfe7dc352907fc980b868725387e983b175b0cb5ac5af9bcd2650e4c507648"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1903bb7b3b6070a3f5dadc7677d1add", "guid": "bfdfe7dc352907fc980b868725387e983641ebf39347c01183acd257b84d374a"}], "guid": "bfdfe7dc352907fc980b868725387e987f245afa378f532dc1f6de51975cebd0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e9879994a7462a5cecaa649762304fa3fab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98580093888bf617f96216b6e919905ca7", "guid": "bfdfe7dc352907fc980b868725387e98216efcd91fd01acb29ca400081621e10"}], "guid": "bfdfe7dc352907fc980b868725387e98d8686c06fdec7410ae3e05af9443ddc2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982cac065c7a6688136e57b2944c9764f6", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e987cf90ea5e88e73bce3eedc12b7563366", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}