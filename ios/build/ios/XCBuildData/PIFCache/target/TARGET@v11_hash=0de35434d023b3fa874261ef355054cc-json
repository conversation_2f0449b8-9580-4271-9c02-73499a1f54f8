{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9bb2894899713bb6459846f25763be9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9888dfb188339eb47edd40832ad2821c4a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98079fd9eea674b1c59db1845175869c14", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98432f5da9a2d22ff69ae2663a6e79589e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98079fd9eea674b1c59db1845175869c14", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e5b54b2e1e32c1c8b0b46a1b95a4b921", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9845647031a3176590a3ed027876870fe6", "guid": "bfdfe7dc352907fc980b868725387e984f006964d4146824486993980a439b59", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983967ecc73d95d1d07be9e342eece721e", "guid": "bfdfe7dc352907fc980b868725387e98014809c20b94e74ab296896599d37287", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4957e79300a2cc5fa1b304283612901", "guid": "bfdfe7dc352907fc980b868725387e98e4331fff38541695dcd8b3c99b193acf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4334010cfcf0e00086fa09726018977", "guid": "bfdfe7dc352907fc980b868725387e98f10b4279dc5e2c8d173d382a11e2c171", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817995a4def41d822186c46b136d82042", "guid": "bfdfe7dc352907fc980b868725387e98bd9771354c228ffa9a68c71f381bc93b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b14ef085106ce2cac85d307155f5fd36", "guid": "bfdfe7dc352907fc980b868725387e9883181d4a39a64897fe8923fc8117393f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868f3b3534c332ba5fffd8385e60aa310", "guid": "bfdfe7dc352907fc980b868725387e98aab82f86eaeb2b395b0863cd4301789d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825b166016505984177d6a33ec9499fb5", "guid": "bfdfe7dc352907fc980b868725387e98290017cd3c311ba2e9a06ee618ae5ac3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98617833cfb9cdc3c32c234b5c2b774a83", "guid": "bfdfe7dc352907fc980b868725387e98057f1eb795f558bf15bbbb969c1cf4ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896b72d74ed35428ebc12ae4c7b670795", "guid": "bfdfe7dc352907fc980b868725387e9839bdbcc63c29d030d5d4950f4235d0ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9b5f334a85ae8222555d5080d5ce2b7", "guid": "bfdfe7dc352907fc980b868725387e98f1e2edf44c3193fb71bf2b5527ba7ca2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810a5850ee3d61f3ded491f18f6d9844f", "guid": "bfdfe7dc352907fc980b868725387e9864f800a52649433e83b15d8ec51b4664", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98132a89a3c05d38639e2f0fb4dc4b587f", "guid": "bfdfe7dc352907fc980b868725387e98d7eea04980cf24138cbfdfe22517e16c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4522182e34357a1752c1c4484cf2df0", "guid": "bfdfe7dc352907fc980b868725387e98eccce3f49c74d6c8ddd3253309d048ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dd98c4f305a22c7ae1c19822478dd6d", "guid": "bfdfe7dc352907fc980b868725387e98ec9377d7a9375e3998a1d9a47ab34ef2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981551a371a30a0836d6697a03fc2d326b", "guid": "bfdfe7dc352907fc980b868725387e98b3f41cc96370253b2b4886a601308390", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ee66d34396eda0f4ff52289e74b8146", "guid": "bfdfe7dc352907fc980b868725387e982e8b00e582837917494d7d3c3f0348e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a497e3b8ebe16371956789444a0dc726", "guid": "bfdfe7dc352907fc980b868725387e98994931f4229d845f9f9e1830c9b58a1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce7f89da52d1462280a2840484ef3b33", "guid": "bfdfe7dc352907fc980b868725387e9808c4d0927d6bda041722d7f69f57c154", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984854851b0908bf8550648b59a8a0142d", "guid": "bfdfe7dc352907fc980b868725387e98c04339b28817521909af2475fb4a0b6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3fca3b364b6a91190ad07a11a16fd30", "guid": "bfdfe7dc352907fc980b868725387e981b1d8a30098843dc5bef631e5a55ea90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845897ef141789c0fadb156fcb4e65140", "guid": "bfdfe7dc352907fc980b868725387e984aef3ef8009f7e80a812596dcae0c4c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f03e421ae7ce3c1a6f6811a1961c265", "guid": "bfdfe7dc352907fc980b868725387e985876f7e07cdeffb2734f9ca2583d3ae1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980829be899a5d3c492809e529520616b1", "guid": "bfdfe7dc352907fc980b868725387e98cba1137242b78c74a5dfc7acc98bb498", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813ace515f041194f3c93cdaa74b150eb", "guid": "bfdfe7dc352907fc980b868725387e9823d408385ae5ee0ee4458d5fed9bcded", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98487b10d05c8c16bf287b4e0fdd2a230e", "guid": "bfdfe7dc352907fc980b868725387e98f9e7b154f5778877ae0e6db98c98d490", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5a9b4cd5d54a067d758d4b2a1ed46c3", "guid": "bfdfe7dc352907fc980b868725387e988a5494fe2707a3411db8451dfb736e92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dac67b006dc10a87a93d0447af210895", "guid": "bfdfe7dc352907fc980b868725387e98c0bc3c850e23741846f1b5f51456456c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b11a11fe095e1d1aee302f1443ab494", "guid": "bfdfe7dc352907fc980b868725387e983b0a148bbc2e9b43b841791813d41774", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f431d3a3d201bda808e140e0e20ecbf", "guid": "bfdfe7dc352907fc980b868725387e98f23932a2086bf5764780cd6b15476be4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981fe91c5810c21681ee98dd9f3f0ab2d0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982560c00b77dfe3fb238d02b282a95406", "guid": "bfdfe7dc352907fc980b868725387e9819b2e62e3509e859a4a1d7055a7f2871"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827ad8736284a929c6a951fc17cc872f1", "guid": "bfdfe7dc352907fc980b868725387e9813aa204108d1e96f4a656c4d5f9d6bb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98359d0d78b9c066afc82f4af6d965b15b", "guid": "bfdfe7dc352907fc980b868725387e98987b1dbda9433c545d0648e641c353fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b5162c3d90e750e14725e8c7aeca11a", "guid": "bfdfe7dc352907fc980b868725387e98afe07d525aca1c2ff944b1ac7be40b16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984861f085f9c0b7087a10377b28e3217e", "guid": "bfdfe7dc352907fc980b868725387e980b6d6772e670c26ad948afc6c5ab32dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98159c77b264ff591e06f1f2b3d72595e4", "guid": "bfdfe7dc352907fc980b868725387e98daef7f7a781ff1447ae7853c0f2c3bb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803c7a1c3a4485b8e9d7080f7d2959a22", "guid": "bfdfe7dc352907fc980b868725387e9839e6003f9bbb864d15746448a210dfa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981622a91ecf321e9d35f284656050cb5e", "guid": "bfdfe7dc352907fc980b868725387e9814d2ec4b679ef206dcb98223e46ae7cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b2fcb75a7bd796bf106136d197819d0", "guid": "bfdfe7dc352907fc980b868725387e9829c103d670bc6ca7523f07355b23f139"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98025f78c1f3716552b864bcb5d93cb0f3", "guid": "bfdfe7dc352907fc980b868725387e9865b2948fd5344ee4962b8c23634b2c51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e4194eb99fcb53a538c6ed2e408ca6d", "guid": "bfdfe7dc352907fc980b868725387e98a6dbfe01e26af3cad888714485d6ac55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98405992834c8264e0d85c0e37e006704b", "guid": "bfdfe7dc352907fc980b868725387e98559709aa235c6fab958a84d1b9362e6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98461fa34be908d27d6813c18ff3ad1730", "guid": "bfdfe7dc352907fc980b868725387e9889b063dd1a646d2be69fb6df77fc51ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980443d207c207d35ad51efe6612677aab", "guid": "bfdfe7dc352907fc980b868725387e9881f78736736f84748951bd4e8844759d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828f3a383bb9a9564a523a8a78a171f26", "guid": "bfdfe7dc352907fc980b868725387e98762517fcbd905c86107446663a389923"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df3ccca2839b0b0baf977a11e46a10ca", "guid": "bfdfe7dc352907fc980b868725387e983955b061caab43f489df1b6c201616d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98767e599cf349ce3f5356f5b0161ed795", "guid": "bfdfe7dc352907fc980b868725387e9867241449872f8d14a458c8349fc8b895"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8a38c69a2808c06a96f0ea0d795e221", "guid": "bfdfe7dc352907fc980b868725387e98b5c51ae59e10439dbfaa02c12dc91d2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e7339caa3878e91db86e05c70290db3", "guid": "bfdfe7dc352907fc980b868725387e987f09c5e43f107c0997f582094c1f7fa2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b8c1aa7500fd20040fd4653ce3abd54", "guid": "bfdfe7dc352907fc980b868725387e9872759c3d874ac1fd2d50a1da4b67398d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fca42af7869c83bb09ea6a2013eba4c", "guid": "bfdfe7dc352907fc980b868725387e98f12a5be6d5dc8ab77680b14a6dd28d8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859d66f2ee6483f874ea2089fbf5a46d6", "guid": "bfdfe7dc352907fc980b868725387e985c609122c71bf69362d0c1c43a91ccf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98892e7ba40ac67e75083b4e7dd9e5e82f", "guid": "bfdfe7dc352907fc980b868725387e98e7a296a20995374d18eb18f1e3bc29b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb8e528af87a34f7212b112a67baeaa1", "guid": "bfdfe7dc352907fc980b868725387e98b373fcc753d69ccfb65c84c92a697500"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986de28dbc86638ee78e0c3e0e62ab60b6", "guid": "bfdfe7dc352907fc980b868725387e984cd8526002ae6df0c887b8e74d5604fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3a5a3ebbdc0c843a7bf750217f5cadc", "guid": "bfdfe7dc352907fc980b868725387e98b8722d815546be60f727f24077da62a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e7737eec538e7d4836435f0b473be77", "guid": "bfdfe7dc352907fc980b868725387e98bc463aac492467ba00a2a5ee6298c98b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984496a1be0d3358f3203c7c3474b742d2", "guid": "bfdfe7dc352907fc980b868725387e980bbd6c21d897589e6d5768d0c613e559"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989288d60834860c35557527926862940f", "guid": "bfdfe7dc352907fc980b868725387e98e2d60f7763010f96107829c3086835fc"}], "guid": "bfdfe7dc352907fc980b868725387e98b8aa25d9f69599f54063ee42407bf98f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e9865042f57472a0b9c84246d46d134cd7e"}], "guid": "bfdfe7dc352907fc980b868725387e981a649a27595dea55770b9130b2f87e3d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98819f51274c2a92ebe77b60bee07cca66", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e9814192a7118d15c4b0cbfc04e3fe934f8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}