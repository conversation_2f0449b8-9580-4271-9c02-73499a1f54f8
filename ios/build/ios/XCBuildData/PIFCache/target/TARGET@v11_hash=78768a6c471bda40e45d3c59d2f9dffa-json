{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a24c14d46d8000bd9d5a7704b6b8dcf8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a0e15b7c3ddc1ec6d0d1af87d99eaf8e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879294eb3d2466afad594a9ace7358c62", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980397ae9fa17fc7aacb88c5afc434068d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879294eb3d2466afad594a9ace7358c62", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98864d2dce565807bfd5d2fe1f70498ab2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987953148de6bec255bfa804ce3adb6ae2", "guid": "bfdfe7dc352907fc980b868725387e98895bb014662607c21891fa120f29cd88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827dea8bbb016106d13b969aabeea81af", "guid": "bfdfe7dc352907fc980b868725387e98dd7a496ea85e858505ce21731e1fe868", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989146f975fe691a6a115db02d268fbe3d", "guid": "bfdfe7dc352907fc980b868725387e984968d818d05599b4604eab8884ca3579", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bf657c898ceaf1cf44d3b110c655d393", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dded5d4c9f9a78f8e7576f90774b9d9c", "guid": "bfdfe7dc352907fc980b868725387e984dd4a7e84e47ad1bd2f50d4f7781b3a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c09bc2d621d80962ae6c442e3bb07fb9", "guid": "bfdfe7dc352907fc980b868725387e987c7a67185311a52b6561310ee9585ba2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ed3591667faae6745abc98bf613c3f1", "guid": "bfdfe7dc352907fc980b868725387e98533dce2afc0336d087b232c35c419739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b8ae6772c71972c24b4483f3b10b37e", "guid": "bfdfe7dc352907fc980b868725387e9808ac90ea1a604fc2247d502d8ea07a77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865a50a823c6205b79ca907828fe76360", "guid": "bfdfe7dc352907fc980b868725387e9899e405fba2e9e28fcd3a7b67c2601f9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe0e8542722df7d202f5b6f450852cb9", "guid": "bfdfe7dc352907fc980b868725387e98d6b8079f70bcdb2216b885caa1ebadd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832e6815c39f1bc583c648de59918f898", "guid": "bfdfe7dc352907fc980b868725387e983f968c137192995fde305680518f8c09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e543c7606485dcc6428ebd4489795ea", "guid": "bfdfe7dc352907fc980b868725387e98c6b93e7bf2c88e6b82f1c98b14b91dda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c681bef0177740bb68142a0d9e2cb86c", "guid": "bfdfe7dc352907fc980b868725387e98e48bd6ce926f8ceecc682684cb4cc5bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ea8ca5398bac59f2404462480b001b0", "guid": "bfdfe7dc352907fc980b868725387e98633ce99d84eaddeaaa41b9fb572a1142"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7ba6f6acb5f8336a727f8fcda040c37", "guid": "bfdfe7dc352907fc980b868725387e9880a7e8b54de8c2a3a9699bdf878e7852"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb08d3e075b45a243095d89b2b9270ad", "guid": "bfdfe7dc352907fc980b868725387e98cbaa2a7b49f8bcc88eec28bc9a6f2088"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986081d3515c949d664ddb5609f70121fe", "guid": "bfdfe7dc352907fc980b868725387e98fa391f62380a97ca4d84af192da01a8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f261dfb5d1d3bfe2827a6aa2794e6078", "guid": "bfdfe7dc352907fc980b868725387e98a923cdb7530c42be95ec3e889a9fb43a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d520d1bd0c8b69db0c64d6dbc8ddf16e", "guid": "bfdfe7dc352907fc980b868725387e984f2fa4fff0e02aff0b1f73d60e58c992"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c149774ef37ebdf2cefecdbfdc7fe394", "guid": "bfdfe7dc352907fc980b868725387e9873879b8e77aca42c5e8ed8d49e38a694"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c652f3d556f4e9de3cb89b4dba40131e", "guid": "bfdfe7dc352907fc980b868725387e98e23b3acf55d8aab48eb10de7b23beb49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809b56ac6cd984474a2eb26f6a0d6352e", "guid": "bfdfe7dc352907fc980b868725387e98d2e0129d3d076c87827a61193033a54a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd22b8991e30830ec7aadd69080d796c", "guid": "bfdfe7dc352907fc980b868725387e98fae58f77b55bf9fa83fd281a1c4d2a5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cabc0a0b5bb40da72941aa9f6fbb8756", "guid": "bfdfe7dc352907fc980b868725387e989c0398f3a0be3c8f3b4d8ed4cadc1738"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837a41a5e47dd2cf7c8f5aff50831df1a", "guid": "bfdfe7dc352907fc980b868725387e981e34bcc4cd5befa224ffab8f5455b0ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98575bbb53dec11c034d474ddf6ccbfae0", "guid": "bfdfe7dc352907fc980b868725387e987b170cb4595a4ce8a094e7ba3f0bc154"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b9c0b730e3f64d4b5bfd716732dc255", "guid": "bfdfe7dc352907fc980b868725387e986b319283bdd5d769dd83c33af438cb24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3082e4391d1a65d3cad15a14aff3df5", "guid": "bfdfe7dc352907fc980b868725387e98248db75e9aea3c0d6598fb6608a9d8c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b50991e6858c4ee886f8fcbe96d86b3", "guid": "bfdfe7dc352907fc980b868725387e98cc023fae4a8538a641fbc62cd168b38c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dde4ac0b67655cbe5f3093793db15b1", "guid": "bfdfe7dc352907fc980b868725387e98b3334c9c3f168bbd29d5a64bc3160c33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d36e0bb6a46493fe1f037b1e7eecb8c", "guid": "bfdfe7dc352907fc980b868725387e98dd32511e6b56f234e3f3877c2a65c3d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982da887d205173a2485bab0fdcc41769d", "guid": "bfdfe7dc352907fc980b868725387e98ee6f31e145937ed28518b279de1e3b35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989495936f28609a11e02ed71a7d85cb99", "guid": "bfdfe7dc352907fc980b868725387e98376cc849599d2f6645105508dc70d46e"}], "guid": "bfdfe7dc352907fc980b868725387e98831274f18504116851327f8e4c95ee44", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e980b6199606e95234c5e742f36962785ee"}], "guid": "bfdfe7dc352907fc980b868725387e988d1f0248481aa6d0f905e879b12ad95f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9830cf9c6e24ce887c70b771fb0ab10d58", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a41ba860aa6fc56673ac239987133d67", "name": "FirebaseSessions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}