{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833364e8c4ac8d7936a59053f31ed39bd", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ae575e705f0dd6a30aa7f539d3b1fda5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ae575e705f0dd6a30aa7f539d3b1fda5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9895edba9fba39acee492b26bbd31f0811", "guid": "bfdfe7dc352907fc980b868725387e98451271ac92a2a6b3bc220a26c9165552", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f895d47362c6928f0bc82102f81821b3", "guid": "bfdfe7dc352907fc980b868725387e98803fd33d35f2b4a487541d8ec4e94db2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982247ee87f9ef4fa2abe4bc8081c74d36", "guid": "bfdfe7dc352907fc980b868725387e98ca13dab421a323308e8ddc6cdb237977", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854514fd54d4eebe38f9359c25dd44a38", "guid": "bfdfe7dc352907fc980b868725387e9855863e1daf52a474496c62b8e26b1623", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dab6ffd4be330f49945238b6670e30d", "guid": "bfdfe7dc352907fc980b868725387e98fbeb2ccbdccf43c8c39119e52627393e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98468a4acd19abb12b2a8452d317d5c18f", "guid": "bfdfe7dc352907fc980b868725387e98de1977ee82e83b08cdd262f30ab1e546", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98875fa9c4d5b1a3f34dae3b13fca1af8c", "guid": "bfdfe7dc352907fc980b868725387e98f2ff79ac1b761f0f79e693ae36952994", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f80ea3dacd327bd5cc6e79235df7847", "guid": "bfdfe7dc352907fc980b868725387e98f1833bff7e312b1000761b520b229d5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820f712465ec96cb79479e2db28b83f11", "guid": "bfdfe7dc352907fc980b868725387e98b7a704666dd231f45c8c0c5039cf962c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f44a96c464f8f6d4e3cc4e9f3a13bd2", "guid": "bfdfe7dc352907fc980b868725387e98d254ab7cac101db2e9608be9268e33df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830f31c1ca6b666b4fac741512638cb62", "guid": "bfdfe7dc352907fc980b868725387e9841995aad82cee4f0d1af9a0970f1d9fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d02141ddadc0244e53978b1576c60802", "guid": "bfdfe7dc352907fc980b868725387e98f6a6e186019a7b6bbcf7edf80d739ab3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a40eb7bc98d7aa132676bd96b0dd9a4", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b217d9bc33bf3b8073fa795c9b3dfe86", "guid": "bfdfe7dc352907fc980b868725387e9878bf6ea35872ff69d8716d14e8726fb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bdaf52cce6e08f3125a89984647be5a", "guid": "bfdfe7dc352907fc980b868725387e980929dec59f51f260fb14375cd3aee123", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fce93fd62a8f051168b5c831201754e", "guid": "bfdfe7dc352907fc980b868725387e98978956dcfaf4dcaa425b470b0ab6591c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852205bd6dffd4a818852d4d7ec254955", "guid": "bfdfe7dc352907fc980b868725387e98d3e49fdf20728c8da29875785f93fa34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98139165e08fb30d83b9687d884c862688", "guid": "bfdfe7dc352907fc980b868725387e9875ed083cdffa950109d382aa9bb635a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847c9db12d31df177e30df9ff9b3064c9", "guid": "bfdfe7dc352907fc980b868725387e98da892b09f0181aca06faf2f98c1b6abd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866c738021aa720e725568bd40d080cab", "guid": "bfdfe7dc352907fc980b868725387e98e03749eb0dfd6a1bd972248fd565e7f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890923220f1e4c704fe5ce0546853103c", "guid": "bfdfe7dc352907fc980b868725387e98ba32cbf0b2f954399f58199c72f84410", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa2a3d2627cdbb543d9d03800687eaac", "guid": "bfdfe7dc352907fc980b868725387e981ec4f2ee3baa2378c72056f6d51532bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985190959dfa32a60cd1274584dee47845", "guid": "bfdfe7dc352907fc980b868725387e981f1bb3e1716086026593817395ba776c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e81a524fc4bc00fc4a49298161f7763e", "guid": "bfdfe7dc352907fc980b868725387e98c78b17d465f66fc46ff613f796c4b8e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c1cf5e3e374bed80c48ca47d7b9c557", "guid": "bfdfe7dc352907fc980b868725387e98e2fdcfc26cf4ff69bfe0979351b649db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4075965443fbcdfbc6b1334e887c83f", "guid": "bfdfe7dc352907fc980b868725387e98d5c7402eee4379664783cc3196df872b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831e740fb692904b89752f7c0c6e051b9", "guid": "bfdfe7dc352907fc980b868725387e98f2b049120e51de6572d77eb4fa27601b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb06d79e2c48461d9863fb9cc28dcba0", "guid": "bfdfe7dc352907fc980b868725387e98470c0a98e19494a4f7a55af39256cbd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac868b30884a6d735c5b4af0bf4cad05", "guid": "bfdfe7dc352907fc980b868725387e985e260f4f5b1601c288ba7f6d58a3836b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852d1c74d57cc51fb97f884198e9fb368", "guid": "bfdfe7dc352907fc980b868725387e980a606703b1dc9d426d651976283c7afa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e9d88e97986516978237c807aa61782", "guid": "bfdfe7dc352907fc980b868725387e98794fa1083299c389c3e06b1da4679eb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850cf25037b287149ed22a6dfa0112436", "guid": "bfdfe7dc352907fc980b868725387e984f81bf588b7132779cfda37357f5988a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5d02f6adbdcfc6c44de6dbc40d390bc", "guid": "bfdfe7dc352907fc980b868725387e983b380180ac8372c476a807b9287534c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a46d351a964bb390255bd1b14373c00a", "guid": "bfdfe7dc352907fc980b868725387e9826dbc66437fe08e5f660cd7d57a07ade"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dba73e53ec864f646df9251f661cd7b3", "guid": "bfdfe7dc352907fc980b868725387e9848228779c89b7cf4c56f96d9771d2ce3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a389c147feed3bff063303977007e238", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821cd636d0ecf29d7224e43d7d91f642c", "guid": "bfdfe7dc352907fc980b868725387e98ff0bc9241cfa1703ee578d05d882114d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980195e50a570b494a25a9578b5e98b4df", "guid": "bfdfe7dc352907fc980b868725387e98dc21c349147484be8818b0c698c008d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857c5e002b85c1e6ac0af9d24a027b18c", "guid": "bfdfe7dc352907fc980b868725387e9854ae6e6291c1d1636792ade71a7bed6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6a0b9232e45615ad478270decf5b75a", "guid": "bfdfe7dc352907fc980b868725387e98a76ab6623a0005b7a05de9a09b4bbe0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a01cbcbfd2a60d81eeac0ee80bc926e1", "guid": "bfdfe7dc352907fc980b868725387e985233b06bb9e39497179a34ca3d93e52b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98476c0eea42237d21dfd9537672ec82d8", "guid": "bfdfe7dc352907fc980b868725387e98bdf453b73e17a86d84100cf68d60e04b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c38370e5c2427232833ab2cccaae7f7", "guid": "bfdfe7dc352907fc980b868725387e98700a094c3bb1e173435375a335e28e84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983433277d4faf2b5a609381bd80ad7db0", "guid": "bfdfe7dc352907fc980b868725387e981d3f412c09d65d91f50cc28e7385ca9f"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}