{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f02ed91c322663067e8db3c78ecc205d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ee255266871bb18a51e9313ce0a27464", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e66582ded712652caf262be75ccb67df", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9887f160f1fa33c5733a62a128bfc0f81b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e66582ded712652caf262be75ccb67df", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d85d5eb200a3ceaa4e1c015c0c1a879b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9801a3be7a2ba9cb031371f5ac56088268", "guid": "bfdfe7dc352907fc980b868725387e98fc2db623e1007c279a8785cb35652237"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986793b7264c793911bb59f32e1169c2a3", "guid": "bfdfe7dc352907fc980b868725387e983776c2b37474dcbdadbd3a752aab5435", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f078748dfec94b215afa099239b50e99", "guid": "bfdfe7dc352907fc980b868725387e98c044e892c9c8057328ed286a935b2ece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980de65ea8db23e90afad715c760ba82c9", "guid": "bfdfe7dc352907fc980b868725387e984d0bec1bca6ad3c56186ebfca5498cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f47d435c96d01399f683c073c93c4df1", "guid": "bfdfe7dc352907fc980b868725387e98f1ba4249ee5ab50804f784dd2261c7a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98113ea8e53ae749512c4c79c003fe6c8e", "guid": "bfdfe7dc352907fc980b868725387e989faba8ec5a8f704617ae31a5875479c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c7a4da3624ffd9d659b245e1622454", "guid": "bfdfe7dc352907fc980b868725387e980a7a944aa0ec03128fd108eb9f8adb76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dab7693bf5814ee88dead1b77673584", "guid": "bfdfe7dc352907fc980b868725387e9864e0cdb16b73f5df35ec54d2e6a57989"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ffd7135540a53893ac829ba5dd1797", "guid": "bfdfe7dc352907fc980b868725387e98f233f7bbc7977558e5efa894ff6389e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986482693fef9c17e5d3c6a1788004ad7d", "guid": "bfdfe7dc352907fc980b868725387e98e89fd09acb1f29af26ccaa8bfe38bf3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848b2c7a2bd25e23db9bd5f7e17aeba8b", "guid": "bfdfe7dc352907fc980b868725387e98d916c738434282a00db5bbf9f07df1e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b771c7ba37f919089e936e50dced9fa", "guid": "bfdfe7dc352907fc980b868725387e98a35614b98d4e7094ad249b6eb2556828", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a134597161c053fd3c861e1d22311696", "guid": "bfdfe7dc352907fc980b868725387e98185107bbd4d6379d246941df8aa1690a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8dd1485c7098822c6edd6720e641522", "guid": "bfdfe7dc352907fc980b868725387e98996e5734ceec53cf7dc2a28522878dc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e0db92f81bd1e1edd0be144e39eb020", "guid": "bfdfe7dc352907fc980b868725387e9854d03cd084171d6d780be1caa7706cc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2fcc46f8feed4f14e9f84c69f76a1b6", "guid": "bfdfe7dc352907fc980b868725387e98ce256399ac30fcd1d052306f31a47190"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874cd0c3bd65b6a6f0eb84b1bd072e38b", "guid": "bfdfe7dc352907fc980b868725387e9812d4a202db9eac238bc27835e002489e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7e2799cf94d61b1f2967b9aaf2ca1c6", "guid": "bfdfe7dc352907fc980b868725387e98f7ae6814fe0731eba58143c1a65cd450", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e133bc50ce7cf9a1215c6d94948b16e", "guid": "bfdfe7dc352907fc980b868725387e98b3b982c3bcb0f9f893851633cea2d862", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f5681095b67b7afacd160c8809294f8", "guid": "bfdfe7dc352907fc980b868725387e98c82aba84a3798d98d0f475272511be49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980889ee3dbad4ab62d5c68894a837dc2d", "guid": "bfdfe7dc352907fc980b868725387e980ac68e784bdc38e95fae4c0523b57112", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893834f032a16fa653df787ea091bec64", "guid": "bfdfe7dc352907fc980b868725387e98d090d1858c6942518e0ada7bba2a7eda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c95c67a38ff3939dde9cc8679b151cf7", "guid": "bfdfe7dc352907fc980b868725387e980a682b2d2390cee57a03dc38513f21dc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98abce9e5cef852a60cfc3db3e4c31f513", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986620472ab3866315c41bb7f159b0cfb7", "guid": "bfdfe7dc352907fc980b868725387e982a0f17fb5d38d312f647d6f48f5fd697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f474bbb186aae894081e257d18097eed", "guid": "bfdfe7dc352907fc980b868725387e983924a4e1312f9722adf4378320cfafc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98728e4e9b51d8ebac89fcbcc02d95c9ca", "guid": "bfdfe7dc352907fc980b868725387e987dfc1bee847e6cf6e9b3e6d3d445bd1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfaa68449b836c7fc63c56d556ae0f99", "guid": "bfdfe7dc352907fc980b868725387e98b833b00fd94a8cb73931d725b75ecbf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825feffaff14130386a45408a8f05928b", "guid": "bfdfe7dc352907fc980b868725387e980eaceb6b275d29e6039c10b0ba0f1771"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d799d028311a2918b9c3956cb4df8b8a", "guid": "bfdfe7dc352907fc980b868725387e9845a854d9acb09bd1fef202a4579175a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b481492c0f3eddb95fc5c255bed6834e", "guid": "bfdfe7dc352907fc980b868725387e98d54c9ce5ce3deb744cc2a580bafaf429"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b824991d985be935413464d547c23ffc", "guid": "bfdfe7dc352907fc980b868725387e9812736f85e9742999a23d14b38e6d8f3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844e709f700db4c7af30ac2a65e08d5a8", "guid": "bfdfe7dc352907fc980b868725387e98d78e19aa836a69e12343072d3b988f8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984944a7a60c1b21cb6899efd40fbf5bf9", "guid": "bfdfe7dc352907fc980b868725387e98e8b67963773dee8802c29d4248d6ee4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c0517c5273f2f17fab2292b2a9972f7", "guid": "bfdfe7dc352907fc980b868725387e98a17cae14db32313d8a98532a5405de21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895f6ad25daaa7cd8d8b386d337fb511e", "guid": "bfdfe7dc352907fc980b868725387e98e1634260392e1f861da3432aa99ee6dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989870091a66d1f3e83149a7bc91b23ed8", "guid": "bfdfe7dc352907fc980b868725387e989bbe9cc309e4c6ac48385627601dbdd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f29487667abc192fbf052e3849ab79f", "guid": "bfdfe7dc352907fc980b868725387e98a562070a3b23eb4bb611f2eeabbb4d35"}], "guid": "bfdfe7dc352907fc980b868725387e98a67bb654ab2813ef8d1d9372857d0526", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e984ee98b90a4db1f92eea94d575fe42efc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98978e6e5f352bb4fae445c10c8dc74d2e", "guid": "bfdfe7dc352907fc980b868725387e98b5765d0d675b8ea734f22c32930be2b8"}], "guid": "bfdfe7dc352907fc980b868725387e98443b8b4083a2b99b4bbb69526646a2fe", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987c053a597f7e0259ec5e231b13ba5c1b", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e988c713f16aabb416caffa17043d05b9b0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}