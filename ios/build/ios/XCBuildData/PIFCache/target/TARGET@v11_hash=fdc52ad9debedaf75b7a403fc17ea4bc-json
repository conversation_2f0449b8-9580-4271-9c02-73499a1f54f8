{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e40d3b0f1c50a9adb45a0554eb802f2d", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/google_sign_in_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "google_sign_in_ios", "INFOPLIST_FILE": "Target Support Files/google_sign_in_ios/ResourceBundle-google_sign_in_ios_privacy-google_sign_in_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "google_sign_in_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98638b58f0402fc589ec586b82462d40bf", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983fda8952f1ae7a6d0a22fbef9a77b109", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/google_sign_in_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "google_sign_in_ios", "INFOPLIST_FILE": "Target Support Files/google_sign_in_ios/ResourceBundle-google_sign_in_ios_privacy-google_sign_in_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "google_sign_in_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98c5b45dae2c0203d7613c9d42fd31de55", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983fda8952f1ae7a6d0a22fbef9a77b109", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/google_sign_in_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "google_sign_in_ios", "INFOPLIST_FILE": "Target Support Files/google_sign_in_ios/ResourceBundle-google_sign_in_ios_privacy-google_sign_in_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "google_sign_in_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98bbcdfad0ae3e5a0aadeab8c3aeef6cdd", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98787addfdf7292cca0b5bda2af75b4dc5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9849f7db01bf396e0dfc9d84913ad6b2aa", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d191a182ef1f2c25ad1d6a58f61b88ec", "guid": "bfdfe7dc352907fc980b868725387e98d0721e06e695e2c0b29799254ab51f49"}], "guid": "bfdfe7dc352907fc980b868725387e987bffafac9863f4fda3f0db6148e3fc01", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e983c7fa4bfabecc448c813cef02922c100", "name": "google_sign_in_ios-google_sign_in_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983b0eb6bf04eb9be2ba23f1911e7be724", "name": "google_sign_in_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}