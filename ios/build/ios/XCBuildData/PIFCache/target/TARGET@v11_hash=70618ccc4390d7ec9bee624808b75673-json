{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98997a81afb90957242bb269e5dc5ad84f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984787c4a613a81d6957d5ef52f9a04c9f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989a3b6e7da218339c4bfc707b44d4b671", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988ccda092c598f6e6b272451cf1e9593c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989a3b6e7da218339c4bfc707b44d4b671", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9834cbfab38ae2e9035bfd2aaca70f5c35", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984fb96aa61f86e6eac33adbd0cbbeaf3d", "guid": "bfdfe7dc352907fc980b868725387e98939b588a1f1afa1db1959c3c8ff72d8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821ae2e3b5e7543233b3ea0d22b744464", "guid": "bfdfe7dc352907fc980b868725387e982b925b49c679bfaaa70206cbcce620a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f325a34d8a5056a5c8f4ac00abab77f8", "guid": "bfdfe7dc352907fc980b868725387e98963d197d5cafd0e0ef960fdf430d6d8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898a98039416d817543680e27a3a4bcf6", "guid": "bfdfe7dc352907fc980b868725387e98aa7d8c730517b2bf29d1a98de6a725db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd7c9f6c04be1b0e600d0f5ed103fbe2", "guid": "bfdfe7dc352907fc980b868725387e987d19bd01a6497189f499f2f171c08e2c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6a1ac498e91c40b56d17b619d8a374a", "guid": "bfdfe7dc352907fc980b868725387e98c7431ee2c35a6700eeb7c1f3b060f01a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ba48fe0330a62fd7d16442bef0b97d8", "guid": "bfdfe7dc352907fc980b868725387e9805e7fc477f4f13da74d279bf65a4e529", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868af62d377cddc1d3b54c73131588efe", "guid": "bfdfe7dc352907fc980b868725387e9896490ff3ad27fc7b4fedcd0204dbeb67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862777aba1cac8d87078a873e9bc11814", "guid": "bfdfe7dc352907fc980b868725387e98d67b65fa321fa1e3a5ef4d2c534fdee1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7cbfb30702b3bc132adbf3965e523bb", "guid": "bfdfe7dc352907fc980b868725387e980db7e68ab00732d91c1db53afc9cc9d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb54864100929b05e1277c102ae8ca77", "guid": "bfdfe7dc352907fc980b868725387e981cb61ae6bcfa8935b8652caf635768fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5f039320a387d6b4f64d36846896fa6", "guid": "bfdfe7dc352907fc980b868725387e989e9f55ad35b1ca829378818a4c3b055f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980741c23b4a9e57edcb75d20aa08bfb12", "guid": "bfdfe7dc352907fc980b868725387e98a244804c69a17fcd16a7f1462c4b4d16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b3dff5495023f061adfabdf4dd5fd4f", "guid": "bfdfe7dc352907fc980b868725387e987dd19f084fa98286496e541a6b26ec86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817f9dd454ac1c1ddceb7b7f842ce2c57", "guid": "bfdfe7dc352907fc980b868725387e987d251f742deac78778b67fb9af6f55b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddae099aef8ae3c3a0f8e9cad58611ba", "guid": "bfdfe7dc352907fc980b868725387e98158f6c3e1c33073bbf2a1c8ed3c584c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b45298c3ed90c4b7f928f0e47fb678e6", "guid": "bfdfe7dc352907fc980b868725387e98f68fb0b423d21643aeced4857a800a74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989efd045c9934233b936b23340e5f85a4", "guid": "bfdfe7dc352907fc980b868725387e989317d23247247a3b154e0f14932234bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980267ee15e40c70a3851eb6121c590aa6", "guid": "bfdfe7dc352907fc980b868725387e986f6230dac13f6dfe9e2cd497d917d5a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98541e8df6f0f0b72bce78b81ef3a4c652", "guid": "bfdfe7dc352907fc980b868725387e9868fe2c5d68409caa2d5dab36e4cd6e95", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b69f71d5e0f4fbaea075ce29ba124185", "guid": "bfdfe7dc352907fc980b868725387e98001bec5a875dd7efe532913ab1bf3bd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a497e4272ada564b85e25fe0cebdf54", "guid": "bfdfe7dc352907fc980b868725387e987c8013360a255927234927eeb40b893b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982466734cb6c49d59156d73b0adccdacb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e193b4fe085dc3f653d998935d0e6cee", "guid": "bfdfe7dc352907fc980b868725387e9851b770501e708698295cfe049ec50e1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbcee17c45b30fffba02ee7b8e193fd9", "guid": "bfdfe7dc352907fc980b868725387e983e2942e47c6481ea46ae2679f2c64906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982704079faca381392cab310d389b2b68", "guid": "bfdfe7dc352907fc980b868725387e98bad999f5d4e896fb6302932749390bce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986aa7dee2281d126a5fc7b0f36e6a3620", "guid": "bfdfe7dc352907fc980b868725387e98e1d4a3adbc16583b68dad4bb72f39f89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986984653b002b933f1ff74cb04d9e5a9a", "guid": "bfdfe7dc352907fc980b868725387e982ac90d5d8c13de719b43fd2fd644d8c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98835ad773026c1c9ced33811586025660", "guid": "bfdfe7dc352907fc980b868725387e9899c8ab1b49196c3cc3872fd1f48d5a03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98763108a575045838ff77314f2fda2d2d", "guid": "bfdfe7dc352907fc980b868725387e9885deee80b1ae0236937e8f89ef3020ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dabff44d1b39cad8f4c5a24fbc1be35a", "guid": "bfdfe7dc352907fc980b868725387e98cb902589e11a1247dfc49129be63dcbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98875ca1e0d6d7a7940eb3f2df2900085d", "guid": "bfdfe7dc352907fc980b868725387e98e33dd05c5556de40d33d713acf137f2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8056f3d625b3e9ea8d39ef69cf61231", "guid": "bfdfe7dc352907fc980b868725387e9863a8d6d90249ec96aac41eba61a64b07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0ce7aab677348e9215c1aaa1465bf06", "guid": "bfdfe7dc352907fc980b868725387e9800c3ac4d85f7cef0a170d16a86dfdf91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa3ec7d8796bde5995440f6e59797666", "guid": "bfdfe7dc352907fc980b868725387e9803a9b95dae7005c966332ba094559474"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f68b940b117857e46669af3b774a6790", "guid": "bfdfe7dc352907fc980b868725387e98f328cd49978dd6ba029de7e0885682e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca10ba84a7265a10313f43719186e17d", "guid": "bfdfe7dc352907fc980b868725387e98b481ad52d59868de06acfb724879a30a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a7ff67a7276d506073490f39eb5e54f", "guid": "bfdfe7dc352907fc980b868725387e98a69e85f153b07a3a8da677466605b5a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ea4338188b5226078829733e8e73eaa", "guid": "bfdfe7dc352907fc980b868725387e984829f51b7003b054094a97e7ab80f8bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986415322aebdeecce8b28d55634e199bd", "guid": "bfdfe7dc352907fc980b868725387e98f96bde3ba17680a54a0cb92fb11837eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864a8610dd2c4a2b644302fb7adf4f56d", "guid": "bfdfe7dc352907fc980b868725387e98000c57a6151c023b9d485797f93a2145"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899ffd9224a5f28e90e9732c0165af7c6", "guid": "bfdfe7dc352907fc980b868725387e98867fec9aa364d7769568fcbea84236a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843402adc4a896098d7f3a59638ddcfa2", "guid": "bfdfe7dc352907fc980b868725387e98b6c42a59e6f5f7ee4464cf3675beadcd"}], "guid": "bfdfe7dc352907fc980b868725387e98fc58c78998af5affe59fbeea0a9142bf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e98c60e7851b3da18381d1cee79e27a2eec"}], "guid": "bfdfe7dc352907fc980b868725387e9812284bd69c7e52c83b3f2c6af26062fb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9865dd56e8801c5297686a8052d83bf74b", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98106c6989d58b939167663f375a5722e4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}