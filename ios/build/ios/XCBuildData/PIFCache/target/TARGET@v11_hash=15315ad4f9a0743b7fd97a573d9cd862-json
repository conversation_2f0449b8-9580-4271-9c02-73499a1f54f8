{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f7f232ac9f9d2102169eabb429c57ad", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9890d6696da75756086ee7e4107767c249", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e87d30d6a9a8ecd7c26df5089738d958", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98efe5ba9774d0c4c9abc45a4ad1c413dd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e87d30d6a9a8ecd7c26df5089738d958", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9850ddc6d27f2c2b9258ec6cebe0f6ab0b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c8caa17a37095d9a3344850a35f3c9c0", "guid": "bfdfe7dc352907fc980b868725387e98ce3739d8e1d351a30995e088a281d625", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9882a4670f8433cccd1b11ad592dc5d408", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985b4bf74e29b2631d5b0883db8ee87d0f", "guid": "bfdfe7dc352907fc980b868725387e98203e4cf22e10cf53332dec1a3ff791d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98831525740eebf9fc160f295549ea281c", "guid": "bfdfe7dc352907fc980b868725387e98de0c470962d24e99a80148ad39a9e79c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822eb3e3c479dbf5bd6227eaff88e95ff", "guid": "bfdfe7dc352907fc980b868725387e98a518c4e6873341eab3b89aed9f8e6d9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987998ca5437e591d0336a4a3545a134c7", "guid": "bfdfe7dc352907fc980b868725387e985a790738f6cdf2062b6259df3f40d9f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ffcf9940ca5f49bf97b744a99aa74f6", "guid": "bfdfe7dc352907fc980b868725387e982df999a4ef467530d7564b5206507f87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829d358870879e609deacde293e74b13e", "guid": "bfdfe7dc352907fc980b868725387e98d2dd7edf2a4ea33735101d45c9b0af8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f89c671a3f4edfbe19cb8fa173ab660", "guid": "bfdfe7dc352907fc980b868725387e98f13d26a38c6276c11103bbe2c5e40bf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9c7995431338c0b6f4a0930b66d1cc8", "guid": "bfdfe7dc352907fc980b868725387e98889532a57a740b0e24f9cde15c1f9e63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f699b26368b5ea45c925b5ee6f28ca15", "guid": "bfdfe7dc352907fc980b868725387e980dd549d8b2857d95b68fb089bf5a3114"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848729ee82aa9152f7259ba1ec0679218", "guid": "bfdfe7dc352907fc980b868725387e98e118c836fa0d70cc416cb7900166cc7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984692d257df83f22894b9b2dec4b4ae28", "guid": "bfdfe7dc352907fc980b868725387e989ffc9d4bcf5e0ab6e9850431c9e71fd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832edd16034b40b9fe9b6bdb7cc44625c", "guid": "bfdfe7dc352907fc980b868725387e98f91dccb03daffb24b5a838732d924129"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4d2c9243165b9c6afcffcf04edb4d2e", "guid": "bfdfe7dc352907fc980b868725387e9827ee21d71508bd5cb5c0f6ea0306b421"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98605612a05f5a0932d2e0ba5cd2004aca", "guid": "bfdfe7dc352907fc980b868725387e988df6f357dc03dd0557e329df48e93722"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed46430509e54362057a3d1faa0f1b04", "guid": "bfdfe7dc352907fc980b868725387e98eec1b3b62894d752cba114c856eb8383"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896ffb3495c8df28bf84b822117dedcbe", "guid": "bfdfe7dc352907fc980b868725387e988af4fdb54bb363b582c723f529221bd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd4a26edb4f40436940f7f3b3b957493", "guid": "bfdfe7dc352907fc980b868725387e98905186d6213d2077fcc80aefccf01b61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a82d6722ca3ec81b0981b67a6c4fbe7", "guid": "bfdfe7dc352907fc980b868725387e987bd19faaae2c37a34712fcef2946083e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98830e8d580dfc4496fa1e9e0c87fd80dd", "guid": "bfdfe7dc352907fc980b868725387e98609ee773f5a49f0ea8c9fc4d1d646139"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98610539cafcd5f3196f0ce55bb7290f3f", "guid": "bfdfe7dc352907fc980b868725387e98b3ddf909ad2307dee49b2855ff7920bf"}], "guid": "bfdfe7dc352907fc980b868725387e98141d740f5193dd89106fc9155950c175", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e9832725fcd36ddb446655248eee8fa148a"}], "guid": "bfdfe7dc352907fc980b868725387e98529ad1249f20451faa2537d1ee87cc0a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a65071cbbf82a5b80bfbb724cc855b4d", "targetReference": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3"}], "guid": "bfdfe7dc352907fc980b868725387e98df87b1e93233144e0c60a3b9e2282f0f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3", "name": "PromisesSwift-Promises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982bfe7b75487d9ef7158f28fa2f89d57f", "name": "Promises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}