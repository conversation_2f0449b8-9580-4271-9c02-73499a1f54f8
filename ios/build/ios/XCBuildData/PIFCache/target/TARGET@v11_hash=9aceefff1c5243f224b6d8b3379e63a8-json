{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98699516f92fb441a04823da54c72a2d5f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983bfdcd63d424e93c84356832476ff988", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983bfdcd63d424e93c84356832476ff988", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98932afa9071c313f9885653cbe66e75c3", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825477e4d68a749bb731671fe14d6a350", "guid": "bfdfe7dc352907fc980b868725387e98006b3146ab5490bc86ca3be65611a065", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854862052dcb8e52ae6a4d6bf4a42c28a", "guid": "bfdfe7dc352907fc980b868725387e98bccef5af1b09c4a49de4d5d608c78676", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8dc6da9e0b23f81909b554b9b64b6ef", "guid": "bfdfe7dc352907fc980b868725387e985e8a429d7f56e949832c52afdc1679a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3281523d8e05d41cb4b1f5deec9bc92", "guid": "bfdfe7dc352907fc980b868725387e980dabd741d5775b6deabbae6fd8a43605", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e43b7ac40d47f4583f8cf9b20fd1e470", "guid": "bfdfe7dc352907fc980b868725387e9821cb9c1e5d86eb8bc1b6b250f2a6f991", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2b0212d5395b6a2079e94bd33b6e014", "guid": "bfdfe7dc352907fc980b868725387e980a2ee191da4c83568ebed383586563ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f18ff4a3657325b4310b1bac4b723dd", "guid": "bfdfe7dc352907fc980b868725387e98180edffcf1975940f5072adeca8031e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d59332d56920b047fbb6015274e5d089", "guid": "bfdfe7dc352907fc980b868725387e98f587849874d04fdd51fdb8c7b963eba8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dc5905adb1532cec639d45072e5c008", "guid": "bfdfe7dc352907fc980b868725387e980c0e18cc5352199278add305be614750", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efa03f302ec80fd7f69faa515612e62e", "guid": "bfdfe7dc352907fc980b868725387e98695029e1c8175aebd4b553a08b985eb5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885bab115a4244a0d879f016cc1d7f34a", "guid": "bfdfe7dc352907fc980b868725387e9837e61c1fba7b09dd7acad02c42dc194f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f33b585bdbb8d8919702b1cd72effdf2", "guid": "bfdfe7dc352907fc980b868725387e981ed4b1a915a88acebf3dfdf475a6b1c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ed71fba1ba93fd80e68476d5738351d", "guid": "bfdfe7dc352907fc980b868725387e98bf559eabe688555e80ca3f4eb34f9b37", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9872d0212d60204c0b9ad13a5f1fe558b9", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821e57beb7e7a1cad714034df8c757517", "guid": "bfdfe7dc352907fc980b868725387e9824c7e0d11f290264285f7a7df41f85a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984babfec59f8cb078a8c17720a706abe1", "guid": "bfdfe7dc352907fc980b868725387e983718fe15f400feafaac9115caef64a60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cf1353c8bacac5984b4621991877e03", "guid": "bfdfe7dc352907fc980b868725387e98463b87160e9ccac58242a27a92856881"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988782ffe10903151a7bd5d0c70b1e5ba0", "guid": "bfdfe7dc352907fc980b868725387e981d853c9fbaa9dceabf10fd7b9f63b821"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836b581f293ff3110329a7cba85012d71", "guid": "bfdfe7dc352907fc980b868725387e98561d01fb0d390242c926fcf74260cb66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e189b437ff386b5ca1a640ab3d7c429c", "guid": "bfdfe7dc352907fc980b868725387e98d770dd4301049b109ac92e62589c4bcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989542427b3781b12d24e5de9cb111c258", "guid": "bfdfe7dc352907fc980b868725387e98c3e873c1225f5979604cb228e7e0b89a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860fa423b4dfadfb0fd534ec88f7bf58a", "guid": "bfdfe7dc352907fc980b868725387e983ea22f9b4b52272414378d16c7f59be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad7aa7f514626f7aa693a09b91db6903", "guid": "bfdfe7dc352907fc980b868725387e98622ff0f9461364bf79a2eaa9dcb4927b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad2a7b7c08f130787ae614a9c32d64c1", "guid": "bfdfe7dc352907fc980b868725387e98a56126068b8944efa657c4be6e1d364f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6642d5542d582bc7258d7f89198b2ba", "guid": "bfdfe7dc352907fc980b868725387e98123f07f2daa8fe3708ec5e1d3cfee5d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98340fe6e4136e3809ce32740d31031476", "guid": "bfdfe7dc352907fc980b868725387e985580d404eb55d5b95697e53623fdb141"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}