{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860fdeca1f08ade18526e5d3970650a14", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980d3926596ad3cd9c175f9e28b372b54b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a390a1c3cc53f46bd25cf88467511a3c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98288f36904c064e8ffee3307684f0adf4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a390a1c3cc53f46bd25cf88467511a3c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ad21424d04da79e5b6b09fe13bb9d64", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98004f716ca7e530fd6d38209684cc048f", "guid": "bfdfe7dc352907fc980b868725387e9834fb19e066e389ba3c0f8d38a17cff7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987119be04bf3f66ececfdaec849e5a2d5", "guid": "bfdfe7dc352907fc980b868725387e98d59716ad7b9da88be8e56e4bfff1e3b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865073cc42bff3d5199bffe9526b6dc88", "guid": "bfdfe7dc352907fc980b868725387e98e604de7d790ba1831021f132e5f261b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb5160f47aaf8039e31af34d37ca7569", "guid": "bfdfe7dc352907fc980b868725387e98f5f3a0df469538be3193c30a03e34d14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856f72f82e7c519b4c26ecda7982bdb75", "guid": "bfdfe7dc352907fc980b868725387e987d380c2f23d5712bbce873aa159164a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb40e3bdbc50181268f682c8635e6613", "guid": "bfdfe7dc352907fc980b868725387e98e27fbf10d3066ad052c52355adcc4268", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adeff30a2443271ad4bcdf5142bee65d", "guid": "bfdfe7dc352907fc980b868725387e982946f2b4d21f3f9ed2911f54bf945753", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a683352d2db8bf529b53ffa3a54dd960", "guid": "bfdfe7dc352907fc980b868725387e9839a388f49ff9b69d89da3ab0a64603c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f508984b633b046272c87c1e78b80f0b", "guid": "bfdfe7dc352907fc980b868725387e98d38ad3ac18be107b80e1965955428542", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e26e48c144b8381f13c3dec4788e5739", "guid": "bfdfe7dc352907fc980b868725387e9805bafdc9473b938304dd72a689bebf4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eb84d8cae213ea00d133aa4e1c85cb8", "guid": "bfdfe7dc352907fc980b868725387e98d1e493c025639c5e45f8c77ff9c4e12d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c109d14a3cb043a89e4e2b1afc462e00", "guid": "bfdfe7dc352907fc980b868725387e985c0026149786f9b345ddb2d79d16f912", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ceb833b15968267c61ac84b41dd2458", "guid": "bfdfe7dc352907fc980b868725387e987a43cbfcaacff385500d55088d9e429f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d95f75df4c813ecdceda2e0435c44cc", "guid": "bfdfe7dc352907fc980b868725387e9866fd6de77940c6bff305dc8f4371b96b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed28bea984820e61a0a9e7c52e4a2053", "guid": "bfdfe7dc352907fc980b868725387e988082e29df518601422cd5c30d06e2237", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856edd1db84c73c4460dadbe96245f722", "guid": "bfdfe7dc352907fc980b868725387e98c657fea3a2efa6de4954af6033eda004", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837e01efc27507ae86e02831e24553733", "guid": "bfdfe7dc352907fc980b868725387e989a01f3d3aec6b136c5580b818c3cb73e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd0ba248f82c9eb7c910970fa073f8b0", "guid": "bfdfe7dc352907fc980b868725387e985c148c407d40ee242d8e6fdfd8357d19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835049c74d5f0ca0e44db804404d296c5", "guid": "bfdfe7dc352907fc980b868725387e98908882c9d60c801228a8a63aaafc67fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d404e3bb4d73b77fc314c514a3a95adb", "guid": "bfdfe7dc352907fc980b868725387e983945cc60718cbe08f93c527aff7cba60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9fe8775be35ee6eb50d7abf37931049", "guid": "bfdfe7dc352907fc980b868725387e981bfd27de7aa83d0852a1863eb82da66f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7dbc73764384606fb72ff303a986a70", "guid": "bfdfe7dc352907fc980b868725387e98c3f2d6df0915c9568b28f046592ed7ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d59d420deba2d0b6814e9a8611db29", "guid": "bfdfe7dc352907fc980b868725387e98e7053aae7455dbb89e224df996be2e1d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d6496d3aa56cd1627892b59a417d8bd", "guid": "bfdfe7dc352907fc980b868725387e982fc3e8de067cbbe8c722925423ecc07c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981664dfc7329363764fdacd5d07a0c8e8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98902cff6686c191595b8c14780d48cc5d", "guid": "bfdfe7dc352907fc980b868725387e984466b01a2e0c43767b13b7f01fb57e00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a328a19d7597a374963f0c5d4046e0a0", "guid": "bfdfe7dc352907fc980b868725387e983f78e485fb1767d169f47ef4ebd1ebcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a88db6eb9637f881caa8ad32d8f2d11", "guid": "bfdfe7dc352907fc980b868725387e9869907df7675f856abc0283d3bfe5994a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bab782e4271d353eb55bed78a098dc83", "guid": "bfdfe7dc352907fc980b868725387e981cb64dec2da7a76e7335d151551bff2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98242afc3e1e2b4d757ed183d89dadca7c", "guid": "bfdfe7dc352907fc980b868725387e98e4f9555c43341125ccf9ef4d67acdd4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a26e1b79bd1d0b844199fc6e629b9f62", "guid": "bfdfe7dc352907fc980b868725387e988185c27d6fb55acad08262323393e3d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca1a4cdf2a766f7a254f23ac1466565", "guid": "bfdfe7dc352907fc980b868725387e98bc8741640bde07f8d8ed53ea12629456"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b886d0ffeb38c1ca4ed1386766abd3e", "guid": "bfdfe7dc352907fc980b868725387e98678d16ede98e713310b070b38ab23c1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e66d4574ff1e91354b0507fdabff6144", "guid": "bfdfe7dc352907fc980b868725387e98ec949ad1d2a7909ed0c9b0275e111c24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d25ff4ba3a43df5ef238f76029e1fc2", "guid": "bfdfe7dc352907fc980b868725387e98a304593a63834bd83bdc5d94c5ab6892"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2153ba5a67d8c75c582aea03069112c", "guid": "bfdfe7dc352907fc980b868725387e98f8bdd266ac3a2ad8ac4a54fa67a645cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfd88bb84c732d0db73de3efa65515da", "guid": "bfdfe7dc352907fc980b868725387e98c201914027e76e69526e1ba1a1e8721f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984585c209243c54735165747a5ca3db1b", "guid": "bfdfe7dc352907fc980b868725387e98eb192609fecb224a80b2755043b09089"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98118b814f7f20921ee96b20f1856cc977", "guid": "bfdfe7dc352907fc980b868725387e980788a228f3be57a76e22ac2fc79bc671"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98732a1f4069f2469126e04e145c2e39cb", "guid": "bfdfe7dc352907fc980b868725387e987ae0d037dc2958fd0fd16d82f4bd6672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9f8f3215ba6efeef5de67ed3eb77679", "guid": "bfdfe7dc352907fc980b868725387e98b978ca5f45383247c29e5c5fb1457256"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882d83c8ad16482b95e245e61eec9fde2", "guid": "bfdfe7dc352907fc980b868725387e9866c0f13f850b2f5678b3ff54b3914dc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9f373a457000056dbbc61ed13f9a2a2", "guid": "bfdfe7dc352907fc980b868725387e98d51dfa5b839a99d154b38943e715a3ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6c8a2ba3890f8143841be99015a07cf", "guid": "bfdfe7dc352907fc980b868725387e98412e526c83b90975379eaa4393152663"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859ac53c5f3b9b51ec7d31e339236afaf", "guid": "bfdfe7dc352907fc980b868725387e98bb333c35b205bad380aec8e5dc557f4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1d386cf383fb778b152bb6ada5da145", "guid": "bfdfe7dc352907fc980b868725387e982cf8490a4194c2db7aea4ecdaf9d7727"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888e5193b64de54a1498ac827e7f93d06", "guid": "bfdfe7dc352907fc980b868725387e984f0a454611d5dce5b9287f0abaff30aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fab5048ad8ff1066e889ceb4dc109609", "guid": "bfdfe7dc352907fc980b868725387e9830d50d3b8dada408fbd5ac3ca1c576d1"}], "guid": "bfdfe7dc352907fc980b868725387e98339f3c5c18cf261c37731f1ff9cd3c6b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e98f76f428393514513ee220ee3cd45b4b7"}], "guid": "bfdfe7dc352907fc980b868725387e98cf9e1d75d6fd474380329cbf47d24d45", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98911b19d9ae38fc82e6a73bfd4cf29ba7", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98f5ee52bb1ad32275d40a0a37ff2d7e9c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}