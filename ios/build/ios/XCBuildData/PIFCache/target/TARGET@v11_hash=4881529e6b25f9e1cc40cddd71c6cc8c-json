{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b0d118edcddf887038a632d48b75a9c1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ffc65ec7f35696abb1770539b7dfbd31", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9841028089bc064e6f1195c6a52b9d305d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bebc72881bcd619e3b2edc767efcbbf2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9841028089bc064e6f1195c6a52b9d305d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9872f94b4827e8a198610d528b0add270a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986373e5c83da9061c051d28faf823de8c", "guid": "bfdfe7dc352907fc980b868725387e9820e0d40587d0a5bb6a5604c45bc9b993", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98868f7bd32fd29f4d292c188418c71ec7", "guid": "bfdfe7dc352907fc980b868725387e9884e13d350376fc35d5219e6bb439aa25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ac9ef79708388a8da0dab3a8dbba11e", "guid": "bfdfe7dc352907fc980b868725387e989ad91433d884b9c213677c268963ee3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d62c4a84b10ed4093e2aa1a210224719", "guid": "bfdfe7dc352907fc980b868725387e98ed3e96264bba041c7a4595f929efbdef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aa6f817c0d5f90251a201ba739528af", "guid": "bfdfe7dc352907fc980b868725387e98ee0d05223e2c0d4ce97a72e28c8a6497", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc2a8ab16f042352e0cc972c857de4e0", "guid": "bfdfe7dc352907fc980b868725387e9814d1be8e0a3a199470919df081b15cfd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0d92b046df089d96eae5a2aa6a64a6f", "guid": "bfdfe7dc352907fc980b868725387e98fe5af199d6db0e855c97608537eda6c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6e579636d9801143173331e9d068b9f", "guid": "bfdfe7dc352907fc980b868725387e98e6e80f3e764d022eb454892a4c9fa728", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b86046c2642045e86abaa72722e8ea78", "guid": "bfdfe7dc352907fc980b868725387e98e8bd0e71401aa33432dacd497fe27739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e10952036296490cc6cab181504d875d", "guid": "bfdfe7dc352907fc980b868725387e989828bf88d09daf8bbf14ff976a691c0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869f92fe6ad03874767889169f5ea0bcf", "guid": "bfdfe7dc352907fc980b868725387e98f47b4849da05019dd39a0b09b47b0c89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802b0b4bf6e3c5982074c277268f1f47b", "guid": "bfdfe7dc352907fc980b868725387e98353cdecc0c7485450576a95fb0d33728", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824f1766653e626a654d0fb28d239b449", "guid": "bfdfe7dc352907fc980b868725387e981b19cf965e7cc7a94a29cf0733c4f493", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb73741b2c9579d4479e2c9f645f7f16", "guid": "bfdfe7dc352907fc980b868725387e982652f7018687325a04ab001078ef4be9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b69d4a0507604f1b0eb214226b345c3", "guid": "bfdfe7dc352907fc980b868725387e985d4d6eb155c033a60baf5cea0a44c974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f46058771bf4c3e243dbfe4eab581d87", "guid": "bfdfe7dc352907fc980b868725387e988e6b5e06d4776011d4956411d3bf3fe9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888afebc383b239cff6b4e857e7897462", "guid": "bfdfe7dc352907fc980b868725387e98ab6dd433fd2fcce4df57344527dfb319", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989049e8aea775e6bb1932098071710614", "guid": "bfdfe7dc352907fc980b868725387e986e89964cf3778800a7d5a14e67b10305", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efbee4a0f9e848e9670a8fab5d475663", "guid": "bfdfe7dc352907fc980b868725387e98d6f4d0da7171a6382111fff0de0c56a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c2f127f649e8532a1d996b713e80aef", "guid": "bfdfe7dc352907fc980b868725387e9829b1f6827baf1fb7218f4802cecb01cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdb51665f9f2a33dab8151e977ee177d", "guid": "bfdfe7dc352907fc980b868725387e9889ebed65b4ae23a134228025a225313a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988019f34cc87acc919a40fabf0e83376d", "guid": "bfdfe7dc352907fc980b868725387e982063830ff4d6824fe9f9a7bc0c595a29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6771714951bf06f9cf9ee136d9aa481", "guid": "bfdfe7dc352907fc980b868725387e9833ab0cd003750aa67fb566a658c56d65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9c8579e9c67af430a46b8b0bdc60f85", "guid": "bfdfe7dc352907fc980b868725387e989a9a16f17b5444bca019bd09bb27f049", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f6859456e40e7c76a7dbfc1b69790f0", "guid": "bfdfe7dc352907fc980b868725387e98122738887acd728d3b6d744a49d0fe02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809562bd45695d9713b3c765261e87749", "guid": "bfdfe7dc352907fc980b868725387e9840af8352f0c4a60ceb7f4232659e718d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c99c008299c04dbbf778e4ff5e7d7588", "guid": "bfdfe7dc352907fc980b868725387e9867c61fcc66e8bcdbf4d10a488fc57d1d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dda8c842153944c299ce0c7d0f57cc7", "guid": "bfdfe7dc352907fc980b868725387e987864878df7f23859a6addacb1971d4bc"}], "guid": "bfdfe7dc352907fc980b868725387e985057cc14e2daf3db123f6c1e3072d603", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804185fc164a60f7a2324fdb5919d6acb", "guid": "bfdfe7dc352907fc980b868725387e984b402a23151fbf0cfc1ccd8d8d88b255"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809085e406634ac481434729e07874fae", "guid": "bfdfe7dc352907fc980b868725387e98e6d17e10ef415b2ec8a6ea1228ed7b62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cb5d5e578c663a3e2e090cc76c882ee", "guid": "bfdfe7dc352907fc980b868725387e984bd7754e42d9f1df9799cb8d8bc6f90c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f070ee393c0d4442d6934487e6dfd29", "guid": "bfdfe7dc352907fc980b868725387e981aeb68b46fdf6367ff5bbb8e4f926bad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b6782763cfa2287e6a9c2f915a519af", "guid": "bfdfe7dc352907fc980b868725387e98f86f414d1627adf6a9c6716f13e00de5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaaf9d0d36c10633c0a75c9aa101d1d7", "guid": "bfdfe7dc352907fc980b868725387e98b086301aefa68cc36c2896e03e9f358a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bf6c0b7fde001eebaf18332b3628872", "guid": "bfdfe7dc352907fc980b868725387e989efbbd68b50a35f7de8f760f5c3882a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814930722613993ef57e150152e117900", "guid": "bfdfe7dc352907fc980b868725387e9841cb266e85c35be85a30011717c78d6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987811730727ea5a626171c8280b904834", "guid": "bfdfe7dc352907fc980b868725387e98e9f4ffa8aaf8583165dcd027e7e3d959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98730e3709fe30e0eef0838fb29ae7581e", "guid": "bfdfe7dc352907fc980b868725387e9873dfee997476ed5a3264455aa18b4c79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98519614b9beca23b0bed7abd8ce79a39d", "guid": "bfdfe7dc352907fc980b868725387e982f13435f81ef5053eab3673a076586f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807eb59591f47a58784fa15d2e21bfd67", "guid": "bfdfe7dc352907fc980b868725387e9834640a5a8012be9ce207faf40eb027dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898e2734a3ce6fccffb6595ed02b28f21", "guid": "bfdfe7dc352907fc980b868725387e98ed972cb21cb9a15c1492490a310c56b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98115865d46ef5c246881220994f28f5a2", "guid": "bfdfe7dc352907fc980b868725387e98402b010320f148210316bae608c640e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98810e4bd0d41afeb17be03e9618ce6439", "guid": "bfdfe7dc352907fc980b868725387e98003b7ed0518b56c024a94144513f0e36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d19e61782a07b04d76661736618181c", "guid": "bfdfe7dc352907fc980b868725387e98167312a600769c684499dfd6dab7f79b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbeffc388beeea29523dca2ff6727688", "guid": "bfdfe7dc352907fc980b868725387e98bb5f4dd91ccd4b40295ca618a2ce4b47"}], "guid": "bfdfe7dc352907fc980b868725387e985f181790cc2159d2e6e568a221cc90f9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e98883c0d847067c7810d880ea11d788c51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98580093888bf617f96216b6e919905ca7", "guid": "bfdfe7dc352907fc980b868725387e983f242eae6687cc38896eee5cd8e9fa59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821f202ae213180c07ac9670aaa2a5378", "guid": "bfdfe7dc352907fc980b868725387e98acc7671f1ba916824244bcc9b830c353"}], "guid": "bfdfe7dc352907fc980b868725387e989aac5277fbeed961b82e50463c93be5e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987c9c4ee2501d1ad446d91afec16b4162", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9865c56aba9761e89b574bc76d51367e46", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}