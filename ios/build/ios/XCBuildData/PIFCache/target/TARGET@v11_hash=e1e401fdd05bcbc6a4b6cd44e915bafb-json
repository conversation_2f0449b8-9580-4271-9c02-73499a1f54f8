{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b7af84c6a7ef3c32fca1abd00b59967", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d824d7d16094a5601cd8464f18b0411b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9820a9a967331b73116c7564647fceba51", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983725295f3e8fd99cd352837cc493c926", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9820a9a967331b73116c7564647fceba51", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a3387235260a143605af9e0d6627a57", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984f9eb4db4d23e2aedbe2d5072a0a20ce", "guid": "bfdfe7dc352907fc980b868725387e989f16b281e214e681a65291343efb18a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9b83622094ef1a6d9460c3a7708ee79", "guid": "bfdfe7dc352907fc980b868725387e981dba1a834fcc08a02cd1565d74c19a57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982efa97779b97945ed1a71219a27f3274", "guid": "bfdfe7dc352907fc980b868725387e984c213492c25d2aed6fca5f93301d7637"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f30ce087219937673c719e722fa2611", "guid": "bfdfe7dc352907fc980b868725387e98335fa825a4a6b2c89d9e9d65e2b4aeed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898228ca03209b15bb18a987a80601cca", "guid": "bfdfe7dc352907fc980b868725387e98fe0665a75f2e7a0fc842ccd01e17e04e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf9165cab62cda2b597990be03ff743c", "guid": "bfdfe7dc352907fc980b868725387e98142c47d111c8f1bf1656ac77ebcbcee3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983626bad8025d64825c00ad16a24ce9dc", "guid": "bfdfe7dc352907fc980b868725387e98656a72e645707c06fdf32043172d71f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d905c2109e1d42854b0c7ffbc9d2796", "guid": "bfdfe7dc352907fc980b868725387e98fff90d22c757c75581ce4019d3210b9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98583ec5914d5680d7d46eb954a8b09afa", "guid": "bfdfe7dc352907fc980b868725387e98d25175bf7240e2583d8a1cecb098a56d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984208cdcf3af89dc108a743704dd36d01", "guid": "bfdfe7dc352907fc980b868725387e9800bba25dead4d92f9126205e7a8c4339", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98594ab5e366458c3d5fc46302dd57f6bb", "guid": "bfdfe7dc352907fc980b868725387e9863949c4828bc947f114045a498e001df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9553e8ec25ae254ae691fb5d3606d60", "guid": "bfdfe7dc352907fc980b868725387e98769746e650b8f4687552a94278c322c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986376a6087e690e893eae141a3e4d8657", "guid": "bfdfe7dc352907fc980b868725387e985e456b209793ed6f5bd1acb2f5f41127"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7bb460ce207af2bce93e3d1d6a8e84c", "guid": "bfdfe7dc352907fc980b868725387e984efc8156945757ea6cce9ec6e2fcf730"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98618079ca8a3da60fc95453bdaf9f477f", "guid": "bfdfe7dc352907fc980b868725387e98065d4429f83d5228cba9a4a45f1694c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983815f8f1cc964bbd08d0602772492523", "guid": "bfdfe7dc352907fc980b868725387e986a45a38a030cc391084af5b6f142c9d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98385c5b77dd869321bd631a8357fce0eb", "guid": "bfdfe7dc352907fc980b868725387e98d258bedd17cb87f326a5c9abedc04919", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa62fb7476491e3802cf8495858ce9a2", "guid": "bfdfe7dc352907fc980b868725387e98b082631ca65162903362ce4d1be4c423", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2fbd6c3781667e4bce458598007b3c2", "guid": "bfdfe7dc352907fc980b868725387e987bf70f34583342561d9b76a35f0d5bd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d60c8316100619bb1cbefaac931ce0a6", "guid": "bfdfe7dc352907fc980b868725387e98b227601c77c7c1459f778743ca9c1fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98983f322c08dd068dfaaa496c141b79f1", "guid": "bfdfe7dc352907fc980b868725387e9895f49ce89d99948625e1199d3e59cdf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856de3bbb5f3548050191e915098ab475", "guid": "bfdfe7dc352907fc980b868725387e980bca50f383bed95115a357f59843c9b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828d751974f9fe3c577d375b79fc9b5a9", "guid": "bfdfe7dc352907fc980b868725387e98ed645f11f19ec382c5b2df8392a5bd30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4d8d4c460772157c5a625514f452d3d", "guid": "bfdfe7dc352907fc980b868725387e9870befbae39ddc527dd9e8a7828bd1467"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c66506435ec4a7615297fdc029eb7b7", "guid": "bfdfe7dc352907fc980b868725387e98616bf2b5a80c66d1cb499344a4a4bcc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986843f0c82fa02343540766b8fbfa4835", "guid": "bfdfe7dc352907fc980b868725387e98f11248ae6f836d4ae4a6e613d3410173"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985212227fadf2bb0273990d6c294ffa03", "guid": "bfdfe7dc352907fc980b868725387e98db9de657d0ce458d6b940f2734a28163"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866631760df32ba9b9dcb0c1283dc9d30", "guid": "bfdfe7dc352907fc980b868725387e98456a42e3b8ee1cce1bb4b617c97e698a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981500bbccc3230ba3e07799ccf286965c", "guid": "bfdfe7dc352907fc980b868725387e989eb08cb6858d6ce916df7ce454bc0d8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98802e08c65e56e5b7ebcf2b7644f157e3", "guid": "bfdfe7dc352907fc980b868725387e98cdbf54e45dcf0fe83f9c353fb454264a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b33d7f6c0faa2757012cea35311ba43c", "guid": "bfdfe7dc352907fc980b868725387e9868e1e2df444af18833b85cdc86bf62f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a49cbe37a9f96cdcf78741917e30fe7", "guid": "bfdfe7dc352907fc980b868725387e9809394468cf49684e18f44adedfeea792", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fd426ed77e7d3ed7ba790c20c2fc7fe", "guid": "bfdfe7dc352907fc980b868725387e98067b2eeadf1ea70ee1d65dea195f3e65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98591ce2286adf33bc681a840b8ba8b701", "guid": "bfdfe7dc352907fc980b868725387e98a011f3dec54214471585109a84b82429"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af4f6ecf120e3cd059d3156881b4a543", "guid": "bfdfe7dc352907fc980b868725387e98fb68c84229136a9436d17f61114dcbc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e36baabe68a206fccf083dc6f7e2c1a9", "guid": "bfdfe7dc352907fc980b868725387e98d9e2ed641ff96b6f1f90a8fd9fd1fa7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1212a780f0c063d653c1456ce7a5b9", "guid": "bfdfe7dc352907fc980b868725387e9869f046392073dfc03947de7464be6f93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6ee67712fa31ddc2c196b6e97b5298d", "guid": "bfdfe7dc352907fc980b868725387e98c4b4d29264529436c83bb67c912fb074"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987da0460f137a60db57ccdfccec23d738", "guid": "bfdfe7dc352907fc980b868725387e9828cafc51a6c301a7e8c8e6a97091a358"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f78fce8ed4f4551c3f5bf39ee92560e7", "guid": "bfdfe7dc352907fc980b868725387e989079a045770a56dc61c7ea8193f00ac3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838ab62af319fa313f8b452161b075e01", "guid": "bfdfe7dc352907fc980b868725387e98afdbded289f9986f7efa914784f42499"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873d36bfca6fb8230aa68fabb27416e02", "guid": "bfdfe7dc352907fc980b868725387e980cd9f48ec4eca1e03db4275dae1c6324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840ede73fb16fcce685b63215ddf2108e", "guid": "bfdfe7dc352907fc980b868725387e9841d15d0a35cf83dac200f18090a09f84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6ad153713ac21e1258df8cd7432bb8d", "guid": "bfdfe7dc352907fc980b868725387e981fe88e6d7157028b8f25e294d5cbd9a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c47dd4efeb2f82b8f274641fe49e184", "guid": "bfdfe7dc352907fc980b868725387e98ad57e5ba19f5ef4b1ed7a1c71b686fc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98971698707b2f0a12b090df8253499654", "guid": "bfdfe7dc352907fc980b868725387e989ca717046d1cfb9b5a2f3675f0f3c5eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc634aeb07433090dc4a3014f7a5c7a7", "guid": "bfdfe7dc352907fc980b868725387e98580745db3444fcccac986e329b830866"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f1b2d576d11a780f050797ca2f1a245", "guid": "bfdfe7dc352907fc980b868725387e984feab69fd14542e25264997f4779fc05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5d705f7569e88fc10e201a25997c6b5", "guid": "bfdfe7dc352907fc980b868725387e98bda756f2dfb6f44a0eb47bebcc61523f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c4f76d517cb201bfb304ff4a61e1bab", "guid": "bfdfe7dc352907fc980b868725387e98dd8fd0393977e3e32252f8251cdddfae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c67d2f159a835fcae790e7690d92755", "guid": "bfdfe7dc352907fc980b868725387e989c54d4eef37fdd6559d75eab403f7a53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988389b0ab4aa94d13bd3914327646d8be", "guid": "bfdfe7dc352907fc980b868725387e98cad97fc57714667a833c7e87d8d69cec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dfe95c0b1aa8306001fba2b70a2797a", "guid": "bfdfe7dc352907fc980b868725387e9840bffd9e22fbd2927332520ee7c2292c"}], "guid": "bfdfe7dc352907fc980b868725387e986fc1c9666314f02f81bf1cb6fd274b00", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985e7d1dcf7104a504d8f934b5b28c9259", "guid": "bfdfe7dc352907fc980b868725387e98998ffb7d0fe7f5dce7bc65bcd29eb6c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f75e8ef0d10dad345927cb20e0f210c4", "guid": "bfdfe7dc352907fc980b868725387e989a1b92ee62bd91fd42988b57abeb1f3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ec7795a410d772abab60ff35a940f17", "guid": "bfdfe7dc352907fc980b868725387e987646cad213a0eae272d963e4dc643ac6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98debda3936c2b01b047246d87025b4cdb", "guid": "bfdfe7dc352907fc980b868725387e981e855ebaa05af7452c0fe45c7fa0b4b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d5ca41efbae15cf95b8719c06f6efe8", "guid": "bfdfe7dc352907fc980b868725387e987a7ec373e106f446335d42c5e2165c96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826bf8bb0cbf195a3961f3cd2e9cea658", "guid": "bfdfe7dc352907fc980b868725387e98dd1e36eaee302e4facafe75a7db4100c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820cebd147e4698d262b48d91cf5708e2", "guid": "bfdfe7dc352907fc980b868725387e98276eaff96f093fa790f0ed9217d32bf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce95fb1a17c93e85cd6f633a68b5f7fe", "guid": "bfdfe7dc352907fc980b868725387e98fc062fdf34188f0ecf84dae3ec047e5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be43a49cc1569fc12a53e1873f743811", "guid": "bfdfe7dc352907fc980b868725387e989c26eb4355e20563cf53ec24e8fadf2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98224010207b09d43b1dc66e36d5b91ecf", "guid": "bfdfe7dc352907fc980b868725387e986c77c4bda6efa3013c4a4995b294ff15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0668d545f5b44174ef55a78029844c0", "guid": "bfdfe7dc352907fc980b868725387e9892cc414a978dc82550a3e0db6a40b441"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b679e3a146a5d7e6a4ff6c6fc64273d6", "guid": "bfdfe7dc352907fc980b868725387e98294515ecdc7ba83b9cc4bec5233ad107"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d46f8167e7b5a9adbd46a4460772b50", "guid": "bfdfe7dc352907fc980b868725387e984d0cf2b00793e60625484ea6ff0b4e3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895627d0ed43aaa781752f0dc6a806243", "guid": "bfdfe7dc352907fc980b868725387e9862afb4bcbe8425ea5fc96a3525bd0322"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c68180036444302d571889179c36739", "guid": "bfdfe7dc352907fc980b868725387e989401073d73254ed37a673a8b4ba8af2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bd559c06374c43ecbc5edbc2c9b2a7c", "guid": "bfdfe7dc352907fc980b868725387e98b2f9af5da9af0bb7bf8839b595e8f32c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880186561562ac5700918395afd062b2d", "guid": "bfdfe7dc352907fc980b868725387e98a485e9752937b3e3aa4c44e7257c433c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cc7bce11368d54574266f3d3d32119b", "guid": "bfdfe7dc352907fc980b868725387e98ffe374dd15e300e290c7817955b7c7cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98add10f4efcdb864ea349011474ddb673", "guid": "bfdfe7dc352907fc980b868725387e985d76e0e28c581b44a6c23b06988bac4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da1d7c0de82c53ce53017c541ed55e86", "guid": "bfdfe7dc352907fc980b868725387e98bbb2ea90c01b9e1effd573824a987161"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98865e93cf1b17f77b990c68e203e8a8f7", "guid": "bfdfe7dc352907fc980b868725387e98e83d78bfb220d6847e091cfb2201fa45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981820d885491e4f0531a1c256e0eb4235", "guid": "bfdfe7dc352907fc980b868725387e98f850861e38af349e130c4337cc57771a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869d9107939d806d894226ffc96cf4dce", "guid": "bfdfe7dc352907fc980b868725387e9810307f17feb2f9e55eb440ffeb6133c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859c4a0ace9b53ed4badde8e6e03b8eb9", "guid": "bfdfe7dc352907fc980b868725387e989ba2e0d8a1df77c7dbcace2c6f945225"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1f388ef18c1470a8059af35629c153e", "guid": "bfdfe7dc352907fc980b868725387e982f07b456f00962d4afadd4e39d302931"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e2d75a111bb234d3357083be34c0116", "guid": "bfdfe7dc352907fc980b868725387e9894cfdd37f21f8bc520aaa20b828cb041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f11ecab6d9972572acd88cd8c4a87c1", "guid": "bfdfe7dc352907fc980b868725387e9892a27e218d883698aa63f38298e660c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98448e7ed418d447a17cfea03241d69b10", "guid": "bfdfe7dc352907fc980b868725387e989de49a0cd41fc1bd4e3a863b45a66ee4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800816b0368ab9963ba510c2b16d63153", "guid": "bfdfe7dc352907fc980b868725387e98bd97127f3d2c9e415436c6dc3d380573"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc89c7e286fb043dcac67baed83c4f72", "guid": "bfdfe7dc352907fc980b868725387e9850f341ce5fb3a1a5e1c6535f2ad4ebb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb7932fa9e0d7afb658b64ba322980f5", "guid": "bfdfe7dc352907fc980b868725387e982b19eb88648dda52dfdc5531a0e6a797"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b58efd7534f5d80bd687678164c06a3b", "guid": "bfdfe7dc352907fc980b868725387e98150561ac08f24583047e09fbbb475adf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad633249f78abf17eb9db7e64527c8a2", "guid": "bfdfe7dc352907fc980b868725387e98e79e71f088fe07c1f8b277dee9f42b43"}], "guid": "bfdfe7dc352907fc980b868725387e9898e72fbaa96d8553cb23bf342f89c2d4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e98a5ee3dc48dc2f15194ef2305fa28b42f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821f202ae213180c07ac9670aaa2a5378", "guid": "bfdfe7dc352907fc980b868725387e988242dda89f859a8ee7f4a5f7c81ee343"}], "guid": "bfdfe7dc352907fc980b868725387e985b783cdf97c32f0f95c0ee9f6d23949c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982dd0df97a82cbbfb047ec5d874439a77", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e986a154f84b22acf7508e330c9539d0212", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}