{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98effd5515dd3bc7533f63156c5b258a32", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98db3e14fc9bbf84d0959b48a6b374628f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9876b0769d418e93fe3117fa3c39cc7e89", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9849543c6e8096987582c137d435ffeee5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9876b0769d418e93fe3117fa3c39cc7e89", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9891d8fd4b169e481592b29bf2dab18c18", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9839fd60a6ba451ca0626e268343e55259", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9834bba9edd125d252a407f153b20c5347", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9821ccc5a1053dd7a100f4db8cbfa326f4", "guid": "bfdfe7dc352907fc980b868725387e98730cb64bb234012a147a3f1a2773d249"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de74c177b09ec3020c8e4d020298e4cd", "guid": "bfdfe7dc352907fc980b868725387e98ae5e59ff25f93501dcd8328185d7ec5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cab04b610850cf3fa21fc41d3576091", "guid": "bfdfe7dc352907fc980b868725387e98e893bfc238766f2bb6809b06a6826fc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984986e318a26d37c0d5d432e8b157bca7", "guid": "bfdfe7dc352907fc980b868725387e98d45370a484de6ebfaa50c46c482ad518"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830c190b2d7bb7cbd2804b138d5876eaf", "guid": "bfdfe7dc352907fc980b868725387e989ba85114ad6f64b33236a5ac6e45b23d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef2fabaeee4442826a5234ab11245da5", "guid": "bfdfe7dc352907fc980b868725387e98f4a608918c053fdf42f64ff220524efe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a60567f372329287fa140847978badb8", "guid": "bfdfe7dc352907fc980b868725387e98941ee1f3639348e6ce765d49f24b1e7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809f47f1b7b650497a4cdfff94f40addb", "guid": "bfdfe7dc352907fc980b868725387e9888b9ecdefee6209ba994ffc15af0e0c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da5de6ae69ecbe989b31f3b7204c81df", "guid": "bfdfe7dc352907fc980b868725387e9877866d6f57efc34608e1bc818e91b9ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f56aa71432807e230d5d020a2cf1906e", "guid": "bfdfe7dc352907fc980b868725387e98964ef6ee9226b5aee59e216804f3daa1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1ffbc92efbaf7bec4c7de0392a0594a", "guid": "bfdfe7dc352907fc980b868725387e986f6f1be33bb49ad4909f99da7cf83702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b92e52f1834882fa1c222d65653e8e66", "guid": "bfdfe7dc352907fc980b868725387e98b07483409b012c5f370f40ab4c8dae99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0342164af5af024e6f2cd0f32227d63", "guid": "bfdfe7dc352907fc980b868725387e98fb9aca04fe965824e39ba79496a922d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98735defcf60ed576efee4405cde4cd176", "guid": "bfdfe7dc352907fc980b868725387e98baab08b3e59398e0348a19b13e93be4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b907625057ef1229dc8f397b7cfc3621", "guid": "bfdfe7dc352907fc980b868725387e98e98079a25e49e62ed5de0c8cdf7905a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850c1e79e4b6075a687378631fedd15f6", "guid": "bfdfe7dc352907fc980b868725387e98d57c0731808436c8967fe1fda65526a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1c807d32a1ac8b6138606aa6dcfd29c", "guid": "bfdfe7dc352907fc980b868725387e98b899129a5759c69415d782e6552f10b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e211bba0fa66d39d89e931b0164fa671", "guid": "bfdfe7dc352907fc980b868725387e9859d9c8d38b9506be2621bfb7bf79e0b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c0e36855b6720549e7aadcfdbea3225", "guid": "bfdfe7dc352907fc980b868725387e98d97bd477f98db92b003d8365cac17bb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e50eb4f0e99bed4620c90411b4d040a", "guid": "bfdfe7dc352907fc980b868725387e9854513b91f060347a99246dda800237c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8a6134cb2782f4a96d0ba2476eb9014", "guid": "bfdfe7dc352907fc980b868725387e9884b5c7f58d3d1541802b6b2c8d2f5c90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd10db3786360c47cfa3cee06725d507", "guid": "bfdfe7dc352907fc980b868725387e98b24ede05cbdefaa0a17a787684822e7e"}], "guid": "bfdfe7dc352907fc980b868725387e98c553650d1b7ceec4cc1c432f39f97935", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}