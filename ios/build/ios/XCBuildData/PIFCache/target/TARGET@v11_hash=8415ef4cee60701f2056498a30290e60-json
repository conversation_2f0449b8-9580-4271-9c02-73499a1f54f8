{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988534ff8b6a4390140fbda9c4b3948a34", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b6eb1dc10ddb68a4d0240508e4029466", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985b3114bde1016603ffe713d3d7950a04", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9837b5ad4ea81c0028ac0a3a36e4d22047", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985b3114bde1016603ffe713d3d7950a04", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9819a7108d7f240524f5380ec245efda46", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f6dfa0a2a10f7cc23a95bae72962c52a", "guid": "bfdfe7dc352907fc980b868725387e98638c2f7cddea8d7516d2d686a4c93782", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9837a6062c6d48feeaf08e3a73a2213389", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9885585240162e1ab0d8a1149e325e9148", "guid": "bfdfe7dc352907fc980b868725387e9874e9844a022ab50b26b164cd646e4af1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986493fbb5a4ae098b1f374b6e8677eedc", "guid": "bfdfe7dc352907fc980b868725387e98a35e1823a88dc54e2b1ab710aa423d97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c01589e516d19c2c788ed3a7e8e64d99", "guid": "bfdfe7dc352907fc980b868725387e981c8cbd244ddf354ad0f1d889fccaa409"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98243c048fcac3b7d7294e638a436966f8", "guid": "bfdfe7dc352907fc980b868725387e981e8573ea13bb0290c6b38d305a1bd8c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fef1b5ccb11ae2cfff852b76996ddd1", "guid": "bfdfe7dc352907fc980b868725387e98333180924c4e74648adb9913d20a12c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804c942f6ff3740b5b9b0c4f234568b73", "guid": "bfdfe7dc352907fc980b868725387e983025da7e09cfd9a574a73c6316ef2774"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e056cf10299cdcba27809d09027db8e7", "guid": "bfdfe7dc352907fc980b868725387e9802d56e28ba139cf8445dba869981a3bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eea3eca18f53ee9d64f4e88c95113f9a", "guid": "bfdfe7dc352907fc980b868725387e985ce873018ed2cef2b4a212dd274e72fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981598be617fbd91394595d175150d8820", "guid": "bfdfe7dc352907fc980b868725387e986a29563521fe35c2a2681d08bc25293f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802c10574d38282b4eccaeb100d5e6f4e", "guid": "bfdfe7dc352907fc980b868725387e985b3a9e06b39be305e16ccd35fd1efeab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b8b2cd8634ede774100d9dc82a7845", "guid": "bfdfe7dc352907fc980b868725387e98ae43fc72aa3f322124f0acde30c00957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a60fd3755d4350ba0990bb849c64f146", "guid": "bfdfe7dc352907fc980b868725387e980cc8fc1b488e0273587b35f93c73b594"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b95327a415faab506b5fe0e1dadde38d", "guid": "bfdfe7dc352907fc980b868725387e98a646c563ed9a92f29613cead1caa0b36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6a49d30b4e7433e7591428b5e3b7954", "guid": "bfdfe7dc352907fc980b868725387e98d9bebcdb305591f068c95266e1f585ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f709d0be6651d49bce439aabc9b0687c", "guid": "bfdfe7dc352907fc980b868725387e988688f3e2e910f178eb24d5114bc138d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987109dbc26756d2234a5e34405b3fce2e", "guid": "bfdfe7dc352907fc980b868725387e98827725cf67a1384bea71ce308e3f6220"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf6ba94b061f3e4940340e5806fbea45", "guid": "bfdfe7dc352907fc980b868725387e98e4a81b462485b97a31b11134f9292350"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808cb9e69753111a828a6697a8f43a3fb", "guid": "bfdfe7dc352907fc980b868725387e98193e39d0cd5c9dc5fad363a0d6324948"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98919d2e40f7f4b870fad19f679ec2b2d4", "guid": "bfdfe7dc352907fc980b868725387e98925108f8e92285373cd0207c39a15ddb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983334f05a05ef5ea246f6fd52bc77c4cc", "guid": "bfdfe7dc352907fc980b868725387e98c8a011dd4e2bd42b7a64688896957918"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815d45106c5f5d5e58132d51d25bf0e21", "guid": "bfdfe7dc352907fc980b868725387e9853c3cf263e7c4b355425bb85f873f7c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f515969d2579d0a89a218dae8735c1ea", "guid": "bfdfe7dc352907fc980b868725387e981a8aeac17d75932594a8d4e5b3c9b010"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98004df2106a8d595d5f9d081f25f43bd1", "guid": "bfdfe7dc352907fc980b868725387e988c020bb09dcb9111c2b9165a7660b44d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dcd3c7902318f68989187548d2c3192", "guid": "bfdfe7dc352907fc980b868725387e986fbe0f6cd18dfb3ebc9ae0626f51f01c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864be31934bf37dc3efc3a9635bceb7eb", "guid": "bfdfe7dc352907fc980b868725387e980b4a887cb61c63ad69feda0965de9ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2ce88398675795349103e7343027200", "guid": "bfdfe7dc352907fc980b868725387e98ba951a441c98e23f8db12e5181c89b89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e61c04dd13b891279bc01d1efc5ee0de", "guid": "bfdfe7dc352907fc980b868725387e98ed17fc7f2483489e496b3df77690bd18"}], "guid": "bfdfe7dc352907fc980b868725387e984eeea82234e4011d05139777019ff235", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980016cb835706e60cf4ea5b41015bb056", "guid": "bfdfe7dc352907fc980b868725387e98879f561dd9aabb1ecee222bdaa5bfa8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2414fe36aaec75f84b3205ba9dc0e5c", "guid": "bfdfe7dc352907fc980b868725387e98206c0727591e2726432c192eb3f288c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818733e61eac725d4a6546ffacdeb6605", "guid": "bfdfe7dc352907fc980b868725387e981c053494d9d8549cc613bc6f4a01eadd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf7385693e3d449b92dcf8e1704751ca", "guid": "bfdfe7dc352907fc980b868725387e980f9aeaf7d89de78f5f0eb481e1dc3354"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98978e6e5f352bb4fae445c10c8dc74d2e", "guid": "bfdfe7dc352907fc980b868725387e9888aaf00e9593b782bf6199d3db268a3b"}], "guid": "bfdfe7dc352907fc980b868725387e9803c85cca6c79ee2fdb40f5e593e844ee", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98779943cd0ec01035914f8f66aad94c3b", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e988851ef49aa7bfff325186ecda70313b9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}