<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>580215990334-i05504llri1d48cp6hcv7h9qj7ickd9h.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.580215990334-i05504llri1d48cp6hcv7h9qj7ickd9h</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>580215990334-12i24gmio6ne7ohp42fmh05etk4s670b.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyCe7CxAZ_TZfUujXnKLOlguFTcW8Un7a24</string>
	<key>GCM_SENDER_ID</key>
	<string>580215990334</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>ae.element8.hrportal</string>
	<key>PROJECT_ID</key>
	<string>e8-hr-portal</string>
	<key>STORAGE_BUCKET</key>
	<string>e8-hr-portal.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:580215990334:ios:4ecc49b356cc9a32c1c96f</string>
</dict>
</plist>