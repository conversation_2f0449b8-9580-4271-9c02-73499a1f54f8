<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>HR Connect</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>e8_hr_portal</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ae.element8.hrportal</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.580215990334-i05504llri1d48cp6hcv7h9qj7ickd9h</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FDAllFilesDownloadedMessage</key>
	<string>All files have been downloaded</string>
	<key>FLTEnableImpeller</key>
	<false/>
	<key>FacebookAppID</key>
	<string>486546996330287</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>HR Connect</string>
	<key>GIDClientID</key>
	<string>580215990334-i05504llri1d48cp6hcv7h9qj7ickd9h.apps.googleusercontent.com</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>mailto</string>
		<string>tel</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Need BLE permission</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Need BLE permission</string>
	<key>NSCameraUsageDescription</key>
	<string>This app requires access to the camera.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app not using the microphone</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Need Location permission</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Need Location permission</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Need Location permission</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>For uploading business logo and user profile photo. For uploading new posts. For sharing images in chat</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
	<key>NSLocalNetworkUsageDescription</key>
	<string>This app uses the local network to support debugging features during development.</string>
</dict>
</plist>
