// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:e8_hr_portal/helper/circula_loading_widget.dart';
// import 'package:e8_hr_portal/model/birthday_model.dart';
// import 'package:e8_hr_portal/provider/company_activities_provider.dart';
// import 'package:e8_hr_portal/util/colors.dart';
// import 'package:e8_hr_portal/util/date_formatter.dart';
// import 'package:e8_hr_portal/util/styles.dart';
// import 'package:provider/provider.dart';

// class BirthdayCard extends StatelessWidget {
//   final String? date;

//   const BirthdayCard({Key? key, required this.date}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     String personDate;
//     return FutureBuilder<List<BirthdayModel>>(
//         future: Provider.of<CompanyActivitiesProvider>(context)
//             .getUpcomingBirthdays(),
//         builder: (context, snapshot) {
//           if (snapshot.connectionState == ConnectionState.waiting) {
//             return const SizedBox(
//               height: 130,
//               child: Center(
//                 child: CircularLoadingWidget(),
//               ),
//             );
//           }
//           List<BirthdayModel> birthdays = [];
//           if (snapshot.hasData) {
//             birthdays = snapshot.data!;
//             print(birthdays);
//           }
//           if (birthdays.isEmpty) {
//             return Container();
//           }
//           return SizedBox(
//             height: 130,
//             child: ListView.builder(
//               scrollDirection: Axis.horizontal,
//               itemCount: birthdays.length,
//               itemBuilder: (BuildContext context, index) {
//                 BirthdayModel birthday = birthdays.elementAt(index);
//                 personDate = formatDateFromDate(
//                   dateTime: birthday.dob!.toDate(),
//                   format: 'dd MMM',
//                 );
//                 return Card(
//                   shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(15),
//                       side: personDate == date
//                           ? BorderSide(color: ThemeColors.secondaryColor)
//                           : BorderSide.none),
//                   child: Padding(
//                     padding: const EdgeInsets.only(left: 10, right: 10),
//                     child: SizedBox(
//                       height: 130,
//                       width: 80,
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.center,
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           Container(
//                             height: 50,
//                             width: 50,
//                             decoration: BoxDecoration(
//                               // border: Border.all(color: Colors.grey),
//                               shape: BoxShape.circle,
//                               image: birthday.profilePhoto != null
//                                   ? DecorationImage(
//                                       image: CachedNetworkImageProvider(
//                                         birthday.profilePhoto!,
//                                       ),
//                                       fit: BoxFit.cover,
//                                     )
//                                   : const DecorationImage(
//                                       image: AssetImage(
//                                         "assets/images/user.png",
//                                       ),
//                                       fit: BoxFit.cover,
//                                     ),
//                             ),
//                           ),
//                           const SizedBox(
//                             height: 5,
//                           ),
//                           Align(
//                             alignment: Alignment.center,
//                             child: Text(
//                               birthday.name.toString(),
//                               overflow: TextOverflow.ellipsis,
//                               textAlign: TextAlign.center,
//                               style: GoogleFonts.rubik(
//                                 fontSize: 12,
//                                 fontWeight: FontWeight.w500,
//                                 color: Colors.black,
//                               ),
//                             ),
//                           ),
//                           const SizedBox(
//                             height: 5,
//                           ),
//                           Align(
//                             alignment: Alignment.center,
//                             child: Text(
//                               formatDateFromDate(
//                                 dateTime: birthday.dob!.toDate(),
//                                 format: 'dd MMM',
//                               ),
//                               style: tsS12W5F83456,
//                             ),
//                           )
//                         ],
//                       ),
//                     ),
//                   ),
//                 );
//               },
//             ),
//           );
//         });
//   }
// }
