import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:e8_hr_portal/model/logged_in_user.dart';
import 'package:e8_hr_portal/provider/company_activities_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/other_screens/company_activities/add_gift_exchange.dart';
import 'package:provider/provider.dart';
import '../../../util/date_formatter.dart';

class CompanyActivities extends StatelessWidget {
  const CompanyActivities({super.key});

  @override
  Widget build(BuildContext context) {
    final CompanyActivitiesProvider companyActivitiesProvider =
        Provider.of<CompanyActivitiesProvider>(context);
    String? indianHolidayDate;
    String? dateNow;
    // final DateTime datenow = DateTime.now();
    //String date = formatDateFromDate(dateTime: datenow, format: "dd MMM");
    // print(date);

    // print(datenow);
    return Scaffold(
      appBar: AppBar(
        actions: [
          if (LoggedInUser.isAdmin)
            IconButton(
              onPressed: () => PageNavigator.push(
                context: context,
                route: const AddGiftExchangeEventScreen(),
              ),
              icon: const Icon(Icons.add),
            ),
        ],
        leading: InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: const Icon(
            Icons.arrow_back_ios_new_outlined,
            color: Colors.white,
            size: 20,
          ),
        ),
        title: const Text("Company Activities"),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(15, 15, 15, 15),
          child: Column(
            children: [
              if (companyActivitiesProvider.birthdayList?.data != null)
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    "Birthdays",
                    style: tsS181E2138,
                  ),
                ),
              if (companyActivitiesProvider.birthdayList?.data != null)
                const SizedBox(
                  height: 15,
                ),
              // BirthdayCard(
              //   date: date,
              // ),
              if (companyActivitiesProvider.birthdayList?.data != null)
                SizedBox(
                  height: 130,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount:
                        companyActivitiesProvider.birthdayList?.data?.length ??
                            0,
                    itemBuilder: (context, index) {
                      String? formatted;
                      final data =
                          companyActivitiesProvider.birthdayList?.data?[index];
                      if (data?.dateOfBirth != null) {
                        DateTime dt =
                            DateTime.parse(data!.dateOfBirth.toString());
                        final DateFormat formatter = DateFormat('dd MMM');
                        formatted = formatter.format(dt);
                        dateNow = formatter.format(DateTime.now());
                      }
                      return companyActivitiesProvider.isBirthdayLoading == true
                          ? const Center(
                              child: CircularProgressIndicator(),
                            )
                          : Card(
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  side: formatted == dateNow
                                      ? BorderSide(
                                          color: ThemeColors.secondaryColor)
                                      : BorderSide.none),
                              child: Padding(
                                padding:
                                    const EdgeInsets.only(left: 10, right: 10),
                                child: SizedBox(
                                  height: 130,
                                  width: 80,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        height: 50,
                                        width: 50,
                                        decoration: BoxDecoration(
                                          border:
                                              Border.all(color: Colors.grey),
                                          shape: BoxShape.circle,
                                          image: data?.profilePic != null
                                              ? DecorationImage(
                                                  image:
                                                      CachedNetworkImageProvider(
                                                    data!.profilePic.toString(),
                                                  ),
                                                  fit: BoxFit.cover,
                                                )
                                              : const DecorationImage(
                                                  image: AssetImage(
                                                    "assets/images/user.png",
                                                  ),
                                                  fit: BoxFit.cover,
                                                ),
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 5,
                                      ),
                                      Align(
                                        alignment: Alignment.center,
                                        child: Text(
                                          data?.name.toString() ?? "Name",
                                          overflow: TextOverflow.ellipsis,
                                          textAlign: TextAlign.center,
                                          style: GoogleFonts.rubik(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 5,
                                      ),
                                      Align(
                                        alignment: Alignment.center,
                                        child: Text(
                                          formatted.toString(),
                                          style: tsS12W5F83456,
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            );
                    },
                  ),
                ), //////////////////////////////
              const SizedBox(
                height: 10,
              ),
              StreamBuilder<QuerySnapshot>(
                  stream: FirebaseFirestore.instance
                      .collection("events")
                      .where("events_date",
                          isGreaterThanOrEqualTo: DateTime(DateTime.now().year,
                              DateTime.now().month, DateTime.now().day))
                      .snapshots(),
                  builder: (context, snapshot) {
                    if (!snapshot.hasData) {
                      return const Center(
                        child: CircularProgressIndicator(
                          backgroundColor: Colors.lightBlueAccent,
                        ),
                      );
                    } else {
                      final event = snapshot.data!.docs;

                      return ListView.builder(
                          shrinkWrap: true,
                          itemCount: event.length,
                          itemBuilder: (context, index) {
                            return SizedBox(
                              height: 100,
                              child: Column(
                                children: [
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Text(
                                      "Events",
                                      style: tsS181E2138,
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 15,
                                  ),
                                  Container(
                                    alignment: Alignment.center,
                                    height: 60,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      color: Colors.white,
                                      boxShadow: const [
                                        BoxShadow(
                                          offset: Offset(2, 2),
                                          blurRadius: 10,
                                          color: Color.fromRGBO(0, 0, 0, 0.16),
                                        )
                                      ],
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                          left: 15.0, right: 15, top: 10),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          Container(
                                              alignment: Alignment.center,
                                              height: 35,
                                              width: 35,
                                              child: CircleAvatar(
                                                backgroundColor:
                                                    ThemeColors.secondaryColor,
                                                child: const ImageIcon(
                                                  AssetImage(
                                                      "assets/icons/calendar.png"),
                                                  color: Colors.white,
                                                ),
                                              )),
                                          const SizedBox(
                                            width: 20,
                                          ),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              children: [
                                                Align(
                                                  alignment: Alignment.topLeft,
                                                  child: Text(
                                                    "Gift Exchange Event",
                                                    style: tsS14w500,
                                                  ),
                                                ),
                                                const SizedBox(
                                                  height: 5,
                                                ),
                                                Align(
                                                  alignment: Alignment.topLeft,
                                                  child: Text(
                                                    formatDateFromDate(
                                                        dateTime: event[index]
                                                            .get('events_date')
                                                            .toDate(),
                                                        format:
                                                            "EEE, dd MMM yyyy "),
                                                    style: tsS128391B5,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(
                                            height: 25,
                                            child: Icon(
                                              Icons.favorite_border_outlined,
                                              color: Colors.grey,
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          });
                    }
                  }),
              Column(children: [
                const SizedBox(
                  height: 10,
                ),
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    "Work Anniversaries",
                    style: tsS181E2138,
                  ),
                ),
                const SizedBox(
                  height: 15,
                ),
                Consumer<CompanyActivitiesProvider>(
                  builder: (context, provider, child) {
                    return provider.isAnniversayLoading == true
                        ? const Center(
                            child: CircularProgressIndicator(),
                          )
                        : ListView.separated(
                            separatorBuilder: (context, index) {
                              return const SizedBox(
                                height: 8,
                              );
                            },
                            shrinkWrap: true,
                            itemCount:
                                provider.anniversaryList?.data?.length ?? 0,
                            itemBuilder: (context, index) {
                              final data =
                                  provider.anniversaryList?.data?[index];
                              return AnniversaryPost(
                                name: data?.name,
                                // location: data?.location,
                                profilePic: data?.profilePic,
                                year: data?.year.toString(),
                              );
                            },
                          );
                  },
                )
              ]),
              const SizedBox(
                height: 25,
              ),
              Row(
                children: [
                  Align(
                    alignment: Alignment.topLeft,
                    child: Text(
                      "Holidays",
                      style: tsS181E2138,
                    ),
                  ),
                  // const SizedBox(
                  //   width: 7,
                  // ),
                  // Align(
                  //   alignment: Alignment.topLeft,
                  //   child: Text(
                  //     "(as per India govt)",
                  //     style: tsS12grey,
                  //   ),
                  // ),
                ],
              ),
              const SizedBox(
                height: 15,
              ),
              Consumer<CompanyActivitiesProvider>(
                builder: (context, provider, child) {
                  return provider.isIndianLoading == true
                      ? const Center(
                          child: CircularProgressIndicator(),
                        )
                      : Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            color: Colors.white,
                            boxShadow: const [
                              BoxShadow(
                                offset: Offset(2, 2),
                                blurRadius: 10,
                                color: Color.fromRGBO(0, 0, 0, 0.16),
                              )
                            ],
                          ),
                          child: ListView.builder(
                              shrinkWrap: true,
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              physics: const BouncingScrollPhysics(),
                              itemCount: provider.indianHolidaysList.length,
                              itemBuilder: (BuildContext context, index) {
                                final data = provider.indianHolidaysList[index];
                                if (data.date != null) {
                                  DateTime birthdate =
                                      DateTime.parse(data.date.toString());
                                  final DateFormat formatter =
                                      DateFormat('EE, dd MMM');
                                  indianHolidayDate =
                                      formatter.format(birthdate);
                                }
                                return Padding(
                                  padding:
                                      const EdgeInsets.fromLTRB(8, 8, 8, 8),
                                  child: SizedBox(
                                    // width: 343,
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      children: [
                                        SizedBox(
                                          // width: 70,
                                          child: Row(
                                            children: [
                                              Align(
                                                alignment: Alignment.topLeft,
                                                child: Text(
                                                  indianHolidayDate.toString(),
                                                  style: tsS128391B5,
                                                ),
                                              ),
                                              if (data.restrictedHoliday ==
                                                  true)
                                                Align(
                                                  alignment: Alignment.topLeft,
                                                  child: Text("*",
                                                      style: tsS128391B5),
                                                )
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          width: 140,
                                          child: Align(
                                            alignment: Alignment.center,
                                            child: Text(
                                              data.occassion.toString(),
                                              style: tsS128391B5,
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          width: 50,
                                          child: Align(
                                            alignment: Alignment.topRight,
                                            child: Text(
                                              "${data.days ?? 1} days",
                                              style: tsS128391B5,
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                );
                              }),
                        );
                },
              ),
              const SizedBox(
                height: 15,
              ),
              // Row(
              //   children: [
              //     Align(
              //       alignment: Alignment.topLeft,
              //       child: Text(
              //         "Holidays",
              //         style: tsS181E2138,
              //       ),
              //     ),
              //     const SizedBox(
              //       width: 7,
              //     ),
              //     Align(
              //       alignment: Alignment.topLeft,
              //       child: Text(
              //         "(as per UAE govt)",
              //         style: tsS12grey,
              //       ),
              //     ),
              //   ],
              // ),
              // const SizedBox(
              //   height: 15,
              // ),
              // Container(
              //     width: double.infinity,
              //     decoration: BoxDecoration(
              //       borderRadius: BorderRadius.circular(5),
              //       color: Colors.white,
              //       boxShadow: const [
              //         BoxShadow(
              //           offset: Offset(2, 2),
              //           blurRadius: 10,
              //           color: Color.fromRGBO(0, 0, 0, 0.16),
              //         )
              //       ],
              //     ),
              //     child: Consumer<CompanyActivitiesProvider>(
              //         builder: (context, provider, child) {
              //       return provider.isUaeLoading == true
              //           ? const Center(
              //               child: CircularProgressIndicator(),
              //             )
              //           : ListView.builder(
              //               shrinkWrap: true,
              //               physics: const BouncingScrollPhysics(),
              //               itemCount:
              //                   provider.uaeHolidaysList?.data?.length ?? 0,
              //               itemBuilder: (BuildContext context, index) {
              //                 final data =
              //                     provider.uaeHolidaysList?.data?[index];
              //                 if (data?.date != null) {
              //                   DateTime birthdate =
              //                       DateTime.parse(data!.date.toString());
              //                   final DateFormat formatter =
              //                       DateFormat('EE, dd MMM');
              //                   uaeHolidayDate = formatter.format(birthdate);
              //                 }
              //                 return Padding(
              //                   padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
              //                   child: SizedBox(
              //                     // width: 343,
              //                     child: Row(
              //                       crossAxisAlignment:
              //                           CrossAxisAlignment.start,
              //                       mainAxisAlignment:
              //                           MainAxisAlignment.spaceEvenly,
              //                       children: [
              //                         SizedBox(
              //                           // width: 70,
              //                           child: Row(
              //                             children: [
              //                               Align(
              //                                 alignment: Alignment.topLeft,
              //                                 child: Text(
              //                                   uaeHolidayDate.toString(),
              //                                   style: tsS128391B5,
              //                                 ),
              //                               ),
              //                               if (data?.restrictedHoliday == true)
              //                                 Align(
              //                                   alignment: Alignment.topLeft,
              //                                   child: Text("*",
              //                                       style: tsS128391B5),
              //                                 )
              //                             ],
              //                           ),
              //                         ),
              //                         SizedBox(
              //                           width: 140,
              //                           child: Align(
              //                             alignment: Alignment.center,
              //                             child: Text(
              //                               data?.occassion.toString() ??
              //                                   "Occassion",
              //                               style: tsS128391B5,
              //                             ),
              //                           ),
              //                         ),
              //                         SizedBox(
              //                           width: 50,
              //                           child: Align(
              //                             alignment: Alignment.topRight,
              //                             child: Text(
              //                               "${data?.days.toString()} days",
              //                               style: tsS128391B5,
              //                             ),
              //                           ),
              //                         )
              //                       ],
              //                     ),
              //                   ),
              //                 );
              //               });
              //     }))
            ],
          ),
        ),
      ),
    );
  }
}

class AnniversaryPost extends StatelessWidget {
  final String? profilePic;
  final String? year;
  // final String? location;
  final String? name;
  const AnniversaryPost({
    super.key,
    required this.profilePic,
    required this.year,
    // required this.location,
    required this.name,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        color: Colors.white,
        boxShadow: const [
          BoxShadow(
            offset: Offset(2, 2),
            blurRadius: 10,
            color: Color.fromRGBO(0, 0, 0, 0.16),
          )
        ],
      ),
      child: Padding(
        padding:
            const EdgeInsets.only(left: 15.0, right: 15, top: 12, bottom: 10),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ThemeColors.primaryColor,
                  shape: BoxShape.circle,
                  image: profilePic != null
                      ? DecorationImage(
                          image: NetworkImage(profilePic.toString()),
                          fit: BoxFit.cover)
                      : null),
              child: profilePic == null
                  ? Center(
                      child: Text(
                      name.toString().substring(0, 1).toUpperCase(),
                      style: tsS14FFFFF,
                    ))
                  : null,

              // image:  AssetImage("assets/images/user.png"),
              // fit: BoxFit.fill)),
            ),
            const SizedBox(
              width: 20,
            ),
            SizedBox(
              width: 208,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Align(
                    alignment: Alignment.topLeft,
                    child: Text(
                      "Congratulate $name for $year years at Element 8.",
                      style: tsS12NormalBlack,
                    ),
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  // InkWell(
                  //     onTap: () {
                  //       //goto_chat_screen
                  //     },
                  //     child: Text(
                  //       "Say Congrats",
                  //       style: tsS12second,
                  //     )),
                ],
              ),
            ),
            // const SizedBox(
            //   width: 35,
            // ),
            // const SizedBox(
            //   height: 25,
            //   child: Icon(
            //     Icons.thumb_up_outlined,
            //     color: Colors.grey,
            //   ),
            // )
          ],
        ),
      ),
    );
  }
}
