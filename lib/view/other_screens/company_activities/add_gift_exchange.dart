import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/helper/common_textfield.dart';
import 'package:e8_hr_portal/model/logged_in_user.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';

import '../../meeting_room_hisence/meeting/common_widgets/common_button.dart';

class AddGiftExchangeEventScreen extends StatefulWidget {
  const AddGiftExchangeEventScreen({super.key});

  @override
  State<AddGiftExchangeEventScreen> createState() =>
      _AddGiftExchangeEventScreenState();
}

class _AddGiftExchangeEventScreenState
    extends State<AddGiftExchangeEventScreen> {
  final TextEditingController eventController = TextEditingController();
  final TextEditingController dateController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Event")),
      body: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              CommonTextFieldWidget(
                labelText: "Event Name",
                controller: eventController,
                validator: (value) =>
                    Validator.textSpecific(value!, 'event name'),
              ),
              const SizedBox(
                height: 25,
              ),
              CommonTextFieldWidget(
                labelText: "Date",
                readOnly: true,
                controller: dateController,
                onTap: () {
                  _selectEventDate(context);
                },
                validator: (value) => Validator.textSpecific(value!, 'date'),
              ),
              const SizedBox(
                height: 25,
              ),
              CommonButton(
                hPadding: 0,
                color: ThemeColors.secondaryColor,
                buttonName: "Save",
                style: tsS14FFFFF,
                function: () async {
                  if (_formKey.currentState!.validate()) {
                    await FirebaseFirestore.instance.collection("events").add({
                      "events_name": eventController.text,
                      "events_date": eventDate,
                      "save_date": DateTime.now(),
                      "uid": LoggedInUser.uid,
                    }).then((value) {
                      eventController.clear();
                      dateController.clear();
                      showToastText("Succeessfully Added");
                    });
                  }
                },
              )
            ],
          ),
        ),
      ),
    );
  }

  DateTime? eventDate;

  Future<void> _selectEventDate(BuildContext context) async {
    DateTime initialDate = DateTime.now();
    DateTime firstDate = DateTime(DateTime.now().year - 100);
    DateTime lastDate = DateTime(DateTime.now().year + 1);

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
    );
    if (picked != null) {
      String selectEventDate =
          formatDateFromDate(dateTime: picked, format: 'dd MMM yyyy');
      dateController.text = selectEventDate;
      eventDate = picked;
    }
  }
}
