class ApplyLeaveModel {
  ApplyLeaveModel({
    required this.status,
    required this.responseMessage,
    required this.id,
  });

  bool? status;
  String? responseMessage;
  int? id;

  ApplyLeaveModel.fromJson(Map<String, dynamic> json) {
    status = json["status"];
    responseMessage = json["response_message"];
    if (json.containsKey('ID')) {
      id = int.parse(json['ID'].toString());
    }
  }
}
