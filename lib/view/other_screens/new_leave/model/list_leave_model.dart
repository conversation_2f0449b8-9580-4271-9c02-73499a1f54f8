// To parse this JSON data, do
//
//     final listLeavemodel = listLeavemodelFromJson(jsonString);

import 'dart:convert';

ListLeavemodel listLeavemodelFromJson(String str) =>
    ListLeavemodel.fromJson(json.decode(str));

String listLeavemodelToJson(ListLeavemodel data) => json.encode(data.toJson());

class ListLeavemodel {
  ListLeavemodel({
    required this.status,
    required this.leaveTypes,
  });

  bool status;
  LeaveTypes leaveTypes;

  factory ListLeavemodel.fromJson(Map<String, dynamic> json) => ListLeavemodel(
        status: json["status"],
        leaveTypes: LeaveTypes.fromJson(json["leave_types"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "leave_types": leaveTypes.toJson(),
      };
}

class LeaveTypes {
  LeaveTypes({
    required this.leaveTypes,
  });

  List<String> leaveTypes;

  factory LeaveTypes.fromJson(Map<String, dynamic> json) {
    List<String> tempLeaveTypes = [];
    json.forEach((key, value) => tempLeaveTypes.add(value.toString()));
    return LeaveTypes(leaveTypes: tempLeaveTypes);
  }

  toJson() {}
}

// abstract class IlistModel {
//   Map get iTypes;
//   String? get iStatus;
// }
