import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class UnderlinedTextFieldWidget extends StatelessWidget {
  final TextEditingController controller;
  final bool? enabled;
  final String? labelText;
  final String? hintText;
  final TextStyle? hintStyle;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final TextCapitalization textCapitalization;
  final bool obscureText;
  final Color borderColor;
  final InputBorder? border;
  final Widget? suffixIcon;
  final Widget? suffix;
  final Widget? prefixIcon;
  final FocusNode? focusNode;
  final void Function()? onEditingComplete;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final int? maxLines;
  const UnderlinedTextFieldWidget(
      {super.key,
      required this.controller,
      this.labelText,
      this.hintText,
      this.enabled = true,
      this.validator,
      this.keyboardType,
      this.textCapitalization = TextCapitalization.words,
      this.obscureText = false,
      this.borderColor = const Color(0xFFDEE7FF),
      this.border,
      this.prefixIcon,
      this.suffixIcon,
      this.focusNode,
      this.onEditingComplete,
      this.onChanged,
      this.maxLines,
      this.onTap,
      this.suffix,
      this.hintStyle});

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      enabled: enabled,
      keyboardType: keyboardType,
      obscureText: obscureText,
      textCapitalization: textCapitalization,
      focusNode: focusNode,
      onEditingComplete: onEditingComplete,
      onChanged: onChanged,
      style: GoogleFonts.rubik(color: Colors.black),
      maxLines: obscureText ? 1 : maxLines,
      readOnly: true,
      onTap: onTap,
      validator: validator,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: hintStyle,
        suffixIcon: suffixIcon,
        suffix: suffix,
        labelText: labelText,
        labelStyle: GoogleFonts.rubik(
          color: const Color(0xFF8391B5),
          //ithinte mumb undaayirunnath == const Color(0xFF8391B5),
        ),
        border: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: Color(0xFFDEE7FF),
          ),
        ),
        enabledBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.grey),
        ),
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: Colors.grey,
          ),
        ),
      ),
    );
  }
}
