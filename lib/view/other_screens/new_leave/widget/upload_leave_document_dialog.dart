import 'package:flutter/material.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:provider/provider.dart';

class LeaveDocUploadDialog extends StatelessWidget {
  final BuildContext ctx;
  const LeaveDocUploadDialog({super.key, required this.ctx});

  @override
  Widget build(BuildContext context) {
    LeaveApplicationProvider provider =
        Provider.of<LeaveApplicationProvider>(context, listen: false);
    return AlertDialog(
      title: const Text("Options"),
      content: SingleChildScrollView(
        child: ListBody(
          children: [
            GestureDetector(
              child: const Text("Capture image from camera"),
              onTap: () async {
                await provider.openCamera(context);
              },
            ),
            const Padding(
              padding: EdgeInsets.all(10),
            ),
            GestureDetector(
              child: const Text("Upload an image from gallery"),
              onTap: () async {
                await provider.openGallery(context);
              },
            ),
            const Padding(
              padding: EdgeInsets.all(10),
            ),
            GestureDetector(
              child: const Text("Pick file from memory"),
              onTap: () async {
                await provider.pickFile(context: context);
              },
            ),
          ],
        ),
      ),
    );
  }
}
