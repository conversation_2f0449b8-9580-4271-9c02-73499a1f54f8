// ignore_for_file: use_build_context_synchronously

import 'dart:developer';

import 'package:dotted_border/dotted_border.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/model/leave_overview_model.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/view/other_screens/new_leave/view_leave_file.dart';
import 'package:e8_hr_portal/view/other_screens/new_leave/widget/upload_leave_document_dialog.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:e8_hr_portal/view/widgets/hisense_drop_down_tile.dart';
import 'package:e8_hr_portal/view/widgets/hisense_text_form_field.dart';
import 'package:provider/provider.dart';
import '../../widgets/type_ahead_form_field_widget.dart';

class NewLeaveScreens extends StatefulWidget {
  final bool isComingFromOverViewScreen;
  final LeaveDetails? leaveDetails;
  final bool isRequestAgain;
  const NewLeaveScreens(
      {this.isComingFromOverViewScreen = false,
      this.leaveDetails,
      super.key,
      this.isRequestAgain = false});
  //leave_type
  //day_type
  //start_date
  //end_date
  //day_count
  //reason
  //leave_doc
//reportingPersonId
  @override
  State<NewLeaveScreens> createState() => _NewLeaveScreensState();
}

class _NewLeaveScreensState extends State<NewLeaveScreens> {
  final _newLeaveFormkey = GlobalKey<FormState>();
  final TextEditingController dateController = TextEditingController();
  // final TextEditingController _remainingLeaveController =
  //     TextEditingController();
  final TextEditingController _numberOfDaysController = TextEditingController();
  final TextEditingController _leaveReasonController = TextEditingController();
  final TextEditingController _staffInChargeController =
      TextEditingController();

  bool colorSwicth = true;
  bool colorSwicth2 = true;
  int index = 0;
  int afterNoon = 0;
  num? dateDiff = 1;
  bool check = true;
  String? day = 'Afternoon';
  String? dateDifference;
  List<String> checkStaff = [];
  bool validPolicy = false;

  @override
  void initState() {
    init();
    LeaveApplicationProvider provider =
        Provider.of<LeaveApplicationProvider>(context, listen: false);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _calculateDays(provider);
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // LeaveApplicationProvider provider =reportingPersonId
    //     Provider.of<LeaveApplicationProvider>(context, listen: false);

    return CustomScaffold(
      screenTitle: 'Apply Leave',
      body: SafeArea(
        child: Form(
          key: _newLeaveFormkey,
          // autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Column(
              children: [
                Expanded(
                  child: ListView(
                    shrinkWrap: true,
                    physics: const BouncingScrollPhysics(),
                    children: [
                      SizedBox(height: h * 35),
                      _subTitile(subtitle: 'Leave Type', isMandatory: true),
                      Consumer<LeaveApplicationProvider>(
                        builder: (context, provider, _) {
                          return HisenseDropdownTile(
                            title: '',
                            hintText: 'Select leave type',
                            hintStyle: tsS14w400454444,
                            style: tsS14w400454444,
                            validator: (value) {
                              if (value == null) {
                                return 'Select leave type';
                              }
                              return null;
                            },
                            onChanged: (int? value) {
                              if (value != null) {
                                provider.selectedLeaveType = value;
                                try {
                                  provider.remainingLeaves = provider
                                          .leaveBalanceModel?.data
                                          ?.firstWhere((element) =>
                                              element.leaveType?.id == value)
                                          .availableLeave ??
                                      0;
                                } catch (e) {
                                  provider.remainingLeaves = 0;
                                }
                              }
                            },
                            value: provider.selectedLeaveType,
                            items: provider.leaveTypesList?.data?.map((e) {
                              return DropdownMenuItem<int>(
                                value: e.id,
                                child: Text(e.title.toString()),
                              );
                            }).toList(),
                          );
                        },
                      ),
                      Consumer<LeaveApplicationProvider>(
                        builder: (context, provider, _) {
                          if (provider.selectedLeaveType == 2) {
                            return const SizedBox();
                          }

                          // if (provider.selectedLeaveType != null) {
                          //   print('${provider.leaveBalanceModel?.data?.length} LLLl');
                          //   print(provider.selectedLeaveType);

                          //   // WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
                          //   //   if (remainingLeaves > 1) {
                          //   //     _remainingLeaveController.text =
                          //   //         '$remainingLeaves Leaves';
                          //   //   } else if (remainingLeaves <= 1) {
                          //   //     _remainingLeaveController.text =
                          //   //         '$remainingLeaves Leave';
                          //   //   }
                          //   // });
                          // }

                          return Column(
                            children: [
                              SizedBox(height: h * 15),
                              _subTitile(subtitle: 'Remaining Leaves'),
                              // HisenseTextFormField(
                              //   controller: _remainingLeaveController,
                              //   hintText: 'Remaining Leaves',
                              //   hintStyle: tsS14w400454444,
                              //   textStyle: tsS14w400454444,
                              //   keyboardType: TextInputType.text,
                              //   // validator: Validator.text,
                              //   enabled: false,
                              // ),
                              Container(
                                padding: const EdgeInsets.only(left: 10),
                                alignment: Alignment.centerLeft,
                                height: h * 48,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                    color: ThemeColors.colorE3E3E3,
                                    borderRadius: BorderRadius.circular(5)),
                                child: Text(
                                  (provider.remainingLeaves != null &&
                                          (provider.remainingLeaves == 0 ||
                                              provider.remainingLeaves == 1 ||
                                              provider.remainingLeaves == 0.5))
                                      ? '${provider.remainingLeaves} Leave'
                                      : (provider.remainingLeaves != null &&
                                              provider.remainingLeaves! > 1)
                                          ? '${provider.remainingLeaves} Leaves'
                                          : 'Remaining Leaves',
                                  style: tsS14w400454444,
                                ),
                              )
                            ],
                          );
                        },
                      ),
                      SizedBox(height: h * 15),
                      _subTitile(subtitle: 'Leave Day Type', isMandatory: true),
                      Consumer<LeaveApplicationProvider>(
                        builder: (context, provider, _) {
                          return HisenseDropdownTile(
                            title: '',
                            hintText: 'Select day type',
                            hintStyle: tsS14w400454444,
                            style: tsS14w400454444,
                            validator: (value) {
                              if (value == null) {
                                return 'Select day type';
                              }
                              return null;
                            },
                            value: provider.selectedDayType,
                            onChanged: (int? value) {
                              if (value != null) {
                                WidgetsBinding.instance.addPostFrameCallback(
                                  (timeStamp) {
                                    provider.selectedDayType = value;
                                    _calculateDays(provider);
                                  },
                                );
                              }
                            },
                            items: provider.dayTypesModel.map((item) {
                              return DropdownMenuItem(
                                value: item.id,
                                child: Text(
                                  item.text,
                                  style: tsS14w400454444,
                                ),
                              );
                            }).toList(),
                          );
                        },
                      ),
                      SizedBox(height: h * 15),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Consumer<LeaveApplicationProvider>(
                            builder: (context, provider, _) {
                              return _calenderWidget(
                                isMandatory: true,
                                subtitle: 'From',
                                date: provider.formattedFromDate,
                                onTap: () async {
                                  // if (Platform.isAndroid) {
                                  await provider.selectFromDate(
                                      context: context);
                                  //commented on 2 nov 2023 for remaining leaves count bug
                                  // provider.remainigLeavesWithDate(
                                  //     leaveType:
                                  //         provider.selectedLeaveType.toString(),
                                  //     startDate: formatDateFromDate(
                                  //         dateTime: provider.selectedFromDate!,
                                  //         format: 'yyyy-MM-dd'));
                                  _calculateDays(provider);

                                  // } else if (Platform.isIOS) {
                                  //   await provider.selectFromDateIOS(ctx: context);
                                  // }
                                },
                              );
                            },
                          ),
                          Consumer<LeaveApplicationProvider>(
                            builder: (context, provider, _) {
                              if (provider.selectedDayType != 2 &&
                                  provider.selectedDayType != 3) {
                                return _calenderWidget(
                                  isMandatory: true,
                                  subtitle: 'To',
                                  date: provider.formattedToDate,
                                  onTap: () async {
                                    // if (Platform.isAndroid) {

                                    await provider.selectToDate(
                                        context: context);
                                    _calculateDays(provider);

                                    // } else if (Platform.isIOS) {
                                    //   await provider.selectToDateIOS(ctx: context);
                                    // }
                                  },
                                );
                              }
                              return const SizedBox();
                            },
                          ),
                        ],
                      ),
                      SizedBox(height: h * 15),
                      _subTitile(subtitle: 'Number of days'),
                      Consumer<LeaveApplicationProvider>(
                        builder: (context, provider, _) {
                          // if (widget.isComingFromOverViewScreen &&
                          //     widget.leaveDetails?.endDate != null &&
                          //     dateDifference != null &&
                          //     dateDifference != '') {
                          //   // WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
                          //   // _numberOfDaysController.text = dateDifference!;
                          //   _calculateDays();
                          //   // });
                          // }

                          // WidgetsBinding.instance.addPostFrameCallback((timeStamp) {

                          //   //   // else {
                          //   //   //   _numberOfDaysController.clear();
                          //   //   // }
                          // });

                          return HisenseTextFormField(
                            controller: _numberOfDaysController,
                            hintText: 'Number of days',
                            hintStyle: tsS14w400454444,
                            textStyle: tsS14w400454444,
                            keyboardType: TextInputType.text,
                            validator: Validator.text,
                            enabled: false,
                          );
                        },
                      ),
                      SizedBox(height: h * 15),
                      _subTitile(subtitle: 'Leave Reason', isMandatory: true),
                      TextFieldWidget(
                        controller: _leaveReasonController,
                        hintStyle: tsS12w400c9F9F9F,
                        textStyle: tsS14w400454444,
                        keyboardType: TextInputType.text,
                        textCapitalization: TextCapitalization.sentences,
                        maxLines: 5,
                        borderColor: ThemeColors.colorE3E3E3,
                        validator: Validator.reason,
                        hintText: 'Write Something',
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                              RegExp(r'[a-zA-Z\s]'))
                        ],
                      ),
                      SizedBox(height: h * 15),
                      Consumer<LeaveApplicationProvider>(
                          builder: (context, pro, _) {
                        return _subTitile(
                          subtitle: 'Upload Recommended Documents',
                          // isMandatory: pro.selectedLeaveType == 3 ? true : false
                        );
                      }),
                      _uploadYourDocumentWidget(),
                      SizedBox(height: h * 15),
                      _subTitile(
                          subtitle: 'Staff in charge on absence',
                          isMandatory: true),
                      _typeAheadTextFiledField(hintText: 'Staff in charge'),
                      SizedBox(height: h * 30),
                      SizedBox(height: h * 61),
                    ],
                  ),
                ),
                _buttons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _subTitile({required String subtitle, bool isMandatory = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: Row(
            children: [
              Text(
                subtitle,
                style: tsS14w400c30292F,
              ),
              if (isMandatory)
                Text(
                  '*',
                  style: tsS14w400cFA0000,
                ),
            ],
          ),
        ),
        SizedBox(height: h * 4),
      ],
    );
  }

  String _calculateDays(LeaveApplicationProvider provider) {
    // LeaveApplicationProvider provider =
    //     Provider.of<LeaveApplicationProvider>(context, listen: false);
    String formattedFromDate = formatDateFromString(
        provider.formattedFromDate, 'dd MMM yyyy', 'yyyy-MM-dd');
    String formattedToDate = formatDateFromString(
        provider.formattedToDate, 'dd MMM yyyy', 'yyyy-MM-dd');

    provider.calculateDaysBetween(
        context: context,
        selectedFromDate: DateTime.parse(formattedFromDate),
        selectedToDate: DateTime.parse(formattedToDate));

    if (provider.selectedDayType == 1) {
      num d = provider.daysBetweenPlus;

      num days = (d);

      if (days > 1) {
        _numberOfDaysController.text = '$days Days';
      }
      if (days <= 1 && days > 0) {
        _numberOfDaysController.text = '$days Day';
      }

      if (provider.selectedToDate == provider.selectedFromDate) {
        _numberOfDaysController.text = '1 Day';
        days = 1;
      }
      if (formattedFromDate == formattedToDate) {
        _numberOfDaysController.text = '1 Day';
        days = 1;
      }
      return days.toString();
    }

    if (provider.selectedDayType == 2 || provider.selectedDayType == 3) {
      _numberOfDaysController.text = '0.5 Day';
      return _numberOfDaysController.text;
    }

    if (provider.selectedDayType == 1) {
      num d = provider.daysBetweenPlus;
      num days = (d);
      if (days > 1) {
        _numberOfDaysController.text = '$days Days';
      }
      if (days <= 1) {
        _numberOfDaysController.text = '$days Day';
      }
      if (provider.selectedToDate == provider.selectedFromDate) {
        _numberOfDaysController.text = '1 Day';
        days = 1;
      }
      return days.toString();
    }
    if (provider.selectedDayType == 2 || provider.selectedDayType == 3) {
      _numberOfDaysController.text = '0.5 Day';
      return _numberOfDaysController.text;
    }
    // if (provider.selectedDayType == 2 || provider.selectedDayType == 3) {
    //   num days = provider.daysBetweenPlus + 1;
    //   if (days > 1) {
    //     _numberOfDaysController.text = '${(days) / 2} Days';
    //   }
    //   if (days <= 1) {
    //     _numberOfDaysController.text = '${(days) / 2} Day';
    //   }
    //   return (days / 2).toString();
    // }

    return '';
  }

  // Widget desabledTextFormField() {
  //   return Container(
  //     height: h * 45,
  //     width: w * 343,
  //     alignment: Alignment.centerLeft,
  //     padding: const EdgeInsets.all(11),
  //     decoration: BoxDecoration(
  //         borderRadius: BorderRadius.circular(5),
  //         color: ThemeColors.colorE3E3E3),
  //     child: Text(
  //       '3 days',
  //       style: tsS14w400454444,
  //     ),
  //   );
  // }

  Widget _calenderWidget(
      {required String subtitle,
      required bool isMandatory,
      required String date,
      required VoidCallback onTap}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _subTitile(subtitle: subtitle, isMandatory: isMandatory),
        Align(
          alignment: Alignment.centerLeft,
          child: GestureDetector(
            onTap: onTap,
            child: Container(
              height: h * 48,
              width: w * 164,
              padding: EdgeInsets.symmetric(horizontal: w * 11),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: ThemeColors.colorFFFFFF,
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: ThemeColors.colorE3E3E3, width: 1),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    date,
                    style: tsS14w400454444,
                  ),
                  Image.asset(
                    'assets/icons/calendar_black.png',
                    height: h * 18,
                    width: w * 18,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _uploadYourDocumentWidget() {
    return DottedBorder(
      dashPattern: [w * 10, w * 7], strokeWidth: 1.4,
      // color: ThemeColors.colorD9D9D9,
      color: validPolicy ? ThemeColors.colorFF0000 : ThemeColors.colorD9D9D9,

      borderType: BorderType.RRect,
      radius: const Radius.circular(3),
      // padding: EdgeInsets.all(6),
      child: GestureDetector(
        onTap: () {
          showDialog(
            context: context,
            builder: (context) => LeaveDocUploadDialog(ctx: context),
          );
        },
        child: ClipRRect(
          borderRadius: const BorderRadius.all(
            Radius.circular(3),
          ),
          child: Consumer<LeaveApplicationProvider>(
            builder: (context, provider, _) {
              return Container(
                width: w * 343,
                color: ThemeColors.colorFFFFFF,
                alignment: Alignment.center,
                padding: EdgeInsets.only(top: h * 30, bottom: h * 22),
                child: Column(
                  children: [
                    if (provider.pickedFile == null)
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Image.asset('assets/icons/upload.png',
                              height: h * 24, width: w * 29.34),
                          SizedBox(height: h * 8),
                          Text(
                            'Click to upload your document',
                            style: tsS14w400c30292F,
                          ),
                          SizedBox(height: h * 3),
                          Text(
                            'Supports : JPEG, PNG, PDF',
                            style: tsS12w400979797,
                          ),
                        ],
                      ),
                    Consumer<LeaveApplicationProvider>(
                      builder: (context, provider, _) {
                        if (provider.pickedFile == null &&
                            provider.leaveOverviewModel?.leaveDetails
                                    ?.leaveDoc ==
                                null) {
                          return const SizedBox();
                        }
                        return TextButton(
                          onPressed: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => const ViewLeaveFile(),
                              ),
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                                border: Border.all(color: Colors.black),
                                borderRadius: BorderRadius.circular(8)),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              // mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                Expanded(
                                  child: Text(
                                    provider.pickedFile != null
                                        ? '${provider.pickedFile?.name}'
                                        : 'Document',
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                if (provider.pickedFile != null)
                                  InkWell(
                                    onTap: () async {
                                      provider.pickedFile = null;
                                    },
                                    child: const Icon(
                                      Icons.delete_outline_sharp,
                                      color: Colors.black,
                                    ),
                                  )
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  void init() {
    LeaveApplicationProvider provider =
        Provider.of<LeaveApplicationProvider>(context, listen: false);
    _numberOfDaysController.clear();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      provider.selectedLeaveType = null;
      provider.selectedDayType = null;
      provider.finalImageFile = null;
      provider.selectedFromDate = DateTime(
          DateTime.now().year, DateTime.now().month, DateTime.now().day);
      provider.selectedToDate = DateTime(
          DateTime.now().year, DateTime.now().month, DateTime.now().day);
      provider.formattedFromDate =
          formatDateFromDate(dateTime: DateTime.now(), format: 'dd MMM yyyy');
      provider.formattedToDate = formatDateFromDate(
          dateTime: provider.selectedFromDate!, format: 'dd MMM yyyy');
      provider.daysBetweenPlus = 0;
      provider.daysBetween = 0;
      // _remainingLeaveController.clear();
      _numberOfDaysController.clear();
      _leaveReasonController.clear();
      if (widget.isComingFromOverViewScreen == true) {
        isComingFromOverviewInitFunction();
      }

      setState(() {});
    });
  }

  void isComingFromOverviewInitFunction() {
    LeaveApplicationProvider provider =
        Provider.of<LeaveApplicationProvider>(context, listen: false);

    if (widget.leaveDetails?.leaveType != null) {
      String leaveType = widget.leaveDetails!.leaveType!.toLowerCase().trim();
      switch (leaveType) {
        case 'annual leave':
          provider.selectedLeaveType = 1;
          break;
        case 'unpaid leave':
          provider.selectedLeaveType = 2;
          break;
        case 'sick leave':
          provider.selectedLeaveType = 3;
          break;
      }
    }
    if (widget.leaveDetails?.dayType != null) {
      String dayType = widget.leaveDetails!.dayType!.toLowerCase().trim();

      switch (dayType) {
        case 'full day':
          provider.selectedDayType = 1;
          break;
        case 'forenoon':
          provider.selectedDayType = 2;
          break;
        case 'afternoon':
          provider.selectedDayType = 3;
          break;
      }
    }
    try {
      provider.remainingLeaves = provider.leaveBalanceModel?.data
              ?.firstWhere((element) =>
                  element.leaveType?.name == widget.leaveDetails?.leaveType)
              .availableLeave ??
          0;
    } catch (e) {
      provider.remainingLeaves = 0;
    }

    if (widget.leaveDetails?.startDate != null &&
        widget.leaveDetails?.endDate != null) {
      String formattedStartDate = formatDateFromString(
          widget.leaveDetails!.startDate.toString(),
          'dd MMM,yyyy',
          'yyyy-MM-dd hh:mm:ss');
      String formattedEndDate = formatDateFromString(
          widget.leaveDetails!.endDate.toString(),
          'dd MMM,yyyy',
          'yyyy-MM-dd hh:mm:ss');
      // DateTime now = DateTime.now();
      // DateTime startDate = DateTime.parse(formattedStartDate);
      DateTime endDate = DateTime.parse(formattedEndDate);

      // if (startDate.isAfter(now)) {
      // provider.selectedFromDate = startDate;
      provider.formattedFromDate = formatDateFromString(
          formattedStartDate, 'yyyy-MM-dd hh:mm:ss', 'dd MMM yyyy');

      provider.daysBetweenPlus = widget.leaveDetails!.dayCount!.toInt();

      provider.formattedToDate = formatDateFromString(
          formattedEndDate, 'yyyy-MM-dd hh:mm:ss', 'dd MMM yyyy');
      provider.selectedToDate = endDate;

      // }
      // if (endDate.isAfter(now)) {
      //   provider.selectedToDate = endDate;
      //   provider.formattedToDate = formatDateFromString(
      //       formattedEndDate, 'yyyy-MM-dd hh:mm:ss', 'dd MMM yyyy');
      //   int difference = endDate.difference(startDate).inDays + 1;

      //   // _numberOfDaysController.text = '$difference Days';
      //   dateDifference = '$difference Days';

      //   provider.daysBetweenPlus = difference;
      // }
    }
    if (widget.leaveDetails?.reason != null ||
        widget.leaveDetails?.reason != '') {
      _leaveReasonController.text = widget.leaveDetails!.reason!;
    }

    if (widget.leaveDetails?.staffIncharge?.id != null) {
      provider.selectedUserID = widget.leaveDetails?.staffIncharge?.id;
      _staffInChargeController.text =
          widget.leaveDetails?.staffIncharge?.name ?? '';
    }
  }

  Widget _typeAheadTextFiledField({required String hintText}) {
    return Consumer<LeaveApplicationProvider>(
      builder: (context, provider, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TypeAheadFormFieldWidget(
              controller: _staffInChargeController,
              hintText: hintText,
              suffixIcon: provider.selectedUserID != null
                  ? IconButton(
                      onPressed: () {
                        _staffInChargeController.clear();
                        provider.selectedUserID = null;
                      },
                      icon: Icon(
                        Icons.close,
                        color: ThemeColors.color000000,
                      ),
                    )
                  : Icon(
                      Icons.close,
                      color: ThemeColors.color000000,
                    ),
              itemBuilder: (context, suggestion) {
                checkStaff
                    .add('${suggestion.firstName} ${suggestion.lastName}');

                return ListTile(
                  title: Text('${suggestion.firstName} ${suggestion.lastName}'),
                );
              },
              validator: (value) {
                if ((value == null ||
                        value.isEmpty ||
                        !checkStaff.contains(value)) &&
                    provider.selectedUserID == null) {
                  return 'Select staff in charge';
                }
                return null;
              },
              onSelected: (suggestion) {
                _staffInChargeController.text =
                    '${suggestion.firstName} ${suggestion.lastName}';
                provider.selectedUserID = suggestion.id;
                log('_staffInChargeController.text  == ${_staffInChargeController.text}');
              },
              suggestionsCallback: (pattern) {
                return provider.staffInChargeList!
                    .where((item) => '${item.firstName} ${item.lastName}'
                        .toLowerCase()
                        .startsWith(pattern.toLowerCase()))
                    .toList();
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buttons() {
    return Padding(
      padding: EdgeInsets.only(top: 10, bottom: 20 * h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Consumer<LeaveApplicationProvider>(
            builder: (context, provider, _) {
              return GeneralButton(
                height: h * 50,
                width: w * 164,
                // isDisabled: true,
                isDisabledColor: true,
                title: 'Cancel',
                textStyle: tsS18w600cFFFFFF,
                onPressed: () {
                  WidgetsBinding.instance.addPostFrameCallback(
                    (timeStamp) {
                      provider.selectedLeaveType = null;
                      provider.selectedReportingPerson = null;
                    },
                  );
                  Navigator.pop(context);
                },
              );
            },
          ),
          Consumer<LeaveApplicationProvider>(
            builder: (context, provider, _) {
              return GeneralButton(
                height: h * 50,
                width: w * 164,
                title: 'Apply',
                textStyle: tsS18w600cFFFFFF,
                onPressed: () async {
                  if (_newLeaveFormkey.currentState!.validate()) {
                    if (LoginModel.policyId == 1 &&
                        provider.selectedLeaveType == 3 &&
                        (provider.pickedFile == null &&
                            !widget.isComingFromOverViewScreen)) {
                      setState(() {
                        validPolicy = true;
                      });
                      return;
                    } else {
                      setState(() {
                        validPolicy = false;
                      });
                    }
                    EasyLoading.show();

                    final navigator = Navigator.of(context);
                    if (provider.selectedDayType == 2 ||
                        provider.selectedDayType == 3) {
                      provider.selectedToDate = provider.selectedFromDate;
                      provider.formattedToDate = provider.formattedFromDate;
                    }

                    String formattedFromDate = formatDateFromString(
                        provider.formattedFromDate,
                        'dd MMM yyyy',
                        'yyyy-MM-dd');
                    String formattedToDate = formatDateFromString(
                        provider.formattedToDate, 'dd MMM yyyy', 'yyyy-MM-dd');
                    String? noOfDays;

                    noOfDays = _calculateDays(provider);
                    if (provider.selectedDayType == 2 ||
                        provider.selectedDayType == 3) {
                      provider.daysBetweenPlus = 0;
                      provider.daysBetween = 0;
                      noOfDays = '0.5';
                      _numberOfDaysController.text = '$noOfDays Day';
                    }

                    if (!widget.isComingFromOverViewScreen) {
                      // if (provider.pickedFile == null &&
                      //     provider.selectedLeaveType == 3) {
                      //   showToastText('Document is Mandatory');
                      // } else {
                      await provider.applyForLeave(
                          fromDate: formattedFromDate,
                          toDate: formattedToDate,
                          noOfDays: noOfDays,
                          leavefor: provider.selectedDayType.toString(),
                          reason: _leaveReasonController.text,
                          context: context);

                      // }
                    }

                    // // ______________________________update api calling_________________________________///
                    else if (widget.isComingFromOverViewScreen &&
                        widget.isRequestAgain == false) {
                      bool isAllowPop = await provider.editAndApplyForLeave(
                          leaveID: '${widget.leaveDetails?.id}',
                          leavefor: provider.selectedDayType.toString(),
                          startDate: formattedFromDate,
                          endDate: formattedToDate,
                          reason: _leaveReasonController.text,
                          noOfDays: noOfDays,
                          context: context);
                      if (isAllowPop && mounted) {
                        navigator.pop();
                      }
                      provider.currentPageLeaveTypes = 0;
                      provider.pagingControllerLeaveTypes?.refresh();
                    } else if (widget.isComingFromOverViewScreen &&
                        widget.isRequestAgain == true) {
                      // log('request again');
                      await provider.applyForLeave(
                          fromDate: formattedFromDate,
                          toDate: formattedToDate,
                          noOfDays: noOfDays,
                          leavefor: provider.selectedDayType.toString(),
                          reason: _leaveReasonController.text,
                          context: context);
                      Navigator.of(context).pop();
                    }

                    await provider.getLeaveRefresh();

                    EasyLoading.dismiss();
                  }
                },
              );
            },
          ),
        ],
      ),
    );
  }
}
