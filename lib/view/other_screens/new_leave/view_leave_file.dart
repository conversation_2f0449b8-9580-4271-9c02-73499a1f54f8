import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:photo_view/photo_view.dart';
import 'package:provider/provider.dart';

class ViewLeaveFile extends StatefulWidget {
  const ViewLeaveFile({super.key});

  @override
  State<ViewLeaveFile> createState() => _ViewLeaveFileState();
}

class _ViewLeaveFileState extends State<ViewLeaveFile> {
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "",
      horizontalPadding: 0,
      body: Consumer<LeaveApplicationProvider>(
        builder: (context, provider, _) {
          String? fileExtention;
          String? ext;
          if (provider.pickedFileFilePath != null) {
            ext = provider.pickedFileFilePath!
                .substring(provider.pickedFileFilePath!.length - 4);
            if (ext == ".pdf" ||
                ext == ".png" ||
                // ext == ".doc" ||
                ext == ".jpg") {
              fileExtention = ext;
            } else if (ext == "jpeg") {
              fileExtention = ".jpeg";
            }
            // else if (ext == "docx") {
            //   fileExtention = ".docx";
            // }
          }
          if (provider.leaveOverviewModel?.leaveDetails?.leaveDoc != null) {
            ext = provider.leaveOverviewModel!.leaveDetails!.leaveDoc
                .toString()
                .substring(provider.leaveOverviewModel!.leaveDetails!.leaveDoc
                        .toString()
                        .length -
                    4);
            if (ext == ".pdf" ||
                ext == ".png" ||
                // ext == ".doc" ||
                ext == ".jpg") {
              fileExtention = ext;
            } else if (ext == "jpeg") {
              fileExtention = ".jpeg";
            }
            // else if (ext == "docx") {
            //   fileExtention = ".docx";
            // }
          }

          return Column(
            children: [
              if (provider.pickedFile == null &&
                  provider.leaveOverviewModel?.leaveDetails?.leaveDoc == null)
                const Center(child: Text('No file selected.')),
              if (provider.pickedFile != null && (fileExtention == ".pdf"))
                Expanded(
                  child: PDFView(
                    filePath: provider.pickedFile?.path,
                  ),
                ),
              if (provider.pickedFile != null &&
                  (fileExtention == ".png" ||
                      fileExtention == ".jpg" ||
                      fileExtention == ".jpeg"))
                Expanded(
                  child: Image.file(
                    File(provider.pickedFile!.path),
                  ),
                ),
              if (provider.leaveOverviewModel?.leaveDetails?.leaveDoc != null &&
                  (fileExtention == ".png" ||
                      fileExtention == ".jpg" ||
                      fileExtention == ".jpeg"))
                Expanded(
                  child: PhotoView(
                      imageProvider: CachedNetworkImageProvider(
                    provider.leaveOverviewModel!.leaveDetails!.leaveDoc
                        .toString(),
                  )),
                ),
              // if (provider.pickedFile != null && fileExtention == ".doc")
              //   ClipRRect(
              //     borderRadius: BorderRadius.circular(12),
              //     child: Container(
              //       height: h * 200,
              //       width: w * 200,
              //       color: Colors.red,
              //       child: SfPdfViewer.file(
              //         provider.pickedFile!,
              //       ),
              //     ),
              //   ),
            ],
          );
        },
      ),
    );
  }
}
