// ignore_for_file: use_build_context_synchronously

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/model/leave_overview_model.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/employee_relations/widget/photo_view.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/view/other_screens/new_leave/new_leave.dart';
import 'package:e8_hr_portal/view/other_screens/overview/widgets/cancel_button.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';

class OverViewScreen extends StatelessWidget {
  const OverViewScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Color? statusBgColor(String? status) {
      switch (status?.toLowerCase()) {
        case 'pending':
          return ThemeColors.colorFFF2EF;
        case 'in-progress':
          return ThemeColors.colorFFF2EF;
        case 'rejected':
          return ThemeColors.colorF64D44.withOpacity(0.14);
        case 'approved':
          return ThemeColors.color03AD9E.withOpacity(0.10);
        case 'cancelled':
          return ThemeColors.colorF64D44.withOpacity(0.14);
        case 'expired':
          return ThemeColors.colorF64D44.withOpacity(0.14);
        case 'holiday':
          return ThemeColors.colorF64D44.withOpacity(0.14);
        default:
          return null;
      }
    }

    TextStyle? statusStyle(String? status) {
      switch (status?.toLowerCase()) {
        case 'pending':
          return tsS12W6FE5B900;
        case 'in-progress':
          return tsS12W6FE5B900;
        case 'rejected':
          return tsS12w600cF64D44;
        case 'approved':
          return tsS12w600c519C66;
        case 'cancelled':
          return tsS12w600cF64D44;
        case 'expired':
          return tsS12w600cF64D44;
        case 'holiday':
          return tsS12w600cF64D44;
        default:
          return null;
      }
    }

    bool showCancelLeavebutton = false;
    final provider =
        Provider.of<LeaveApplicationProvider>(context, listen: false);
    LeaveDetails? leaveDetails = provider.leaveOverviewModel?.leaveDetails;
    String? leaveDoc = leaveDetails?.leaveDoc;
    String? docExtension = leaveDetails?.docExtension;
    String? dateCreated = leaveDetails?.createdAt;
    String? createdAt = dateCreated;
    String? dayType = leaveDetails?.dayType;
    String? status = leaveDetails?.status;
    int? leaveID = leaveDetails?.id;

    String text = 'Pending';

    switch (status) {
      case 'Pending':
        text = 'Pending';
        break;
      case 'In-progress':
        text = 'In-progress';
        break;
      case 'Approved':
        text = 'Approved';
        break;
      case 'Rejected':
        text = 'Rejected';
        break;
      case 'Cancelled':
        text = 'Cancelled';
        break;
      case 'Expired':
        text = 'Expired';
        break;
      case 'Holiday':
        text = 'Holiday';
        break;
    }

    return HisenseScaffold(
      screenTitle: 'Overview',
      actions: [
        _requestNewButton(
          context: context,
          leaveDetails: leaveDetails,
          text: text,
        )
      ],
      body: Consumer(
        builder: (context, provider, _) {
          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(15.0),
                    child: Consumer<LeaveApplicationProvider>(
                      builder: (context, provider, _) {
                        final data = provider.leaveOverviewModel;
                        return provider.leaveOverviewModel == null ||
                                data == null
                            ? const SizedBox()
                            : Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Text(
                                      'Leave Overview',
                                      style: tsS16w500,
                                    ),
                                  ),
                                  const SizedBox(height: 15),
                                  Container(
                                    padding: const EdgeInsets.all(15),
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius:
                                            BorderRadius.circular(12)),
                                    child: Column(
                                      children: [
                                        Row(
                                          children: [
                                            Container(
                                              height: 45 * h,
                                              width: 45 * w,
                                              margin: const EdgeInsets.only(
                                                  right: 10),
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                color: ThemeColors.colorFCC400
                                                    .withOpacity(0.10),
                                              ),
                                              child: Center(
                                                child: data.leaveDetails
                                                            ?.leaveType ==
                                                        'Annual Leave'
                                                    ? ImageIcon(
                                                        const AssetImage(
                                                            'assets/icons/calendar-2.png'),
                                                        color: ThemeColors
                                                            .colorFCC400,
                                                      )
                                                    : data.leaveDetails
                                                                ?.leaveType ==
                                                            'Sick Leave '
                                                        ? ImageIcon(
                                                            const AssetImage(
                                                                'assets/icons/hospital.png'),
                                                            color: ThemeColors
                                                                .colorFCC400,
                                                          )
                                                        : data.leaveDetails
                                                                    ?.leaveType ==
                                                                'Unpaid Leave'
                                                            ? ImageIcon(
                                                                const AssetImage(
                                                                    'assets/icons/calculator.png'),
                                                                color: ThemeColors
                                                                    .colorFCC400,
                                                              )
                                                            : ImageIcon(
                                                                const AssetImage(
                                                                    'assets/icons/work_update.png'),
                                                                color: ThemeColors
                                                                    .colorFCC400,
                                                              ),
                                              ),
                                            ),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                // mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Text(
                                                        data.leaveDetails
                                                                ?.leaveType ??
                                                            '',
                                                        style: tsS14w500Black,
                                                      ),
                                                      Column(
                                                        children: [
                                                          Container(
                                                            padding:
                                                                const EdgeInsets
                                                                    .fromLTRB(
                                                                    10,
                                                                    4,
                                                                    10,
                                                                    4),
                                                            decoration:
                                                                BoxDecoration(
                                                              color: statusBgColor(
                                                                  data.leaveDetails
                                                                      ?.status),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          8),
                                                            ),
                                                            child: Text(
                                                              data.leaveDetails
                                                                      ?.status ??
                                                                  '',
                                                              style: statusStyle(
                                                                  data.leaveDetails
                                                                      ?.status),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(height: 7),
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Expanded(
                                                        child: Row(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Padding(
                                                              padding: EdgeInsets
                                                                  .only(
                                                                      top: h *
                                                                          2),
                                                              child: ImageIcon(
                                                                const AssetImage(
                                                                    'assets/icons/calendar-3.png'),
                                                                color: ThemeColors
                                                                    .colorFCC400,
                                                                size: 12,
                                                              ),
                                                            ),
                                                            const SizedBox(
                                                                width: 5),
                                                            if (data.leaveDetails
                                                                        ?.startDate !=
                                                                    null &&
                                                                data.leaveDetails
                                                                        ?.endDate !=
                                                                    null)
                                                              Expanded(
                                                                child: Text(
                                                                  '${data.leaveDetails?.startDate.toString()} - ${data.leaveDetails?.endDate.toString()} • ${data.leaveDetails?.dayCount} ${data.leaveDetails!.dayCount! <= 1 ? 'Day' : 'Days'} • $dayType', //leavetype
                                                                  style:
                                                                      tsS12w400979797,
                                                                  overflow:
                                                                      TextOverflow
                                                                          .clip,
                                                                ),
                                                              ),
                                                          ],
                                                        ),
                                                      ),
                                                      const SizedBox(width: 10),
                                                      Text(
                                                        createdAt ?? '',
                                                        style: tsS10w400c4D4D4D,
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: h * 14),
                                        Align(
                                          alignment: Alignment.topLeft,
                                          child: Text(
                                            'Reason',
                                            style: tsS12w400979797,
                                          ),
                                        ),
                                        const SizedBox(
                                          height: 5,
                                        ),
                                        Align(
                                          alignment: Alignment.topLeft,
                                          child: Text(
                                            data.leaveDetails?.reason ?? '',
                                            style: tsS14w500Black,
                                          ),
                                        ),
                                        const SizedBox(
                                          height: 5,
                                        ),
                                        Divider(
                                          thickness: 1,
                                          color: ThemeColors.colorD9D9D9,
                                        ),
                                        /////Document section
                                        if (leaveDoc != null)
                                          Column(
                                            children: [
                                              Align(
                                                alignment: Alignment.topLeft,
                                                child: Text(
                                                  'Document',
                                                  style: tsS12w400979797,
                                                ),
                                              ),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                    'Document',
                                                    style: tsS14w500Black,
                                                  ),
                                                  SizedBox(
                                                    height: h * 30,
                                                    child: InkWell(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                      radius: 20,
                                                      onTap: () {
                                                        PageNavigator.push(
                                                          context: context,
                                                          route:
                                                              PhotoViewScreen(
                                                            image: leaveDoc,
                                                            extension:
                                                                docExtension,
                                                          ),
                                                        );
                                                      },
                                                      child: Row(
                                                        children: [
                                                          Text(
                                                            'View',
                                                            style: GoogleFonts.poppins(
                                                                fontSize: 12,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                color: ThemeColors
                                                                    .primaryColor,
                                                                decoration:
                                                                    TextDecoration
                                                                        .underline),
                                                          ),
                                                          SizedBox(
                                                              width: w * 2),
                                                          Image.asset(
                                                            'assets/icons/download_icon.png',
                                                            height: h * 11.67,
                                                            width: w * 11.67,
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              // const SizedBox(
                                              //   height: 5,
                                              // ),
                                              Divider(
                                                thickness: 1,
                                                color: ThemeColors.colorD9D9D9,
                                              ),
                                            ],
                                          ),
                                        const SizedBox(height: 10),
                                        Align(
                                          alignment: Alignment.topLeft,
                                          child: Text(
                                            'Staff in charge on absence',
                                            style: tsS12w400979797,
                                          ),
                                        ),
                                        const SizedBox(height: 9),
                                        Align(
                                          alignment: Alignment.topLeft,
                                          child: Text(
                                            data.leaveDetails?.staffIncharge
                                                    ?.name ??
                                                'Staff in charge on absence',
                                            style: tsS14w500Black,
                                          ),
                                        ),
                                        const SizedBox(
                                          height: 4,
                                        ),
                                        Divider(
                                          thickness: 1,
                                          color: ThemeColors.colorD9D9D9,
                                        ),
                                        // if (data.first.reportingPersonList!.isNotEmpty)
                                        Align(
                                          alignment: Alignment.topLeft,
                                          child: Text(
                                              'Approved by / Rejected by',
                                              style: tsS12w400979797),
                                        ),
                                        const SizedBox(height: 13),

                                        Row(
                                          children: [
                                            if (data.reportingPersonList !=
                                                null)
                                              _reportingPersonsWidget(
                                                  data: data),
                                            if (data.hrStatus != null)
                                              _hrApprovalWidget(
                                                  profilePic: data.hrStatus
                                                          ?.profilePic ??
                                                      '',
                                                  name:
                                                      data.hrStatus?.name ?? '',
                                                  isApprove: data.hrStatus
                                                          ?.isApprove ??
                                                      ''),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),

                                  const SizedBox(height: 20),
                                  // if (text.toLowerCase() == 'rejected')
                                  _hrRejectStatusBuilder(data),
                                  if (data.hrStatus?.comment != null)
                                    const SizedBox(height: 20),
                                  _rejectStatusBuilder(data),

                                  SizedBox(height: 66 * h)
                                ],
                              );
                      },
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
      floatingWidget: Consumer<LeaveApplicationProvider>(
        builder: (context, provider, _) {
          final data = provider.leaveOverviewModel;
          String? status = data?.leaveDetails?.status;
          // status = 'Cancelled';
          String stringDate = '${data?.leaveDetails?.startDate}';
          DateTime? startDate =
              stringToDateTime(date: stringDate, format: 'dd MMM,yyyy');
          DateTime now = DateTime(
              DateTime.now().year, DateTime.now().month, DateTime.now().day);
          if (startDate != null) {
            showCancelLeavebutton = !now.isBefore(startDate);
          }

          if ((status == 'Approved') && showCancelLeavebutton == true) {
            return const SizedBox();
          }
          // if ((status == 'Pending' || status == 'Approved')) {
          //   return _cancelButton(
          //       context: context, leaveID: leaveID ?? 0, status: status);
          // }
          if (status == 'Rejected' ||
              status == 'Cancelled' ||
              status == 'Holiday') {
            return const SizedBox();
          }

          return _cancelButton(
              context: context, leaveID: leaveID ?? 0, status: status);
        },
      ),
    );
  }

  Widget _hrRejectStatusBuilder(LeaveOverviewModel data) {
    HrStatus? hrStatus = data.hrStatus;
    if (['false', 'rejected', 'true'].contains(
          hrStatus?.isApprove.toString().toLowerCase(),
        ) &&
        hrStatus?.comment != null) {
      return _leaveRejectedCard(
        comment: hrStatus?.comment ?? '',
        rejectedBy: hrStatus?.name ?? '',
        rejectedDate: hrStatus?.createAt ?? '',
        appOrReject: hrStatus?.isApprove ?? '',
      );
    }
    return const SizedBox();
  }

  Widget _rejectStatusBuilder(LeaveOverviewModel data) {
    if (data.reportingPersonList != null) {
      return ListView.separated(
        itemCount: data.reportingPersonList!.length,
        shrinkWrap: true,
        physics: const ScrollPhysics(),
        itemBuilder: (context, index) {
          ReportingPersonList? e = data.reportingPersonList?[index];

          if (['false', 'rejected', 'true']
                  .contains(e?.isApprove.toString().toLowerCase()) &&
              e?.comment != null) {
            return _leaveRejectedCard(
              comment: e?.comment ?? '',
              rejectedBy: e?.name ?? '',
              rejectedDate: e?.createAt ?? '',
              appOrReject: e?.isApprove ?? '',
            );
          }
          return const SizedBox();
        },
        separatorBuilder: (context, index) {
          return SizedBox(height: h * 10);
        },
        // children: data.reportingPersonList!.map((e) {
        //   return _leaveRejectedCard(
        //       comment: e.comment ?? '', rejectedBy: e.name ?? '');
        // }).toList(),
      );
    }
    return const SizedBox();
  }

  Widget _cancelButton(
      {required BuildContext context, required int leaveID, String? status}) {
    return Padding(
        padding: const EdgeInsets.fromLTRB(30, 1, 0, 0),
        child: CancelButton(
          height: h * 56,
          width: w * 343,
          title: status == 'Approved' ? 'Cancel Leave' : 'Cancel Leave Request',
          textStyle: tsS18w600cFFFFFF,
          onPressed: () async {
            showModalBottomSheet(
              context: context,
              isDismissible: true,
              backgroundColor: ThemeColors.colorFFFFFF,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              builder: (context) {
                return _showModalBottomSheet(
                    context: context, leaveID: leaveID);
              },
            );
          },
        )
        // : const SizedBox(),
        );
  }

  Widget _showModalBottomSheet(
      {required BuildContext context, required int leaveID}) {
    return Container(
      height: h * 249,
      decoration: BoxDecoration(
        // color: ThemeColors.colorFFFFFF,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          SizedBox(height: h * 14),
          Container(
            height: h * 5,
            width: w * 134,
            decoration: BoxDecoration(
              color: ThemeColors.colorEAEBED,
              borderRadius: BorderRadius.circular(100),
            ),
          ),
          SizedBox(height: h * 44),
          Text('Cancel this leave', style: tsS26w500cFFFFFF),
          SizedBox(height: h * 2),
          Text('Are you sure about cancelling this leave ? ',
              style: tsS16w500c9F9F9F),
          SizedBox(height: h * 33),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: w * 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GeneralButton(
                  height: h * 50,
                  width: w * 164,
                  isDisabledColor: true,
                  title: 'No',
                  textStyle: tsS18w600cFFFFFF,
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
                GeneralButton(
                  height: h * 50,
                  width: w * 164,
                  title: 'Yes',
                  textStyle: tsS18w600cFFFFFF,
                  // color: ThemeColors.secondaryColor,
                  onPressed: () async {
                    EasyLoading.show();
                    final navigator = Navigator.of(context);
                    final provider = Provider.of<LeaveApplicationProvider>(
                        context,
                        listen: false);
                    bool isPop = await provider.cancelLeaveRequest(
                        leaveId: leaveID, context: context);

                    if (isPop) {
                      navigator.pop();
                      navigator.pop();
                    }
                    EasyLoading.dismiss();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _requestNewButton(
      {required BuildContext context,
      required LeaveDetails? leaveDetails,
      String? text}) {
    // if (text?.toLowerCase() != 'pending' &&
    //     text?.toLowerCase() != 'cancelled') {
    //   return const SizedBox();
    // }

    return text?.toLowerCase() == 'cancelled' ||
            text?.toLowerCase() == 'pending' ||
            text?.toLowerCase() == 'rejected'
        ? Padding(
            padding: EdgeInsets.symmetric(vertical: h * 10, horizontal: w * 10),
            child: TextButton(
              onPressed: () async {
                Provider.of<LeaveApplicationProvider>(context, listen: false)
                    .pickedFile = null;
                EasyLoading.show();
                await Provider.of<LeaveApplicationProvider>(context,
                        listen: false)
                    .getStaffinCharge();
                await Provider.of<LeaveApplicationProvider>(context,
                        listen: false)
                    .getLeaveTypes();
                EasyLoading.dismiss();
                if (text?.toLowerCase() == 'cancelled' ||
                    text?.toLowerCase() == 'rejected') {
                  PageNavigator.push(
                    context: context,
                    route: NewLeaveScreens(
                      isComingFromOverViewScreen: true,
                      leaveDetails: leaveDetails,
                      isRequestAgain: true,
                    ),
                  );
                } else {
                  PageNavigator.push(
                    context: context,
                    route: NewLeaveScreens(
                      isComingFromOverViewScreen: true,
                      leaveDetails: leaveDetails,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: ThemeColors.colorTransparent,
                minimumSize: const Size(10, 36),
              ),
              child: Text(
                text?.toLowerCase() == 'cancelled' ||
                        text?.toLowerCase() == 'rejected'
                    ? 'Request Again'
                    : 'Edit & Apply',
                style: tsS14w500cFFFFFF,
              ),
            ),
          )
        : const SizedBox();
  }

  Widget _leaveRejectedCard(
      {required String rejectedBy,
      required String comment,
      required String? rejectedDate,
      required String? appOrReject}) {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                height: 24,
                width: 24,
                margin: const EdgeInsets.only(right: 10),
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                    color: appOrReject?.toLowerCase() == 'false'
                        ? ThemeColors.colorFCD2D0
                        : ThemeColors.color06AA37.withOpacity(.2),
                    borderRadius: BorderRadius.circular(6)),
                child: Container(
                  decoration: BoxDecoration(
                      color: appOrReject?.toLowerCase() == 'false'
                          ? ThemeColors.colorF64D44
                          : ThemeColors.color06AA37,
                      shape: BoxShape.circle),
                  child: Center(
                    child: Icon(
                      appOrReject?.toLowerCase() == 'false'
                          ? Icons.close_rounded
                          : Icons.done_rounded,
                      color: Colors.white,
                      size: 10,
                    ),
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      appOrReject?.toLowerCase() == 'false'
                          ? 'Leave Rejected'
                          : 'Leave Approved',
                      style: appOrReject?.toLowerCase() == 'false'
                          ? tsS12w4cF64D44
                          : tsS12w4c06AA37,
                    ),
                  ),
                  SizedBox(
                    height: 15,
                    width: 280 * w,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: Text(
                            rejectedBy,
                            style: tsS10w400c646363,
                          ),
                        ),
                        SizedBox(
                          width: 10 * w,
                        ),
                        Expanded(
                          child: Text(
                            rejectedDate.toString(),
                            style: tsS10w400c646363,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          Divider(
            thickness: 1,
            color: ThemeColors.colorD9D9D9,
          ),
          const SizedBox(height: 12),
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              comment,
              style: tsS14w400979797,
            ),
          )
        ],
      ),
    );
  }

  Widget _hrApprovalWidget(
      {required String profilePic,
      required String name,
      required String isApprove}) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(5.0),
            child: ClipOval(
              child: Stack(
                children: [
                  CachedNetworkImage(
                    imageUrl: profilePic.toString(),
                    width: 45 * w,
                    height: 45 * h,
                    fit: BoxFit.cover,
                    errorWidget: (context, url, error) {
                      return Container(
                        height: 50,
                        width: 50,
                        decoration: BoxDecoration(
                          color: ThemeColors.primaryColor,
                          shape: BoxShape.circle,
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          name.substring(0, 1).toUpperCase(),
                          style: GoogleFonts.rubik(
                              fontSize: 22,
                              fontWeight: FontWeight.w500,
                              color: Colors.white),
                        ),
                      );
                    },
                  ),
                  if (isApprove.toLowerCase().trim() == 'pending')
                    Container(
                      height: 45 * h,
                      width: 45 * w,
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: Column(
            children: [
              if (isApprove.toLowerCase().trim() == 'pending') const SizedBox(),
              if (isApprove.toLowerCase().trim() == 'true')
                ImageIcon(
                  const AssetImage(
                    'assets/icons/tick.png',
                  ),
                  size: 13,
                  color: ThemeColors.color06AA37,
                ),
              if (isApprove.toLowerCase().trim() == 'false')
                ImageIcon(
                  const AssetImage(
                    'assets/icons/close_red.png',
                  ),
                  size: 13,
                  color: ThemeColors.colorB80000,
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _reportingPersonsWidget({required LeaveOverviewModel data}) {
    return Row(
      children: data.reportingPersonList!.map(
        (e) {
          return Stack(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(5.0),
                  child: ClipOval(
                    child: Stack(
                      children: [
                        CachedNetworkImage(
                          imageUrl: e.profilePic.toString(),
                          width: 45 * w,
                          height: 45 * h,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) {
                            return Container(
                              height: 50,
                              width: 50,
                              decoration: BoxDecoration(
                                color: ThemeColors.primaryColor,
                                shape: BoxShape.circle,
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                e.name?.substring(0, 1).toUpperCase() ?? '',
                                style: GoogleFonts.rubik(
                                    fontSize: 22,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.white),
                              ),
                            );
                          },
                        ),
                        if (e.isApprove?.toLowerCase().trim() == 'pending')
                          Container(
                            height: 45 * h,
                            width: 45 * w,
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Column(
                  children: [
                    if (e.isApprove?.toLowerCase().trim() == 'pending')
                      const SizedBox(),
                    if (e.isApprove?.toLowerCase().trim() == 'true')
                      ImageIcon(const AssetImage('assets/icons/tick.png'),
                          size: 13, color: ThemeColors.color06AA37),
                    if (e.isApprove?.toLowerCase().trim() == 'false')
                      ImageIcon(const AssetImage('assets/icons/close_red.png'),
                          size: 13, color: ThemeColors.colorB80000),
                  ],
                ),
              )
            ],
          );
        },
      ).toList(),
    );
  }
}
