import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/colors.dart';

class CancelButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? title;
  final Widget? child;
  final Color? color;
  final bool isLoading;
  final TextStyle? textStyle;
  final EdgeInsets? margin;
  final double width;
  final double height;
  const CancelButton({
    super.key,
    required this.onPressed,
    required this.height,
    required this.width,
    this.title,
    this.child,
    this.isLoading = false,
    this.margin,
    this.color,
    this.textStyle,
  });
  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: margin ?? const EdgeInsets.all(0),
        child: Container(
          // width: width,
          // height: height,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
                // stops: [96.13, 83.07],
                begin: Alignment.centerLeft,
                colors: [Color(0xffA01212), Color(0xffFA0000)]),
            borderRadius: BorderRadius.circular(50),
          ),
          child: ElevatedButton(
            onPressed: isLoading ? null : onPressed,
            style: ElevatedButton.styleFrom(
              elevation: 0,
              foregroundColor: ThemeColors.colorTransparent,
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              minimumSize: Size(width, height),
              // minimumSize: const Size(double.infinity, 55),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
              ),
            ),
            child: isLoading
                ? const CupertinoActivityIndicator()
                : child ?? Text('$title', style: textStyle),
          ),
        ));
  }
}

class CancelButton2 extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? title;
  final Widget? child;
  final Color? color;
  final bool isLoading;
  final TextStyle? textStyle;
  final EdgeInsets? margin;
  final double width;
  final double height;
  const CancelButton2({
    super.key,
    required this.onPressed,
    required this.height,
    required this.width,
    this.title,
    this.child,
    this.isLoading = false,
    this.margin,
    this.color,
    this.textStyle,
  });
  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: margin ?? const EdgeInsets.all(0),
        child: Container(
          // width: width,
          // height: height,
          decoration: BoxDecoration(
            gradient: LinearGradient(
                // stops: [96.13, 83.07],
                begin: Alignment.centerLeft,
                colors: [ThemeColors.color979797, ThemeColors.color979797]),
            borderRadius: BorderRadius.circular(50),
          ),
          child: ElevatedButton(
            onPressed: isLoading ? null : onPressed,
            style: ElevatedButton.styleFrom(
              elevation: 0,
              foregroundColor: ThemeColors.colorTransparent,
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              minimumSize: Size(width, height),
              // minimumSize: const Size(double.infinity, 55),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
              ),
            ),
            child: isLoading
                ? const CupertinoActivityIndicator()
                : child ?? Text('$title', style: textStyle),
          ),
        ));
  }
}
