// ignore_for_file: use_build_context_synchronously

import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/helper/drop_down_widget.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/view/latest_updates/policies_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';
import 'package:flutter_quill/flutter_quill.dart';
// import 'package:fleather/fleather.dart';
import 'package:video_player/video_player.dart';
import '../../provider/profile_provider.dart';
import '../../util/colors.dart';

class PostNewUpdatesScreen extends StatefulWidget {
  const PostNewUpdatesScreen({super.key});
  @override
  State<PostNewUpdatesScreen> createState() => _PostNewUpdatesScreenState();
}

class _PostNewUpdatesScreenState extends State<PostNewUpdatesScreen> {
  late UpdatesProvider _updatesProvider;
  final QuillController quillController = QuillController.basic();
  @override
  void initState() {
    _updatesProvider = Provider.of<UpdatesProvider>(context, listen: false);
    super.initState();
  }

  @override
  void dispose() {
    _updatesProvider.videoPlayerController?.dispose();
    super.dispose();
  }

  void unFocusEditor() => quillController.dispose();
  final _formKey = GlobalKey<FormState>();
  bool enable = false;
  bool bold = false;
  bool playPause = false;
  @override
  Widget build(BuildContext context) {
    final profileProvider = Provider.of<ProfileProvider>(context);
    final UpdatesProvider updateProvider =
        Provider.of<UpdatesProvider>(context);
    return GestureDetector(
      onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
      child: HisenseScaffold(
        screenTitle: 'Add Post',
        onTap: updateProvider.removeSelectedPosts,
        actions: [
          Consumer<UpdatesProvider>(builder: (context, provider, child) {
            return InkWell(
              onTap: provider.isCreating == true
                  ? null
                  : () async {
                      hideKeyboard(context);
                      if (_formKey.currentState!.validate()) {
                        // provider.addLatestUpdate(
                        //   context: context,
                        //   description: _textEditingController.text,
                        // );
                        if (LoginModel.isAdmin == true) {
                          if (provider.validationSub == false) {
                            provider.postCreate(
                              // update: _textEditingController.text,
                              update: quillController.document.toPlainText(),
                              context: context,
                            );
                          } else {
                            provider.validationSub = true;
                          }
                        } else {
                          provider.postCreate(
                            update: quillController.document.toPlainText(),
                            context: context,
                          );
                        }
                      }
                    },
              child: Center(
                child: Text(
                  "Post Now",
                  style: !enable ? tsS14w500cFFFFFFwop60 : tsS14w500cFFFFFF,
                ),
              ),
            );
          }),
          const SizedBox(
            width: 20,
          )
        ],
        body: Padding(
          padding: const EdgeInsets.fromLTRB(15.0, 15.0, 15.0, 0),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                Expanded(
                  child: ListView(
                    physics: const ClampingScrollPhysics(),
                    children: [
                      if (LoginModel.isAdmin == true)
                        _subTitile(subtitle: "User policy", isMandatory: true),
                      if (LoginModel.isAdmin == true)
                        Consumer<UpdatesProvider>(
                          builder: (context, provider, _) {
                            return DropdownWidget2(
                              hintText: provider
                                      .selectedUserPoliciesList.isEmpty
                                  ? 'Policies'
                                  : provider.selectedUserPoliciesList.length ==
                                          provider.userPoliciesModel?.length
                                      ? "Selected All"
                                      : provider.selectedUserPoliciesList
                                          .map((e) => e.name)
                                          .join(','),
                              selectedValue: provider.selectedUserPoliciesList
                                  .map((e) => e.name)
                                  .join(','),
                              onTap: () {
                                SystemChannels.textInput
                                    .invokeMethod('TextInput.hide');

                                showModalBottomSheet(
                                    isScrollControlled: true,
                                    isDismissible: true,
                                    useSafeArea: true,
                                    context: context,
                                    shape: const RoundedRectangleBorder(
                                      borderRadius:
                                          BorderRadiusDirectional.only(
                                        topEnd: Radius.circular(25),
                                        topStart: Radius.circular(25),
                                      ),
                                    ),
                                    builder: (context) {
                                      return PolicesScreen();
                                    });
                              },
                              validator: (p0) {
                                if (p0 == null || p0 == '') {
                                  provider.validationSub = true;
                                } else {
                                  provider.validationSub = false;
                                }
                                return null;
                              },
                              validatorColor: provider.validationSub,
                            );
                          },
                        ),
                      SizedBox(
                        height: 20 * h,
                      ),
                      Container(
                        padding: const EdgeInsets.all(12),
                        // height: 374 * h,
                        height: 520 * h,
                        // height: (height + 100) * h,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(50),
                                  child: CachedNetworkImage(
                                    height: 45,
                                    width: 45,
                                    fit: BoxFit.fill,
                                    imageUrl: "${profileProvider.profilePic}",
                                    errorWidget: (context, url, error) {
                                      return Container(
                                        height: 45,
                                        width: 45,
                                        decoration: BoxDecoration(
                                          color: ThemeColors.primaryColor,
                                          shape: BoxShape.circle,
                                        ),
                                        alignment: Alignment.center,
                                        child: Text(
                                          profileProvider.firstName!
                                              .substring(0, 1)
                                              .toUpperCase(),
                                          style: GoogleFonts.rubik(
                                            color: Colors.white,
                                            fontSize: 20,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                        profileProvider.firstName ?? "Username",
                                        style: tsS14w500Black),
                                    Text(
                                      profileProvider.designation ?? "",
                                      style: tsS12w400979797,
                                    )
                                    // const SizedBox(height: 2),
                                    // Text(
                                    //     profileProvider.data?.firstName.toString() ??
                                    //         "Username",
                                    //     style: tsS14w500Black),
                                  ],
                                ),
                              ],
                            ),
                            const SizedBox(height: 5),
                            Expanded(
                              child: Consumer<UpdatesProvider>(
                                  builder: (context, provider, _) {
                                return Column(
                                  children: [
                                    // Toolbar removed for simplicity

                                    Expanded(
                                      // height: provider.selectedPostsImages
                                      //             .isNotEmpty ||
                                      //         provider.selectedPostsVideo != null
                                      //     ? 100
                                      //     : null,
                                      child: QuillEditor.basic(
                                        controller: quillController,
                                      ),
                                    ),

                                    // TextFormField(
                                    //   onChanged: (value) {
                                    //     if (_textEditingController.text.isEmpty) {
                                    //       setState(() {
                                    //         enable = false;
                                    //       });
                                    //     } else {
                                    //       setState(() {
                                    //         enable = true;
                                    //       });
                                    //     }
                                    //   },
                                    //   maxLines: provider.selectedPostsImages
                                    //               .isNotEmpty ||
                                    //           provider.selectedPostsVideo != null
                                    //       ? 3
                                    //       : 10,
                                    //   autofocus: true,
                                    //   controller: _textEditingController,
                                    //   style: GoogleFonts.poppins(
                                    //       fontSize: 16,
                                    //       fontWeight: FontWeight.w400),
                                    //   decoration: InputDecoration(
                                    //     hintText: 'Write something...',
                                    //     hintStyle: GoogleFonts.poppins(
                                    //         fontSize: 16,
                                    //         fontWeight: FontWeight.w400),
                                    //     errorStyle: const TextStyle(fontSize: 14),
                                    //     border: InputBorder.none,
                                    //   ),
                                    //   validator: (value) {
                                    //     return value!.trimLeft().isEmpty
                                    //         ? "Type something you want to share"
                                    //         : null;
                                    //   },
                                    // ),
                                    SizedBox(
                                      height: h * 5,
                                    ),
                                    Column(
                                      children: [
                                        if (provider.selectedPostsVideo != null)
                                          InkWell(
                                            onTap: () {
                                              if (!playPause) {
                                                provider.videoPlayerController!
                                                    .play();
                                                setState(() {
                                                  playPause = !playPause;
                                                });
                                              } else {
                                                provider.videoPlayerController!
                                                    .pause();
                                                setState(() {
                                                  playPause = !playPause;
                                                });
                                              }
                                            },
                                            child: Stack(
                                              children: [
                                                SizedBox(
                                                  height: 152 * h,
                                                  width: 360 * w,
                                                  child: AspectRatio(
                                                    aspectRatio: provider
                                                        .videoPlayerController!
                                                        .value
                                                        .aspectRatio,
                                                    child: VideoPlayer(provider
                                                        .videoPlayerController!),
                                                  ),
                                                ),
                                                Align(
                                                    alignment:
                                                        Alignment.topRight,
                                                    child: Container(
                                                      margin:
                                                          const EdgeInsets.all(
                                                              4),
                                                      width: 35,
                                                      height: 35,
                                                      decoration:
                                                          const BoxDecoration(
                                                              shape: BoxShape
                                                                  .circle,
                                                              color:
                                                                  Colors.white),
                                                      child: Center(
                                                        child: IconButton(
                                                            onPressed: () {
                                                              provider
                                                                  .removeVideo();
                                                            },
                                                            icon: const Icon(
                                                              Icons.delete,
                                                              size: 18,
                                                            )),
                                                      ),
                                                    ))
                                              ],
                                            ),
                                          ),
                                        // Positioned.fill(
                                        //   child: Image(
                                        //     image: VideoThumbnail.image(
                                        //       video: widget.videoUrl,
                                        //       imageFormat: ImageFormat.JPEG,
                                        //     ),
                                        //     fit: BoxFit.cover,
                                        //   ),
                                        // ),
                                        Wrap(
                                          spacing: 1,
                                          runSpacing: 1,
                                          children: provider.selectedPostsImages
                                              .map((e) {
                                            int index = provider
                                                .selectedPostsImages
                                                .indexOf(e);

                                            return index > 3
                                                ? const SizedBox()
                                                : InkWell(
                                                    onTap: () {
                                                      Navigator.of(context).push(
                                                          MaterialPageRoute(
                                                              builder: (context) =>
                                                                  const ImageFullViewScreenXFile()));
                                                    },
                                                    child: SizedBox(
                                                      height: provider
                                                                  .selectedPostsImages
                                                                  .length ==
                                                              1
                                                          ? 152
                                                          : index == 0
                                                              ? 100
                                                              : 90,
                                                      width: index == 0
                                                          ? double.infinity
                                                          : 100,
                                                      child: index <= 2
                                                          ? Image.file(
                                                              File(e.path),
                                                              fit: BoxFit.cover,
                                                            )
                                                          : Container(
                                                              height: 90,
                                                              width: 90,
                                                              color:
                                                                  Colors.white,
                                                              child: Center(
                                                                child: Text(
                                                                    "+ ${provider.selectedPostsImages.length - 3}"),
                                                              ),
                                                              // decoration: BoxDecoration(
                                                              //     image: DecorationImage(
                                                              //         image: FileImage(
                                                              //             File(e.path)))),
                                                            ),
                                                    ),
                                                  );
                                          }).toList(),
                                        ),
                                      ],
                                    ),
                                  ],
                                );
                              }),
                            ),
                            SizedBox(
                              height: 10 * h,
                            ),
                            Consumer<UpdatesProvider>(
                                builder: (context, provider, _) {
                              return Row(
                                children: [
                                  InkWell(
                                    onTap: provider.selectedPostsVideo != null
                                        ? null
                                        : () async {
                                            await provider.addImages(context);
                                          },
                                    child: ImageIcon(
                                      const AssetImage(
                                          "assets/icons/gallery.png"),
                                      color: ThemeColors.color979797,
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 30,
                                  ),
                                  InkWell(
                                    onTap: provider
                                            .selectedPostsImages.isNotEmpty
                                        ? null
                                        : () async {
                                            await provider.addVideos(context);
                                          },
                                    child: ImageIcon(
                                      const AssetImage(
                                          "assets/icons/video.png"),
                                      color: ThemeColors.color979797,
                                    ),
                                  ),
                                  // const SizedBox(
                                  //   width: 30,
                                  // ),
                                  // InkWell(
                                  //   onTap: () {
                                  //     // setState(() {
                                  //     //   bold = !bold;
                                  //     // });
                                  //     print(fleatherController.document.toJson());
                                  //     print(fleatherController.document.toString());
                                  //   },
                                  //   child: ImageIcon(
                                  //     const AssetImage("assets/icons/B.png"),
                                  //     color: ThemeColors.color979797,
                                  //     size: 18,
                                  //   ),
                                  // ),
                                  // const SizedBox(
                                  //   width: 30,
                                  // ),
                                  // ImageIcon(
                                  //   const AssetImage("assets/icons/I.png"),
                                  //   color: ThemeColors.color979797,
                                  //   size: 18,
                                  // ),
                                  // const SizedBox(
                                  //   width: 30,
                                  // ),
                                  // ImageIcon(
                                  //   const AssetImage("assets/icons/link.png"),
                                  //   color: ThemeColors.color979797,
                                  //   size: 18,
                                  // ),
                                ],
                              );
                            })
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        // floatingActionButton: Consumer<UpdatesProvider>(
        //   builder: (context, provider, child) {
        //     return FloatingActionButton(
        //       onPressed: () {
        //         hideKeyboard(context);
        //         if (_formKey.currentState!.validate()) {
        //           // provider.addLatestUpdate(
        //           //   context: context,
        //           //   description: _textEditingController.text,
        //           // );
        //           provider.shareUpdates(
        //               update: _textEditingController.text, context: context);
        //         }
        //       },
        //       child: provider.ispostUpdateLoading
        //           ? const CircularLoadingWidget()
        //           : const Icon(Icons.send),
        //     );
        //   },
        // ),
      ),
    );
  }

  Widget _subTitile({required String subtitle, bool isMandatory = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: Row(
            children: [
              Text(
                subtitle,
                style: tsS14w400c30292F,
              ),
              if (isMandatory)
                Text(
                  "*",
                  style: tsS14w400cFA0000,
                ),
            ],
          ),
        ),
        SizedBox(height: h * 4),
      ],
    );
  }
}

class ImageFullViewScreenXFile extends StatelessWidget {
  const ImageFullViewScreenXFile({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<UpdatesProvider>(
      builder: (context, provider, child) {
        return Scaffold(
            // appBar: AppBar(
            //   leading: IconButton(
            //       onPressed: () {
            //         Navigator.pop(context);
            //       },
            //       icon: const Icon(
            //         Icons.arrow_back_ios,
            //         color: Colors.white,
            //       )),
            //   actions: [
            //     Padding(
            //       padding: const EdgeInsets.only(right: 8.0),
            //       child: IconButton(
            //           onPressed: () {
            //             provider.removeSelectedImage(context, currentIndex);
            //           },
            //           icon: const Icon(
            //             Icons.delete,
            //             color: Colors.white,
            //           )),
            //     )
            //   ],
            //   backgroundColor: Colors.black,
            // ),
            backgroundColor: Colors.black,
            body: SafeArea(
              child: PageView.builder(
                itemCount: provider.selectedPostsImages.length,
                itemBuilder: (context, index) {
                  final data = provider.selectedPostsImages[index];
                  return Column(
                    children: [
                      SizedBox(
                        height: 60 * h,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                                icon: const Icon(
                                  Icons.arrow_back_ios,
                                  color: Colors.white,
                                )),
                            IconButton(
                                onPressed: () {
                                  provider.removeSelectedImage(context, index);
                                },
                                icon: const Icon(
                                  Icons.delete,
                                  color: Colors.white,
                                ))
                          ],
                        ),
                      ),
                      Expanded(
                          child: Image.file(
                        File(data.path),
                      )),
                      const SizedBox()
                    ],
                  );
                },
              ),
            ));
      },
    );
  }
}
