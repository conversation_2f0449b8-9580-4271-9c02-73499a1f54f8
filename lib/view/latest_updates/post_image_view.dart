import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/post_model.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view_gallery.dart';

// import '../../model/get_updates_model.dart';

class PostImageViewer extends StatelessWidget {
  final List<PostFile>? fileView;
  const PostImageViewer({super.key, required this.fileView});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.black,
        leading: InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: const Icon(
            Icons.arrow_back_sharp,
            color: Colors.white,
          ),
        ),
      ),
      body: Container(
        color: Colors.black,
        child: PhotoViewGallery.builder(
          backgroundDecoration: const BoxDecoration(color: Colors.black),
          itemCount: fileView!.length,
          scrollPhysics: const ClampingScrollPhysics(),
          builder: (context, index) {
            return PhotoViewGalleryPageOptions(
              imageProvider:
                  CachedNetworkImageProvider(fileView![index].file.toString()),
              // initialScale: PhotoViewComputedScale.contained * 0.8,
              // heroAttributes:
              //     PhotoViewHeroAttributes(tag: fileView![index].file.toString()),
            );
          },
        ),
      ),
    );
  }
}
