import 'package:chewie/chewie.dart';
import 'package:e8_hr_portal/model/post_model.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:video_player/video_player.dart';

// import '../../model/get_updates_model.dart';

class VideoViewScreen extends StatefulWidget {
  final List<PostFile>? file;
  const VideoViewScreen({super.key, required this.file});

  @override
  State<VideoViewScreen> createState() => _VideoViewScreenState();
}

class _VideoViewScreenState extends State<VideoViewScreen> {
  VideoPlayerController? videoPlayerController;
  ChewieController? chewieController;
  @override
  void initState() {
    if (widget.file != null && widget.file!.isNotEmpty) {
      videoPlayerController =
          VideoPlayerController.networkUrl(Uri.parse(widget.file!.first.file!));
      videoPlayerController!.addListener(() {});
      videoPlayerController!.setLooping(true);
      videoPlayerController!.initialize();
      videoPlayerController!.play();
      chewieController = ChewieController(
          fullScreenByDefault: true,
          videoPlayerController: videoPlayerController!,
          autoPlay: true,
          looping: true,
          aspectRatio: 16 / 9);
    }
    super.initState();
  }

  @override
  void dispose() {
    videoPlayerController?.dispose();
    chewieController?.dispose();
    super.dispose();
  }

  bool playPause = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.black,
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            onPressed: () => Navigator.pop(context),
          )),
      backgroundColor: Colors.white,
      body: Container(
          alignment: Alignment.center,
          color: Colors.black,
          child: widget.file!.first.fileType == "video"
              ?

              // final chewieController = ChewieController(
              //   videoPlayerController: videoPlayerController,
              //   autoPlay: true,
              //   looping: true,
              //   aspectRatio: 16 / 8,
              // );
              InkWell(
                  onTap: () {
                    // if (!playPause) {
                    //   videoPlayerController.play();

                    //   playPause = !playPause;
                    // } else {
                    //   videoPlayerController.pause();

                    //   playPause = !playPause;
                    // }
                  },
                  child: SizedBox(
                    height: 200 * h,
                    width: 360 * w,
                    child: AspectRatio(
                      aspectRatio: videoPlayerController!.value.aspectRatio,
                      child: Stack(
                        children: [
                          Chewie(controller: chewieController!)
                          // VideoPlayer(videoPlayerController!),
                          // _ControlsOverlay(
                          //   controller: videoPlayerController,
                          // ),
                          // Align(
                          //   alignment: Alignment.bottomCenter,
                          //   child: VideoProgressIndicator(
                          //       videoPlayerController,
                          //       padding: const EdgeInsets.fromLTRB(
                          //           10, 0, 20, 10),
                          //       colors: const VideoProgressColors(
                          //           backgroundColor: Colors.grey,
                          //           playedColor: Colors.white),
                          //       allowScrubbing: true),
                          // ),
                        ],
                      ),
                    ),
                  ),
                )
              : const SizedBox()),
    );
  }
}
