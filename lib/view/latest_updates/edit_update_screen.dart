// ignore_for_file: use_build_context_synchronously

import 'dart:io' as io;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/helper/drop_down_widget.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/model/post_model.dart';
import 'package:e8_hr_portal/view/latest_updates/policies_screen.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/latest_updates/post_new_updates_screen.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';
import 'package:flutter_quill/flutter_quill.dart';
// import 'package:fleather/fleather.dart';
import 'package:video_player/video_player.dart';
// import '../../model/get_updates_model.dart' as d;
import '../../provider/profile_provider.dart';
import '../../util/colors.dart';

class EditUpdatesScreen extends StatefulWidget {
  final String content;
  final List<PostFile>? file;
  final int? postId;
  // final String? uint8list;
  const EditUpdatesScreen({
    super.key,
    required this.content,
    this.file,
    required this.postId,
    // this.uint8list,
  });

  @override
  State<EditUpdatesScreen> createState() => _EditUpdatesScreenState();
}

class _EditUpdatesScreenState extends State<EditUpdatesScreen> {
  final TextEditingController _contentEditController = TextEditingController();
  late UpdatesProvider _updatesProvider;

  String? thumbnail;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _contentEditController.text = widget.content;

      _updatesProvider = Provider.of<UpdatesProvider>(context, listen: false);
      _updatesProvider.getPostDetails(postId: widget.postId!);

      debugPrint(_updatesProvider.selectedUserPoliciesList.length.toString());
      debugPrint(_updatesProvider.userPoliciesModel?.length.toString());
      if (widget.file != null && widget.file!.isEmpty) {
        _updatesProvider.isVideoRemoved = true;
      }
      if (widget.file != null && widget.file?.first.fileType == 'image') {
        _updatesProvider.isVideoRemoved = true;
        _updatesProvider.alreadyPostImageList = widget.file;
      } else {
        _updatesProvider.isVideoRemoved = false;
      }
      if (_updatesProvider.selectedPostsVideo != null) {}
    });
    super.initState();
  }

  @override
  void dispose() {
    _updatesProvider.videoPlayerController?.dispose();

    _contentEditController.dispose();
    super.dispose();
  }

  final QuillController quillController = QuillController.basic();
  void unFocusEditor() => quillController.dispose();
  final _formKey = GlobalKey<FormState>();
  bool enable = false;
  bool bold = false;
  bool playPause = false;

  @override
  Widget build(BuildContext context) {
    final profileProvider = Provider.of<ProfileProvider>(context);
    final UpdatesProvider updateProvider =
        Provider.of<UpdatesProvider>(context);

    return HisenseScaffold(
      screenTitle: 'Edit Post',
      onTap: updateProvider.removeSelectedPosts,
      actions: [
        Consumer<UpdatesProvider>(builder: (context, provider, child) {
          return InkWell(
            onTap: provider.isCreating == true
                ? null
                : () async {
                    hideKeyboard(context);

                    if (_formKey.currentState!.validate()) {
                      // provider.addLatestUpdate(
                      //   context: context,
                      //   description: _textEditingController.text,
                      // );
                      // String? textt = await quillEditorController.getText();

                      await provider.postEdit(
                        postId: widget.postId.toString(),
                        update: quillController.document.toPlainText(),
                        context: context,
                      );
                    }
                  },
            child: Center(
              child: Text(
                "Update",
                style:
                    //!enable ?
                    //  tsS14w500cFFFFFFwop60
                    //:
                    tsS14w500cFFFFFF,
              ),
            ),
          );
        }),
        const SizedBox(
          width: 20,
        )
      ],
      body: Padding(
        padding: const EdgeInsets.fromLTRB(15.0, 15.0, 15.0, 0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: ListView(
                  physics: const ClampingScrollPhysics(),
                  children: [
                    if (LoginModel.isAdmin == true)
                      _subTitile(subtitle: "User policy", isMandatory: true),
                    if (LoginModel.isAdmin == true)
                      Consumer<UpdatesProvider>(
                        builder: (context, provider, _) {
                          return DropdownWidget2(
                            hintText: provider.selectedUserPoliciesList.isEmpty
                                ? 'Policies'
                                : (provider.selectedUserPoliciesList.length ==
                                        provider.userPoliciesModel?.length)
                                    ? "Selected All"
                                    : provider.selectedUserPoliciesList
                                        .map((e) => e.name)
                                        .join(','),
                            selectedValue:
                                provider.selectedUserPoliciesList.map((e) {
                              return e.name;
                            }).join(','),
                            onTap: () {
                              showModalBottomSheet(
                                  isScrollControlled: true,
                                  isDismissible: true,
                                  useSafeArea: true,
                                  context: context,
                                  shape: const RoundedRectangleBorder(
                                    borderRadius: BorderRadiusDirectional.only(
                                      topEnd: Radius.circular(25),
                                      topStart: Radius.circular(25),
                                    ),
                                  ),
                                  builder: (context) {
                                    return PolicesScreen();
                                  });
                            },
                            validator: (p0) {
                              if (p0 == null || p0 == '') {
                                provider.validationSub = true;
                              } else {
                                provider.validationSub = false;
                              }
                              return null;
                            },
                            validatorColor: provider.validationSub,
                          );
                        },
                      ),
                    SizedBox(
                      height: 20 * h,
                    ),
                    Container(
                      padding: const EdgeInsets.all(12),
                      height: 560 * h,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12)),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(50),
                                child: CachedNetworkImage(
                                  height: 50,
                                  width: 50,
                                  fit: BoxFit.fill,
                                  imageUrl: "${profileProvider.profilePic}",
                                  errorWidget: (context, url, error) {
                                    return Container(
                                      height: 50,
                                      width: 50,
                                      decoration: BoxDecoration(
                                        color: ThemeColors.primaryColor,
                                        shape: BoxShape.circle,
                                      ),
                                      alignment: Alignment.center,
                                      child: Text(
                                        profileProvider.firstName!
                                            .substring(0, 1)
                                            .toUpperCase(),
                                        style: GoogleFonts.rubik(
                                          color: Colors.white,
                                          fontSize: 20,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(width: 10),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(profileProvider.firstName ?? "Username",
                                      style: tsS14w500Black),
                                  Text(
                                    profileProvider.designation ?? "",
                                    style: tsS12w400979797,
                                  )
                                  // const SizedBox(height: 2),
                                  // Text(
                                  //     profileProvider.data?.firstName.toString() ??
                                  //         "Username",
                                  //     style: tsS14w500Black),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 5),
                          Expanded(
                            child: Consumer<UpdatesProvider>(
                                builder: (context, provider, _) {
                              return Column(
                                children: [
                                  // Toolbar removed for simplicity
                                  Expanded(
                                    // height: provider.selectedPostsImages
                                    //             .isNotEmpty ||
                                    //         provider.selectedPostsVideo != null
                                    //     ? 100
                                    //     : null,
                                    child: QuillEditor.basic(
                                      controller: quillController,
                                    ),
                                  ),
                                  Column(
                                    children: [
                                      if (widget.file != null &&
                                          widget.file!.isNotEmpty &&
                                          widget.file!.first.fileType ==
                                              "image")
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(top: 10),
                                          child: Wrap(
                                            spacing: 1,
                                            runSpacing: 1,
                                            children: provider
                                                        .alreadyPostImageList ==
                                                    null
                                                ? []
                                                : provider.alreadyPostImageList!
                                                    .map((e) {
                                                    int index = provider
                                                        .alreadyPostImageList!
                                                        .indexOf(e);

                                                    int urlCount = provider
                                                                .alreadyPostImageList ==
                                                            null
                                                        ? 0
                                                        : provider
                                                                .alreadyPostImageList!
                                                                .length -
                                                            3;
                                                    // int fileCount = provider
                                                    //     .selectedPostsImages
                                                    //     .length;

                                                    // int totalCount =
                                                    //     urlCount + fileCount;

                                                    return index > 3
                                                        ? const SizedBox()
                                                        : InkWell(
                                                            onTap: () {
                                                              Navigator.of(
                                                                      context)
                                                                  .push(MaterialPageRoute(
                                                                      builder:
                                                                          (context) =>
                                                                              const ImageFullViewScreen()));
                                                            },
                                                            child: SizedBox(
                                                              height: index == 0
                                                                  ? 120 * h
                                                                  : 90 * w,
                                                              width: index == 0
                                                                  ? double
                                                                      .infinity
                                                                  : 100,
                                                              child: index <= 2
                                                                  ? Image
                                                                      .network(
                                                                      e.file
                                                                          .toString(),
                                                                      fit: BoxFit
                                                                          .cover,
                                                                    )
                                                                  : Container(
                                                                      height:
                                                                          90 *
                                                                              h,
                                                                      width:
                                                                          90 *
                                                                              w,
                                                                      color: Colors
                                                                          .white,
                                                                      child:
                                                                          Center(
                                                                        child: Text(
                                                                            "+ $urlCount"),
                                                                      ),
                                                                    ),
                                                            ),
                                                          );
                                                  }).toList(),
                                          ),
                                        )
                                      else if (widget.file != null &&
                                          widget.file!.isNotEmpty &&
                                          widget.file!.first.fileType ==
                                              "video" &&
                                          provider.isVideoRemoved == false)
                                        _videoThumbnail(),
                                      const SizedBox(
                                        height: 10,
                                      ),

                                      if (provider.selectedPostsVideo != null)
                                        InkWell(
                                          onTap: () {
                                            if (!playPause) {
                                              provider.videoPlayerController!
                                                  .play();
                                              setState(() {
                                                playPause = !playPause;
                                              });
                                            } else {
                                              provider.videoPlayerController!
                                                  .pause();
                                              setState(() {
                                                playPause = !playPause;
                                              });
                                            }
                                          },
                                          child: Stack(
                                            children: [
                                              SizedBox(
                                                height: 200 * h,
                                                width: 360 * w,
                                                child: AspectRatio(
                                                  aspectRatio: provider
                                                      .videoPlayerController!
                                                      .value
                                                      .aspectRatio,
                                                  child: VideoPlayer(provider
                                                      .videoPlayerController!),
                                                ),
                                              ),
                                              Align(
                                                  alignment: Alignment.topRight,
                                                  child: Container(
                                                    margin:
                                                        const EdgeInsets.all(4),
                                                    width: 35,
                                                    height: 35,
                                                    decoration:
                                                        const BoxDecoration(
                                                            shape:
                                                                BoxShape.circle,
                                                            color:
                                                                Colors.white),
                                                    child: Center(
                                                      child: IconButton(
                                                          onPressed: () {
                                                            provider
                                                                .removeVideo();
                                                          },
                                                          icon: const Icon(
                                                            Icons.delete,
                                                            size: 18,
                                                          )),
                                                    ),
                                                  ))
                                            ],
                                          ),
                                        ),
                                      // Positioned.fill(
                                      //   child: Image(
                                      //     image: VideoThumbnail.image(
                                      //       video: widget.videoUrl,
                                      //       imageFormat: ImageFormat.JPEG,
                                      //     ),
                                      //     fit: BoxFit.cover,
                                      //   ),
                                      // ),
                                      Wrap(
                                        spacing: 10,
                                        runSpacing: 10,
                                        children: provider.selectedPostsImages
                                            .map((e) {
                                          int index = provider
                                              .selectedPostsImages
                                              .indexOf(e);

                                          return index > 3
                                              ? const SizedBox()
                                              : InkWell(
                                                  onTap: () {
                                                    Navigator.of(context).push(
                                                        MaterialPageRoute(
                                                            builder: (context) =>
                                                                const ImageFullViewScreenXFile()));
                                                  },
                                                  child: SizedBox(
                                                    height:
                                                        index == 0 ? 120 : 90,
                                                    width: index == 0
                                                        ? double.infinity
                                                        : 100,
                                                    child: index <= 2
                                                        ? Image.file(
                                                            io.File(e.path),
                                                            fit: BoxFit.cover,
                                                          )
                                                        : Container(
                                                            height: 90,
                                                            width: 90,
                                                            color: Colors.white,
                                                            child: Center(
                                                              child: Text(
                                                                  "+ ${provider.selectedPostsImages.length - 3}"),
                                                            ),
                                                            // decoration: BoxDecoration(
                                                            //     image: DecorationImage(
                                                            //         image: FileImage(
                                                            //             File(e.path)))),
                                                          ),
                                                  ),
                                                );
                                        }).toList(),
                                      ),
                                    ],
                                  ),
                                ],
                              );
                            }),
                          ),
                          SizedBox(
                            height: 10 * h,
                          ),
                          Consumer<UpdatesProvider>(
                              builder: (context, provider, _) {
                            return Row(
                              children: [
                                InkWell(
                                  onTap: provider.selectedPostsVideo == null &&
                                          provider.isVideoRemoved == true
                                      ? () async {
                                          await provider.addImages(context);
                                        }
                                      : null,
                                  child: ImageIcon(
                                    const AssetImage(
                                        "assets/icons/gallery.png"),
                                    color: ThemeColors.color979797,
                                  ),
                                ),
                                const SizedBox(
                                  width: 30,
                                ),
                                InkWell(
                                  onTap: provider
                                              .selectedPostsImages.isNotEmpty ||
                                          provider.alreadyPostImageList!
                                              .isNotEmpty ||
                                          thumbnail != null ||
                                          provider.isVideoRemoved == false ||
                                          provider.selectedPostsVideo != null
                                      ? () {}
                                      : () async {
                                          await provider.addVideos(context);
                                        },
                                  child: ImageIcon(
                                    const AssetImage("assets/icons/video.png"),
                                    color: ThemeColors.color979797,
                                  ),
                                ),
                              ],
                            );
                          })
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _subTitile({required String subtitle, bool isMandatory = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: Row(
            children: [
              Text(
                subtitle,
                style: tsS14w400c30292F,
              ),
              if (isMandatory)
                Text(
                  "*",
                  style: tsS14w400cFA0000,
                ),
            ],
          ),
        ),
        SizedBox(height: h * 4),
      ],
    );
  }

  Widget _videoThumbnail() {
    return SizedBox(
      height: 200 * h,
      width: 360 * w,
      child: Stack(
        children: [
          FutureBuilder<String?>(
            future: _getVideoThumbnail(widget.file!.first.file!),
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                return Consumer<UpdatesProvider>(
                  builder: (context, provider, child) {
                    return Stack(
                      children: [
                        Image.file(
                          io.File(snapshot.data!),
                          height: 200 * h,
                          width: 360 * w,
                          fit: BoxFit.fill,
                        ),
                        Align(
                            alignment: Alignment.topRight,
                            child: Container(
                              margin: const EdgeInsets.all(4),
                              width: 35,
                              height: 35,
                              decoration: const BoxDecoration(
                                  shape: BoxShape.circle, color: Colors.white),
                              child: Center(
                                child: IconButton(
                                    onPressed: () {
                                      provider.isVideoRemoved = true;
                                      provider.removedImageIdList.clear();
                                      provider.addRemoveImageID(
                                          widget.file!.first.id!.toInt());
                                      // setState(() {
                                      //   thumbnail = null;
                                      // });
                                    },
                                    icon: const Icon(
                                      Icons.delete,
                                      size: 18,
                                    )),
                              ),
                            ))
                      ],
                    );
                  },
                );
              }
              return const Center(
                child: CircularProgressIndicator(),
              );
            },
          ),
          const Center(
            child: Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 30,
            ),
          )
        ],
      ),
    );
  }
}

Future<String?> _getVideoThumbnail(String? video) async {
  String? uint8list;
//  uint8list = await VideoThumbnail.thumbnailFile(
//     video: video ?? '',
//     thumbnailPath: (await getTemporaryDirectory()).path,
//     imageFormat: ImageFormat.PNG,
//     maxHeight:
//         100, // specify the height of the thumbnail, let the width auto-scaled to keep the source aspect ratio
//     maxWidth: 360,
//     quality: 100,
//   );
  return uint8list;
}

class ImageFullViewScreen extends StatefulWidget {
  const ImageFullViewScreen({super.key});

  @override
  State<ImageFullViewScreen> createState() => _ImageFullViewScreenState();
}

class _ImageFullViewScreenState extends State<ImageFullViewScreen> {
  int currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Consumer<UpdatesProvider>(
      builder: (context, provider, child) {
        return Scaffold(
            backgroundColor: Colors.black,
            body: PageView.builder(
              onPageChanged: (value) {
                setState(() {
                  currentIndex = value;
                });
              },
              itemCount: provider.alreadyPostImageList?.length ?? 0,
              itemBuilder: (context, index) {
                final data = provider.alreadyPostImageList?[index].file;
                return SafeArea(
                  child: Column(
                    children: [
                      SizedBox(
                        height: 60 * h,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                                icon: const Icon(
                                  Icons.arrow_back_ios,
                                  color: Colors.white,
                                )),
                            IconButton(
                                onPressed: () {
                                  provider.removeAlreadySelectedImage(
                                      index, context);
                                },
                                icon: const Icon(
                                  Icons.delete,
                                  color: Colors.white,
                                ))
                          ],
                        ),
                      ),
                      Expanded(child: Image.network(data.toString())),
                    ],
                  ),
                );
              },
            ));
      },
    );
  }
}
