// ignore_for_file: use_build_context_synchronously
import 'dart:ui';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/model/post_model.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/view/employee_relations/widget/photo_view.dart';
import 'package:e8_hr_portal/view/latest_updates/widgets/post_option_bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/latest_updates/post_image_view.dart';
import 'package:e8_hr_portal/view/latest_updates/update_detailed_screen.dart';
import 'package:e8_hr_portal/view/latest_updates/video_view.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';
import '../../util/colors.dart';
import '../../util/size_config.dart';
import 'edit_update_screen.dart';
import 'post_new_updates_screen.dart';
import 'survey_section/survey_post_card.dart';

class LatestUpdatesScreen extends StatefulWidget {
  final bool isFromHome;
  const LatestUpdatesScreen({super.key, this.isFromHome = false});

  @override
  State<LatestUpdatesScreen> createState() => _LatestUpdatesScreenState();
}

class _LatestUpdatesScreenState extends State<LatestUpdatesScreen> {
  final List<Color> cardColors = const [
    Color(0xFFFFFCEB),
    Color(0xFFF8FFEB),
    Color(0xFFEBFFFC),
    Color(0xFFEBF5FF),
    Color(0xFFFFEBFA),
  ];

  @override
  void initState() {
    Provider.of<UpdatesProvider>(context, listen: false).init(context);
    super.initState();
  }

  @override
  void dispose() {
    var pro = Provider.of<UpdatesProvider>(context, listen: false);
    pro.pagingController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        isFromHome: widget.isFromHome,
        screenTitle: 'Latest Posts',
        actions: [
          InkWell(
            onTap: () async {
              await EasyLoading.show();
              UpdatesProvider provider =
                  Provider.of<UpdatesProvider>(context, listen: false);
              provider.selectedPostsImages.clear();
              provider.selectedPostsVideo = null;
              provider.alreadyPostImageList?.clear();
              await provider.getUserPolicies();
              provider.selectedUserPoliciesList.clear();
              provider.selectedUserPoliciesListIds.clear();
              provider.validationSub = false;
              provider.selectedPostType = null;
              if (LoginModel.isAdmin == true) {
                await showModalBottomSheet<void>(
                  useSafeArea: false,
                  context: context,
                  isDismissible: true,
                  isScrollControlled: true,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(12),
                    ),
                  ),
                  builder: (BuildContext context) {
                    EasyLoading.dismiss();
                    return PostOptionBottomSheet();
                  },
                );
              } else {
                await EasyLoading.dismiss();
                PageNavigator.pushSlideRight(
                    context: context, route: const PostNewUpdatesScreen());
              }
              await EasyLoading.dismiss();
            },
            child: Center(
              child: Text(
                'Write a post',
                style: tsS14FFFFF,
              ),
            ),
          ),
          const SizedBox(width: 10)
        ],
        body: Column(
          children: [
            Consumer<UpdatesProvider>(
              builder: (context, provider, child) {
                // if (provider.scrollList.isEmpty) {
                //   return const Center(child: Text('No Post'));
                // }
                // return provider.isUpdatesLoading == true
                //     ? const Center(
                //         child: CircularProgressIndicator(),
                //       )
                //     : RefreshIndicator.adaptive(
                //         onRefresh: () {
                //           return provider.getUpdates(context: context);
                //         },
                // child: ListView.separated(
                //   padding: const EdgeInsets.all(15.0),
                //   itemBuilder: (context, index) {
                //     PostModel? post = provider.scrollList[index];
                //     if (post.type == 'post') {
                //       return SeeAllUpdateCard(
                //         color: Colors.white,
                //         index: index,
                //         post: post,
                //       );
                //     }
                //     return SurveyPostCard(postModel: post);
                //   },
                //   separatorBuilder: (context, index) {
                //     return const SizedBox(height: 15);
                //   },
                //   itemCount: provider.scrollList.length,
                // ),
                return provider.isUpdatesLoading == true
                    ? const SizedBox(
                        height: 500,
                        // width: double.infinity,
                        child: Center(
                          child: CircularProgressIndicator(),
                        ),
                      )
                    : Expanded(
                        child: PagedListView.separated(
                          // physics: const ClampingScrollPhysics(),
                          padding: const EdgeInsets.all(15.0),
                          shrinkWrap: true,
                          pagingController: provider.pagingController!,
                          builderDelegate: PagedChildBuilderDelegate<PostModel>(
                            noItemsFoundIndicatorBuilder: (_) {
                              return const Padding(
                                padding: EdgeInsets.only(top: 80.0),
                                child: Center(child: Text("No Data Found")),
                              );
                            },
                            // noMoreItemsIndicatorBuilder: (context) => const Text("End"),
                            newPageProgressIndicatorBuilder: (_) {
                              return Center(
                                child: CircularProgressIndicator(
                                    color: ThemeColors.color06AA37),
                              );
                            },
                            firstPageProgressIndicatorBuilder: (_) {
                              return const SizedBox();
                            },

                            itemBuilder: (context, item, index) {
                              if (item.type == 'post') {
                                return SeeAllUpdateCard(
                                  color: Colors.white,
                                  index: index,
                                  post: item,
                                );
                              }
                              return SurveyPostCard(postModel: item);
                            },
                          ),
                          separatorBuilder: (context, index) {
                            return const SizedBox(height: 15);
                          },
                          // )
                        ),
                      );
              },
            ),
          ],
        ));
  }
}

// ignore: must_be_immutable
class SeeAllUpdateCard extends StatefulWidget {
  final PostModel post;
  final bool goToDetails;
  final Color color;
  final int? index;

  const SeeAllUpdateCard({
    super.key,
    this.goToDetails = true,
    required this.post,
    required this.color,
    required this.index,
  });

  @override
  State<SeeAllUpdateCard> createState() => _SeeAllUpdateCardState();
}

class _SeeAllUpdateCardState extends State<SeeAllUpdateCard> {
  String? formatted;

  @override
  Widget build(BuildContext context) {
    PostModel post = widget.post;
    int? length;
    double? height;
    double? width;
    if (post.file != null && post.file!.isNotEmpty) {
      length = post.file!.length;
    }

    return Consumer<UpdatesProvider>(builder: (context, provid, child) {
      return InkWell(
        onDoubleTap: () async {
          if (!mounted) return;
          await provid.likePost(
            postId: post.id.toString(),
            context: context,
            // like: post.userLiked == true ? false : true,
            like: provid.postLikeId.contains(post.id) ? false : true,
          );
        },
        onTap: widget.goToDetails == true
            ? () async {
                await EasyLoading.show();
                var pvd = Provider.of<UpdatesProvider>(context, listen: false);
                await pvd.getPostDetails(postId: post.id!.toInt());
                await pvd.getComments(postId: post.id!.toInt());
                await EasyLoading.dismiss();
                if (!mounted) return;
                await Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => UpdateDetailedScreen(
                      color: widget.color,
                      post: post,
                    ),
                  ),
                );
              }
            : () {},
        child: IntrinsicHeight(
          child: Card(
            color: widget.color,
            margin: const EdgeInsets.all(0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      profileSectionPostView(),
                      const SizedBox(height: 9),
                      if (post.description != null)
                        SizedBox(
                          height:
                              ((post.file != null) && (post.file!.isNotEmpty)
                                  ? 45 * h
                                  : 80 * h),
                          child: Html(
                            data: post.description,
                            shrinkWrap: true,
                          ),
                        ),
                      if (post.description.toString().length >= 100)
                        Text(
                          "see more",
                          style: ts12second,
                        )
                    ],
                  ),
                ),

                if (post.file != null && post.file!.isNotEmpty)
                  imageVideoSection(
                      height: height, length: length, width: width),
                const Spacer(),
                // const SizedBox(height: 15),
                const Divider(
                  color: Color(0xFFF3F3F3),
                  thickness: 1,
                  height: 0,
                  endIndent: 20,
                  indent: 20,
                ),
                const SizedBox(height: 15),
                commentSection(),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget commentSection() {
    PostModel post = widget.post;
    return Padding(
      padding: const EdgeInsets.fromLTRB(15, 0, 15, 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Consumer<UpdatesProvider>(
            builder: (context, provider, child) {
              return InkWell(
                onTap: () async {
                  // await EasyLoading.show();
                  if (!mounted) return;
                  if (provider.postLikeId.contains(post.id)) {
                    if (widget.post.like != null) {
                      widget.post.like = widget.post.like! - 1;
                    }
                    provider.postLikeId.remove(post.id);
                  } else {
                    widget.post.like = (widget.post.like ?? 0) + 1;
                    provider.postLikeId.add(post.id);
                  }
                  post = widget.post;
                  await provider.likePost(
                      postId: post.id.toString(),
                      context: context,
                      // like: post.userLiked == true ? false : true,
                      like: (provider.postLikeId.contains(post.id) ||
                              post.userLiked == true)
                          ? true
                          : false);

                  // provider.currentPage = 0;
                  // provider.pagingController?.addListener(() {
                  //   return provider.
                  // });

                  // await provider.getPostDetails(
                  //   postId: post.id!,
                  // );
                  // await EasyLoading.dismiss();
                },
                child: Row(
                  children: [
                    Icon(
                      // post.userLiked == true
                      provider.postLikeId.contains(post.id)
                          ? Icons.favorite
                          : Icons.favorite_outline_rounded,
                      size: 15,
                      color:
                          // post.userLiked == true
                          provider.postLikeId.contains(post.id)
                              ? Colors.red
                              : Colors.black,
                    ),
                    const SizedBox(width: 5),
                    Text(
                      // '${(provider.postLikeId.contains(post.id)) ? post.like! + 1 : post.like ?? 0} ${post.like == 1 ? "Like" : "Likes"}',
                      '${post.like ?? 0} ${post.like == 1 ? "Like" : "Likes"}',
                      style: GoogleFonts.rubik(
                        fontSize: 12,
                        color: const Color(0xFF1E2138),
                      ),
                    )
                  ],
                ),
              );
            },
          ),
          const Spacer(),
          const ImageIcon(
            AssetImage('assets/icons/comments.png'),
            size: 15,
          ),
          const SizedBox(width: 5),
          Text(
            '${post.comments ?? 0} ${post.comments == 1 ? "Comment" : "Comments"}',
            style: GoogleFonts.rubik(
              fontSize: 12,
              color: const Color(0xFF1E2138),
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () async {
              await share(
                title: "title",
                text: post.description.toString(),
                linkUrl: post.postLink.toString(),
              );
            },
            child: Row(
              children: [
                const ImageIcon(
                  AssetImage('assets/icons/share-icon.png'),
                  size: 15,
                ),
                const SizedBox(width: 5),
                Text(
                  'Share',
                  style: GoogleFonts.rubik(
                    fontSize: 12,
                    color: const Color(0xFF1E2138),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget imageVideoSection({
    required int? length,
    required double? height,
    required double? width,
  }) {
    PostModel post = widget.post;
    return Container(
      padding: const EdgeInsets.only(left: 8.0),
      child: Wrap(
        runSpacing: 1,
        spacing: 1,
        children: post.file!.map((e) {
          int index = post.file!.indexOf(e);

          if (length! == 1) {
            height = 170 * h;
            width = 329 * w;
          } else if (length == 2) {
            height = 170 * h;
            width = 160 * w;
          } else if (length == 3) {
            // height = 170 * h;
            // width = 160 * w;
            if (index == 0) {
              height = 170 * h;
              width = 329 * w;
            } else {
              height = 86 * h;
              width = 164 * w;
            }
          } else if (length >= 4) {
            if (index == 0) {
              height = 170 * h;
              width = 329 * w;
            } else {
              height = 86 * h;
              width = 109 * w;
            }
          }

          if (post.file!.first.fileType == "video") {
            return InkWell(
              onTap: () {
                var pvd = Provider.of<UpdatesProvider>(context, listen: false);
                pvd.getPostDetails(postId: post.id!.toInt());

                pvd.getComments(postId: post.id!.toInt());
                PageNavigator.push(
                  context: context,
                  route: UpdateDetailedScreen(
                    color: Colors.white,
                    post: post,
                  ),
                );
              },
              child: _videoThumbnail(),
            );
          }

          return index > 3
              ? const SizedBox()
              : SizedBox(
                  height: height,
                  width: width,
                  child: index <= 2
                      ? CachedNetworkImage(
                          imageUrl:
                              '${e.file.toString()}?${DateTime.now().minute}',
                          fit: BoxFit.cover,
                        )
                      : Container(
                          height: 70 * h,
                          width: 106 * w,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            image: DecorationImage(
                                image: CachedNetworkImageProvider(
                                  '${e.file.toString()}?${DateTime.now().minute}',
                                ),
                                fit: BoxFit.fill),
                          ),
                          child: ClipRRect(
                            child: BackdropFilter(
                              filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
                              child: Container(
                                alignment: Alignment.center,
                                color: Colors.grey.withOpacity(0.1),
                                child: Text(
                                  "+ ${post.file!.length - 3}",
                                  style: tsS12w600FFFFF,
                                ),
                              ),
                            ),
                          ),
                        ));
        }).toList(),
      ),
    );
  }

  Widget profileSectionPostView() {
    PostModel post = widget.post;
    return Row(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(50),
          child: CachedNetworkImage(
            height: 50,
            width: 50,
            fit: BoxFit.cover,
            imageUrl:
                '${post.profilePhoto.toString()}?${DateTime.now().minute}', //double tap like -- other wise profile image reload every time
            errorWidget: (context, url, error) {
              return Container(
                height: 50,
                width: 50,
                decoration: BoxDecoration(
                  color: ThemeColors.primaryColor,
                  shape: BoxShape.circle,
                ),
                alignment: Alignment.center,
                child: Text(
                  post.name?.substring(0, 1).toUpperCase() ?? "",
                  style: GoogleFonts.rubik(
                    fontSize: 22,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(width: 15),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (post.name != null)
                Text(
                  post.name.toString(),
                  overflow: TextOverflow.ellipsis,
                  style: GoogleFonts.rubik(
                    color: const Color(0xFF1E2138),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              Row(
                children: [
                  if (post.designation != null)
                    Text(
                      post.designation.toString(),
                      style: GoogleFonts.rubik(
                        color: const Color(0xFF1E2138),
                        fontSize: 12,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 5 * w),
                    height: 5,
                    width: 5,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle, color: ThemeColors.color808080),
                  ),
                  Text(
                    post.createdAt.toString(),
                    style: GoogleFonts.rubik(
                      color: ThemeColors.color808080,
                      fontSize: 12,
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        if (post.isOwner == true)
          PopupMenuButton(
            icon: Icon(
              Icons.more_vert_outlined,
              color: ThemeColors.color979797,
            ),
            onSelected: (value) {
              if (value == 'delete') {
                showDialog(
                    context: context,
                    builder: (ctxt) {
                      return AlertDialog(
                        title: const Text('Confirm delete'),
                        content: const Text('Are you sure you want to delete?'),
                        actions: [
                          ElevatedButton(
                            onPressed: () => Navigator.pop(context),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey[300],
                            ),
                            child: const Text(
                              'Cancel',
                              style: TextStyle(color: Colors.black),
                            ),
                          ),
                          Consumer<UpdatesProvider>(
                            builder: (context, provider, child) {
                              return ElevatedButton(
                                onPressed: () {
                                  provider.deleteUpdate(
                                      postId: post.id.toString(),
                                      context: context);
                                  if (widget.goToDetails == false) {
                                    Navigator.pop(context);
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: ThemeColors.secondaryColor,
                                ),
                                child: const Text('Delete'),
                              );
                            },
                          )
                        ],
                      );
                    });
              } else if (value == "edit") {
                showDialog(
                    context: context,
                    builder: (ctxt) {
                      return AlertDialog(
                        title: const Text('Confirm edit'),
                        content:
                            const Text('Are you sure want to edit the post?'),
                        actions: [
                          ElevatedButton(
                            onPressed: () => Navigator.pop(context),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey[300],
                            ),
                            child: const Text(
                              'Cancel',
                              style: TextStyle(color: Colors.black),
                            ),
                          ),
                          Consumer<UpdatesProvider>(
                            builder: (context, provider, child) {
                              return ElevatedButton(
                                onPressed: () async {
                                  Navigator.pop(context);
                                  provider.selectedPostsImages.clear();
                                  provider.removedImageIdList.clear();
                                  provider.selectedPostsVideo == null;
                                  provider.alreadyPostImageList?.clear();
                                  provider.isVideoRemoved = false;

                                  await Navigator.of(context)
                                      .push(MaterialPageRoute(
                                    builder: (context) => EditUpdatesScreen(
                                      postId: post.id?.toInt(),
                                      file: post.file,
                                      content: post.description.toString(),
                                    ),
                                  ));

                                  provider.getUpdates(context: context);
                                  provider.getUserPolicies();
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: ThemeColors.secondaryColor,
                                ),
                                child: const Text('Edit'),
                              );
                            },
                          )
                        ],
                      );
                    });
              }
            },
            itemBuilder: (context) {
              return [
                const PopupMenuItem(
                  value: 'edit',
                  child: Text('Edit'),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Text('Delete'),
                ),
              ];
            },
          )
      ],
    );
  }

  Widget _videoThumbnail() {
    return SizedBox(
      height: 200 * h,
      width: 360 * w,
      child: Stack(
        children: [
          SizedBox(
            height: 200 * h,
            width: 360 * w,
            child: Image.network(
              widget.post.file!.first.thumbnail.toString(),
              fit: BoxFit.cover,
            ),
          ),
          const Center(
            child: Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 30,
            ),
          )
        ],
      ),
    );
  }
}

class SeeAllUpdateCardDetails extends StatelessWidget {
  final bool goToDetails;
  final Color color;
  final PostModel post;

  const SeeAllUpdateCardDetails({
    super.key,
    this.goToDetails = true,
    required this.color,
    required this.post,
  });

  final bool mounted = true;

  @override
  Widget build(BuildContext context) {
    int? length;
    double? height;
    double? width;
    if (post.file != null && post.file!.isNotEmpty) {
      length = post.file!.length;
    }

    // final document = htmlparser.parse(content ?? '');
    // final String parsedString =
    //     htmlparser.parse(document.body!.text).documentElement!.text;

    return Consumer<UpdatesProvider>(builder: (context, provid, child) {
      return InkWell(
        onDoubleTap: () {
          if (!mounted) return;
          provid.likePost(
            postId: post.id.toString(),
            context: context,
            like: post.userLiked == true ? false : true,
          );
          // if()

          provid.getPostDetails(postId: post.id ?? 0);
        },
        onTap: goToDetails == true
            ? () async {
                var pvd = Provider.of<UpdatesProvider>(context, listen: false);
                await pvd.getPostDetails(postId: post.id ?? 0);

                await pvd.getComments(postId: post.id ?? 0);
                if (!mounted) return;
                await Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => UpdateDetailedScreen(
                      color: color,
                      post: post,
                    ),
                  ),
                );
              }
            : () {},
        child: Card(
          color: color,
          margin: const EdgeInsets.all(0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    profileViewPost(context),
                    const SizedBox(height: 9),
                    if (post.description != null)
                      HtmlContentWidget(
                        htmlContent: post.description ?? '',
                      ),
                  ],
                ),
              ),
              if (post.file != null && post.file!.isNotEmpty)
                fileContent(
                  context: context,
                  length: length,
                  height: height,
                  width: width,
                ),
              const Divider(
                color: Color(0xFFF3F3F3),
                thickness: 1,
                height: 0,
                endIndent: 20,
                indent: 20,
              ),
              const SizedBox(height: 15),
              Padding(
                padding: const EdgeInsets.fromLTRB(15, 0, 15, 15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Consumer<UpdatesProvider>(
                      builder: (context, provider, child) {
                        return InkWell(
                          onTap: () async {
                            if (!mounted) return;
                            provider.likePost(
                              postId: post.id.toString(),
                              context: context,
                              like: post.userLiked == true ? false : true,
                            );

                            provider.getPostDetails(
                              postId: post.id ?? 0,
                            );
                          },
                          child: Row(
                            children: [
                              Icon(
                                post.userLiked == true
                                    ? Icons.favorite
                                    : Icons.favorite_outline_rounded,
                                size: 15,
                                color: post.userLiked == true
                                    ? Colors.red
                                    : Colors.black,
                              ),
                              const SizedBox(width: 5),
                              Text(
                                '${post.like ?? 0} ${(post.like == 1 || post.like == 0) ? "Like" : "Likes"}',
                                style: GoogleFonts.rubik(
                                  fontSize: 12,
                                  color: const Color(0xFF1E2138),
                                ),
                              )
                            ],
                          ),
                        );
                      },
                    ),
                    const Spacer(),
                    const ImageIcon(
                      AssetImage('assets/icons/comments.png'),
                      size: 15,
                    ),
                    const SizedBox(width: 5),
                    Text(
                      '${post.comments ?? 0} ${(post.comments == 1 || post.comments == 0) ? "Comment" : "Comments"}',
                      style: GoogleFonts.rubik(
                        fontSize: 12,
                        color: const Color(0xFF1E2138),
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () async {
                        await share(
                          title: "title",
                          text: post.description.toString(),
                          linkUrl: post.postLink.toString(),
                        );
                      },
                      child: Row(
                        children: [
                          const ImageIcon(
                            AssetImage('assets/icons/share-icon.png'),
                            size: 15,
                          ),
                          const SizedBox(width: 5),
                          Text(
                            'Share',
                            style: GoogleFonts.rubik(
                              fontSize: 12,
                              color: const Color(0xFF1E2138),
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget fileContent({
    required BuildContext context,
    required int? length,
    required double? height,
    required double? width,
  }) {
    return Container(
      padding: const EdgeInsets.only(left: 8.0),
      child: Wrap(
        runSpacing: 1,
        spacing: 1,
        children: post.file!.map((e) {
          int index = post.file!.indexOf(e);

          if (length! == 1) {
            height = 400 * h;
            width = 329 * w;
          } else if (length == 2) {
            height = 180 * h;
            width = 160 * w;
          } else if (length == 3) {
            if (index == 0) {
              height = 250 * h;
              width = 329 * w;
            } else {
              height = 136 * h;
              width = 164 * w;
            }
          } else if (length >= 4) {
            if (index == 0) {
              height = 250 * h;
              width = 329 * w;
            } else {
              height = 136 * h;
              width = 109 * w;
            }
          }
          if (post.file!.first.fileType == "video") {
            return InkWell(
              onTap: () {
                PageNavigator.push(
                  context: context,
                  route: VideoViewScreen(file: post.file),
                );
              },
              child: SizedBox(
                height: 300 * h,
                width: 360 * w,
                child: Stack(
                  children: [
                    SizedBox(
                      height: 300 * h,
                      width: 360 * w,
                      child: CachedNetworkImage(
                        imageUrl:
                            '${post.file!.first.thumbnail}?${DateTime.now().minute}',
                        fit: BoxFit.cover,
                      ),
                    ),
                    const Center(
                      child: Icon(
                        Icons.play_arrow,
                        color: Colors.white,
                        size: 30,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          return index > 3
              ? const SizedBox()
              : InkWell(
                  onTap: () async {
                    PageNavigator.push(
                        context: context,
                        route: PostImageViewer(
                          fileView: post.file,
                        ));
                  },
                  child: SizedBox(
                    height: height,
                    width: width,
                    child: index <= 2
                        ? CachedNetworkImage(
                            imageUrl:
                                '${e.file.toString()}?${DateTime.now().minute}',
                            fit: BoxFit.cover,
                          )
                        : Container(
                            height: 70 * h,
                            width: 106 * w,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              image: DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      '${e.file.toString()}?${DateTime.now().minute}'),
                                  fit: BoxFit.fill),
                            ),
                            child: ClipRRect(
                              // make sure we apply clip it properly
                              child: BackdropFilter(
                                filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
                                child: Container(
                                  alignment: Alignment.center,
                                  color: Colors.grey.withOpacity(0.1),
                                  child: Text(
                                    "+ ${post.file!.length - 3}",
                                    style: tsS12w600FFFFF,
                                  ),
                                ),
                              ),
                            ),
                          ),
                  ),
                );
        }).toList(),
      ),
    );
  }

  Widget profileViewPost(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            if (post.profilePhoto != null) {
              PageNavigator.push(
                context: context,
                route: PhotoViewScreen(
                    image: post.profilePhoto, extension: "jpeg"),
              );
            }
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(50),
            child: CachedNetworkImage(
              height: 50,
              width: 50,
              fit: BoxFit.fill,
              imageUrl:
                  '${post.profilePhoto.toString()}?${DateTime.now().minute}',
              errorWidget: (context, url, error) {
                return Container(
                  height: 50,
                  width: 50,
                  decoration: BoxDecoration(
                    color: ThemeColors.primaryColor,
                    shape: BoxShape.circle,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    post.name?.substring(0, 1).toUpperCase() ?? "",
                    style: GoogleFonts.rubik(
                      fontSize: 22,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(width: 15),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (post.name != null)
                Text(
                  post.name.toString(),
                  overflow: TextOverflow.ellipsis,
                  style: GoogleFonts.rubik(
                    color: const Color(0xFF1E2138),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              Row(
                children: [
                  if (post.designation != null)
                    Text(
                      post.designation.toString(),
                      style: GoogleFonts.rubik(
                        color: const Color(0xFF1E2138),
                        fontSize: 12,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 5 * w),
                    height: 5,
                    width: 5,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle, color: ThemeColors.color808080),
                  ),
                  Text(
                    post.createdAt.toString(),
                    style: GoogleFonts.rubik(
                      color: ThemeColors.color808080,
                      fontSize: 12,
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        if (post.isOwner == true)
          PopupMenuButton(
            icon: Icon(
              Icons.more_vert_outlined,
              color: ThemeColors.color979797,
            ),
            onSelected: (value) {
              if (value == 'delete') {
                showDialog(
                    context: context,
                    builder: (ctxt) {
                      return AlertDialog(
                        title: const Text('Confirm delete'),
                        content: const Text('Are you sure you want to delete?'),
                        actions: [
                          ElevatedButton(
                            onPressed: () => Navigator.pop(context),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey[300],
                            ),
                            child: const Text(
                              'Cancel',
                              style: TextStyle(color: Colors.black),
                            ),
                          ),
                          Consumer<UpdatesProvider>(
                            builder: (context, provider, child) {
                              return ElevatedButton(
                                onPressed: () {
                                  provider.deleteUpdate(
                                    postId: post.id.toString(),
                                    context: context,
                                  );
                                  if (goToDetails == false) {
                                    Navigator.pop(context);
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: ThemeColors.secondaryColor,
                                ),
                                child: const Text('Delete'),
                              );
                            },
                          )
                        ],
                      );
                    });
              } else if (value == 'edit') {
                showDialog(
                    context: context,
                    builder: (ctxt) {
                      return AlertDialog(
                        title: const Text('Confirm edit'),
                        content:
                            const Text('Are you sure want to edit the post?'),
                        actions: [
                          ElevatedButton(
                            onPressed: () => Navigator.pop(context),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey[300],
                            ),
                            child: const Text(
                              'Cancel',
                              style: TextStyle(color: Colors.black),
                            ),
                          ),
                          Consumer<UpdatesProvider>(
                            builder: (context, provider, child) {
                              return ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                  Navigator.pop(context);
                                  provider.selectedPostsImages.clear();
                                  provider.removedImageIdList.clear();
                                  provider.selectedPostsVideo == null;
                                  provider.alreadyPostImageList?.clear();
                                  provider.isVideoRemoved = false;
                                  provider.getUserPolicies();
                                  Navigator.of(context).push(MaterialPageRoute(
                                    builder: (context) => EditUpdatesScreen(
                                      postId: post.id?.toInt(),
                                      file: post.file,
                                      content: post.description.toString(),
                                    ),
                                  ));
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: ThemeColors.secondaryColor,
                                ),
                                child: const Text('Edit'),
                              );
                            },
                          )
                        ],
                      );
                    });
              }
            },
            itemBuilder: (context) {
              return [
                const PopupMenuItem(
                  value: 'edit',
                  child: Text('Edit'),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Text('Delete'),
                ),
              ];
            },
          )
      ],
    );
  }
}

class _ControlsOverlay extends StatefulWidget {
  const _ControlsOverlay({required this.controller});

  static const List<double> _examplePlaybackRates = <double>[
    0.25,
    0.5,
    1.0,
    1.5,
    2.0,
    3.0,
    5.0,
    10.0,
  ];

  final VideoPlayerController controller;

  @override
  State<_ControlsOverlay> createState() => _ControlsOverlayState();
}

class _ControlsOverlayState extends State<_ControlsOverlay> {
  @override
  Widget build(BuildContext context) {
    debugPrint("video controller ${widget.controller.value.isPlaying}");
    return Stack(
      children: <Widget>[
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 50),
          reverseDuration: const Duration(milliseconds: 200),
          child: widget.controller.value.isPlaying
              ? const SizedBox.shrink()
              : Container(
                  color: Colors.black26,
                  child: const Center(
                    child: Icon(
                      Icons.play_arrow,
                      color: Colors.white,
                      size: 100.0,
                      semanticLabel: 'Play',
                    ),
                  ),
                ),
        ),
        GestureDetector(
          onTap: () {
            setState(() {
              widget.controller.value.isPlaying
                  ? widget.controller.pause()
                  : widget.controller.play();
            });
          },
          onDoubleTap: () {
            widget.controller.seekTo(const Duration(seconds: 10));
          },
        ),
        Align(
          alignment: Alignment.topRight,
          child: PopupMenuButton<double>(
            initialValue: widget.controller.value.playbackSpeed,
            tooltip: 'Playback speed',
            onSelected: (double speed) {
              setState(() {
                widget.controller.setPlaybackSpeed(speed);
              });
            },
            itemBuilder: (BuildContext context) {
              return <PopupMenuItem<double>>[
                for (final double speed
                    in _ControlsOverlay._examplePlaybackRates)
                  PopupMenuItem<double>(
                    value: speed,
                    child: Text(
                      '${speed}x',
                      style: tsS14BN,
                    ),
                  )
              ];
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 12,
                horizontal: 16,
              ),
              child: Text(
                '${widget.controller.value.playbackSpeed}x',
                style: tsS14FFFFF,
              ),
            ),
          ),
        ),
        const Align(
          alignment: Alignment.bottomRight,
          child: Icon(
            Icons.volume_mute_sharp,
            color: Colors.white,
          ),
        )
      ],
    );
  }
}

class HtmlContentWidget extends StatelessWidget {
  final String htmlContent;

  const HtmlContentWidget({super.key, required this.htmlContent});

  @override
  Widget build(BuildContext context) {
    return ListView(
      shrinkWrap:
          true, // Allows the ListView to take up only the necessary height
      physics:
          const NeverScrollableScrollPhysics(), // Disable scrolling for the ListView
      children: [
        Html(data: htmlContent),
      ],
    );
  }
}
