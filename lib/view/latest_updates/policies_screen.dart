import 'package:e8_hr_portal/model/user_policies_model.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/latest_updates/widgets/checkbox_row_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class PolicesScreen extends StatelessWidget {
  static const route = 'policies_screen';
  PolicesScreen({super.key});
  final TextEditingController _searchController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Consumer<UpdatesProvider>(
      builder: (context, provider, child) {
        return SizedBox(
          height: 400 * h,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: w * 17, vertical: h * 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Align(
                  alignment: Alignment.center,
                  child: Container(
                    height: 8,
                    width: 100,
                    decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.4),
                        borderRadius: BorderRadius.circular(12)),
                  ),
                ),
                // TextfieldWithFilterIcon(
                //   controller: _searchController,
                //   hintText: 'Search ...',
                //   onChanged: (value) {
                //     // provider.subCategorySearch(value);
                //     setState(() {});
                //   },
                //   isShowFilterButton: false,
                //   isShowCloseButton:
                //       _searchController.text.isEmpty ? false : true,
                //   onButtonPressed: () {
                //     _searchController.clear();
                //     // provider.subCategorySearch('');
                //     setState(() {});
                //   },
                // ),
                Column(
                  children: [
                    CheckboxRowWidget(
                        title: 'Select All',
                        isBoxChecked:
                            provider.selectedUserPoliciesList.length ==
                                provider.userPoliciesModel?.length,
                        onTap: () {
                          provider.selectPoliciesList();
                        }),
                    Divider(
                        color: ThemeColors.color7E7E7E.withOpacity(.2),
                        thickness: 1),
                    if (provider.userPoliciesModel!
                        .where((element) => element.name!
                            .toLowerCase()
                            .contains(_searchController.text))
                        .isEmpty)
                      const Expanded(
                        child: Center(
                          child: Text("No result found"),
                        ),
                      ),
                    ListView.separated(
                      shrinkWrap: true,
                      padding:
                          const EdgeInsets.only(left: 5, top: 8, bottom: 20),
                      itemCount: provider.userPoliciesModel!.length,
                      itemBuilder: (context, index) {
                        UserPoliciesModel subCategory =
                            provider.userPoliciesModel![index];

                        return CheckboxRowWidget(
                          title: subCategory.name,
                          isBoxChecked: provider.selectedUserPoliciesList
                              .contains(subCategory),
                          onChanged: (p0) {
                            provider.changeSelectedSubCategory(subCategory);
                          },
                          onTap: () =>
                              provider.changeSelectedSubCategory(subCategory),
                        );
                      },
                      separatorBuilder: (context, index) {
                        return Divider(
                            color: ThemeColors.color7E7E7E.withOpacity(.2),
                            thickness: 1);
                      },
                    ),
                  ],
                )
              ],
            ),
          ),
        );
      },

      // floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      // floatingActionButton:
      //     Consumer<ForecastReportProvder>(builder: (context, provider2, _) {
      //   return Container(
      //     padding: EdgeInsets.fromLTRB(18 * w, 5 * h, 18 * w, 10 * w),
      //     child: Row(
      //       children: [
      //         Expanded(
      //             child: ButtonWidget(
      //           onPressed: () {
      //             provider2.clearSubCat();
      //           },
      //           color: CustomColors.color9F9F9F,
      //           title: "Clear",
      //           height: 45 * h,
      //         )),
      //         SizedBox(
      //           width: 20 * w,
      //         ),
      //         Expanded(
      //           child: ButtonWidget(
      //             onPressed: () {
      //               Navigator.pop(context);
      //             },
      //             title: "Submit",
      //             height: 45 * h,
      //           ),
      //         ),
      //       ],
      //     ),
      //   );
      // }
      // ),
    );
  }
}
