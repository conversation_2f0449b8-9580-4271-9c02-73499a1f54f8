import 'dart:io';

import 'package:flutter/material.dart';
import 'package:e8_hr_portal/helper/circula_loading_widget.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:provider/provider.dart';

class PostImageScreen extends StatefulWidget {
  final File? selectedImage;
  const PostImageScreen({
    super.key,
    required this.selectedImage,
  });

  @override
  _PostImageScreenState createState() => _PostImageScreenState();
}

class _PostImageScreenState extends State<PostImageScreen> {
  late TextEditingController _captionController;
  @override
  void initState() {
    _captionController = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    _captionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Share image'),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Expanded(
          //   child: Image.file(
          //     widget.selectedImage,
          //     fit: BoxFit.contain,
          //   ),
          // ),
          Container(
            height: kBottomNavigationBarHeight,
            padding: const EdgeInsets.symmetric(horizontal: 15),
            margin: const EdgeInsets.only(bottom: 5, top: 10),
            child: Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _captionController,
                    textCapitalization: TextCapitalization.sentences,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      hintText: 'Add a caption',
                      hintStyle: TextStyle(
                        color: Color(0xFFC2C2C2),
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                      fillColor: Color(0xFFF1F1F1),
                      filled: true,
                    ),
                  ),
                ),
                const SizedBox(width: 5),
                Consumer<UpdatesProvider>(builder: (context, provider, _) {
                  return provider.postingImage
                      ? Container(
                          alignment: Alignment.center,
                          height: kBottomNavigationBarHeight,
                          padding: const EdgeInsets.all(10),
                          child: const CircularLoadingWidget(),
                        )
                      : Container(
                          decoration: BoxDecoration(
                            color: ThemeColors.secondaryColor,
                            borderRadius: BorderRadius.circular(5),
                          ),
                          child: IconButton(
                            onPressed: () {
                              hideKeyboard(context);
                              // provider.postImage(
                              //   context: context,
                              //   // image: widget.selectedImage!,
                              //   caption: _captionController.text,
                              // );
                            },
                            icon: const Icon(
                              Icons.send,
                              color: Colors.white,
                            ),
                          ),
                        );
                }),
              ],
            ),
          )
        ],
      ),
    );
  }
}
