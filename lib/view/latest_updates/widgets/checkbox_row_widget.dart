import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import '../../../../../util/styles.dart';

class CheckboxRowWidget extends StatelessWidget {
  final String? title;

  final bool isBoxChecked;
  final Function()? onTap;
  final Function(bool?)? onChanged;
  const CheckboxRowWidget(
      {super.key,
      this.title,
      required this.isBoxChecked,
      this.onChanged,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.only(top: h * 6),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title ?? '',
                style: tsS14w500c2C2D33,
              ),
            ),
            Checkbox(
                value: isBoxChecked,
                onChanged: onChanged,
                activeColor: Colors.grey)
          ],
        ),
      ),
    );
  }
}
