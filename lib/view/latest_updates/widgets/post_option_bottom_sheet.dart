import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:provider/provider.dart';
import '../../../util/page_navigator.dart';
import '../post_new_updates_screen.dart';
import '../survey_section/create_survey_screen.dart';

class PostOptionBottomSheet extends StatelessWidget {
  PostOptionBottomSheet({super.key});

  final List postOptions = ['Create Social Post', 'Create Survey'];
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 14),
          height: 5,
          width: 135,
          decoration: BoxDecoration(color: ThemeColors.colorD9D9D9),
        ),
        SizedBox(
          height: h * 15,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 29),
          child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Choose Post Type',
                style: tsS16w4009F9F9F,
              )),
        ),
        const SizedBox(height: 7),
        Consumer<UpdatesProvider>(
          builder: (context, provider, child) {
            return ListView.separated(
                padding: const EdgeInsets.symmetric(horizontal: 29),
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  final data = postOptions[index];
                  return InkWell(
                    onTap: () async {
                      provider.selectedPostType = data;
                      Navigator.pop(context);
                      if (index == 0) {
                        PageNavigator.pushSlideRight(
                            context: context,
                            route: const PostNewUpdatesScreen());
                      } else {
                        provider.extraOptionNumber = 0;
                        provider.textControllers.clear();
                        provider.textControllers.addAll(
                            [TextEditingController(), TextEditingController()]);
                        provider.allowMultipleAnswers = false;
                        provider.allowResultPublic = false;

                        PageNavigator.pushSlideRight(
                            context: context, route: CreateSurveyScreen());
                      }
                    },
                    child: Column(
                      children: [
                        const SizedBox(height: 10),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Align(
                                alignment: Alignment.centerLeft,
                                child: Text(data,
                                    style: provider.selectedPostType == data
                                        ? tsS14w500c03AD9E
                                        : tsS14w500Black)),
                            if (provider.selectedPostType == data)
                              ImageIcon(
                                const AssetImage(
                                  'assets/icons/tick.png',
                                ),
                                color: ThemeColors.colorF9637D,
                              )
                          ],
                        ),
                        const SizedBox(height: 10),
                      ],
                    ),
                  );
                },
                separatorBuilder: (context, index) {
                  return Divider(
                    thickness: 1,
                    color: ThemeColors.colorD9D9D9,
                  );
                },
                itemCount: postOptions.length);
          },
        ),
        SizedBox(height: h * 25),
      ],
    );
  }
}
