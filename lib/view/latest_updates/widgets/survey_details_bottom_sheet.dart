import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/view_vote_model.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class SurveyDetailsBottomSheet extends StatelessWidget {
  const SurveyDetailsBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<UpdatesProvider>(
      builder: (context, provider, child) {
        ViewVoteModel? voteDetails = provider.viewVote;
        return Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                margin: const EdgeInsets.symmetric(vertical: 14),
                height: 5,
                width: 135,
                decoration: BoxDecoration(color: ThemeColors.colorD9D9D9),
              ),
              SizedBox(
                height: h * 15,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 29),
                child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'Survey Details',
                      style: tsS16w4009F9F9F,
                    )),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 29),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    voteDetails?.question ?? '',
                    style: tsS14w500Black,
                  ),
                ),
              ),
              provider.isViewVoteLoading == true
                  ? const Expanded(
                      child:
                          Center(child: CircularProgressIndicator.adaptive()))
                  : Expanded(
                      child: ListView.separated(
                        physics: const ClampingScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: voteDetails?.options?.length ?? 0,
                        itemBuilder: (context, index) {
                          Options? option = voteDetails?.options?[index];
                          return _voteListWidget(
                              option: option?.name ?? '',
                              votes: option?.totalVotes ?? 0,
                              win: option?.isMaximum ?? false,
                              users: option?.users);
                        },
                        separatorBuilder: (context, index) {
                          return SizedBox(height: h * 15);
                        },
                      ),
                    ),
              SizedBox(height: h * 25),
            ]);
      },
    );
  }
}

Widget _voteListWidget(
    {required String option,
    required int votes,
    required bool win,
    required List<Users>? users}) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 29),
    child: Column(
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                option,
                style: tsS14w500c45464E,
              ),
            ),
            SizedBox(width: w * 5),
            if (win && votes != 0)
              const Icon(
                Icons.star,
                color: Color(0xffFED800),
              ),
            SizedBox(width: w * 3),
            Text(
              votes == 1 ? '1 Vote' : '$votes Votes',
              style: tsS12w500c979797,
            )
          ],
        ),
        // SizedBox(height: h * 5),
        const Divider(color: Color(0xffD9D9D9)),
        if (users != null)
          ListView.separated(
              padding: const EdgeInsets.only(top: 3),
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemBuilder: (context, index) {
                Users? user = users[index];
                return Row(
                  children: [
                    CachedNetworkImage(
                      imageUrl: user.profPic ?? '',
                      imageBuilder: (context, imageProvider) {
                        return Container(
                          height: h * 28,
                          width: w * 28,
                          decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              image: DecorationImage(
                                  image: NetworkImage(user.profPic ?? ''),
                                  fit: BoxFit.cover)),
                        );
                      },
                      errorWidget: (context, url, error) {
                        return Container(
                          height: h * 28,
                          width: w * 28,
                          decoration: BoxDecoration(
                            color: ThemeColors.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            user.name!.substring(0, 1).toUpperCase(),
                            style: GoogleFonts.rubik(
                              color: Colors.white,
                              fontSize: 17,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        );
                      },
                    ),
                    SizedBox(width: w * 8),
                    Text(
                      user.name ?? '',
                      style: tsS12w400cC1C1C1,
                    )
                  ],
                );
              },
              separatorBuilder: (context, index) {
                return Divider(color: const Color(0xffD9D9D9).withOpacity(.6));
              },
              itemCount: users.length)
      ],
    ),
  );
}
