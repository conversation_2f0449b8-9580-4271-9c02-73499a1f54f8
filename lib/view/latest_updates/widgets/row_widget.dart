import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import '../../../../../util/styles.dart';

class RowWidget extends StatelessWidget {
  final String? title;
  final Function()? onTap;

  const RowWidget({
    super.key,
    this.title,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: h * 16),
          Text(title ?? '', style: tsS14w500c2C2D33),
          <PERSON>zed<PERSON><PERSON>(height: h * 10),
        ],
      ),
    );
  }
}
