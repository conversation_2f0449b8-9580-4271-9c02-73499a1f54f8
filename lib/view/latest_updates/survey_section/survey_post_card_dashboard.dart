// ignore: must_be_immutable
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/post_model.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../util/colors.dart';
import '../../../util/page_navigator.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../latest_updates_screen.dart';
import 'survey_post_card.dart';

class SurveyPostCardDashboard extends StatelessWidget {
  final int? index;
  final PostModel? postModel;
  final int currentLength;
  const SurveyPostCardDashboard(
      {super.key,
      required this.postModel,
      required this.index,
      required this.currentLength});

  @override
  Widget build(BuildContext context) {
    int maxVote = getMaxVote(postModel?.options);
    double height = 240 * h;
    double width = 350 * w;

    if (currentLength == 3) {
      switch (index) {
        case 0:
          height = 220 * h;
          width = 350 * w;
          break;
        case 1:
          height = 240 * h;
          width = 335 * w;
          break;
        default:
          height = 260 * h;
          width = 320 * w;
          break;
      }
    } else if (currentLength == 2) {
      switch (index) {
        case 0:
          height = 240 * h;
          width = 350 * w;
          break;
        default:
          height = 260 * h;
          width = 335 * w;
          break;
      }
    }
    return Dismissible(
        key: GlobalKey(),
        onDismissed: (direction) {
          UpdatesProvider provider = Provider.of(context, listen: false);
          provider.removeListScroll(index: index!);
        },
        direction: DismissDirection.endToStart,
        child: Align(
          alignment: Alignment.centerLeft,
          child: InkWell(
            onTap: () async {
              Provider.of<UpdatesProvider>(context, listen: false)
                  .getUpdates(context: context);
              PageNavigator.pushSlideup(
                context: context,
                route: const LatestUpdatesScreen(),
              );
            },
            child: AnimatedContainer(
              duration: const Duration(seconds: 2),
              curve: Curves.fastOutSlowIn,
              // height: 240,
              height: height,
              width: width,
              padding: const EdgeInsets.fromLTRB(15, 20, 15, 10),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: const [
                  BoxShadow(
                    offset: Offset(1, 1),
                    color: Color.fromRGBO(0, 0, 0, 0.10),
                    blurRadius: 10,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(50),
                            child: CachedNetworkImage(
                              height: 35 * h,
                              width: 35 * w,
                              fit: BoxFit.cover,
                              imageUrl: postModel?.profilePhoto ?? '',
                              errorWidget: (context, url, error) {
                                return Container(
                                  height: 35 * h,
                                  width: 35 * w,
                                  decoration: BoxDecoration(
                                    color: ThemeColors.primaryColor,
                                    shape: BoxShape.circle,
                                  ),
                                  alignment: Alignment.center,
                                  child: Text(
                                    postModel?.name
                                            ?.substring(0, 1)
                                            .toUpperCase() ??
                                        '',
                                    style: GoogleFonts.rubik(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.white),
                                  ),
                                );
                              },
                            ),
                          ),
                          const SizedBox(width: 15),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  postModel?.name ?? '',
                                  overflow: TextOverflow.ellipsis,
                                  style: GoogleFonts.rubik(
                                    color: const Color(0xFF1E2138),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Row(
                                  children: [
                                    Text(postModel?.designation ?? '',
                                        style: tsS12Normalf66666),
                                    Container(
                                      margin: EdgeInsets.symmetric(
                                          horizontal: 5 * w),
                                      height: 5,
                                      width: 5,
                                      decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: ThemeColors.color808080),
                                    ),
                                    Text(
                                      postModel?.createdAt ?? '',
                                      style: GoogleFonts.rubik(
                                        color: ThemeColors.color808080,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w300,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          if (postModel?.isOwner == true)
                            popUp(surveyID: postModel!.id!, context: context)
                        ],
                      ),
                      const SizedBox(height: 3),
                      Text(
                        postModel?.questions ?? '',
                        overflow: TextOverflow.ellipsis,
                        style: tsS12w500c161616,
                        maxLines: 1,
                      ),
                      // const SizedBox(height: 3),
                      Text(
                        postModel?.isAllowMultiple == true
                            ? 'Select one or more options'
                            : 'Select one option',
                        style: tsS10w400979797,
                      ),
                      // const SizedBox(height: 12),
                      Consumer<UpdatesProvider>(
                        builder: (context, provider, child) {
                          return Column(
                              children: postModel?.options == null
                                  ? []
                                  : postModel!.options!
                                      .map((e) {
                                        double percentage = 0;
                                        // if (e.votePercentage! > 9) {
                                        //   percentage = e.votePercentage! / 100;
                                        // } else {
                                        //   percentage = e.votePercentage! / 10;
                                        // }
                                        if (maxVote != 0) {
                                          percentage =
                                              ((e.totalVotes ?? 0) / maxVote);
                                        }
                                        return _answerOptions(
                                            value: e.id!,
                                            option: e.name ?? '',
                                            count: e.totalVotes ?? 0,
                                            groupValue: e.groupValue ?? 0,
                                            percentage: percentage,
                                            onTap: () {
                                              // provider.surveyVote(
                                              //     surveyID:
                                              //         postModel!.id.toString(),
                                              //     optionID: e.id.toString(),
                                              //     context: context);
                                              Provider.of<UpdatesProvider>(
                                                      context,
                                                      listen: false)
                                                  .getUpdates(context: context);
                                              PageNavigator.pushSlideup(
                                                  context: context,
                                                  route:
                                                      const LatestUpdatesScreen());
                                            },
                                            maxLines: 1);
                                      })
                                      .toList()
                                      .sublist(0, 2));
                        },
                      ),
                      SizedBox(height: h * 10),

                      InkWell(
                        onTap: () async {
                          Provider.of<UpdatesProvider>(context, listen: false)
                              .getUpdates(context: context);
                          PageNavigator.pushSlideup(
                            context: context,
                            route: const LatestUpdatesScreen(),
                          );
                        },
                        child: Text(
                          'Tap here to vote',
                          style: tsS12w500cF9637D,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      // SizedBox(height: h * 5),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ));
  }

  Widget _answerOptions(
      {required String option,
      required int count,
      required int value,
      required int groupValue,
      required double percentage,
      required Function()? onTap,
      int? maxLines}) {
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.only(right: 3),
        child: Column(
          children: [
            Row(
              children: [
                Radio(
                    fillColor: WidgetStatePropertyAll<Color>(value == groupValue
                        ? const Color(0xffF9637D)
                        : const Color(0xffC0C1C5)),
                    value: value,
                    groupValue: groupValue,
                    onChanged: null),
                Expanded(
                  child: Text(option,
                      maxLines: maxLines,
                      overflow: maxLines == null ? null : TextOverflow.ellipsis,
                      style: GoogleFonts.poppins(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xff45464E))),
                ),
                SizedBox(width: h * 8),
                Text(
                  count.toString(),
                  style: tsS10w400c979797,
                )
              ],
            ),
            Padding(
                padding: EdgeInsets.only(left: w * 42),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: LinearProgressIndicator(
                    minHeight: h * 3,
                    color: const Color(0xff34A853),
                    value: percentage,
                    backgroundColor: const Color(0xffE9E9E9),
                  ),
                )),
            // SizedBox(height: h * 8),
          ],
        ),
      ),
    );
  }
}
