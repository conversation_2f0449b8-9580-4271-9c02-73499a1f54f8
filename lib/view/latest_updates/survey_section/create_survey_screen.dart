import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../helper/drop_down_widget.dart';
import '../../../helper/textfield_widget.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../../util/validator.dart';
import '../policies_screen.dart';

class CreateSurveyScreen extends StatelessWidget {
  CreateSurveyScreen({super.key});
  final TextEditingController _questionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Consumer<UpdatesProvider>(
      builder: (context, provider, child) {
        return HisenseScaffold(
            screenTitle: 'Create Survey',
            actions: [
              Center(
                  child: provider.isCreatingSurveyLoading
                      ? const CircularProgressIndicator.adaptive()
                      : InkWell(
                          onTap: () {
                            if (_formKey.currentState!.validate()) {
                              if (provider.validationSub == false) {
                                provider.createSurvey(
                                    qustion: _questionController.text,
                                    options: provider.textControllers
                                        .where((element) =>
                                            element.text.isNotEmpty)
                                        .toList(),
                                    context: context);
                              }
                            }
                          },
                          child: Text('Post Now', style: tsS14FFFFF))),
              const SizedBox(width: 10)
            ],
            body: Form(
                key: _formKey,
                child: ListView(
                  padding: const EdgeInsets.all(16),
                  children: [
                    _textFormFieldWidget(
                        title: 'Question',
                        controller: _questionController,
                        keyboardType: TextInputType.text,
                        hintText: 'Ask Question',
                        validator: Validator.text),
                    Text('Options', style: tsS12w400c30292F),
                    SizedBox(height: h * 4),
                    ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          return TextFieldWidget(
                            controller: provider.textControllers[index],
                            hintText: 'Add option',
                            hintStyle: tsS12w400c9F9F9F,
                            textStyle: tsS12w500c131515,
                            keyboardType: TextInputType.text,
                            validator: index == 0 || index == 1
                                ? Validator.text
                                : null,
                            suffixIcon: provider.extraOptionNumber == index - 1
                                ? IconButton(
                                    onPressed: () {
                                      provider.addOption();
                                    },
                                    icon: const Icon(
                                      Icons.add_circle,
                                      color: Color(0xffE0E0E0),
                                      size: 26,
                                    ),
                                  )
                                : null,
                          );
                        },
                        separatorBuilder: (context, index) {
                          return SizedBox(height: h * 10);
                        },
                        itemCount: provider.textControllers.length),
                    _switchRowWidget(
                        title: 'Allow Multiple Answers',
                        provider: provider,
                        onChanged: (value) {
                          provider.changeMultipleAnswers();
                        },
                        value: provider.allowMultipleAnswers),
                    const SizedBox(height: 10),
                    _switchRowWidget(
                        title: 'Allow Result Public',
                        provider: provider,
                        onChanged: (value) {
                          provider.changeResultPublic();
                        },
                        value: provider.allowResultPublic),
                    const SizedBox(height: 10),
                    _subTitile(subtitle: 'User policy', isMandatory: true),
                    _policySection(),
                  ],
                )));
      },
    );
  }

  Consumer<UpdatesProvider> _policySection() {
    return Consumer<UpdatesProvider>(
      builder: (context, provider, _) {
        return DropdownWidget2(
          hintText: provider.selectedUserPoliciesList.isEmpty
              ? 'Policies'
              : provider.selectedUserPoliciesList.length ==
                      provider.userPoliciesModel?.length
                  ? 'All Selected'
                  : provider.selectedUserPoliciesList
                      .map((e) => e.name)
                      .join(','),
          selectedValue:
              provider.selectedUserPoliciesList.map((e) => e.name).join(','),
          onTap: () {
            showModalBottomSheet(
                isScrollControlled: true,
                isDismissible: true,
                useSafeArea: true,
                context: context,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadiusDirectional.only(
                    topEnd: Radius.circular(25),
                    topStart: Radius.circular(25),
                  ),
                ),
                builder: (context) {
                  return PolicesScreen();
                });
          },
          validator: (p0) {
            if (p0 == null || p0 == '') {
              provider.validationSub = true;
            } else {
              provider.validationSub = false;
            }
            return null;
          },
          validatorColor: provider.validationSub,
        );
      },
    );
  }

  Widget _subTitile({required String subtitle, bool isMandatory = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: Row(
            children: [
              Text(
                subtitle,
                style: tsS14w400c30292F,
              ),
              if (isMandatory)
                Text(
                  "*",
                  style: tsS14w400cFA0000,
                ),
            ],
          ),
        ),
        SizedBox(height: h * 4),
      ],
    );
  }

  Row _switchRowWidget(
      {required UpdatesProvider provider,
      required String title,
      required void Function(bool)? onChanged,
      required bool value}) {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      Text(
        title,
        style: tsS14w400c30292F,
      ),
      CupertinoSwitch(
        value: value,
        onChanged: onChanged,
      )
    ]);
  }

  Widget _textFormFieldWidget({
    required String title,
    String? hintText,
    required TextEditingController controller,
    String? Function(String?)? validator,
    required TextInputType keyboardType,
    String? errorText,
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: tsS12w400c30292F),
        SizedBox(height: h * 4),
        TextFieldWidget(
          controller: controller,
          hintText: hintText,
          hintStyle: tsS12w400c9F9F9F,
          textStyle: tsS12w500c131515,
          keyboardType: keyboardType,
          validator: validator,
          enabled: enabled,
          error: errorText,
          textCapitalization: TextCapitalization.sentences,
        ),
        SizedBox(height: h * 15),
      ],
    );
  }
}
