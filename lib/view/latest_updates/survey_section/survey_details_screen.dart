import 'package:flutter/material.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';

class SurveyDetailsScreen extends StatelessWidget {
  final Widget surveyCard;
  const SurveyDetailsScreen({super.key, required this.surveyCard});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        screenTitle: 'Latest Posts',
        body: Padding(
          padding: const EdgeInsets.fromLTRB(15, 15.0, 15.0, 0),
          child: <PERSON>umn(
            children: [
              surveyCard,
            ],
          ),
        ));
  }
}
