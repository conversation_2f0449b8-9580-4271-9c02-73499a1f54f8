// ignore: must_be_immutable
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/post_model.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/view/latest_updates/widgets/survey_details_bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../util/colors.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';

class SurveyPostCard extends StatelessWidget {
  final PostModel? postModel;
  const SurveyPostCard({super.key, required this.postModel});

  @override
  Widget build(BuildContext context) {
    int maxVote = getMaxVote(postModel?.options);
    return InkWell(
      onTap: () async {},
      child: IntrinsicHeight(
        child: Card(
          margin: const EdgeInsets.all(0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Row(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(50),
                          child: CachedNetworkImage(
                            height: 50,
                            width: 50,
                            fit: BoxFit.cover,
                            imageUrl: postModel?.profilePhoto ?? '',
                            errorWidget: (context, url, error) {
                              return Container(
                                height: 50,
                                width: 50,
                                decoration: BoxDecoration(
                                  color: ThemeColors.primaryColor,
                                  shape: BoxShape.circle,
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  postModel?.name
                                          ?.substring(0, 1)
                                          .toUpperCase() ??
                                      '',
                                  style: GoogleFonts.rubik(
                                      fontSize: 22,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white),
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(width: 15),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                postModel?.name ?? '',
                                overflow: TextOverflow.ellipsis,
                                style: GoogleFonts.rubik(
                                  color: const Color(0xFF1E2138),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Row(
                                children: [
                                  Text(
                                    postModel?.designation ?? '',
                                    style: GoogleFonts.rubik(
                                      color: const Color(0xFF1E2138),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w300,
                                    ),
                                  ),
                                  Container(
                                    margin:
                                        EdgeInsets.symmetric(horizontal: 5 * w),
                                    height: 5,
                                    width: 5,
                                    decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: ThemeColors.color808080),
                                  ),
                                  Text(
                                    postModel?.createdAt ?? '',
                                    style: GoogleFonts.rubik(
                                      color: ThemeColors.color808080,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w300,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        if (postModel?.isOwner == true)
                          popUp(surveyID: postModel!.id!, context: context)
                      ],
                    ),
                    const SizedBox(height: 9),
                    Text(
                      postModel?.questions ?? '',
                      style: tsS14w500c161616,
                    ),
                    const SizedBox(height: 3),
                    Text(
                      postModel?.isAllowMultiple == true
                          ? 'Select one or more options'
                          : 'Select one option',
                      style: tsS12w400979797,
                    ),
                    const SizedBox(height: 12),
                    Consumer<UpdatesProvider>(
                      builder: (context, provider, child) {
                        return Column(
                            children: postModel?.options == null
                                ? []
                                : postModel!.options!.map((e) {
                                    double percentage = 0;
                                    // if (e.votePercentage! > 9) {
                                    //   percentage = e.votePercentage! / 100;
                                    // } else {
                                    //   percentage = e.votePercentage! / 10;
                                    // }
                                    if (maxVote != 0) {
                                      percentage =
                                          ((e.totalVotes ?? 0) / maxVote);
                                    }

                                    return answerOptions(
                                      value: e.id!,
                                      option: e.name ?? '',
                                      count: e.totalVotes ?? 0,
                                      groupValue: e.groupValue ?? 0,
                                      percentage: percentage,
                                      onTap: () {
                                        provider.surveyVote(
                                            surveyID: postModel!.id.toString(),
                                            optionID: e.id.toString(),
                                            context: context);
                                      },
                                    );
                                  }).toList());
                      },
                    ),
                    SizedBox(height: h * 8),
                    if (postModel?.isAllowResultPublic == true)
                      InkWell(
                        onTap: () {
                          UpdatesProvider provider =
                              Provider.of<UpdatesProvider>(context,
                                  listen: false);
                          provider.getVotedDetails(surveyID: postModel!.id!);
                          showModalBottomSheet<void>(
                            useSafeArea: true,
                            context: context,
                            isDismissible: true,
                            isScrollControlled: true,
                            shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadius.vertical(
                                top: Radius.circular(12),
                              ),
                            ),
                            builder: (BuildContext context) {
                              return const SurveyDetailsBottomSheet();
                            },
                          );
                        },
                        child: Text(
                          'Survey Results',
                          style: tsS14w500cF9637D,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    SizedBox(height: h * 5),
                  ],
                ),
              ),
              // const Spacer(),
              // const Divider(
              //   color: Color(0xFFF3F3F3),
              //   thickness: 1,
              //   height: 0,
              //   endIndent: 20,
              //   indent: 20,
              // ),
              // const SizedBox(height: 15),
              // Padding(
              //   padding: const EdgeInsets.fromLTRB(15, 0, 15, 15),
              //   child: Row(
              //     mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              //     children: [
              //       InkWell(
              //         onTap: () async {},
              //         child: Row(
              //           children: [
              //             Icon(
              //               isLiked == "true"
              //                   ? Icons.favorite
              //                   : Icons.favorite_outline_rounded,
              //               size: 15,
              //               color:
              //                   isLiked == "true" ? Colors.red : Colors.black,
              //             ),
              //             const SizedBox(width: 5),
              //             Text(
              //               '${likeCount ?? 0} ${likeCount == 1 ? "Like" : "Likes"}',
              //               style: GoogleFonts.rubik(
              //                 fontSize: 12,
              //                 color: const Color(0xFF1E2138),
              //               ),
              //             )
              //           ],
              //         ),
              //       ),
              //       const Spacer(),
              //       const ImageIcon(
              //         AssetImage('assets/icons/comments.png'),
              //         size: 15,
              //       ),
              //       const SizedBox(width: 5),
              //       Text(
              //         '${commentCount ?? 0} ${commentCount == 1 ? "Comment" : "Comments"}',
              //         style: GoogleFonts.rubik(
              //           fontSize: 12,
              //           color: const Color(0xFF1E2138),
              //         ),
              //       ),
              //       const Spacer(),
              //       GestureDetector(
              //         onTap: () async {},
              //         child: Row(
              //           children: [
              //             const ImageIcon(
              //               AssetImage('assets/icons/share-icon.png'),
              //               size: 15,
              //             ),
              //             const SizedBox(width: 5),
              //             Text(
              //               'Share',
              //               style: GoogleFonts.rubik(
              //                 fontSize: 12,
              //                 color: const Color(0xFF1E2138),
              //               ),
              //             ),
              //           ],
              //         ),
              //       )
              //     ],
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}

int getMaxVote(List<SurveyOptions>? listOfOptions) {
  int maxVote = 0;
  List<SurveyOptions> optionsList = [];
  if (listOfOptions != null) {
    optionsList.addAll(listOfOptions);
    optionsList.sort((a, b) => a.totalVotes!.compareTo(b.totalVotes ?? 0));
    maxVote = optionsList.last.totalVotes ?? 0;
  }
  return maxVote;
}

PopupMenuButton<String> popUp(
    {required BuildContext context, required int surveyID}) {
  return PopupMenuButton(
    icon: Icon(
      Icons.more_vert_outlined,
      color: ThemeColors.color979797,
    ),
    onSelected: (value) {
      if (value == 'delete') {
        showDialog(
            context: context,
            builder: (ctxt) {
              return AlertDialog(
                title: const Text('Confirm delete'),
                content: const Text('Are you sure you want to delete?'),
                actions: [
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[300],
                    ),
                    child: const Text(
                      'Cancel',
                      style: TextStyle(color: Colors.black),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      UpdatesProvider provider =
                          Provider.of<UpdatesProvider>(context, listen: false);
                      provider.deleteSurvey(
                          surveyId: surveyID, context: context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ThemeColors.secondaryColor,
                    ),
                    child: const Text('Delete'),
                  )
                ],
              );
            });
      } else if (value == "edit") {
        showDialog(
            context: context,
            builder: (ctxt) {
              return AlertDialog(
                title: const Text('Confirm edit'),
                content: const Text('Are you sure want to edit the post?'),
                actions: [
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[300],
                    ),
                    child: const Text(
                      'Cancel',
                      style: TextStyle(color: Colors.black),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () async {},
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ThemeColors.secondaryColor,
                    ),
                    child: const Text('Edit'),
                  )
                ],
              );
            });
      }
    },
    itemBuilder: (context) {
      return [
        // const PopupMenuItem(
        //   value: 'edit',
        //   child: Text('Edit'),
        // ),
        const PopupMenuItem(
          value: 'delete',
          child: Text('Delete'),
        ),
      ];
    },
  );
}

Widget answerOptions(
    {required String option,
    required int count,
    required int value,
    required int groupValue,
    required double percentage,
    Function()? onTap,
    int? maxLines}) {
  return InkWell(
    splashColor: Colors.transparent,
    highlightColor: Colors.transparent,
    onTap: onTap,
    child: Padding(
      padding: const EdgeInsets.only(right: 3),
      child: Column(
        children: [
          Row(
            children: [
              Radio(
                fillColor: WidgetStatePropertyAll<Color>(value == groupValue
                    ? const Color(0xffF9637D)
                    : const Color(0xffC0C1C5)),
                value: value,
                groupValue: groupValue,
                onChanged: null,
              ),
              Expanded(
                child: Text(option,
                    maxLines: maxLines,
                    overflow: maxLines == null ? null : TextOverflow.ellipsis,
                    style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xff45464E))),
              ),
              SizedBox(width: h * 8),
              Text(
                count.toString(),
                style: tsS12w400c979797,
              )
            ],
          ),
          Padding(
              padding: EdgeInsets.only(left: w * 42),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: LinearProgressIndicator(
                  minHeight: h * 4,
                  color: const Color(0xff34A853),
                  value: percentage,
                  backgroundColor: const Color(0xffE9E9E9),
                ),
              )),
          SizedBox(height: h * 8),
        ],
      ),
    ),
  );
}
