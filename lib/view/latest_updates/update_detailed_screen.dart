import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/post_model.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/employee_relations/widget/photo_view.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';

import 'latest_updates_screen.dart';

class UpdateDetailedScreen extends StatefulWidget {
  final PostModel post;
  final Color? color;

  const UpdateDetailedScreen({
    super.key,
    required this.post,
    this.color,
  });

  @override
  State<UpdateDetailedScreen> createState() => _UpdateDetailedScreenState();
}

class _UpdateDetailedScreenState extends State<UpdateDetailedScreen> {
  final TextEditingController _commentController = TextEditingController();

  late PostModel post;
  @override
  void initState() {
    post = widget.post;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Latest Posts',
      body: Consumer<UpdatesProvider>(
        builder: (context, provider, child) {
          final post = provider.postList.first;
          if (provider.commentsLoading == true ||
              provider.isUpdatesLoading == true) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          return Padding(
            padding: const EdgeInsets.fromLTRB(15, 15.0, 15.0, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const SizedBox(height: 15),
                        SeeAllUpdateCardDetails(
                          goToDetails: false,
                          color: Colors.white,
                          post: post,
                        ),
                        commentSection(provider),
                        const SizedBox(
                          height: 20,
                        )
                      ],
                    ),
                  ),
                ),
                writeCommentSection(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget writeCommentSection() {
    return Consumer<UpdatesProvider>(builder: (context, provider, child) {
      return Container(
        height: 56 * h,
        margin: const EdgeInsets.only(bottom: 30),
        child: Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _commentController,
                textCapitalization: TextCapitalization.sentences,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                      borderSide: BorderSide.none,
                      borderRadius: BorderRadius.circular(212)),
                  enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide.none,
                      borderRadius: BorderRadius.circular(212)),
                  focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide.none,
                      borderRadius: BorderRadius.circular(25)),
                  hintText: 'Write your comment here',
                  hintStyle: const TextStyle(
                    color: Color(0xFFC2C2C2),
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                  fillColor: Colors.white,
                  filled: true,
                  suffixIcon: TextButton(
                    onPressed: () async {
                      if (_commentController.text.isNotEmpty) {
                        hideKeyboard(context);
                        await provider.postComment(
                            context: context,
                            postId: post.id ?? 0,
                            comment: _commentController.text);
                        _commentController.clear();
                      }
                    },
                    child: Text(
                      'Post',
                      style: tsS14w500F03AD9E,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget commentSection(UpdatesProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(0, 30, 15, 0),
          child: Text(
            // 'Comments ($comcount)',
            'Comments ',
            style: GoogleFonts.poppins(
              fontSize: 17,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(height: 20),
        if (provider.commentsList != null &&
            provider.commentsList!.records!.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(12)),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const BouncingScrollPhysics(),
              itemCount: provider.commentsList?.records?.length ?? 0,
              separatorBuilder: (context, index) {
                return const SizedBox(
                  height: 08,
                );
              },
              itemBuilder: (context, index) {
                final data = provider.commentsList?.records?[index];

                // DateTime date = DateTime.parse(
                //     data!.createdAt.toString());

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              GestureDetector(
                                onTap: () {
                                  if (data.profilePhoto != null) {
                                    PageNavigator.push(
                                      context: context,
                                      route: PhotoViewScreen(
                                        image: data.profilePhoto.toString(),
                                        extension: "jpeg",
                                      ),
                                    );
                                  }
                                },
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(50),
                                  child: CachedNetworkImage(
                                    height: 50,
                                    width: 50,
                                    fit: BoxFit.cover,
                                    imageUrl: data!.profilePhoto.toString(),
                                    errorWidget: (context, url, error) {
                                      return Container(
                                        height: 50,
                                        width: 50,
                                        decoration: BoxDecoration(
                                          color: ThemeColors.primaryColor,
                                          shape: BoxShape.circle,
                                        ),
                                        alignment: Alignment.center,
                                        child: Text(
                                          data.name!
                                              .substring(0, 1)
                                              .toUpperCase(),
                                          style: GoogleFonts.poppins(
                                            color: Colors.white,
                                            fontSize: 16,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                              const SizedBox(width: 15),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      data.name ?? '',
                                      style: GoogleFonts.rubik(
                                        color: const Color(0xFF1E2138),
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 2,
                                    ),
                                    Row(
                                      children: [
                                        if (data.designation != null)
                                          Text(
                                            data.designation!.length > 18
                                                ? data.designation
                                                    .toString()
                                                    .substring(0, 18)
                                                : data.designation.toString(),
                                            style: tsS12w400979797,
                                          ),
                                        Container(
                                          margin: const EdgeInsets.only(
                                            left: 6.0,
                                          ),
                                          width: 3.5 * w,
                                          height: 3.5 * h,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: ThemeColors.color979797,
                                          ),
                                        ),
                                        Expanded(
                                          child: Padding(
                                            padding: const EdgeInsets.only(
                                              left: 6.0,
                                            ),
                                            child: Text(
                                              data.createdAt.toString(),
                                              style: GoogleFonts.rubik(
                                                fontWeight: FontWeight.w400,
                                                fontSize: 12,
                                                color: ThemeColors.titleColor,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (data.isOwner == true)
                          PopupMenuButton(
                            icon: const Icon(
                              Icons.more_vert,
                            ),
                            onSelected: (value) {
                              if (value == 'delete') {
                                showDialog(
                                    context: context,
                                    builder: (ctxt) {
                                      return AlertDialog(
                                        title: const Text('Confirm delete'),
                                        content: const Text(
                                            'Are you sure you want to delete?'),
                                        actions: [
                                          ElevatedButton(
                                            onPressed: () =>
                                                Navigator.pop(context),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.grey[300],
                                            ),
                                            child: const Text(
                                              'Cancel',
                                              style: TextStyle(
                                                  color: Colors.black),
                                            ),
                                          ),
                                          ElevatedButton(
                                            onPressed: () async {
                                              Navigator.pop(context);
                                              provider.deleteComment(
                                                commentId: data.id.toString(),
                                                postId: post.id ?? 0,
                                                context: ctxt,
                                              );

                                              //comment delete function
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  ThemeColors.secondaryColor,
                                            ),
                                            child: const Text('Delete'),
                                          ),
                                        ],
                                      );
                                    });
                              }
                            },
                            itemBuilder: (context) {
                              return [
                                const PopupMenuItem(
                                  value: 'delete',
                                  child: Text('Delete comment'),
                                ),
                              ];
                            },
                          )
                      ],
                    ),
                    const SizedBox(height: 13),
                    Padding(
                      padding: const EdgeInsets.only(left: 69),
                      child: Container(
                        width: 260 * w,
                        // height: 40 * h,
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                            color: ThemeColors.colorF8F8F8,
                            borderRadius: BorderRadius.circular(6)),
                        child: Align(
                          alignment: Alignment.topLeft,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                data.description ?? "comment",
                                style: GoogleFonts.rubik(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 14,
                                  color: const Color(0xFF1E2138),
                                ),
                              ),
                              const Divider(),
                              InkWell(
                                onTap: () async {
                                  await provider.likeComment(
                                      commentId: data.id.toString(),
                                      postId: post.id.toString(),
                                      context: context,
                                      like: data.userLiked == true
                                          ? false
                                          : true);
                                },
                                child: Row(
                                  children: [
                                    Icon(
                                      data.userLiked == true
                                          ? Icons.favorite
                                          : Icons.favorite_outline_rounded,
                                      size: 15,
                                      color: data.userLiked == true
                                          ? Colors.red
                                          : Colors.black,
                                    ),
                                    const SizedBox(width: 5),
                                    Text(
                                      '${data.like ?? 0} ${data.like == 1 ? "Like" : "Likes"}',
                                      style: GoogleFonts.rubik(
                                        fontSize: 12,
                                        color: const Color(0xFF1E2138),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
      ],
    );
  }
}
