import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/view/latest_updates/update_detailed_screen.dart';

import 'package:provider/provider.dart';

class UpdateCard extends StatelessWidget {
  final String? isLiked;
  final String? name;
  final String? content;
  final int? likeCount;
  final int? commentCount;
  final String? date;
  final String? profileImage;
  final int? postId;
  final BuildContext context2;
  final bool isOwner;
  const UpdateCard({
    super.key,
    required this.isLiked,
    required this.postId,
    required this.profileImage,
    required this.name,
    required this.content,
    required this.likeCount,
    required this.commentCount,
    required this.date,
    required this.context2,
    required this.isOwner,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<UpdatesProvider>(
      builder: (context, provider, _) {
        return InkWell(
          onTap: () => onTap(context),
          child: Card(
            margin: const EdgeInsets.only(bottom: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(50),
                            child: CachedNetworkImage(
                              height: 50,
                              width: 50,
                              fit: BoxFit.fill,
                              imageUrl: profileImage.toString(),
                              errorWidget: (context, url, error) {
                                return Container(
                                  height: 50,
                                  width: 50,
                                  decoration: BoxDecoration(
                                    color: ThemeColors.primaryColor,
                                    shape: BoxShape.circle,
                                  ),
                                  alignment: Alignment.center,
                                  child: Text(
                                    name?.substring(0, 1).toUpperCase() ?? "",
                                    style: GoogleFonts.rubik(
                                        fontSize: 22,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.white),
                                  ),
                                );
                              },
                            ),
                          ),
                          const SizedBox(width: 15),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (name != null)
                                  Text(
                                    name.toString(),
                                    overflow: TextOverflow.ellipsis,
                                    style: GoogleFonts.rubik(
                                      color: const Color(0xFF1E2138),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                // if (designation != null)
                                //   Text(
                                //     designation.toString(),
                                //     style: GoogleFonts.rubik(
                                //       color: const Color(0xFF1E2138),
                                //       fontSize: 12,
                                //       fontWeight: FontWeight.w300,
                                //     ),
                                //   ),
                              ],
                            ),
                          ),
                          if (isOwner == true)
                            PopupMenuButton(
                              onSelected: (value) {
                                if (value == 'delete') {
                                  showDialog(
                                      context: context,
                                      builder: (ctxt) {
                                        return AlertDialog(
                                          title: const Text('Confirm delete'),
                                          content: const Text(
                                              'Are you sure you want to delete?'),
                                          actions: [
                                            ElevatedButton(
                                              onPressed: () =>
                                                  Navigator.pop(context),
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor:
                                                    Colors.grey[300],
                                              ),
                                              child: const Text(
                                                'Cancel',
                                                style: TextStyle(
                                                    color: Colors.black),
                                              ),
                                            ),
                                            Consumer<UpdatesProvider>(
                                              builder:
                                                  (context, provider, child) {
                                                return ElevatedButton(
                                                  onPressed: () {
                                                    provider.deleteUpdate(
                                                        postId:
                                                            postId.toString(),
                                                        context: context);
                                                  },
                                                  style:
                                                      ElevatedButton.styleFrom(
                                                    backgroundColor: ThemeColors
                                                        .secondaryColor,
                                                  ),
                                                  child: const Text('Delete'),
                                                );
                                              },
                                            )
                                          ],
                                        );
                                      });
                                }
                              },
                              itemBuilder: (context) {
                                return [
                                  const PopupMenuItem(
                                    value: 'edit',
                                    child: Text('Edit'),
                                  ),
                                  const PopupMenuItem(
                                    value: 'delete',
                                    child: Text('Delete'),
                                  ),
                                ];
                              },
                            )
                        ],
                      ),
                      const SizedBox(height: 15),
                      if (content != null)
                        Text(
                          content.toString(),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 5,
                        ),
                    ],
                  ),
                ),
                const Spacer(),
                const Divider(
                  color: Color(0xFFFFD700),
                  thickness: 1,
                  height: 0,
                ),
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 15),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Consumer<UpdatesProvider>(
                        builder: (context, provider, child) {
                          return InkWell(
                            onTap: () {
                              provider.likePost(
                                  postId: postId.toString(),
                                  context: context,
                                  like: isLiked == "true" ? false : true);
                              provider.getUpdates(context: context);
                            },
                            child: Row(
                              children: [
                                Icon(
                                  isLiked == "true"
                                      ? Icons.favorite
                                      : Icons.favorite_outline_rounded,
                                  size: 15,
                                  color: isLiked == "true"
                                      ? Colors.red
                                      : Colors.black,
                                ),
                                const SizedBox(width: 5),
                                Text(
                                  '${likeCount ?? 0} Likes',
                                  style: GoogleFonts.rubik(
                                    fontSize: 12,
                                    color: const Color(0xFF1E2138),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                      const Spacer(),
                      const ImageIcon(
                        AssetImage('assets/icons/comments.png'),
                        size: 15,
                      ),
                      const SizedBox(width: 5),
                      Text(
                        '${commentCount ?? 0} Comments',
                        style: GoogleFonts.rubik(
                          fontSize: 12,
                          color: const Color(0xFF1E2138),
                        ),
                      ),
                      const Spacer(),
                      Text(
                        date.toString(),
                        style: GoogleFonts.rubik(
                          fontSize: 12,
                          color: const Color(0xFF1E2138),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void onTap(BuildContext context) async {
    var provider = context.read<UpdatesProvider>();
    await provider.getComments(postId: postId!.toInt());
    await provider.getPostDetails(postId: postId!.toInt());
    if (provider.postList.isNotEmpty && context.mounted) {
      Navigator.of(context2).push(
        MaterialPageRoute(
          builder: (context) => UpdateDetailedScreen(
            color: Colors.white,
            post: provider.postList.first,
          ),
        ),
      );
    }
  }
}
