import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/model/leave_pending_model.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/view/leave_request_for_reporting_person/leave_request_overview.dart';
import 'package:e8_hr_portal/view/leave_request_for_reporting_person/widget/leave_accept_reason_dialogebox.dart';
import 'package:e8_hr_portal/view/leave_request_for_reporting_person/widget/leave_reject_reason_dialogbox.dart';
import 'package:provider/provider.dart';

class LeaveRequestedTilesCard extends StatelessWidget {
  final Data? data;
  const LeaveRequestedTilesCard({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    String? status = data!.leaveDetails?.status;
    List<String> statusList = ["cancelled", "rejected", "approved", "expired"];
    if (!statusList.contains(status?.toLowerCase())) {
      return Slidable(
        key: ValueKey(data?.leaveDetails?.id),
        endActionPane: ActionPane(
          openThreshold: 0.0001,
          extentRatio: 0.3,
          motion: const ScrollMotion(),
          children: (data?.leaveDetails?.leavePermission == "allow" &&
                  (status?.toLowerCase() == "pending" ||
                      status?.toLowerCase() == "in-progress"))
              ? [
                  GestureDetector(
                    onTap: () =>
                        _onLeaveAccepted(data?.leaveDetails?.id, context),
                    child: Container(
                      margin: EdgeInsets.only(left: w * 22),
                      width: 25,
                      child: Image.asset(
                        "assets/icons/check_mark.png",
                        height: 50,
                        width: 50,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () =>
                        _onLeaveRejected(data?.leaveDetails?.id, context),
                    child: Container(
                      margin: EdgeInsets.only(left: w * 20),
                      width: 20,
                      child: Image.asset(
                        "assets/icons/cross_mark.png",
                        height: 50,
                        width: 50,
                      ),
                    ),
                  ),
                ]
              : [],
        ),
        child: _leaveRequestCard(request: data!, context: context),
      );
    }
    return _leaveRequestCard(request: data!, context: context);
  }

  _onLeaveAccepted(int? requestId, BuildContext context) async {
    EasyLoading.show();

    if (requestId != null) {
      showDialog(
        context: context,
        builder: (context) {
          return LeaveAcceptReasonDialog(
            leaveId: requestId,
          );
        },
      );
    }
    EasyLoading.dismiss();
  }

  _onLeaveRejected(int? requestId, BuildContext context) async {
    EasyLoading.show();

    if (requestId != null) {
      showDialog(
        context: context,
        builder: (context) {
          return LeaveRectionReasonDialog(
            leaveId: requestId,
          );
        },
      );
    }
    //    final leaveProvider =
    // Provider.of<LeaveApplicationProvider>(context, listen: false);
    // await leaveProvider.leaveAcceptOrReject(
    //     leaveId: requestId, acceptOrReject: 2, context: context);
    // await leaveProvider.getLeaveRequest();
    // await leaveProvider.getLeaveRequestReporting();
    EasyLoading.dismiss();
  }

  Widget _leaveRequestCard(
      {required Data request, required BuildContext context}) {
    String? fromDate;
    String? toDate;
    String? createdAt;
    if (request.leaveDetails?.createdAt != null) {
      // createdAt = formatDateFromString(
      //     request.createdAt!, "yyyy-MM-ddThh:mm:ss", "MM EEE, yy");
      createdAt = request.leaveDetails?.createdAt;
    }
    if (request.leaveDetails?.startDate != null) {
      // DateTime dt = DateTime.parse(request.startDate.toString());
      // final DateFormat formatter = DateFormat('dd MMM yyyy');
      fromDate = request.leaveDetails?.startDate;
    }
    if (request.leaveDetails?.endDate != null) {
      // DateTime dt = DateTime.parse(request.endDate.toString());
      // final DateFormat formatter = DateFormat('dd MMM yyyy');
      toDate = request.leaveDetails?.endDate;
    }
    List<ReportingPersonList>? reportingPersonList =
        request.reportingPersonList;
    HrStatus? hrStatus = request.hrStatus;

    return GestureDetector(
      onTap: () async {
        final provider =
            Provider.of<LeaveApplicationProvider>(context, listen: false);

        if (request.leaveDetails?.id != null) {
          await EasyLoading.show();
          bool isGo = await provider.getRequestedLeaveOverviewForReportedPerson(
              leaveId: request.leaveDetails!.id!);

          await EasyLoading.dismiss();
          if (isGo) {
// ignore: use_build_context_synchronously
            PageNavigator.pushSlideRight(
                context: context, route: const LeaveRequestOverView());
          }
        }

        // getRequestedLeaveOverview
      },
      child: Container(
        width: w * 343,
        decoration: BoxDecoration(
          color: Colors.white,
          // boxShadow: const [
          //   BoxShadow(
          //     blurRadius: 10,
          //     color: Color.fromRGBO(0, 0, 0, 0.16),
          //     offset: Offset(2, 2),
          //   ),
          // ],
          borderRadius: BorderRadius.circular(8),
        ),
        // margin: EdgeInsets.symmetric(horizontal: w * 16),
        padding: const EdgeInsets.all(10.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Row(
                children: [
                  Container(
                    height: h * 45,
                    width: w * 45,
                    margin: EdgeInsets.only(right: w * 10),
                    decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        shape: BoxShape.circle,
                        image: request.leaveDetails?.userDetails?.profilePic
                                    .toString() !=
                                null
                            ? DecorationImage(
                                image: NetworkImage(
                                    "${request.leaveDetails?.userDetails?.profilePic.toString()}"),
                                fit: BoxFit.cover,
                              )
                            : null),
                    alignment: Alignment.center,
                    child: request.leaveDetails?.userDetails?.profilePic == null
                        ? Text(
                            '${request.leaveDetails?.userDetails?.name?.substring(0, 1).toUpperCase()}',
                            style: GoogleFonts.rubik(
                                fontSize: 22,
                                fontWeight: FontWeight.normal,
                                color: Colors.white),
                          )
                        : null,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${request.leaveDetails?.userDetails?.name} ",
                          style: tsS14w500c181818,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 3),
                              child: Image.asset(
                                  "assets/icons/calendar_green.png",
                                  height: h * 10,
                                  width: w * 10),
                            ),
                            SizedBox(width: w * 4),
                            Expanded(
                              child: Text(
                                '$fromDate - $toDate • ${request.leaveDetails?.dayCount?.toString()} ${request.leaveDetails!.dayCount! <= 1 ? 'Day' : 'Days'}',
                                style: tsS12w400c979797,
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(width: w * 4.52),
                        Row(
                          children: [
                            Image.asset("assets/icons/calendar-remove.png",
                                height: h * 10, width: w * 10),
                            SizedBox(width: w * 4),
                            Text(
                              '${request.leaveDetails?.leaveType}',
                              style: tsS12w400c979797,
                            ),
                          ],
                        ),
                        SizedBox(height: h * 4.52),
                        Row(
                          children: [
                            if (reportingPersonList != null)
                              _reportingPersonsWidget(
                                  data: reportingPersonList),
                            if (hrStatus != null)
                              _hrApprovalWidget(
                                  profilePic: hrStatus.profilePic ?? "",
                                  name: hrStatus.name ?? "",
                                  isApprove: hrStatus.isApprove ?? ""),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            if (request.leaveDetails?.status != null)
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  _statusWidget(
                    status: request.leaveDetails?.status ?? "",
                  ),
                  SizedBox(height: h * 5),
                  Text(
                    createdAt ?? "12 Wed, 22",
                    style: tsS12w400979797,
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _hrApprovalWidget(
      {required String profilePic,
      required String name,
      required String isApprove}) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: ClipRRect(
              borderRadius: BorderRadius.circular(5.0),
              child: ClipOval(
                child: Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: profilePic.toString(),
                      width: 30 * w,
                      height: 30 * h,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) {
                        return Container(
                          height: 30,
                          width: 30,
                          decoration: BoxDecoration(
                            color: ThemeColors.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            name.substring(0, 1).toUpperCase(),
                            style: GoogleFonts.rubik(
                                fontSize: 22,
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                          ),
                        );
                      },
                    ),
                    if (isApprove.toLowerCase().trim() == "pending")
                      Container(
                        height: 30 * h,
                        width: 30 * w,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                  ],
                ),
              )),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: Column(
            children: [
              if (isApprove.toLowerCase().trim() == "pending") const SizedBox(),
              if (isApprove.toLowerCase().trim() == "true")
                ImageIcon(
                  const AssetImage(
                    "assets/icons/tick.png",
                  ),
                  size: 13,
                  color: ThemeColors.color06AA37,
                ),
              if (isApprove.toLowerCase().trim() == "false")
                ImageIcon(
                  const AssetImage(
                    "assets/icons/close_red.png",
                  ),
                  size: 13,
                  color: ThemeColors.colorB80000,
                ),
            ],
          ),
        )
      ],
    );
  }

  Widget _reportingPersonsWidget({required List<ReportingPersonList>? data}) {
    return Row(
      children: data!.map((e) {
        return Stack(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(5.0),
                  child: ClipOval(
                    child: Stack(
                      children: [
                        CachedNetworkImage(
                          imageUrl: e.profilePic.toString(),
                          width: 30 * w,
                          height: 30 * h,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) {
                            return Container(
                              height: 30,
                              width: 30,
                              decoration: BoxDecoration(
                                color: ThemeColors.primaryColor,
                                shape: BoxShape.circle,
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                e.name?.substring(0, 1).toUpperCase() ?? "",
                                style: GoogleFonts.rubik(
                                    fontSize: 22,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.white),
                              ),
                            );
                          },
                        ),
                        if (e.isApprove?.toLowerCase().trim() == "pending")
                          Container(
                            height: 30 * h,
                            width: 30 * w,
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                      ],
                    ),
                  )),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              child: Column(
                children: [
                  if (e.isApprove?.toLowerCase().trim() == "pending")
                    const SizedBox(),
                  if (e.isApprove?.toLowerCase().trim() == "true")
                    ImageIcon(
                      const AssetImage(
                        "assets/icons/tick.png",
                      ),
                      size: 13,
                      color: ThemeColors.color06AA37,
                    ),
                  if (e.isApprove?.toLowerCase().trim() == "false")
                    ImageIcon(
                      const AssetImage(
                        "assets/icons/close_red.png",
                      ),
                      size: 13,
                      color: ThemeColors.colorB80000,
                    ),
                ],
              ),
            )
          ],
        );
      }).toList(),
    );
  }

  Widget _filterDropDownButton() {
    return Container(
      padding: const EdgeInsets.fromLTRB(10, 5, 5, 5),
      width: 120 * w,
      height: 30 * h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
      ),
      child: DropdownButtonHideUnderline(
        child: Consumer<LeaveApplicationProvider>(
          builder: (context, provider, _) {
            return DropdownButton<LeaveRequestFilterModel>(
              borderRadius: BorderRadius.circular(6),
              icon: Icon(Icons.keyboard_arrow_down, size: w * 18),
              dropdownColor: Colors.white,
              isDense: true,
              value: provider.selectedFilter,
              onChanged: (value) {
                provider.selectedFilter = value;
                provider.refreshLeaveRequest();
              },
              items: provider.leaveFilterModel.map((item) {
                return DropdownMenuItem(
                  value: item,
                  child: Text(
                    item.text,
                    style: tsS12w400c495057,
                  ),
                );
              }).toList(),
            );
          },
        ),
      ),
    );
  }

  Widget _statusWidget({required String status}) {
    TextStyle? style;
    Color? color;
    switch (status) {
      case "Approved":
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
        break;
      case "Rejected":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case "Pending":
        {
          style = tsS12w600cE5B900;
          color = ThemeColors.colorFFF2E2;
        }
        break;
      case "In-progress":
        {
          style = tsS12W6FE5B900;
          color = ThemeColors.colorFFF2E2;
        }
        break;
      case "Cancelled":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case "Expired":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case "Holiday":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      default:
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      height: h * 23,
      // width: w * 74,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: color,
      ),
      child: Text(
        status,
        style: style,
      ),
    );
  }
}
