// ignore_for_file: use_build_context_synchronously

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../util/colors.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../leave applications/provider/leave_apllication_provider.dart';
import '../../widgets/general_button.dart';
import '../../widgets/type_ahead_form_field_widget.dart';

class LeaveForwardBottomsheet extends StatefulWidget {
  final int leaveId;
  const LeaveForwardBottomsheet({super.key, required this.leaveId});

  @override
  State<LeaveForwardBottomsheet> createState() =>
      _LeaveForwardBottomsheetState();
}

class _LeaveForwardBottomsheetState extends State<LeaveForwardBottomsheet> {
  late LeaveApplicationProvider _provider;
  final TextEditingController _staffInChargeController =
      TextEditingController();
  final _formkey = GlobalKey<FormState>();
  List<String> checkStaff = [];
  @override
  void initState() {
    super.initState();
    _provider = context.read<LeaveApplicationProvider>();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _provider.selectedUserID = null;
      _provider.getStaffinCharge();
    });
  }

  @override
  void dispose() {
    super.dispose();
    _staffInChargeController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formkey,
      child: Padding(
        padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 16,
            right: 16),
        child: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Consumer<LeaveApplicationProvider>(
                builder: (context, provider, _) {
                  if (provider.isReportingPersonLoading) {
                    return SizedBox(
                        height: 202,
                        child:
                            const Center(child: CircularProgressIndicator()));
                  }
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 40,
                        height: 4,
                        margin: const EdgeInsets.symmetric(vertical: 10),
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Text(
                          'Forward Leave',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black, // Added color for clarity
                          ),
                        ),
                      ),
                      // Content Message
                      // const Padding(
                      //   padding:
                      //       EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      //   child: Text(
                      //     'Are you sure you want to forward this leave request for approval?',
                      //     textAlign: TextAlign.center,
                      //     style: TextStyle(
                      //       fontSize: 16,
                      //       color: Colors.grey, // Added color for clarity
                      //     ),
                      //   ),
                      // ),
                      const SizedBox(height: 20),
                      _typeAheadTextFiledField(hintText: 'Staff in charge'),
                      const SizedBox(height: 20),
                      // Action Buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Expanded(
                            child: GeneralButton(
                              title: 'Cancel',
                              height: h * 50,
                              width: double.infinity,
                              isDisabledColor: true,
                              textStyle: tsS18w500cFFFFFF,
                              onPressed: () => Navigator.pop(context),
                            ),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: GeneralButton(
                              title: 'Forward',
                              height: h * 50,
                              width: double.infinity,
                              textStyle: tsS18w500cFFFFFF,
                              onPressed: () async {
                                bool isSuccess = await provider.leaveForward(
                                    leaveID: widget.leaveId);
                                if (isSuccess) {
                                  if (!mounted) return;
                                  Navigator.pop(context);
                                  Navigator.pop(context);
                                  provider.refreshLeaveRequest();
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                          height: 20), // Add some space at the bottom
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _typeAheadTextFiledField({required String hintText}) {
    return Consumer<LeaveApplicationProvider>(
      builder: (context, provider, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TypeAheadFormFieldWidget(
              controller: _staffInChargeController,
              hintText: hintText,
              suffixIcon: provider.selectedUserID != null
                  ? IconButton(
                      onPressed: () {
                        _staffInChargeController.clear();
                        provider.selectedUserID = null;
                      },
                      icon: Icon(
                        Icons.close,
                        color: ThemeColors.color000000,
                      ),
                    )
                  : Icon(
                      Icons.close,
                      color: ThemeColors.color000000,
                    ),
              itemBuilder: (context, suggestion) {
                checkStaff
                    .add('${suggestion.firstName} ${suggestion.lastName}');

                return ListTile(
                  title: Text('${suggestion.firstName} ${suggestion.lastName}'),
                );
              },
              validator: (value) {
                if ((value == null ||
                        value.isEmpty ||
                        !checkStaff.contains(value)) &&
                    provider.selectedUserID == null) {
                  return 'Select staff in charge';
                }
                return null;
              },
              onSelected: (suggestion) {
                _staffInChargeController.text =
                    '${suggestion.firstName} ${suggestion.lastName}';
                provider.selectedUserID = suggestion.id;
                log('_staffInChargeController.text  == ${_staffInChargeController.text}');
                FocusManager.instance.primaryFocus?.unfocus();
              },
              suggestionsCallback: (pattern) {
                return provider.staffInChargeList!
                    .where((item) => '${item.firstName} ${item.lastName}'
                        .toLowerCase()
                        .startsWith(pattern.toLowerCase()))
                    .toList();
              },
            ),
          ],
        );
      },
    );
  }
}
