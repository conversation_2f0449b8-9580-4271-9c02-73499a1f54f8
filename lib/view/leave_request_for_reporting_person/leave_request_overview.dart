import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/view/leave_request_for_reporting_person/widget/leave_forward_bottomsheet.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/model/leave_overview_for_reported_person_model.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/employee_relations/widget/photo_view.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/view/leave_request_for_reporting_person/widget/leave_accept_reason_dialogebox.dart';
import 'package:e8_hr_portal/view/leave_request_for_reporting_person/widget/leave_reject_reason_dialogbox.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';
import '../../util/dailoge.dart';
import '../../util/page_navigator.dart';
import '../../util/styles.dart';
import 'employee_leave_history.dart';

class LeaveRequestOverView extends StatelessWidget {
  const LeaveRequestOverView({super.key});

  @override
  Widget build(BuildContext context) {
    final LeaveApplicationProvider provider =
        Provider.of<LeaveApplicationProvider>(context, listen: false);
    LeaveDetails? leaveDetails =
        provider.leaveOverviewForReportingPerson?.data?.leaveDetails;
    UserDetails? staffIncharge =
        provider.leaveOverviewForReportingPerson?.data?.staffIncharge;
    List<ReportingPersonList>? reportingPersonList =
        provider.leaveOverviewForReportingPerson?.data?.reportingPersonList;

    UserDetails? userDetails = leaveDetails?.userDetails;

    String? profilePic = userDetails?.profilePic;
    String? name = userDetails?.name;
    int? leaveId = leaveDetails?.id;
    String? startDate = leaveDetails?.startDate;
    String? endDate = leaveDetails?.endDate;
    num? dayCount = leaveDetails?.dayCount;
    String? leavePermission = leaveDetails?.leavePermission;
    String? status = leaveDetails?.status;
    String? createdAt = leaveDetails?.createdAt;
    String? reason = leaveDetails?.reason;
    String? staffName = staffIncharge?.name;
    String? leaveDoc = leaveDetails?.leaveDoc;
    int? employeeId = userDetails?.id;
    String? fromDate;
    String? toDate;
    String? createdDate;
    HrStatus? hrStatus =
        provider.leaveOverviewForReportingPerson?.data?.hrStatus;

    if (startDate != null) {
      fromDate = startDate;
    }
    if (endDate != null) {
      toDate = endDate;
    }
    if (createdAt != null) {
      createdDate = createdAt;
    }
    ImageProvider? imageProvider;

    if (leaveDoc != null && leaveDoc.isNotEmpty) {
      imageProvider = Image.network(leaveDoc).image;
    }

    return HisenseScaffold(
      screenTitle: 'Overview',
      onTap: () {
        provider.getPendingLeaves();
      },
      actions: [
        _requestNewButton(context: context, employeeId: employeeId ?? 0)
      ],
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
            child: Consumer<LeaveApplicationProvider>(
              builder: (context, provider, _) {
                return Column(
                  children: [
                    Expanded(
                      child: ListView(
                        shrinkWrap: true,
                        // crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: h * 32),
                          Text('Request Overview', style: tsS18w500c181818),
                          SizedBox(height: h * 15),
                          Container(
                            width: w * 343,
                            padding: EdgeInsets.fromLTRB(
                                w * 10, h * 15, w * 11, h * 11),
                            decoration: BoxDecoration(
                              color: ThemeColors.colorFFFFFF,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Row(
                                        children: [
                                          Container(
                                            height: h * 45,
                                            width: w * 45,
                                            margin:
                                                EdgeInsets.only(right: w * 10),
                                            decoration: BoxDecoration(
                                                color: ThemeColors.primaryColor,
                                                shape: BoxShape.circle,
                                                image: profilePic != null
                                                    ? DecorationImage(
                                                        image: NetworkImage(
                                                            profilePic),
                                                        fit: BoxFit.cover,
                                                      )
                                                    : null),
                                            alignment: Alignment.center,
                                            child: profilePic == null
                                                ? Text(
                                                    '${name?.substring(0, 1).toUpperCase()}',
                                                    style: GoogleFonts.rubik(
                                                        fontSize: 22,
                                                        fontWeight:
                                                            FontWeight.normal,
                                                        color: Colors.white),
                                                  )
                                                : null,
                                          ),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                    // '${request.userDetails?.name} ',
                                                    name ?? '',
                                                    style: tsS14w500c181818),
                                                SizedBox(height: h * 7),
                                                Row(
                                                  children: [
                                                    Image.asset(
                                                        'assets/icons/calendar_green.png',
                                                        height: h * 10,
                                                        width: w * 10),
                                                    SizedBox(width: w * 4),
                                                    Expanded(
                                                      child: Text(
                                                        fromDate != null &&
                                                                toDate != null
                                                            ? '$fromDate - $toDate • $dayCount ${dayCount! <= 1 ? 'Day' : 'Days'}'
                                                            : '',
                                                        style: tsS12w400c979797,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(width: w * 4.52),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    if (status != null)
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          _statusWidget(status: status),
                                          SizedBox(height: h * 5),
                                          Text(createdDate ?? '',
                                              style: tsS12w400979797),
                                        ],
                                      ),
                                  ],
                                ),
                                SizedBox(height: h * 19),
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text('Leave Type',
                                      style: tsS12w400979797),
                                ),
                                const SizedBox(height: 5),
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text(leaveDetails?.leaveType ?? '',
                                      style: tsS14w500Black),
                                ),
                                Divider(
                                    color: ThemeColors.colorD9D9D9,
                                    thickness: 1),
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text('Reason', style: tsS12w400979797),
                                ),
                                const SizedBox(
                                  height: 5,
                                ),
                                Align(
                                  alignment: Alignment.topLeft,
                                  child:
                                      Text(reason ?? '', style: tsS14w500Black),
                                ),
                                // _details(subtitle: 'Reason', title: reason ?? ''),
                                if (leaveDoc != null)
                                  Divider(
                                      color: ThemeColors.colorD9D9D9,
                                      thickness: 1),
                                if (leaveDoc != null)
                                  _details(
                                    subtitle: 'Document',
                                    title: 'Document',
                                    textButton: InkWell(
                                      onTap: () {
                                        if (imageProvider != null) {
                                          PageNavigator.push(
                                            context: context,
                                            route: PhotoViewScreen(
                                              image: leaveDoc,
                                              extension:
                                                  leaveDetails?.docExtension,
                                            ),
                                          );

                                          // showImageViewer(
                                          //   context, imageProvider,
                                          //   useSafeArea: true,
                                          //   backgroundColor: ThemeColors.color000000,
                                          //   closeButtonColor: ThemeColors.errorColor,
                                          //   // closeButtonTooltip: '',
                                          //   immersive: true,
                                          //   swipeDismissible: true,
                                          //   doubleTapZoomable: true,
                                          //   //     onViewerDismissed: () {
                                          //   //   print('dismissed');
                                          //   // }
                                          // );
                                        } else {
                                          showToastText('No image found');
                                        }
                                      },
                                      child: Text(
                                        'View',
                                        style: GoogleFonts.poppins(
                                          color: ThemeColors.colorFF0000,
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ),
                                  ),
                                Divider(
                                    color: ThemeColors.colorD9D9D9,
                                    thickness: 1),
                                _details(
                                    subtitle: 'Staff in charge on absence',
                                    title: staffName ?? ''),
                                Divider(
                                    color: ThemeColors.colorD9D9D9,
                                    thickness: 1),
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text('Approved by / Rejected by',
                                      style: tsS12w400979797),
                                ),
                                const SizedBox(height: 13),
                                Row(
                                  children: [
                                    if (reportingPersonList != null)
                                      _reportingPersonsWidget(
                                          data: reportingPersonList),
                                    if (hrStatus != null)
                                      _hrApprovalWidget(
                                          profilePic: hrStatus.profilePic ?? '',
                                          name: hrStatus.name ?? '',
                                          isApprove: hrStatus.isApprove ?? ''),
                                  ],
                                ),
                              ],
                            ),
                          ),

                          SizedBox(height: 20 * h),
                          // if (text.toLowerCase() == 'rejected')
                          _hrRejectStatusBuilder(hrStatus),

                          SizedBox(height: 20 * h),
                          _rejectStatusBuilder(reportingPersonList),
                          SizedBox(height: 70 * h),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
          if (leavePermission == 'allow' &&
              (status == 'Pending' || status == 'In-progress'))
            Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: EdgeInsets.only(
                    left: 16.0 * w, right: 16 * w, bottom: 20 * h),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: GeneralButton(
                            title: 'Reject',
                            height: h * 50,
                            width: w * 164,
                            // isDisabled: true,
                            textStyle: tsS18w500cFFFFFF,
                            isDisabledColor: true,
                            onPressed: () {
                              if (leaveId != null) {
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    return LeaveRectionReasonDialog(
                                      leaveId: leaveId,
                                    );
                                  },
                                );
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                            child: OutlinedButton(
                          onPressed: () {
                            if (leaveId != null) {
                              _onForwardLeave(
                                  context: context, leaveID: leaveId);
                            }
                          },
                          style: OutlinedButton.styleFrom(
                            foregroundColor: ThemeColors.colorFFF2E2,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(50)),
                            minimumSize: Size(w * 164, h * 50),
                            side: BorderSide(
                                color: ThemeColors.primaryColor,
                                width: 2), // Customize border color and width
                            // You can add more styling here, e.g., padding, shape, etc.
                          ),
                          child: Text('Forward Leave', style: tsS18w500cE5B900),
                        )),
                      ],
                    ),
                    SizedBox(height: 15),
                    Consumer<LeaveApplicationProvider>(
                      builder: (context, provider, _) {
                        if (provider.isLoading) {
                          return const Center(
                            child: CircularProgressIndicator.adaptive(),
                          );
                        }
                        return GeneralButton(
                          title: 'Approve',
                          height: h * 50,
                          width: double.infinity,
                          textStyle: tsS18w500cFFFFFF,
                          onPressed: () {
                            if (leaveId != null) {
                              showDialog(
                                context: context,
                                builder: (context) {
                                  return LeaveAcceptReasonDialog(
                                    leaveId: leaveId,
                                  );
                                },
                              );
                            }
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _rejectStatusBuilder(List<ReportingPersonList>? reportingPersonList) {
    if (reportingPersonList != null) {
      return ListView.separated(
        itemCount: reportingPersonList.length,
        shrinkWrap: true,
        physics: const ScrollPhysics(),
        itemBuilder: (context, index) {
          ReportingPersonList? e = reportingPersonList[index];
          // e?.comment = 'akdfhsdkufh';
          // if ((e?.comment == null && e?.isApprove == 'true') ||
          //     data.hrStatus?.isApprove == 'True') {
          //   return const SizedBox();
          // }

          if (['false', 'rejected', 'true']
                  .contains(e.isApprove.toString().toLowerCase()) &&
              e.comment != null) {
            return _leaveRejectedCard(
              comment: e.comment ?? '',
              rejectedBy: e.name ?? '',
              rejectedDate: e.createdAt ?? '',
              appOrReject: e.isApprove ?? '',
            );
          }
          return const SizedBox();
        },
        separatorBuilder: (context, index) {
          return SizedBox(height: h * 10);
        },
        // children: data.reportingPersonList!.map((e) {
        //   return _leaveRejectedCard(
        //       comment: e.comment ?? '', rejectedBy: e.name ?? '');
        // }).toList(),
      );
    }
    return const SizedBox();
  }

  Widget _hrRejectStatusBuilder(HrStatus? hrStatus) {
    if (['false', 'rejected', 'true'].contains(
          hrStatus?.isApprove.toString().toLowerCase(),
        ) &&
        hrStatus?.comment != null) {
      return _leaveRejectedCard(
        comment: hrStatus?.comment ?? '',
        rejectedBy: hrStatus?.name ?? '',
        rejectedDate: hrStatus?.createdAt ?? '',
        appOrReject: hrStatus?.isApprove ?? '',
      );
    }
    return const SizedBox();
  }

  Widget _leaveRejectedCard(
      {required String rejectedBy,
      required String comment,
      required String? rejectedDate,
      required String? appOrReject}) {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                height: 24,
                width: 24,
                margin: const EdgeInsets.only(right: 10),
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                    color: appOrReject?.toLowerCase() == 'false'
                        ? ThemeColors.colorFCD2D0
                        : ThemeColors.color06AA37.withOpacity(.2),
                    borderRadius: BorderRadius.circular(6)),
                child: Container(
                  decoration: BoxDecoration(
                      color: appOrReject?.toLowerCase() == 'false'
                          ? ThemeColors.colorF64D44
                          : ThemeColors.color06AA37,
                      shape: BoxShape.circle),
                  child: Center(
                    child: Icon(
                      appOrReject?.toLowerCase() == 'false'
                          ? Icons.close_rounded
                          : Icons.done_rounded,
                      color: Colors.white,
                      size: 10,
                    ),
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      appOrReject?.toLowerCase() == 'false'
                          ? 'Leave Rejected'
                          : 'Leave Approved',
                      style: appOrReject?.toLowerCase() == 'false'
                          ? tsS12w4cF64D44
                          : tsS12w4c06AA37,
                    ),
                  ),
                  SizedBox(
                    height: 15,
                    width: 280 * w,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: Text(
                            rejectedBy,
                            style: tsS10w400c646363,
                          ),
                        ),
                        SizedBox(
                          width: 10 * w,
                        ),
                        Expanded(
                          child: Text(
                            rejectedDate.toString(),
                            style: tsS10w400c646363,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          Divider(
            thickness: 1,
            color: ThemeColors.colorD9D9D9,
          ),
          const SizedBox(height: 12),
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              comment,
              style: tsS14w400979797,
            ),
          )
        ],
      ),
    );
  }

  Widget _details(
      {required String subtitle, required String title, Widget? textButton}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(subtitle, style: tsS12w400c949494),
        SizedBox(height: h * 3),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title, style: tsS14w500c2C2D33),
            textButton ?? const SizedBox()
          ],
        ),
        SizedBox(height: h * 8),
      ],
    );
  }

  Widget _statusWidget({required String status}) {
    TextStyle? style;
    Color? color;
    switch (status) {
      case 'Approved':
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
        break;
      case 'Rejected':
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case 'Expired':
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case 'Holiday':
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case 'Pending':
        {
          style = tsS12w600cE5B900;
          color = ThemeColors.colorFFF2E2;
        }
        break;
      case 'In-progress':
        {
          style = tsS12W6FE5B900;
          color = ThemeColors.colorFFF2E2;
        }
        break;
      case 'Cancelled':
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      default:
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
    }
    return Container(
      height: h * 23,
      // width: w * 74,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: color,
      ),
      child: Text(
        status,
        style: style,
      ),
    );
  }

  Widget _requestNewButton(
      {required BuildContext context, required int employeeId}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: h * 15, horizontal: w * 10),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        radius: 10,
        onTap: () async {
          PageNavigator.push(
            context: context,
            route: EmployeeLeavehistory(employeeID: employeeId),
          );
        },
        child: Padding(
          padding: EdgeInsets.only(top: h * 7, left: w * 10, right: w * 10),
          child: Text('Leave History', style: tsS14w500cFFFFFF),
        ),
      ),
    );
  }

  Widget _hrApprovalWidget(
      {required String profilePic,
      required String name,
      required String isApprove}) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: ClipRRect(
              borderRadius: BorderRadius.circular(5.0),
              child: ClipOval(
                child: Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: profilePic.toString(),
                      width: 45 * w,
                      height: 45 * h,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) {
                        return Container(
                          height: 50,
                          width: 50,
                          decoration: BoxDecoration(
                            color: ThemeColors.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            name.substring(0, 1).toUpperCase(),
                            style: GoogleFonts.rubik(
                                fontSize: 22,
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                          ),
                        );
                      },
                    ),
                    if (isApprove.toLowerCase().trim() == 'pending')
                      Container(
                        height: 45 * h,
                        width: 45 * w,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                  ],
                ),
              )),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: Column(
            children: [
              if (isApprove.toLowerCase().trim() == 'pending') const SizedBox(),
              if (isApprove.toLowerCase().trim() == 'true')
                ImageIcon(
                  const AssetImage(
                    'assets/icons/tick.png',
                  ),
                  size: 13,
                  color: ThemeColors.color06AA37,
                ),
              if (isApprove.toLowerCase().trim() == 'false')
                ImageIcon(
                  const AssetImage(
                    'assets/icons/close_red.png',
                  ),
                  size: 13,
                  color: ThemeColors.colorB80000,
                ),
            ],
          ),
        )
      ],
    );
  }

  Widget _reportingPersonsWidget({required List<ReportingPersonList>? data}) {
    return Row(
      children: data!.map(
        (e) {
          return Stack(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(5.0),
                    child: ClipOval(
                      child: Stack(
                        children: [
                          CachedNetworkImage(
                            imageUrl: e.profilePic.toString(),
                            width: 45 * w,
                            height: 45 * h,
                            fit: BoxFit.cover,
                            errorWidget: (context, url, error) {
                              return Container(
                                height: 50,
                                width: 50,
                                decoration: BoxDecoration(
                                  color: ThemeColors.primaryColor,
                                  shape: BoxShape.circle,
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  e.name?.substring(0, 1).toUpperCase() ?? '',
                                  style: GoogleFonts.rubik(
                                      fontSize: 22,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white),
                                ),
                              );
                            },
                          ),
                          if (e.isApprove?.toLowerCase().trim() == 'pending')
                            Container(
                              height: 45 * h,
                              width: 45 * w,
                              color: Colors.white.withValues(alpha: 0.8),
                            ),
                        ],
                      ),
                    )),
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Column(
                  children: [
                    if (e.isApprove?.toLowerCase().trim() == 'pending')
                      const SizedBox(),
                    if (e.isApprove?.toLowerCase().trim() == 'true')
                      ImageIcon(
                        const AssetImage(
                          'assets/icons/tick.png',
                        ),
                        size: 13,
                        color: ThemeColors.color06AA37,
                      ),
                    if (e.isApprove?.toLowerCase().trim() == 'false')
                      ImageIcon(
                        const AssetImage(
                          'assets/icons/close_red.png',
                        ),
                        size: 13,
                        color: ThemeColors.colorB80000,
                      ),
                  ],
                ),
              ),
            ],
          );
        },
      ).toList(),
    );
  }

  Future<void> _onForwardLeave(
      {required BuildContext context, required int leaveID}) async {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        builder: (BuildContext context) =>
            LeaveForwardBottomsheet(leaveId: leaveID));
  }
}
