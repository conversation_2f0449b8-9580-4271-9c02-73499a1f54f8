// ignore_for_file: use_build_context_synchronously

import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/model/leave_requested_view.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/leave_request_for_reporting_person/widget/leave_reject_reason_dialogbox.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_dialog_box.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import '../../util/colors.dart';
import 'leave_request_overview.dart';
import 'package:e8_hr_portal/model/leave_requested_view.dart' as req;

class LeaveRequestForReportingPerson extends StatefulWidget {
  const LeaveRequestForReportingPerson({super.key});

  @override
  State<LeaveRequestForReportingPerson> createState() =>
      _LeaveRequestForReportingPersonState();
}

class _LeaveRequestForReportingPersonState
    extends State<LeaveRequestForReportingPerson> {
  late LeaveApplicationProvider provider;
  @override
  void initState() {
    provider = Provider.of<LeaveApplicationProvider>(context, listen: false);

    provider.initPageLeaveRequests();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      provider.init();
    });
    super.initState();
  }

  @override
  void dispose() {
    // log("response inside == == ");

    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        provider.mainFiltereOfLeave = "Pending to me";
        provider.refreshLeaveRequest();
      },
    );

    provider.pagingControllerLeaveRequests?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // String? role = Provider.of<ProfileProvider>(context).userRole;

    return CustomScaffold(
      screenTitle: "Employee Leave Requests",
      horizontalPadding: w,
      body: Column(
        children: [
          SizedBox(height: h * 31),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: w * 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("Leave Requests", style: tsS18w500c181818),
                // _filterDropDownButton(),
              ],
            ),
          ),
          const SizedBox(height: 10),
          Consumer<LeaveApplicationProvider>(
              builder: (context, leaveProvider, _) {
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: w * 16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(child: _filterMainDropDown()),
                  if (leaveProvider.mainFiltereOfLeave != "Pending to me") ...[
                    SizedBox(width: 20 * w),
                    Expanded(child: _filterDropDownButton()),
                  ]
                ],
              ),
            );
          }),
          SizedBox(height: h * 14),
          // Approve All Past Leaves Button
          Padding(
            padding: EdgeInsets.symmetric(horizontal: w * 16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _onApproveAllPastLeaves,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ThemeColors.primaryColor,
                  padding: EdgeInsets.symmetric(vertical: h * 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  "Approve All Past Leaves",
                  style: tsS14w500FFFFF,
                ),
              ),
            ),
          ),
          SizedBox(height: h * 14),
          // Approve All Pending Leaves Button
          Padding(
            padding: EdgeInsets.symmetric(horizontal: w * 16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _onApproveAllPendingLeaves,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ThemeColors.primaryColor,
                  padding: EdgeInsets.symmetric(vertical: h * 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  "Approve All Pending Leaves",
                  style: tsS14w500FFFFF,
                ),
              ),
            ),
          ),
          SizedBox(height: h * 14),
          Expanded(
            child: Consumer<LeaveApplicationProvider>(
              builder: (context, provider, _) {
                return PagedListView.separated(
                  physics: const BouncingScrollPhysics(),
                  padding: EdgeInsets.only(bottom: h * 60),
                  pagingController: provider.pagingControllerLeaveRequests!,
                  builderDelegate:
                      PagedChildBuilderDelegate<LeaveRequestViewModel>(
                    noItemsFoundIndicatorBuilder: (context) {
                      return const Center(
                        child: Text(
                          "No Leave Requests",
                          style: TextStyle(color: Colors.black, fontSize: 18),
                        ),
                      );
                    },
                    itemBuilder: (context, data, index) {
                      return _leaveRequestTile(data: data);
                    },
                  ),
                  separatorBuilder: (context, index) {
                    return SizedBox(height: w * 10);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _leaveRequestTile({LeaveRequestViewModel? data}) {
    String? status = data?.leaveDetails?.status;
    List<String> statusList = ["cancelled", "rejected", "approved", "expired"];
    if (!statusList.contains(status?.toLowerCase())) {
      return Slidable(
        key: ValueKey(data?.leaveDetails?.id),
        endActionPane: ActionPane(
          openThreshold: 0.0001,
          extentRatio: 0.3,
          motion: const ScrollMotion(),
          children: (data?.leaveDetails?.leavePermission == "allow" &&
                  (status?.toLowerCase() == "pending" ||
                      status?.toLowerCase() == "in-progress"))
              ? [
                  GestureDetector(
                    onTap: () => _onLeaveAccepted(data?.leaveDetails?.id),
                    child: Container(
                      margin: EdgeInsets.only(left: w * 22),
                      width: 25,
                      child: Image.asset(
                        "assets/icons/check_mark.png",
                        height: 50,
                        width: 50,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () => _onLeaveRejected(data?.leaveDetails?.id),
                    child: Container(
                      margin: EdgeInsets.only(left: w * 20),
                      width: 20,
                      child: Image.asset(
                        "assets/icons/cross_mark.png",
                        height: 50,
                        width: 50,
                      ),
                    ),
                  ),
                ]
              : [],
        ),
        child: _leaveRequestCard(request: data!),
      );
    }
    return _leaveRequestCard(request: data!);
  }

  Widget _filterMainDropDown() {
    return Container(
      padding: const EdgeInsets.fromLTRB(10, 5, 5, 5),
      height: 30 * h,
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(6)),
      child: Consumer<LeaveApplicationProvider>(
        builder: (context, provider, _) {
          return DropdownButton(
            isExpanded: true,
            underline: const SizedBox(),
            value: provider.mainFiltereOfLeave,
            items: (LoginModel.isAdmin != null && LoginModel.isAdmin!)
                ? provider.mainFilterDataLeaveHR.map(
                    (item) {
                      return DropdownMenuItem<String>(
                        value: item,
                        child: FittedBox(
                          child: Text(item.toString()),
                        ),
                      );
                    },
                  ).toList()
                : provider.mainFilterDataLeave.map(
                    (item) {
                      return DropdownMenuItem<String>(
                        value: item,
                        child: FittedBox(
                          child: Text(
                            item.toString(),
                          ),
                        ),
                      );
                    },
                  ).toList(),
            onChanged: (String? value) {
              if (value == null) return;
              provider.mainFiltereOfLeave = value;
              if (value.toLowerCase() == 'all') {
                provider.selectedFilter = provider.leaveFilterModel.firstWhere(
                    (element) => element.text.toLowerCase() == 'all',
                    orElse: () => LeaveRequestFilterModel('All', 1));
              } else if (value.toLowerCase() == 'pending to me') {
                provider.selectedFilter = provider.leaveFilterModel.firstWhere(
                    (element) => element.text.toLowerCase() == 'pending',
                    orElse: () => LeaveRequestFilterModel('All', 1));
              } else if (value.toLowerCase() == 'assigned to me') {
                provider.selectedFilter = provider.leaveFilterModel.firstWhere(
                    (element) => element.text.toLowerCase() == 'all',
                    orElse: () => LeaveRequestFilterModel('All', 1));
              }

              provider.refreshLeaveRequest();
            },
          );
        },
      ),
    );
  }

  _onLeaveAccepted(int? requestId) async {
    showDialog(
      context: context,
      builder: (context) {
        return GeneralConfirmationDialog(
          onYesStyle: tsS14w500c475366,
          onNoStyle: tsS14w500c475366,
          onNoPressed: () {
            Navigator.pop(context);
          },
          onYesPressed: () async {
            final navigator = Navigator.of(context);
            final leaveProvider =
                Provider.of<LeaveApplicationProvider>(context, listen: false);
            EasyLoading.show();
            await leaveProvider.leaveAcceptOrReject(
                leaveId: requestId, acceptOrReject: 1, context: context);
            // await leaveProvider.getLeaveRequest();
            leaveProvider.refreshLeaveRequest();
            navigator.pop();
            EasyLoading.dismiss();
          },
          title: "Confirmation",
          content: "Are you sure do you want to approve this request ?",
          acceptLabel: "Yes",
          cancelLabel: "No",
        );
      },
    );
  }

  _onLeaveRejected(int? requestId) async {
    EasyLoading.show();

    if (requestId != null) {
      showDialog(
        context: context,
        builder: (context) {
          return LeaveRectionReasonDialog(
            leaveId: requestId,
          );
        },
      );
    }
    //    final leaveProvider =
    // Provider.of<LeaveApplicationProvider>(context, listen: false);
    // await leaveProvider.leaveAcceptOrReject(
    //     leaveId: requestId, acceptOrReject: 2, context: context);
    // await leaveProvider.getLeaveRequest();
    // await leaveProvider.getLeaveRequestReporting();
    EasyLoading.dismiss();
  }

  Widget _leaveRequestCard({required LeaveRequestViewModel request}) {
    String? fromDate;
    String? toDate;
    String? createdAt;
    if (request.leaveDetails?.createdAt != null) {
      // createdAt = formatDateFromString(
      //     request.createdAt!, "yyyy-MM-ddThh:mm:ss", "MM EEE, yy");
      createdAt = request.leaveDetails?.createdAt;
    }
    if (request.leaveDetails?.startDate != null) {
      // DateTime dt = DateTime.parse(request.startDate.toString());
      // final DateFormat formatter = DateFormat('dd MMM yyyy');
      fromDate = request.leaveDetails?.startDate;
    }
    if (request.leaveDetails?.endDate != null) {
      // DateTime dt = DateTime.parse(request.endDate.toString());
      // final DateFormat formatter = DateFormat('dd MMM yyyy');
      toDate = request.leaveDetails?.endDate;
    }

    List<req.ReportingPersonList>? reportingPersonList =
        request.reportingPersonList;
    req.HrStatus? hrStatus = request.hrStatus;

    return GestureDetector(
      onTap: () async {
        final provider =
            Provider.of<LeaveApplicationProvider>(context, listen: false);
        if (request.leaveDetails?.id != null) {
          EasyLoading.show();
          bool isGo = await provider.getRequestedLeaveOverviewForReportedPerson(
              leaveId: request.leaveDetails!.id!);

          EasyLoading.dismiss();
          if (isGo) {
            PageNavigator.pushSlideRight(
                context: context, route: const LeaveRequestOverView());
          }
        }
      },
      child: Container(
        width: w * 343,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        margin: EdgeInsets.symmetric(horizontal: w * 16),
        padding: const EdgeInsets.all(10.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Row(
                children: [
                  Container(
                    height: h * 45,
                    width: w * 45,
                    margin: EdgeInsets.only(right: w * 10),
                    decoration: BoxDecoration(
                        color: ThemeColors.primaryColor,
                        shape: BoxShape.circle,
                        image: request.leaveDetails?.userDetails?.profilePic
                                    .toString() !=
                                null
                            ? DecorationImage(
                                image: NetworkImage(
                                    "${request.leaveDetails?.userDetails?.profilePic.toString()}"),
                                fit: BoxFit.cover,
                              )
                            : null),
                    alignment: Alignment.center,
                    child: request.leaveDetails?.userDetails?.profilePic == null
                        ? Text(
                            '${request.leaveDetails?.userDetails?.name?.substring(0, 1).toUpperCase().toUpperCase()}',
                            style: GoogleFonts.rubik(
                                fontSize: 22,
                                fontWeight: FontWeight.normal,
                                color: Colors.white),
                          )
                        : null,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text("${request.leaveDetails?.userDetails?.name} ",
                            style: tsS14w500c181818),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 3),
                              child: Image.asset(
                                  "assets/icons/calendar_green.png",
                                  color: ThemeColors.primaryColor,
                                  height: h * 10,
                                  width: w * 10),
                            ),
                            SizedBox(width: w * 4),
                            Expanded(
                              child: Text(
                                '$fromDate - $toDate • ${request.leaveDetails?.dayCount?.toString()} ${request.leaveDetails!.dayCount! <= 1 ? 'Day' : 'Days'}',
                                style: tsS12w400c979797,
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(width: w * 4.52),
                        Row(
                          children: [
                            Image.asset("assets/icons/calendar-remove.png",
                                color: ThemeColors.primaryColor,
                                height: h * 10,
                                width: w * 10),
                            SizedBox(width: w * 4),
                            Text('${request.leaveDetails?.leaveType}',
                                style: tsS12w400c979797),
                          ],
                        ),
                        SizedBox(height: h * 4.52),
                        Row(
                          children: [
                            if (reportingPersonList != null)
                              _reportingPersonsWidget(
                                  data: reportingPersonList),
                            if (hrStatus != null)
                              _hrApprovalWidget(
                                  profilePic: hrStatus.profilePic ?? "",
                                  name: hrStatus.name ?? "",
                                  isApprove: hrStatus.isApprove ?? ""),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            if (request.leaveDetails?.status != null)
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  _statusWidget(status: request.leaveDetails?.status ?? ""),
                  SizedBox(height: h * 5),
                  Text(createdAt ?? "", style: tsS12w400979797),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _filterDropDownButton() {
    return Container(
      padding: const EdgeInsets.fromLTRB(10, 5, 5, 5),
      height: 30 * h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
      ),
      child: DropdownButtonHideUnderline(
        child: Consumer<LeaveApplicationProvider>(
          builder: (context, provider, _) {
            return DropdownButton<LeaveRequestFilterModel>(
              borderRadius: BorderRadius.circular(6),
              icon: Icon(Icons.keyboard_arrow_down, size: w * 18),
              dropdownColor: Colors.white,
              isDense: true,
              isExpanded: true, // This ensures the dropdown takes full width
              value: provider.selectedFilter,
              onChanged: (value) {
                provider.selectedFilter = value;
                provider.refreshLeaveRequest();
              },
              items: provider.leaveFilterModel.map((item) {
                return DropdownMenuItem(
                  value: item,
                  child: Text(
                    item.text,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    // style: TextStyle(
                    //     fontSize: 12 * w), // Adjust font size if needed
                  ),
                );
              }).toList(),
            );
          },
        ),
      ),
    );
  }

  Widget _statusWidget({required String status}) {
    TextStyle? style;
    Color? color;
    switch (status) {
      case "Approved":
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
        break;
      case "Rejected":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case "Pending":
        {
          style = tsS12w600cE5B900;
          color = ThemeColors.colorFFF2E2;
        }
        break;
      case "In-progress":
        {
          style = tsS12W6FE5B900;
          color = ThemeColors.colorFFF2E2;
        }
        break;
      case "Cancelled":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case "Expired":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      default:
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      height: h * 23,
      // width: w * 74,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: color,
      ),
      child: Text(
        status,
        style: style,
      ),
    );
  }

  Widget _hrApprovalWidget(
      {required String profilePic,
      required String name,
      required String isApprove}) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: ClipRRect(
              borderRadius: BorderRadius.circular(5.0),
              child: ClipOval(
                child: Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: profilePic.toString(),
                      width: 30 * w,
                      height: 30 * h,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) {
                        return Container(
                          height: 30,
                          width: 30,
                          decoration: BoxDecoration(
                            color: ThemeColors.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            name.substring(0, 1).toUpperCase(),
                            style: GoogleFonts.rubik(
                                fontSize: 22,
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                          ),
                        );
                      },
                    ),
                    if (isApprove.toLowerCase().trim() == "pending")
                      Container(
                        height: 30 * h,
                        width: 30 * w,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                  ],
                ),
              )),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: Column(
            children: [
              if (isApprove.toLowerCase().trim() == "pending") const SizedBox(),
              if (isApprove.toLowerCase().trim() == "true")
                ImageIcon(
                  const AssetImage(
                    "assets/icons/tick.png",
                  ),
                  size: 13,
                  color: ThemeColors.color06AA37,
                ),
              if (isApprove.toLowerCase().trim() == "false")
                ImageIcon(
                  const AssetImage(
                    "assets/icons/close_red.png",
                  ),
                  size: 13,
                  color: ThemeColors.colorB80000,
                ),
            ],
          ),
        )
      ],
    );
  }

  Widget _reportingPersonsWidget(
      {required List<req.ReportingPersonList>? data}) {
    return Row(
      children: data!.map((e) {
        return Stack(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(5.0),
                child: ClipOval(
                  child: Stack(
                    children: [
                      CachedNetworkImage(
                        imageUrl: e.profilePic.toString(),
                        width: 30 * w,
                        height: 30 * h,
                        fit: BoxFit.cover,
                        errorWidget: (context, url, error) {
                          return Container(
                            height: 30,
                            width: 30,
                            decoration: BoxDecoration(
                              color: ThemeColors.primaryColor,
                              shape: BoxShape.circle,
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              e.name
                                      ?.substring(0, 1)
                                      .toUpperCase()
                                      .toUpperCase() ??
                                  "",
                              style: GoogleFonts.rubik(
                                  fontSize: 22,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white),
                            ),
                          );
                        },
                      ),
                      if (e.isApprove?.toLowerCase().trim() == "pending")
                        Container(
                          height: 30 * h,
                          width: 30 * w,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              child: Column(
                children: [
                  if (e.isApprove?.toLowerCase().trim() == "pending")
                    const SizedBox(),
                  if (e.isApprove?.toLowerCase().trim() == "true")
                    ImageIcon(
                      const AssetImage(
                        "assets/icons/tick.png",
                      ),
                      size: 13,
                      color: ThemeColors.color06AA37,
                    ),
                  if (e.isApprove?.toLowerCase().trim() == "false")
                    ImageIcon(
                      const AssetImage(
                        "assets/icons/close_red.png",
                      ),
                      size: 13,
                      color: ThemeColors.colorB80000,
                    ),
                ],
              ),
            )
          ],
        );
      }).toList(),
    );
  }

  /// Method to approve all past leaves with pending or in-progress status
  _onApproveAllPastLeaves() async {
    showDialog(
      context: context,
      builder: (context) {
        return GeneralConfirmationDialog(
          onYesStyle: tsS14w500c475366,
          onNoStyle: tsS14w500c475366,
          title: "Confirm Approval",
          content:
              "Are you sure you want to approve all past leaves that are pending or in-progress?",
          acceptLabel: "Yes",
          cancelLabel: "No",
          onYesPressed: () async {
            final navigator = Navigator.of(context);
            navigator.pop(); // Close dialog first

            await _approveAllPastLeaves();
          },
          onNoPressed: () {
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  /// Method to approve all pending leaves regardless of date
  _onApproveAllPendingLeaves() async {
    showDialog(
      context: context,
      builder: (context) {
        return GeneralConfirmationDialog(
          onYesStyle: tsS14w500c475366,
          onNoStyle: tsS14w500c475366,
          title: "Confirm Approval",
          content: "Are you sure you want to approve all pending leaves?",
          acceptLabel: "Yes",
          cancelLabel: "No",
          onYesPressed: () async {
            final navigator = Navigator.of(context);
            navigator.pop(); // Close dialog first

            await _approveAllPendingLeaves();
          },
          onNoPressed: () {
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  /// Helper method to process and approve all eligible past leaves
  Future<void> _approveAllPastLeaves() async {
    try {
      EasyLoading.show();

      final leaveProvider =
          Provider.of<LeaveApplicationProvider>(context, listen: false);

      // Get all leave requests from the paging controller
      List<LeaveRequestViewModel> allLeaveRequests = [];

      // Collect all items from the paging controller
      if (leaveProvider.pagingControllerLeaveRequests != null) {
        try {
          final itemList =
              leaveProvider.pagingControllerLeaveRequests!.itemList;
          if (itemList != null) {
            allLeaveRequests = itemList;
          }
        } catch (e) {
          // Controller might be disposed, return early
          debugPrint('PagingController access error: $e');
          EasyLoading.dismiss();
          return;
        }
      }

      // Get current date to check for past leaves
      DateTime currentDate = DateTime.now();

      // Filter past leaves with pending/in-progress status and allow permission
      List<LeaveRequestViewModel> eligibleLeaves =
          allLeaveRequests.where((leave) {
        if (leave.leaveDetails == null) return false;

        final leaveDetails = leave.leaveDetails!;
        final status = leaveDetails.status?.toLowerCase();
        final permission = leaveDetails.leavePermission;
        final endDateStr = leaveDetails.endDate;

        // Check if leave permission is "allow"
        if (permission != "allow") return false;

        // Check if status is pending or in-progress
        if (status != "pending" && status != "in-progress") return false;

        // Check if leave is in the past
        if (endDateStr != null) {
          try {
            // Parse date in format "dd MMM,yyyy" (e.g., "25 Dec,2024")
            DateTime endDate = _parseCustomDateFormat(endDateStr);
            return endDate.isBefore(currentDate);
          } catch (e) {
            debugPrint('Error parsing date: $endDateStr - $e');
            return false;
          }
        }

        return false;
      }).toList();

      debugPrint('Eligible leaves found: ${eligibleLeaves.length}');

      if (eligibleLeaves.isEmpty) {
        EasyLoading.dismiss();
        if (mounted) {
          _showInfoMessage('No eligible past leaves found to approve.');
        }
        return;
      }

      // Process each eligible leave
      int successCount = 0;
      int totalCount = eligibleLeaves.length;

      for (int i = 0; i < eligibleLeaves.length; i++) {
        final leave = eligibleLeaves[i];
        final leaveId = leave.leaveDetails?.id;

        if (leaveId != null) {
          // Show progress via SnackBar
          EasyLoading.show();

          try {
            await leaveProvider.leaveAcceptOrReject(
              leaveId: leaveId,
              acceptOrReject: 1, // 1 for approve
              context: context,
            );
            successCount++;
          } catch (e) {
            // Continue with next leave even if one fails
            debugPrint('Failed to approve leave $leaveId: $e');
          }
        }
      }

      // Refresh the leave requests list only if widget is still mounted
      if (mounted) {
        // Use a delayed refresh to avoid duplication issues
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted && leaveProvider.pagingControllerLeaveRequests != null) {
            try {
              leaveProvider.currentPageLeaveRequest = 0;
              leaveProvider.pagingControllerLeaveRequests!.refresh();
            } catch (e) {
              debugPrint('Error refreshing leave requests: $e');
            }
          }
        });
      }

      EasyLoading.dismiss();

      // Show result message only if widget is still mounted
      if (mounted) {
        if (successCount == totalCount) {
          _showSuccessMessage(
              'Successfully approved $successCount past leaves!');
        } else if (successCount > 0) {
          _showInfoMessage(
              'Approved $successCount out of $totalCount past leaves.');
        } else {
          _showErrorMessage('Failed to approve any past leaves.');
        }
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (mounted) {
        _showErrorMessage('An error occurred while processing past leaves.');
      }
      debugPrint('Error in _approveAllPastLeaves: $e');
    }
  }

  /// Helper method to process and approve all pending leaves regardless of date
  Future<void> _approveAllPendingLeaves() async {
    try {
      EasyLoading.show();

      final leaveProvider =
          Provider.of<LeaveApplicationProvider>(context, listen: false);

      // Get all leave requests from the paging controller
      List<LeaveRequestViewModel> allLeaveRequests = [];

      // Collect all items from the paging controller
      if (leaveProvider.pagingControllerLeaveRequests != null) {
        try {
          final itemList =
              leaveProvider.pagingControllerLeaveRequests!.itemList;
          if (itemList != null) {
            allLeaveRequests = itemList;
          }
        } catch (e) {
          // Controller might be disposed, return early
          debugPrint('PagingController access error: $e');
          EasyLoading.dismiss();
          return;
        }
      }

      // Filter pending/in-progress leaves with allow permission
      List<LeaveRequestViewModel> eligibleLeaves =
          allLeaveRequests.where((leave) {
        if (leave.leaveDetails == null) return false;

        final leaveDetails = leave.leaveDetails!;
        final status = leaveDetails.status?.toLowerCase();
        final permission = leaveDetails.leavePermission;

        // Check if leave permission is "allow"
        if (permission != "allow") return false;

        // Check if status is pending or in-progress
        return status == "pending" || status == "in-progress";
      }).toList();

      debugPrint('Eligible pending leaves found: ${eligibleLeaves.length}');

      if (eligibleLeaves.isEmpty) {
        EasyLoading.dismiss();
        if (mounted) {
          _showInfoMessage('No eligible pending leaves found to approve.');
        }
        return;
      }

      // Process each eligible leave
      int successCount = 0;
      int totalCount = eligibleLeaves.length;

      for (int i = 0; i < eligibleLeaves.length; i++) {
        final leave = eligibleLeaves[i];
        final leaveId = leave.leaveDetails?.id;

        if (leaveId != null) {
          // Show progress via SnackBar
          EasyLoading.show();

          try {
            await leaveProvider.leaveAcceptOrReject(
              leaveId: leaveId,
              acceptOrReject: 1, // 1 for approve
              context: context,
            );
            successCount++;
          } catch (e) {
            // Continue with next leave even if one fails
            debugPrint('Failed to approve leave $leaveId: $e');
          }
        }
      }

      // Refresh the leave requests list only if widget is still mounted
      if (mounted) {
        // Use a delayed refresh to avoid duplication issues
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted && leaveProvider.pagingControllerLeaveRequests != null) {
            try {
              leaveProvider.currentPageLeaveRequest = 0;
              leaveProvider.pagingControllerLeaveRequests!.refresh();
            } catch (e) {
              debugPrint('Error refreshing leave requests: $e');
            }
          }
        });
      }

      EasyLoading.dismiss();

      // Show result message only if widget is still mounted
      if (mounted) {
        if (successCount == totalCount) {
          _showSuccessMessage(
              'Successfully approved $successCount pending leaves!');
        } else if (successCount > 0) {
          _showInfoMessage(
              'Approved $successCount out of $totalCount pending leaves.');
        } else {
          _showErrorMessage('Failed to approve any pending leaves.');
        }
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (mounted) {
        _showErrorMessage('An error occurred while processing pending leaves.');
      }
      debugPrint('Error in _approveAllPendingLeaves: $e');
    }
  }

  /// Helper methods to show messages that work around EasyLoading text visibility issues
  void _showInfoMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.info_outline, color: Colors.white),
            SizedBox(width: 8),
            Expanded(
                child: Text(message, style: TextStyle(color: Colors.white))),
          ],
        ),
        backgroundColor: Colors.orange.shade600,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle_outline, color: Colors.white),
            SizedBox(width: 8),
            Expanded(
                child: Text(message, style: TextStyle(color: Colors.white))),
          ],
        ),
        backgroundColor: Colors.green.shade600,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.white),
            SizedBox(width: 8),
            Expanded(
                child: Text(message, style: TextStyle(color: Colors.white))),
          ],
        ),
        backgroundColor: Colors.red.shade600,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// Parse date in format "dd MMM,yyyy" (e.g., "25 Dec,2024")
  DateTime _parseCustomDateFormat(String dateStr) {
    try {
      // Remove any extra spaces and split by comma
      String cleanDate = dateStr.trim();

      // Handle format "dd MMM,yyyy" or "dd MMM, yyyy"
      List<String> parts = cleanDate.split(',');
      if (parts.length != 2) {
        throw FormatException('Invalid date format: $dateStr');
      }

      String dayMonth = parts[0].trim(); // "25 Dec"
      String year = parts[1].trim(); // "2024"

      // Split day and month
      List<String> dayMonthParts = dayMonth.split(' ');
      if (dayMonthParts.length != 2) {
        throw FormatException('Invalid day-month format: $dayMonth');
      }

      String day = dayMonthParts[0];
      String month = dayMonthParts[1];

      // Convert month name to number
      Map<String, int> monthMap = {
        'Jan': 1,
        'Feb': 2,
        'Mar': 3,
        'Apr': 4,
        'May': 5,
        'Jun': 6,
        'Jul': 7,
        'Aug': 8,
        'Sep': 9,
        'Oct': 10,
        'Nov': 11,
        'Dec': 12
      };

      int? monthNumber = monthMap[month];
      if (monthNumber == null) {
        throw FormatException('Invalid month: $month');
      }

      return DateTime(int.parse(year), monthNumber, int.parse(day));
    } catch (e) {
      debugPrint('Error parsing custom date format: $dateStr - $e');
      rethrow;
    }
  }
}

class StatusWidget2 extends StatelessWidget {
  const StatusWidget2({
    super.key,
    required this.data,
  });

  final LeaveRequestViewModel? data;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 15.0,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(3),
        color: data?.leaveDetails?.leavePermission == "allow"
            ? const Color(0xFFB9FFCE)
            : const Color(0xFFFFECC1),
      ),
      child: Text(
        data?.leaveDetails?.leavePermission == "allow"
            ? "Approved"
            : "Rejected",
        style: GoogleFonts.rubik(
          fontSize: 10,
          color: data?.leaveDetails?.leavePermission == "allow"
              ? const Color(0xFF06AA37)
              : const Color(0xFFFFB100),
        ),
      ),
    );
  }
}

void doNothing(BuildContext context) {}

class StatusWidget1 extends StatelessWidget {
  const StatusWidget1({
    super.key,
    required this.data,
  });

  final LeaveRequestViewModel? data;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 15.0,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(3),
        color: data?.leaveDetails?.leavePermission == "allow"
            ? const Color(0xFFB9FFCE)
            : const Color(0xFFFFECC1),
      ),
      child: Text(
        data?.leaveDetails?.leavePermission == "allow"
            ? "Approved"
            : "Rejected",
        style: GoogleFonts.rubik(
          fontSize: 10,
          color: data?.leaveDetails?.leavePermission == "allow"
              ? const Color(0xFF06AA37)
              : const Color(0xFFFFB100),
        ),
      ),
    );
  }
}

class StatusWidget extends StatelessWidget {
  const StatusWidget({
    super.key,
    required this.data,
  });

  final LeaveRequestViewModel? data;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 15.0,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(3),
        color: data?.leaveDetails?.status == "Approved"
            ? const Color(0xFFB9FFCE).withOpacity(0.10)
            : data?.leaveDetails?.status == "Pending"
                ? const Color(0xFFFFECC1).withOpacity(0.10)
                : data?.leaveDetails?.status == "In-progress"
                    ? const Color(0xFF5570F1).withOpacity(0.10)
                    : data?.leaveDetails?.status == "Cancelled"
                        ? const Color(0xFFF64D44).withOpacity(0.10)
                        : const Color(0xFFF64D44).withOpacity(0.10),
      ),
      child: Text(
        data?.leaveDetails?.status == "Approved"
            ? "Approved"
            : data?.leaveDetails?.status == "Pending"
                ? "Pending"
                : data?.leaveDetails?.status == "In-progress"
                    ? "In-progress"
                    : data?.leaveDetails?.status == "Cancelled"
                        ? "Cancelled"
                        : "Rejected",
        style: GoogleFonts.rubik(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: data?.leaveDetails?.status == "Approved"
              ? const Color.fromARGB(255, 12, 127, 47)
              : data?.leaveDetails?.status == "Pending"
                  ? const Color.fromARGB(255, 235, 191, 88)
                  : data?.leaveDetails?.status == "In-progress"
                      ? const Color(0xFF5570F1)
                      : data?.leaveDetails?.status == "Cancelled"
                          ? const Color(0xFFF64D44)
                          : const Color(0xFFF64D44),
        ),
      ),
    );
  }
}

class ButtonRowWidget extends StatelessWidget {
  final int? id;
  const ButtonRowWidget({super.key, required this.id});

  @override
  Widget build(BuildContext context) {
    final leaveProvider =
        Provider.of<LeaveApplicationProvider>(context, listen: false);
    return Row(
      children: [
        ElevatedButton(
          onPressed: () async {
            await leaveProvider.leaveAcceptOrReject(
                leaveId: id!, acceptOrReject: 1, context: context);

            // await leaveProvider.getLeaveRequest();
            leaveProvider.refreshLeaveRequest();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: ThemeColors.color06AA37,
          ),
          child: const Text('Approve'),
        ),
        const SizedBox(width: 10.0),
        ElevatedButton(
          onPressed: () async {
            await leaveProvider.leaveAcceptOrReject(
                leaveId: id!, acceptOrReject: 2, context: context);

            // await leaveProvider.getLeaveRequest();
            leaveProvider.refreshLeaveRequest();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: ThemeColors.colorF64D44,
          ),
          child: const Text('Reject'),
        )
      ],
    );
  }
}
