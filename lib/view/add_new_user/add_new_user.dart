// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:e8_hr_portal/helper/common_textfield.dart';

// import 'package:e8_hr_portal/provider/user_provider.dart';
// import 'package:e8_hr_portal/util/colors.dart';
// import 'package:e8_hr_portal/util/dailoge.dart';
// import 'package:e8_hr_portal/util/date_formatter.dart';
// import 'package:e8_hr_portal/util/general_functions.dart';
// import 'package:e8_hr_portal/util/page_navigator.dart';
// import 'package:e8_hr_portal/util/styles.dart';
// import 'package:e8_hr_portal/util/validator.dart';


// import 'package:provider/provider.dart';

// import '../master/master_screen.dart';

// class AddNewUserScreen extends StatefulWidget {
//   const AddNewUserScreen({Key? key}) : super(key: key);

//   @override
//   State<AddNewUserScreen> createState() => _AddNewUserScreenState();
// }

// class _AddNewUserScreenState extends State<AddNewUserScreen> {
//   List skills = ["flutter developer", "python developer"];
//   List selectedSkills = [];
//   final List<String> _skillList = [];

//   final TextEditingController employeeIDController = TextEditingController();
//   final TextEditingController fullNameConoller = TextEditingController();
//   final TextEditingController designationController = TextEditingController();
//   final TextEditingController landlineExtensionController =
//       TextEditingController();
//   final TextEditingController emailAddressController = TextEditingController();
//   final TextEditingController contactNumberController = TextEditingController();
//   final TextEditingController dateofBirthController = TextEditingController();
//   final TextEditingController dateofJoinController = TextEditingController();
//   final TextEditingController skillController = TextEditingController();
//   String? _selectedDesignation;
//   final _formKey = GlobalKey<FormState>();

//   @override
//   void initState() {
//     Provider.of<UserProvider>(context, listen: false).getDesignations();
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final UserProvider userProvider = Provider.of<UserProvider>(context);
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text("Add New User"),
//       ),
//       body: SingleChildScrollView(
//           child: Padding(
//         padding: const EdgeInsets.all(15.0),
//         child: Form(
//           key: _formKey,
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.stretch,
//             children: [
//               Container(
//                 width: double.infinity,
//                 alignment: Alignment.center,
//                 child: Container(
//                   height: 156,
//                   width: 156,
//                   decoration: BoxDecoration(
//                       borderRadius: BorderRadius.circular(15),
//                       image: const DecorationImage(
//                         image: AssetImage("assets/images/user.png"),
//                         fit: BoxFit.fill,
//                       )),
//                 ),
//               ),
//               CommonTextFieldWidget(
//                 labelText: "Employee ID",
//                 controller: employeeIDController,
//               ),
//               const SizedBox(
//                 height: 15,
//               ),
//               CommonTextFieldWidget(
//                 labelText: "Full Name",
//                 textCapitalization: TextCapitalization.words,
//                 controller: fullNameConoller,
//                 validator: (value) =>
//                     Validator.textSpecific(value!, 'full name'),
//               ),
//               const SizedBox(
//                 height: 15,
//               ),
//               DropdownButtonFormField(
//                 items: userProvider.designationList.map((e) {
//                   return DropdownMenuItem(
//                     value: e,
//                     child: Text(e),
//                   );
//                 }).toList(),
//                 onChanged: (String? value) {
//                   setState(() {
//                     _selectedDesignation = value;
//                   });
//                 },
//                 value: _selectedDesignation,
//                 decoration: InputDecoration(
//                   labelText: 'Designation',
//                   labelStyle: GoogleFonts.rubik(
//                     color: ThemeColors.titleColor,
//                   ),
//                   border: const UnderlineInputBorder(
//                     borderSide: BorderSide(
//                       color: Color(0xFFDEE7FF),
//                     ),
//                   ),
//                   enabledBorder: const UnderlineInputBorder(
//                     borderSide: BorderSide(
//                       color: Color(0xFFDEE7FF),
//                     ),
//                   ),
//                 ),
//               ),
//               // CommonTextFieldWidget(
//               //   labelText: "Designation",
//               //   controller: designationController,
//               // ),
//               // const SizedBox(
//               //   height: 15,
//               // ),
//               // CommonTextFieldWidget(
//               //   labelText: "Landline Extension",
//               //   controller: landlineExtensionController,
//               // ),
//               const SizedBox(
//                 height: 15,
//               ),
//               CommonTextFieldWidget(
//                 labelText: "Email Address",
//                 controller: emailAddressController,
//                 validator: Validator.email,
//               ),
//               const SizedBox(
//                 height: 15,
//               ),
//               CommonTextFieldWidget(
//                 labelText: "Contact Number",
//                 keyboardType: TextInputType.phone,
//                 maxLength: 10,
//                 counter: const Offstage(),
//                 controller: contactNumberController,
//               ),
//               const SizedBox(
//                 height: 15,
//               ),
//               CommonTextFieldWidget(
//                 labelText: "Date of Birth",
//                 controller: dateofBirthController,
//                 readOnly: true,
//                 onTap: () {
//                   _selectDateOfBirth(context);
//                 },
//               ),
//               const SizedBox(
//                 height: 15,
//               ),
//               CommonTextFieldWidget(
//                 labelText: "Date of Join",
//                 controller: dateofJoinController,
//                 readOnly: true,
//                 onTap: () {
//                   _selectDateOfJoin(context);
//                 },
//               ),
//               const SizedBox(
//                 height: 15,
//               ),
//               SizedBox(
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     SizedBox(
//                       width: MediaQuery.of(context).size.width - 100,
//                       child: CommonTextFieldWidget(
//                         labelText: "Skill",
//                         controller: skillController,
//                         textCapitalization: TextCapitalization.words,
//                       ),
//                     ),
//                     IconButton(
//                       onPressed: () {
//                         if (skillController.text.isNotEmpty) {
//                           hideKeyboard(context);
//                           setState(() {
//                             _skillList.add(skillController.text);
//                           });
//                           skillController.clear();
//                         }
//                       },
//                       icon: const Icon(Icons.add_circle_outline),
//                     ),
//                   ],
//                 ),
//               ),
//               const SizedBox(
//                 height: 15,
//               ),
//               Wrap(
//                 direction: Axis.horizontal,
//                 spacing: 10,
//                 children: _skillList
//                     .map(
//                       (e) => Chip(
//                         label: Text(e),
//                         onDeleted: () {
//                           setState(() {
//                             _skillList.remove(e);
//                           });
//                         },
//                       ),
//                     )
//                     .toList(),
//               ),
//               // Align(
//               //   alignment: Alignment.topLeft,
//               //   child: Text(
//               //     "skill",
//               //     style: tsS14c8391B5,
//               //   ),
//               // ),
//               // const SizedBox(
//               //   height: 15,
//               // ),
//               // DropdownButtonFormField(
//               //     items: skills.map((e) {
//               //       return DropdownMenuItem(value: e, child: Text(e.toString()));
//               //     }).toList(),
//               //     onChanged: (v) {
//               //       if (!selectedSkills.contains(v)) {
//               //         setState(() {
//               //           selectedSkills.add(v);
//               //         });
//               //       }
//               //     }),
//               // ListView.builder(
//               //     shrinkWrap: true,
//               //     itemCount: selectedSkills.length,
//               //     itemBuilder: (context, index) {
//               //       print("-----------------------$selectedSkills");
//               //       return Row(
//               //         children: [Text(selectedSkills[index])],
//               //       );
//               //     }),
//               const SizedBox(
//                 height: 10,
//               ),

//               CommonButton(
//                 buttonName: "Save",
//                 style: tsS14FFFFF,
//                 color: ThemeColors.secondaryColor,
//                 hPadding: 0,
//                 function: () async {
//                   EasyLoading.show();
//                   if (_formKey.currentState!.validate()) {
//                     if (await userProvider
//                         .getUserDetails(emailAddressController.text)) {
//                       UserCredential credential = await FirebaseAuth.instance
//                           .createUserWithEmailAndPassword(
//                               email: emailAddressController.text,
//                               password: "123456");
//                       await FirebaseFirestore.instance
//                           .collection("users")
//                           .doc(credential.user!.uid)
//                           .set({
//                         "uid": credential.user!.uid,
//                         "name": fullNameConoller.text,
//                         "email": emailAddressController.text,
//                         "isWorkFromHome": false,
//                         "approval_status": "approved",
//                         'employeeId': employeeIDController.text,
//                         'designation_name': _selectedDesignation,
//                         'phone': contactNumberController.text,
//                         'birthdate': dateofBirthController.text,
//                         'start_date': dateofJoinController.text,
//                         'skills': _skillList,
//                       });

//                       PageNavigator.push(
//                           context: context, route: const MasterScreen());
//                       showToastText("Successfully added");
//                     } else {
//                       showToastText("Email already exists");
//                     }
//                   }
//                   EasyLoading.dismiss();
//                 },
//               ),
//             ],
//           ),
//         ),
//       )),
//     );
//   }

//   DateTime? dateOfBirth;
//   Future<void> _selectDateOfBirth(BuildContext context) async {
//     DateTime initialDate = DateTime(DateTime.now().year - 15);
//     DateTime firstDate = DateTime(DateTime.now().year - 100);
//     DateTime lastDate = DateTime(DateTime.now().year - 15);
//     final DateTime? picked = await showDatePicker(
//       context: context,
//       initialDate: initialDate,
//       firstDate: firstDate,
//       lastDate: lastDate,
//     );
//     if (picked != null) {
//       String selectedDateOfBirth =
//           formatDateFromDate(dateTime: picked, format: 'dd MMM yyyy');
//       dateofBirthController.text = selectedDateOfBirth;
//       dateOfBirth = picked;
//     }
//   }

//   DateTime? dateOfJoin;
//   Future<void> _selectDateOfJoin(BuildContext context) async {
//     DateTime initialDate = DateTime.now();
//     DateTime firstDate = DateTime(DateTime.now().year - 100);
//     DateTime lastDate = DateTime.now();
//     final DateTime? picked = await showDatePicker(
//       context: context,
//       initialDate: initialDate,
//       firstDate: firstDate,
//       lastDate: lastDate,
//     );
//     if (picked != null) {
//       String selectedDateOfJoin =
//           formatDateFromDate(dateTime: picked, format: 'dd MMM yyyy');
//       dateofJoinController.text = selectedDateOfJoin;
//       dateOfJoin = picked;
//     }
//   }
// }
