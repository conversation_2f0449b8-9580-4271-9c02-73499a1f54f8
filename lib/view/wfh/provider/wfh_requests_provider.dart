import 'dart:convert';
import 'dart:developer';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_hr_overview_model.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_requests_model.dart';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class WfhRequestsProvider with ChangeNotifier {
  WfhRequestListDatas? _wfhRequests;

  final Dio _dio = Dio();

  bool _isLoading = false;
  bool get isLoading => _isLoading;
  set isLoading(bool value) {
    _isLoading = value;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  bool isApprovingFromSlidable = false;
  bool isRejectingFromSlidable = false;
  WfhRequestListDatas? get wfhRequests => _wfhRequests;

  Future<void> fetchWfhRequests() async {
    notifyListeners();

    try {
      final response = await _dio.get(wfhRequestsListUrl.toString(),
          options: Options(
              headers: await getHeaders(), validateStatus: (status) => true));

      if (response.statusCode == 200) {
        _wfhRequests = WfhRequestListDatas.fromJson(response.data);
        log('${_wfhRequests?.leaveDetails?.id}');
      } else {}
    } catch (e) {
      print(e);
    } finally {
      notifyListeners();
    }
  }

  int currentPageWfhRequest = 0;
  String _mainFiltereOfWfhRequests = "Pending to me";
  String get mainFiltereOfWfhRequests => _mainFiltereOfWfhRequests;
  set mainFiltereOfWfhRequests(String value) {
    _mainFiltereOfWfhRequests = value;
    notifyListeners();
  }

  PagingController<int, WfhHrOverviewModel>? pagingControllerWfhRequests;

  initPageLeaveRequests() {
    currentPageWfhRequest = 0;
    pagingControllerWfhRequests = PagingController(firstPageKey: 1);
    pagingControllerWfhRequests!.addPageRequestListener((pageKey) {
      getWfhRequestReporting(page: pageKey);
    });
  }

  String _selectedAction = '';
  String get selectedAction => _selectedAction;
  set selectedAction(String value) {
    _selectedAction = value;
    notifyListeners();
  }

  String _mainFiltereOfWfh = "Pending to me";
  String get mainFiltereOfWfh => _mainFiltereOfWfh;
  set mainFiltereOfWfh(String value) {
    _mainFiltereOfWfh = value;
    notifyListeners();
  }

  Future<void> getWfhRequestReporting({
    required int page,
  }) async {
    currentPageWfhRequest = page;
    String? action = '20';
    String? actionFilter = "pending_to_me";

    switch (selectedAction.toLowerCase()) {
      case 'pending/in-progress':
        action = "20";
        break;
      case 'all':
        action = '';
        break;

      case 'pending':
        action = "1";
        break;
      case 'in-progress':
        action = "2";
        break;

      case 'approved':
        action = "3";
        break;
      case 'rejected':
        action = "4";
        break;
      case 'cancelled':
        action = "5";
        break;
      case 'expired':
        action = "6";
        break;
      case 'assigned to me':
        action = "assigned_to_me";
        break;
      default:
        action = '';
    }
    switch (mainFiltereOfWfh.toLowerCase()) {
      case 'all':
        actionFilter = null;
        break;
      case 'pending to me':
        actionFilter = "pending_to_me";
        action = "";
        break;
      case 'assigned to me':
        actionFilter = "assigned_to_me";
        break;

      default:
        actionFilter = null;
    }

    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    isLoading = true;
    notifyListeners();

    StringBuffer url = StringBuffer("$wfhRequestsListUrl?limit=10&page=$page");

    // log("leave module -==== $action === $actionFilter");
    // log("leave module -==== ${LoginModel.isAdmin} ========= ${LoggedInUser.isAdmin}");
    if (action.isNotEmpty && actionFilter != "All") {
      url.write("&filter_data=$action");
    }
    // if (actionFilter != null && actionFilter != "All") {
    //   url.write("&main_filter=$actionFilter");
    // }

    var response = await http.get(Uri.parse(url.toString()),
        headers: {"Authorization": "Bearer $auth"});

    log("WFH Requested ${actionFilter}");
    log("WFH Requested ${auth}");
    log("WFH Requested ${response.body}");
    log("WFH Requested ${response.request}");
    log("Status code ${response.statusCode}");

    if (response.statusCode == 200) {
      isLoading = false;
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      List<WfhHrOverviewModel> tempList = [];
      tempList = (data['data'] as List)
          .map((e) => WfhHrOverviewModel.fromJson(e))
          .toList();

      if (data['has_next'] == true) {
        int nextPage = page + 1;

        pagingControllerWfhRequests!.appendPage(tempList, nextPage);
      } else {
        pagingControllerWfhRequests!.appendLastPage(tempList);
      }
    } else {
      int nextPage = 0;
      pagingControllerWfhRequests!.appendPage([], nextPage);
    }
    notifyListeners();
  }

  List<String> mainFilterDataWfhHR = ["All", "Pending to me", "Assigned to me"];

  List<String> mainFilterDataWfh = ["All", "Pending to me"];

  init() {
    selectedFilter = wfhFilterModel.first;
  }

  List<LeaveRequestFilterModel> wfhFilterModel = [
    LeaveRequestFilterModel("Pending/In-progress", 20),
    LeaveRequestFilterModel("All", 1),
    LeaveRequestFilterModel("Approved", 2),
    LeaveRequestFilterModel("Rejected", 3),
    LeaveRequestFilterModel("Pending", 4),
    LeaveRequestFilterModel("In-progress", 5),
    // LeaveRequestFilterModel("assigned to me", 7),
    // LeaveRequestFilterModel("Expired", 6),
  ];
  int currentPagewfhRequest = 0;

  // todo
  Future<void> wfhAcceptOrReject(
      {required int? leaveId,
      required int? acceptOrReject,
      String? reason,
      required context}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    try {
      var response = await http.post(
        leaveApproveReject,
        headers: <String, String>{
          "Authorization": "Bearer $auth",
          "Content-Type": "application/json",
        },
        body: jsonEncode(<String, dynamic>{
          "leave_id": leaveId,
          "leave_status": acceptOrReject.toString(),
          "remark": reason ?? "",
        }),
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);
        // refreshLeaveRequest();
        currentPagewfhRequest = 0;
        pagingControllerWfhRequests!.refresh();
        pagingControllerWfhRequests!.notifyListeners();

        notifyListeners();
        showToastText(data["result"]);
      }
      if (response.statusCode == 400) {}
      notifyListeners();
      // EasyLoading.dismiss();
    } catch (e) {
      // EasyLoading.dismiss();
      debugPrint(e.toString());
    }
    // EasyLoading.dismiss();
  }

  LeaveRequestFilterModel? _selectedFilter;
  LeaveRequestFilterModel? get selectedFilter => _selectedFilter;
  set selectedFilter(LeaveRequestFilterModel? value) {
    _selectedFilter = value;
    selectedAction = value!.text;
    notifyListeners();
  }

  void refreshWfhRequest() {
    currentPagewfhRequest = 0;
    pagingControllerWfhRequests!.refresh();
    notifyListeners();
  }

  Future<bool> rejectWfhRequest({
    required int requestId,
    required String reason,
  }) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    final url = '$wfhRejectUrl';

    try {
      final response = await _dio.post(
        url,
        data: {
          "id": requestId,
          "rejection_reason": reason,
        },
        options: Options(
          headers: {
            "Authorization": "Bearer $auth",
            "Content-Type": "application/json",
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Handle success
        print("WFH rejected successfully: ${response.data}");
        return true;
      } else {
        print("Failed to reject WFH: ${response.statusCode}");
        return false;
      }
    } catch (e) {
      print("Error rejecting WFH: $e");
      return false;
    }
  }

  Future<bool> approveWfhRequest({
    required int requestId,
    required String comment,
  }) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    try {
      if (isLoading) return false;
      Map<String, dynamic> data = {"id": requestId, "approve_comment": comment};
      isLoading = true;
      final response = await _dio.post(
        '$wfhApproveUrl',
        data: data,
        options: Options(headers: {
          "Authorization": "Bearer $auth",
          "Content-Type": "application/json"
        }, validateStatus: (status) => true),
      );
      isLoading = false;
      log('approveWfhRequest -> ${response.data} - ${response.statusCode} - ${response.realUri} - ${data} - $auth');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        if (response.data['status'] == false) {
          Map<String, dynamic> data = response.data;
          if (data.containsKey('message')) {
            if (data['message'] is Map) {
              Map<String, dynamic> message = data['message'];

              if (message.containsKey('non_field_errors')) {
                List errors = (message['non_field_errors'] ?? []) as List;
                if (errors.isNotEmpty) {
                  showToastText(errors.first);
                }
              }
            } else if (data['message'] is String) {
              showToastText(data['message']);
            }
          }
        }
        return false;
      }
    } catch (e) {
      print("Error approving WFH: $e");
      isLoading = false;
      return false;
    } finally {
      isLoading = false;
    }
  }
}

class LeaveRequestFilterModel {
  final String text;
  final int id;
  LeaveRequestFilterModel(this.text, this.id);
}
