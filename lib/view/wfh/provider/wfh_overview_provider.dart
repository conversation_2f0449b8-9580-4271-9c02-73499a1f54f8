import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_hr_overview_model.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/wfh_overview_model.dart';

class WfhOverviewProvider with ChangeNotifier {
  WfhOverviewModel? _overview;
  bool _isLoading = false;
  String? _error;

  WfhOverviewModel? get overview => _overview;
  bool get isLoading => _isLoading;
  String? get error => _error;
  final Dio _dio = Dio();
  Future<WfhOverviewModel?> fetchOverview(int id) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      Response response = await _dio.get('$wfhRecordsOverviewURL$id/overview/',
          options: Options(
              headers: await getHeaders(), validateStatus: (status) => true));
      log('Response Body: ${response.data} --$id--- ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonData = response.data;
        _overview = WfhOverviewModel.fromJson(jsonData['data']);
        log('overView is ----- ${overview?.leaveDetails?.id}');
        return _overview;
      } else {
        _error = 'Failed with status: ${response.statusCode}';
      }
    } catch (e, stackTrace) {
      _error = 'Error: $e';
      log(stackTrace.toString());
    } finally {
      _isLoading = false;
      notifyListeners();
    }
    return null;
  }

  // In your provider
  bool _isCancellingWfh = false;
  bool get isCancellingWfh => _isCancellingWfh;

  Future<bool> cancelWfhRequest({required int wfhId}) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('access_token');
    if (_isCancellingWfh) return false;

    _isCancellingWfh = true;
    notifyListeners();

    try {
      log("Cancelling WFH ID: $wfhId");

      var headers = await getHeaders();
      log("Headers being sent: $headers"); // Add this line

      Map<String, int> requestData = {"id": wfhId};
      log("Request data: $requestData");
      FormData formData = FormData.fromMap(requestData);
      var response = await _dio.patch('$wfhCancelUrl',
          data: formData,
          options: Options(
              headers: {'Authorization': 'Bearer $token'},
              validateStatus: (status) => true));

      log("Response status: ${response.statusCode}");
      log("Response data: ${response.data} ---- ${response.realUri}");

      if (response.statusCode == 200) {
        log('cancelled the request');
        return true;
      } else {
        log("Error: Status ${response.statusCode}, Data: ${response.data}");
      }
    } catch (e) {
      log("Exception: $e");
      debugPrint(e.toString());
    } finally {
      _isCancellingWfh = false;
      notifyListeners();
    }
    return false;
  }
  // Add this function to your WfhOverviewProvider class

  bool _isUpdatingWfh = false;
  bool get isUpdatingWfh => _isUpdatingWfh;
  Future<bool> updateWfhRequest({
    required int wfhId,
    String? fromDate,
    String? toDate,
    WfhOverviewModel? item,
    required String remark,
    required String wfhType,
  }) async {
    if (_isUpdatingWfh) return false;
    _isUpdatingWfh = true;
    notifyListeners();
    try {
      // Prepare the request body
      Map<String, dynamic> requestBody = {
        'remark': remark,
        'wfh_type': wfhType,
      };
      if (fromDate != null) {
        requestBody['from_date'] = fromDate;
      }
      if (toDate != null) {
        requestBody['to_date'] = toDate;
      }
      log('Updating WFH request with data: $requestBody');

      var response = await _dio.put(
        "$wfhUpdateUrl$wfhId/update/",
        data: requestBody,
        options: Options(
          headers: await getHeaders(),
          validateStatus: (status) => true,
        ),
      );

      log('Update WFH Response: ${response.data} ----- ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Optionally refresh the overview after successful update

        await fetchOverview(wfhId);
        return true;
      } else {
        _error =
            'Failed to update WFH request with status: ${response.statusCode}';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'Error updating WFH request: $e';
      notifyListeners();
      debugPrint('Update WFH Error: $e');
      return false;
    } finally {
      _isUpdatingWfh = false;
      notifyListeners();
    }
  }

  WfhHrOverviewModel? _overviewData;
  bool isloading = false;
  WfhHrOverviewModel? get hrOverviewData => _overviewData;

  Future<WfhHrOverviewModel?> fetchWfhReportingPersonOverview(
      int requestId) async {
    try {
      isloading = true;
      notifyListeners();

      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");

      final String url =
          '${wfhRequestsHrOverviewUrl.toString()}$requestId/overview/';

      final response = await _dio.get(
        url,
        options: Options(
          headers: {
            "Authorization": "Bearer $auth",
            "Content-Type": "application/json",
          },
        ),
      );
      isloading = false;
      notifyListeners();
      log('fetchWfhReportingPersonOverview => ${jsonEncode(response.data)} - ${response.realUri} - $auth');
      if (response.statusCode == 200 && response.data != null) {
        _overviewData = WfhHrOverviewModel.fromJson(response.data['data']);
        notifyListeners();
        return _overviewData;
      }
    } catch (e) {
      print(e);
      isloading = false;
      notifyListeners();
    }
    return null;
  }
}
