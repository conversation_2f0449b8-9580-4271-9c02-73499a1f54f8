// import 'package:e8_hr_portal/util/size_config.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:e8_hr_portal/util/styles.dart';
// import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
// import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
// import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
// import 'package:provider/provider.dart';


// class EmployeeWfhhistory extends StatefulWidget {
//   final int employeeID;
//   const EmployeeWfhhistory({required this.employeeID, super.key});
//   @override
//   State<EmployeeWfhhistory> createState() => _EmployeeWfhhistoryState();
// }

// class _EmployeeWfhhistoryState extends State<EmployeeWfhhistory> {
//   @override
//   void initState() {
//     LeaveApplicationProvider provider =
//         Provider.of<LeaveApplicationProvider>(context, listen: false);
//     provider.initPage(context: context, employeeID: widget.employeeID);

//     WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
//       EasyLoading.show();
//       await provider.refreshPage(context: context);
//       EasyLoading.dismiss();
//     });
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return CustomScaffold(
//       screenTitle: "Leave History",
//       body: Column(
//         children: [
//           Expanded(
//             child: Consumer<LeaveApplicationProvider>(
//               builder: (context, provider, _) {
//                 return PagedListView.separated(
//                   physics: const BouncingScrollPhysics(),
//                   shrinkWrap: true,
//                   padding: EdgeInsets.only(top: h * 15, bottom: h * 60),
//                   pagingController: provider.pagingController,
//                   builderDelegate:
//                       PagedChildBuilderDelegate<EmployeeLeaveHostory>(
//                     noItemsFoundIndicatorBuilder: (_) {
//                       return const Text("No Data Found");
//                     },
//                     // noMoreItemsIndicatorBuilder: (context) => const Text("End"),
//                     newPageProgressIndicatorBuilder: (_) {
//                       return Center(
//                         child: CircularProgressIndicator(
//                             color: ThemeColors.color06AA37),
//                       );
//                     },
//                     firstPageProgressIndicatorBuilder: (_) {
//                       return const SizedBox();
//                     },
//                     itemBuilder: (context, item, index) {
//                       String? reason = item.reason;
//                       num? dayCount = item.dayCount;
//                       String? startDate = item.startDate;
//                       String? endDate = item.endDate;
//                       String? status = item.status;
//                       String? leaveType = item.leaveType;
//                       return _leaveTile(
//                           dayCount: dayCount ?? 0,
//                           endDate: endDate ?? "",
//                           reason: reason ?? "",
//                           startDate: startDate ?? "",
//                           status: status ?? "",
//                           leaveType: leaveType ?? "");
//                     },
//                   ),
//                   separatorBuilder: (context, index) =>
//                       SizedBox(height: h * 10),
//                 );
//               },
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _leaveTile({
//     required String reason,
//     required num dayCount,
//     required String startDate,
//     required String endDate,
//     required String status,
//     required String leaveType,
//   }) {
//     String stat = status.toString().toLowerCase().trim();
//     Color backGround = ThemeColors.color32936F.withOpacity(0.16);
//     TextStyle textStyle = tsS12w600c949494;
//     Image statusImage = Image.asset(
//       "assets/icons/calendar-2.png",
//       color: ThemeColors.primaryColor,
//     );
//     switch (leaveType.toLowerCase().trim()) {
//       case "unpaid leave":
//         statusImage = Image.asset(
//           "assets/icons/calculator.png",
//           color: ThemeColors.primaryColor,
//         );
//         break;
//       case "annual leave":
//         statusImage = Image.asset(
//           "assets/icons/calendar-2.png",
//           color: ThemeColors.primaryColor,
//         );
//         break;
//       case "sick leave":
//         statusImage = Image.asset(
//           "assets/icons/hospital.png",
//           color: ThemeColors.primaryColor,
//         );
//         break;
//     }
//     switch (stat) {
//       case "approved":
//         backGround = ThemeColors.color32936F.withOpacity(0.16);
//         textStyle = tsS12w600c949494;
//         break;
//       case "pending":
//         backGround = const Color(0xffFFF2E2);
//         textStyle = tsS12w600E5B900;
//         break;
//       case 'rejected':
//         backGround = ThemeColors.colorF64D44.withOpacity(0.15);
//         textStyle = tsS12w600F64D44;
//         break;
//       case 'in-progress':
//         backGround = const Color(0xffFFF2E2);
//         textStyle = tsS12w600E5B900;
//         break;
//       case 'cancelled':
//         backGround = ThemeColors.colorF64D44.withOpacity(0.15);
//         textStyle = tsS12w600F64D44;
//         break;
//       case 'expired':
//         backGround = ThemeColors.colorF64D44.withOpacity(0.15);
//         textStyle = tsS12w600F64D44;
//         break;
//     }
//     return Container(
//       height: h * 66,
//       width: w * 343,
//       padding: EdgeInsets.only(right: w * 11, left: w * 10),
//       decoration: BoxDecoration(
//         color: ThemeColors.colorFFFFFF,
//         borderRadius: BorderRadius.circular(8),
//       ),
//       child: Row(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Expanded(
//             child: Row(
//               children: [
//                 Container(
//                   height: 45,
//                   width: 45,
//                   margin: EdgeInsets.only(right: w * 10),
//                   padding: const EdgeInsets.all(11),
//                   decoration: BoxDecoration(
//                     shape: BoxShape.circle,
//                     color: ThemeColors.colorF8F8F8,
//                   ),
//                   child: statusImage,
//                 ),
//                 Column(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       leaveType,
//                       style: tsS12w500c181818,
//                     ),
//                     SizedBox(height: h * 7),
//                     Row(
//                       children: [
//                         Image.asset("assets/icons/calendar_green.png",
//                             color: ThemeColors.primaryColor,
//                             height: h * 10,
//                             width: w * 10),
//                         SizedBox(width: w * 4),
//                         Text(
//                           "$startDate - $endDate • $dayCount ${dayCount <= 1 ? "Day" : "Days"}",
//                           style: tsS10w400c979797,
//                         ),
//                       ],
//                     ),
//                   ],
//                 )
//               ],
//             ),
//           ),
//           Container(
//             height: h * 23,
//             width: w * 74,
//             alignment: Alignment.center,
//             margin: EdgeInsets.only(top: h * 12),
//             decoration: BoxDecoration(
//               color: backGround,
//               borderRadius: BorderRadius.circular(8),
//             ),
//             child: Text(
//               status,
//               style: textStyle,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
