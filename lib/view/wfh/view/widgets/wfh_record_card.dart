// ignore_for_file: use_build_context_synchronously

import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_overview_model.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_record_model.dart';
import 'package:e8_hr_portal/view/wfh/provider/wfh_overview_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../../../util/page_navigator.dart';
import '../../../../util/size_config.dart';
import '../../../../util/styles.dart';
import '../wfh_overview_screen.dart';

class WfhRecordCard extends StatelessWidget {
  final WFHRecordsModel item;
  const WfhRecordCard({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    List<ReportingPersonList> reportingPersonList =
        item.reportingPersonList ?? [];
    HrStatus? hrStatus = item.hrStatus;
    LeaveDetails data = item.leaveDetails ?? LeaveDetails();
    String createdAt = data.createdAt ?? '';
    String endDate = data.endDate ?? '';
    String leaveLevel = data.leaveLevel ?? '';
    List<ReportingPersonsActions> reportingPersonsActions =
        data.reportingPersonsActions ?? [];
    String startDate = data.startDate ?? '';
    String status = data.status ?? 'Pending';
    UserDetails userDetails = data.userDetails ?? UserDetails();
    String wfhType = data.wfhType ?? '';

    Color statusTileColor = ThemeColors.colorFFF2EF;
    TextStyle statusTileStyle = tsS12W6FE5B900;
    switch (status) {
      case 'Pending':
        statusTileColor = ThemeColors.colorFFF2EF;
        statusTileStyle = tsS12W6FE5B900;
        break;
      case 'Rejected':
        statusTileColor = ThemeColors.colorF64D44.withOpacity(0.14);
        statusTileStyle = tsS12w600cF64D44;
        break;
      case 'Approved':
        statusTileColor = ThemeColors.color03AD9E.withOpacity(0.10);
        statusTileStyle = tsS12w600c519C66;
        break;
      case 'Expired':
        statusTileColor = ThemeColors.colorF64D44.withOpacity(0.14);
        statusTileStyle = tsS12w600cF64D44;
        break;
      case 'Holiday':
        statusTileColor = ThemeColors.colorF64D44.withOpacity(0.14);
        statusTileStyle = tsS12w600cF64D44;
        break;
      case 'Cancelled':
        statusTileColor = ThemeColors.colorF64D44.withOpacity(0.14);
        statusTileStyle = tsS12w600cF64D44;
        break;
      case 'In-progress':
        statusTileColor = ThemeColors.colorFFF2EF;
        statusTileStyle = tsS12W6FE5B900;
        break;
    }
    String leaveTypeIcon = 'assets/icons/calendar-2.png';
    switch (wfhType.toString().toLowerCase().trim()) {
      case 'temporary':
        leaveTypeIcon = 'assets/icons/calendar-2.png';
        break;
      case 'regular':
        leaveTypeIcon = 'assets/icons/calculator.png';
        break;
    }
    return InkWell(
      onTap: () async {
        final provid = Provider.of<WfhOverviewProvider>(context, listen: false);
        EasyLoading.show();
        await provid.fetchOverview(item.leaveDetails?.id ?? 0);
        PageNavigator.push(
            context: context, route: WfhOverviewScreen(item: item));
        EasyLoading.dismiss();
        // final provid =
        //     Provider.of<LeaveApplicationProvider>(context, listen: false);
        // EasyLoading.show();
        // bool isGo = await provid.getLeaveOverView(leaveId: leaveId);
        // bool isGetRepPerson = await provid.getReportingPerson();
        // EasyLoading.dismiss();
        // if (isGo && isGetRepPerson) {
        //   PageNavigator.push(context: context, route: const OverViewScreen());
        // }
      },
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(8)),
        height: 100 * h,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 45 * h,
                    width: 45 * w,
                    margin: const EdgeInsets.only(right: 10),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: ThemeColors.colorFCC400.withOpacity(0.10),
                    ),
                    child: Center(
                      child: ImageIcon(
                        AssetImage(leaveTypeIcon),
                        color: ThemeColors.colorFCC400,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Align(
                          alignment: Alignment.topLeft,
                          child: Text(wfhType, style: tsS14w500Black),
                        ),
                        const SizedBox(
                          height: 7,
                        ),
                        item.leaveDetails?.wfhType?.toLowerCase() == "regular"
                            ? SizedBox(height: 5)
                            : Row(
                                children: [
                                  ImageIcon(
                                    const AssetImage(
                                        'assets/icons/calendar-3.png'),
                                    color: ThemeColors.colorFCC400,
                                    size: 12,
                                  ),
                                  const SizedBox(width: 5),
                                  Text(
                                    '$startDate - $endDate',
                                    style: tsS10w400979797,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                        SizedBox(height: h * 3.52),
                        Row(
                          children: [
                            if (reportingPersonList.isNotEmpty)
                              _reportingPersonsWidget(
                                  item: reportingPersonList),
                            if (hrStatus != null)
                              _hrApprovalWidget(
                                  profilePic: hrStatus.profilePic ?? '',
                                  name: hrStatus.name ?? '',
                                  isApprove: hrStatus.isApprove ?? ''),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                SizedBox(height: h * 8),
                Container(
                  padding: const EdgeInsets.fromLTRB(10, 4, 10, 4),
                  decoration: BoxDecoration(
                      color: statusTileColor,
                      borderRadius: BorderRadius.circular(8)),
                  child: Text(status, style: statusTileStyle),
                ),
                SizedBox(height: h * 5),
                Text(
                  createdAt,
                  overflow: TextOverflow.ellipsis,
                  style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w400,
                      color: const Color(0xff979797)),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _hrApprovalWidget(
      {required String profilePic,
      required String name,
      required String isApprove}) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: ClipRRect(
              borderRadius: BorderRadius.circular(5.0),
              child: ClipOval(
                child: Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: profilePic.toString(),
                      width: 30 * w,
                      height: 30 * h,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) {
                        return Container(
                          height: 30,
                          width: 30,
                          decoration: BoxDecoration(
                            color: ThemeColors.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            name.substring(0, 1).toUpperCase(),
                            style: GoogleFonts.rubik(
                                fontSize: 22,
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                          ),
                        );
                      },
                    ),
                    if (isApprove.toLowerCase().trim() == 'pending')
                      Container(
                        height: 30 * h,
                        width: 30 * w,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                  ],
                ),
              )),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: Column(
            children: [
              if (isApprove.toLowerCase().trim() == 'pending') const SizedBox(),
              if (isApprove.toLowerCase().trim() == 'true')
                ImageIcon(
                  const AssetImage(
                    'assets/icons/tick.png',
                  ),
                  size: 13,
                  color: ThemeColors.color06AA37,
                ),
              if (isApprove.toLowerCase().trim() == 'false')
                ImageIcon(
                  const AssetImage(
                    'assets/icons/close_red.png',
                  ),
                  size: 13,
                  color: ThemeColors.colorB80000,
                ),
            ],
          ),
        )
      ],
    );
  }

  Widget _reportingPersonsWidget({required List<ReportingPersonList> item}) {
    return Row(
      children: item.map((e) {
        return Stack(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(5.0),
                  child: ClipOval(
                    child: Stack(
                      children: [
                        CachedNetworkImage(
                          imageUrl: e.profilePic.toString(),
                          width: 30 * w,
                          height: 30 * h,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) {
                            return Container(
                              height: 30,
                              width: 30,
                              decoration: BoxDecoration(
                                color: ThemeColors.primaryColor,
                                shape: BoxShape.circle,
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                e.name?.substring(0, 1).toUpperCase() ?? '',
                                style: GoogleFonts.rubik(
                                    fontSize: 22,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.white),
                              ),
                            );
                          },
                        ),
                        if (e.isApprove?.toLowerCase().trim() == 'pending')
                          Container(
                            height: 30 * h,
                            width: 30 * w,
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                      ],
                    ),
                  )),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              child: Column(
                children: [
                  if (e.isApprove?.toLowerCase().trim() == 'pending')
                    const SizedBox(),
                  if (e.isApprove?.toLowerCase().trim() == 'true')
                    ImageIcon(
                      const AssetImage(
                        'assets/icons/tick.png',
                      ),
                      size: 13,
                      color: ThemeColors.color06AA37,
                    ),
                  if (e.isApprove?.toLowerCase().trim() == 'false')
                    ImageIcon(
                      const AssetImage(
                        'assets/icons/close_red.png',
                      ),
                      size: 13,
                      color: ThemeColors.colorB80000,
                    ),
                ],
              ),
            )
          ],
        );
      }).toList(),
    );
  }
}
