import 'package:e8_hr_portal/provider/wfh_provider.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_record_model.dart';
import 'package:e8_hr_portal/view/wfh/view/widgets/wfh_record_card.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';

class WfhRecordsScreen extends StatefulWidget {
  static const route = 'wfh_records_screen';
  final WfhTypeModel? wfhTypeModel;
  const WfhRecordsScreen({super.key, this.wfhTypeModel});

  @override
  State<WfhRecordsScreen> createState() => _WfhRecordsScreenState();
}

class _WfhRecordsScreenState extends State<WfhRecordsScreen> {
  late WFHProvider _provider;

  @override
  void initState() {
    super.initState();
    _provider = context.read<WFHProvider>();
    if (widget.wfhTypeModel == null) {
      _provider.selectedWFHType = _provider.wfhTypes.first;
    } else {
      _provider.selectedWFHType = widget.wfhTypeModel!;
    }
    _provider.selectedWFHRequestStatusType = _provider.wfhStatusList.first;
    _provider.currentPage = 0;
    _provider.initWFHRecordsPagination();
  }

  @override
  dispose() {
    _provider.whfPaginationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: 'WFH Records',
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () {
            _provider.currentPage = 0;
            _provider.whfPaginationController.refresh();
            return Future.delayed(Duration(microseconds: 1));
          },
          child: Column(
            children: [
              SizedBox(height: 15 * h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Align(
                    alignment: Alignment.topLeft,
                    child: Text("WFH Records", style: tsS16w500),
                  ),
                  const Spacer(),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.fromLTRB(10, 5, 5, 5),
                      width: 90 * w,
                      height: 30 * h,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Consumer<WFHProvider>(
                        builder: (context, provider, _) {
                          return DropdownButton(
                            isExpanded: true,
                            underline: const SizedBox(),
                            value: provider.selectedWFHRequestStatusType,
                            items: provider.wfhStatusList.map((item) {
                              return DropdownMenuItem<WfhTypeModel>(
                                value: item,
                                child: FittedBox(
                                  child: Text(item.name),
                                ),
                              );
                            }).toList(),
                            onChanged: (WfhTypeModel? value) {
                              if (value != null) {
                                provider.onChangedWFHRequestStatusType(
                                    item: value);
                                provider.currentPage = 0;
                                provider.whfPaginationController.refresh();
                              }
                            },
                          );
                        },
                      ),
                    ),
                  ),
                  SizedBox(width: 10),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.fromLTRB(10, 5, 5, 5),
                      width: 90 * w,
                      height: 30 * h,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Consumer<WFHProvider>(
                        builder: (context, provider, _) {
                          return DropdownButton(
                            isExpanded: true,
                            underline: const SizedBox(),
                            value: provider.selectedWFHType,
                            items: provider.wfhTypes.map((item) {
                              return DropdownMenuItem<WfhTypeModel>(
                                value: item,
                                child: FittedBox(
                                  child: Text(item.name),
                                ),
                              );
                            }).toList(),
                            onChanged: (WfhTypeModel? value) {
                              if (value != null) {
                                provider.onChangedWFHType(item: value);
                                provider.currentPage = 0;
                                provider.whfPaginationController.refresh();
                              }
                            },
                          );
                        },
                      ),
                    ),
                  )
                ],
              ),
              Expanded(
                child: PagedListView.separated(
                    padding: EdgeInsets.only(top: 30, bottom: 30),
                    pagingController: _provider.whfPaginationController,
                    builderDelegate: PagedChildBuilderDelegate<WFHRecordsModel>(
                      itemBuilder: (context, item, index) {
                        return WfhRecordCard(item: item);
                      },
                      noItemsFoundIndicatorBuilder: (context) {
                        return const Center(
                          child: Text(
                            'No WFH Found',
                            style: TextStyle(color: Colors.black, fontSize: 18),
                          ),
                        );
                      },
                      firstPageProgressIndicatorBuilder: (context) {
                        return const Center(
                          child: CircularProgressIndicator.adaptive(),
                        );
                      },
                    ),
                    separatorBuilder: (context, index) =>
                        const SizedBox(height: 15)),
              ),

              // Expanded(
              //   child: Consumer<WFHProvider>(
              //     builder: (context, provider, _) {
              //       if (provider.isFetching && provider.wfhRecords.isEmpty) {
              //         return Center(child: CircularProgressIndicator.adaptive());
              //       }
              //       if (!provider.isFetching && provider.wfhRecords.isEmpty) {
              //         return Center(child: Text('No Data Found'));
              //       }
              //       return ListView.separated(
              //         controller: provider.scrollController,
              //         padding: EdgeInsets.only(top: 30, bottom: 30),
              //         itemCount: provider.wfhRecords.length + 1,
              //         itemBuilder: (context, index) {
              //           if (index == provider.wfhRecords.length) {
              //             return provider.isFetching &&
              //                     provider.wfhRecords.isNotEmpty
              //                 ? Center(child: CircularProgressIndicator())
              //                 : SizedBox();
              //           }

              //           return WfhRecordCard(item: provider.wfhRecords[index]);
              //         },
              //         separatorBuilder: (context, index) => SizedBox(height: 8),
              //       );
              //     },
              //   ),
              // ),
              // Expanded(
              //   child: Consumer<WFHProvider>(
              //     builder: (context, provider, _) {
              //       return PagedListView.separated(
              //         physics: const ClampingScrollPhysics(),
              //         shrinkWrap: true,
              //         padding: EdgeInsets.only(top: h * 15, bottom: h * 75),
              //         pagingController: provider.pagingControllerLeaveRecords!,
              //         builderDelegate: PagedChildBuilderDelegate<WFHRecordsModel>(
              //           noItemsFoundIndicatorBuilder: (_) {
              //             return const Padding(
              //               padding: EdgeInsets.only(top: 80.0),
              //               child: Center(child: Text("No Data Found")),
              //             );
              //           },
              //           // noMoreItemsIndicatorBuilder: (context) => const Text("End"),
              //           newPageProgressIndicatorBuilder: (_) {
              //             return Center(
              //               child: CircularProgressIndicator(
              //                   color: ThemeColors.color06AA37),
              //             );
              //           },
              //           firstPageProgressIndicatorBuilder: (_) {
              //             return const SizedBox();
              //           },
              //           itemBuilder: (context, data, index) {
              //             // List<Data>? filteredList =
              //             //     provider.empLeaveDetailesRecords?.where((e) {
              //             //   return e.status == provider.selectedTypeOfLeave;
              //             // }).toList();

              //             return LeaveApplicationCard(
              //               createDate: data.leaveDetails?.createdAt,
              //               leaveId: data.leaveDetails?.id,
              //               fromLeaveDay: data.leaveDetails?.startDate,
              //               toleaveDay: data.leaveDetails?.endDate.toString(),
              //               reason: data.leaveDetails?.createdAt.toString(),
              //               endDate: data.leaveDetails?.endDate.toString(),
              //               topDate: 'leaveDate'.toString(),
              //               date: data.leaveDetails?.startDate,
              //               leaveDay: 1,
              //               noOfDays: '0',
              //               leaveType: data.leaveDetails?.createdAt,
              //               showDate: false,
              //               status: data.leaveDetails?.status.toString(),
              //               leaveID: data.leaveDetails?.id ?? 0,
              //               dayType: "Full Day",
              //               dayCount: 1,
              //               reportingPersonList: [],
              //               hrStatus: HrStatus(),
              //             );
              //           },
              //         ),
              //         separatorBuilder: (context, index) =>
              //             SizedBox(height: h * 10),
              //       );
              //     },
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
