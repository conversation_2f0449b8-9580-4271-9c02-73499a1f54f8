// ignore_for_file: use_build_context_synchronously

import 'dart:developer';
import 'dart:io';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/provider/wfh_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_overview_model.dart';
import 'package:e8_hr_portal/view/wfh/provider/wfh_overview_provider.dart';
import 'package:e8_hr_portal/view/wfh/view/wfh_records_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../../helper/textfield_widget.dart';
import '../../../util/colors.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../widgets/custom_scaffold.dart';
import '../../widgets/general_button.dart';
import '../../widgets/hisense_drop_down_tile.dart';

class ApplyWfhScreen extends StatefulWidget {
  final WfhOverviewModel? item;
  static const route = '/apply_wfh_screen';
  const ApplyWfhScreen({super.key, this.item});

  @override
  State<ApplyWfhScreen> createState() => _ApplyWfhScreenState();
}

class _ApplyWfhScreenState extends State<ApplyWfhScreen> {
  late WFHProvider _provider;
  late ProfileProvider _profileProvider;
  late WfhOverviewProvider _wfhOverviewProvider;
  final _formkey = GlobalKey<FormState>();
  final TextEditingController _fromController = TextEditingController();
  final TextEditingController _toController = TextEditingController();
  final TextEditingController _remarkController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _provider = context.read<WFHProvider>();
    _profileProvider = context.read<ProfileProvider>();
    _wfhOverviewProvider = context.read<WfhOverviewProvider>();

    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) async {
        if (widget.item != null) {
          WfhOverviewModel? item = await _wfhOverviewProvider
              .fetchOverview(widget.item?.leaveDetails?.id ?? 0);
          LeaveDetails? leaveDetails = item?.leaveDetails;
          String? startDate = leaveDetails?.startDate;
          String? endDate = leaveDetails?.endDate;
          _provider.selectedWFHType = _provider.wfhTypes.firstWhere((element) =>
              element.name.toLowerCase() ==
              item?.leaveDetails?.wfhType?.toLowerCase());
          if (startDate != null) {
            _fromController.text =
                formatDateFromString(startDate, 'dd MMM,yyyy', 'dd-MM-yyyy');
          }
          if (endDate != null) {
            _toController.text =
                formatDateFromString(endDate, 'dd MMM,yyyy', 'dd-MM-yyyy');
          }

          _remarkController.text = item?.leaveDetails?.reason ?? '';
        } else {
          _provider.selectedWFHType = _provider.wfhTypes.first;
        }
        setState(() {});
      },
    );
  }

  @override
  dispose() {
    _fromController.dispose();
    _toController.dispose();
    _remarkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: widget.item == null ? 'Apply WFH' : 'Edit WFH',
      avoidBottom: false,
      body: Consumer<WfhOverviewProvider>(builder: (context, provider, _) {
        if (provider.isLoading) {
          return Center(child: CircularProgressIndicator());
        }
        return SafeArea(
          child: Form(
            key: _formkey,
            child: Column(
              children: [
                Expanded(
                  child: ListView(
                    children: [
                      SizedBox(height: h * 35),
                      Opacity(
                        opacity: widget.item == null ? 1 : 0.4,
                        child: Column(
                          children: [
                            _subTitile(subtitle: 'WFH Type', isMandatory: true),
                            Consumer<WFHProvider>(
                              builder: (context, provider, _) {
                                return HisenseDropdownTile(
                                  title: '',
                                  hintText: 'Select WFH type',
                                  hintStyle: tsS14w400454444,
                                  style: tsS14w400454444,
                                  validator: (value) {
                                    if (value == null) {
                                      return 'Select WFH type';
                                    }
                                    return null;
                                  },
                                  onChanged: widget.item == null
                                      ? (WfhTypeModel? value) {
                                          if (value != null) {
                                            provider.onChangedWFHType(
                                                item: value);
                                            if (provider.selectedWFHType.id ==
                                                provider.wfhTypes[1].id) {
                                              _toController.clear();
                                              _fromController.clear();
                                            }
                                          }
                                        }
                                      : null,
                                  value: provider.selectedWFHType,
                                  items: provider.wfhTypes.map((e) {
                                    return DropdownMenuItem<WfhTypeModel>(
                                      value: e,
                                      child: Text(e.name.toString()),
                                    );
                                  }).toList(),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      Consumer<WFHProvider>(
                        builder: (context, provider, _) {
                          if (provider.selectedWFHType ==
                              provider.wfhTypes[1]) {
                            return const SizedBox();
                          }
                          return Column(
                            children: [
                              SizedBox(height: h * 15),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _subTitile(
                                            subtitle: 'From',
                                            isMandatory: true),
                                        TextFieldWidget(
                                          readOnly: true,
                                          controller: _fromController,
                                          hintStyle: tsS12w400c9F9F9F,
                                          textStyle: tsS14w400454444,
                                          keyboardType: TextInputType.none,
                                          borderColor: ThemeColors.colorE3E3E3,
                                          hintText: 'Select From Date',
                                          contentPadding: EdgeInsets.symmetric(
                                              vertical: 4, horizontal: 10),
                                          validator: (v) {
                                            if (_fromController.text.isEmpty) {
                                              return 'Please select from date';
                                            }
                                            return null;
                                          },
                                          onTap: () async {
                                            DateTime now = DateTime.now();
                                            DateTime? picked = await _pickDate(
                                                initialDate: now,
                                                firstDate: now);
                                            if (picked != null) {
                                              _fromController.text =
                                                  formatDateFromDate(
                                                      dateTime: picked,
                                                      format: 'dd-MM-yyyy');
                                              // Clear toDate if already selected (to ensure user reselects it)
                                              _toController.clear();
                                            }
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _subTitile(
                                            subtitle: 'To', isMandatory: true),
                                        TextFieldWidget(
                                          readOnly: true,
                                          controller: _toController,
                                          hintStyle: tsS12w400c9F9F9F,
                                          textStyle: tsS14w400454444,
                                          keyboardType: TextInputType.none,
                                          borderColor: ThemeColors.colorE3E3E3,
                                          hintText: 'Select To Date',
                                          validator: (v) {
                                            if (_toController.text.isEmpty) {
                                              return 'Please select to date';
                                            }
                                            return null;
                                          },
                                          contentPadding: EdgeInsets.symmetric(
                                              vertical: 4, horizontal: 10),
                                          onTap: () async {
                                            print("TextField tapped");
                                            if (_fromController.text.isEmpty) {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(
                                                SnackBar(
                                                    content: Text(
                                                        "Please select From Date first")),
                                              );
                                              return;
                                            }

                                            // Parse the fromDate from the controller
                                            DateTime? fromDate =
                                                DateFormat('dd-MM-yyyy').parse(
                                                    _fromController.text);
                                            DateTime? picked = await _pickDate(
                                              initialDate: fromDate,
                                              firstDate: fromDate,
                                            );

                                            if (picked != null) {
                                              _toController.text =
                                                  formatDateFromDate(
                                                dateTime: picked,
                                                format: 'dd-MM-yyyy',
                                              );
                                            }
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          );
                        },
                      ),
                      SizedBox(height: h * 15),
                      _subTitile(subtitle: 'Remark', isMandatory: true),
                      TextFieldWidget(
                        controller: _remarkController,
                        hintStyle: tsS12w400c9F9F9F,
                        textStyle: tsS14w400454444,
                        textCapitalization: TextCapitalization.sentences,
                        // keyboardType: TextInputType.none,
                        borderColor: ThemeColors.colorE3E3E3,
                        hintText: 'Enter Remark',
                        maxLines: 5,
                        validator: (v) {
                          if (_remarkController.text.trim().isEmpty) {
                            return 'Please enter Remark';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      }),
      bottomNavigationBar: BottomAppBar(
        color: ThemeColors.colorF4F5FA,
        shadowColor: Colors.transparent,
        padding: EdgeInsets.only(
            bottom: Platform.isAndroid ? 20 : 0, top: 0, left: 16, right: 16),
        child: Consumer<WfhOverviewProvider>(
          builder: (context, provider, _) {
            return GeneralButton(
              height: h * 50,
              width: double.infinity,
              title: 'Submit',
              textStyle: tsS18w600cFFFFFF,
              onPressed: provider.isUpdatingWfh
                  ? null
                  : () async {
                      if (!_formkey.currentState!.validate()) return;

                      bool isSuccess = false;

                      if (widget.item != null &&
                          widget.item!.leaveDetails != null) {
                        final provid = Provider.of<WfhOverviewProvider>(context,
                            listen: false);
                        EasyLoading.show();
                        String? tempFromDate;
                        String? tempToDate;
                        if (_provider.selectedWFHType.id ==
                            _provider.wfhTypes.first.id) {
                          tempFromDate = formatDateFromString(
                              _fromController.text, 'dd-MM-yyyy', 'yyyy-MM-dd');
                          tempToDate = formatDateFromString(
                              _toController.text, 'dd-MM-yyyy', 'yyyy-MM-dd');
                        }
                        isSuccess = await provid.updateWfhRequest(
                          wfhId: widget.item!.leaveDetails!.id ?? 0,
                          fromDate: tempFromDate,
                          toDate: tempToDate,
                          remark: _remarkController.text,
                          wfhType: _provider.selectedWFHType.id.toString(),
                        );
                        log('refreshing 1----------------------------------- ');
                        if (isSuccess) {
                          log('refreshing 2----------------------------------- ');
                          // _provider.currentPage = 1;
                          // _provider.whfPaginationController.refresh();
                          _profileProvider.getProfileData(context: context);
                          PageNavigator.pushReplacement(
                              context: context,
                              route: WfhRecordsScreen(
                                  wfhTypeModel: _provider.selectedWFHType));
                        }
                        EasyLoading.dismiss();
                        await provid
                            .fetchOverview(widget.item?.leaveDetails?.id ?? 0);

                        Navigator.pop(context);
                      } else {
                        EasyLoading.show();
                        isSuccess = await _provider.postWfhApplication(
                            fromDate: _fromController.text,
                            toDate: _toController.text,
                            remark: _remarkController.text);
                        EasyLoading.dismiss();
                        // Navigator.of(context).popUntil((route) => route.isFirst);
                        if (isSuccess) {
                          // _provider.selectedWFHType = _provider.wfhTypes[1];
                          // _provider.currentPage = 1;
                          // _provider.whfPaginationController.refresh();

                          _profileProvider.getProfileData(context: context);
                          PageNavigator.pushReplacement(
                              context: context,
                              route: WfhRecordsScreen(
                                  wfhTypeModel: _provider.selectedWFHType));
                        }
                      }
                    },
            );
          },
        ),
      ),
    );
  }

  Future<DateTime?> _pickDate(
      {required DateTime initialDate, required DateTime firstDate}) async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: DateTime(3100),
    );
    return picked;
  }

  Widget _subTitile({required String subtitle, bool isMandatory = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: Row(
            children: [
              Text(subtitle, style: tsS14w400c30292F),
              if (isMandatory) Text('*', style: tsS14w400cFA0000),
            ],
          ),
        ),
        SizedBox(height: h * 4),
      ],
    );
  }

  Widget _calenderWidget(
      {required String subtitle,
      required bool isMandatory,
      required String date,
      required VoidCallback onTap}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _subTitile(subtitle: subtitle, isMandatory: isMandatory),
        Align(
          alignment: Alignment.centerLeft,
          child: GestureDetector(
            onTap: onTap,
            child: Container(
              height: h * 48,
              width: w * 164,
              padding: EdgeInsets.symmetric(horizontal: w * 11),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: ThemeColors.colorFFFFFF,
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: ThemeColors.colorE3E3E3, width: 1),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    date,
                    style: tsS14w400454444,
                  ),
                  Image.asset(
                    'assets/icons/calendar_black.png',
                    height: h * 18,
                    width: w * 18,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
