// ignore_for_file: use_build_context_synchronously
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_hr_overview_model.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_overview_model.dart';
import 'package:e8_hr_portal/view/wfh/provider/wfh_overview_provider.dart';
import 'package:e8_hr_portal/view/wfh/provider/wfh_requests_provider.dart';
import 'package:e8_hr_portal/view/wfh/view/wfh_requests_overview_screen_for_reporting_person.dart';
import 'package:e8_hr_portal/view/widgets/Wfh_reject_reason_dialog.dart';
import 'package:e8_hr_portal/view/widgets/wfh_accept_reason_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

class WFHRequestsScreen extends StatefulWidget {
  const WFHRequestsScreen({super.key});

  @override
  State<WFHRequestsScreen> createState() => _WFHRequestsScreenState();
}

class _WFHRequestsScreenState extends State<WFHRequestsScreen> {
  late WfhRequestsProvider provider;
  @override
  void initState() {
    provider = Provider.of<WfhRequestsProvider>(context, listen: false);

    provider.initPageLeaveRequests();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      provider.init();
    });
    super.initState();
  }

  @override
  void dispose() {
    // log("response inside == == ");

    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        provider.mainFiltereOfWfh = "Pending to me";
        provider.refreshWfhRequest();
      },
    );

    provider.pagingControllerWfhRequests?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // String? role = Provider.of<ProfileProvider>(context).userRole;

    return CustomScaffold(
      screenTitle: "Employee WFH Requests",
      horizontalPadding: w,
      body: Column(
        children: [
          SizedBox(height: h * 31),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: w * 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("WFH Requests", style: tsS18w500c181818),
                // _filterDropDownButton(),
              ],
            ),
          ),
          const SizedBox(height: 10),
          Consumer<WfhRequestsProvider>(
            builder: (context, leaveProvider, _) {
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: w * 16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(child: _filterMainDropDown()),
                    if (leaveProvider.mainFiltereOfWfh != "Pending to me") ...[
                      SizedBox(width: 20 * w),
                      Expanded(child: _filterDropDownButton()),
                    ]
                  ],
                ),
              );
            },
          ),
          SizedBox(height: h * 14),
          Expanded(
            child: Consumer<WfhRequestsProvider>(
              builder: (context, provider, _) {
                return PagedListView.separated(
                  physics: const BouncingScrollPhysics(),
                  padding: EdgeInsets.only(bottom: h * 60),
                  pagingController: provider.pagingControllerWfhRequests!,
                  builderDelegate:
                      PagedChildBuilderDelegate<WfhHrOverviewModel>(
                    noItemsFoundIndicatorBuilder: (context) {
                      return const Center(
                        child: Text("No WFH Requests",
                            style:
                                TextStyle(color: Colors.black, fontSize: 18)),
                      );
                    },
                    itemBuilder: (context, data, index) {
                      return _wfhRequestTile(data: data);
                    },
                  ),
                  separatorBuilder: (context, index) =>
                      SizedBox(height: w * 10),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _wfhRequestTile({required WfhHrOverviewModel data}) {
    final pro = Provider.of<WfhRequestsProvider>(context, listen: false);
    String? status = data.leaveDetails?.status;
    List<String> statusList = ["cancelled", "rejected", "approved", "expired"];
    if (!statusList.contains(status?.toLowerCase())) {
      return Slidable(
        key: ValueKey(data.leaveDetails?.id),
        endActionPane: ActionPane(
          openThreshold: 0.0001,
          extentRatio: 0.3,
          motion: const ScrollMotion(),
          children: (data.leaveDetails?.leavePermission == "allow" &&
                  (status?.toLowerCase() == "pending" ||
                      status?.toLowerCase() == "in-progress"))
              ? [
                  GestureDetector(
                    onTap: () {
                      pro.isApprovingFromSlidable = true;
                      _onWfhAccepted(data.leaveDetails?.id);
                    },
                    child: Container(
                      margin: EdgeInsets.only(left: w * 22),
                      width: 25,
                      child: Image.asset(
                        "assets/icons/check_mark.png",
                        height: 50,
                        width: 50,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      pro.isRejectingFromSlidable = true;
                      _onWfhRejected(data.leaveDetails?.id);
                    },
                    child: Container(
                      margin: EdgeInsets.only(left: w * 20),
                      width: 20,
                      child: Image.asset(
                        "assets/icons/cross_mark.png",
                        height: 50,
                        width: 50,
                      ),
                    ),
                  ),
                ]
              : [],
        ),
        child: _wfhRequestCard(data: data),
      );
    }
    return _wfhRequestCard(data: data);
  }

  Widget _filterMainDropDown() {
    return Container(
      padding: const EdgeInsets.fromLTRB(10, 5, 5, 5),
      width: 120 * w,
      height: 30 * h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Consumer<WfhRequestsProvider>(
        builder: (context, provider, _) {
          return DropdownButton(
            isExpanded: true,
            underline: const SizedBox(),
            value: provider.mainFiltereOfWfh,
            items: (LoginModel.isAdmin != null && LoginModel.isAdmin!)
                ? provider.mainFilterDataWfhHR.map((item) {
                    return DropdownMenuItem<String>(
                      value: item,
                      child: FittedBox(
                        child: Text(
                          item.toString(),
                        ),
                      ),
                    );
                  }).toList()
                : provider.mainFilterDataWfh.map((item) {
                    return DropdownMenuItem<String>(
                      value: item,
                      child: FittedBox(
                        child: Text(
                          item.toString(),
                        ),
                      ),
                    );
                  }).toList(),
            onChanged: (String? value) {
              if (value == null) return;
              provider.mainFiltereOfWfh = value;
              if (value.toLowerCase() == 'all') {
                provider.selectedFilter = provider.wfhFilterModel.firstWhere(
                    (element) => element.text.toLowerCase() == 'all',
                    orElse: () => LeaveRequestFilterModel('All', 1));
              } else if (value.toLowerCase() == 'pending to me') {
                provider.selectedFilter = provider.wfhFilterModel.firstWhere(
                    (element) => element.text.toLowerCase() == 'pending',
                    orElse: () => LeaveRequestFilterModel('All', 1));
              } else if (value.toLowerCase() == 'assigned to me') {
                provider.selectedFilter = provider.wfhFilterModel.firstWhere(
                    (element) => element.text.toLowerCase() == 'all',
                    orElse: () => LeaveRequestFilterModel('All', 1));
              }

              provider.refreshWfhRequest();
            },
          );
        },
      ),
    );
  }

  _onWfhAccepted(int? requestId) async {
    showDialog(
      context: context,
      builder: (context) {
        return WfhAcceptReasonDialog(leaveId: requestId ?? 0);
      },
    );
  }

  _onWfhRejected(int? requestId) async {
    if (requestId != null) {
      showDialog(
        context: context,
        builder: (context) {
          return WfhRejectReasonDialog(leaveId: requestId);
        },
      );
    }
    //    final leaveProvider =
    // Provider.of<LeaveApplicationProvider>(context, listen: false);
    // await leaveProvider.leaveAcceptOrReject(
    //     leaveId: requestId, acceptOrReject: 2, context: context);
    // await leaveProvider.getLeaveRequest();
    // await leaveProvider.getLeaveRequestReporting();
    // EasyLoading.dismiss();
  }

  Widget _wfhRequestCard({required WfhHrOverviewModel data}) {
    String? fromDate;
    String? toDate;
    String? createdAt;
    if (data.leaveDetails?.createdAt != null) {
      // createdAt = formatDateFromString(
      //     request.createdAt!, "yyyy-MM-ddThh:mm:ss", "MM EEE, yy");
      createdAt = data.leaveDetails?.createdAt;
    }
    if (data.leaveDetails?.startDate != null) {
      // DateTime dt = DateTime.parse(request.startDate.toString());
      // final DateFormat formatter = DateFormat('dd MMM yyyy');
      fromDate = data.leaveDetails?.startDate;
    }
    if (data.leaveDetails?.endDate != null) {
      // DateTime dt = DateTime.parse(request.endDate.toString());
      // final DateFormat formatter = DateFormat('dd MMM yyyy');
      toDate = data.leaveDetails?.endDate;
    }

    List<ReportingPersonList>? reportingPersonList = data.reportingPersonList;
    HrStatus? hrStatus = data.hrStatus;

    return GestureDetector(
      onTap: () async {
        print('from date --- $fromDate  to date --- $toDate');
        // final provider =
        //     Provider.of<LeaveApplicationProvider>(context, listen: false);
        // if (request.leaveDetails?.id != null) {
        //   EasyLoading.show();
        //   bool isGo = await provider.getRequestedLeaveOverviewForReportedPerson(
        //       leaveId: request.leaveDetails!.id!);

        //   EasyLoading.dismiss();
        //   if (isGo) {
        //     PageNavigator.pushSlideRight(
        //       context: context,
        //       route: const LeaveRequestOverView(),
        //     );
        //   }
        // }
        final provid = Provider.of<WfhOverviewProvider>(context, listen: false);
        EasyLoading.show();
        await provid
            .fetchWfhReportingPersonOverview(data.leaveDetails?.id ?? 0);
        PageNavigator.push(
            context: context,
            route: WfhRequestOverviewScreenForReportingPerson(data: data));
        EasyLoading.dismiss();
      },
      child: Container(
        width: w * 343,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        margin: EdgeInsets.symmetric(horizontal: w * 16),
        padding: const EdgeInsets.all(10.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Row(
                children: [
                  Container(
                    height: h * 45,
                    width: w * 45,
                    margin: EdgeInsets.only(right: w * 10),
                    decoration: BoxDecoration(
                        color: ThemeColors.primaryColor,
                        shape: BoxShape.circle,
                        image: data.leaveDetails?.userDetails?.profilePic
                                    .toString() !=
                                null
                            ? DecorationImage(
                                image: NetworkImage(
                                    "${data.leaveDetails?.userDetails?.profilePic.toString()}"),
                                fit: BoxFit.cover,
                              )
                            : null),
                    alignment: Alignment.center,
                    child: data.leaveDetails?.userDetails?.profilePic == null
                        ? Text(
                            '${data.leaveDetails?.userDetails?.name?.substring(0, 1).toUpperCase()}',
                            style: GoogleFonts.rubik(
                                fontSize: 22,
                                fontWeight: FontWeight.normal,
                                color: Colors.white),
                          )
                        : null,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${data.leaveDetails?.userDetails?.name} ",
                          style: tsS14w500c181818,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 3),
                              child: data.leaveDetails?.wfhType == 'Regular'
                                  ? SizedBox.shrink()
                                  : Image.asset(
                                      "assets/icons/calendar_green.png",
                                      height: h * 10,
                                      width: w * 10,
                                      color: ThemeColors.primaryColor,
                                    ),
                            ),
                            SizedBox(width: w * 4),
                            Expanded(
                              child: data.leaveDetails?.wfhType == 'Regular'
                                  ? SizedBox.shrink()
                                  : Text(
                                      '$fromDate - $toDate • ${_calculateDayCount(fromDate, toDate)} ${_calculateDayCount(fromDate, toDate) <= 1 ? 'Day' : 'Days'}',
                                      style: tsS12w400c979797,
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                    ),
                            ),
                          ],
                        ),
                        SizedBox(width: w * 4.52),
                        Row(
                          children: [
                            Image.asset("assets/icons/calendar-remove.png",
                                color: ThemeColors.primaryColor,
                                height: h * 10,
                                width: w * 10),
                            SizedBox(width: w * 4),
                            Text(
                              '${data.leaveDetails?.wfhType}',
                              style: tsS12w400c979797,
                            ),
                          ],
                        ),
                        SizedBox(height: h * 4.52),
                        Row(
                          children: [
                            if (reportingPersonList != null)
                              _reportingPersonsWidget(
                                  data: reportingPersonList),
                            if (hrStatus != null)
                              _hrApprovalWidget(
                                  profilePic: hrStatus.profilePic ?? "",
                                  name: hrStatus.name ?? "",
                                  isApprove: hrStatus.isApprove ?? ""),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            if (data.leaveDetails?.status != null)
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  _statusWidget(
                    status: data.leaveDetails?.status ?? "",
                  ),
                  SizedBox(height: h * 5),
                  Text(
                    createdAt ?? "12 Wed, 22",
                    style: tsS12w400979797,
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  int _calculateDayCount(String? from, String? to) {
    if (from == null || to == null) return 0;

    try {
      final format = DateFormat('dd MMM,yyyy');
      final fromDate = format.parse(from);
      final toDate = format.parse(to);
      return toDate.difference(fromDate).inDays + 1;
    } catch (e) {
      return 0;
    }
  }

  Widget _filterDropDownButton() {
    return Container(
      padding: const EdgeInsets.fromLTRB(10, 5, 5, 5),
      width: 120 * w,
      height: 30 * h,
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(6)),
      child: DropdownButtonHideUnderline(
        child: Consumer<WfhRequestsProvider>(
          builder: (context, provider, _) {
            return DropdownButton<LeaveRequestFilterModel>(
              borderRadius: BorderRadius.circular(6),
              icon: Icon(Icons.keyboard_arrow_down, size: w * 18),
              dropdownColor: Colors.white,
              isDense: true, isExpanded: true, //
              value: provider.selectedFilter,
              onChanged: (value) {
                provider.selectedFilter = value;
                provider.refreshWfhRequest();
              },
              items: provider.wfhFilterModel.map((item) {
                return DropdownMenuItem(
                  value: item,
                  child: Text(
                    item.text,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                );
              }).toList(),
            );
          },
        ),
      ),
    );
  }

  Widget _statusWidget({required String status}) {
    TextStyle? style;
    Color? color;
    switch (status) {
      case "Approved":
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
        break;
      case "Rejected":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case "Pending":
        {
          style = tsS12w600cE5B900;
          color = ThemeColors.colorFFF2E2;
        }
        break;
      case "In-progress":
        {
          style = tsS12W6FE5B900;
          color = ThemeColors.colorFFF2E2;
        }
        break;
      case "Cancelled":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case "Expired":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      default:
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      height: h * 23,
      // width: w * 74,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: color,
      ),
      child: Text(
        status,
        style: style,
      ),
    );
  }

  Widget _hrApprovalWidget(
      {required String profilePic,
      required String name,
      required String isApprove}) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: ClipRRect(
              borderRadius: BorderRadius.circular(5.0),
              child: ClipOval(
                child: Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: profilePic.toString(),
                      width: 30 * w,
                      height: 30 * h,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) {
                        return Container(
                          height: 30,
                          width: 30,
                          decoration: BoxDecoration(
                            color: ThemeColors.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            name.substring(0, 1).toUpperCase(),
                            style: GoogleFonts.rubik(
                                fontSize: 22,
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                          ),
                        );
                      },
                    ),
                    if (isApprove.toLowerCase().trim() == "pending")
                      Container(
                        height: 30 * h,
                        width: 30 * w,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                  ],
                ),
              )),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: Column(
            children: [
              if (isApprove.toLowerCase().trim() == "pending") const SizedBox(),
              if (isApprove.toLowerCase().trim() == "true")
                ImageIcon(
                  const AssetImage(
                    "assets/icons/tick.png",
                  ),
                  size: 13,
                  color: ThemeColors.color06AA37,
                ),
              if (isApprove.toLowerCase().trim() == "false")
                ImageIcon(
                  const AssetImage(
                    "assets/icons/close_red.png",
                  ),
                  size: 13,
                  color: ThemeColors.colorB80000,
                ),
            ],
          ),
        )
      ],
    );
  }

  Widget _reportingPersonsWidget({required List<ReportingPersonList>? data}) {
    return Row(
      children: data!.map((e) {
        print(e.isApprove?.toLowerCase().trim());
        return Stack(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(5.0),
                child: ClipOval(
                  child: Stack(
                    children: [
                      CachedNetworkImage(
                        imageUrl: e.profilePic.toString(),
                        width: 30 * w,
                        height: 30 * h,
                        fit: BoxFit.cover,
                        errorWidget: (context, url, error) {
                          return Container(
                            height: 30,
                            width: 30,
                            decoration: BoxDecoration(
                              color: ThemeColors.primaryColor,
                              shape: BoxShape.circle,
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              e.name?.substring(0, 1).toUpperCase() ?? "",
                              style: GoogleFonts.rubik(
                                  fontSize: 22,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white),
                            ),
                          );
                        },
                      ),
                      if (e.isApprove?.toLowerCase().trim() == "pending")
                        Container(
                          height: 30 * h,
                          width: 30 * w,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              child: Column(
                children: [
                  if (e.isApprove?.toLowerCase().trim() == "pending")
                    const SizedBox(),
                  if (e.isApprove?.toLowerCase().trim() == "true")
                    ImageIcon(
                      const AssetImage(
                        "assets/icons/tick.png",
                      ),
                      size: 13,
                      color: ThemeColors.color06AA37,
                    ),
                  if (e.isApprove?.toLowerCase().trim() == "false")
                    ImageIcon(
                      const AssetImage(
                        "assets/icons/close_red.png",
                      ),
                      size: 13,
                      color: ThemeColors.colorB80000,
                    ),
                ],
              ),
            )
          ],
        );
      }).toList(),
    );
  }
}

// class StatusWidget2 extends StatelessWidget {
//   final WfhRequestsModel? data;
//   const StatusWidget2({super.key, required this.data});

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       padding: const EdgeInsets.symmetric(
//         horizontal: 15.0,
//         vertical: 4,
//       ),
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(3),
//         color: data?.data?.first.leaveDetails?.leavePermission == "allow"
//             ? const Color(0xFFB9FFCE)
//             : const Color(0xFFFFECC1),
//       ),
//       child: Text(
//         data?.data?.first.leaveDetails?.leavePermission == "allow"
//             ? "Approved"
//             : "Rejected",
//         style: GoogleFonts.rubik(
//           fontSize: 10,
//           color: data?.data?.first.leaveDetails?.leavePermission == "allow"
//               ? const Color(0xFF06AA37)
//               : const Color(0xFFFFB100),
//         ),
//       ),
//     );
//   }
// }

void doNothing(BuildContext context) {}

// class StatusWidget1 extends StatelessWidget {
//   const StatusWidget1({
//     super.key,
//     required this.data,
//   });

//   final WfhRequestsModel? data;

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       padding: const EdgeInsets.symmetric(
//         horizontal: 15.0,
//         vertical: 4,
//       ),
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(3),
//         color: data?.data?.first.leaveDetails?.leavePermission == "allow"
//             ? const Color(0xFFB9FFCE)
//             : const Color(0xFFFFECC1),
//       ),
//       child: Text(
//         data?.data?.first.leaveDetails?.leavePermission == "allow"
//             ? "Approved"
//             : "Rejected",
//         style: GoogleFonts.rubik(
//           fontSize: 10,
//           color: data?.data?.first.leaveDetails?.leavePermission == "allow"
//               ? const Color(0xFF06AA37)
//               : const Color(0xFFFFB100),
//         ),
//       ),
//     );
//   }
// }

// class StatusWidget extends StatelessWidget {
//   const StatusWidget({
//     super.key,
//     required this.data,
//   });

//   final WfhRequestsModel? data;

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       padding: const EdgeInsets.symmetric(
//         horizontal: 15.0,
//         vertical: 4,
//       ),
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(3),
//         color: data?.data?.first.leaveDetails?.status == "Approved"
//             ? const Color(0xFFB9FFCE).withOpacity(0.10)
//             : data?.data?.first.leaveDetails?.status == "Pending"
//                 ? const Color(0xFFFFECC1).withOpacity(0.10)
//                 : data?.data?.first.leaveDetails?.status == "In-progress"
//                     ? const Color(0xFF5570F1).withOpacity(0.10)
//                     : data?.data?.first.leaveDetails?.status == "Cancelled"
//                         ? const Color(0xFFF64D44).withOpacity(0.10)
//                         : const Color(0xFFF64D44).withOpacity(0.10),
//       ),
//       child: Text(
//         data?.data?.first.leaveDetails?.status == "Approved"
//             ? "Approved"
//             : data?.data?.first.leaveDetails?.status == "Pending"
//                 ? "Pending"
//                 : data?.data?.first.leaveDetails?.status == "In-progress"
//                     ? "In-progress"
//                     : data?.data?.first.leaveDetails?.status == "Cancelled"
//                         ? "Cancelled"
//                         : "Rejected",
//         style: GoogleFonts.rubik(
//           fontSize: 10,
//           fontWeight: FontWeight.w600,
//           color: data?.data?.first.leaveDetails?.status == "Approved"
//               ? const Color.fromARGB(255, 12, 127, 47)
//               : data?.data?.first.leaveDetails?.status == "Pending"
//                   ? const Color.fromARGB(255, 235, 191, 88)
//                   : data?.data?.first.leaveDetails?.status == "In-progress"
//                       ? const Color(0xFF5570F1)
//                       : data?.data?.first.leaveDetails?.status == "Cancelled"
//                           ? const Color(0xFFF64D44)
//                           : const Color(0xFFF64D44),
//         ),
//       ),
//     );
//   }
// }

class ButtonRowWidget extends StatelessWidget {
  final int? id;
  const ButtonRowWidget({super.key, required this.id});

  @override
  Widget build(BuildContext context) {
    final leaveProvider =
        Provider.of<WfhRequestsProvider>(context, listen: false);
    return Row(
      children: [
        ElevatedButton(
          onPressed: () async {
            // await leaveProvider.leaveAcceptOrReject(
            //     leaveId: id!, acceptOrReject: 1, context: context);

            // await leaveProvider.getLeaveRequest();
            leaveProvider.refreshWfhRequest();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: ThemeColors.color06AA37,
          ),
          child: const Text('Approve'),
        ),
        const SizedBox(width: 10.0),
        ElevatedButton(
          onPressed: () async {
            // await leaveProvider.leaveAcceptOrReject(
            //     leaveId: id!, acceptOrReject: 2, context: context);

            // await leaveProvider.getLeaveRequest();
            leaveProvider.refreshWfhRequest();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: ThemeColors.colorF64D44,
          ),
          child: const Text('Reject'),
        )
      ],
    );
  }
}
