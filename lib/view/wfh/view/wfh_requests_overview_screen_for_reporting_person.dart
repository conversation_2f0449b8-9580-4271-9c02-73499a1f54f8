import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_hr_overview_model.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_overview_model.dart';
import 'package:e8_hr_portal/view/wfh/provider/wfh_overview_provider.dart';
import 'package:e8_hr_portal/view/widgets/Wfh_reject_reason_dialog.dart';
import 'package:e8_hr_portal/view/widgets/wfh_accept_reason_dialog.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';

class WfhRequestOverviewScreenForReportingPerson extends StatefulWidget {
  final WfhHrOverviewModel? data;
  static const route = 'wfh_overview_screen';

  const WfhRequestOverviewScreenForReportingPerson({super.key, this.data});

  @override
  State<WfhRequestOverviewScreenForReportingPerson> createState() =>
      _WfhRequestOverviewScreenForReportingPersonState();
}

class _WfhRequestOverviewScreenForReportingPersonState
    extends State<WfhRequestOverviewScreenForReportingPerson> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final provider = Provider.of<WfhOverviewProvider>(context, listen: false);
      provider
          .fetchWfhReportingPersonOverview(widget.data?.leaveDetails?.id ?? 0);
    });
  }

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<WfhOverviewProvider>(context);
    final hrOverviewData = provider.hrOverviewData;

    // HrStatus? hrStatus = item.hrStatus;
    LeaveDetails data = hrOverviewData?.leaveDetails ?? LeaveDetails();
    String? leavePermission = data.leavePermission;
    int? leaveId = data.id;
    log('leavePermission -> ${leavePermission}');
    String status = data.status ?? 'Pending';
    Color statusTileColor = ThemeColors.colorFFF2EF;
    TextStyle statusTileStyle = tsS12W6FE5B900;
    switch (status) {
      case 'Pending':
        statusTileColor = ThemeColors.colorFFF2EF;
        statusTileStyle = tsS12W6FE5B900;
        break;
      case 'Rejected':
        statusTileColor = ThemeColors.colorF64D44.withOpacity(0.14);
        statusTileStyle = tsS12w600cF64D44;
        break;
      case 'Approved':
        statusTileColor = ThemeColors.color03AD9E.withOpacity(0.10);
        statusTileStyle = tsS12w600c519C66;
        break;
      case 'Expired':
        statusTileColor = ThemeColors.colorF64D44.withOpacity(0.14);
        statusTileStyle = tsS12w600cF64D44;
        break;
      case 'Holiday':
        statusTileColor = ThemeColors.colorF64D44.withOpacity(0.14);
        statusTileStyle = tsS12w600cF64D44;
        break;
      case 'Cancelled':
        statusTileColor = ThemeColors.colorF64D44.withOpacity(0.14);
        statusTileStyle = tsS12w600cF64D44;
        break;
      case 'In-progress':
        statusTileColor = ThemeColors.colorFFF2EF;
        statusTileStyle = tsS12W6FE5B900;
        break;
    }

    // final provider =
    //     Provider.of<LeaveApplicationProvider>(context, listen: false);
    // LeaveDetails? leaveDetails = provider.leaveOverviewModel?.leaveDetails;
    String? dateCreated = hrOverviewData?.leaveDetails?.createdAt;
    String? createdAt = dateCreated;
    // String? dayType = item.leaveDetails?.dayType;
    // int? leaveID = item.leaveDetails?.id;

    // ignore: unused_local_variable
    String text = 'Pending';

    switch (status) {
      case 'Pending':
        text = 'Pending';
        break;
      case 'In-progress':
        text = 'In-progress';
        break;
      case 'Approved':
        text = 'Approved';
        break;
      case 'Rejected':
        text = 'Rejected';
        break;
      case 'Cancelled':
        text = 'Cancelled';
        break;
      case 'Expired':
        text = 'Expired';
        break;
      case 'Holiday':
        text = 'Holiday';
        break;
    }

    return HisenseScaffold(
      screenTitle: 'WFH Overview',
      // actions: [
      //  _requestNewButton(context: context, employeeId: employeeId ?? 0)
      // ],
      body: Consumer<WfhOverviewProvider>(
        builder: (context, provider, _) {
          final hrOverviewdata = provider.hrOverviewData;
          log(
            hrOverviewdata?.leaveDetails?.wfhType ?? '',
          );
          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(15.0),
                    child: hrOverviewdata == null
                        ? const SizedBox()
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Align(
                                alignment: Alignment.topLeft,
                                child: Text('WFH Overview', style: tsS16w500),
                              ),
                              const SizedBox(height: 15),
                              Container(
                                padding: const EdgeInsets.all(15),
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(12)),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          height: 45 * h,
                                          width: 45 * w,
                                          margin:
                                              const EdgeInsets.only(right: 10),
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: ThemeColors.colorFCC400
                                                .withOpacity(0.10),
                                          ),
                                          child: Center(
                                              child: ImageIcon(
                                                  const AssetImage(
                                                      'assets/icons/calendar-2.png'),
                                                  color:
                                                      ThemeColors.colorFCC400)),
                                        ),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            // mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                      hrOverviewdata
                                                              .leaveDetails
                                                              ?.wfhType ??
                                                          '',
                                                      style: tsS14w500Black),
                                                  Column(
                                                    children: [
                                                      Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .fromLTRB(
                                                                10, 4, 10, 4),
                                                        decoration:
                                                            BoxDecoration(
                                                          color:
                                                              statusTileColor,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8),
                                                        ),
                                                        child: Text(
                                                            hrOverviewdata
                                                                    .leaveDetails
                                                                    ?.status ??
                                                                '',
                                                            style:
                                                                statusTileStyle),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 7),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Expanded(
                                                    child: Row(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Padding(
                                                          padding:
                                                              EdgeInsets.only(
                                                                  top: h * 2),
                                                          child: hrOverviewdata
                                                                      .leaveDetails
                                                                      ?.wfhType ==
                                                                  "Regular"
                                                              ? SizedBox
                                                                  .shrink()
                                                              : ImageIcon(
                                                                  const AssetImage(
                                                                      'assets/icons/calendar-3.png'),
                                                                  color: ThemeColors
                                                                      .colorFCC400,
                                                                  size: 12),
                                                        ),
                                                        const SizedBox(
                                                            width: 5),
                                                        if (hrOverviewdata
                                                                    .leaveDetails
                                                                    ?.startDate !=
                                                                null &&
                                                            hrOverviewdata
                                                                    .leaveDetails
                                                                    ?.endDate !=
                                                                null)
                                                          Expanded(
                                                            child: Text(
                                                                '${hrOverviewdata.leaveDetails?.startDate.toString()} - ${hrOverviewdata.leaveDetails?.endDate.toString()}', //leavetype
                                                                style:
                                                                    tsS12w400979797,
                                                                overflow:
                                                                    TextOverflow
                                                                        .clip),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                  const SizedBox(width: 10),
                                                  Text(createdAt ?? '',
                                                      style: tsS10w400c4D4D4D),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: h * 14),
                                    Align(
                                      alignment: Alignment.topLeft,
                                      child: Text('Reason',
                                          style: tsS12w400979797),
                                    ),
                                    const SizedBox(height: 5),
                                    Align(
                                      alignment: Alignment.topLeft,
                                      child: Text(
                                          hrOverviewdata.leaveDetails?.reason ??
                                              '',
                                          style: tsS14w500Black),
                                    ),
                                    const SizedBox(height: 5),
                                    Divider(
                                        thickness: 1,
                                        color: ThemeColors.colorD9D9D9),

                                    // if (data.first.reportingPersonList!.isNotEmpty)
                                    Align(
                                        alignment: Alignment.topLeft,
                                        child: Text('Approved by / Rejected by',
                                            style: tsS12w400979797)),
                                    const SizedBox(height: 13),
                                    Row(
                                      children: [
                                        if (hrOverviewdata
                                                .reportingPersonList !=
                                            null)
                                          _reportingPersonsWidget(
                                              data: hrOverviewdata),
                                        if (hrOverviewdata.hrStatus != null)
                                          _hrApprovalWidget(
                                              profilePic: hrOverviewdata
                                                      .hrStatus?.profilePic ??
                                                  '',
                                              name: hrOverviewdata
                                                      .hrStatus?.name ??
                                                  '',
                                              isApprove: hrOverviewdata
                                                      .hrStatus?.isApprove ??
                                                  ''),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 20),
                              if (text.toLowerCase() == 'rejected')
                                _hrRejectStatusBuilder(hrOverviewdata),
                              if (hrOverviewdata.hrStatus?.comment != null)
                                const SizedBox(height: 20),
                              _rejectStatusBuilder(hrOverviewdata),
                              SizedBox(height: 66 * h)
                            ],
                          ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
      floatingWidget: (leavePermission == 'allow' &&
              (status == 'Pending' || status == 'In-progress'))
          ? Padding(
              padding: EdgeInsets.only(left: 27.0 * h),
              child: Row(
                children: [
                  Expanded(
                    child: GeneralButton(
                      title: 'Reject',
                      height: h * 50,
                      width: double.infinity,
                      // isDisabled: true,
                      textStyle: tsS18w500cFFFFFF,
                      isDisabledColor: true,
                      onPressed: () {
                        if (leaveId != null) {
                          showDialog(
                            context: context,
                            builder: (context) {
                              return WfhRejectReasonDialog(leaveId: leaveId);
                            },
                          );
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Consumer<WfhOverviewProvider>(
                      builder: (context, provider, _) {
                        // if (provider.isloading) {
                        //   return const Center(
                        //     child: CircularProgressIndicator.adaptive(),
                        //   );
                        // }

                        return GeneralButton(
                          title: 'Approve',
                          height: h * 50,
                          width: double.infinity,
                          textStyle: tsS18w500cFFFFFF,
                          onPressed: () {
                            if (leaveId != null) {
                              showDialog(
                                  context: context,
                                  builder: (context) =>
                                      WfhAcceptReasonDialog(leaveId: leaveId));
                            }
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            )
          : null,
    );
  }

  // Widget _hrRejectStatusBuilder(WfhOverviewModel data) {
  Widget _rejectStatusBuilder(WfhHrOverviewModel data) {
    if (data.reportingPersonList != null) {
      return ListView.separated(
        itemCount: data.reportingPersonList!.length,
        shrinkWrap: true,
        physics: const ScrollPhysics(),
        itemBuilder: (context, index) {
          ReportingPersonList? e = data.reportingPersonList?[index];

          if (['false', 'rejected', 'true']
                  .contains(e?.isApprove.toString().toLowerCase()) &&
              e?.comment != null) {
            return _wfhRejectedCard(
              comment: e?.comment ?? '',
              rejectedBy: e?.name ?? '',
              rejectedDate: e?.createdAt,
              appOrReject: e?.isApprove ?? '',
            );
          }
          return const SizedBox();
        },
        separatorBuilder: (context, index) {
          return SizedBox(height: h * 10);
        },
        // children: data.reportingPersonList!.map((e) {
        //   return _wfhRejectedCard(
        //       comment: e.comment ?? '', rejectedBy: e.name ?? '');
        // }).toList(),
      );
    }
    return const SizedBox();
  }

  Widget _hrRejectStatusBuilder(WfhHrOverviewModel data) {
    final hrStatus = data.hrStatus;
    if (['false', 'rejected', 'true']
            .contains(hrStatus?.isApprove.toString().toLowerCase()) &&
        hrStatus?.comment != null) {
      return _leaveRejectedCard(
        comment: hrStatus?.comment ?? '',
        rejectedBy: hrStatus?.name ?? '',
        rejectedDate: hrStatus?.createdAt ?? '',
        appOrReject: hrStatus?.isApprove ?? '',
      );
    }
    return const SizedBox();
  }

  Widget _leaveRejectedCard(
      {required String rejectedBy,
      required String comment,
      required String? rejectedDate,
      required String? appOrReject}) {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                height: 24,
                width: 24,
                margin: const EdgeInsets.only(right: 10),
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                    color: appOrReject?.toLowerCase() == 'false'
                        ? ThemeColors.colorFCD2D0
                        : ThemeColors.color06AA37.withOpacity(.2),
                    borderRadius: BorderRadius.circular(6)),
                child: Container(
                  decoration: BoxDecoration(
                      color: appOrReject?.toLowerCase() == 'false'
                          ? ThemeColors.colorF64D44
                          : ThemeColors.color06AA37,
                      shape: BoxShape.circle),
                  child: Center(
                    child: Icon(
                      appOrReject?.toLowerCase() == 'false'
                          ? Icons.close_rounded
                          : Icons.done_rounded,
                      color: Colors.white,
                      size: 10,
                    ),
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      appOrReject?.toLowerCase() == 'false'
                          ? 'WFH Rejected'
                          : 'WFH Approved',
                      style: appOrReject?.toLowerCase() == 'false'
                          ? tsS12w4cF64D44
                          : tsS12w4c06AA37,
                    ),
                  ),
                  SizedBox(
                    height: 15,
                    width: 280 * w,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: Text(
                            rejectedBy,
                            style: tsS10w400c646363,
                          ),
                        ),
                        SizedBox(
                          width: 10 * w,
                        ),
                        Expanded(
                          child: Text(
                            rejectedDate.toString(),
                            style: tsS10w400c646363,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          Divider(
            thickness: 1,
            color: ThemeColors.colorD9D9D9,
          ),
          const SizedBox(height: 12),
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              comment,
              style: tsS14w400979797,
            ),
          )
        ],
      ),
    );
  }

  // Widget _requestNewButton(
  //     {required BuildContext context, required int employeeId}) {
  //   return Padding(
  //     padding: EdgeInsets.symmetric(vertical: h * 15, horizontal: w * 10),
  //     child: InkWell(
  //       borderRadius: BorderRadius.circular(8),
  //       radius: 10,
  //       onTap: () async {
  //         PageNavigator.push(
  //           context: context,
  //           route: EmployeeWfhhistory(employeeID: employeeId),
  //         );
  //       },
  //       child: Padding(
  //         padding: EdgeInsets.only(top: h * 7, left: w * 10, right: w * 10),
  //         child: Text(
  //           'Leave History',
  //           style: tsS14w500cFFFFFF,
  //         ),
  //       ),
  //     ),
  //   );
  // }

  Widget _wfhRejectedCard(
      {required String rejectedBy,
      required String comment,
      required String? rejectedDate,
      required String? appOrReject}) {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                height: 24,
                width: 24,
                margin: const EdgeInsets.only(right: 10),
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                    color: appOrReject?.toLowerCase() == 'false'
                        ? ThemeColors.colorFCD2D0
                        : ThemeColors.color06AA37.withOpacity(.2),
                    borderRadius: BorderRadius.circular(6)),
                child: Container(
                  decoration: BoxDecoration(
                      color: appOrReject?.toLowerCase() == 'false'
                          ? ThemeColors.colorF64D44
                          : ThemeColors.color06AA37,
                      shape: BoxShape.circle),
                  child: Center(
                    child: Icon(
                        appOrReject?.toLowerCase() == 'false'
                            ? Icons.close_rounded
                            : Icons.done_rounded,
                        color: Colors.white,
                        size: 10),
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                        appOrReject?.toLowerCase() == 'false'
                            ? 'WFH Rejected'
                            : 'WFH Approved',
                        style: appOrReject?.toLowerCase() == 'false'
                            ? tsS12w4cF64D44
                            : tsS12w4c06AA37),
                  ),
                  SizedBox(
                    height: 15,
                    width: 280 * w,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: Text(rejectedBy, style: tsS10w400c646363),
                        ),
                        SizedBox(width: 10 * w),
                        Expanded(
                          child: Text(rejectedDate.toString(),
                              style: tsS10w400c646363),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          Divider(thickness: 1, color: ThemeColors.colorD9D9D9),
          const SizedBox(height: 12),
          Align(
            alignment: Alignment.topLeft,
            child: Text(comment, style: tsS14w400979797),
          )
        ],
      ),
    );
  }

  Widget _hrApprovalWidget(
      {required String profilePic,
      required String name,
      required String isApprove}) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: ClipRRect(
              borderRadius: BorderRadius.circular(5.0),
              child: ClipOval(
                child: Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: profilePic.toString(),
                      width: 45 * w,
                      height: 45 * h,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) {
                        return Container(
                          height: 50,
                          width: 50,
                          decoration: BoxDecoration(
                            color: ThemeColors.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            name.substring(0, 1).toUpperCase(),
                            style: GoogleFonts.rubik(
                                fontSize: 22,
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                          ),
                        );
                      },
                    ),
                    if (isApprove.toLowerCase().trim() == 'pending')
                      Container(
                        height: 45 * h,
                        width: 45 * w,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                  ],
                ),
              )),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: Column(
            children: [
              if (isApprove.toLowerCase().trim() == 'pending') const SizedBox(),
              if (isApprove.toLowerCase().trim() == 'true')
                ImageIcon(
                  const AssetImage('assets/icons/tick.png'),
                  size: 13,
                  color: ThemeColors.color06AA37,
                ),
              if (isApprove.toLowerCase().trim() == 'false')
                ImageIcon(
                  const AssetImage('assets/icons/close_red.png'),
                  size: 13,
                  color: ThemeColors.colorB80000,
                ),
            ],
          ),
        )
      ],
    );
  }

  Widget _reportingPersonsWidget({required WfhHrOverviewModel data}) {
    return Row(
      children: data.reportingPersonList!.map((e) {
        return Stack(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(5.0),
                child: ClipOval(
                  child: Stack(
                    children: [
                      CachedNetworkImage(
                        imageUrl: e.profilePic.toString(),
                        width: 45 * w,
                        height: 45 * h,
                        fit: BoxFit.cover,
                        errorWidget: (context, url, error) {
                          return Container(
                            height: 50,
                            width: 50,
                            decoration: BoxDecoration(
                              color: ThemeColors.primaryColor,
                              shape: BoxShape.circle,
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              e.name?.substring(0, 1).toUpperCase() ?? '',
                              style: GoogleFonts.rubik(
                                  fontSize: 22,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white),
                            ),
                          );
                        },
                      ),
                      if (e.isApprove?.toLowerCase().trim() == 'pending')
                        Container(
                            height: 45 * h,
                            width: 45 * w,
                            color: Colors.white.withValues(alpha: 0.8)),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              child: Column(
                children: [
                  if (e.isApprove?.toLowerCase().trim() == 'pending')
                    const SizedBox(),
                  if (e.isApprove?.toLowerCase().trim() == 'true')
                    ImageIcon(const AssetImage('assets/icons/tick.png'),
                        size: 13, color: ThemeColors.color06AA37),
                  if (e.isApprove?.toLowerCase().trim() == 'false')
                    ImageIcon(const AssetImage('assets/icons/close_red.png'),
                        size: 13, color: ThemeColors.colorB80000),
                ],
              ),
            )
          ],
        );
      }).toList(),
    );
  }
}
