import 'dart:developer';
import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/provider/wfh_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_overview_model.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_record_model.dart';
import 'package:e8_hr_portal/view/wfh/provider/wfh_overview_provider.dart';
import 'package:e8_hr_portal/view/wfh/view/apply_wfh_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/other_screens/overview/widgets/cancel_button.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';

class WfhOverviewScreen extends StatefulWidget {
  final WFHRecordsModel? item;
  static const route = 'wfh_overview_screen';

  const WfhOverviewScreen({super.key, this.item});

  @override
  State<WfhOverviewScreen> createState() => _WfhOverviewScreenState();
}

class _WfhOverviewScreenState extends State<WfhOverviewScreen> {
  late WfhOverviewProvider _provider;
  @override
  void initState() {
    super.initState();
    _provider = Provider.of<WfhOverviewProvider>(context, listen: false);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _provider.fetchOverview(widget.item?.leaveDetails?.id ?? 0);
    });
  }

  @override
  Widget build(BuildContext context) {
    WfhOverviewModel? wfhOverViewItem = _provider.overview;

    LeaveDetails data = wfhOverViewItem?.leaveDetails ?? LeaveDetails();
    HrStatus? hrStatus = wfhOverViewItem?.hrStatus;
    String status = data.status ?? 'Pending';

    Color statusTileColor = ThemeColors.colorFFF2EF;
    TextStyle statusTileStyle = tsS12W6FE5B900;
    switch (status) {
      case 'Pending':
        statusTileColor = ThemeColors.colorFFF2EF;
        statusTileStyle = tsS12W6FE5B900;
        break;
      case 'Rejected':
        statusTileColor = ThemeColors.colorF64D44.withOpacity(0.14);
        statusTileStyle = tsS12w600cF64D44;
        break;
      case 'Approved':
        statusTileColor = ThemeColors.color03AD9E.withOpacity(0.10);
        statusTileStyle = tsS12w600c519C66;
        break;
      case 'Expired':
        statusTileColor = ThemeColors.colorF64D44.withOpacity(0.14);
        statusTileStyle = tsS12w600cF64D44;
        break;
      case 'Holiday':
        statusTileColor = ThemeColors.colorF64D44.withOpacity(0.14);
        statusTileStyle = tsS12w600cF64D44;
        break;
      case 'Cancelled':
        statusTileColor = ThemeColors.colorF64D44.withOpacity(0.14);
        statusTileStyle = tsS12w600cF64D44;
        break;
      case 'In-progress':
        statusTileColor = ThemeColors.colorFFF2EF;
        statusTileStyle = tsS12W6FE5B900;
        break;
    }

    bool showCancelLeavebutton = false;
    // final provider =
    //     Provider.of<LeaveApplicationProvider>(context, listen: false);
    // LeaveDetails? leaveDetails = provider.leaveOverviewModel?.leaveDetails;
    String? dateCreated = wfhOverViewItem?.leaveDetails?.createdAt;
    String? createdAt = dateCreated;
    // String? dayType = item.leaveDetails?.dayType;
    // int? leaveID = item.leaveDetails?.id;

    String text = 'Pending';

    switch (status) {
      case 'Pending':
        text = 'Pending';
        break;
      case 'In-progress':
        text = 'In-progress';
        break;
      case 'Approved':
        text = 'Approved';
        break;
      case 'Rejected':
        text = 'Rejected';
        break;
      case 'Cancelled':
        text = 'Cancelled';
        break;
      case 'Expired':
        text = 'Expired';
        break;
      case 'Holiday':
        text = 'Holiday';
        break;
    }

    return HisenseScaffold(
      screenTitle: 'Overview',
      actions: [
        _requestNewButton(
            context: context,
            leaveDetails: wfhOverViewItem?.leaveDetails,
            text: text,
            wfhOverViewItem: wfhOverViewItem)
      ],
      body: Consumer<WfhOverviewProvider>(
        builder: (context, provider, _) {
          final wfhOverview = provider.overview;
          log(
            wfhOverview?.leaveDetails?.wfhType ?? '',
          );
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.error != null) {
            return Center(child: Text(provider.error!));
          }

          if (wfhOverViewItem == null) {
            return const Center(child: Text('No data available'));
          }
          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(15.0),
                    child: wfhOverview == null
                        ? const SizedBox()
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Align(
                                alignment: Alignment.topLeft,
                                child: Text('WFH  ', style: tsS16w500),
                              ),
                              const SizedBox(height: 15),
                              Container(
                                padding: const EdgeInsets.all(15),
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(12)),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          height: 45 * h,
                                          width: 45 * w,
                                          margin:
                                              const EdgeInsets.only(right: 10),
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: ThemeColors.colorFCC400
                                                .withOpacity(0.10),
                                          ),
                                          child: Center(
                                              child: ImageIcon(
                                            const AssetImage(
                                                'assets/icons/calendar-2.png'),
                                            color: ThemeColors.colorFCC400,
                                          )),
                                        ),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            // mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                      wfhOverview.leaveDetails
                                                              ?.wfhType ??
                                                          '',
                                                      style: tsS14w500Black),
                                                  Column(
                                                    children: [
                                                      Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .fromLTRB(
                                                                10, 4, 10, 4),
                                                        decoration: BoxDecoration(
                                                            color:
                                                                statusTileColor,
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        8)),
                                                        child: Text(
                                                            wfhOverview
                                                                    .leaveDetails
                                                                    ?.status ??
                                                                '',
                                                            style:
                                                                statusTileStyle),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 7),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Expanded(
                                                    child: Row(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        if (wfhOverview
                                                                    .leaveDetails
                                                                    ?.startDate !=
                                                                null &&
                                                            wfhOverview
                                                                    .leaveDetails
                                                                    ?.endDate !=
                                                                null)
                                                          Padding(
                                                            padding:
                                                                EdgeInsets.only(
                                                                    top: h * 2),
                                                            child: ImageIcon(
                                                                const AssetImage(
                                                                    'assets/icons/calendar-3.png'),
                                                                color: ThemeColors
                                                                    .colorFCC400,
                                                                size: 12),
                                                          ),
                                                        const SizedBox(
                                                            width: 5),
                                                        if (wfhOverview
                                                                    .leaveDetails
                                                                    ?.startDate !=
                                                                null &&
                                                            wfhOverview
                                                                    .leaveDetails
                                                                    ?.endDate !=
                                                                null)
                                                          Expanded(
                                                            child: Text(
                                                                '${wfhOverview.leaveDetails?.startDate.toString()} - ${wfhOverview.leaveDetails?.endDate.toString()}', //leavetype
                                                                style:
                                                                    tsS12w400979797,
                                                                overflow:
                                                                    TextOverflow
                                                                        .clip),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                  const SizedBox(width: 10),
                                                  Text(createdAt ?? '',
                                                      style: tsS10w400c4D4D4D),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: h * 14),
                                    Align(
                                      alignment: Alignment.topLeft,
                                      child: Text('Reason',
                                          style: tsS12w400979797),
                                    ),
                                    const SizedBox(height: 5),
                                    Align(
                                      alignment: Alignment.topLeft,
                                      child: Text(
                                          wfhOverview.leaveDetails?.reason ??
                                              '',
                                          style: tsS14w500Black),
                                    ),
                                    const SizedBox(height: 5),
                                    Divider(
                                        thickness: 1,
                                        color: ThemeColors.colorD9D9D9),

                                    // if (data.first.reportingPersonList!.isNotEmpty)
                                    Align(
                                      alignment: Alignment.topLeft,
                                      child: Text('Approved by / Rejected by',
                                          style: tsS12w400979797),
                                    ),
                                    const SizedBox(height: 13),

                                    Row(
                                      children: [
                                        if (wfhOverview.reportingPersonList !=
                                            null)
                                          _reportingPersonsWidget(
                                              data: wfhOverview),
                                        if (wfhOverview.hrStatus != null)
                                          _hrApprovalWidget(
                                              profilePic: wfhOverview
                                                      .hrStatus?.profilePic ??
                                                  '',
                                              name:
                                                  wfhOverview.hrStatus?.name ??
                                                      '',
                                              isApprove: wfhOverview
                                                      .hrStatus?.isApprove ??
                                                  ''),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 20),
                              if (text.toLowerCase() == 'rejected')
                                _hrRejectStatusBuilder(wfhOverViewItem),
                              if (hrStatus?.comment != null)
                                const SizedBox(height: 20),
                              _rejectStatusBuilder(wfhOverViewItem),
                              SizedBox(height: 66 * h)
                            ],
                          ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingWidget: Padding(
        padding: EdgeInsets.only(bottom: Platform.isAndroid ? 30 : 0),
        child: Consumer<WfhOverviewProvider>(
          builder: (context, provider, _) {
            final wfhOverviewItem = provider.overview;
            String? status = wfhOverviewItem?.leaveDetails?.status;
            // status = 'Cancelled';
            String stringDate = '${wfhOverviewItem?.leaveDetails?.startDate}';
            DateTime? startDate =
                stringToDateTime(date: stringDate, format: 'dd MMM,yyyy');
            DateTime now = DateTime(
                DateTime.now().year, DateTime.now().month, DateTime.now().day);
            if (startDate != null) {
              showCancelLeavebutton = !now.isBefore(startDate);
            }

            if ((status == 'Approved') && showCancelLeavebutton == true) {
              return const SizedBox();
            }
            if ((status == 'Pending' || status == 'Approved')) {
              return _cancelButton(
                  context: context,
                  wfhId: wfhOverviewItem?.leaveDetails?.id ?? 0,
                  status: status);
            }
            if (status == 'Rejected' ||
                status == 'Cancelled' ||
                status == 'Holiday') {
              return const SizedBox();
            }

            return _cancelButton(
                context: context,
                wfhId: wfhOverviewItem?.leaveDetails?.id ?? 0,
                status: status);
          },
        ),
      ),
    );
  }

  Widget _hrRejectStatusBuilder(WfhOverviewModel data) {
    final hrStatus = data.hrStatus;
    if (['false', 'rejected', 'true']
            .contains(hrStatus?.isApprove.toString().toLowerCase()) &&
        hrStatus?.comment != null) {
      return _leaveRejectedCard(
        comment: hrStatus?.comment ?? '',
        rejectedBy: hrStatus?.name ?? '',
        rejectedDate: hrStatus?.createdAt ?? '',
        appOrReject: hrStatus?.isApprove ?? '',
      );
    }
    return const SizedBox();
  }

  Widget _rejectStatusBuilder(WfhOverviewModel data) {
    if (data.reportingPersonList != null) {
      return ListView.separated(
        itemCount: data.reportingPersonList!.length,
        shrinkWrap: true,
        physics: const ScrollPhysics(),
        itemBuilder: (context, index) {
          ReportingPersonList? e = data.reportingPersonList?[index];

          if (['false', 'rejected', 'true']
                  .contains(e?.isApprove.toString().toLowerCase()) &&
              e?.comment != null) {
            return _leaveRejectedCard(
              comment: e?.comment ?? '',
              rejectedBy: e?.name ?? '',
              rejectedDate: e?.createdAt ?? '',
              appOrReject: e?.isApprove ?? '',
            );
          }
          return const SizedBox();
        },
        separatorBuilder: (context, index) {
          return SizedBox(height: h * 10);
        },
        // children: data.reportingPersonList!.map((e) {
        //   return _leaveRejectedCard(
        //       comment: e.comment ?? '', rejectedBy: e.name ?? '');
        // }).toList(),
      );
    }
    return const SizedBox();
  }

  Widget _leaveRejectedCard(
      {required String rejectedBy,
      required String comment,
      required String? rejectedDate,
      required String? appOrReject}) {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                height: 24,
                width: 24,
                margin: const EdgeInsets.only(right: 10),
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                    color: appOrReject?.toLowerCase() == 'false'
                        ? ThemeColors.colorFCD2D0
                        : ThemeColors.color06AA37.withOpacity(.2),
                    borderRadius: BorderRadius.circular(6)),
                child: Container(
                  decoration: BoxDecoration(
                      color: appOrReject?.toLowerCase() == 'false'
                          ? ThemeColors.colorF64D44
                          : ThemeColors.color06AA37,
                      shape: BoxShape.circle),
                  child: Center(
                    child: Icon(
                      appOrReject?.toLowerCase() == 'false'
                          ? Icons.close_rounded
                          : Icons.done_rounded,
                      color: Colors.white,
                      size: 10,
                    ),
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      appOrReject?.toLowerCase() == 'false'
                          ? 'WFH Rejected'
                          : 'WFH Approved',
                      style: appOrReject?.toLowerCase() == 'false'
                          ? tsS12w4cF64D44
                          : tsS12w4c06AA37,
                    ),
                  ),
                  SizedBox(
                    height: 15,
                    width: 280 * w,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: Text(
                            rejectedBy,
                            style: tsS10w400c646363,
                          ),
                        ),
                        SizedBox(
                          width: 10 * w,
                        ),
                        Expanded(
                          child: Text(
                            rejectedDate.toString(),
                            style: tsS10w400c646363,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          Divider(
            thickness: 1,
            color: ThemeColors.colorD9D9D9,
          ),
          const SizedBox(height: 12),
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              comment,
              style: tsS14w400979797,
            ),
          )
        ],
      ),
    );
  }
  // Widget _hrRejectStatusBuilder(WfhOverviewModel data) {
  // Widget _rejectStatusBuilder(WfhOverviewModel data) {
  //   if (data.reportingPersonList != null) {
  //     return ListView.separated(
  //       itemCount: data.reportingPersonList!.length,
  //       shrinkWrap: true,
  //       physics: const ScrollPhysics(),
  //       itemBuilder: (context, index) {
  //         ReportingpersonList? e = data.reportingPersonList?[index];

  //         if (['false', 'rejected', 'true']
  //                 .contains(e?.isApprove.toString().toLowerCase()) &&
  //             e?.comment != null) {
  //           return _wfhRejectedCard(
  //             comment: e?.comment ?? '',
  //             rejectedBy: e?.name ?? '',
  //             rejectedDate: e?.createdAt,
  //             appOrReject: e?.isApprove ?? '',
  //           );
  //         }
  //         return const SizedBox();
  //       },
  //       separatorBuilder: (context, index) {
  //         return SizedBox(height: h * 10);
  //       },
  //       // children: data.reportingPersonList!.map((e) {
  //       //   return _wfhRejectedCard(
  //       //       comment: e.comment ?? '', rejectedBy: e.name ?? '');
  //       // }).toList(),
  //     );
  //   }
  //   return const SizedBox();
  // }

  Widget _cancelButton(
      {required BuildContext context, required int wfhId, String? status}) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: CancelButton(
          height: h * 56,
          width: double.infinity,
          title: status == 'Approved' ? 'Cancel WFH' : 'Cancel WFH Request',
          textStyle: tsS18w600cFFFFFF,
          onPressed: () async {
            showModalBottomSheet(
              context: context,
              isDismissible: true,
              backgroundColor: ThemeColors.colorFFFFFF,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.0)),
              builder: (context) {
                return _showModalBottomSheet(context: context, wfhId: wfhId);
              },
            );
          },
        )
        // : const SizedBox(),
        );
  }

  Widget _showModalBottomSheet(
      {required BuildContext context, required int wfhId}) {
    return Container(
      height: h * 249,
      decoration: BoxDecoration(
        // color: ThemeColors.colorFFFFFF,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          SizedBox(height: h * 14),
          Container(
            height: h * 5,
            width: w * 134,
            decoration: BoxDecoration(
              color: ThemeColors.colorEAEBED,
              borderRadius: BorderRadius.circular(100),
            ),
          ),
          SizedBox(height: h * 44),
          Text('Cancel this WFH', style: tsS26w500cFFFFFF),
          SizedBox(height: h * 2),
          Text('Are you sure about cancelling this WFH ? ',
              style: tsS16w500c9F9F9F),
          SizedBox(height: h * 33),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: w * 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GeneralButton(
                  height: h * 50,
                  width: w * 164,
                  isDisabledColor: true,
                  title: 'No',
                  textStyle: tsS18w600cFFFFFF,
                  onPressed: () => Navigator.pop(context),
                ),
                Consumer<WfhOverviewProvider>(
                  builder: (context, provider, child) {
                    return GeneralButton(
                      height: h * 50,
                      width: w * 164,
                      title: 'Yes',
                      textStyle: tsS18w600cFFFFFF,
                      // color: ThemeColors.secondaryColor,
                      onPressed: provider.isCancellingWfh
                          ? null
                          : () async {
                              final recordsProvider = Provider.of<WFHProvider>(
                                  context,
                                  listen: false);
                              EasyLoading.show();
                              final navigator = Navigator.of(context);
                              bool isPop = await provider.cancelWfhRequest(
                                  wfhId:
                                      provider.overview?.leaveDetails?.id ?? 0);

                              if (isPop) {
                                recordsProvider.currentPage = 1;
                                recordsProvider.whfPaginationController
                                    .refresh();
                                navigator.pop();
                                navigator.pop();
                              }
                              EasyLoading.dismiss();
                            },
                    );
                  },
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _requestNewButton(
      {required BuildContext context,
      required LeaveDetails? leaveDetails,
      String? text,
      WfhOverviewModel? wfhOverViewItem}) {
    // if (text?.toLowerCase() != 'pending' &&
    //     text?.toLowerCase() != 'cancelled') {
    //   return const SizedBox();
    // }

    return text?.toLowerCase() == 'pending'
        ? Padding(
            padding: EdgeInsets.symmetric(vertical: h * 10, horizontal: w * 10),
            child: TextButton(
              onPressed: () async {
                // await _provider
                //     .fetchOverview(wfhOverViewItem?.leaveDetails?.id ?? 0);
                PageNavigator.push(
                    context: context,
                    route: ApplyWfhScreen(item: wfhOverViewItem));
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: ThemeColors.colorTransparent,
                minimumSize: const Size(10, 36),
              ),
              child: Text('Edit & Apply', style: tsS14w500cFFFFFF),
            ),
          )
        : const SizedBox();
  }

  Widget _wfhRejectedCard(
      {required String rejectedBy,
      required String comment,
      required String? rejectedDate,
      required String? appOrReject}) {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                height: 24,
                width: 24,
                margin: const EdgeInsets.only(right: 10),
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                    color: appOrReject?.toLowerCase() == 'false'
                        ? ThemeColors.colorFCD2D0
                        : ThemeColors.color06AA37.withOpacity(.2),
                    borderRadius: BorderRadius.circular(6)),
                child: Container(
                  decoration: BoxDecoration(
                      color: appOrReject?.toLowerCase() == 'false'
                          ? ThemeColors.colorF64D44
                          : ThemeColors.color06AA37,
                      shape: BoxShape.circle),
                  child: Center(
                    child: Icon(
                      appOrReject?.toLowerCase() == 'false'
                          ? Icons.close_rounded
                          : Icons.done_rounded,
                      color: Colors.white,
                      size: 10,
                    ),
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      appOrReject?.toLowerCase() == 'false'
                          ? 'WFH Rejected'
                          : 'WFH Approved',
                      style: appOrReject?.toLowerCase() == 'false'
                          ? tsS12w4cF64D44
                          : tsS12w4c06AA37,
                    ),
                  ),
                  SizedBox(
                    height: 15,
                    width: 280 * w,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: Text(
                            rejectedBy,
                            style: tsS10w400c646363,
                          ),
                        ),
                        SizedBox(
                          width: 10 * w,
                        ),
                        Expanded(
                          child: Text(
                            rejectedDate.toString(),
                            style: tsS10w400c646363,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          Divider(thickness: 1, color: ThemeColors.colorD9D9D9),
          const SizedBox(height: 12),
          Align(
            alignment: Alignment.topLeft,
            child: Text(comment, style: tsS14w400979797),
          )
        ],
      ),
    );
  }

  Widget _hrApprovalWidget(
      {required String profilePic,
      required String name,
      required String isApprove}) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: ClipRRect(
              borderRadius: BorderRadius.circular(5.0),
              child: ClipOval(
                child: Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: profilePic.toString(),
                      width: 45 * w,
                      height: 45 * h,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) {
                        return Container(
                          height: 50,
                          width: 50,
                          decoration: BoxDecoration(
                            color: ThemeColors.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            name.substring(0, 1).toUpperCase(),
                            style: GoogleFonts.rubik(
                                fontSize: 22,
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                          ),
                        );
                      },
                    ),
                    if (isApprove.toLowerCase().trim() == 'pending')
                      Container(
                        height: 45 * h,
                        width: 45 * w,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                  ],
                ),
              )),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: Column(
            children: [
              if (isApprove.toLowerCase().trim() == 'pending') const SizedBox(),
              if (isApprove.toLowerCase().trim() == 'true')
                ImageIcon(
                  const AssetImage(
                    'assets/icons/tick.png',
                  ),
                  size: 13,
                  color: ThemeColors.color06AA37,
                ),
              if (isApprove.toLowerCase().trim() == 'false')
                ImageIcon(
                  const AssetImage(
                    'assets/icons/close_red.png',
                  ),
                  size: 13,
                  color: ThemeColors.colorB80000,
                ),
            ],
          ),
        )
      ],
    );
  }

  Widget _reportingPersonsWidget({required WfhOverviewModel data}) {
    return Row(
      children: data.reportingPersonList!.map(
        (e) {
          return Stack(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(5.0),
                    child: ClipOval(
                      child: Stack(
                        children: [
                          CachedNetworkImage(
                            imageUrl: e.profilePic.toString(),
                            width: 45 * w,
                            height: 45 * h,
                            fit: BoxFit.cover,
                            errorWidget: (context, url, error) {
                              return Container(
                                height: 50,
                                width: 50,
                                decoration: BoxDecoration(
                                  color: ThemeColors.primaryColor,
                                  shape: BoxShape.circle,
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  e.name?.substring(0, 1).toUpperCase() ?? '',
                                  style: GoogleFonts.rubik(
                                      fontSize: 22,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white),
                                ),
                              );
                            },
                          ),
                          if (e.isApprove?.toLowerCase().trim() == 'pending')
                            Container(
                              height: 45 * h,
                              width: 45 * w,
                              color: Colors.white.withValues(alpha: 0.8),
                            ),
                        ],
                      ),
                    )),
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Column(
                  children: [
                    if (e.isApprove?.toLowerCase().trim() == 'pending')
                      const SizedBox(),
                    if (e.isApprove?.toLowerCase().trim() == 'true')
                      ImageIcon(
                        const AssetImage(
                          'assets/icons/tick.png',
                        ),
                        size: 13,
                        color: ThemeColors.color06AA37,
                      ),
                    if (e.isApprove?.toLowerCase().trim() == 'false')
                      ImageIcon(
                        const AssetImage(
                          'assets/icons/close_red.png',
                        ),
                        size: 13,
                        color: ThemeColors.colorB80000,
                      ),
                  ],
                ),
              )
            ],
          );
        },
      ).toList(),
    );
  }
}
