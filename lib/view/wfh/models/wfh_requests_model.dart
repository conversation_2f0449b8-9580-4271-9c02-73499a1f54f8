// class WfhRequestsModel {
//   String? result;
//   List<WfhRequestListDatas>? data;
//   bool? hasNext;
//   bool? hasPrevious;

//   WfhRequestsModel({this.result, this.data, this.hasNext, this.hasPrevious});

//   factory WfhRequestsModel.fromJson(Map<String, dynamic> json) =>
//       WfhRequestsModel(
//         result: json['result'],
//         data: (json['data'] as List?)
//             ?.map((e) => WfhRequestListDatas.fromJson(e))
//             .toList(),
//         hasNext: json['has_next'],
//         hasPrevious: json['has_previous'],
//       );

//   Map<String, dynamic> toJson() => {
//         'result': result,
//         'data': data?.map((e) => e.toJson()).toList(),
//         'has_next': hasNext,
//         'has_previous': hasPrevious,
//       };
// }
import 'package:e8_hr_portal/view/wfh/models/wfh_overview_model.dart';

class WfhRequestListDatas {
  LeaveDetails? leaveDetails;
  HrStatus? hrStatus;
  List<ReportingPersonList>? reportingPersonList;

  WfhRequestListDatas(
      {this.leaveDetails, this.hrStatus, this.reportingPersonList});

  factory WfhRequestListDatas.fromJson(Map<String, dynamic> json) =>
      WfhRequestListDatas(
        leaveDetails: json['leave_details'] != null
            ? LeaveDetails.fromJson(json['leave_details'])
            : null,
        hrStatus: json['hr_status'] != null
            ? HrStatus.fromJson(json['hr_status'])
            : null,
        reportingPersonList: (json['reporting_person_list'] as List?)
            ?.map((e) => ReportingPersonList.fromJson(e))
            .toList(),
      );
}

// class LeaveDetails {
//   int? id;
//   String? reason;
//   UserDetails? userDetails;
//   String? status;
//   String? wfhType;
//   String? leaveLevel;
//   String? leavePermission;
//   List<ReportingPersonsActions>? reportingPersonsActions;
//   String? startDate;
//   String? endDate;
//   String? createdAt;

//   LeaveDetails({
//     this.id,
//     this.reason,
//     this.userDetails,
//     this.status,
//     this.wfhType,
//     this.leaveLevel,
//     this.leavePermission,
//     this.reportingPersonsActions,
//     this.startDate,
//     this.endDate,
//     this.createdAt,
//   });

//   factory LeaveDetails.fromJson(Map<String, dynamic> json) => LeaveDetails(
//         id: json['id'],
//         reason: json['reason'],
//         userDetails: json['user_details'] != null
//             ? UserDetails.fromJson(json['user_details'])
//             : null,
//         status: json['status'],
//         wfhType: json['wfh_type'],
//         leaveLevel: json['leave_level'],
//         leavePermission: json['leave_permission'],
//         reportingPersonsActions: (json['reporting_persons_actions'] as List?)
//             ?.map((e) => ReportingPersonsActions.fromJson(e))
//             .toList(),
//         startDate: json['start_date'],
//         endDate: json['end_date'],
//         createdAt: json['created_at'],
//       );
// }

// class HrStatus {
//   int? id;
//   String? name;
//   List<Role>? role;
//   String? profilePic;
//   String? isApprove;
//   String? comment;
//   String? createdAt;

//   HrStatus({
//     this.id,
//     this.name,
//     this.role,
//     this.profilePic,
//     this.isApprove,
//     this.comment,
//     this.createdAt,
//   });

//   factory HrStatus.fromJson(Map<String, dynamic> json) => HrStatus(
//         id: json['id'],
//         name: json['name'],
//         role: (json['role'] as List?)?.map((e) => Role.fromJson(e)).toList(),
//         profilePic: json['profile_pic'],
//         isApprove: json['is_approve'],
//         comment: json['comment'],
//         createdAt: json['created_at'],
//       );

//   Map<String, dynamic> toJson() => {
//         'id': id,
//         'name': name,
//         'role': role?.map((e) => e.toJson()).toList(),
//         'profile_pic': profilePic,
//         'is_approve': isApprove,
//         'comment': comment,
//         'created_at': createdAt,
//       };
// }

// class UserDetails {
//   int? id;
//   String? name;
//   String? email;
//   String? designation;
//   String? profilePic;

//   UserDetails({
//     this.id,
//     this.name,
//     this.email,
//     this.designation,
//     this.profilePic,
//   });

//   factory UserDetails.fromJson(Map<String, dynamic> json) => UserDetails(
//         id: json['id'],
//         name: json['name'],
//         email: json['email'],
//         designation: json['designation'],
//         profilePic: json['profile_pic'],
//       );

//   Map<String, dynamic> toJson() => {
//         'id': id,
//         'name': name,
//         'email': email,
//         'designation': designation,
//         'profile_pic': profilePic,
//       };
// }

// class ReportingPersonsActions {
//   String? name;
//   dynamic isApprove;
//   dynamic profilePic;
//   List<Role>? role;

//   ReportingPersonsActions({
//     this.name,
//     this.isApprove,
//     this.profilePic,
//     this.role,
//   });

//   factory ReportingPersonsActions.fromJson(Map<String, dynamic> json) =>
//       ReportingPersonsActions(
//         name: json['name'],
//         isApprove: json['is_approve'],
//         profilePic: json['profile_pic'],
//         role: (json['role'] as List?)?.map((e) => Role.fromJson(e)).toList(),
//       );

//   Map<String, dynamic> toJson() => {
//         'name': name,
//         'is_approve': isApprove,
//         'profile_pic': profilePic,
//         'role': role?.map((e) => e.toJson()).toList(),
//       };
// }

// class Role {
//   String? name;

//   Role({this.name});

//   factory Role.fromJson(Map<String, dynamic> json) => Role(
//         name: json['name'],
//       );

//   Map<String, dynamic> toJson() => {
//         'name': name,
//       };
// }
