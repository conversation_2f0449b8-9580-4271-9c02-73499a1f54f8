import 'package:e8_hr_portal/view/wfh/models/wfh_overview_model.dart';

class WFHRecordsModel {
  LeaveDetails? leaveDetails;
  HrStatus? hrStatus;
  List<ReportingPersonList>? reportingPersonList;

  WFHRecordsModel({this.leaveDetails, this.hrStatus, this.reportingPersonList});

  WFHRecordsModel.fromJson(Map<String, dynamic> json) {
    leaveDetails = json['leave_details'] != null
        ? LeaveDetails.fromJson(json['leave_details'])
        : null;
    hrStatus =
        json['hr_status'] != null ? HrStatus.fromJson(json['hr_status']) : null;
    if (json['reporting_person_list'] != null) {
      reportingPersonList = <ReportingPersonList>[];
      json['reporting_person_list'].forEach((v) {
        reportingPersonList!.add(ReportingPersonList.fromJson(v));
      });
    }
  }
}

// class LeaveDetails {
//   int? id;
//   UserDetails? userDetails;
//   String? status;
//   String? wfhType;
//   String? leaveLevel;
//   String? leavePermission;
//   List<ReportingPersonsActions>? reportingPersonsActions;
//   String? startDate;
//   String? endDate;
//   String? createdAt;
//   String? reason;

//   LeaveDetails(
//       {this.id,
//       this.userDetails,
//       this.status,
//       this.wfhType,
//       this.leaveLevel,
//       this.leavePermission,
//       this.reportingPersonsActions,
//       this.startDate,
//       this.endDate,
//       this.createdAt,
//       this.reason});

//   LeaveDetails.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     userDetails = json['user_details'] != null
//         ? UserDetails.fromJson(json['user_details'])
//         : null;
//     status = json['status'];
//     wfhType = json['wfh_type'];
//     leaveLevel = json['leave_level'];
//     leavePermission = json['leave_permission'];
//     if (json['reporting_persons_actions'] != null) {
//       reportingPersonsActions = <ReportingPersonsActions>[];
//       json['reporting_persons_actions'].forEach((v) {
//         reportingPersonsActions!.add(ReportingPersonsActions.fromJson(v));
//       });
//     }
//     startDate = json['start_date'];
//     endDate = json['end_date'];
//     createdAt = json['created_at'];
//     reason = json['reason'];
//   }
// }
