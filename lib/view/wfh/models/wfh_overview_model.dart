class WfhOverviewModel {
  LeaveDetails? leaveDetails;
  HrStatus? hrStatus;
  List<ReportingPersonList>? reportingPersonList;

  WfhOverviewModel(
      {this.leaveDetails, this.hrStatus, this.reportingPersonList});

  factory WfhOverviewModel.fromJson(Map<String, dynamic> json) =>
      WfhOverviewModel(
        leaveDetails: json['leave_details'] != null
            ? LeaveDetails.fromJson(json['leave_details'])
            : null,
        hrStatus: json['hr_status'] != null
            ? HrStatus.fromJson(json['hr_status'])
            : null,
        reportingPersonList: json['reporting_person_list'] != null
            ? (json['reporting_person_list'] as List)
                .map((e) => ReportingPersonList.fromJson(e))
                .toList()
            : null,
      );
}

class LeaveDetails {
  int? id;
  String? reason;
  UserDetails? userDetails;
  String? status;
  String? wfhType;
  String? leaveLevel;
  String? leavePermission;
  List<ReportingPersonsActions>? reportingPersonsActions;
  String? startDate;
  String? endDate;
  String? createdAt;

  LeaveDetails({
    this.id,
    this.reason,
    this.userDetails,
    this.status,
    this.wfhType,
    this.leaveLevel,
    this.leavePermission,
    this.reportingPersonsActions,
    this.startDate,
    this.endDate,
    this.createdAt,
  });

  factory LeaveDetails.fromJson(Map<String, dynamic> json) => LeaveDetails(
        id: json['id'],
        reason: json['reason'],
        userDetails: json['user_details'] != null
            ? UserDetails.fromJson(json['user_details'])
            : null,
        status: json['status'],
        wfhType: json['wfh_type'],
        leaveLevel: json['leave_level'],
        leavePermission: json['leave_permission'],
        reportingPersonsActions: json['reporting_persons_actions'] != null
            ? (json['reporting_persons_actions'] as List)
                .map((e) => ReportingPersonsActions.fromJson(e))
                .toList()
            : null,
        startDate: json['start_date'],
        endDate: json['end_date'],
        createdAt: json['created_at'],
      );
}

class UserDetails {
  int? id;
  String? name;
  String? email;
  String? designation;
  String? profilePic;

  UserDetails({
    this.id,
    this.name,
    this.email,
    this.designation,
    this.profilePic,
  });

  factory UserDetails.fromJson(Map<String, dynamic> json) => UserDetails(
        id: json['id'],
        name: json['name'],
        email: json['email'],
        designation: json['designation'],
        profilePic: json['profile_pic'],
      );
}

class ReportingPersonsActions {
  String? name;
  dynamic isApprove;
  String? profilePic;
  List<Role>? role;

  ReportingPersonsActions({
    this.name,
    this.isApprove,
    this.profilePic,
    this.role,
  });

  factory ReportingPersonsActions.fromJson(Map<String, dynamic> json) =>
      ReportingPersonsActions(
        name: json['name'],
        isApprove: json['is_approve'],
        profilePic: json['profile_pic'],
        role: json['role'] != null
            ? (json['role'] as List).map((e) => Role.fromJson(e)).toList()
            : null,
      );
}

class Role {
  String? name;
  Role({this.name});
  factory Role.fromJson(Map<String, dynamic> json) => Role(
        name: json['name'],
      );
}

class ReportingPersonList {
  int? id;
  String? name;
  String? role;
  String? profilePic;
  String? isApprove;
  String? comment;
  String? createdAt;
  int? level;

  ReportingPersonList({
    this.id,
    this.name,
    this.role,
    this.profilePic,
    this.isApprove,
    this.comment,
    this.createdAt,
    this.level,
  });

  factory ReportingPersonList.fromJson(Map<String, dynamic> json) =>
      ReportingPersonList(
        id: json['id'],
        name: json['name'],
        role: json['role'],
        profilePic: json['profile_pic'],
        isApprove: json['is_approve'],
        comment: json['comment'],
        createdAt: json['created_at'],
        level: json['level'],
      );
}

class HrStatus {
  int? id;
  String? name;
  List<Role>? role;
  String? profilePic;
  String? isApprove;
  String? comment;
  String? createdAt;

  HrStatus(
      {this.id,
      this.name,
      this.role,
      this.profilePic,
      this.isApprove,
      this.comment,
      this.createdAt});

  HrStatus.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    if (json['role'] != null) {
      role = <Role>[];
      json['role'].forEach((v) {
        role!.add(Role.fromJson(v));
      });
    }
    profilePic = json['profile_pic'];
    isApprove = json['is_approve'];
    comment = json['comment'];
    createdAt = json['created_at'];
  }
}
