import 'package:e8_hr_portal/view/wfh/models/wfh_overview_model.dart';

class WfhHrOverviewModel {
  LeaveDetails? leaveDetails;
  HrStatus? hrStatus;
  List<ReportingPersonList>? reportingPersonList;

  WfhHrOverviewModel({
    this.leaveDetails,
    this.hrStatus,
    this.reportingPersonList,
  });

  WfhHrOverviewModel.fromJson(Map<String, dynamic> json) {
    leaveDetails = json['leave_details'] != null
        ? LeaveDetails.fromJson(json['leave_details'])
        : null;
    hrStatus =
        json['hr_status'] != null ? HrStatus.fromJson(json['hr_status']) : null;
    if (json['reporting_person_list'] != null) {
      reportingPersonList = List<ReportingPersonList>.from(
        json['reporting_person_list']
            .map((x) => ReportingPersonList.fromJson(x)),
      );
    }
  }
}

// class Leavedetails {
//   int? id;
//   String? reason;
//   UserDetails? userDetails;
//   String? status;
//   String? wfhType;
//   String? leaveLevel;
//   String? leavePermission;
//   List<ReportingPersonsActions>? reportingPersonsActions;
//   String? startDate;
//   String? endDate;
//   String? createdAt;

//   // ✅ Unnamed constructor
//   Leavedetails({
//     this.id,
//     this.reason,
//     this.userDetails,
//     this.status,
//     this.wfhType,
//     this.leaveLevel,
//     this.leavePermission,
//     this.reportingPersonsActions,
//     this.startDate,
//     this.endDate,
//     this.createdAt,
//   });

//   Leavedetails.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     reason = json['reason'];
//     userDetails = json['user_details'] != null
//         ? UserDetails.fromJson(json['user_details'])
//         : null;
//     status = json['status'];
//     wfhType = json['wfh_type'];
//     leaveLevel = json['leave_level'];
//     leavePermission = json['leave_permission'];
//     if (json['reporting_persons_actions'] != null) {
//       reportingPersonsActions = List<ReportingPersonsActions>.from(
//         json['reporting_persons_actions']
//             .map((x) => ReportingPersonsActions.fromJson(x)),
//       );
//     }
//     startDate = json['start_date'];
//     endDate = json['end_date'];
//     createdAt = json['created_at'];
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'reason': reason,
//       if (userDetails != null) 'user_details': userDetails!.toJson(),
//       'status': status,
//       'wfh_type': wfhType,
//       'leave_level': leaveLevel,
//       'leave_permission': leavePermission,
//       if (reportingPersonsActions != null)
//         'reporting_persons_actions':
//             reportingPersonsActions!.map((x) => x.toJson()).toList(),
//       'start_date': startDate,
//       'end_date': endDate,
//       'created_at': createdAt,
//     };
//   }
// }

// class UserDetails {
//   int? id;
//   String? name;
//   String? email;
//   String? designation;
//   String? profilePic;

//   UserDetails({
//     this.id,
//     this.name,
//     this.email,
//     this.designation,
//     this.profilePic,
//   });

//   UserDetails.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     email = json['email'];
//     designation = json['designation'];
//     profilePic = json['profile_pic'];
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'name': name,
//       'email': email,
//       'designation': designation,
//       'profile_pic': profilePic,
//     };
//   }
// }

class ReportingPersonsActions {
  String? name;
  bool? isApprove;
  String? profilePic;
  List<Role>? role;

  ReportingPersonsActions({
    this.name,
    this.isApprove,
    this.profilePic,
    this.role,
  });

  ReportingPersonsActions.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    isApprove = json['is_approve'];
    profilePic = json['profile_pic'];
    if (json['role'] != null) {
      role = List<Role>.from(json['role'].map((x) => Role.fromJson(x)));
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'is_approve': isApprove,
      'profile_pic': profilePic,
      if (role != null) 'role': role!.map((x) => x.toJson()).toList(),
    };
  }
}

class Role {
  String? name;

  Role({this.name});

  Role.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() => {
        'name': name,
      };
}
