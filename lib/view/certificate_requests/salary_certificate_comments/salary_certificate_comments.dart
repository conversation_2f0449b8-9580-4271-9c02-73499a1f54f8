import 'package:flutter/material.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/provider/salary_certificate_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:provider/provider.dart';

import '../../widgets/general_button.dart';

class SalaryCertificateComments extends StatefulWidget {
  final int? requestId;
  const SalaryCertificateComments({required this.requestId, super.key});

  @override
  State<SalaryCertificateComments> createState() =>
      _SalaryCertificateCommentsState();
}

class _SalaryCertificateCommentsState extends State<SalaryCertificateComments> {
  final TextEditingController _commentsController = TextEditingController();
  final _commentsFormKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "Comments",
      body: Form(
        key: _commentsFormKey,
        child: Padding(
          padding: EdgeInsets.only(top: h * 32, bottom: h * 38),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextFieldWidget(
                controller: _commentsController,
                hintText: 'Write Something',
                keyboardType: TextInputType.emailAddress,
                textStyle: tsS14w400c30292F,

                // error:
                validator: Validator.text,
                textCapitalization: TextCapitalization.none,
                maxLines: 10,
              ),
              GeneralButton(
                height: h * 56,
                width: w * 343,
                title: "Submit",
                textStyle: tsS18w600cFFFFFF,
                onPressed: () async {
                  SalaryCertificateProvider provider =
                      Provider.of<SalaryCertificateProvider>(context,
                          listen: false);
                  if (_commentsFormKey.currentState!.validate()) {
                    await provider.salaryCertificateCommentCreate(
                        action: "salary_certificates",
                        actionId: widget.requestId ?? 0,
                        comment: _commentsController.text,
                        context: context);
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
