// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/salary_transfer_request.dart';
import 'package:e8_hr_portal/provider/salary_transfer_letter_provider.dart';
import 'package:e8_hr_portal/util/capitalize.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/certificate_requests/new_salary_request/new_salary_transfer_request.dart';
import 'package:e8_hr_portal/view/certificate_requests/salary_certificate/widget/salary_cert_rejected_commend.dart';
import 'package:e8_hr_portal/view/certificate_requests/salary_transfer_certificate/view_salary_transfer_certificate.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../util/dailoge.dart';

class SalaryTransferCertificate extends StatelessWidget {
  final bool mounted = true;
  const SalaryTransferCertificate({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "Salary Transfer Certificate",
      // actions: [_requestNewButton(context: context)],
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: h * 25),
          Text(
            "Previous Requests",
            style: tsS18w500c181818,
          ),
          SizedBox(height: h * 15),
          Consumer<SalaryTransferLetterProvider>(
              builder: (context, provider, _) {
            return Expanded(
              child: provider.requestList.isEmpty
                  ? const Center(
                      child: Text("No Data Found"),
                    )
                  : SmartRefresher(
                      controller: provider.refreshController,
                      onRefresh: provider.onRefresh,
                      header: WaterDropMaterialHeader(
                        backgroundColor: ThemeColors.colorFCC400,
                      ),
                      child: ListView.separated(
                        padding: EdgeInsets.only(bottom: 100 * h),
                        shrinkWrap: true,
                        physics: const BouncingScrollPhysics(),
                        itemCount: provider.requestList.length,
                        itemBuilder: (context, index) {
                          return _requestTile(
                            context: context,
                            request: provider.requestList[index],
                          );
                        },
                        separatorBuilder: (context, index) =>
                            SizedBox(height: h * 10),
                      ),
                    ),
            );
          }),
        ],
      ),
      floatingActionButton: GeneralButton(
        textStyle: tsS18w600cFFFFFF,
        title: "Request New",
        height: h * 56,
        width: w * 343,
        onPressed: () => _requestNew(context),
      ),
    );
  }

  // Widget _requestNewButton({required BuildContext context}) {
  //   return Padding(
  //     padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
  //     child: InkWell(
  //       borderRadius: BorderRadius.circular(8),
  //       radius: 10,
  //       onTap: () => _requestNew(context),
  //       child: Align(
  //         alignment: Alignment.center,
  //         child: Text(
  //           "Request New",
  //           style: tsS14w500cFFFFFF,
  //         ),
  //       ),
  //     ),
  //   );
  // }

  _requestNew(BuildContext context) async {
    var provider =
        Provider.of<SalaryTransferLetterProvider>(context, listen: false);
    EasyLoading.show();
    await provider.fetchPersonalInfo();
    EasyLoading.dismiss();
    if (!mounted) return;
    PageNavigator.push(
      context: context,
      route: const NewSalaryTransferRequest(),
    );
  }

  Widget _detailsWidget({required String title, required String date}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: tsS12w400c949494,
        ),
        SizedBox(height: h * 3),
        Text(
          date,
          style: tsS14w500c2C2D33,
        )
      ],
    );
  }

  Widget _status({required String status}) {
    Color bgColor = ThemeColors.color32936F.withOpacity(0.16);
    TextStyle style = tsS12w600c949494;
    if (status.toLowerCase() == 'pending') {
      bgColor = const Color(0xFFFFF2E2);
      style = tsS12W6FE5B900;
    }
    if (status.toLowerCase() == 'rejected') {
      bgColor = ThemeColors.colorF64D44.withOpacity(0.15);
      style = tsS12w600F64D44;
    }
    //Rejected
    return Container(
      height: h * 23,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: w * 11),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(status.capitalize(), style: style),
    );
  }

  Widget _requestTile(
      {required BuildContext context, required SalaryTransferRequest request}) {
    String requestedDate = request.requestedDate ?? "";
    String approvedOrRejectedDate = request.approvedOrRejectedDate ?? "";
    return GestureDetector(
      onTap: () async {
        if (request.status == "approved") {
          final navigator = Navigator.of(context);
          // Navigator.of(context).push(
          //   MaterialPageRoute(
          //       builder: (context) => const SalaryTransferComments()),
          // );
          //ViewSalaryTransferCertificate
          EasyLoading.show();
          bool isGo = await Provider.of<SalaryTransferLetterProvider>(context,
                  listen: false)
              .getSalaryTransferCertificatePdf(
                  certificateId: request.id.toString());
          EasyLoading.dismiss();

          if (isGo) {
            navigator.push(
              MaterialPageRoute(
                  builder: (context) => ViewSalaryTransferCertificate(
                        requestId: request.id ?? 0,
                      )),
            );
          } else {
            showToastText("No document found");
          }
        } else if (request.status?.toLowerCase() == "rejected" &&
            request.rejectedComment != null) {
          showDialog(
            context: context,
            builder: (context) => RequestRejectedCommentDialog(
                comment: request.rejectedComment.toString(),
                rejectedPerson: request.approvedBy),
          );
        }
      },
      child: Container(
        // height: h * 106,
        width: w * 343,
        padding: EdgeInsets.fromLTRB(w * 10, h * 10, w * 10, h * 13),
        decoration: BoxDecoration(
          color: ThemeColors.colorFFFFFF,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _detailsWidget(date: requestedDate, title: "Requested Date"),
                SizedBox(height: h * 11),
                if (request.approvedBy != null)
                  _detailsWidget(
                      date: "${request.approvedBy}", title: "Approved By"),
              ],
            ),
            if (request.approvedOrRejectedDate != null)
              _detailsWidget(
                  date: approvedOrRejectedDate, title: "Approved Date"),
            _status(status: request.status ?? ''),
          ],
        ),
      ),
    );
  }
}
