import 'package:flutter/material.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/provider/salary_transfer_letter_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:provider/provider.dart';

class SalaryTransferComments extends StatefulWidget {
  final int requestId;
  const SalaryTransferComments({required this.requestId, super.key});

  @override
  State<SalaryTransferComments> createState() => _SalaryTransferCommentsState();
}

class _SalaryTransferCommentsState extends State<SalaryTransferComments> {
  final TextEditingController _transferCommentsController =
      TextEditingController();
  final _transferCommentsFormKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "Comments",
      body: Form(
        key: _transferCommentsFormKey,
        child: Padding(
          padding: EdgeInsets.only(top: h * 32, bottom: h * 38),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextFieldWidget(
                controller: _transferCommentsController,
                hintText: 'Write Something',
                keyboardType: TextInputType.emailAddress,
                textStyle: tsS14w400c30292F,

                // error:
                validator: Validator.text,
                textCapitalization: TextCapitalization.none,
                maxLines: 10,
              ),
              GeneralButton(
                height: h * 56,
                width: w * 343,
                title: "Submit",
                textStyle: tsS18w600cFFFFFF,
                onPressed: () async {
                  final provider = Provider.of<SalaryTransferLetterProvider>(
                      context,
                      listen: false);
                  if (_transferCommentsFormKey.currentState!.validate()) {
                    await provider.salaryTransferCommentCreate(
                        action: "salary_transfer_letter",
                        actionId: widget.requestId,
                        comment: _transferCommentsController.text,
                        context: context);
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
