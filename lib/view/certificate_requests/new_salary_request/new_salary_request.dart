import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/model/salary_certificate_personal_info.dart';
import 'package:e8_hr_portal/provider/salary_certificate_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:provider/provider.dart';

import '../../widgets/hisense_drop_down_tile.dart';

class NewSalaryRequest extends StatefulWidget {
  const NewSalaryRequest({super.key});

  @override
  State<NewSalaryRequest> createState() => _NewSalaryRequestState();
}

class _NewSalaryRequestState extends State<NewSalaryRequest> {
  final TextEditingController _toController = TextEditingController();
  final TextEditingController _passportController = TextEditingController();
  final TextEditingController _certNameController = TextEditingController();
  final TextEditingController _designationController = TextEditingController();

  final _certFormKey = GlobalKey<FormState>();

  @override
  void initState() {
    SalaryCertificateProvider provider =
        Provider.of<SalaryCertificateProvider>(context, listen: false);
    provider.getYear();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      DateTime now = DateTime.now();
      provider.selectedYear = now.year;
      provider.onYearSelected(now.year);

      SalaryCertificatePersonalInfo info =
          provider.salaryCertificatePersonalInfo!;
      if (info.passport != null) {
        _passportController.text = info.passport!.passportNumber ?? "";
      }
      if (info.name != null) {
        _certNameController.text = info.name ?? "";
      }
      if (info.designation != null) {
        _designationController.text = info.designation ?? "";
      }
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "Salary Certificate",
      body: Form(
        key: _certFormKey,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: h * 29),
              Text("To", style: tsS12w400c30292F),
              SizedBox(height: h * 4),
              _textFormField(
                  controller: _toController,
                  validator: Validator.text,
                  hintText: 'To',
                  keyboardType: TextInputType.text),
              // SizedBox(height: h * 15),
              // _designationDropDownTile(),
              SizedBox(height: h * 15),
              _monthDropDownTile(),
              SizedBox(height: h * 15),
              _yearDropDownTile(),
              SizedBox(height: h * 20),
              Container(
                height: h * 3,
                color: Colors.white,
              ),
              SizedBox(height: h * 20),
              Text('Mr/Ms', style: tsS12w400c30292F),
              SizedBox(height: h * 4),
              _textFormField(
                  controller: _certNameController,
                  validator: Validator.text,
                  hintText: 'Mr/Mrs',
                  enabled: false,
                  keyboardType: TextInputType.text),
              SizedBox(height: h * 15),
              Text("Passport Number", style: tsS12w400c30292F),
              SizedBox(height: h * 4),
              Consumer<SalaryCertificateProvider>(
                  builder: (context, provider, _) {
                return _textFormField(
                  controller: _passportController,
                  validator: (value) {
                    if (value == null) {
                      return "Please update Passport number";
                    } else if (provider.salaryCertificatePersonalInfo!.passport!
                            .isExpired ==
                        true) {
                      return "fhd";
                    }
                    return null;
                  },
                  errorText: provider.salaryCertificatePersonalInfo!.passport!
                              .isExpired ==
                          true
                      ? "Passport Number Expired"
                      : null,
                  enabled: false,
                  keyboardType: TextInputType.number,
                );
              }),
              SizedBox(height: h * 15),
              Text("Your Designation", style: tsS12w400c30292F),
              SizedBox(height: h * 4),
              _textFormField(
                controller: _designationController,
                enabled: false,
                hintText: "Your Designation",
                validator: Validator.text,
              ),
              // const Spacer(),
              SizedBox(height: h * 110),
              ButtonWidget(
                title: 'Submit',
                textStyle: tsS16w600cFFFFFF,
                color: Theme.of(context).primaryColor,
                onPressed: () async {
                  SalaryCertificateProvider provider =
                      Provider.of<SalaryCertificateProvider>(context,
                          listen: false);
                  if (provider.salaryCertificatePersonalInfo?.isProfileCompleted
                          ?.isCompleted ==
                      true) {
                    if (_certFormKey.currentState!.validate()) {
                      SalaryCertificateProvider provider =
                          Provider.of(context, listen: false);

                      String date =
                          "${provider.selectedYear} ${provider.selectedMonth}";
                      String dateAndYear =
                          formatDateFromString(date, "yyyy MMMM", "MMM, yyyy");

                      EasyLoading.show();
                      await provider.salaryCertificateCreate(
                          context: context,
                          monthAndYear: dateAndYear,
                          to: _toController.text);
                      EasyLoading.dismiss();
                    }
                  } else {
                    showToastText(provider.salaryCertificatePersonalInfo!
                        .isProfileCompleted!.message
                        .toString());
                  }
                },
              ),
              SizedBox(height: h * 110),
            ],
          ),
        ),
      ),
    );
  }

  Widget _textFormField({
    required TextEditingController controller,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    bool? enabled,
    String? hintText,
    String? errorText,
  }) {
    return TextFieldWidget(
      enabled: enabled ?? true,
      controller: controller,
      textStyle: tsS14w400c30292F,
      hintStyle: tsS12w400c9F9F9F,
      hintText: hintText,
      error: errorText,

      keyboardType: keyboardType,
      borderColor: ThemeColors.colorE3E3E3,
      contentPadding:
          EdgeInsets.symmetric(vertical: h * 14, horizontal: w * 11),
      border: OutlineInputBorder(
        borderSide: BorderSide(
          width: 1,
          color: ThemeColors.colorE3E3E3,
        ),
        borderRadius: BorderRadius.circular(5),
      ),
      // error: provider.loginErrorEmail,
      validator: validator,
    );
  }

  // Widget _designationDropDownTile() {
  //   return Consumer<SalaryCertificateProvider>(
  //     builder: (context, provider, _) {
  //       return HisenseDropdownTile(
  //         title: "Designation",
  //         titleStyle: tsS12w400c30292F,
  //         hintText: "Select Designation",
  //         hintStyle: tsS12w400c9F9F9F,
  //         style: tsS14w400454444,
  //         value: provider.selectedDesignation,
  //         errorStyle: tsS11w400cerrorColor,
  //         contentPadding:
  //             EdgeInsets.symmetric(vertical: h * 12, horizontal: w * 11),
  //         validator: (value) {
  //           if (value == null) {
  //             return "Select Designation";
  //           }
  //           return null;
  //         },
  //         // contentPadding:
  //         //     EdgeInsets.symmetric(horizontal: w * 12, vertical: h * 12),
  //         onChanged: (String? value) {
  //           if (value != null) {
  //             WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
  //               provider.selectedDesignation = value;
  //             });
  //           }
  //         },
  //         items: provider.designationList.map((item) {
  //           return DropdownMenuItem(
  //             value: item.text,
  //             child: Text(
  //               item.text,
  //               style: tsS14w400454444,
  //             ),
  //           );
  //         }).toList(),
  //       );
  //     },
  //   );
  // }

  Widget _monthDropDownTile() {
    return Consumer<SalaryCertificateProvider>(
      builder: (context, provider, _) {
        return HisenseDropdownTile(
          title: "Month",
          titleStyle: tsS12w400c30292F,
          hintText: "Select Month",
          hintStyle: tsS12w400c9F9F9F,
          style: tsS14w400454444,
          value: provider.selectedMonth,
          errorStyle: tsS11w400cerrorColor,
          contentPadding:
              EdgeInsets.symmetric(vertical: h * 12, horizontal: w * 11),
          validator: (value) {
            if (value == null) {
              return "Select Month";
            }
            return null;
          },
          // contentPadding:
          //     EdgeInsets.symmetric(horizontal: w * 12, vertical: h * 12),
          onChanged: (String? month) {
            if (month != null) {
              WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
                provider.onMonthSelected(month);
              });
            }
          },
          items: provider.availableMonths?.map((item) {
            return DropdownMenuItem(
              value: item,
              child: Text(
                item,
                style: tsS14w400454444,
              ),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _yearDropDownTile() {
    return Consumer<SalaryCertificateProvider>(
      builder: (context, provider, _) {
        return HisenseDropdownTile(
          title: "Year",
          titleStyle: tsS12w400c30292F,
          hintText: "Select Year",
          hintStyle: tsS12w400c9F9F9F,
          style: tsS14w400454444,
          value: provider.selectedYear,
          errorStyle: tsS11w400cerrorColor,
          contentPadding:
              EdgeInsets.symmetric(vertical: h * 12, horizontal: w * 11),
          validator: (value) {
            if (value == null) {
              return "Select Year";
            }
            return null;
          },
          onChanged: (int? year) {
            // provider.selectedYear = year;
            // WidgetsBinding.instance.addPostFrameCallback((timeStamp) { });
            if (year != null) {
              provider.onYearSelected(year);
            }
          },
          items: provider.yearList?.map((item) {
            return DropdownMenuItem<int>(
              value: item,
              child: Text(
                item.toString(),
              ),
            );
          }).toList(),
        );
      },
    );
  }

  // Widget _yourDesignationTile() {
  //   return Consumer<SalaryCertificateProvider>(
  //     builder: (context, provider, _) {
  //       return HisenseDropdownTile(
  //         enabled: false,
  //         title: "Your Designation",
  //         titleStyle: tsS12w400c30292F,
  //         hintText: "Select Designation",
  //         hintStyle: tsS12w400c9F9F9F,
  //         style: tsS14w400454444,
  //         value: provider.salaryCertificatePersonalInfo?.designation,

  //         errorStyle: tsS11w400cerrorColor,
  //         contentPadding:
  //             EdgeInsets.symmetric(vertical: h * 12, horizontal: w * 11),
  //         validator: (value) {
  //           if (value == null) {
  //             return "Select Designation";
  //           }
  //           return null;
  //         },
  //         // contentPadding:
  //         //     EdgeInsets.symmetric(horizontal: w * 12, vertical: h * 12),
  //         onChanged: (String? value) {
  //           if (value != null) {
  //             WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
  //               provider.selectedyourDesignation = value;
  //             });
  //           }
  //         },

  //         items: provider.yourDesignationList.map((item) {
  //           return DropdownMenuItem(
  //             value: item.text,
  //             child: Text(
  //               item.text,
  //               style: tsS14w400454444,
  //             ),
  //           );
  //         }).toList(),
  //       );
  //     },
  //   );
  // }
}
