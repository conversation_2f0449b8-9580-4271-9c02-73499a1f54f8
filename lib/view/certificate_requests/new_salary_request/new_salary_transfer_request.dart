// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/provider/salary_transfer_letter_provider.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:provider/provider.dart';

class NewSalaryTransferRequest extends StatefulWidget {
  const NewSalaryTransferRequest({super.key});

  @override
  State<NewSalaryTransferRequest> createState() =>
      _NewSalaryTransferRequestState();
}

class _NewSalaryTransferRequestState extends State<NewSalaryTransferRequest> {
  final TextEditingController _toNameController = TextEditingController();
  final TextEditingController _bankNameController = TextEditingController();
  final TextEditingController _branchNameController = TextEditingController();
  final TextEditingController _yourNameController = TextEditingController();
  final TextEditingController _nationalityController = TextEditingController();
  final TextEditingController _passportNoController = TextEditingController();
  final TextEditingController _dateOfJoiningController =
      TextEditingController();
  final TextEditingController _designationController = TextEditingController();
  final TextEditingController _monthlySalaryController =
      TextEditingController();
  final TextEditingController _accountNoController = TextEditingController();
  final TextEditingController _ibanNoController = TextEditingController();
  final TextEditingController _branchController = TextEditingController();
  final _salaryTransferFormKey = GlobalKey<FormState>();

  late SalaryTransferLetterProvider _provider;

  @override
  void initState() {
    _provider =
        Provider.of<SalaryTransferLetterProvider>(context, listen: false);
    _yourNameController.text = _provider.personalInfo?.name ?? '';
    _nationalityController.text = _provider.personalInfo?.nationality ?? '';
    _passportNoController.text =
        _provider.personalInfo?.passport?.passportNumber ?? '';
    _dateOfJoiningController.text = _provider.personalInfo?.doj ?? '';
    _designationController.text = _provider.personalInfo?.designation ?? '';
    _monthlySalaryController.text = _provider.personalInfo?.salary ?? '';
    _accountNoController.text =
        _provider.personalInfo?.bank?.accountNumber ?? '';
    _ibanNoController.text = _provider.personalInfo?.bank?.iban ?? '';
    _branchController.text = _provider.personalInfo?.bank?.branch ?? '';
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "Salary Transfer Certificate",
      body: Padding(
        padding: EdgeInsets.only(top: h * 29),
        child: Form(
          key: _salaryTransferFormKey,
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _textFormFieldWidget(
                  title: "To",
                  controller: _toNameController,
                  hintText: "Name",
                  keyboardType: TextInputType.text,
                  validator: Validator.text,
                ),
                _textFormFieldWidget(
                  title: "Bank Name",
                  controller: _bankNameController,
                  hintText: "Enter bank name",
                  keyboardType: TextInputType.text,
                  validator: Validator.text,
                ),
                _textFormFieldWidget(
                  title: "Branch Name",
                  controller: _branchNameController,
                  hintText: "Enter branch name",
                  keyboardType: TextInputType.text,
                  validator: Validator.text,
                ),
                Container(
                  height: h * 3,
                  color: Colors.white,
                ),
                SizedBox(height: h * 15),
                _textFormFieldWidget(
                  title: 'Mr/Ms',
                  controller: _yourNameController,
                  hintText: "Mr/Mrs",
                  keyboardType: TextInputType.text,
                  enabled: false,
                ),
                _textFormFieldWidget(
                  title: "Nationality",
                  controller: _nationalityController,
                  hintText: "Select Nationality",
                  keyboardType: TextInputType.text,
                  enabled: false,
                ),
                Consumer<SalaryTransferLetterProvider>(
                    builder: (context, provider, _) {
                  return _textFormFieldWidget(
                      title: "Passport Number",
                      controller: _passportNoController,
                      keyboardType: TextInputType.text,
                      enabled: false,
                      validator: Validator.text,
                      errorText:
                          provider.personalInfo?.passport?.isExpired == true
                              ? "Passport Number Expired"
                              : null);
                }),
                _textFormFieldWidget(
                    title: "Date of Joining",
                    controller: _dateOfJoiningController,
                    keyboardType: TextInputType.text,
                    enabled: false,
                    validator: Validator.text),
                _textFormFieldWidget(
                    title: "Your Designation",
                    controller: _designationController,
                    keyboardType: TextInputType.text,
                    enabled: false,
                    validator: Validator.text),
                _textFormFieldWidget(
                  title: "Monthly Salary(AED)",
                  controller: _monthlySalaryController,
                  keyboardType: TextInputType.number,
                  enabled: false,
                ),
                _textFormFieldWidget(
                    title: "Bank Account Number",
                    controller: _accountNoController,
                    keyboardType: TextInputType.number,
                    enabled: false,
                    validator: Validator.text),
                _textFormFieldWidget(
                    title: "IBAN Number",
                    controller: _ibanNoController,
                    keyboardType: TextInputType.number,
                    enabled: false,
                    validator: Validator.text),
                _textFormFieldWidget(
                    title: "Branch",
                    controller: _branchController,
                    keyboardType: TextInputType.number,
                    enabled: false,
                    validator: Validator.text),
                SizedBox(height: h * 40),
                Consumer<SalaryTransferLetterProvider>(
                  builder: (context, provider, _) {
                    if (provider.isLoading) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }
                    return ButtonWidget(
                      title: 'Submit',
                      textStyle: tsS16w600cFFFFFF,
                      color: Theme.of(context).primaryColor,
                      onPressed: () async {
                        if (provider.personalInfo?.isProfileCompleted
                                ?.isCompleted ==
                            true) {
                          if (_salaryTransferFormKey.currentState!.validate()) {
                            _submitRequest(provider);
                          }
                        } else {
                          showToastText(provider
                              .personalInfo!.isProfileCompleted!.message
                              .toString());
                        }
                      },
                    );
                  },
                ),
                SizedBox(height: h * 80),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _submitRequest(SalaryTransferLetterProvider provider) async {
    bool status = await provider.submitRequest(
      to: _toNameController.text.trimLeft(),
      bankName: _bankNameController.text.trimLeft(),
      branchName: _branchNameController.text.trimLeft(),
    );
    if (status && mounted) {
      Text text = const Text('Request sent successfully.');
      SnackBar snackBar = SnackBar(content: text);
      ScaffoldMessenger.of(context).showSnackBar(snackBar);
      Navigator.pop(context);
      await provider.fetchPreviousRequests();
    } else if (!status) {
      Text text = Text(provider.errorMsg ?? 'Something went wrong');
      SnackBar snackBar = SnackBar(content: text);
      ScaffoldMessenger.of(context).showSnackBar(snackBar);
    }
  }

  // Widget _nationalityDropDownTile() {
  //   return Consumer<ProfileProvider>(
  //     builder: (context, provider, _) {
  //       return Column(
  //         children: [
  //           DropdownTile<String>(
  //             title: 'Nationality',
  //             errorStyle: tsS11w400cerrorColor,
  //             titleStyle: tsS12w400c30292F,
  //             items: const [
  //               DropdownMenuItem(
  //                 value: 'Bahrain',
  //                 child: Text('Bahrain'),
  //               ),
  //               DropdownMenuItem(
  //                 value: 'Kuwait',
  //                 child: Text('Kuwait'),
  //               ),
  //               DropdownMenuItem(
  //                 value: 'Oman',
  //                 child: Text('Oman'),
  //               ),
  //               DropdownMenuItem(
  //                 value: 'Qatar',
  //                 child: Text('Qatar'),
  //               ),
  //               DropdownMenuItem(
  //                 value: 'Saudi Arabia',
  //                 child: Text('Saudi Arabia'),
  //               ),
  //               DropdownMenuItem(
  //                 value: 'United Arab Emirates',
  //                 child: Text('United Arab Emirates'),
  //               ),
  //             ],
  //             hintText: "Select Nationality",
  //             onChanged: (value) {
  //               setState(() {
  //                 if (value != null) {
  //                   _selectedNation = value;
  //                 }
  //               });
  //             },
  //             value: _selectedNation,
  //             validator: (value) {
  //               if (value == null) {
  //                 return 'Please select your nation';
  //               }
  //               return null;
  //             },
  //           ),
  //           SizedBox(height: h * 15),
  //         ],
  //       );
  //     },
  //   );
  // }

  // Widget _yourDesignationTile() {
  //   return Consumer<ProfileProvider>(
  //     builder: (context, provider, _) {
  //       return Column(
  //         children: [
  //           DropdownTile<String>(
  //             title: 'Your Designation',
  //             errorStyle: tsS11w400cerrorColor,
  //             titleStyle: tsS12w400c30292F,
  //             items: const [
  //               DropdownMenuItem(
  //                 value: 'Eployee',
  //                 child: Text('Eployee'),
  //               ),
  //               DropdownMenuItem(
  //                 value: 'HR Admin',
  //                 child: Text('HR Admin'),
  //               ),
  //               DropdownMenuItem(
  //                 value: 'Manager',
  //                 child: Text('Manager'),
  //               ),
  //             ],
  //             hintText: "Select Designation",
  //             onChanged: (value) {
  //               setState(() {
  //                 if (value != null) {
  //                   _selectedYourDesig = value;
  //                 }
  //               });
  //             },
  //             value: _selectedYourDesig,
  //             validator: (value) {
  //               if (value == null) {
  //                 return 'Please select Designation';
  //               }
  //               return null;
  //             },
  //           ),
  //           SizedBox(height: h * 15),
  //         ],
  //       );
  //     },
  //   );
  // }

  Widget _textFormFieldWidget({
    required String title,
    String? hintText,
    required TextEditingController controller,
    String? Function(String?)? validator,
    required TextInputType keyboardType,
    String? errorText,
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: tsS12w400c30292F),
        SizedBox(height: h * 4),
        TextFieldWidget(
          controller: controller,
          hintText: hintText,
          hintStyle: tsS12w400c9F9F9F,
          textStyle: tsS12w400c30292F,
          keyboardType: keyboardType,
          validator: validator,
          enabled: enabled,
          error: errorText,
        ),
        SizedBox(height: h * 15),
      ],
    );
  }
}
