import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/driving_license_list_model.dart';
import 'package:e8_hr_portal/provider/driving_license_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../salary_certificate/widget/salary_cert_rejected_commend.dart';

class DrivingCertificates extends StatelessWidget {
  const DrivingCertificates({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<DrivingLicenseProvider>(
      builder: (context, provider, child) {
        return CustomScaffold(
          screenTitle: "Driving License",
          // actions: [_requestNewButton(context: context)],
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: h * 25),
              Text(
                "Previous Requests",
                style: tsS18w500c181818,
              ),
              SizedBox(height: h * 15),
              Expanded(
                child: provider.drivingLicenseList.isEmpty
                    ? const Center(child: Text("No Data Found"))
                    : Consumer<DrivingLicenseProvider>(
                        builder: (context, pro, _) {
                        // if (pro.drivingLicenseList != null) {
                        //   return Container();
                        // }
                        return SmartRefresher(
                          controller: provider.refreshController,
                          onRefresh: provider.onRefresh,
                          header: WaterDropMaterialHeader(
                            backgroundColor: ThemeColors.colorFCC400,
                          ),
                          child: ListView.separated(
                            shrinkWrap: true,
                            physics: const BouncingScrollPhysics(),
                            scrollDirection: Axis.vertical,
                            itemCount: pro.drivingLicenseList.length,
                            itemBuilder: (context, index) {
                              DrivingLicenseListModel element =
                                  pro.drivingLicenseList[index];
                              return _requestTile(
                                  context: context, element: element);
                            },
                            separatorBuilder: (context, index) =>
                                SizedBox(height: h * 10),
                          ),
                        );
                      }),
              ),
            ],
          ),
          floatingActionButton: GeneralButton(
            textStyle: tsS18w600cFFFFFF,
            title: "Request New",
            height: h * 56,
            width: w * 343,
            onPressed: () async {
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              DrivingLicenseProvider provider =
                  Provider.of<DrivingLicenseProvider>(context, listen: false);
              EasyLoading.show();
              bool status = await provider.submitRequest();

              if (status) {
                Text text = const Text('Request sent successfully.');
                SnackBar snackBar = SnackBar(content: text);
                scaffoldMessenger.showSnackBar(snackBar);
              } else {
                Text text = Text(provider.errorMsg ?? 'Something went wrong');
                SnackBar snackBar = SnackBar(content: text);
                scaffoldMessenger.showSnackBar(snackBar);
              }
              // await provider.getDrivingLicenseList();

              EasyLoading.dismiss();
            },
          ),
        );
      },
    );
  }

  // Widget _requestNewButton({required BuildContext context}) {
  //   return Padding(
  //     padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
  //     child: InkWell(
  //       borderRadius: BorderRadius.circular(8),
  //       radius: 10,
  //       onTap: () async {
  //         final scaffoldMessenger = ScaffoldMessenger.of(context);
  //         DrivingLicenseProvider provider =
  //             Provider.of<DrivingLicenseProvider>(context, listen: false);
  //         EasyLoading.show();
  //         bool status = await provider.submitRequest();

  //         if (status) {
  //           Text text = const Text('Request sent successfully.');
  //           SnackBar snackBar = SnackBar(content: text);
  //           scaffoldMessenger.showSnackBar(snackBar);
  //         } else {
  //           Text text = Text(provider.errorMsg ?? 'Something went wrong');
  //           SnackBar snackBar = SnackBar(content: text);
  //           scaffoldMessenger.showSnackBar(snackBar);
  //         }
  //         // await provider.getDrivingLicenseList();

  //         EasyLoading.dismiss();
  //       },
  //       child: Align(
  //         alignment: Alignment.center,
  //         child: Text(
  //           "Request New",
  //           style: tsS14w500cFFFFFF,
  //         ),
  //       ),
  //     ),
  //     // child: TextButton(
  //     //   onPressed: () async {
  //     //     final scaffoldMessenger = ScaffoldMessenger.of(context);
  //     //     DrivingLicenseProvider provider =
  //     //         Provider.of<DrivingLicenseProvider>(context, listen: false);
  //     //     EasyLoading.show();
  //     //     bool status = await provider.submitRequest();
  //     //     if (status) {
  //     //       Text text = const Text('Request sent successfully.');
  //     //       SnackBar snackBar = SnackBar(content: text);
  //     //       scaffoldMessenger.showSnackBar(snackBar);
  //     //     } else {
  //     //       Text text = Text(provider.errorMsg ?? 'Something went wrong');
  //     //       SnackBar snackBar = SnackBar(content: text);
  //     //       scaffoldMessenger.showSnackBar(snackBar);
  //     //     }
  //     //     EasyLoading.dismiss();
  //     //   },
  //     //   style: ElevatedButton.styleFrom(
  //     //     backgroundColor: ThemeColors.colorTransparent,
  //     //     minimumSize: const Size(10, 36),
  //     //   ),
  //     //   child: Text(
  //     //     "Request New",
  //     //     style: tsS14w500cFFFFFF,
  //     //   ),
  //     // ),
  //   );
  // }

  // Widget _popUpMenuItem({required BuildContext context}) {
  //   MasterProvider provider =
  //       Provider.of<MasterProvider>(context, listen: false);
  //   return PopupMenuButton<String>(
  //     icon: Icon(
  //       Icons.more_vert,
  //       size: 25,
  //       color: ThemeColors.colorFFFFFF,
  //     ),
  //     onSelected: (value) {
  //       provider.handleClick(
  //           context: context, value: value, route: "driving_license_comments");
  //     },
  //     itemBuilder: (BuildContext context) {
  //       return provider.pop.map((String choice) {
  //         return PopupMenuItem<String>(
  //           enabled: true,
  //           // height: h * 62,
  //           value: choice,
  //           child: Text(choice),
  //         );
  //       }).toList();
  //     },
  //   );
  // }

  Widget _detailsWidget({required String title, required String date}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: tsS12w400c949494,
        ),
        SizedBox(height: h * 3),
        Text(
          date,
          style: tsS14w500c2C2D33,
        )
      ],
    );
  }

  Widget _status({required String status, required String statusCheck}) {
    return Container(
      height: h * 23,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: w * 11),
      decoration: BoxDecoration(
        color: statusCheck == "approved"
            ? ThemeColors.color32936F.withOpacity(0.16)
            : statusCheck == "pending"
                ? ThemeColors.colorFFF2E2
                : ThemeColors.colorF64D44.withOpacity(0.15),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(status,
          style: statusCheck == "approved"
              ? tsS12w600c949494
              : statusCheck == "pending"
                  ? tsS12w600cE5B900
                  : tsS12w600cF64D44),
    );
  }

  Widget _requestTile(
      {required BuildContext context,
      required DrivingLicenseListModel element}) {
    String? requestedDate = element.requestedDate;
    String? approvedDate = element.approvedOrRejectedDate;
    String? status =
        "${element.status?[0].toUpperCase()}${element.status?.substring(1).toLowerCase()}";
    String? approvedBy = element.approvedBy;
    String? statusCheck = element.status?.toLowerCase().trim();
    String? rejectedComment = element.rejectedComment;

    // String? status = "proce";
    return GestureDetector(
      onTap: () async {
        final scaffoldMessenger = ScaffoldMessenger.of(context);

        if (statusCheck == "pending") {
          Text text = const Text('Request pending');
          SnackBar snackBar = SnackBar(content: text);
          scaffoldMessenger.showSnackBar(snackBar);
        } else if (statusCheck?.toLowerCase() == "rejected" &&
            rejectedComment != null) {
          showDialog(
            context: context,
            builder: (context) {
              return RequestRejectedCommentDialog(
                  comment: rejectedComment.toString(),
                  rejectedPerson: approvedBy);
            },
          );
        }
        // else if (statusCheck == "approved") {
        //   final navigator = Navigator.of(context);
        //   DrivingLicenseProvider provider =
        //       Provider.of<DrivingLicenseProvider>(context, listen: false);
        //   EasyLoading.show();
        //   bool isGo = await provider.getDrivingLicensePDf(
        //     action: "driving_license_permission",
        //     actionId: id ?? 0,
        //   );
        //   EasyLoading.dismiss();
        //   if (isGo) {
        //     navigator.push(MaterialPageRoute(
        //         builder: (context) => ViewDrivingLicense(
        //               requestId: id ?? 0,
        //             )));
        //   }
        // }
      },
      child: Container(
        // height: h * 106,
        width: w * 343,
        padding: EdgeInsets.fromLTRB(w * 10, h * 10, w * 10, h * 13),
        decoration: BoxDecoration(
          color: ThemeColors.colorFFFFFF,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _detailsWidget(
                    date: requestedDate ?? "", title: "Requested Date"),
                SizedBox(height: h * 11),
                if (statusCheck != "pending")
                  _detailsWidget(
                      date: approvedBy ?? "",
                      title: statusCheck == "approved"
                          ? "Approved By"
                          : "Rejected By"),
              ],
            ),
            if (statusCheck != "pending")
              _detailsWidget(
                  date: approvedDate ?? "",
                  title: statusCheck == "approved"
                      ? "Approved Date"
                      : "Rejected Date"),
            _status(status: status, statusCheck: statusCheck ?? ""),
          ],
        ),
      ),
    );
  }
}
