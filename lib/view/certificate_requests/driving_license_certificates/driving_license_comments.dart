import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/provider/driving_license_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:provider/provider.dart';

class DrivingLicenseComments extends StatefulWidget {
  final int actionId;
  const DrivingLicenseComments({required this.actionId, super.key});

  @override
  State<DrivingLicenseComments> createState() => _DrivingLicenseCommentsState();
}

class _DrivingLicenseCommentsState extends State<DrivingLicenseComments> {
  final TextEditingController _drivingCommentsController =
      TextEditingController();
  final _drivingCommentsFormKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "Comments",
      body: Form(
        key: _drivingCommentsFormKey,
        child: Padding(
          padding: EdgeInsets.only(top: h * 32, bottom: h * 38),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextFieldWidget(
                controller: _drivingCommentsController,
                hintText: 'Write Something',
                keyboardType: TextInputType.emailAddress,
                textStyle: tsS14w400c30292F,

                // error:
                validator: Validator.text,
                textCapitalization: TextCapitalization.none,
                maxLines: 10,
              ),
              ButtonWidget(
                title: 'Submit',
                textStyle: tsS16w600cFFFFFF,
                color: Theme.of(context).primaryColor,
                onPressed: () async {
                  if (_drivingCommentsFormKey.currentState!.validate()) {
                    DrivingLicenseProvider provider =
                        Provider.of<DrivingLicenseProvider>(context,
                            listen: false);

                    EasyLoading.show();
                    await provider.drivingLicenseCommentCreate(
                        action: "driving_license_permission",
                        actionId: widget.actionId,
                        comment: _drivingCommentsController.text,
                        context: context);
                    EasyLoading.dismiss();
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
