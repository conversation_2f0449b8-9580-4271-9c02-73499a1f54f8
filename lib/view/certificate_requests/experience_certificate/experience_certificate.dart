// ignore_for_file: use_build_context_synchronously
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/model/experience_certificate_request.dart';
import 'package:e8_hr_portal/provider/experience_certifcate_provider.dart';
import 'package:e8_hr_portal/util/capitalize.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/certificate_requests/salary_certificate/widget/salary_cert_rejected_commend.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:flutter/material.dart';
import 'view_experience_certificate.dart';

class ExperienceCertificate extends StatefulWidget {
  const ExperienceCertificate({super.key});
  @override
  State<ExperienceCertificate> createState() => _ExperienceCertificateState();
}

class _ExperienceCertificateState extends State<ExperienceCertificate> {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  void _onRefresh() async {
    var provider =
        Provider.of<ExperienceCertifcateProvider>(context, listen: false);
    EasyLoading.show();
    await provider.fetchPreviousRequests();
    EasyLoading.dismiss();
    _refreshController.refreshCompleted();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "Experience Certificate",
      // actions: [_requiestNewButton(context: context)],
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: h * 25),
          Text(
            "Previous Requests",
            style: tsS18w500c181818,
          ),
          SizedBox(height: h * 15),
          Consumer<ExperienceCertifcateProvider>(
            builder: (context, provider, _) {
              if (provider.requestList.isEmpty) {
                return const Expanded(
                    child: Center(child: Text("No Data Found")));
              }
              return Expanded(
                child: SmartRefresher(
                  controller: _refreshController,
                  onRefresh: _onRefresh,
                  header: WaterDropMaterialHeader(
                    backgroundColor: ThemeColors.colorFCC400,
                  ),
                  child: ListView.separated(
                    padding: EdgeInsets.only(bottom: 100 * h),
                    shrinkWrap: true,
                    physics: const BouncingScrollPhysics(),
                    itemCount: provider.requestList.length,
                    itemBuilder: (context, index) {
                      return _requestTile(
                        context: context,
                        request: provider.requestList[index],
                        onTapDownload: () {},
                      );
                    },
                    separatorBuilder: (context, index) =>
                        SizedBox(height: h * 10),
                  ),
                ),
              );
            },
          ),
        ],
      ),
      floatingActionButton: GeneralButton(
        textStyle: tsS18w600cFFFFFF,
        title: "Request New",
        height: h * 56,
        width: w * 343,
        onPressed: () async {
          var expProvider =
              Provider.of<ExperienceCertifcateProvider>(context, listen: false);
          EasyLoading.show();
          bool status = await expProvider.submitRequest();
          if (status && mounted) {
            Text text = const Text('Request sent successfully.');
            SnackBar snackBar = SnackBar(content: text);
            ScaffoldMessenger.of(context).showSnackBar(snackBar);
          } else {
            Text text = Text(expProvider.errorMsg ?? 'Something went wrong');
            SnackBar snackBar = SnackBar(content: text);
            ScaffoldMessenger.of(context).showSnackBar(snackBar);
          }
          EasyLoading.dismiss();
        },
      ),
    );
  }

  // Widget _requiestNewButton({required BuildContext context}) {
  //   return Padding(
  //     padding: EdgeInsets.symmetric(vertical: h * 15, horizontal: w * 10),
  //     child: InkWell(
  //       borderRadius: BorderRadius.circular(8),
  //       radius: 10,
  //       onTap: () async {
  //         var expProvider =
  //             Provider.of<ExperienceCertifcateProvider>(context, listen: false);
  //         EasyLoading.show();
  //         bool status = await expProvider.submitRequest();
  //         if (status && mounted) {
  //           Text text = const Text('Request sent successfully.');
  //           SnackBar snackBar = SnackBar(content: text);
  //           ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //         } else {
  //           Text text = Text(expProvider.errorMsg ?? 'Something went wrong');
  //           SnackBar snackBar = SnackBar(content: text);
  //           ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //         }
  //         EasyLoading.dismiss();
  //       },
  //       child: Align(
  //         alignment: Alignment.center,
  //         child: Text(
  //           "Request New",
  //           style: tsS14w500cFFFFFF,
  //         ),
  //       ),
  //     ),
  //     // child: TextButton(
  //     //   onPressed: () async {
  //     //     var expProvider =
  //     //         Provider.of<ExperienceCertifcateProvider>(context, listen: false);
  //     //     EasyLoading.show();
  //     //     bool status = await expProvider.submitRequest();
  //     //     if (status && mounted) {
  //     //       Text text = const Text('Request sent successfully.');
  //     //       SnackBar snackBar = SnackBar(content: text);
  //     //       ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //     //     } else {
  //     //       Text text = Text(expProvider.errorMsg ?? 'Something went wrong');
  //     //       SnackBar snackBar = SnackBar(content: text);
  //     //       ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //     //     }
  //     //     EasyLoading.dismiss();
  //     //   },
  //     //   style: ElevatedButton.styleFrom(
  //     //     backgroundColor: ThemeColors.colorTransparent,
  //     //     minimumSize: const Size(10, 36),
  //     //   ),
  //     //   child: Text(
  //     //     "Request New",
  //     //     style: tsS14w500cFFFFFF,
  //     //   ),
  //     // ),
  //   );
  // }

  // Widget _popUpMenuItem({required BuildContext context}) {
  Widget _detailsWidget({required String title, required String date}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: tsS12w400c949494,
        ),
        SizedBox(height: h * 3),
        Text(
          date,
          style: tsS14w500c2C2D33,
        )
      ],
    );
  }

  Widget _status({required String status}) {
    Color bgColor = ThemeColors.color32936F.withOpacity(0.16);
    TextStyle style = tsS12w600c949494;
    if (status.toLowerCase() == 'pending') {
      bgColor = const Color(0xFFFFF2E2);
      style = tsS12W6FE5B900;
    } else if (status.toLowerCase() == 'rejected') {
      bgColor = const Color(0xFFF64D44).withOpacity(0.15);
      style = tsS12w600cF64D44;
    }
    return Container(
      height: h * 23,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: w * 11),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(status.capitalize(), style: style),
    );
  }

  bool _isLoading = false;

  Widget _requestTile(
      {required BuildContext context,
      required VoidCallback onTapDownload,
      required ExperienceCertifcateRequest request}) {
    String requestedDate = request.requestedDate ?? '';
    String approvedOrRejectedDate =
        request.approvedOrRejectedDate ?? '2023-02-14';
    int? id = request.id;
    String? status = request.status.toString().toLowerCase().trim();
    return GestureDetector(
      onTap: () async {
        if (request.status?.toLowerCase().trim() == "approved" && !_isLoading) {
          _isLoading = true;
          final navigator = Navigator.of(context);
          ExperienceCertifcateProvider provider =
              Provider.of<ExperienceCertifcateProvider>(context, listen: false);
          await provider.getExperienceCertificate(
              action: "experience_certificates", actionId: id ?? 0);
          navigator.push(MaterialPageRoute(
              builder: (context) => ViewExperienceCertificate(
                    requestId: request.id ?? 0,
                  )));
          _isLoading = false;
        } else if (request.status?.toLowerCase() == "rejected" &&
            request.rejectedComment != null) {
          showDialog(
            context: context,
            builder: (context) {
              return RequestRejectedCommentDialog(
                  comment: request.rejectedComment.toString(),
                  rejectedPerson: request.approvedBy);
            },
          );
        }
      },
      child: Container(
        // height: h * 106,
        width: w * 343,
        padding: EdgeInsets.fromLTRB(w * 10, h * 10, w * 10, h * 13),
        decoration: BoxDecoration(
          color: ThemeColors.colorFFFFFF,
          borderRadius: BorderRadius.circular(8),
        ),

        child: Column(children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _detailsWidget(date: requestedDate, title: "Requested Date"),
              if (request.approvedOrRejectedDate != null)
                _detailsWidget(
                    date: approvedOrRejectedDate,
                    title: status == "approved"
                        ? "Approved Date"
                        : "Rejected Date"),
              _status(status: request.status ?? ''),
            ],
          ),
          SizedBox(height: h * 11),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (request.approvedBy != null)
                _detailsWidget(
                    date: '${request.approvedBy}',
                    title:
                        status == "approved" ? "Approved By" : "Rejected By"),
              if (request.status?.toLowerCase() == 'approved')
                TextButton(
                  onPressed: null,
                  child: Row(
                    children: [
                      Text(
                        "Download",
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: ThemeColors.colorFCC400,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                      SizedBox(width: w * 1.17),
                      Image.asset(
                        "assets/icons/download_icon.png",
                        height: h * 11.67,
                        width: w * 11.67,
                      ),
                      // ImageIcon(
                      //   AssetImage(
                      //     "assets/icons/download_icon.png",
                      //   ),
                      //   size: 17,
                      // )
                    ],
                  ),
                ),
            ],
          )
        ]),
      ),
    );
  }
}
//   TextButton(
            //   onPressed: () {},
            //   child: Text("nkgugh"),
            // ),