import 'dart:io';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/provider/experience_certifcate_provider.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/certificate_requests/experience_certificate/experience_certificate_comments.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import '../../../provider/general_provider.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../widgets/download_dialog.dart';

class ViewExperienceCertificate extends StatelessWidget {
  final int requestId;
  const ViewExperienceCertificate({required this.requestId, super.key});

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "",
      actions: [_requiestNewButton(context: context)],
      body: Consumer<ExperienceCertifcateProvider>(
        builder: (context, provider, child) {
          return Column(
            children: [
              SizedBox(height: h * 37.33),
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: provider.experienceCertificate == null
                      ? const Center(
                          child: Text(
                            "No certificate found",
                            style: TextStyle(fontSize: 14, color: Colors.black),
                          ),
                        )
                      : SfPdfViewer.network(
                          provider.experienceCertificate!,
                        ),
                ),
              ),
              SizedBox(height: h * 90.08),
              if (provider.experienceCertificate != null)
                GeneralButton(
                  height: h * 56,
                  width: w * 343,
                  onPressed: () async {
                    if (Platform.isAndroid) {
                      showDialog(
                        context: context,
                        builder: (context) => DownloadingDialog(
                            fileName: "Element8 Experience",
                            extention: ".pdf",
                            url: provider.experienceCertificate!),
                      );
                    }
                    if (Platform.isIOS) {
                      GeneralProvider generalProvider =
                          Provider.of<GeneralProvider>(context, listen: false);
                      await generalProvider.launchUniversalLinkIos(
                          url: provider.experienceCertificate!);
                    }
                  },
                  title: "Download",
                  textStyle: tsS18w600cFFFFFF,
                ),
              SizedBox(height: h * 50.08),
            ],
          );
        },
      ),
    );
  }

  Widget _requiestNewButton({required BuildContext context}) {
    // return Padding(
    //   padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
    //   child: TextButton(
    //     onPressed: () {
    //       PageNavigator.push(
    //         context: context,
    //         route: ExperienceCertificateComments(actionId: requestId),
    //       );
    //     },
    //     style: ElevatedButton.styleFrom(
    //       backgroundColor: ThemeColors.colorTransparent,
    //       minimumSize: const Size(10, 36),
    //     ),
    //     child: Text(
    //       "Request Change",
    //       style: tsS14w500cFFFFFF,
    //     ),
    //   ),
    // );
    return Padding(
      padding: EdgeInsets.symmetric(vertical: h * 5, horizontal: w * 10),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        radius: 10,
        onTap: () {
          PageNavigator.push(
            context: context,
            route: ExperienceCertificateComments(actionId: requestId),
          );
        },
        child: Align(
          alignment: Alignment.center,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: w * 8),
            child: Text(
              "Request Change",
              style: tsS14w500cFFFFFF,
            ),
          ),
        ),
      ),
    );
  }
}
