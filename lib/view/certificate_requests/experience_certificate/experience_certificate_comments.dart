import 'package:flutter/material.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/provider/experience_certifcate_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:provider/provider.dart';

class ExperienceCertificateComments extends StatefulWidget {
  final int actionId;
  const ExperienceCertificateComments({required this.actionId, super.key});

  @override
  State<ExperienceCertificateComments> createState() =>
      _ExperienceCertificateCommentsState();
}

class _ExperienceCertificateCommentsState
    extends State<ExperienceCertificateComments> {
  final TextEditingController _experienceCommentsController =
      TextEditingController();
  final _experienceCommentsFormKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "Comments",
      body: Form(
        key: _experienceCommentsFormKey,
        child: Padding(
          padding: EdgeInsets.only(top: h * 32, bottom: h * 38),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextFieldWidget(
                controller: _experienceCommentsController,
                hintText: 'Write Something',
                keyboardType: TextInputType.emailAddress,
                textStyle: tsS14w400c30292F,

                // error:
                validator: Validator.text,
                textCapitalization: TextCapitalization.none,
                maxLines: 10,
              ),
              GeneralButton(
                height: h * 56,
                width: w * 343,
                title: "Submit",
                textStyle: tsS18w600cFFFFFF,
                onPressed: () async {
                  final ExperienceCertifcateProvider provider =
                      Provider.of<ExperienceCertifcateProvider>(context,
                          listen: false);
                  if (_experienceCommentsFormKey.currentState!.validate()) {
                    await provider.experienceCommentCreate(
                        action: "experience_certificates",
                        actionId: widget.actionId,
                        comment: _experienceCommentsController.text,
                        context: context);
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
