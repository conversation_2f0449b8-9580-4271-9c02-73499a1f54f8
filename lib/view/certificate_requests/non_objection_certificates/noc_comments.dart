import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/provider/noc_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:provider/provider.dart';

class NocCommentsComments extends StatefulWidget {
  final int id;
  const NocCommentsComments({required this.id, super.key});

  @override
  State<NocCommentsComments> createState() => _NocCommentsCommentsState();
}

class _NocCommentsCommentsState extends State<NocCommentsComments> {
  final TextEditingController _nocCommentsController = TextEditingController();
  final _nocCommentsFormKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "Comments",
      body: Form(
        key: _nocCommentsFormKey,
        child: Padding(
          padding: EdgeInsets.only(top: h * 32, bottom: h * 38),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextFieldWidget(
                controller: _nocCommentsController,
                hintText: 'Write Something',
                keyboardType: TextInputType.emailAddress,
                textStyle: tsS14w400c30292F,
                validator: Validator.text,
                textCapitalization: TextCapitalization.none,
                maxLines: 10,
              ),
              GeneralButton(
                height: h * 56,
                width: w * 343,
                title: "Submit",
                textStyle: tsS18w600cFFFFFF,
                onPressed: _onSubmit,
              ),
            ],
          ),
        ),
      ),
    );
  }

  _onSubmit() async {
    var provider = Provider.of<NocProvider>(context, listen: false);
    if (_nocCommentsFormKey.currentState!.validate()) {
      EasyLoading.show();
      await provider.nocCommentCreate(
        action: "non_objection_certificate",
        actionId: widget.id,
        comment: _nocCommentsController.text,
        context: context,
      );
      EasyLoading.dismiss();
    }
  }
}
