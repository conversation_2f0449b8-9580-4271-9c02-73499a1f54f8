// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:e8_hr_portal/provider/noc_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/certificate_requests/non_objection_certificates/noc_comments.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import '../../../provider/general_provider.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../widgets/download_dialog.dart';

class ViewNocCertificate extends StatefulWidget {
  final int id;
  const ViewNocCertificate({required this.id, super.key});

  @override
  State<ViewNocCertificate> createState() => _ViewNocCertificateState();
}

class _ViewNocCertificateState extends State<ViewNocCertificate> {
  @override
  Widget build(BuildContext context) {
    NocProvider provider = Provider.of<NocProvider>(context, listen: false);

    return CustomScaffold(
      screenTitle: "",
      actions: [_requiestNewButton(context: context, id: widget.id)],
      body: Column(
        children: [
          SizedBox(height: h * 37.33),
          // Image.asset("assets/dummy/salary_cert_template.png"),
          if (provider.nocCertificate != null)
            Expanded(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: SfPdfViewer.network(provider.nocCertificate!,
                    canShowScrollHead: false, canShowScrollStatus: false),
              ),
            ),
          SizedBox(height: h * 90.08),
          GeneralButton(
            height: h * 56,
            width: w * 343,
            onPressed: () async {
              if (Platform.isAndroid) {
                bool asd = await check404Url(provider.nocCertificate!);

                if (!asd) {
                  showDialog(
                    context: context,
                    builder: (context) => DownloadingDialog(
                        fileName: "Element8 NOC",
                        extention: ".pdf",
                        url: provider.nocCertificate!),
                  );
                } else {
                  showToastText("404 Not Found");
                }
              }
              if (Platform.isIOS) {
                GeneralProvider generalProvider =
                    Provider.of<GeneralProvider>(context, listen: false);
                await generalProvider.launchUniversalLinkIos(
                    url: provider.nocCertificate!);
              }
            },
            title: "Download",
            textStyle: tsS18w600cFFFFFF,
          ),
          SizedBox(height: h * 50),
        ],
      ),
    );
  }

  Widget _requiestNewButton({required BuildContext context, required int? id}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
      child: TextButton(
        onPressed: () {
          PageNavigator.push(
            context: context,
            route: NocCommentsComments(id: id ?? 0),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: ThemeColors.colorTransparent,
          minimumSize: const Size(10, 36),
        ),
        child: Text(
          "Request Change",
          style: tsS14w500cFFFFFF,
        ),
      ),
    );
  }
}

Future<bool> check404Url(String url) async {
  final response = await http.get(Uri.parse(url));
  return response.statusCode == 404;
}
