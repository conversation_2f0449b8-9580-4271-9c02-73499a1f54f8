import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/provider/noc_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/hisense_drop_down_tile.dart';
import 'package:provider/provider.dart';

import '../../../model/flight_ticket_personal_info.dart';
import '../../../provider/flight_ticket_provider.dart';

class NewNocRequest extends StatefulWidget {
  const NewNocRequest({super.key});

  @override
  State<NewNocRequest> createState() => _NewNocRequestState();
}

class _NewNocRequestState extends State<NewNocRequest> {
  final TextEditingController _toNameController = TextEditingController();
  final TextEditingController _visaNameController = TextEditingController();
  final TextEditingController _travelCountryNameController =
      TextEditingController();
  final TextEditingController _yourNameController = TextEditingController();
  final TextEditingController _passportNoController = TextEditingController();
  final TextEditingController _placeOfTravelController =
      TextEditingController();
  // final TextEditingController _dateOfJoiningController =
  //     TextEditingController();
  final TextEditingController _designationController = TextEditingController();
  final TextEditingController _nationalityController = TextEditingController();
  final _salaryTransferFormKey = GlobalKey<FormState>();
  late FlightTicketPersonalInfo? flightTicketPersonalInfoProvider;
  late NocProvider provider;
  @override
  void initState() {
    provider = Provider.of<NocProvider>(context, listen: false);
    flightTicketPersonalInfoProvider =
        Provider.of<FlightTicketProvider>(context, listen: false)
            .flightTicketPersonalInfo;
    _passportNoController.text =
        flightTicketPersonalInfoProvider?.passport?.passportNumber ?? "";
    _yourNameController.text = flightTicketPersonalInfoProvider?.name ?? "";
    _designationController.text =
        flightTicketPersonalInfoProvider?.designation ?? "";
    _nationalityController.text =
        flightTicketPersonalInfoProvider?.nationality ?? "";
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (flightTicketPersonalInfoProvider?.doj != null) {
        provider.dateTime = stringToDateTime(
            date: flightTicketPersonalInfoProvider!.doj.toString(),
            format: 'yyyy-MM-dd');
      }
      if (flightTicketPersonalInfoProvider?.designation != null) {
        provider.selectedDesignation =
            flightTicketPersonalInfoProvider!.designation;
      }

      // provider.selectedNationality =
      //     flightTicketPersonalInfoProvider?.nationality;
      // provider.selectedNationality = "Oman";
      // provider.selectedFromDate = DateTime.now();
      // provider.selectedToDate = DateTime.now();
      // provider.selectedReturnDate = DateTime.now();
      // provider.formattedFromDate =
      //     formatDateFromDate(dateTime: DateTime.now(), format: "dd MMM yyyy");
      // provider.formattedToDate = formatDateFromDate(
      //     dateTime: provider.selectedFromDate!, format: "dd MMM yyyy");
      // provider.formattedReturnDate = formatDateFromDate(
      //     dateTime: provider.selectedFromDate!, format: "dd MMM yyyy");
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "Non-Objection Certificate",
      horizontalPadding: 0,
      body: Padding(
        padding: EdgeInsets.only(top: h * 29),
        child: Form(
          key: _salaryTransferFormKey,
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: w * 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _purposeDropdownTile(),
                      SizedBox(height: h * 15),
                      _textFormFieldWidget(
                        title: "To",
                        controller: _toNameController,
                        hintText: "Name",
                        keyboardType: TextInputType.text,
                        validator: Validator.text,
                      ),
                      _textFormFieldWidget(
                        title: "Entry Visa",
                        controller: _visaNameController,
                        hintText: "Entry visa name",
                        keyboardType: TextInputType.text,
                        validator: Validator.text,
                      ),
                      _divider(),
                      _calenderWidgets(),
                      _textFormFieldWidget(
                        readOnly: true,
                        title: 'Mr/Ms',
                        hintText: "Mr/Mrs",
                        // inputFormatters: [
                        //   FilteringTextInputFormatter.allow(
                        //     RegExp("[a-zA-Z ]"),
                        //   ),
                        // ],
                        controller: _yourNameController,
                        keyboardType: null,
                        validator: Validator.text,
                      ),
                      // _nationalityDropDownTile(),
                      _textFormFieldWidget(
                        title: "Nationality",
                        readOnly: true,
                        controller: _nationalityController,
                        keyboardType: null,
                        validator: Validator.text,
                      ),
                      // SizedBox(height: h * 15),
                      Consumer<FlightTicketProvider>(
                          builder: (context, provider, _) {
                        return _textFormFieldWidget(
                            title: "Passport Number",
                            readOnly: true,
                            controller: _passportNoController,
                            keyboardType: TextInputType.text,
                            validator: Validator.text,
                            errorText: provider.flightTicketPersonalInfo
                                        ?.passport?.isExpired ==
                                    true
                                ? "Passport Number Expired"
                                : null);
                      }),
                      Consumer<NocProvider>(
                        builder: (context, provider, _) {
                          String date = "";
                          if (provider.dateTime != null) {
                            date = formatDateFromDate(
                                dateTime: provider.dateTime!,
                                format: "dd-MM-yyyy");
                          }
                          return _textFormFieldWidget(
                              title: "Date of Joining",
                              readOnly: true, // to make the TextField read-only
                              controller: TextEditingController(text: date),
                              onTap: () async {
                                // NocProvider provider = Provider.of<NocProvider>(
                                //     context,
                                //     listen: false);
                                // await provider.selectDate(context);
                                // _dateOfJoiningController.text = date;
                              },
                              // keyboardType: TextInputType.datetime,
                              // validator: Validator.text,
                              keyboardType: null,
                              validator: (value) {}
                              // inputFormatters: [
                              //   FilteringTextInputFormatter.allow(RegExp(
                              //       r'\d{2}\/\d{2}\/\d{4}')), // format xx/xx/xxxx
                              // ],
                              );
                        },
                      ),
                      _textFormFieldWidget(
                        validator: Validator.text, keyboardType: null,
                        title: "Your Designation",
                        controller: _designationController, readOnly: true,
                        hintText: "Your Designation",
                        // keyboardType: TextInputType.text,
                        // validator: Validator.text,
                      ),
                      _designationAsPerDropdownTile(),
                      // _yourDesignationTile(),
                      SizedBox(height: h * 15),
                      _textFormFieldWidget(
                        title: "Place of Travel",
                        controller: _placeOfTravelController,
                        keyboardType: TextInputType.text,
                        validator: Validator.text,
                      ),
                      _textFormFieldWidget(
                        title: "Travel Country",
                        controller: _travelCountryNameController,
                        keyboardType: TextInputType.text,
                        validator: Validator.text,
                      ),
                      SizedBox(height: h * 13),
                    ],
                  ),
                ),
                _checkedBoxWidget(),
                SizedBox(height: h * 40),
                _buttonWidget(),
                SizedBox(height: h * 80),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Widget _nationalityDropDownTile() {
  //   return Consumer<NocProvider>(
  //     builder: (context, provider, _) {
  //       return HisenseDropdownTile(
  //         title: "Nationality",
  //         titleStyle: tsS14w400c30292F,
  //         hintText: "Select Your Nationality",
  //         hintStyle: tsS14w4009F9F9F,
  //         style: tsS14w400454444,
  //         value: provider.selectedNationality,
  //         validator: (value) {
  //           if (value == null) {
  //             return "Select Your Nationality";
  //           }
  //           return null;
  //         },
  //         contentPadding:
  //             EdgeInsets.symmetric(horizontal: w * 12, vertical: h * 12),
  //         onChanged: (String? value) {
  //           if (value != null) {
  //             WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
  //               provider.selectedNationality = value;
  //             });
  //           }
  //         },
  //         items: provider.nocNationalityList.map((item) {
  //           return DropdownMenuItem(
  //             value: item.text,
  //             child: Text(
  //               item.text,
  //               style: tsS14w400454444,
  //             ),
  //           );
  //         }).toList(),
  //       );
  //     },
  //   );
  // }

  // Widget _yourDesignationTile() {
  //   return Consumer<NocProvider>(
  //     builder: (context, provider, _) {
  //       return HisenseDropdownTile(
  //         title: "Your Designation",
  //         titleStyle: tsS14w400c30292F,
  //         hintText: "Select Your Designation",
  //         hintStyle: tsS14w4009F9F9F,
  //         style: tsS14w400454444,
  //         value: provider.selectedDesignation,
  //         validator: (value) {
  //           if (value == null) {
  //             return "Select Your Designation";
  //           }
  //           return null;
  //         },
  //         contentPadding:
  //             EdgeInsets.symmetric(horizontal: w * 12, vertical: h * 12),
  //         onChanged: (String? value) {
  //           if (value != null) {
  //             WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
  //               provider.selectedDesignation = value;
  //             });
  //           }
  //         },
  //         items: provider.nocDesignationList.map((item) {
  //           return DropdownMenuItem(
  //             value: item.text,
  //             child: Text(
  //               item.text,
  //               style: tsS14w400454444,
  //             ),
  //           );
  //         }).toList(),
  //       );
  //     },
  //   );
  // }

  Widget _textFormFieldWidget(
      {required String title,
      String? hintText,
      bool? enabled,
      bool readOnly = false,
      VoidCallback? onTap,
      List<TextInputFormatter>? inputFormatters,
      required TextEditingController controller,
      required var validator,
      required TextInputType? keyboardType,
      String? errorText}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: tsS14w400c30292F),
        SizedBox(height: h * 4),
        TextFieldWidget(
          enabled: enabled ?? true,
          controller: controller,
          textStyle: tsS14w400c30292F,
          hintStyle: tsS14w4009F9F9F,
          hintText: hintText,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          borderColor: ThemeColors.colorE3E3E3,
          readOnly: readOnly,
          onTap: onTap,
          error: errorText,

          contentPadding:
              EdgeInsets.symmetric(vertical: h * 14, horizontal: w * 11),
          border: OutlineInputBorder(
            borderSide: BorderSide(
              width: 1,
              color: ThemeColors.colorE3E3E3,
            ),
            borderRadius: BorderRadius.circular(5),
          ),
          // error: provider.loginErrorEmail,
          validator: validator,
        ),
        SizedBox(height: h * 15),
      ],
    );
  }

  Widget _purposeDropdownTile() {
    return Consumer<NocProvider>(
      builder: (context, provider, _) {
        return HisenseDropdownTile(
          title: "Purpose",
          titleStyle: tsS14w400c30292F,
          hintText: "Select Your Purpose",
          hintStyle: tsS14w4009F9F9F,
          style: tsS14w400454444,
          value: provider.selectedPurpose,
          validator: (value) {
            if (value == null) {
              return "Select Your Desired Purpose";
            }
            return null;
          },
          contentPadding:
              EdgeInsets.symmetric(horizontal: w * 12, vertical: h * 12),
          onChanged: (String? value) {
            if (value != null) {
              WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
                provider.selectedPurpose = value;
              });
            }
          },
          items: provider.nocPurposeList.map((item) {
            return DropdownMenuItem(
              value: item.text,
              child: Text(
                item.name,
                style: tsS14w400454444,
              ),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _designationAsPerDropdownTile() {
    return Consumer<NocProvider>(
      builder: (context, provider, _) {
        return HisenseDropdownTile(
            title: "Designation as per",
            titleStyle: tsS14w400c30292F,
            hintText: "Select Designation as per",
            hintStyle: tsS14w4009F9F9F,
            style: tsS14w400454444,
            value: provider.selectedDesignationAsPer,
            validator: (value) {
              if (value == null) {
                return "Select Designation as per";
              }
              return null;
            },
            contentPadding:
                EdgeInsets.symmetric(horizontal: w * 12, vertical: h * 12),
            onChanged: (String? value) {
              provider.selectedDesignationAsPer = value;
            },
            items: provider.designationAsPerList.map((item) {
              return DropdownMenuItem<String>(
                value: item['action'],
                child: Text(
                  item['name'].toString(),
                  style: tsS14w400454444,
                ),
              );
            }).toList());
      },
    );
  }

  Widget _calenderWidget(
      {required String subtitle,
      required bool isMandatory,
      required String date,
      required VoidCallback onTap}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _subTitile(subtitle: subtitle, isMandatory: isMandatory),
        Align(
          alignment: Alignment.centerLeft,
          child: GestureDetector(
            onTap: onTap,
            child: Container(
              height: h * 45,
              width: w * 164,
              padding: EdgeInsets.symmetric(horizontal: w * 11),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: ThemeColors.colorFFFFFF,
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: ThemeColors.colorE3E3E3, width: 1),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    date,
                    style: tsS14w400454444,
                  ),
                  Image.asset(
                    "assets/icons/calendar_black.png",
                    height: h * 18,
                    width: w * 18,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _subTitile({required String subtitle, bool isMandatory = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: Row(
            children: [
              Text(
                subtitle,
                style: tsS14w400c30292F,
              ),
              if (isMandatory)
                Text(
                  "*",
                  style: tsS14w400cFA0000,
                ),
            ],
          ),
        ),
        SizedBox(height: h * 4),
      ],
    );
  }

  _calenderWidgets() {
    return Consumer<NocProvider>(
      builder: (context, provider, _) {
        if (provider.selectedPurpose == null ||
            provider.selectedPurpose == "business_purpose") {
          return const SizedBox();
        }
        return Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Consumer<NocProvider>(
                  builder: (context, provider, _) {
                    return _calenderWidget(
                      isMandatory: true,
                      subtitle: "From",
                      date: formatDateFromDate(
                          dateTime: provider.selectedFromDate,
                          format: "dd MMM yyyy"),
                      onTap: () async {
                        // if (Platform.isAndroid) {
                        await provider.selectFromDate(context: context);

                        // } else if (Platform.isIOS) {
                        //   await provider.selectFromDateIOS(ctx: context);
                        // }
                      },
                    );
                  },
                ),
                Consumer<NocProvider>(
                  builder: (context, provider, _) {
                    return _calenderWidget(
                      isMandatory: true,
                      subtitle: "To",
                      date: formatDateFromDate(
                          dateTime: provider.selectedToDate,
                          format: "dd MMM yyyy"),
                      onTap: () async {
                        // if (Platform.isAndroid) {

                        await provider.selectToDate(context: context);

                        // } else if (Platform.isIOS) {
                        //   await provider.selectToDateIOS(ctx: context);
                        // }
                      },
                    );
                  },
                ),
              ],
            ),
            SizedBox(height: h * 15),
            Consumer<NocProvider>(
              builder: (context, provider, _) {
                return _calenderWidget(
                  isMandatory: true,
                  subtitle: "off.  Return Date",
                  date: formatDateFromDate(
                      dateTime: provider.selectedReturnDate,
                      format: "dd MMM yyyy"),
                  onTap: () async {
                    // if (Platform.isAndroid) {

                    await provider.selectReturnDate(context: context);

                    // } else if (Platform.isIOS) {
                    //   await provider.selectToDateIOS(ctx: context);
                    // }
                  },
                );
              },
            ),
            // _calenderWidget(
            //     date: "",
            //     isMandatory: false,
            //     onTap: () {},
            //     subtitle: "off.  Return Date"),
            SizedBox(height: h * 20),
            _divider(),
          ],
        );
      },
    );
  }

  Widget _checkedBoxWidget() {
    return Row(
      children: [
        SizedBox(width: w * 16),
        Consumer<NocProvider>(
          builder: (context, provider, _) {
            return Transform.scale(
              scale: 1.4,
              child: Theme(
                data: ThemeData(
                  checkboxTheme: CheckboxThemeData(
                    side: WidgetStateBorderSide.resolveWith(
                      (states) => BorderSide(
                        width: 1.0,
                        color: ThemeColors.colorE3E3E3,
                      ),
                    ),
                  ),
                ),
                child: Container(
                  height: h * 16,
                  width: w * 16,
                  margin: EdgeInsets.only(left: w * 5, right: w * 13),
                  decoration: BoxDecoration(
                    color: ThemeColors.colorFFFFFF,
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Checkbox(
                    value: provider.isSalaryIncluded,
                    checkColor: Colors.white,
                    fillColor:
                        WidgetStateProperty.all(ThemeColors.primaryColor),
                    activeColor: ThemeColors.primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(5),
                    ),
                    onChanged: (isChecked) {
                      provider.isSalaryIncluded = isChecked!;
                    },
                  ),
                ),
              ),
            );
          },
        ),
        Text(
          "Include Salary",
          style: tsS14w400c30292F,
        )
      ],
    );
  }

  Widget _buttonWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: w * 16.0),
      child: ButtonWidget(
        title: 'Submit',
        textStyle: tsS16w600cFFFFFF,
        color: Theme.of(context).primaryColor,
        onPressed: () async {
          NocProvider provider =
              Provider.of<NocProvider>(context, listen: false);

          if (_salaryTransferFormKey.currentState!.validate()) {
            EasyLoading.show();
            if (provider.selectedPurpose == "tourism_purpose") {
              await nocForTourism(provider: provider);
            }
            if (provider.selectedPurpose == "business_purpose") {
              await nocForBusiness(provider: provider);
            }
            EasyLoading.dismiss();
          }
        },
      ),
    );
  }

  Widget _divider() {
    return Container(
      height: h * 3,
      color: Colors.white,
      margin: EdgeInsets.only(bottom: h * 13),
    );
  }

  Future<void> nocForBusiness({required NocProvider provider}) async {
    await provider.nocCreate(
        context: context,
        entryVisa: _visaNameController.text,
        placeOfTravel: _placeOfTravelController.text,
        purpose: provider.selectedPurpose ?? "",
        to: _toNameController.text,
        travelCountry: _travelCountryNameController.text);
  }

  Future<void> nocForTourism({required NocProvider provider}) async {
    String fromDate = formatDateFromDate(
        dateTime: provider.selectedFromDate, format: "yyyy-MM-dd");
    String toDate = formatDateFromDate(
        dateTime: provider.selectedToDate, format: "yyyy-MM-dd");
    String officeReturnDate = formatDateFromDate(
        dateTime: provider.selectedReturnDate, format: "yyyy-MM-dd");

    await provider.nocCreate(
        context: context,
        entryVisa: _visaNameController.text,
        fromDate: fromDate,
        officeReturnDate: officeReturnDate,
        placeOfTravel: _placeOfTravelController.text,
        purpose: provider.selectedPurpose ?? "",
        to: _toNameController.text,
        toDate: toDate,
        travelCountry: _travelCountryNameController.text);
  }
}
