import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/noc_certificate_list.dart';
import 'package:e8_hr_portal/provider/noc_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/certificate_requests/non_objection_certificates/new_noc_request.dart';
import 'package:e8_hr_portal/view/certificate_requests/non_objection_certificates/view_noc_cetificate.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../salary_certificate/widget/salary_cert_rejected_commend.dart';

class NonObjectionCertificates extends StatelessWidget {
  const NonObjectionCertificates({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "Non-Objection Certificate",
      // actions: [_requestNewButton(context: context)],
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: h * 25),
          Text(
            "Previous Requests",
            style: tsS18w500c181818,
          ),
          SizedBox(height: h * 15),
          Expanded(
            child: Consumer<NocProvider>(
              builder: (context, provider, _) {
                if (provider.nocCertificateList.isEmpty) {
                  return const Center(child: Text("No Data Found"));
                }
                return SmartRefresher(
                  controller: provider.refreshController,
                  onRefresh: provider.onRefresh,
                  header: WaterDropMaterialHeader(
                    backgroundColor: ThemeColors.colorFCC400,
                  ),
                  child: ListView.separated(
                    padding: EdgeInsets.only(bottom: 100 * h),
                    physics: const BouncingScrollPhysics(),
                    itemCount: provider.nocCertificateList.length,
                    itemBuilder: (context, index) {
                      NocCertificateListModel element =
                          provider.nocCertificateList[index];
                      return _requestTile(context: context, element: element);
                    },
                    separatorBuilder: (context, index) =>
                        SizedBox(height: h * 10),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: GeneralButton(
        textStyle: tsS18w600cFFFFFF,
        title: "Request New",
        height: h * 56,
        width: w * 343,
        onPressed: () async {
          NocProvider provider =
              Provider.of<NocProvider>(context, listen: false);
          provider.selectedFromDate = DateTime.now();
          provider.selectedToDate = DateTime.now();
          provider.selectedReturnDate = DateTime.now();
          provider.selectedPurpose = null;
          provider.selectedDesignationAsPer = null;
          PageNavigator.push(
            context: context,
            route: const NewNocRequest(),
          );
        },
      ),
    );
  }

  // Widget _requestNewButton({required BuildContext context}) {
  //   return Padding(
  //     padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
  //     child: InkWell(
  //       borderRadius: BorderRadius.circular(8),
  //       radius: 10,
  //       onTap: () {
  //         NocProvider provider =
  //             Provider.of<NocProvider>(context, listen: false);
  //         provider.selectedFromDate = DateTime.now();
  //         provider.selectedToDate = DateTime.now();
  //         provider.selectedReturnDate = DateTime.now();
  //         provider.selectedPurpose = null;
  //         provider.selectedDesignationAsPer = null;
  //         PageNavigator.push(
  //           context: context,
  //           route: const NewNocRequest(),
  //         );
  //       },
  //       child: Align(
  //         alignment: Alignment.center,
  //         child: Text(
  //           "Request New",
  //           style: tsS14w500cFFFFFF,
  //         ),
  //       ),
  //     ),
  //   );
  // }

  // Widget _popUpMenuItem({required BuildContext context}) {
  //   MasterProvider provider =
  //       Provider.of<MasterProvider>(context, listen: false);
  //   return PopupMenuButton<String>(
  //     icon: Icon(
  //       Icons.more_vert,
  //       size: 25,
  //       color: ThemeColors.colorFFFFFF,
  //     ),
  //     onSelected: (value) {
  //       provider.handleClick(context: context, value: value, route: "noc");
  //     },
  //     itemBuilder: (BuildContext context) {
  //       return provider.pop.map((String choice) {
  //         return PopupMenuItem<String>(
  //           enabled: true,
  //           // height: h * 62,
  //           value: choice,
  //           child: Text(choice),
  //         );
  //       }).toList();
  //     },
  //   );
  // }

  Widget _detailsWidget({required String title, required String date}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: tsS12w400c949494,
        ),
        SizedBox(height: h * 3),
        Text(
          date,
          style: tsS14w500c2C2D33,
        )
      ],
    );
  }

  Widget _status({required String status}) {
    return Container(
      height: h * 23,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: w * 11),
      decoration: BoxDecoration(
        color: status.toLowerCase().trim() == "approved"
            ? ThemeColors.color32936F.withOpacity(0.16)
            : status.toLowerCase().trim() == "pending"
                ? ThemeColors.colorFFF2E2
                : ThemeColors.colorF64D44.withOpacity(0.15),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(status,
          style: status.toLowerCase().trim() == "approved"
              ? tsS12w600c949494
              : status.toLowerCase().trim() == "pending"
                  ? tsS12w600cE5B900
                  : tsS12w600cF64D44),
    );
  }

  Widget _requestTile(
      {required BuildContext context,
      required NocCertificateListModel element}) {
    String? requestedDate = element.requestedDate;
    String? approvedDate = element.approvedOrRejectedDate;
    String? status = element.status;
    String? approvedBy = element.approvedBy;
    int? id = element.id;
    String? rejectedComment = element.rejectedComment;
    // String? status = "proce";
    return GestureDetector(
      onTap: () async {
        if (status?.toLowerCase().trim() == "approved") {
          NocProvider provider =
              Provider.of<NocProvider>(context, listen: false);
          EasyLoading.show();
          bool isGo = await provider.getNocCertificate(
            action: "non_objection_certificate",
            actionId: id ?? 0,
          );
          EasyLoading.dismiss();

          if (isGo) {
            // ignore: use_build_context_synchronously
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => ViewNocCertificate(
                  id: id ?? 0,
                ),
              ),
            );
          }
        } else if (status?.toLowerCase() == "rejected" &&
            rejectedComment != null) {
          showDialog(
            context: context,
            builder: (context) {
              return RequestRejectedCommentDialog(
                  comment: rejectedComment.toString(),
                  rejectedPerson: approvedBy);
            },
          );
        }
      },
      child: Container(
        // height: h * 106,
        width: w * 343,
        padding: EdgeInsets.fromLTRB(w * 10, h * 10, w * 10, h * 13),
        decoration: BoxDecoration(
          color: ThemeColors.colorFFFFFF,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _detailsWidget(
                    date: requestedDate ?? "", title: "Requested Date"),
                if (approvedBy != null && approvedBy.isNotEmpty)
                  SizedBox(height: h * 11),
                if (approvedBy != null &&
                    approvedBy.isNotEmpty &&
                    status?.toLowerCase().trim() != "pending")
                  _detailsWidget(
                      date: approvedBy,
                      title: status?.toLowerCase().trim() == "approved"
                          ? "Approved By"
                          : "Rejected By"),
              ],
            ),
            if (approvedDate != null &&
                approvedDate.isNotEmpty &&
                status?.toLowerCase().trim() != "pending")
              _detailsWidget(
                  date: approvedDate,
                  title: status?.toLowerCase().trim() == "approved"
                      ? "Approved Date"
                      : "Rejected Date"),
            _status(
                status:
                    "${status?[0].toUpperCase()}${status?.substring(1).toLowerCase()}"),
          ],
        ),
      ),
    );
  }
}
