import 'package:flutter/material.dart';
import 'package:e8_hr_portal/provider/experience_certifcate_provider.dart';
import 'package:e8_hr_portal/provider/noc_provider.dart';
import 'package:e8_hr_portal/provider/salary_certificate_provider.dart';
import 'package:e8_hr_portal/provider/salary_transfer_letter_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/certificate_requests/experience_certificate/experience_certificate.dart';
import 'package:e8_hr_portal/view/certificate_requests/non_objection_certificates/non_objection_certificates.dart';
import 'package:e8_hr_portal/view/certificate_requests/salary_certificate/salary_certificate.dart';
import 'package:e8_hr_portal/view/certificate_requests/salary_transfer_certificate/salary_transfer_certificate.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:provider/provider.dart';
import '../../provider/flight_ticket_provider.dart';

class CertificateRequests extends StatefulWidget {
  const CertificateRequests({super.key});

  @override
  State<CertificateRequests> createState() => _CertificateRequestsState();
}

class _CertificateRequestsState extends State<CertificateRequests> {
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
        screenTitle: "Certificate Requests",
        body: Consumer<SalaryCertificateProvider>(
          builder: (context, provider, child) {
            return GridView.count(
              // shrinkWrap: true,
              padding: EdgeInsets.only(top: h * 32),
              childAspectRatio: 1.3,
              crossAxisCount: 2,
              mainAxisSpacing: 15,
              crossAxisSpacing: 15,
              children: List.generate(5, (index) {
                if (index == 0) {
                  return _certificateWidgets(
                    image: "assets/icons/salary_cert.png",
                    title: "Salary Certificate",
                    onTap: () {
                      provider.getSalaryCertificate();
                      Navigator.of(context).push(MaterialPageRoute(
                          builder: (context) => const SalaryCertificate()));
                    },
                  );
                }
                if (index == 1) {
                  return _certificateWidgets(
                    image: "assets/icons/salary_transfer_letter.png",
                    title: "Salary Transfer Letter",
                    onTap: _openSalaryTransferLetter,
                  );
                }
                if (index == 2) {
                  return _certificateWidgets(
                    image: "assets/icons/experience_cert.png",
                    title: "Experience Certificate",
                    onTap: _openExperienceCertificate,
                  );
                }
                if (index == 3) {
                  return _certificateWidgets(
                    image: "assets/icons/non_objection_cert.png",
                    title: "Non-Objection\nCertificates",
                    onTap: () async {
                      Provider.of<FlightTicketProvider>(context, listen: false)
                          .getFlightTicketPersonalInfo();
                      final navigator = Navigator.of(context);
                      NocProvider provider =
                          Provider.of<NocProvider>(context, listen: false);
                      // bool isGo =
                      provider.getNocCertificateList();
                      // if (isGo) {
                      navigator.push(MaterialPageRoute(
                          builder: (context) =>
                              const NonObjectionCertificates()));

                      // }
                    },
                  );
                }
                return const SizedBox();
                // return _certificateWidgets(
                //   image: "assets/icons/driving_license.png",
                //   title: "Driving License",
                //   onTap: () async {
                //     final navigator = Navigator.of(context);
                //     DrivingLicenseProvider provider =
                //         Provider.of<DrivingLicenseProvider>(context,
                //             listen: false);
                //     provider.getDrivingLicenseList();

                //     // if (isGo) {}
                //     navigator.push(MaterialPageRoute(
                //         builder: (context) => const DrivingCertificates()));
                //   },
                // );
              }),
            );
          },
        ));
  }

  _openSalaryTransferLetter() async {
    var provider =
        Provider.of<SalaryTransferLetterProvider>(context, listen: false);

    provider.fetchPreviousRequests();

    if (!mounted) return;
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SalaryTransferCertificate(),
      ),
    );
  }

  _openExperienceCertificate() async {
    var provider =
        Provider.of<ExperienceCertifcateProvider>(context, listen: false);

    provider.fetchPreviousRequests();

    if (!mounted) return;
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ExperienceCertificate(),
      ),
    );
  }

  Widget _certificateWidgets(
      {required String image,
      required String title,
      required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        // height: h * 130,
        width: w * 164,
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: ThemeColors.colorFFFFFF,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Container(
                height: h * 60,
                width: w * 60,
                alignment: Alignment.center,
                margin: EdgeInsets.only(left: w * 10, top: h * 15),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: ThemeColors.colorF8F8F8,
                ),
                child: Image.asset(
                  image,
                  height: h * 24,
                  width: w * 24,
                  color: ThemeColors.primaryColor,
                ),
              ),
            ),
            SizedBox(height: h * 10),
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: EdgeInsets.only(left: w * 12),
                child: Text(
                  title,
                  style: tsS12w500c161616,
                  textAlign: TextAlign.left,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
