// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:e8_hr_portal/model/salary_certificate_model.dart';
import 'package:e8_hr_portal/provider/salary_certificate_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/certificate_requests/new_salary_request/new_salary_request.dart';
import 'package:e8_hr_portal/view/certificate_requests/salary_certificate/view_salary_certificate.dart';
import 'package:e8_hr_portal/view/certificate_requests/salary_certificate/widget/salary_cert_rejected_commend.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class SalaryCertificate extends StatelessWidget {
  const SalaryCertificate({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "Salary Certificate",
      // actions: [_requiestNewButton(context: context)],
      body: Column(
        children: [
          Expanded(
            child: Consumer<SalaryCertificateProvider>(
              builder: (context, provider, child) {
                return
                    //  provider.isCertificatesLoading == true
                    //     ? const Center(
                    //         child: CircularProgressIndicator(),
                    //       )
                    //     :
                    provider.salaryList.isEmpty
                        ? const Center(
                            child: Text(
                              "No Data Found",
                              style:
                                  TextStyle(fontSize: 14, color: Colors.black),
                            ),
                          )
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              SizedBox(height: h * 25),
                              Text(
                                "Previous Requests",
                                style: tsS18w500c181818,
                              ),
                              SizedBox(height: h * 15),
                              Expanded(
                                child: SmartRefresher(
                                  controller: provider.refreshController,
                                  onRefresh: provider.onRefresh,
                                  header: WaterDropMaterialHeader(
                                    backgroundColor: ThemeColors.colorFCC400,
                                  ),
                                  child: ListView.separated(
                                    padding: EdgeInsets.only(bottom: 100 * h),
                                    shrinkWrap: true,
                                    physics: const BouncingScrollPhysics(),
                                    itemCount: provider.salaryList.length,
                                    itemBuilder: (context, index) {
                                      SalaryCertificateModel data =
                                          provider.salaryList[index];
                                      return _requestTile(
                                          context: context, listItem: data);
                                    },
                                    separatorBuilder: (context, index) =>
                                        SizedBox(height: h * 10),
                                  ),
                                ),
                              ),
                            ],
                          );
              },
            ),
          ),
        ],
      ),

      floatingActionButton: GeneralButton(
        textStyle: tsS18w600cFFFFFF,
        title: "Request New",
        height: h * 56,
        width: w * 343,
        onPressed: () async {
          SalaryCertificateProvider provider =
              Provider.of<SalaryCertificateProvider>(context, listen: false);
          bool isGo = await provider.getFlightTicketPersonalInfo();

          if (isGo) {
            PageNavigator.push(
              context: context,
              route: const NewSalaryRequest(),
            );
          }
        },
      ),
    );
  }

  // Widget _requiestNewButton({required BuildContext context}) {
  //   return Padding(
  //     padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
  //     child: InkWell(
  //       borderRadius: BorderRadius.circular(8),
  //       radius: 10,
  //       onTap: () async {},
  //       child: Align(
  //         alignment: Alignment.center,
  //         child: Text(
  //           "Request New",
  //           style: tsS14w500cFFFFFF,
  //         ),
  //       ),
  //     ),
  //     // child: TextButton(
  //     //   onPressed: () async {
  //     //     SalaryCertificateProvider provider =
  //     //         Provider.of<SalaryCertificateProvider>(context, listen: false);
  //     //     bool isGo = await provider.getFlightTicketPersonalInfo();

  //     //     if (isGo) {
  //     //       // ignore: use_build_context_synchronously
  //     //       PageNavigator.push(
  //     //         context: context,
  //     //         route: const NewSalaryRequest(),
  //     //       );
  //     //     }
  //     //   },
  //     //   style: ElevatedButton.styleFrom(
  //     //     backgroundColor: ThemeColors.colorTransparent,
  //     //     minimumSize: const Size(10, 36),
  //     //   ),
  //     //   child: Text(
  //     //     "Request New",
  //     //     style: tsS14w500cFFFFFF,
  //     //   ),
  //     // ),
  //   );
  // }

  Widget _detailsWidget({required String title, required String date}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: tsS12w400c949494,
        ),
        SizedBox(height: h * 3),
        Text(
          date,
          style: tsS14w500c2C2D33,
        )
      ],
    );
  }

  Widget _status({required String status}) {
    Color backGround = ThemeColors.color32936F.withOpacity(0.16);
    TextStyle textStyle = tsS12w600c949494;
    switch (status) {
      case "Approved":
        backGround = ThemeColors.color32936F.withOpacity(0.16);
        textStyle = tsS12w600c949494;
        break;
      case "Pending":
        backGround = const Color(0xffFFF2E2);
        textStyle = tsS12w600E5B900;
        break;
      case 'Rejected':
        backGround = ThemeColors.colorF64D44.withOpacity(0.15);
        textStyle = tsS12w600F64D44;
    }

    return Container(
      height: h * 23,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: w * 11),
      decoration: BoxDecoration(
        color: backGround,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(status, style: textStyle),
    );
  }

  Widget _requestTile(
      {required BuildContext context,
      required SalaryCertificateModel listItem}) {
    return GestureDetector(
      onTap: () async {
        final navigator = Navigator.of(context);
        if (listItem.status == "approved") {
          await Provider.of<SalaryCertificateProvider>(context, listen: false)
              .getSalaryCertificatePdf(certificateId: listItem.id.toString());

          navigator.push(
            MaterialPageRoute(
                builder: (context) => ViewSalaryCertificate(
                      requestId: listItem.id,
                    )),
          );
        } else if (listItem.status?.toLowerCase() == "rejected" &&
            listItem.rejectedComment != null) {
          showDialog(
            barrierDismissible: true,
            context: context,
            builder: (context) => RequestRejectedCommentDialog(
                rejectedPerson: listItem.approvedBy,
                comment: listItem.rejectedComment ?? ""),
          );
        }
      },
      child: Container(
        // height: h * 106,
        width: w * 343,
        padding: EdgeInsets.fromLTRB(w * 10, h * 10, w * 10, h * 13),
        decoration: BoxDecoration(
          color: ThemeColors.colorFFFFFF,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (listItem.requestedDate != null)
                  _detailsWidget(
                      date: listItem.requestedDate ?? "",
                      title: "Requested Date"),
                SizedBox(height: h * 11),
                if (listItem.approvedOrRejectedDate != null)
                  _detailsWidget(
                      date: listItem.approvedBy ?? "",
                      title: listItem.status == "approved"
                          ? "Approved By"
                          : listItem.status == "rejected"
                              ? "Rejected By"
                              : "Pending"),
              ],
            ),
            if (listItem.approvedOrRejectedDate != null)
              _detailsWidget(
                  date: listItem.approvedOrRejectedDate ?? "",
                  title: listItem.status == "approved"
                      ? "Approved Date"
                      : "Rejected Date"),
            _status(
                status: listItem.status == "approved"
                    ? "Approved"
                    : listItem.status == "rejected"
                        ? "Rejected"
                        : "Pending"),
          ],
        ),
      ),
    );
  }
}
