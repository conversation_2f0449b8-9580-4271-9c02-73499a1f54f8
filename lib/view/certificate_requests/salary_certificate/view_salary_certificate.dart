import 'dart:io';

import 'package:flutter/material.dart';
import 'package:e8_hr_portal/provider/salary_certificate_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/certificate_requests/salary_certificate_comments/salary_certificate_comments.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import '../../../provider/general_provider.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../widgets/download_dialog.dart';

class ViewSalaryCertificate extends StatelessWidget {
  final int? requestId;
  const ViewSalaryCertificate({required this.requestId, super.key});

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "",
      actions: [_requiestNewButton(context: context)],
      body: Consumer<SalaryCertificateProvider>(
        builder: (context, provider, child) {
          return Column(
            children: [
              SizedBox(height: h * 37.33),
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: provider.salaryPdf == null
                      ? const Center(
                          child: Text(
                            "No certificate found",
                            style: TextStyle(fontSize: 14, color: Colors.black),
                          ),
                        )
                      : SfPdfViewer.network(
                          provider.salaryPdf!,
                        ),
                ),
              ),
              SizedBox(height: h * 90.08),
              if (provider.salaryPdf != null)
                GeneralButton(
                  height: h * 56,
                  width: w * 343,
                  onPressed: () async {
                    if (Platform.isAndroid) {
                      showDialog(
                        context: context,
                        builder: (context) => DownloadingDialog(
                            fileName: "Element8 NOC",
                            extention: ".pdf",
                            url: provider.salaryPdf!),
                      );
                    }
                    if (Platform.isIOS) {
                      GeneralProvider generalProvider =
                          Provider.of<GeneralProvider>(context, listen: false);
                      await generalProvider.launchUniversalLinkIos(
                          url: provider.salaryPdf!);
                    }
                  },
                  title: "Download",
                  textStyle: tsS18w600cFFFFFF,
                ),
              SizedBox(height: h * 20.08),
            ],
          );
        },
      ),
    );
  }

  Widget _requiestNewButton({required BuildContext context}) {
    return Padding(
        padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
        child:
            //   InkWell(
            // borderRadius: BorderRadius.circular(8),
            // radius: 10,
            // onTap: () {

            //   PageNavigator.push(
            //     context: context,
            //     route: SalaryCertificateComments(requestId: requestId),
            //   );
            // },
            //   child: Padding(
            //     padding: EdgeInsets.only(top: h * 6, left: w * 10, right: w * 10),
            //     child: Text(
            //       "Request Change",
            //       style: tsS14w500cFFFFFF,
            //     ),
            //   ),
            // ),
            //child:
            TextButton(
          onPressed: () {
            PageNavigator.push(
              context: context,
              route: SalaryCertificateComments(requestId: requestId),
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: ThemeColors.colorTransparent,
            minimumSize: const Size(10, 36),
          ),
          child: Text(
            "Request Change",
            style: tsS14w500cFFFFFF,
          ),
        )
        //),
        );
  }
}
