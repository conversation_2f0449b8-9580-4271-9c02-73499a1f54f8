import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/size_config.dart';

import '../../../../util/colors.dart';
import '../../../../util/styles.dart';

class RequestRejectedCommentDialog extends StatelessWidget {
  final String comment;
  final String? rejectedPerson;
  const RequestRejectedCommentDialog(
      {required this.comment, super.key, required this.rejectedPerson});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(26)),
      child: Container(
        constraints: BoxConstraints(minHeight: h * 150),
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(12)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  height: 24,
                  width: 24,
                  margin: const EdgeInsets.only(right: 10),
                  padding: const EdgeInsets.all(5),
                  decoration: BoxDecoration(
                      color: ThemeColors.colorFCD2D0,
                      borderRadius: BorderRadius.circular(6)),
                  child: Container(
                    decoration: BoxDecoration(
                        color: ThemeColors.colorF64D44, shape: BoxShape.circle),
                    child: const Center(
                      child: Icon(
                        Icons.close_rounded,
                        color: Colors.white,
                        size: 10,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          "Request Rejected",
                          style: tsS12w4cF64D44,
                        ),
                      ),
                      if (rejectedPerson != null)
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            rejectedPerson ?? "",
                            style: tsS10w400c646363,
                          ),
                        ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Icon(
                    Icons.close,
                    color: ThemeColors.colorD6D6D6,
                    size: h * 20,
                  ),
                ),
              ],
            ),
            Divider(
              thickness: 1,
              color: ThemeColors.colorD9D9D9,
            ),
            // const SizedBox(height: 5),
            Align(
              alignment: Alignment.topLeft,
              child: Text(
                comment,
                style: tsS14w400979797,
              ),
            )
          ],
        ),
      ),
      // child: Container(
      //   // height: h * 244,
      //   padding: EdgeInsets.all(10),
      //   constraints: BoxConstraints(maxHeight: h * 200),
      //   child: Column(
      //     children: [
      //       Expanded(child: Container()),
      //       Padding(
      //         padding: EdgeInsets.all(15),
      //         child: GeneralButton(
      //           title: "Close",
      //           height: h * 40,
      //           textStyle: tsS14w500cFFFFFF,
      //           width: double.infinity,
      //           onPressed: () {
      //             Navigator.pop(context);
      //           },
      //         ),
      //       ),
      //     ],
      //   ),
      // ),
    );
    // AlertDialog(
    //   contentPadding: EdgeInsets.symmetric(horizontal: w * 25),
    //   title: Column(
    //     crossAxisAlignment: CrossAxisAlignment.end,
    //     children: [
    //       IconButton(
    //           onPressed: () {
    //             Navigator.pop(context);
    //           },
    //           icon: const Icon(Icons.close)),
    //       const Align(alignment: Alignment.center, child: Text("Comment")),
    //     ],
    //   ),
    //   shape: const RoundedRectangleBorder(
    //       borderRadius: BorderRadius.all(Radius.circular(20.0))),
    //   content: SizedBox(
    //     height: h * 200,
    //     width: w * 300,
    //     child: SingleChildScrollView(
    //       child: Column(
    //         crossAxisAlignment: CrossAxisAlignment.stretch,
    //         children: [
    //           SizedBox(height: h * 20),
    //           if (comment.isNotEmpty) Text(comment),
    //           if (comment.isEmpty)
    //             const Center(
    //               child: Text("No comments"),
    //             )
    //         ],
    //       ),
    //     ),
    //   ),
    // );
  }
}
