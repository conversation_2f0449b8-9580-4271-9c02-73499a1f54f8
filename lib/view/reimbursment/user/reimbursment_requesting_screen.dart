import 'package:dotted_border/dotted_border.dart';
import 'package:e8_hr_portal/provider/reimbursment_provider.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:open_file/open_file.dart';
import 'package:provider/provider.dart';
import '../../../model/reimbursment_data_model.dart';
import '../../../model/reimbursment_details_model.dart';
import '../../../util/general_functions.dart';
import '../../widgets/hisense_drop_down_tile.dart';
import 'reimbursment_details_screen.dart';

class ReimbursmentRequestingScreen extends StatefulWidget {
  final bool isForEdit;
  const ReimbursmentRequestingScreen({this.isForEdit = false, super.key});

  @override
  State<ReimbursmentRequestingScreen> createState() =>
      _ReimbursmentRequestingScreenState();
}

class _ReimbursmentRequestingScreenState
    extends State<ReimbursmentRequestingScreen> {
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _dateRangeController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  String? ticketsId;
  String? date;
  @override
  void initState() {
    ReimbursementProvider provider = context.read<ReimbursementProvider>();
    _dateRangeController.text =
        '${formatDateFromDate(dateTime: provider.startDate!, format: 'dd MMM yy')} - ${formatDateFromDate(dateTime: provider.endDate!, format: 'dd MMM yy')}';
    if (widget.isForEdit) {
      ReimbursmentDetailsModel? item = provider.reimbursmentDetailsModel;

      _amountController.text = '${item?.amount ?? ''}';
      _descriptionController.text = item?.description ?? '';
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ReimbursementProvider provider = context.watch<ReimbursementProvider>();
    return HisenseScaffold(
      screenTitle: widget.isForEdit ? 'Edit Request' : 'Create Request',
      onTap: () {},
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16 * w),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.isForEdit ? 'Edit Request' : 'Create Request',
                        style: tsS18w500c161616,
                      ),
                      SizedBox(height: h * 15),
                      HisenseDropdownTile<ReimbursmentDropdownDataModel>(
                          title: 'Select Title',
                          titleStyle: GoogleFonts.poppins(
                            color: const Color(0xFF30292F),
                            fontSize: 12 * f,
                            fontWeight: FontWeight.w400,
                          ),
                          isMandatory: true,
                          hintText: 'Select Title',
                          hintStyle: GoogleFonts.poppins(
                            fontSize: 12 * f,
                            fontWeight: FontWeight.w400,
                          ),
                          style: tsS14w400454444,
                          errorStyle: tsS11w400cerrorColor,
                          value: provider.selectedTitle,
                          contentPadding: EdgeInsets.symmetric(
                              vertical: h * 12, horizontal: w * 11),
                          validator: (value) {
                            if (value == null) {
                              return 'Select a title';
                            }
                            return null;
                          },
                          onChanged: (ReimbursmentDropdownDataModel? value) {
                            if (value != null) {
                              provider.selectedTitle = value;
                            }
                          },
                          items: provider.titleList
                              .map(
                                (e) => DropdownMenuItem(
                                  value: e,
                                  child: Text(
                                    e.title ?? '',
                                    style: tsS14w400454444,
                                  ),
                                ),
                              )
                              .toList()),
                      _textFormFields(
                          controller: _descriptionController,
                          isMandatory: true,
                          title: 'Description',
                          maxLines: 5,
                          labelText: 'Write something...'),
                      _textFormFields(
                        controller: _dateRangeController,
                        isMandatory: true,
                        title: 'Date',
                        labelText: 'Select date range',
                        readOnly: true,
                        onTap: () async {
                          DateTimeRange? dateRange = await showDateRangePicker(
                              context: context,
                              firstDate: DateTime(2000),
                              lastDate: DateTime.now());
                          if (dateRange != null) {
                            provider.startDate = dateRange.start;
                            provider.endDate = dateRange.end;
                            _dateRangeController.text =
                                '${formatDateFromDate(dateTime: provider.startDate!, format: 'dd MMM yy')} - ${formatDateFromDate(dateTime: provider.endDate!, format: 'dd MMM yy')}';
                          }
                        },
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: h * 15),
                              RichText(
                                text: TextSpan(
                                    text: 'Amount',
                                    style: tsS14w400c30292F,
                                    children: const [
                                      TextSpan(
                                          text: '*',
                                          style: TextStyle(color: Colors.red))
                                    ]),
                              ),
                              SizedBox(height: h * 4),
                              Container(
                                  width: 85,
                                  height: 47.5,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(5),
                                    border: Border.all(
                                      width: 1,
                                      color: const Color(0xFFDEE7FF),
                                    ),
                                  ),
                                  child: DropdownButton<
                                          ReimbursmentDropdownDataModel>(
                                      isExpanded: true,
                                      padding: const EdgeInsets.only(
                                          left: 16, right: 4),
                                      underline: const SizedBox(),
                                      value: provider.selectedCurrency,
                                      items: provider.currencyList
                                          .map(
                                            (e) => DropdownMenuItem(
                                                value: e,
                                                child:
                                                    Text(e.currencyCode ?? '')),
                                          )
                                          .toList(),
                                      onChanged: (value) {
                                        if (value != null) {
                                          provider.selectedCurrency = value;
                                        }
                                      })),
                            ],
                          ),
                          Expanded(
                              child: _textFormFields(
                                  controller: _amountController,
                                  title: '',
                                  inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly
                              ])),
                        ],
                      ),
                      SizedBox(height: h * 15),
                      RichText(
                        text: TextSpan(
                            text: 'Upload Documents',
                            style: tsS14w400c30292F,
                            children: const [
                              TextSpan(
                                  text: '*',
                                  style: TextStyle(color: Colors.red))
                            ]),
                      ),
                      SizedBox(height: h * 5),
                      _uploadYourDocumentWidget(context: context),
                    ],
                  ),
                ),
              ),
              const SizedBox(
                height: 15,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _cancelButton(),
                  GeneralButton(
                      title: 'Submit',
                      onPressed: () async {
                        if (_formKey.currentState?.validate() ?? false) {
                          FocusManager.instance.primaryFocus?.unfocus();
                          if (provider.filesList.isNotEmpty ||
                              provider.reimbursementAttachements.isNotEmpty) {
                            if (widget.isForEdit) {
                              provider.editReimbursmentRequest(
                                  description: _descriptionController.text,
                                  context: context,
                                  amount: _amountController.text);
                            } else {
                              provider.createRequest(
                                  description: _descriptionController.text,
                                  context: context,
                                  amount: _amountController.text);
                            }
                          } else {
                            showToastText('Please select document');
                          }
                        }
                      },
                      isDisabled: false,
                      textStyle: tsS18w600cFFFFFF,
                      height: h * 50,
                      width: w * 164),
                ],
              ),
              SizedBox(height: h * 50),
            ],
          ),
        ),
      ),
    );
  }

  Widget _cancelButton() {
    return ElevatedButton(
      onPressed: () {
        FocusManager.instance.primaryFocus?.unfocus();
        Navigator.pop(context);
      },
      style: ElevatedButton.styleFrom(
        elevation: 0,
        backgroundColor: ThemeColors.disabledButtonColor,
        disabledBackgroundColor: Colors.transparent,
        shadowColor: Colors.transparent,
        minimumSize: Size(w * 164, h * 50),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(50),
        ),
      ),
      child: Text(
        'Cancel',
        style: tsS18w600cFFFFFF,
      ),
    );
  }

  Widget _textFormFields(
      {required String title,
      String? labelText,
      int? maxLines,
      TextEditingController? controller,
      Function()? onTap,
      bool readOnly = false,
      List<TextInputFormatter>? inputFormatters,
      bool isMandatory = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: h * 15),
        RichText(
            text: TextSpan(
                text: title,
                style: tsS14w400c30292F,
                children: isMandatory
                    ? [
                        const TextSpan(
                            text: '*', style: TextStyle(color: Colors.red))
                      ]
                    : null)),
        SizedBox(height: h * 4),
        TextFieldWidget(
          readOnly: readOnly,
          controller: controller,
          inputFormatters: inputFormatters,
          hintStyle: tsS12w400c9F9F9F,
          textStyle: tsS14w400c30292F,
          keyboardType: TextInputType.text,
          maxLines: maxLines,
          onTap: onTap,
          validator: Validator.text,
          hintText: labelText,
          contentPadding:
              EdgeInsets.symmetric(vertical: h * 12, horizontal: w * 10),
          textCapitalization: TextCapitalization.sentences,
        ),
      ],
    );
  }

  Widget _uploadYourDocumentWidget({required BuildContext context}) {
    return Consumer<ReimbursementProvider>(
      builder: (context, provider, child) {
        List<ReimbursementAttachements> reimbursementAttachements =
            provider.reimbursementAttachements;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DottedBorder(
              dashPattern: [w * 6, w * 7],
              color: ThemeColors.colorD9D9D9,
              borderType: BorderType.RRect,
              radius: const Radius.circular(3),
              child: GestureDetector(
                  onTap: () async {
                    showDialog(
                      context: context,
                      builder: (ctx) => ReimbursementDocUploadDialog(ctx: ctx),
                    );
                  },
                  child: ClipRRect(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(3),
                    ),
                    child: Container(
                        width: w * 343,
                        color: ThemeColors.colorFFFFFF,
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(
                            top: h * 30, bottom: h * 22, left: 10, right: 10),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Image.asset('assets/icons/upload.png',
                                height: h * 24, width: w * 29.34),
                            SizedBox(height: h * 8),
                            Text(
                              'Click to upload your document',
                              style: tsS14w400c30292F,
                            ),
                            SizedBox(height: h * 3),
                            Text(
                              'Supports :JPG, JPEG, PNG, PDF, DOC, DOCX with max size of 10 MB',
                              style: tsS12w400979797,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        )),
                  )),
            ),
            SizedBox(height: h * 20),
            if (provider.filesList.isNotEmpty ||
                reimbursementAttachements.isNotEmpty)
              Align(
                alignment: Alignment.centerLeft,
                child: SizedBox(
                  height: 80,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (widget.isForEdit &&
                            reimbursementAttachements.isNotEmpty) ...[
                          ListView.separated(
                              shrinkWrap: true,
                              physics: const ClampingScrollPhysics(),
                              padding: const EdgeInsets.only(bottom: 30),
                              scrollDirection: Axis.horizontal,
                              itemBuilder: (context, index) {
                                String? file =
                                    reimbursementAttachements[index].attachment;
                                String extension =
                                    file?.split('.').last.toUpperCase() ?? '';
                                String fileName = file?.split('/').last ?? '';
                                return InkWell(
                                  onTap: () {
                                    launchURL(file ?? '');
                                    // Navigator.push(
                                    //     context,
                                    //     MaterialPageRoute(
                                    //         builder: (context) => DocViewScreen(
                                    //             url: file ?? '')));
                                  },
                                  child: Container(
                                    height: 50,
                                    width: 200,
                                    alignment: Alignment.centerLeft,
                                    padding: const EdgeInsets.only(left: 8),
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(4),
                                        color: Colors.grey[300]),
                                    child: Row(
                                      children: [
                                        formatContainer(extension),
                                        const SizedBox(width: 6),
                                        Expanded(
                                          child: Text(
                                            fileName,
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 1,
                                          ),
                                        ),
                                        IconButton(
                                            onPressed: () {
                                              provider.removeFile(index, false);
                                            },
                                            icon: const Icon(Icons.close))
                                      ],
                                    ),
                                  ),
                                );
                              },
                              separatorBuilder: (context, index) =>
                                  const SizedBox(width: 5),
                              itemCount: reimbursementAttachements.length),
                          const SizedBox(width: 5)
                        ],
                        if (provider.filesList.isNotEmpty)
                          ListView.separated(
                              shrinkWrap: true,
                              physics: const ClampingScrollPhysics(),
                              padding: const EdgeInsets.only(bottom: 30),
                              scrollDirection: Axis.horizontal,
                              itemBuilder: (context, index) {
                                PlatformFile file = provider.filesList[index];
                                String extension =
                                    file.name.split('.')[1].toUpperCase();
                                return InkWell(
                                  onTap: () {
                                    OpenFile.open(file.path);
                                  },
                                  child: Container(
                                    height: 50,
                                    width: 200,
                                    alignment: Alignment.centerLeft,
                                    padding: const EdgeInsets.only(left: 8),
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(4),
                                        color: Colors.grey[300]),
                                    child: Row(
                                      children: [
                                        formatContainer(extension),
                                        const SizedBox(width: 6),
                                        Expanded(
                                          child: Text(
                                            file.name,
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 1,
                                          ),
                                        ),
                                        IconButton(
                                            onPressed: () {
                                              provider.removeFile(index, true);
                                            },
                                            icon: const Icon(Icons.close))
                                      ],
                                    ),
                                  ),
                                );
                              },
                              separatorBuilder: (context, index) =>
                                  const SizedBox(width: 5),
                              itemCount: provider.filesList.length),
                      ],
                    ),
                  ),
                ),
              ),
            // if (provider.filesList.isNotEmpty)
            //   SizedBox(
            //     height: 50,
            //     child: ListView.separated(
            //         scrollDirection: Axis.horizontal,
            //         shrinkWrap: true,
            //         padding: EdgeInsets.zero,
            //         itemBuilder: (context, index) {
            //           PlatformFile file = provider.filesList[index];
            //           String extension = file.name.split('.')[1];
            //           return InkWell(
            //             onTap: () {
            //               // Navigator.pushNamed(context, DocxViewScreen.route,
            //               //         arguments: DocxViewScreen(url: ));
            //             },
            //             child: Container(
            //               height: 50,
            //               width: 200,
            //               alignment: Alignment.centerLeft,
            //               padding: EdgeInsets.only(left: 8),
            //               decoration: BoxDecoration(
            //                   borderRadius: BorderRadius.circular(4),
            //                   color: Colors.grey[300]),
            //               child: Row(
            //                 children: [
            //                   Container(
            //                     width: 40,
            //                     height: 35,
            //                     alignment: Alignment.center,
            //                     decoration: BoxDecoration(
            //                       color: Colors.grey[200],
            //                       borderRadius: BorderRadius.circular(4),
            //                     ),
            //                     child: Text(
            //                       extension,
            //                     ),
            //                   ),
            //                   SizedBox(width: 6),
            //                   Expanded(
            //                     child: Text(
            //                       file.name,
            //                       overflow: TextOverflow.ellipsis,
            //                       maxLines: 1,
            //                     ),
            //                   ),
            //                   IconButton(
            //                       onPressed: () {
            //                         provider.removeFile(index);
            //                       },
            //                       icon: Icon(Icons.close))
            //                 ],
            //               ),
            //             ),
            //           );
            //         },
            //         separatorBuilder: (context, index) => SizedBox(width: 8),
            //         itemCount: provider.filesList.length),
            //   ),
            const SizedBox(height: 20)
          ],
        );
      },
    );
  }

  // InkWell _removeImageButton({required Function() onTap}) {
  //   return InkWell(
  //     onTap: onTap,
  //     child: Container(
  //         margin: const EdgeInsets.all(8),
  //         height: 20,
  //         width: 20,
  //         decoration:
  //             const BoxDecoration(shape: BoxShape.circle, color: Colors.black),
  //         child: const Icon(
  //           Icons.close,
  //           color: Colors.white,
  //           size: 12,
  //         )),
  //   );
  // }
}

class ReimbursementDocUploadDialog extends StatelessWidget {
  final BuildContext ctx;
  const ReimbursementDocUploadDialog({super.key, required this.ctx});

  @override
  Widget build(BuildContext context) {
    ReimbursementProvider provider = context.watch<ReimbursementProvider>();
    return AlertDialog(
      title: const Text('Options'),
      content: SingleChildScrollView(
        child: ListBody(
          children: [
            // GestureDetector(
            //   child: const Text('Capture image'),
            //   onTap: () async {
            //     Navigator.pop(ctx);
            //     reimbursementProvider.openCamera();
            //   },
            // ),
            // const Padding(
            //   padding: EdgeInsets.all(10),
            // ),
            GestureDetector(
              child: const Text('Upload image'),
              onTap: () async {
                Navigator.pop(ctx);
                provider.openGallery();
              },
            ),
            const Padding(
              padding: EdgeInsets.all(10),
            ),
            GestureDetector(
              child: const Text('Upload file'),
              onTap: () async {
                Navigator.pop(ctx);
                provider.openFile();
              },
            ),
          ],
        ),
      ),
    );
  }
}
