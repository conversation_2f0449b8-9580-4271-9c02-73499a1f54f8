import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/reimbursment_details_model.dart';
import 'package:e8_hr_portal/provider/reimbursment_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/other_screens/overview/widgets/cancel_button.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:provider/provider.dart';
import '../../../util/general_functions.dart';
import 'reimbursment_requesting_screen.dart';

class ReimbursmentDetailsScreen extends StatelessWidget {
  const ReimbursmentDetailsScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return Consumer<ReimbursementProvider>(
      builder: (context, provider, child) {
        ReimbursmentDetailsModel? reimbursmentDetailsModel =
            provider.reimbursmentDetailsModel;
        String date = '';
        if (reimbursmentDetailsModel!.startDate ==
            reimbursmentDetailsModel.endDate) {
          date = formatDateFromString(
              reimbursmentDetailsModel.startDate!, 'yyyy-MM-dd', 'dd MMM yy');
        } else {
          date =
              '${formatDateFromString(reimbursmentDetailsModel.startDate!, 'yyyy-MM-dd', 'dd MMM yy')} - ${formatDateFromString(reimbursmentDetailsModel.endDate!, 'yyyy-MM-dd', 'dd MMM yy')}';
        }
        final Divider divider = Divider(
          color: ThemeColors.colorD9D9D9,
          thickness: 1,
        );

        return CustomScaffold(
            screenTitle: 'Reimbursement',
            actions: reimbursmentDetailsModel.status?.toLowerCase() == 'pending'
                ? [_editButton(context: context)]
                : [],
            body: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          // height: h * 254,
                          width: w * 343,
                          margin: EdgeInsets.only(top: h * 36),
                          padding: EdgeInsets.fromLTRB(
                              w * 10, h * 20, w * 10, h * 16),
                          decoration: BoxDecoration(
                            color: ThemeColors.colorFFFFFF,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: _detailsTail(
                                        title: 'Title',
                                        desciption:
                                            reimbursmentDetailsModel.title ??
                                                ''),
                                  ),
                                  Align(
                                      alignment: Alignment.topRight,
                                      heightFactor: 2.9,
                                      child: reimbursmentStatusCard(
                                          status:
                                              reimbursmentDetailsModel.status ??
                                                  '')),
                                ],
                              ),
                              divider,
                              _detailsTail(
                                  title: 'Description',
                                  desciption:
                                      reimbursmentDetailsModel.description ??
                                          ''),
                              divider,
                              _detailsTail(title: 'Date', desciption: date),
                              divider,
                              _detailsTail(
                                  title: 'Amount',
                                  desciption:
                                      '${reimbursmentDetailsModel.currency} ${reimbursmentDetailsModel.amount}'),
                              Text(
                                'Document',
                                style: tsS12w400c949494,
                              ),
                              SizedBox(height: h * 5),
                              SizedBox(
                                height: 50,
                                child: ListView.separated(
                                    shrinkWrap: true,
                                    padding: EdgeInsets.zero,
                                    scrollDirection: Axis.horizontal,
                                    itemBuilder: (context, index) {
                                      String? file = reimbursmentDetailsModel
                                          .reimbursementAttachements?[index]
                                          .attachment;
                                      // return ClipRRect(
                                      //     borderRadius:
                                      //         BorderRadius.circular(8),
                                      //     child: InkWell(
                                      //       onTap: () {
                                      //         if (image != null) {
                                      //           PageNavigator.push(
                                      //             context: context,
                                      //             route: PhotoViewScreen(
                                      //               image: image,
                                      //               extension: 'jpeg',
                                      //             ),
                                      //           );
                                      //         }
                                      //       },
                                      //       child: Image.network(
                                      //         height: 120,
                                      //         width: 150,
                                      //         image ?? '',
                                      //         fit: BoxFit.cover,
                                      //       ),
                                      //     ));
                                      String extension =
                                          file?.split('.').last.toUpperCase() ??
                                              '';
                                      String fileName =
                                          file?.split('/').last ?? '';
                                      return InkWell(
                                        onTap: () {
                                          launchURL(file ?? '');
                                          // Navigator.push(
                                          //     context,
                                          //     MaterialPageRoute(
                                          //         builder: (context) =>
                                          //             DocViewScreen(
                                          //                 url: file ?? '')));
                                        },
                                        child: Container(
                                          height: 50,
                                          width: 200,
                                          alignment: Alignment.centerLeft,
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8),
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                              color: Colors.grey[300]),
                                          child: Row(
                                            children: [
                                              formatContainer(extension),
                                              const SizedBox(width: 6),
                                              Expanded(
                                                child: Text(
                                                  fileName,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  maxLines: 1,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                    separatorBuilder: (context, index) =>
                                        const SizedBox(width: 5),
                                    itemCount: reimbursmentDetailsModel
                                            .reimbursementAttachements
                                            ?.length ??
                                        0),
                              ),
                              SizedBox(height: h * 8),
                              divider,
                              Text(
                                'Reporting Persons',
                                style: tsS12w400c949494,
                              ),
                              SizedBox(height: h * 3),
                              _reportingPersonsWidget(
                                  reimbursmentDetailsModel.levelUsers),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                        rejectStatusBuilder(
                            reimbursmentDetailsModel.remarks ?? []),
                        hrApproveOrRejectCard(reimbursmentDetailsModel),
                        const SizedBox(height: 80)
                      ],
                    ),
                  ),
                ),
                SizedBox(height: h * 30),
              ],
            ),
            floatingActionButton:
                reimbursmentDetailsModel.status?.toLowerCase() == 'pending'
                    ? _cancelButton(context: context)
                    : null);
      },
    );
  }

  Widget _detailsTail({required String title, required String desciption}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: tsS12w400c949494,
        ),
        SizedBox(height: h * 3),
        Text(
          desciption,
          style: tsS14w500c2C2D33,
        ),
        SizedBox(height: h * 8),
      ],
    );
  }

  Widget _editButton({required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () async {
          ReimbursementProvider provider =
              context.read<ReimbursementProvider>();
          ReimbursmentDetailsModel? reimbursmentDetailsModel =
              provider.reimbursmentDetailsModel;
          provider.selectedCurrency = provider.currencyList.firstWhere(
              (element) =>
                  element.currencyCode == reimbursmentDetailsModel?.currency);
          if (provider.titleList.any(
              (element) => element.title == reimbursmentDetailsModel?.title)) {
            provider.selectedTitle = provider.titleList.firstWhere(
                (element) => element.title == reimbursmentDetailsModel?.title);
          }
          provider.reimbursementAttachements = reimbursmentDetailsModel
                  ?.reimbursementAttachements
                  ?.map((e) => e)
                  .toList() ??
              [];
          provider.deletedImagesIdList.clear();
          provider.startDate = stringToDateTime(
              date: reimbursmentDetailsModel?.startDate ?? '',
              format: 'yyyy-MM-dd');
          provider.endDate = stringToDateTime(
              date: reimbursmentDetailsModel?.endDate ?? '',
              format: 'yyyy-MM-dd');
          provider.filesList.clear();
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => const ReimbursmentRequestingScreen(
                        isForEdit: true,
                      )));
        },
        child: Align(
          alignment: Alignment.center,
          child: Padding(
            padding: EdgeInsets.only(left: w * 20, right: w * 20, top: h * 5),
            child: Text(
              'Edit',
              style: tsS14w500cFFFFFF,
            ),
          ),
        ),
      ),
    );
  }
}

Widget _cancelButton({required BuildContext context}) {
  return Padding(
      padding: const EdgeInsets.fromLTRB(1, 1, 1, 1),
      child: CancelButton(
        height: h * 56,
        width: w * 343,
        title: 'Cancel Request',
        textStyle: tsS18w600cFFFFFF,
        onPressed: () async {
          showModalBottomSheet(
            context: context,
            isDismissible: true,
            backgroundColor: ThemeColors.colorFFFFFF,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            builder: (context) {
              return _showModalBottomSheet(context: context);
            },
          );
        },
      )
      // : const SizedBox(),
      );
}

Widget _showModalBottomSheet({required BuildContext context}) {
  return Container(
    height: h * 280,
    decoration: BoxDecoration(
      // color: ThemeColors.colorFFFFFF,
      borderRadius: BorderRadius.circular(12),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: h * 14),
        Container(
          height: h * 5,
          width: w * 134,
          decoration: BoxDecoration(
            color: ThemeColors.colorEAEBED,
            borderRadius: BorderRadius.circular(100),
          ),
        ),
        SizedBox(height: h * 44),
        Text(
          'Cancel reimbursment\nrequest',
          style: tsS26w500cFFFFFF,
          textAlign: TextAlign.center,
        ),
        SizedBox(height: h * 2),
        Text('Are you sure ? ', style: tsS16w500c9F9F9F),
        SizedBox(height: h * 33),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: w * 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GeneralButton(
                height: h * 50,
                width: w * 164,
                isDisabledColor: true,
                title: 'No',
                textStyle: tsS18w600cFFFFFF,
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              GeneralButton(
                height: h * 50,
                width: w * 164,
                title: 'Yes',
                textStyle: tsS18w600cFFFFFF,
                onPressed: () async {
                  Navigator.pop(context);
                  ReimbursementProvider provider =
                      context.read<ReimbursementProvider>();
                  provider.cancelReimbursment();
                },
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

Widget reimbursmentStatusCard({required String status}) {
  Color color = ThemeColors.color32936F.withOpacity(0.16);
  TextStyle textStyle = tsS12w600c949494;
  String text = 'Pending';
  switch (status.toLowerCase()) {
    case 'pending':
      textStyle = tsS13FFEFC9;
      color = ThemeColors.colorFFEFC9;
      text = 'Pending';

      break;
    case 'inprogress':
      textStyle = tsS13FFEFC9;
      color = ThemeColors.colorFFEFC9;
      text = 'In-progress';

      break;
    case 'approved':
      textStyle = tsS13BBFFCF;
      color = ThemeColors.colorBBFFCF;
      text = 'Approved';
      break;
    case 'rejected':
      textStyle = tsS13FFBBBB;
      color = ThemeColors.colorFFBBBB;
      text = 'Rejected';
      break;
    case 'cancelled':
      textStyle = tsS13FFBBBB;
      color = ThemeColors.colorFFBBBB;
      text = 'Cancelled';
      break;
    case 'expired':
      textStyle = tsS13FFBBBB;
      color = ThemeColors.colorFFBBBB;
      text = 'Expired';
      break;
  }

  return Container(
    height: h * 23,
    alignment: Alignment.center,
    padding: EdgeInsets.symmetric(horizontal: w * 11),
    decoration: BoxDecoration(
      color: color,
      borderRadius: BorderRadius.circular(8),
    ),
    child: Text(text, style: textStyle),
  );
}

Widget _reportingPersonsWidget(List<LevelUsers>? levelUsers) {
  return SingleChildScrollView(
    scrollDirection: Axis.horizontal,
    child: Row(
      children: levelUsers!.map((e) {
        return Stack(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(5.0),
                  child: ClipOval(
                    child: Stack(
                      children: [
                        CachedNetworkImage(
                          imageUrl: e.addedByProf ?? '',
                          width: 45 * w,
                          height: 45 * h,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) {
                            return Container(
                              height: 50,
                              width: 50,
                              decoration: BoxDecoration(
                                color: ThemeColors.primaryColor,
                                shape: BoxShape.circle,
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                e.addedBy?.substring(0, 1).toUpperCase() ?? '',
                                style: GoogleFonts.rubik(
                                    fontSize: 22,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.white),
                              ),
                            );
                          },
                        ),
                        if (e.approvalStatus?.toLowerCase().trim() == 'pending')
                          Container(
                            height: 45 * h,
                            width: 45 * w,
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                      ],
                    ),
                  )),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              child: Column(
                children: [
                  if (e.approvalStatus?.toLowerCase().trim() == 'pending')
                    const SizedBox()
                  else if (e.approvalStatus?.toLowerCase().trim() == 'approved')
                    ImageIcon(
                      const AssetImage(
                        'assets/icons/tick.png',
                      ),
                      size: 13,
                      color: ThemeColors.color06AA37,
                    )
                  else if (e.approvalStatus?.toLowerCase().trim() == 'rejected')
                    ImageIcon(
                      const AssetImage(
                        'assets/icons/close_red.png',
                      ),
                      size: 13,
                      color: ThemeColors.colorB80000,
                    ),
                ],
              ),
            ),
          ],
        );
      }).toList(),
    ),
  );
}

Widget _leaveRejectedCard(
    {required String rejectedBy,
    required String comment,
    required String? rejectedDate,
    required String? appOrReject}) {
  return Container(
    padding: const EdgeInsets.all(15),
    decoration: BoxDecoration(
        color: Colors.white, borderRadius: BorderRadius.circular(12)),
    child: Column(
      children: [
        Row(
          children: [
            Container(
              height: 24,
              width: 24,
              margin: const EdgeInsets.only(right: 10),
              padding: const EdgeInsets.all(5),
              decoration: BoxDecoration(
                  color: appOrReject?.toLowerCase() == 'rejected'
                      ? ThemeColors.colorFCD2D0
                      : ThemeColors.color06AA37.withOpacity(.2),
                  borderRadius: BorderRadius.circular(6)),
              child: Container(
                decoration: BoxDecoration(
                    color: appOrReject?.toLowerCase() == 'rejected'
                        ? ThemeColors.colorF64D44
                        : ThemeColors.color06AA37,
                    shape: BoxShape.circle),
                child: Center(
                  child: Icon(
                    appOrReject?.toLowerCase() == 'rejected'
                        ? Icons.close_rounded
                        : Icons.done_rounded,
                    color: Colors.white,
                    size: 10,
                  ),
                ),
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    appOrReject?.toLowerCase() == 'rejected'
                        ? 'Request Rejected'
                        : 'Request Approved',
                    style: appOrReject?.toLowerCase() == 'rejected'
                        ? tsS12w4cF64D44
                        : tsS12w4c06AA37,
                  ),
                ),
                SizedBox(
                  height: 15,
                  width: 280 * w,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(rejectedBy, style: tsS10w400c646363),
                      Text(
                          formatDateFromString(
                              rejectedDate ?? '', 'yyyy-MM-dd', 'dd MMM yyyy'),
                          style: tsS10w400c646363),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        Divider(
          thickness: 1,
          color: ThemeColors.colorD9D9D9,
        ),
        const SizedBox(height: 12),
        Align(
          alignment: Alignment.topLeft,
          child: Text(
            comment,
            style: tsS14w400979797,
          ),
        )
      ],
    ),
  );
}

Widget rejectStatusBuilder(List<Remarks> remarks) {
  if (remarks.isEmpty) {
    return const SizedBox();
  }
  return ListView.separated(
    itemCount: remarks.length,
    shrinkWrap: true,
    physics: const NeverScrollableScrollPhysics(),
    itemBuilder: (context, index) {
      Remarks e = remarks[index];
      return _leaveRejectedCard(
        comment: e.remarks ?? '',
        rejectedBy: e.addedBy ?? '',
        rejectedDate: e.createdAt ?? '',
        appOrReject: e.approvalStatus ?? '',
      );
    },
    separatorBuilder: (context, index) {
      return SizedBox(height: h * 10);
    },
  );
}

// class DocViewScreen extends StatefulWidget {
//   static const route = '/docx_view_screen';

//   final String url;
//   const DocViewScreen({super.key, required this.url});

//   @override
//   State<DocViewScreen> createState() => _DocViewScreenState();
// }

// class _DocViewScreenState extends State<DocViewScreen> {
//   WebViewController? webController;

//   get colorFFFFFF => null;

//   @override
//   void initState() {
//     super.initState();
//     String url = widget.url;

//     if (Platform.isAndroid) {
//       String msOfficeUrl = 'https://view.officeapps.live.com/op/view.aspx?src=';
//       url = '$msOfficeUrl$url';
//     }
//     Uri uri = Uri.parse(url);
//     webController = WebViewController()
//       ..clearCache()
//       ..setJavaScriptMode(JavaScriptMode.unrestricted)
//       ..setBackgroundColor(const Color.fromRGBO(0, 0, 0, 0))
//       ..setNavigationDelegate(
//         NavigationDelegate(
//           onUrlChange: (change) {
//             EasyLoading.dismiss();
//           },
//           onNavigationRequest: (request) async {
//             // return NavigationDecision.prevent;
//             return NavigationDecision.navigate;
//           },
//           onPageStarted: (String url) {
//             EasyLoading.show();
//           },
//           onPageFinished: (String url) {
//             EasyLoading.dismiss();
//           },
//           onWebResourceError: (error) {
//             // Navigator.pop(context);
//           },
//           onHttpAuthRequest: (request) {},
//           onProgress: (progress) {
//             if (progress == 100) {
//               EasyLoading.dismiss();
//             } else {
//               EasyLoading.show();
//             }
//           },
//         ),
//       )
//       ..loadRequest(uri);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: colorFFFFFF,
//       appBar: AppBar(
//         actions: [
//           IconButton(
//               onPressed: () {
//                 launchURL(widget.url);
//               },
//               icon: const Icon(
//                 Icons.download,
//                 color: Colors.black,
//               )),
//           const SizedBox(width: 5)
//         ],
//       ),
//       body: WebViewWidget(controller: webController!),
//     );
//   }
// }

Container formatContainer(String extension) {
  return Container(
    width: 40,
    height: 35,
    alignment: Alignment.center,
    decoration: BoxDecoration(
      color: Colors.grey[200],
      borderRadius: BorderRadius.circular(4),
    ),
    child: Text(
      extension,
      style: const TextStyle(fontSize: 10),
    ),
  );
}

hrApproveOrRejectCard(ReimbursmentDetailsModel? reimbursmentDetailsModel) {
  String appOrReject = reimbursmentDetailsModel?.hrStatus ?? '';
  String? hrName = reimbursmentDetailsModel?.hrName;
  List<HrAttachment> hrAttachment = reimbursmentDetailsModel?.hrAttachment
          ?.where((element) => element.hrAttachment != null)
          .toList() ??
      [];
  if ((reimbursmentDetailsModel?.hrRemarks == null ||
          reimbursmentDetailsModel!.hrRemarks!.isEmpty) &&
      (hrAttachment.isEmpty)) {
    return const SizedBox();
  }
  log('${reimbursmentDetailsModel?.hrRemarks}');
  return Container(
    padding: const EdgeInsets.all(15),
    margin: EdgeInsets.only(top: 10),
    decoration: BoxDecoration(
        color: Colors.white, borderRadius: BorderRadius.circular(12)),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              height: 24,
              width: 24,
              margin: const EdgeInsets.only(right: 10),
              padding: const EdgeInsets.all(5),
              decoration: BoxDecoration(
                  color: appOrReject.toLowerCase() == 'rejected'
                      ? ThemeColors.colorFCD2D0
                      : ThemeColors.color06AA37.withOpacity(.2),
                  borderRadius: BorderRadius.circular(6)),
              child: Container(
                decoration: BoxDecoration(
                    color: appOrReject.toLowerCase() == 'rejected'
                        ? ThemeColors.colorF64D44
                        : ThemeColors.color06AA37,
                    shape: BoxShape.circle),
                child: Center(
                  child: Icon(
                    appOrReject.toLowerCase() == 'rejected'
                        ? Icons.close_rounded
                        : Icons.done_rounded,
                    color: Colors.white,
                    size: 10,
                  ),
                ),
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    appOrReject.toLowerCase() == 'rejected'
                        ? 'Request Rejected'
                        : 'Request Approved',
                    style: appOrReject.toLowerCase() == 'rejected'
                        ? tsS12w4cF64D44
                        : tsS12w4c06AA37,
                  ),
                ),
                SizedBox(
                  height: 15,
                  width: 280 * w,
                  child: Text(hrName ?? 'HR', style: tsS10w400c646363),
                ),
              ],
            ),
          ],
        ),
        Divider(
          thickness: 1,
          color: ThemeColors.colorD9D9D9,
        ),
        if (reimbursmentDetailsModel?.hrRemarks != null) ...[
          const SizedBox(height: 12),
          Text(
            reimbursmentDetailsModel?.hrRemarks ?? '',
            style: tsS14w400979797,
          ),
        ],
        SizedBox(height: 5),
        if (hrAttachment.isNotEmpty)
          Container(
            margin: EdgeInsets.only(bottom: 10),
            height: 50,
            child: ListView.separated(
                shrinkWrap: true,
                scrollDirection: Axis.horizontal,
                itemBuilder: (context, index) {
                  String? file = hrAttachment[index].hrAttachment;
                  String extension = file?.split('.').last.toUpperCase() ?? '';
                  String fileName = file?.split('/').last ?? '';
                  return InkWell(
                    onTap: () {
                      launchURL(file ?? '');
                      // Navigator.push(
                      //     context,
                      //     MaterialPageRoute(
                      //         builder: (context) =>
                      //             DocViewScreen(url: file ?? '')));
                    },
                    child: Container(
                      height: 50,
                      width: 200,
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: Colors.grey[300]),
                      child: Row(
                        children: [
                          formatContainer(extension),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              fileName,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
                separatorBuilder: (context, index) => const SizedBox(width: 5),
                itemCount: hrAttachment.length),
          ),
      ],
    ),
  );
}
