// ignore_for_file: use_build_context_synchronously

import 'package:e8_hr_portal/provider/reimbursment_provider.dart';
import 'package:e8_hr_portal/view/reimbursment/user/reimbursment_details_screen.dart';
import 'package:e8_hr_portal/view/reimbursment/user/reimbursment_requesting_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';
import '../../../model/reimbursment_model.dart';
import '../../../util/colors.dart';
import '../../../util/date_formatter.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../../widgets/hisense_scaffold.dart';

class ReimbursmentListingScreen extends StatelessWidget {
  const ReimbursmentListingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    ReimbursementProvider provider = context.watch<ReimbursementProvider>();
    List<ReimbursmentModel> reimbursmentList = provider.reimbursmentList;
    return HisenseScaffold(
      screenTitle: 'Reimbursement',
      actions: provider.showCreateRequestButton == true
          ? [_createTicketButton(context: context)]
          : [],
      onTap: () {},
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16 * w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: h * 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Reimbursement Requests',
                  style: tsS18w500c161616,
                ),
                Container(
                  padding: const EdgeInsets.fromLTRB(10, 5, 5, 5),
                  width: 90 * w,
                  height: 30 * h,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: DropdownButton(
                    isExpanded: true,
                    underline: const SizedBox(),
                    value: provider.selectedFilter,
                    items: provider.statusList
                        .map((e) => DropdownMenuItem(
                            value: e, child: FittedBox(child: Text(e))))
                        .toList(),
                    onChanged: (String? value) {
                      if (value != null) {
                        provider.selectedFilter = value;
                        provider.fetchReimbursmentList();
                      }
                    },
                  ),
                )
                // InkWell(
                //   onTap: () async {
                //     if (provider.startDate != null &&
                //         provider.endDate != null) {
                //       _dateController.text =
                //           '${formatDateFromDate(dateTime: provider.startDate!, format: 'dd MMM yy')} - ${formatDateFromDate(dateTime: provider.endDate!, format: 'dd MMM yy')}';
                //     }
                //     showModalBottomSheet<void>(
                //         useSafeArea: true,
                //         context: context,
                //         isDismissible: true,
                //         isScrollControlled: true,
                //         shape: const RoundedRectangleBorder(
                //           borderRadius: BorderRadius.vertical(
                //             top: Radius.circular(12),
                //           ),
                //         ),
                //         builder: (BuildContext context) {
                //           return Padding(
                //             padding: const EdgeInsets.symmetric(horizontal: 15),
                //             child: Column(
                //                 crossAxisAlignment: CrossAxisAlignment.start,
                //                 mainAxisAlignment: MainAxisAlignment.center,
                //                 mainAxisSize: MainAxisSize.min,
                //                 children: [
                //                   Align(
                //                     alignment: Alignment.center,
                //                     child: Container(
                //                       margin: const EdgeInsets.symmetric(
                //                           vertical: 14),
                //                       height: 5,
                //                       width: 135,
                //                       decoration: BoxDecoration(
                //                           color: ThemeColors.colorD9D9D9),
                //                     ),
                //                   ),
                //                   SizedBox(
                //                     height: h * 15,
                //                   ),
                //                   Align(
                //                       alignment: Alignment.centerLeft,
                //                       child: Text(
                //                         'Filter',
                //                         style: tsS14w4009F9F9F,
                //                       )),
                //                   SizedBox(height: h * 15),
                //                   Text(
                //                     'Date',
                //                     style: tsS14w400c30292F,
                //                   ),
                //                   SizedBox(height: h * 8),
                //                   TextFieldWidget(
                //                     controller: _dateController,
                //                     labelText: 'Select date range',
                //                     readOnly: true,
                //                     contentPadding: EdgeInsets.symmetric(
                //                         vertical: h * 12, horizontal: w * 11),
                //                     onTap: () async {
                //                       DateTimeRange? dateRange =
                //                           await showDateRangePicker(
                //                               context: context,
                //                               firstDate: DateTime(2000),
                //                               lastDate: DateTime.now());
                //                       if (dateRange != null) {
                //                         provider.startDate = dateRange.start;
                //                         provider.endDate = dateRange.end;
                //                         _dateController.text =
                //                             '${formatDateFromDate(dateTime: provider.startDate!, format: 'dd MMM yy')} - ${formatDateFromDate(dateTime: provider.endDate!, format: 'dd MMM yy')}';
                //                       }
                //                     },
                //                   ),
                //                   SizedBox(height: 15),
                //                   HisenseDropdownTile(
                //                       title: 'Title',
                //                       titleStyle: GoogleFonts.poppins(
                //                         color: const Color(0xFF30292F),
                //                         fontSize: 12 * f,
                //                         fontWeight: FontWeight.w400,
                //                       ),
                //                       hintText: 'Title',
                //                       hintStyle: GoogleFonts.poppins(
                //                         fontSize: 12 * f,
                //                         fontWeight: FontWeight.w400,
                //                       ),
                //                       style: tsS14w400454444,
                //                       errorStyle: tsS11w400cerrorColor,
                //                       value: provider.selectedTitle,
                //                       contentPadding: EdgeInsets.symmetric(
                //                           vertical: h * 12, horizontal: w * 11),
                //                       validator: (value) {
                //                         if (value == null) {
                //                           return 'Select a title';
                //                         }
                //                         return null;
                //                       },
                //                       onChanged: (value) {
                //                         if (value != null) {
                //                           provider.selectedTitle = value;
                //                         }
                //                       },
                //                       items: provider.titleList
                //                           .map(
                //                             (e) => DropdownMenuItem(
                //                               value: e,
                //                               child: Text(
                //                                 e.title ?? '',
                //                                 style: tsS14w400454444,
                //                               ),
                //                             ),
                //                           )
                //                           .toList()),
                //                   SizedBox(height: 20),
                //                   Row(
                //                     children: [
                //                       Expanded(
                //                           child: GeneralButton(
                //                               title: 'Clear',
                //                               height: 50,
                //                               width: double.infinity,
                //                               onPressed: () {
                //                                 provider.startDate = null;
                //                                 provider.endDate = null;
                //                                 _dateController.clear();
                //                                 provider
                //                                     .fetchReimbursmentList();
                //                               })),
                //                       SizedBox(width: 8),
                //                       Expanded(
                //                           child: GeneralButton(
                //                               title: 'Submit',
                //                               height: 50,
                //                               width: double.infinity,
                //                               onPressed: () {
                //                                 Navigator.pop(context);
                //                                 provider
                //                                     .fetchReimbursmentList();
                //                               }))
                //                     ],
                //                   ),
                //                   SizedBox(height: 50)
                //                 ]),
                //           );
                //         });
                //   },
                //   child: const ImageIcon(
                //     AssetImage(
                //       "assets/icons/filter.png",
                //     ),
                //   ),
                // ),
              ],
            ),
            SizedBox(height: h * 20),
            Expanded(
                child: reimbursmentList.isEmpty
                    ? const Center(
                        child: Text('No data found'),
                      )
                    : ListView.separated(
                        padding: EdgeInsets.zero,
                        itemBuilder: (context, index) {
                          ReimbursmentModel? item = reimbursmentList[index];
                          return InkWell(
                              onTap: () async {
                                await provider.fetchReimbursmentDetails(
                                    id: item.id);
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            const ReimbursmentDetailsScreen()));
                              },
                              child: _requesTile(item, context));
                        },
                        separatorBuilder: (context, index) =>
                            SizedBox(height: h * 10),
                        itemCount: reimbursmentList.length))
          ],
        ),
      ),
    );
  }
}

Widget _createTicketButton({required BuildContext context}) {
  return Padding(
    padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
    child: InkWell(
      borderRadius: BorderRadius.circular(8),
      onTap: () async {
        EasyLoading.show();
        ReimbursementProvider provider = context.read<ReimbursementProvider>();
        provider.selectedCurrency = provider.currencyList
            .firstWhere((element) => element.id == provider.currency);
        provider.selectedTitle = null;
        provider.startDate = DateTime.now();
        provider.endDate = DateTime.now();
        provider.filesList.clear();
        provider.reimbursementAttachements.clear();
        EasyLoading.dismiss();
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => const ReimbursmentRequestingScreen()));
      },
      child: Align(
        alignment: Alignment.center,
        child: Padding(
          padding: EdgeInsets.only(left: w * 10, right: w * 10, top: h * 5),
          child: Text(
            'Create Request',
            style: tsS14w500cFFFFFF,
          ),
        ),
      ),
    ),
  );
}

Widget _requesTile(ReimbursmentModel item, BuildContext context) {
  String date = '';
  if (item.startDate == item.endDate) {
    date = formatDateFromString(item.startDate!, 'yyyy-MM-dd', 'dd MMM yy');
  } else {
    date =
        '${formatDateFromString(item.startDate!, 'yyyy-MM-dd', 'dd MMM yy')} - ${formatDateFromString(item.endDate!, 'yyyy-MM-dd', 'dd MMM yy')}';
  }
  return Container(
    key: UniqueKey(),
    // height: h * 80,
    width: w * 343,
    padding: EdgeInsets.fromLTRB(w * 10, h * 10, w * 10, h * 10),
    decoration: BoxDecoration(
      color: ThemeColors.colorFFFFFF,
      borderRadius: BorderRadius.circular(8),
    ),
    child: Row(
      children: [
        Container(
          height: h * 48,
          width: w * 48,
          alignment: Alignment.center,
          padding: const EdgeInsets.all(14.15),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: ThemeColors.colorF8F8F8,
          ),
          child: Image.asset(
            'assets/icons/tickets.png',
            color: ThemeColors.primaryColor,
            // height: h * 19.71,
            // width: w * 17.88,
          ),
        ),
        SizedBox(width: w * 11),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.title ?? '',
                style: tsS14w500c161616,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: h * 2),
              Text(
                date,
                style: tsS12w400c979797,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )
            ],
          ),
        ),
        Align(
            alignment: Alignment.topRight,
            heightFactor: 2.9,
            child: reimbursmentStatusCard(status: item.status ?? '')),
      ],
    ),
  );
}
