import 'dart:io';

import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/provider/reimbursment_provider.dart';
import 'package:e8_hr_portal/util/capitalize.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/view/reimbursment/reporting_person/widgets/request_accept_dialog.dart';
import 'package:e8_hr_portal/view/reimbursment/reporting_person/widgets/request_rejection_dialog.dart';
import 'package:easy_stepper/easy_stepper.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../model/reimbursment_details_model.dart';
import '../../../util/colors.dart';
import '../../../util/general_functions.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../../widgets/hisense_scaffold.dart';
import '../../widgets/general_button.dart';
import '../user/reimbursment_details_screen.dart';

class ReimbursementRequestDetailsScreenForReportingPerson
    extends StatelessWidget {
  ReimbursementRequestDetailsScreenForReportingPerson({super.key});
  @override
  Widget build(BuildContext context) {
    ReimbursementProvider provider = context.watch<ReimbursementProvider>();

    ReimbursmentDetailsModel? reimbursmentDetailsModel =
        provider.reimbursmentDetailsModel;
    String date = '';
    String? startDate = reimbursmentDetailsModel?.startDate;
    String? endDate = reimbursmentDetailsModel?.endDate;
    if ((startDate != null && endDate != null)) {
      if (startDate == endDate) {
        date = formatDateFromString(
            reimbursmentDetailsModel!.startDate!, 'yyyy-MM-dd', 'dd MMM yy');
      } else {
        date =
            '${formatDateFromString(reimbursmentDetailsModel!.startDate!, 'yyyy-MM-dd', 'dd MMM yy')} - ${formatDateFromString(reimbursmentDetailsModel.endDate!, 'yyyy-MM-dd', 'dd MMM yy')}';
      }
    }
    bool showButton = (reimbursmentDetailsModel?.levelUsers
                ?.firstWhere((element) => element.addedById == LoginModel.uid)
                .showButton ??
            false) &&
        reimbursmentDetailsModel?.status?.toLowerCase() != 'cancelled';
    return HisenseScaffold(
      screenTitle: 'Reimbursement Details',
      onTap: () {},
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16 * w),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Request Details',
                      style: tsS18w500c181818,
                    ),
                    SizedBox(height: h * 10),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 10 * h),
                          _steperWidget(reimbursmentDetailsModel),
                          divider,
                          SizedBox(height: 10 * h),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: _detailsTail(
                                    title: 'Title',
                                    desciption:
                                        reimbursmentDetailsModel?.title ?? ''),
                              ),
                              Align(
                                  alignment: Alignment.topRight,
                                  heightFactor: 2.9,
                                  child: reimbursmentStatusCard(
                                      status:
                                          reimbursmentDetailsModel?.status ??
                                              '')),
                            ],
                          ),
                          divider,
                          _detailsTail(
                              title: 'Description',
                              desciption:
                                  reimbursmentDetailsModel?.description ?? ''),
                          divider,
                          _detailsTail(title: 'Date', desciption: date),
                          divider,
                          _detailsTail(
                              title: 'Amount',
                              desciption:
                                  '${reimbursmentDetailsModel?.currency} ${reimbursmentDetailsModel?.amount ?? ''}'),
                          Text(
                            'Document',
                            style: tsS12w400c949494,
                          ),
                          SizedBox(height: h * 5),
                          SizedBox(
                            height: 50,
                            child: ListView.separated(
                                shrinkWrap: true,
                                padding: EdgeInsets.zero,
                                scrollDirection: Axis.horizontal,
                                itemBuilder: (context, index) {
                                  String? file = reimbursmentDetailsModel
                                          ?.reimbursementAttachements?[index]
                                          .attachment ??
                                      '';
                                  // return ClipRRect(
                                  //     borderRadius:
                                  //         BorderRadius.circular(8),
                                  //     child: InkWell(
                                  //       onTap: () {
                                  //         if (image != null) {
                                  //           PageNavigator.push(
                                  //             context: context,
                                  //             route: PhotoViewScreen(
                                  //               image: image,
                                  //               extension: 'jpeg',
                                  //             ),
                                  //           );
                                  //         }
                                  //       },
                                  //       child: Image.network(
                                  //         height: 120,
                                  //         width: 150,
                                  //         image ?? '',
                                  //         fit: BoxFit.cover,
                                  //       ),
                                  //     ));
                                  String extension =
                                      file.split('.').last.toUpperCase();
                                  String fileName = file.split('/').last;
                                  return InkWell(
                                    onTap: () {
                                      launchURL(file);
                                      // Navigator.push(
                                      //     context,
                                      //     MaterialPageRoute(
                                      //         builder: (context) =>
                                      //             DocViewScreen(url: file)));
                                    },
                                    child: Container(
                                      height: 50,
                                      width: 200,
                                      alignment: Alignment.centerLeft,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8),
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(4),
                                          color: Colors.grey[300]),
                                      child: Row(
                                        children: [
                                          formatContainer(extension),
                                          const SizedBox(width: 6),
                                          Expanded(
                                            child: Text(
                                              fileName,
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                                separatorBuilder: (context, index) =>
                                    const SizedBox(width: 5),
                                itemCount: reimbursmentDetailsModel
                                        ?.reimbursementAttachements?.length ??
                                    0),
                          ),
                          SizedBox(height: h * 8),
                          // divider,
                          // Text(
                          //   'Reporting Persons',
                          //   style: tsS12w400c949494,
                          // ),
                          // SizedBox(height: h * 3),
                          // _reportingPersonsWidget(reimbursmentDetailsModel.levelUsers),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    rejectStatusBuilder(
                        reimbursmentDetailsModel?.remarks ?? []),
                    hrApproveOrRejectCard(reimbursmentDetailsModel),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 30),
            if (showButton)
              Row(
                children: [
                  Expanded(
                    child: GeneralButton(
                      title: 'Reject',
                      height: h * 50,
                      width: w * 164,
                      textStyle: tsS18w500cFFFFFF,
                      isDisabledColor: true,
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) {
                            return RequestRejectionDialog(
                                id: reimbursmentDetailsModel?.id ?? 0);
                          },
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                      child: GeneralButton(
                    title: 'Approve',
                    height: h * 50,
                    width: w * 164,
                    textStyle: tsS18w500cFFFFFF,
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return RequestAcceptDialog(
                              id: reimbursmentDetailsModel?.id ?? 0);
                        },
                      );
                    },
                  ))
                ],
              ),
            SizedBox(height: Platform.isAndroid ? 10 : 40),
          ],
        ),
      ),

      // floatingWidget: showButton
      //     ? Padding(
      //         padding: EdgeInsets.only(left: 27.0 * h),
      //         child: Row(
      //           children: [
      //             Expanded(
      //               child: GeneralButton(
      //                 title: 'Reject',
      //                 height: h * 50,
      //                 width: w * 164,
      //                 textStyle: tsS18w500cFFFFFF,
      //                 isDisabledColor: true,
      //                 onPressed: () {
      //                   showDialog(
      //                     context: context,
      //                     builder: (context) {
      //                       return RequestRejectionDialog(
      //                           id: reimbursmentDetailsModel?.id ?? 0);
      //                     },
      //                   );
      //                 },
      //               ),
      //             ),
      //             const SizedBox(width: 10),
      //             Expanded(
      //                 child: GeneralButton(
      //               title: 'Approve',
      //               height: h * 50,
      //               width: w * 164,
      //               textStyle: tsS18w500cFFFFFF,
      //               onPressed: () {
      //                 showDialog(
      //                   context: context,
      //                   builder: (context) {
      //                     return RequestAcceptDialog(
      //                         id: reimbursmentDetailsModel?.id ?? 0);
      //                   },
      //                 );
      //               },
      //             ))
      //           ],
      //         ),
      //       )
      //     : null,
    );
  }

  Widget _detailsTail({required String title, required String desciption}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: tsS12w400c949494,
        ),
        SizedBox(height: h * 3),
        Text(
          desciption,
          style: tsS14w500c2C2D33,
        ),
        SizedBox(height: h * 8),
      ],
    );
  }

  final Divider divider = Divider(
    color: ThemeColors.colorD9D9D9,
    thickness: 1,
  );

  Widget _steperWidget(ReimbursmentDetailsModel? item) {
    return EasyStepper(
      alignment: Alignment.center,
      activeStepBorderType: BorderType.normal,
      unreachedStepBorderType: BorderType.normal,
      activeStepBorderColor: ThemeColors.colorECECEC,
      unreachedStepBorderColor: ThemeColors.colorECECEC,
      // stepBorderRadius: 1,
      activeStep: 0,
      lineStyle: LineStyle(
        activeLineColor: ThemeColors.colorECECEC,
        // progressColor: Colors.red,
        defaultLineColor: ThemeColors.colorECECEC,
        lineLength: 35 * w,
        lineType: LineType.normal,
        lineThickness: 2,
        lineSpace: 0,
        lineWidth: 0,
        unreachedLineType: LineType.normal,
      ),
      stepShape: StepShape.circle,
      // stepBorderRadius: ,
      borderThickness: 5,
      internalPadding: 2,
      padding: const EdgeInsetsDirectional.symmetric(
        horizontal: 30,
        vertical: 20,
      ),
      activeStepBackgroundColor: ThemeColors.primaryColor,
      unreachedStepBackgroundColor: Colors.white,
      stepRadius: 15 * h,

      showLoadingAnimation: false,
      steps: item?.levelUsers?.map((e) {
            int stepCount = (item.levelUsers?.indexOf(e) ?? 0) + 1;
            Color color = ThemeColors.primaryColor;
            switch (e.approvalStatus?.toLowerCase()) {
              case 'pending':
                color = ThemeColors.colorFFFFFF;
                break;
              case 'approved':
                color = ThemeColors.primaryColor;
                break;

              case 'rejected':
                color = Colors.red;
                break;
            }
            return EasyStep(
              enabled: false,
              customStep: Container(
                height: 30,
                width: 30,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: color,
                ),
                alignment: Alignment.center,
                child: Text(
                  '$stepCount',
                  style: TextStyle(color: ThemeColors.color000000),
                ),
              ),
              customTitle: Column(
                children: [
                  const SizedBox(height: 1),
                  Text(
                    e.approvalStatus?.capitalize() ?? '',
                    textAlign: TextAlign.center,
                    style: tsS10w400c979797,
                  ),
                  const SizedBox(height: 1),
                  Text(
                    e.addedBy ?? '',
                    textAlign: TextAlign.center,
                    style: tsS10w400c979797,
                  ),
                ],
              ),
            );
          }).toList() ??
          [],
      onStepReached: (index) {},
    );
  }
}
