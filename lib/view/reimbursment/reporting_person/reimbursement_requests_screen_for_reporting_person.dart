import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/view/reimbursment/reporting_person/reimbursement_request_details_screen_for_reporting_person.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../helper/search_textfield.dart';
import '../../../model/department_model.dart';
import '../../../model/reimbursment_model.dart';
import '../../../provider/reimbursment_provider.dart';
import '../../../util/colors.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../../widgets/hisense_scaffold.dart';
import '../../widgets/general_button.dart';
import '../../widgets/hisense_drop_down_tile.dart';
import '../user/reimbursment_details_screen.dart';

class ReimbursementRequestsScreenForReportingPerson extends StatelessWidget {
  ReimbursementRequestsScreenForReportingPerson({super.key});
  final TextEditingController searchController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    ReimbursementProvider provider = context.watch<ReimbursementProvider>();
    List<ReimbursmentModel> reimbursementReqeustListForReportingPerson =
        provider.reimbursementReqeustListForReportingPerson;
    return HisenseScaffold(
        screenTitle: 'Reimbursement Approval',
        onTap: () {},
        body: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16 * w),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              SizedBox(height: h * 10),
              Row(
                children: [
                  Expanded(
                    child: SearchTextFieldWidget(
                      onChanged: (value) {
                        provider.searchKey = value;
                        provider.fetchReportingPersonRequestList();
                      },
                      controller: searchController,
                      contentPadding: const EdgeInsets.symmetric(vertical: 15),
                      prefixIcon: IconButton(
                        onPressed: () {},
                        icon: const ImageIcon(
                          AssetImage(
                            'assets/icons/search.png',
                          ),
                        ),
                      ),
                      hintText: 'Search Employee...',
                    ),
                  ),

                  IconButton(
                      onPressed: () {
                        showModalBottomSheet(
                          context: context,
                          isDismissible: true,
                          backgroundColor: ThemeColors.colorFFFFFF,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                          builder: (context) {
                            return _showModalBottomSheet(context: context);
                          },
                        );
                      },
                      icon: const Icon(
                        Icons.filter_list,
                        size: 30,
                      ))
                  // Container(
                  //   padding: const EdgeInsets.fromLTRB(10, 5, 5, 5),
                  //   width: 90 * w,
                  //   height: 30 * h,
                  //   decoration: BoxDecoration(
                  //     color: Colors.white,
                  //     borderRadius: BorderRadius.circular(6),
                  //   ),
                  //   child: DropdownButton(
                  //     isExpanded: true,
                  //     underline: const SizedBox(),
                  //     value: provider.selectedFilter,
                  //     items: provider.statusList
                  //         .map((e) => DropdownMenuItem(
                  //             value: e, child: FittedBox(child: Text(e))))
                  //         .toList(),
                  //     onChanged: (String? value) {
                  //       if (value != null) {
                  //         provider.selectedFilter = value;
                  //         provider.fetchReimbursmentList();
                  //       }
                  //     },
                  //   ),
                  // )
                ],
              ),
              const SizedBox(height: 15),
              Text(
                'Reimbursement Requests',
                style: tsS18w500c161616,
              ),
              const SizedBox(height: 5),
              Expanded(
                child: reimbursementReqeustListForReportingPerson.isEmpty
                    ? const Center(child: Text('No data found'))
                    : ListView.separated(
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        separatorBuilder: (context, index) =>
                            const SizedBox(height: 4),
                        itemBuilder: (context, index) {
                          ReimbursmentModel? data =
                              reimbursementReqeustListForReportingPerson[index];
                          return InkWell(
                            onTap: () async {
                              await provider.fetchReimbursmentDetails(
                                  id: data.id);
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          ReimbursementRequestDetailsScreenForReportingPerson()));
                            },
                            child: Card(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              elevation: 0,
                              child: Padding(
                                padding:
                                    const EdgeInsets.fromLTRB(10, 8, 10, 8),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        const SizedBox(width: 10),
                                        ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(5.0),
                                          child: ClipOval(
                                            child: CachedNetworkImage(
                                              imageUrl: data.profilePic ?? '',
                                              width: 50,
                                              height: 50,
                                              fit: BoxFit.cover,
                                              errorWidget:
                                                  (context, url, error) {
                                                return Container(
                                                  height: 50,
                                                  width: 50,
                                                  decoration: BoxDecoration(
                                                    color: Theme.of(context)
                                                        .primaryColor,
                                                    shape: BoxShape.circle,
                                                  ),
                                                  alignment: Alignment.center,
                                                  child: Text(
                                                    data.userName?[0]
                                                            .toUpperCase() ??
                                                        '',
                                                    style: GoogleFonts.rubik(
                                                      fontSize: 22,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                        const SizedBox(
                                          width: 10,
                                        ),
                                        Expanded(
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              SizedBox(
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width -
                                                    240,
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      data.userName ?? '',
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      style: tsS14w500,
                                                    ),
                                                    Align(
                                                      alignment:
                                                          Alignment.topLeft,
                                                      child: Text(
                                                        data.title ?? '',
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: tsS12grey,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              reimbursmentStatusCard(
                                                  status: data.status ?? '')
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                        itemCount:
                            reimbursementReqeustListForReportingPerson.length),
              )
            ])));
  }
}

Widget _showModalBottomSheet({required BuildContext context}) {
  ReimbursementProvider provider = context.watch<ReimbursementProvider>();
  List<DepartmentModel> departmentList = provider.departmentList;
  List<DepartmentModel> designationList = provider.designationList;
  return Container(
    height: h * 410,
    decoration: BoxDecoration(
      // color: ThemeColors.colorFFFFFF,
      borderRadius: BorderRadius.circular(12),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: h * 14),
        Align(
          alignment: Alignment.center,
          child: Container(
            height: h * 5,
            width: w * 134,
            decoration: BoxDecoration(
              color: ThemeColors.colorEAEBED,
              borderRadius: BorderRadius.circular(100),
            ),
          ),
        ),
        SizedBox(height: h * 5),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: w * 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Filter', style: tsS20w500c161616),
                  TextButton(
                      onPressed: () {
                        provider.selectedFilter = 'All';
                        provider.selectedDepartment = null;
                        provider.selectedDesignation = null;
                        provider.searchKey = '';
                        provider.fetchReportingPersonRequestList();
                        Navigator.pop(context);
                      },
                      child: Text(
                        'Clear Filter',
                        style: tsS14NormalBlack,
                      ))
                ],
              ),
              // SizedBox(height: h * 12),
              HisenseDropdownTile(
                title: 'Select Status',
                titleStyle: GoogleFonts.poppins(
                  color: const Color(0xFF30292F),
                  fontSize: 12 * f,
                  fontWeight: FontWeight.w400,
                ),
                hintText: 'Select Status',
                hintStyle: GoogleFonts.poppins(
                  fontSize: 12 * f,
                  fontWeight: FontWeight.w400,
                ),
                style: tsS14w400454444,
                errorStyle: tsS11w400cerrorColor,
                value: provider.selectedFilter,
                contentPadding:
                    EdgeInsets.symmetric(vertical: h * 12, horizontal: w * 11),
                items: provider.statusList
                    .map(
                      (e) => DropdownMenuItem(
                        value: e,
                        child: Text(
                          e,
                          style: tsS14w400454444,
                        ),
                      ),
                    )
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    provider.selectedFilter = value;
                  }
                },
              ),
              SizedBox(height: h * 7),
              HisenseDropdownTile(
                  title: 'Select Department',
                  titleStyle: GoogleFonts.poppins(
                    color: const Color(0xFF30292F),
                    fontSize: 12 * f,
                    fontWeight: FontWeight.w400,
                  ),
                  hintText: 'Select Department',
                  hintStyle: GoogleFonts.poppins(
                    fontSize: 12 * f,
                    fontWeight: FontWeight.w400,
                  ),
                  style: tsS14w400454444,
                  errorStyle: tsS11w400cerrorColor,
                  value: provider.selectedDepartment,
                  contentPadding: EdgeInsets.symmetric(
                      vertical: h * 12, horizontal: w * 11),
                  onChanged: (value) {
                    if (value != null) {
                      provider.selectedDepartment = value;
                    }
                  },
                  items: departmentList
                      .map(
                        (e) => DropdownMenuItem(
                          value: e,
                          child: Text(
                            e.name ?? '',
                            style: tsS14w400454444,
                          ),
                        ),
                      )
                      .toList()),
              SizedBox(height: h * 7),
              HisenseDropdownTile(
                  title: 'Select Designation',
                  titleStyle: GoogleFonts.poppins(
                    color: const Color(0xFF30292F),
                    fontSize: 12 * f,
                    fontWeight: FontWeight.w400,
                  ),
                  hintText: 'Select Designation',
                  hintStyle: GoogleFonts.poppins(
                    fontSize: 12 * f,
                    fontWeight: FontWeight.w400,
                  ),
                  style: tsS14w400454444,
                  errorStyle: tsS11w400cerrorColor,
                  value: provider.selectedDesignation,
                  contentPadding: EdgeInsets.symmetric(
                      vertical: h * 12, horizontal: w * 11),
                  onChanged: (value) {
                    if (value != null) {
                      provider.selectedDesignation = value;
                    }
                  },
                  items: designationList
                      .map(
                        (e) => DropdownMenuItem(
                          value: e,
                          child: Text(
                            e.name ?? '',
                            style: tsS14w400454444,
                          ),
                        ),
                      )
                      .toList()),
              SizedBox(height: h * 33),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  GeneralButton(
                    height: h * 50,
                    width: w * 164,
                    isDisabledColor: true,
                    title: 'Cancel',
                    textStyle: tsS18w600cFFFFFF,
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                  GeneralButton(
                    height: h * 50,
                    width: w * 164,
                    title: 'Apply',
                    textStyle: tsS18w600cFFFFFF,
                    // color: ThemeColors.secondaryColor,
                    onPressed: () async {
                      Navigator.pop(context);
                      provider.fetchReportingPersonRequestList();
                    },
                  ),
                ],
              ),
            ],
          ),
        )
      ],
    ),
  );
}
