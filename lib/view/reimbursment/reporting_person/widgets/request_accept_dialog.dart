import 'package:e8_hr_portal/provider/reimbursment_provider.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:e8_hr_portal/view/widgets/hisense_text_form_field.dart';
import 'package:provider/provider.dart';

class RequestAcceptDialog extends StatelessWidget {
  final int id;
  RequestAcceptDialog({required this.id, super.key});
  final TextEditingController _reasonController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: w * 16),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(26),
      ),
      elevation: 0.0,
      backgroundColor: ThemeColors.colorFFFFFF,
      child: Padding(
        padding: EdgeInsets.fromLTRB(w * 20, h * 5, w * 5, h * 25),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Align(
              alignment: Alignment.centerRight,
              child: IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                icon: Icon(
                  Icons.close,
                  color: ThemeColors.colorD6D6D6,
                  size: h * 20,
                ),
              ),
            ),
            SizedBox(height: h * 10),
            Text(
              'Approval reason',
              style: tsS14w500c6E7079,
            ),
            SizedBox(height: h * 6),
            Padding(
              padding: EdgeInsets.only(right: w * 15),
              child: HisenseTextFormField(
                controller: _reasonController,
                hintText: 'Enter the reason',
                hintStyle: tsS12w400c475366,
                maxLines: 3,
              ),
            ),
            SizedBox(height: h * 25),
            Padding(
              padding: EdgeInsets.only(left: w * 7, right: w * 21),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _cancelButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      width: w * 140,
                      height: h * 40,
                      title: 'Cancel',
                      textStyle: tsS14w500c475366),
                  GeneralButton(
                    title: 'Submit',
                    height: h * 40,
                    textStyle: tsS14w500cFFFFFF,
                    width: w * 140,
                    onPressed: () {
                      Navigator.pop(context);
                      ReimbursementProvider provider =
                          context.read<ReimbursementProvider>();
                      provider.requestApproveOrReject(
                          id: id,
                          action: 'approved',
                          comment: _reasonController.text);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _cancelButton(
      {required VoidCallback onPressed,
      required double width,
      required double height,
      required String title,
      required TextStyle textStyle}) {
    return Container(
      decoration: BoxDecoration(
        color: ThemeColors.colorF3F3F9,
        borderRadius: BorderRadius.circular(50),
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50),
          ),
          minimumSize: Size(width, height),
        ),
        child: Text(
          title,
          style: textStyle,
        ),
      ),
    );
  }
}
