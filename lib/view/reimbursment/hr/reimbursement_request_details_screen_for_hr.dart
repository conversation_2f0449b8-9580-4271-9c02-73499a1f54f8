import 'dart:io';

import 'package:dotted_border/dotted_border.dart' as dot;
import 'package:e8_hr_portal/provider/reimbursment_provider.dart';
import 'package:e8_hr_portal/util/capitalize.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:easy_stepper/easy_stepper.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:open_file/open_file.dart';
import 'package:provider/provider.dart';
import '../../../model/reimbursment_details_model.dart';
import '../../../util/colors.dart';
import '../../../util/general_functions.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../../util/validator.dart';
import '../../../widgets/hisense_scaffold.dart';
import '../../widgets/general_button.dart';
import '../../widgets/hisense_text_form_field.dart';
import '../user/reimbursment_details_screen.dart';
import '../user/reimbursment_requesting_screen.dart';

class ReimbursementRequestDetailsScreenForHR extends StatelessWidget {
  ReimbursementRequestDetailsScreenForHR({super.key});
  @override
  Widget build(BuildContext context) {
    ReimbursementProvider provider = context.watch<ReimbursementProvider>();

    ReimbursmentDetailsModel? reimbursmentDetailsModel =
        provider.reimbursmentDetailsModel;
    String date = '';
    String? startDate = reimbursmentDetailsModel?.startDate;
    String? endDate = reimbursmentDetailsModel?.endDate;
    if ((startDate != null && endDate != null)) {
      if (startDate == endDate) {
        date = formatDateFromString(
            reimbursmentDetailsModel!.startDate!, 'yyyy-MM-dd', 'dd MMM yy');
      } else {
        date =
            '${formatDateFromString(startDate, 'yyyy-MM-dd', 'dd MMM yy')} - ${formatDateFromString(endDate, 'yyyy-MM-dd', 'dd MMM yy')}';
      }
    }

    bool showButton =
        reimbursmentDetailsModel?.hrStatus?.toLowerCase() == 'pending';

    return HisenseScaffold(
      screenTitle: 'Reimbursement HR Details',
      onTap: () {},
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16 * w),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Request Details', style: tsS18w500c181818),
                    SizedBox(height: h * 10),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 10 * h),
                          _steperWidget(reimbursmentDetailsModel),
                          divider,
                          SizedBox(height: 10 * h),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: _detailsTail(
                                    title: 'Title',
                                    desciption:
                                        reimbursmentDetailsModel?.title ?? ''),
                              ),
                              Align(
                                  alignment: Alignment.topRight,
                                  heightFactor: 2.9,
                                  child: reimbursmentStatusCard(
                                      status:
                                          reimbursmentDetailsModel?.hrStatus ??
                                              '')),
                            ],
                          ),
                          divider,
                          _detailsTail(
                              title: 'Description',
                              desciption:
                                  reimbursmentDetailsModel?.description ?? ''),
                          divider,
                          _detailsTail(title: 'Date', desciption: date),
                          divider,
                          _detailsTail(
                              title: 'Amount',
                              desciption:
                                  '${reimbursmentDetailsModel?.currency} ${reimbursmentDetailsModel?.amount ?? ''}'),
                          Text('Document', style: tsS12w400c949494),
                          SizedBox(height: h * 5),
                          SizedBox(
                            height: 50,
                            child: ListView.separated(
                                shrinkWrap: true,
                                padding: EdgeInsets.zero,
                                scrollDirection: Axis.horizontal,
                                itemBuilder: (context, index) {
                                  String? file = reimbursmentDetailsModel ==
                                          null
                                      ? ''
                                      : reimbursmentDetailsModel
                                              .reimbursementAttachements?[index]
                                              .attachment ??
                                          '';

                                  String extension =
                                      file.split('.').last.toUpperCase();
                                  String fileName = file.split('/').last;
                                  return InkWell(
                                    onTap: () {
                                      launchURL(file);
                                      // Navigator.push(
                                      //     context,
                                      //     MaterialPageRoute(
                                      //         builder: (context) =>
                                      //             DocViewScreen(url: file)));
                                    },
                                    child: Container(
                                      height: 50,
                                      width: 200,
                                      alignment: Alignment.centerLeft,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8),
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(4),
                                          color: Colors.grey[300]),
                                      child: Row(
                                        children: [
                                          formatContainer(extension),
                                          const SizedBox(width: 6),
                                          Expanded(
                                            child: Text(
                                              fileName,
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                                separatorBuilder: (context, index) =>
                                    const SizedBox(width: 5),
                                itemCount: reimbursmentDetailsModel
                                        ?.reimbursementAttachements?.length ??
                                    0),
                          ),
                          SizedBox(height: h * 8),
                          // divider,
                          // Text(
                          //   'Reporting Persons',
                          //   style: tsS12w400c949494,
                          // ),
                          // SizedBox(height: h * 3),
                          // _reportingPersonsWidget(reimbursmentDetailsModel.levelUsers),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    rejectStatusBuilder(
                        reimbursmentDetailsModel?.remarks ?? []),
                    hrApproveOrRejectCard(reimbursmentDetailsModel)
                  ],
                ),
              ),
            ),
            const SizedBox(height: 30),
            if (showButton)
              Row(
                children: [
                  Expanded(
                    child: GeneralButton(
                      title: 'Reject',
                      height: h * 50,
                      width: w * 164,
                      textStyle: tsS18w500cFFFFFF,
                      isDisabledColor: true,
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) {
                            return RequestRejectionDialogForHR(
                                id: reimbursmentDetailsModel?.id ?? 0);
                          },
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                      child: GeneralButton(
                    title: 'Approve',
                    height: h * 50,
                    width: w * 164,
                    textStyle: tsS18w500cFFFFFF,
                    onPressed: () {
                      provider.filesList.clear();
                      showDialog(
                        context: context,
                        builder: (context) {
                          return RequestAcceptDialogForHR(
                              id: reimbursmentDetailsModel?.id ?? 0);
                        },
                      );
                    },
                  ))
                ],
              ),
            SizedBox(height: Platform.isAndroid ? 10 : 40),
          ],
        ),
      ),
    );
  }

  Widget _detailsTail({required String title, required String desciption}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: tsS12w400c949494,
        ),
        SizedBox(height: h * 3),
        Text(
          desciption,
          style: tsS14w500c2C2D33,
        ),
        SizedBox(height: h * 8),
      ],
    );
  }

  final Divider divider = Divider(
    color: ThemeColors.colorD9D9D9,
    thickness: 1,
  );

  Widget _steperWidget(ReimbursmentDetailsModel? item) {
    return EasyStepper(
      alignment: Alignment.center,
      activeStepBorderType: BorderType.normal,
      unreachedStepBorderType: BorderType.normal,
      activeStepBorderColor: ThemeColors.colorECECEC,
      unreachedStepBorderColor: ThemeColors.colorECECEC,
      // stepBorderRadius: 1,
      activeStep: 0,
      lineStyle: LineStyle(
        activeLineColor: ThemeColors.colorECECEC,
        // progressColor: Colors.red,
        defaultLineColor: ThemeColors.colorECECEC,
        lineLength: 35 * w,
        lineType: LineType.normal,
        lineThickness: 2,
        lineSpace: 0,
        lineWidth: 0,
        unreachedLineType: LineType.normal,
      ),
      stepShape: StepShape.circle,
      // stepBorderRadius: ,
      borderThickness: 5,
      internalPadding: 2,
      padding: const EdgeInsetsDirectional.symmetric(
        horizontal: 30,
        vertical: 20,
      ),
      activeStepBackgroundColor: ThemeColors.primaryColor,
      unreachedStepBackgroundColor: Colors.white,
      stepRadius: 15 * h,

      showLoadingAnimation: false,
      steps: item?.levelUsers?.map((e) {
            int stepCount = (item.levelUsers?.indexOf(e) ?? 0) + 1;
            Color color = ThemeColors.primaryColor;
            switch (e.approvalStatus?.toLowerCase()) {
              case 'pending':
                color = ThemeColors.colorFFFFFF;
                break;
              case 'approved':
                color = ThemeColors.primaryColor;
                break;

              case 'rejected':
                color = Colors.red;
                break;
            }
            return EasyStep(
              enabled: false,
              customStep: Container(
                height: 30,
                width: 30,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: color,
                ),
                alignment: Alignment.center,
                child: Text(
                  '$stepCount',
                  style: TextStyle(color: ThemeColors.color000000),
                ),
              ),
              customTitle: Column(
                children: [
                  const SizedBox(height: 1),
                  Text(
                    e.approvalStatus?.capitalize() ?? '',
                    textAlign: TextAlign.center,
                    style: tsS10w400c979797,
                  ),
                  const SizedBox(height: 1),
                  Text(
                    e.addedBy ?? '',
                    textAlign: TextAlign.center,
                    style: tsS10w400c979797,
                  ),
                ],
              ),
            );
          }).toList() ??
          [],
      onStepReached: (index) {},
    );
  }
}

class RequestRejectionDialogForHR extends StatelessWidget {
  final int id;
  RequestRejectionDialogForHR({required this.id, super.key});
  final TextEditingController _reasonController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: w * 16),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(26),
      ),
      elevation: 0.0,
      backgroundColor: ThemeColors.colorFFFFFF,
      child: Padding(
        padding: EdgeInsets.fromLTRB(w * 20, h * 5, w * 5, h * 25),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.centerRight,
                child: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Icon(
                    Icons.close,
                    color: ThemeColors.colorD6D6D6,
                    size: h * 20,
                  ),
                ),
              ),
              SizedBox(height: h * 10),
              Row(
                children: [
                  Text(
                    'Reason For Rejection',
                    style: tsS14w500c6E7079,
                  ),
                  Text(
                    '*',
                    style: tsS14w400cFA0000,
                  ),
                ],
              ),
              SizedBox(height: h * 6),
              Padding(
                padding: EdgeInsets.only(right: w * 15),
                child: HisenseTextFormField(
                  controller: _reasonController,
                  hintText: 'Enter the reason',
                  hintStyle: tsS12w400c475366,
                  maxLines: 3,
                  validator: Validator.reason,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp('[a-zA-Z]'))
                  ],
                ),
              ),
              SizedBox(height: h * 25),
              Padding(
                padding: EdgeInsets.only(left: w * 7, right: w * 21),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _cancelButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        width: w * 140,
                        height: h * 40,
                        title: 'Cancel',
                        textStyle: tsS14w500c475366),
                    GeneralButton(
                      title: 'Submit',
                      height: h * 40,
                      textStyle: tsS14w500cFFFFFF,
                      width: w * 140,
                      onPressed: () {
                        if (_formKey.currentState?.validate() ?? false) {
                          Navigator.pop(context);
                          ReimbursementProvider provider =
                              context.read<ReimbursementProvider>();
                          provider.requestApproveOrRejectForHR(
                              id: id,
                              action: 'rejected',
                              comment: _reasonController.text);
                        }
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class RequestAcceptDialogForHR extends StatelessWidget {
  final int id;
  RequestAcceptDialogForHR({required this.id, super.key});
  final TextEditingController _reasonController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    ReimbursementProvider provider = context.watch<ReimbursementProvider>();
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: w * 16),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(26),
      ),
      elevation: 0.0,
      backgroundColor: ThemeColors.colorFFFFFF,
      child: Padding(
        padding: EdgeInsets.fromLTRB(20, 0, 20, 30),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 10),
            Align(
              alignment: Alignment.centerRight,
              child: IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                icon: Icon(
                  Icons.close,
                  color: ThemeColors.colorD6D6D6,
                  size: h * 20,
                ),
              ),
            ),
            Text(
              'Approval reason',
              style: tsS14w500c6E7079,
            ),
            SizedBox(height: h * 6),
            HisenseTextFormField(
              controller: _reasonController,
              hintText: 'Enter the reason',
              hintStyle: tsS12w400c475366,
              maxLines: 3,
            ),
            SizedBox(height: h * 10),
            dot.DottedBorder(
              dashPattern: [w * 6, w * 7],
              color: ThemeColors.colorD9D9D9,
              borderType: dot.BorderType.RRect,
              radius: const Radius.circular(3),
              child: GestureDetector(
                  onTap: () async {
                    showDialog(
                      context: context,
                      builder: (ctx) => ReimbursementDocUploadDialog(ctx: ctx),
                    );
                  },
                  child: ClipRRect(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(3),
                    ),
                    child: Container(
                        width: double.infinity,
                        color: ThemeColors.colorFFFFFF,
                        alignment: Alignment.center,
                        padding: EdgeInsets.all(10),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Image.asset('assets/icons/upload.png',
                                height: h * 16, width: w * 22),
                            SizedBox(height: h * 8),
                            Text(
                              'Click to upload your document',
                              style: tsS12w400c30292F,
                            ),
                            SizedBox(height: h * 3),
                            Text(
                              'Supports :JPG, JPEG, PNG, PDF, DOC, DOCX with max size of 10 MB',
                              style: tsS10w400979797,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        )),
                  )),
            ),
            SizedBox(height: 10),
            if (provider.filesList.isNotEmpty)
              SizedBox(
                height: 80,
                child: ListView.separated(
                    shrinkWrap: true,
                    physics: const ClampingScrollPhysics(),
                    padding: const EdgeInsets.only(bottom: 30),
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (context, index) {
                      PlatformFile file = provider.filesList[index];
                      String extension = file.name.split('.')[1].toUpperCase();
                      return InkWell(
                        onTap: () {
                          OpenFile.open(file.path);
                        },
                        child: Container(
                          height: 50,
                          width: 200,
                          alignment: Alignment.centerLeft,
                          padding: const EdgeInsets.only(left: 8),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: Colors.grey[300]),
                          child: Row(
                            children: [
                              formatContainer(extension),
                              const SizedBox(width: 6),
                              Expanded(
                                child: Text(
                                  file.name,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                              IconButton(
                                  onPressed: () {
                                    provider.removeFile(index, true);
                                  },
                                  icon: const Icon(Icons.close))
                            ],
                          ),
                        ),
                      );
                    },
                    separatorBuilder: (context, index) =>
                        const SizedBox(width: 5),
                    itemCount: provider.filesList.length),
              ),
            SizedBox(height: h * 20),
            Row(
              children: [
                Expanded(
                  child: _cancelButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      width: w * 140,
                      height: h * 40,
                      title: 'Cancel',
                      textStyle: tsS14w500c475366),
                ),
                SizedBox(width: 10),
                Expanded(
                  child: GeneralButton(
                    title: 'Submit',
                    height: h * 40,
                    textStyle: tsS14w500cFFFFFF,
                    width: w * 140,
                    onPressed: () {
                      ReimbursementProvider provider =
                          context.read<ReimbursementProvider>();
                      Navigator.pop(context);
                      provider.requestApproveOrRejectForHR(
                          id: id,
                          action: 'approved',
                          comment: _reasonController.text);
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

Widget _cancelButton(
    {required VoidCallback onPressed,
    required double width,
    required double height,
    required String title,
    required TextStyle textStyle}) {
  return Container(
    decoration: BoxDecoration(
      color: ThemeColors.colorF3F3F9,
      borderRadius: BorderRadius.circular(50),
    ),
    child: ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.transparent,
        shadowColor: Colors.transparent,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(50),
        ),
        minimumSize: Size(width, height),
        // fixedSize: Size(size.width * 0.872, size.height * 0.0689),
      ),
      child: Text(
        title,
        style: textStyle,
      ),
    ),
  );
}
