import 'package:e8_hr_portal/model/user_current_tasks.dart';
import 'package:e8_hr_portal/util/capitalize.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../provider/log_time_provider.dart';
import '../../../util/size_config.dart';

class CompletedTaskCard extends StatelessWidget {
  final UserCurrentTasks data;
  final LogTimeProvider provider;
  const CompletedTaskCard({
    super.key,
    required this.data,
    required this.provider,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.fromLTRB(w * 15, h * 15, w * 15, h * 12),
          decoration: ShapeDecoration(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 4.0),
                        child: Text(
                          formatDateFromString(data.createdAt ?? '',
                              'yyyy-MM-dd hh:mm:ss', 'dd MMMM yyyy'),
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF666666),
                            fontSize: 11 * f,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: provider.formattedTime(
                                  timeInSecond: data.timing ?? 0),
                              style: GoogleFonts.poppins(
                                color: const Color(0xFF343434),
                                fontSize: 30 * f,
                                fontWeight: FontWeight.w600,
                                height: 1.3,
                              ),
                            ),
                            TextSpan(
                              text: 'HOURS',
                              style: GoogleFonts.poppins(
                                color: const Color(0xFF343434),
                                fontSize: 15 * f,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Material(
                    child: InkWell(
                      onTap: () {
                        launchUrl(Uri.parse(
                            'https://workflow.element8.ae/timesheet'));
                      },
                      child: Text(
                        'Send Request',
                        style: GoogleFonts.poppins(
                            color: const Color(0xFFF9637D),
                            fontSize: f * 12,
                            fontWeight: FontWeight.w500,
                            decoration: TextDecoration.underline),
                      ),
                    ),
                  ),
                ],
              ),
              Align(
                alignment: Alignment.center,
                child: Container(
                  margin: EdgeInsets.only(top: h * 5),
                  decoration: const ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        width: 0.50,
                        strokeAlign: BorderSide.strokeAlignCenter,
                        color: Color(0x99D9D9D9),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(height: h * 9),
              Text(
                data.project.toString().capitalize(),
                style: GoogleFonts.poppins(
                  color: const Color(0xFF161616),
                  fontSize: 12 * f,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: h * 1),
              Text(
                data.title ?? '',
                style: GoogleFonts.poppins(
                  color: const Color(0xFF666666),
                  fontSize: 12 * f,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
