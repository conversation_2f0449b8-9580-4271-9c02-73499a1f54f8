// ignore_for_file: use_build_context_synchronously

import 'package:e8_hr_portal/model/user_current_tasks.dart';
import 'package:e8_hr_portal/provider/log_time_provider.dart';
import 'package:e8_hr_portal/util/capitalize.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/task_management/sections/completed_task_section.dart';
import 'package:e8_hr_portal/view/widgets/timer_dialoge_stop.dart';
import 'package:e8_hr_portal/view/widgets/timer_stope_dialoge.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../add_new_task/add_new_task.dart';
import 'package:html/parser.dart' as html;

import '../dashboard/widgets/title_text_widget.dart';

class TaskManagementScreen extends StatelessWidget {
  static const route = 'task_management_screen/';
  const TaskManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Task Management',
      body: ScrollConfiguration(
        behavior: const ScrollBehavior().copyWith(overscroll: false),
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 16 * w),
          children: [
            _titleWidget(context: context, title: 'Total Hours'),
            SizedBox(height: 10 * h),
            _totalWorkingHoursWidget(context: context),
            _titleWidget(context: context, title: 'Current Tasks'),
            _currentTaskListViewWidgets(),
            const CompletedTaskSection()
          ],
        ),
      ),
    );
  }

  Widget _totalWorkingHoursWidget({required BuildContext context}) {
    return Consumer<LogTimeProvider>(
      builder: (context, provider, _) {
        String? todayHours = provider.totalWorkingHoursModel?.todayHours;
        String? hours = provider.hourType;
        if (todayHours == '-') {
          todayHours = '0';
        }
        int firstPartOfTodayHours = 0;
        if (todayHours != null && todayHours != '0') {
          List<String> todayHoursList = todayHours.split(':');
          if (todayHoursList.isNotEmpty) {
            firstPartOfTodayHours = int.parse(todayHoursList.first);
          }
        }
        bool isGreen = firstPartOfTodayHours >= 8;
        String dateOfTheDay = formatDateFromDate(
            dateTime: DateTime.now(), format: 'dd MMMM yyyy');
        String dropDownTitle = provider.selectedTypeOfHour.split('’').first;
        return InkWell(
          borderRadius: BorderRadius.circular(65),
          customBorder: const StadiumBorder(),
          onTap: () => _callTimeBottomSheet(context: context),
          child: Container(
            width: double.infinity,
            // height: 82,
            padding: EdgeInsets.fromLTRB(15 * w, 15 * h, 15 * w, 18 * h),
            decoration: ShapeDecoration(
              color:
                  isGreen ? const Color(0xFFD7FFDD) : const Color(0xffffeaee),
              shape: RoundedRectangleBorder(
                side: BorderSide(
                    width: 0.5,
                    color: isGreen
                        ? const Color(0xFF0FAA31)
                        : const Color(0xFFF9637D)),
                borderRadius: BorderRadius.circular(12),
              ),
              shadows: const [
                BoxShadow(
                  color: Color(0x4CC8FFD1),
                  blurRadius: 10,
                  offset: Offset(0, 4),
                  spreadRadius: 4,
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 4.0),
                      child: Text(
                        dateOfTheDay,
                        style: GoogleFonts.poppins(
                          color: const Color(0xFF666666),
                          fontSize: 12 * f,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: hours == '-' || hours == '0'
                                ? '00:00'
                                : hours ?? '00:00:00',
                            style: GoogleFonts.poppins(
                              color: const Color(0xFF343434),
                              fontSize: 31 * f,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          TextSpan(
                            text: 'HOURS',
                            style: GoogleFonts.poppins(
                              color: const Color(0xFF343434),
                              fontSize: 15 * f,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                Container(
                  alignment: Alignment.center,
                  // padding: EdgeInsets.symmetric(horizontal: w * 10, vertical: h * 4),
                  decoration: ShapeDecoration(
                    color: const Color(0xFFF9637D),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(65),
                    ),
                  ),
                  child: Material(
                    color: const Color(0xFFF9637D),
                    borderRadius: BorderRadius.circular(65),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: w * 10, vertical: h * 4),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            dropDownTitle,
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontSize: 12 * f,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Icon(
                            Icons.keyboard_arrow_down,
                            color: Colors.white,
                            size: 20,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _titleWidget({required BuildContext context, required String title}) {
    return Column(
      children: [
        SizedBox(height: h * 15),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            TitleTextWidget(title),
            if (title == 'Current Tasks')
              Material(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                child: InkWell(
                  borderRadius: BorderRadius.circular(8),
                  onTap: () async {
                    LogTimeProvider provider =
                        Provider.of<LogTimeProvider>(context, listen: false);
                    provider.selectedTaskType = null;
                    provider.selectedProject = null;
                    provider.selectedTask = null;
                    provider.selectedStage = null;
                    provider.projectList.clear();
                    provider.taskList.clear();
                    provider.stageList.clear();
                    await provider.getTaskType();
                    provider.selectedTaskType = '1';
                    provider.getProjectTypes();
                    // provider.getProjectTypes();

                    Navigator.push(
                        context,
                        CupertinoPageRoute(
                            builder: (context) => AddNewTaskScreen()));
                  },
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(w * 2, h * 3, w * 0, h * 2),
                    child: Text(
                      '+Add New Task',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFFF9637D),
                        fontSize: f * 15,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _currentTaskListViewWidgets() {
    return Consumer<LogTimeProvider>(builder: (context, provider, _) {
      if (provider.userCurrentTaskList.isEmpty) {
        return Padding(
          padding: EdgeInsets.symmetric(vertical: 50 * h),
          child: const Center(child: Text("No active task found!")),
        );
      }
      return ListView.separated(
        key: UniqueKey(),
        shrinkWrap: true,
        itemCount: provider.userCurrentTaskList.length,
        physics: const ClampingScrollPhysics(),
        padding: EdgeInsets.only(top: h * 15),
        itemBuilder: (context, index) {
          UserCurrentTasks currentTask = provider.userCurrentTaskList[index];
          bool statusTimer = currentTask.status == 1;
          return _currentTaskCardWidget(
            context: context,
            date: currentTask.createdAt,
            pro: provider,
            timeInSeconds: currentTask.timing,
            timerStatus: statusTimer,
            project: currentTask.project,
            title: currentTask.title,
            notes: currentTask.note,
            id: currentTask.id,
            seconds: currentTask.timing ?? 0,
          );
        },
        separatorBuilder: (context, index) => SizedBox(height: h * 10),
      );
    });
  }

  Widget _currentTaskCardWidget({
    required BuildContext context,
    required String? date,
    required LogTimeProvider pro,
    required int? timeInSeconds,
    required bool? timerStatus,
    required String? project,
    required String? title,
    required String? notes,
    required int? id,
    required int seconds,
  }) {
    final note = html.parse(notes.toString());
    final String parsedString =
        html.parse(note.body!.text).documentElement!.text;
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.fromLTRB(w * 15, h * 15, w * 15, h * 12),
          decoration: ShapeDecoration(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 4.0),
                        child: Text(
                          formatDateFromString(date.toString(),
                              "yyyy-MM-dd hh:mm:ss", "dd MMMM yyyy"),
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF666666),
                            fontSize: 11 * f,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: timerStatus!
                                  ? "${pro.hours.toString().padLeft(2, '0')}:${pro.minutes.toString().padLeft(2, '0')}:${pro.seconds.toString().padLeft(2, '0')}"
                                  : pro.formattedTime(
                                      timeInSecond: timeInSeconds!),
                              style: GoogleFonts.poppins(
                                color: const Color(0xFF343434),
                                fontSize: 30 * f,
                                fontWeight: FontWeight.w600,
                                height: 1.3,
                              ),
                            ),
                            TextSpan(
                              text: 'HOURS',
                              style: GoogleFonts.poppins(
                                color: const Color(0xFF343434),
                                fontSize: 15 * f,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      _timerStartStopPauseButton(
                        color: const Color(0xFFC51414),
                        icon: Icons.stop_rounded,
                        onTap: () {
                          showModalBottomSheet(
                            context: context,
                            isDismissible: true,
                            backgroundColor: ThemeColors.colorFFFFFF,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10.0),
                            ),
                            builder: (context) {
                              return TimerDialogStop(
                                taskId: id!,
                                isFrom: true,
                              );
                            },
                          );

                          // pro.timerPlayPauseStope(
                          //     taskId: id!, statusId: 3, context: context);
                        },
                      ),
                      SizedBox(width: w * 8),
                      _timerStartStopPauseButton(
                        color: !timerStatus
                            ? const Color(0xFF387FE9)
                            : const Color(0xFF0CA726),
                        icon: !timerStatus
                            ? Icons.play_arrow_rounded
                            : Icons.pause_rounded,
                        onTap: () async {
                          if (pro.isTimerPaused == false && timerStatus) {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return TimerDialog(taskId: id!);
                              },
                            );
                          } else {
                            // if (pro.timer?.isActive == false) {
                            //   pro.startTimer();
                            // }
                            pro.timerPlayPauseStope(
                                taskId: id!, statusId: 1, context: context);
                          }
                        },
                      )
                    ],
                  )
                ],
              ),
              Align(
                alignment: Alignment.center,
                child: Container(
                  margin: EdgeInsets.only(top: h * 5),
                  decoration: const ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        width: 0.50,
                        strokeAlign: BorderSide.strokeAlignCenter,
                        color: Color(0x99D9D9D9),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(height: h * 9),
              Text(
                project.toString().capitalize(),
                style: GoogleFonts.poppins(
                  color: const Color(0xFF161616),
                  fontSize: 12 * f,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: h * 1),
              Text(
                "$title${parsedString.isNotEmpty ? " - " : ""}${parsedString.toString()}",
                style: GoogleFonts.poppins(
                  color: const Color(0xFF666666),
                  fontSize: 12 * f,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _timerStartStopPauseButton(
      {required IconData icon,
      required Color color,
      required VoidCallback onTap}) {
    return Container(
      width: 48.0,
      height: 48.0,
      decoration: BoxDecoration(
          boxShadow: const [
            BoxShadow(
              color: Colors.white10,
              offset: Offset(0.0, 0.0), //(x,y)
              blurRadius: 1.0,
            ),
          ],
          color: color,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 3)),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          customBorder: const CircleBorder(),
          onTap: onTap,
          child: Icon(
            icon,
            color: Colors.white,
            size: 30,
          ),
        ),
      ),
    );
  }

  _callTimeBottomSheet({required BuildContext context}) {
    showModalBottomSheet(
      backgroundColor: Colors.transparent,
      // isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      showDragHandle: false,

      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(12),
        ),
      ),
      context: context,
      builder: (sheetContext) => _timeBottomSheet(context: sheetContext),
    );
  }

  Widget _timeBottomSheet({required BuildContext context}) {
    return Container(
      height: h * 458,
      width: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(12),
        ),
      ),
      child: ScrollConfiguration(
        behavior: const ScrollBehavior().copyWith(overscroll: false),
        child: ListView(
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.only(top: h * 14, right: w * 29, left: w * 29),
          children: [
            Container(
              height: h * 5,
              margin: EdgeInsets.symmetric(horizontal: w * 89),
              decoration: ShapeDecoration(
                color: const Color(0xFFEAEBED),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(100),
                ),
              ),
            ),
            SizedBox(height: h * 25),
            Text(
              "Evaluate your time in a day/week and understand time you had for tasks.",
              style: GoogleFonts.poppins(
                  fontSize: 14 * f,
                  color: ThemeColors.colorB8B8B8,
                  fontWeight: FontWeight.w400),
            ),
            SizedBox(height: h * 16),
            Consumer<LogTimeProvider>(
              builder: (context, provider, _) {
                return ScrollConfiguration(
                  behavior: const ScrollBehavior().copyWith(overscroll: false),
                  child: ListView.separated(
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: provider.typeOfHoursList.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return _hoursWidgetTile(
                          index: index, provider: provider, context: context);
                    },
                    separatorBuilder: (context, index) {
                      if (index == provider.typeOfHoursList.length) {
                        return const SizedBox();
                      }
                      return Container(
                        width: double.infinity,
                        margin: EdgeInsets.only(top: h * 16),
                        decoration: const ShapeDecoration(
                          shape: RoundedRectangleBorder(
                            side: BorderSide(
                              width: 1,
                              strokeAlign: BorderSide.strokeAlignCenter,
                              color: Color(0x99D9D9D9),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  _hoursWidgetTile(
      {required int index,
      required LogTimeProvider provider,
      required BuildContext context}) {
    // TotalWorkingHoursModel? totalWorkingHoursModel =
    //     provider.totalWorkingHoursModel;
    String typeOfHours = provider.typeOfHoursList[index];
    // bool isSelected = provider.selectedTypeOfHour == typeOfHours;
    return InkWell(
      onTap: () {
        // if (totalWorkingHoursModel != null) {
        //   provider.selectTypeOfHours(
        //       index: index, totalWorkingHoursModel: totalWorkingHoursModel);
        //   Navigator.pop(context);
        // }
      },
      child: Column(
        children: [
          SizedBox(height: h * 15),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                typeOfHours,
                style: GoogleFonts.poppins(
                  color: const Color(0xFF2C2D33),
                  fontSize: 13 * f,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (index == 0)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      children: [
                        Text(
                          "Total Time",
                          style: tsS12colorB8B8B8,
                        ),
                        Text(
                          provider.totalWorkingHoursModel!.todayHoursTotal
                              .toString(),
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF2C2D33),
                            fontSize: 12 * f,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(width: 37 * w),
                    Column(
                      children: [
                        Text(
                          "Avg. Time",
                          style: tsS12colorB8B8B8,
                        ),
                        Text(
                          provider.totalWorkingHoursModel!.todayHoursAverage
                              .toString(),
                          style: GoogleFonts.poppins(
                            color:
                                // isSelected
                                //     ? const Color(0xFFF9637D)
                                const Color(0xFF2C2D33),
                            fontSize: 12 * f,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              if (index == 1)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      children: [
                        Text(
                          "Total Time",
                          style: tsS12colorB8B8B8,
                        ),
                        Text(
                          provider.totalWorkingHoursModel!.past7DaysHoursTotal
                              .toString(),
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF2C2D33),
                            fontSize: 12 * f,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(width: 37 * w),
                    Column(
                      children: [
                        Text(
                          "Avg. Time",
                          style: tsS12colorB8B8B8,
                        ),
                        Text(
                          provider.totalWorkingHoursModel!.past7DaysHoursAverage
                              .toString(),
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF2C2D33),
                            fontSize: 12 * f,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

              if (index == 2)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      children: [
                        Text(
                          "Total Time",
                          style: tsS12colorB8B8B8,
                        ),
                        Text(
                          provider.totalWorkingHoursModel!.lastWeekHoursTotal
                              .toString(),
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF2C2D33),
                            fontSize: 12 * f,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(width: 37 * w),
                    Column(
                      children: [
                        Text(
                          "Avg. Time",
                          style: tsS12colorB8B8B8,
                        ),
                        Text(
                          provider.totalWorkingHoursModel!.lastWeekHoursAverage
                              .toString(),
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF2C2D33),
                            fontSize: 12 * f,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

              if (index == 3)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      children: [
                        Text(
                          "Total Time",
                          style: tsS12colorB8B8B8,
                        ),
                        Text(
                          provider.totalWorkingHoursModel!.thisMonthHoursTotal
                              .toString(),
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF2C2D33),
                            fontSize: 12 * f,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(width: 37 * w),
                    Column(
                      children: [
                        Text(
                          "Avg. Time",
                          style: tsS12colorB8B8B8,
                        ),
                        Text(
                          provider.totalWorkingHoursModel!.thisMonthHoursAverage
                              .toString(),
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF2C2D33),
                            fontSize: 12 * f,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

              if (index == 4)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      children: [
                        Text(
                          "Total Time",
                          style: tsS12colorB8B8B8,
                        ),
                        Text(
                          provider.totalWorkingHoursModel!.lastMonthHoursTotal
                              .toString(),
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF2C2D33),
                            fontSize: 12 * f,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      width: 37 * w,
                    ),
                    Column(
                      children: [
                        Text(
                          "Avg. Time",
                          style: tsS12colorB8B8B8,
                        ),
                        Text(
                          provider.totalWorkingHoursModel!.lastMonthHoursAverage
                              .toString(),
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF2C2D33),
                            fontSize: 12 * f,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

              // if (isSelected)
              //   const Icon(
              //     Icons.check_circle,
              //     color: Color(0xFFF9637D),
              //     size: 20,
              //   ),
            ],
          ),
        ],
      ),
    );
  }
}
