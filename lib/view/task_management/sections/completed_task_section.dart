import 'package:e8_hr_portal/provider/log_time_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../dashboard/widgets/title_text_widget.dart';
import '../widgets/completed_task_card.dart';

class CompletedTaskSection extends StatelessWidget {
  const CompletedTaskSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<LogTimeProvider>(
      builder: (context, provider, child) {
        return Column(
          children: [
            SizedBox(height: 20 * h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const TitleTextWidget('Completed Tasks'),
                InkWell(
                  onTap: () async {
                    DateTime? pickedDate = await showDatePicker(
                        context: context,
                        currentDate: provider.selectedDate,
                        firstDate: DateTime(2010),
                        lastDate: DateTime.now());
                    if (pickedDate != null) {
                      provider.selectedDate = pickedDate;
                      provider.getCompletedTasks();
                    }
                  },
                  child: Container(
                    height: 26 * h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Text(
                            formatDateFromDate(
                                dateTime: provider.selectedDate,
                                format: 'dd, EEEE'),
                            style: tsS10w400495057,
                          ),
                          SizedBox(width: 5 * w),
                          const ImageIcon(
                            AssetImage('assets/icons/ocalendar2.png'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 13 * h),
            provider.completedTasksList.isEmpty
                ? Padding(
                    padding: EdgeInsets.symmetric(vertical: 50 * h),
                    child:
                        const Center(child: Text('No completed task found!')),
                  )
                : ListView.separated(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return CompletedTaskCard(
                          data: provider.completedTasksList[index],
                          provider: provider);
                    },
                    separatorBuilder: (context, index) {
                      return SizedBox(height: 10 * h);
                    },
                    itemCount: provider.completedTasksList.length)
          ],
        );
      },
    );
  }
}
