// ignore_for_file: use_build_context_synchronously
import 'dart:async';
import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/model/employee_model.dart';
import 'package:e8_hr_portal/model/meeting_room_details_model.dart';
import 'package:e8_hr_portal/provider/book_meeting_room_provider.dart';
import 'package:e8_hr_portal/provider/meeting_room_provider.dart';
import 'package:e8_hr_portal/view/master/master_screen.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/book_meeting_rooms/enum/booking_type.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/book_meeting_rooms/meeting_room_history_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../../helper/button_widget.dart';
import '../../../../model/booked_room_status_model.dart';
import '../../../../util/colors.dart';
import '../../../../util/dailoge.dart';
import '../../../../util/date_formatter.dart';
import '../../../../util/styles.dart';
import '../../../../util/validator.dart';
import '../../../profile/widgets/profile_textfield_widget.dart';
import '../new_meeting_room/add_new_meeting_room.dart';
import 'employee_selector.dart';

class BookMeetingScreen extends StatefulWidget {
  final int? bookingID;
  final String? description;
  final BookingType bookingType;
  const BookMeetingScreen(
      {super.key, this.description, this.bookingID, required this.bookingType});
  @override
  State<BookMeetingScreen> createState() => _BookMeetingScreenState();
}

class _BookMeetingScreenState extends State<BookMeetingScreen> {
  final TextEditingController _descriptionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  int _currentPage = 0;
  Timer? _timer;
  late MeetingRoomProvider meetingRoomProvider;
  final PageController _pageController = PageController(initialPage: 0);

  @override
  void initState() {
    if (widget.description != null) {
      _descriptionController.text = widget.description ?? '';
    }
    meetingRoomProvider =
        Provider.of<MeetingRoomProvider>(context, listen: false);
    if (meetingRoomProvider.isMeetingRoomDetailsLoading == false) {
      _timer = Timer.periodic(const Duration(seconds: 3), (Timer timer) {
        if (_currentPage <
            meetingRoomProvider.meetingRoomDetails!.roomImgs!.length - 1) {
          _currentPage++;
        } else {
          _currentPage = 0;
        }

        _pageController.animateToPage(
          _currentPage,
          duration: const Duration(milliseconds: 350),
          curve: Curves.easeIn,
        );
      });
    }
    super.initState();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double maxHeight = MediaQuery.of(context).size.height;
    double maxWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        actions: widget.description == null
            ? [
                _popUpMenuButton(context)
                // IconButton(
                //     onPressed: () {

                //     },
                //     icon: const Icon(
                //       Icons.more_vert,
                //       color: Colors.black,
                //     ))
              ]
            : [],
        leading: InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: Container(
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(5)),
              alignment: Alignment.center,
              padding: const EdgeInsets.only(left: 7),
              margin: const EdgeInsets.fromLTRB(15, 15, 15, 15),
              child: const Icon(Icons.arrow_back_ios)),
        ),
        title: Container(
          padding: const EdgeInsets.symmetric(horizontal: 3),
          decoration: BoxDecoration(
              color: Colors.white.withOpacity(.8),
              borderRadius: BorderRadius.circular(2)),
          child: Text(
            'Book Meeting Room',
            style: GoogleFonts.rubik(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Consumer<MeetingRoomProvider>(
        builder: (context, provider, child) {
          MeetingRoomDetailsModel? roomDetailes = provider.meetingRoomDetails;
          return provider.isMeetingRoomDetailsLoading == true
              ? const Center(
                  child: CircularProgressIndicator(),
                )
              : Stack(
                  children: [
                    _roomImageSection(maxHeight, roomDetailes),
                    _dottedIndicator(maxHeight, context, roomDetailes),
                    DraggableScrollableSheet(
                      initialChildSize: .72,
                      maxChildSize: .72,
                      minChildSize: .72,
                      builder: (context, scrollController) {
                        return ListView(
                          controller: scrollController,
                          children: [
                            // Container(
                            //   height: 220,
                            //   color: Colors.transparent,
                            // ),
                            Container(
                              decoration: const BoxDecoration(
                                borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(35),
                                    topRight: Radius.circular(35)),
                                color: Colors.white,
                              ),
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    vertical: 8, horizontal: maxWidth * .0533),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: 20),
                                    Align(
                                      alignment: Alignment.center,
                                      child: Text(
                                          roomDetailes?.name ?? 'Room name',
                                          style: tsS18w500),
                                    ),
                                    const SizedBox(height: 20),
                                    const SizedBox(height: 10),
                                    Align(
                                      alignment: Alignment.topLeft,
                                      child: Text('Amenities',
                                          style: tsS14c8391B5),
                                    ),
                                    const SizedBox(height: 10),
                                    Row(
                                      children: [
                                        const SizedBox(
                                            height: 25,
                                            width: 25,
                                            child: ImageIcon(
                                                AssetImage(
                                                    'assets/icons/seat.png'),
                                                color: Color(0xff7E7E7E))),
                                        const SizedBox(width: 5),
                                        Text(
                                          '${roomDetailes?.seatCount.toString() ?? 0} Seats',
                                          style: GoogleFonts.rubik(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                              color: const Color(0xff7E7E7E)),
                                        ),
                                        if (roomDetailes?.amenities != null)
                                          Row(
                                            children: roomDetailes!.amenities!
                                                .map((e) {
                                              return Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 10),
                                                child: ImageIcon(
                                                  CachedNetworkImageProvider(
                                                    e.icon.toString(),
                                                  ),
                                                  color:
                                                      const Color(0xff7E7E7E),
                                                  size: 18,
                                                ),
                                              );
                                            }).toList(),
                                          ),
                                      ],
                                    ),
                                    _dateAndTimeSelectingSection(
                                        roomDetailes?.id),
                                    if (widget.bookingType !=
                                        BookingType.reschedule)
                                      Form(
                                        autovalidateMode:
                                            AutovalidateMode.onUserInteraction,
                                        key: _formKey,
                                        child: ProfileTextFieldWidget(
                                          controller: _descriptionController,
                                          // labelText: 'Meeting description',
                                          label: RichText(
                                            text: TextSpan(
                                              text: 'Meeting Description',
                                              style: GoogleFonts.rubik(
                                                color: ThemeColors.titleColor,
                                              ),
                                              children: [
                                                TextSpan(
                                                    text: '*',
                                                    style: GoogleFonts.rubik(
                                                        color: Colors.red))
                                              ],
                                            ),
                                          ),
                                          validator: Validator.text,
                                        ),
                                      ),
                                    _employeeSelectingSection(context),
                                    _bookingButton(),
                                  ],
                                ),
                              ),
                            )
                          ],
                        );
                      },
                    )
                  ],
                );
        },
      ),
    );
  }

  PopupMenuButton<String> _popUpMenuButton(BuildContext context) {
    MeetingRoomDetailsModel? meetingRoomDetails =
        Provider.of<MeetingRoomProvider>(context).meetingRoomDetails;
    return PopupMenuButton(
      onSelected: (value) {
        if (value == 'delete') {
          showDialog(
              context: context,
              builder: (ctxt) {
                return AlertDialog(
                  title: const Text('Confirm delete'),
                  content: Text(
                      'Are you sure you want to delete ${meetingRoomDetails?.name}?'),
                  actions: [
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[300],
                      ),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(color: Colors.black),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        Navigator.pop(context);
                        if (meetingRoomProvider.meetingRoomDetails?.id !=
                            null) {
                          await meetingRoomProvider.deleteMeetingRoom(
                              context: context,
                              roomID: meetingRoomProvider.meetingRoomDetails!.id
                                  .toString());
                          Navigator.pop(context);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ThemeColors.secondaryColor,
                      ),
                      child: const Text('Delete'),
                    )
                  ],
                );
              });
        } else if (value == 'edit') {
          showDialog(
              context: context,
              builder: (ctxt) {
                return AlertDialog(
                  title: const Text('Confirm edit'),
                  content: Text(
                      'Are you sure you want to edit ${meetingRoomDetails?.name}?'),
                  actions: [
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[300],
                      ),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(color: Colors.black),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        Navigator.pop(context);
                        meetingRoomProvider.selectedAmenitiesList =
                            meetingRoomDetails?.amenities ?? [];
                        meetingRoomProvider.selectedAmenity =
                            meetingRoomProvider.amenitiesList.firstWhere(
                                (element) =>
                                    element.id ==
                                    meetingRoomDetails?.amenities?.last.id);
                        meetingRoomProvider.selectedUserPolicy =
                            meetingRoomProvider.policiesList.firstWhere(
                                (e) => e.id == meetingRoomDetails?.policy);
                        meetingRoomProvider.alreadySelectedImagesList =
                            meetingRoomDetails?.roomImgs ?? [];
                        meetingRoomProvider.removedImagesIdList.clear();
                        meetingRoomProvider.selectedMeetingRoomImages.clear();

                        await Navigator.of(context).push(CupertinoPageRoute(
                          builder: (context) => AddNewMeetingRoomScreen(
                              meetingRoomName: meetingRoomDetails?.name,
                              seatCount:
                                  meetingRoomDetails?.seatCount.toString(),
                              meetingRoomID: meetingRoomDetails?.id.toString()),
                        ));
                        if (meetingRoomDetails?.id != null) {
                          meetingRoomProvider.getMeetingRoomDetails(
                              meetingRoomID: meetingRoomDetails!.id.toString());
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ThemeColors.secondaryColor,
                      ),
                      child: const Text('Edit'),
                    )
                  ],
                );
              });
        } else {
          Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => MeetingRoomHistoryScreen(
                  roomID: meetingRoomDetails!.id!.toInt())));
        }
      },
      icon: const Icon(Icons.more_vert),
      itemBuilder: (context) {
        return [
          const PopupMenuItem(
            value: 'history',
            child: Text('Meeting Room History'),
          ),
          if (LoginModel.meetingRoomPermission == true)
            const PopupMenuItem(
              value: 'edit',
              child: Text('Edit Meeting Room'),
            ),
          if (LoginModel.meetingRoomPermission == true)
            const PopupMenuItem(
              value: 'delete',
              child: Text('Delete Meeting Room'),
            ),
        ];
      },
    );
  }

  Widget _dateAndTimeSelectingSection(int? meetingRoomID) {
    return Column(
      children: [
        const SizedBox(height: 10),
        Align(
          alignment: Alignment.topLeft,
          child: Text('Select Meeting Date & Time', style: tsS14c8391B5),
        ),
        const SizedBox(height: 10),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Consumer<BookMeetingRoomProvider>(
                builder: (context, provider, child) {
                  String selectedDate = formatDateFromDate(
                      dateTime: provider.selectedDate, format: 'dd-MM-yyyy');
                  return GestureDetector(
                    onTap: () => selectBookingDate(
                      roomId: meetingRoomID.toString(),
                      context: context,
                      provider: provider,
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: ThemeColors.colorDEE7FF,
                        ),
                        borderRadius: BorderRadius.circular(5.0),
                      ),
                      padding: const EdgeInsets.all(10.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          const ImageIcon(
                              AssetImage('assets/icons/calendar.png'),
                              color: Colors.black
                              //  ThemeColors.color1E2138,
                              ),
                          //    const SizedBox(width: 15),
                          Text(
                            selectedDate,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              flex: 3,
              child: Consumer<BookMeetingRoomProvider>(
                builder: (context, provider, _) {
                  return GestureDetector(
                    onTap: () => selectBookingTimeRange(
                        context: context,
                        provider: provider,
                        roomId: meetingRoomID.toString()),
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: ThemeColors.colorDEE7FF,
                        ),
                        borderRadius: BorderRadius.circular(5.0),
                      ),
                      padding: const EdgeInsets.all(10.0),
                      child: Row(
                        children: [
                          const ImageIcon(AssetImage('assets/icons/clock.png'),
                              color: Colors.black),
                          const SizedBox(width: 15),
                          Text(
                            '${provider.formatTimeOfDayFormat(context: context, time: provider.selectedStartTime)} - ${provider.formatTimeOfDayFormat(context: context, time: provider.selectedEndTime)}',
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 5),
        // Consumer<MeetingRoomBookingProvider>(
        //   builder: (context, provider, child) {
        //     if (provider.isMeetingRoomAvailable == true) {
        //       return Container();
        //     }
        //     return Align(
        //       alignment: Alignment.topLeft,
        //       child: Text(
        //         '* ${provider.roomAvailabilityCheckResponse} . Please change your meeting schedule.',
        //         textAlign: TextAlign.justify,
        //         style: ts12second,
        //       ),
        //     );
        //   },
        // ),
        const SizedBox(height: 5)
      ],
    );
  }

  Widget _employeeSelectingSection(BuildContext context) {
    double maxHeight = MediaQuery.of(context).size.height;
    double maxWidth = MediaQuery.of(context).size.width;
    if (widget.bookingType == BookingType.reschedule) {
      return const SizedBox(height: 15);
    }
    return Consumer<BookMeetingRoomProvider>(
      builder: (context, provider, child) {
        return Column(
          children: [
            const SizedBox(height: 15),
            InkWell(
              onTap: () {
                FocusScope.of(context).unfocus();
                provider.textFieldCleared();
                showEmployeeSelectList(context);
              },
              child: Container(
                height: maxHeight * .049,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: const Color(0xff8391B5))),
                child: Row(
                  children: [
                    SizedBox(
                      width: maxWidth * .018,
                    ),
                    Text(
                      'Invite Your Colleagues',
                      style: GoogleFonts.inter(
                          fontSize: 12, fontWeight: FontWeight.w500),
                    ),
                    const Spacer(),
                    const ImageIcon(AssetImage('assets/icons/expand.png')),
                    SizedBox(
                      width: maxWidth * .018,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 5),
            ListView.separated(
              physics: const BouncingScrollPhysics(),
              padding: const EdgeInsets.all(5),
              shrinkWrap: true,
              itemCount: provider.allSelectedMembers.length,
              itemBuilder: (BuildContext context, int index) {
                EmployeeModel member = provider.allSelectedMembers[index];
                return Container(
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: ThemeColors.colorDEE7FF),
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          child: Row(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(35),
                                child: CachedNetworkImage(
                                  height: 23,
                                  width: 23,
                                  fit: BoxFit.cover,
                                  imageUrl: member.profilePic ?? '',
                                  errorWidget: (context, url, error) {
                                    return Container(
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                          color: ThemeColors.primaryColor,
                                          shape: BoxShape.circle,
                                        ),
                                        child: Center(
                                          child: (Text(member.name
                                              .toString()
                                              .substring(0, 1)
                                              .toUpperCase())),
                                        ));
                                  },
                                ),
                              ),
                              const SizedBox(
                                width: 5,
                              ),
                              Text(
                                member.name ?? '',
                                style: tsS14BN,
                              ),
                            ],
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            provider.addOrRemoveMembers(member);
                          },
                          child: Icon(
                            Icons.remove_circle_outline,
                            color: ThemeColors.secondaryColor,
                          ),
                        )
                      ],
                    ),
                  ),
                );
              },
              separatorBuilder: (BuildContext context, int index) {
                return const SizedBox(
                  height: 5,
                );
              },
            ),
            if (provider.alreadyInvitedMembersList != null)
              ListView.separated(
                physics: const BouncingScrollPhysics(),
                padding: const EdgeInsets.only(left: 5, right: 5, bottom: 5),
                shrinkWrap: true,
                itemCount: provider.alreadyInvitedMembersList?.length ?? 0,
                itemBuilder: (BuildContext context, int index) {
                  InvitedUser? member =
                      provider.alreadyInvitedMembersList?[index];
                  return Container(
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: ThemeColors.colorDEE7FF),
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            child: Row(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(35),
                                  child: CachedNetworkImage(
                                    height: 23,
                                    width: 23,
                                    fit: BoxFit.cover,
                                    imageUrl: member?.profilePic ?? '',
                                    errorWidget: (context, url, error) {
                                      return Container(
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            color: ThemeColors.primaryColor,
                                            shape: BoxShape.circle,
                                          ),
                                          child: Center(
                                            child: (Text(member?.user
                                                    .toString()
                                                    .substring(0, 1)
                                                    .toUpperCase() ??
                                                '')),
                                          ));
                                    },
                                  ),
                                ),
                                const SizedBox(
                                  width: 5,
                                ),
                                Text(
                                  member?.user ?? '',
                                  style: tsS14BN,
                                ),
                              ],
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              provider.removeInvitedMemeber(member!.userId!);
                            },
                            child: Icon(
                              Icons.remove_circle_outline,
                              color: ThemeColors.secondaryColor,
                            ),
                          )
                        ],
                      ),
                    ),
                  );
                },
                separatorBuilder: (BuildContext context, int index) {
                  return const SizedBox(
                    height: 5,
                  );
                },
              ),
            const SizedBox(height: 20),
          ],
        );
      },
    );
  }

  Widget _bookingButton() {
    String title = 'Book Now';
    String buttonTitle = 'Book';
    switch (widget.bookingType) {
      case BookingType.booking:
        title = 'Book Now';
        break;
      case BookingType.edit:
        title = 'Update';
        buttonTitle = 'Update';
        break;
      case BookingType.reschedule:
        title = 'Reschedule';
        buttonTitle = 'Reschedule';
        break;
    }
    return Consumer2<BookMeetingRoomProvider, MeetingRoomProvider>(
      builder: (context, provider, provider2, child) {
        return ButtonWidget(
          onPressed: () async => _onButtonPressed(),
          title: title,
          color: ThemeColors.secondaryColor,
          textStyle: GoogleFonts.rubik(
              fontSize: 16, fontWeight: FontWeight.w500, color: Colors.black),
        );
      },
    );
  }

  _bookigNew() async {
    final provider = context.read<BookMeetingRoomProvider>();
    final provider2 = context.read<MeetingRoomProvider>();
    provider.bookMeetingRoom(
        meetingRoomID: provider2.meetingRoomDetails!.id.toString(),
        description: _descriptionController.text,
        context: context);
  }

  _editBooking() async {
    final provider = context.read<BookMeetingRoomProvider>();
    final provider2 = context.read<MeetingRoomProvider>();
    provider.editBookedMeetingRoom(
        bookingID: widget.bookingID.toString(),
        meetingRoomID: provider2.meetingRoomDetails!.id.toString(),
        description: _descriptionController.text,
        context: context);
  }

  _rescheduleBooking() async {
    final provider = context.read<BookMeetingRoomProvider>();
    bool isSuccess = await provider.rescheduleApprovedMeetingRoom(
        bookingID: widget.bookingID!, reason: _descriptionController.text);
    if (isSuccess) {
      Navigator.pop(context);
      Navigator.pop(context);
    }
  }

  _onButtonPressed() async {
    final provider = context.read<BookMeetingRoomProvider>();
    final provider2 = context.read<MeetingRoomProvider>();
    String buttonTitle = 'Book';
    String dialogTitle = 'Confirm Booking';
    switch (widget.bookingType) {
      case BookingType.booking:
        buttonTitle = 'Book';
        dialogTitle = 'Confirm Booking';
        break;
      case BookingType.edit:
        buttonTitle = 'Update';
        dialogTitle = 'Edit Booking';
        break;
      case BookingType.reschedule:
        buttonTitle = 'Reschedule';
        dialogTitle = 'Reschedule Booking';
        break;
    }
    if (widget.bookingType != BookingType.reschedule) {
      if (_formKey.currentState!.validate()) {
        showDialog(
          context: context,
          builder: (ctxt) {
            return AlertDialog(
              title: Text(dialogTitle),
              content: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Meeting Room : ${provider2.meetingRoomDetails?.name}',
                    style: TextStyle(color: ThemeColors.secondaryColor),
                  ),
                  Text(
                    'Booking Time : ${provider.formatTimeOfDayFormat(context: context, time: provider.selectedStartTime)} - ${provider.formatTimeOfDayFormat(context: context, time: provider.selectedEndTime)}',
                    style: TextStyle(color: ThemeColors.secondaryColor),
                  ),
                  // Text(
                  //   minutes == '0'
                  //       ? 'Duration : $hour Hour'
                  //       : 'Duration : $hour Hour $minutes minutes',
                  //   style: TextStyle(color: ThemeColors.secondaryColor),
                  // ),
                  const SizedBox(
                    height: 10,
                  ),
                  const Text('Are you sure you want to continue?'),
                ],
              ),
              actions: [
                ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[300],
                  ),
                  child: const Text(
                    'Cancel',
                    style: TextStyle(color: Colors.black),
                  ),
                ),
                ElevatedButton(
                  onPressed: () async {
                    Navigator.pop(context);
                    if (widget.bookingType == BookingType.booking) {
                      _bookigNew();
                    } else if (widget.bookingType == BookingType.edit) {
                      _editBooking();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ThemeColors.secondaryColor,
                  ),
                  child: Text(buttonTitle),
                )
              ],
            );
          },
        );
      }
    } else {
      showDialog(
        context: context,
        builder: (ctxt) {
          return AlertDialog(
            title: Text(dialogTitle),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Meeting Room : ${provider2.meetingRoomDetails?.name}',
                  style: TextStyle(color: ThemeColors.secondaryColor),
                ),
                Text(
                  'Booking Time : ${provider.formatTimeOfDayFormat(context: context, time: provider.selectedStartTime)} - ${provider.formatTimeOfDayFormat(context: context, time: provider.selectedEndTime)}',
                  style: TextStyle(color: ThemeColors.secondaryColor),
                ),
                // Text(
                //   minutes == '0'
                //       ? 'Duration : $hour Hour'
                //       : 'Duration : $hour Hour $minutes minutes',
                //   style: TextStyle(color: ThemeColors.secondaryColor),
                // ),
                const SizedBox(
                  height: 10,
                ),
                const Text('Are you sure you want to continue?'),
              ],
            ),
            actions: [
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[300],
                ),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.black),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _rescheduleBooking();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: ThemeColors.secondaryColor,
                ),
                child: Text(buttonTitle),
              )
            ],
          );
        },
      );
    }
  }

  SizedBox _roomImageSection(
      double maxHeight, MeetingRoomDetailsModel? roomDetailes) {
    return SizedBox(
      height: maxHeight * 0.45,
      child: PageView.builder(
        scrollDirection: Axis.horizontal,
        controller: _pageController,
        itemCount: roomDetailes?.roomImgs?.length ?? 0,
        itemBuilder: (BuildContext context, int index) {
          return CachedNetworkImage(
            imageUrl: roomDetailes?.roomImgs?[index].image ?? '',
            fit: BoxFit.cover,
            errorWidget: (context, url, error) {
              return Image.network(
                'https://www.wework.com/ideas/wp-content/uploads/sites/4/2021/08/20201008-199WaterSt-2_v1-scaled.jpg',
                fit: BoxFit.fill,
              );
            },
          );
        },
        onPageChanged: (index) {
          setState(() {
            _currentPage = index;
          });
        },
      ),
    );
  }

  Positioned _dottedIndicator(double maxHeight, BuildContext context,
      MeetingRoomDetailsModel? roomDetailes) {
    return Positioned(
      top: maxHeight * .35,
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            roomDetailes?.roomImgs?.length ?? 0,
            (index) {
              return Container(
                margin: const EdgeInsets.only(right: 5),
                height: 8,
                width: 8,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: index == _currentPage
                      ? ThemeColors.primaryColor
                      : ThemeColors.colorDEE7FF,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Future<void> selectBookingDate({
    required String? roomId,
    required BuildContext context,
    required BookMeetingRoomProvider provider,
  }) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: provider.selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime(2050),
    );
    if (picked != null) {
      if (provider.isValidDate(picked)) {
        provider.selectedDate = picked;
      } else {
        showToastText('Please select a valid date');
      }

      ////////////////////
      // TimeOfDay d = provider.selectedStartTime;
      // String startTime = "${d.hour}:${d.minute}";
      // TimeOfDay e = provider.selectedEndTime;
      // String endTime = "${e.hour}:${e.minute}";
      // final DateFormat formatter = DateFormat('yyyy-MM-dd');
      // final String formatted = formatter.format(provider.selectedDate);

      // provider.checkRoomAvailability(
      //     date: formatted,
      //     startTime: startTime,
      //     endTime: endTime,
      //     idOfRoom: roomId);
//      provider.checkAvailableMeetingRooms(widget.meetingRoom.id);
    }
  }

  Future<void> selectBookingTimeRange(
      {required BuildContext context,
      required BookMeetingRoomProvider provider,
      required String? roomId}) async {
    TimeOfDay? startTime = await showTimePicker(
      initialEntryMode: TimePickerEntryMode.dialOnly,
      context: context,
      initialTime: provider.selectedStartTime,
      helpText: 'SELECT START TIME',
    );
    if (startTime != null) {
      if (provider.isValidStartTime(startTime)) {
        provider.selectedStartTime = startTime;
        provider.selectedEndTime = startTime.add(hour: 1);
        selectBookingEndTime(
            context: context, provider: provider, roomId: roomId);
      } else {
        showToastText('Please select a valid start time');
      }
    }
    // final TimeOfDay? startTime = await showCustomTimePicker(
    //   context: context,
    //   initialTime: provider.selectedStartTime,
    //   onFailValidation: (context) => print('Unavailable selection'),
    //   helpText: 'SELECT START TIME',
    //   //   selectableTimePredicate: (time) => time!.minute % 15 == 0,
    // );
    // if (startTime != null) {
    //   if (provider.isValidStartTime(startTime)) {
    //     provider.selectedStartTime = startTime;
    //     provider.selectedBookingStartTime =
    //         provider.bookingTimetoDateTime(startTime);
    //     provider.selectedBookingEndTime =
    //         provider.selectedBookingStartTime.add(const Duration(hours: 1));
    //     provider.selectedEndTime = TimeOfDay(
    //       hour: provider.selectedBookingEndTime.hour,
    //       minute: provider.selectedBookingEndTime.minute,
    //     );
    //     selectBookingEndTime(
    //         context: context, provider: provider, roomId: roomId);
    //   } else {
    //     showToastText('Please select a valid start time');
    //   }
    // }
  }

  Future<void> selectBookingEndTime(
      {required BuildContext context,
      required BookMeetingRoomProvider provider,
      required String? roomId}) async {
    final TimeOfDay? endTime = await showTimePicker(
      initialEntryMode: TimePickerEntryMode.dialOnly,
      context: context,
      initialTime: provider.selectedEndTime,
      helpText: 'SELECT END TIME',
      // selectableTimePredicate: (time) => time!.minute % 15 == 0,
    );
    if (endTime != null) {
      if (provider.isValidEndTime(endTime)) {
        provider.selectedEndTime = endTime;
        // provider.selectedBookingEndTime =
        //     provider.bookingTimetoDateTime(endTime);
      } else {
        showToastText('Please select a valid end time');
      }
    }

    // TimeOfDay d = provider.selectedStartTime;
    // String startTime = "${d.hour}:${d.minute}";
    // TimeOfDay e = provider.selectedEndTime;
    // String endingTime = "${e.hour}:${e.minute}";
    // final DateFormat formatter = DateFormat('yyyy-MM-dd');
    // final String formatted = formatter.format(provider.selectedDate);

    // provider.checkRoomAvailability(
    //     date: formatted,
    //     startTime: startTime,
    //     endTime: endingTime,
    //     idOfRoom: roomId);
  }
}

Future showEmployeeSelectList(BuildContext context) async {
  return showModalBottomSheet(
      isScrollControlled: true,
      isDismissible: true,
      useSafeArea: true,
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadiusDirectional.only(
          topEnd: Radius.circular(25),
          topStart: Radius.circular(25),
        ),
      ),
      builder: (context) {
        return const EmployeeSelector();
      });
}
