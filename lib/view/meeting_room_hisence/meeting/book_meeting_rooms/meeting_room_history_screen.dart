import 'package:e8_hr_portal/provider/booked_meeting_room_provider.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/book_meeting_rooms/widgets/meeting_room_history_card.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';

import '../../../../model/booked_room_status_model.dart';

class MeetingRoomHistoryScreen extends StatefulWidget {
  final int roomID;
  const MeetingRoomHistoryScreen({super.key, required this.roomID});

  @override
  State<MeetingRoomHistoryScreen> createState() =>
      _MeetingRoomHistoryScreenState();
}

class _MeetingRoomHistoryScreenState extends State<MeetingRoomHistoryScreen> {
  @override
  void initState() {
    Provider.of<BookedMeetingRoomProvider>(context, listen: false)
        .initMeetingRoomHistoryPagination(roomID: widget.roomID);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        screenTitle: 'Meeting Room History',
        body: Consumer<BookedMeetingRoomProvider>(
          builder: (context, provider, child) {
            return PagedListView.separated(
              padding: const EdgeInsets.all(15.0),
              pagingController: provider.roomHistoryPaginationController,
              builderDelegate: PagedChildBuilderDelegate<BookedRoomStatusModel>(
                itemBuilder: (context, item, index) {
                  return MeetingRoomHistoryCard(
                      userName: item.bookedBy?.user,
                      userDesignation: item.bookedBy?.designation,
                      description: item.description,
                      startTime: item.startTime,
                      endTime: item.endTime,
                      id: item.id.toString(),
                      invitedMembersList: item.invitedUser,
                      profileImage: item.bookedBy?.profilePic,
                      roomName: item.room?.name,
                      date: item.bookingDate,
                      roomImage: item.room?.roomImgs?.first.image);
                },
                noItemsFoundIndicatorBuilder: (context) {
                  return const Center(
                    child: Text(
                      'No History',
                      style: TextStyle(color: Colors.black, fontSize: 18),
                    ),
                  );
                },
                firstPageProgressIndicatorBuilder: (context) {
                  return const Center(
                    child: CircularProgressIndicator.adaptive(),
                  );
                },
              ),
              separatorBuilder: (context, index) {
                return const SizedBox(height: 15);
              },
            );
          },
        ));
  }
}
