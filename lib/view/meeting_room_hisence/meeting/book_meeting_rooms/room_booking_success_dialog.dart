// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/material.dart';
// import 'package:office_app/model/logged_in_user.dart';
// import 'package:office_app/util/colors.dart';
// import 'package:office_app/util/styles.dart';

// class RoomBookingSuccessDialog extends StatelessWidget {
//   final String meetingRoomName;
//   final String bookingTime;
//   const RoomBookingSuccessDialog(
//       {required this.meetingRoomName, required this.bookingTime, Key? key})
//       : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
//       insetPadding: const EdgeInsets.symmetric(horizontal: 15),
//       child: Stack(
//         children: [
//           Container(
//             // height: 460,
//             padding: const EdgeInsets.symmetric(vertical: 36),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.center,
//               mainAxisAlignment: MainAxisAlignment.start,
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 const Align(
//                   child: Icon(
//                     Icons.check_circle,
//                     size: 80,
//                     color: Colors.green,
//                   ),
//                 ),
//                 Align(
//                   alignment: Alignment.topCenter,
//                   child: Text(
//                     "Thank You!",
//                     style: ts28BoldBlack,
//                   ),
//                 ),
//                 Align(
//                   alignment: Alignment.topCenter,
//                   child: Text(
//                     "Your meeting room is now scheduled.",
//                     style: tsS14BN,
//                   ),
//                 ),
//                 const SizedBox(
//                   height: 50,
//                 ),
//                 Container(
//                   height: 92,
//                   width: 92,
//                   decoration: BoxDecoration(
//                     color: ThemeColors.colorDEE7FF,
//                     borderRadius: BorderRadius.circular(20),
//                     image: LoggedInUser.profilePhoto == null
//                         ? null
//                         : DecorationImage(
//                             image: CachedNetworkImageProvider(
//                               LoggedInUser.profilePhoto.toString(),
//                             ),
//                             fit: BoxFit.cover,
//                           ),
//                   ),
//                   child: LoggedInUser.profilePhoto == null
//                       ? Center(
//                           child: Text(
//                             LoggedInUser.name.toString().substring(0, 1).toUpperCase(),
//                             style: ts28BoldBlack,
//                           ),
//                         )
//                       : null,
//                 ),
//                 Align(
//                   alignment: Alignment.topCenter,
//                   child: Text(
//                     LoggedInUser.name.toString(),
//                     overflow: TextOverflow.ellipsis,
//                     style: tsS20BN,
//                   ),
//                 ),
//                 if (LoggedInUser.designation != null)
//                   Align(
//                     alignment: Alignment.topCenter,
//                     child: Text(
//                       LoggedInUser.designation.toString(),
//                       style: tsS14c8391B5,
//                     ),
//                   ),
//                 const SizedBox(
//                   height: 20,
//                 ),
//                 Container(
//                   width: 250,
//                   decoration: BoxDecoration(color: ThemeColors.colorF3F3F3),
//                   child: Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: Column(
//                       children: [
//                         Align(
//                           alignment: Alignment.topCenter,
//                           child: Text(
//                             meetingRoomName,
//                             overflow: TextOverflow.ellipsis,
//                             style: tsS14w500,
//                           ),
//                         ),
//                         const SizedBox(
//                           height: 5,
//                         ),
//                         Align(
//                           alignment: Alignment.topLeft,
//                           child: Text(
//                             bookingTime,
//                             style: tsS14BN,
//                           ),
//                         )
//                       ],
//                     ),
//                   ),
//                 )
//               ],
//             ),
//           ),
//           Positioned(
//             right: 11,
//             top: 11,
//             child: InkWell(
//               onTap: () {
//                 Navigator.pop(context);
//               },
//               child: const SizedBox(
//                 height: 24,
//                 width: 24,
//                 child: Image(
//                   image: AssetImage(
//                     "assets/icons/close.png",
//                   ),
//                 ),
//               ),
//             ),
//           )
//         ],
//       ),
//     );
//   }
// }
