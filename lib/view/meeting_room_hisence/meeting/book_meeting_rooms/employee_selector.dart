import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/employee_model.dart';
import 'package:e8_hr_portal/provider/book_meeting_room_provider.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../../util/colors.dart';

class EmployeeSelector extends StatefulWidget {
  const EmployeeSelector({super.key});

  @override
  State<EmployeeSelector> createState() => _EmployeeSelectorState();
}

class _EmployeeSelectorState extends State<EmployeeSelector> {
  TextEditingController searchController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Consumer<BookMeetingRoomProvider>(
      builder: (context, provider, child) {
        return Container(
          decoration: const BoxDecoration(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(40), topRight: Radius.circular(40))),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: size.width * .05333),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: size.height * .0381,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Select Employees',
                        style: GoogleFonts.inter(
                            fontWeight: FontWeight.w500, fontSize: 16)),
                    InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: Container(
                        height: size.height * .036,
                        width: size.width * .16,
                        color: ThemeColors.secondaryColor,
                        child: Center(
                          child: Text(
                            'Done',
                            style: GoogleFonts.inter(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: Colors.white),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                SizedBox(
                  height: size.height * .0209,
                ),
                Container(
                  height: size.height * .051,
                  decoration: BoxDecoration(
                      color: const Color(0xffFFFFFF),
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: const [
                        BoxShadow(
                          offset: Offset(2, 2),
                          blurRadius: 10,
                          color: Color.fromRGBO(200, 200, 200, .5),
                        ),
                      ]),
                  child: TextFormField(
                    onChanged: (value) {
                      provider.filter(value);
                      setState(() {});
                    },
                    controller: searchController,
                    decoration: InputDecoration(
                        prefixIcon: const ImageIcon(
                          AssetImage('assets/icons/search2.png'),
                          color: Color(0xffAFAFAF),
                        ),
                        suffixIcon: searchController.text.isEmpty
                            ? null
                            : InkWell(
                                onTap: () {
                                  searchController.clear();
                                  setState(() {});
                                  provider.textFieldCleared();
                                },
                                child: const ImageIcon(
                                  AssetImage('assets/icons/clear.png'),
                                  color: Color(0xff131313),
                                ),
                              ),
                        contentPadding: const EdgeInsets.all(10),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide:
                              const BorderSide(color: Color(0xffFFFFFF)),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide:
                              const BorderSide(color: Color(0xffFFFFFF)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide:
                              const BorderSide(color: Color(0xffFFFFFF)),
                        ),
                        hintText: 'Search',
                        hintStyle: GoogleFonts.poppins(
                            color: const Color(0xffAFAFAF),
                            fontSize: 12,
                            fontWeight: FontWeight.w400)),
                  ),
                ),
                SizedBox(height: size.height * 0.02),
                Expanded(
                  child: provider.colleguesListToShowResult.isEmpty
                      ? const Center(child: Text('No Result Found'))
                      : ListView.separated(
                          separatorBuilder: (context, index) {
                            return const Divider(
                              thickness: 1,
                              color: Color(0xffE3E3E3),
                            );
                          },
                          padding: EdgeInsets.zero,
                          physics: const ClampingScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: provider.colleguesListToShowResult.length,
                          itemBuilder: (context, index) {
                            EmployeeModel data =
                                provider.colleguesListToShowResult[index];

                            return InkWell(
                              onTap: () {
                                //for edit booked meeting room
                                if (provider.alreadyInvitedMembersList !=
                                        null &&
                                    provider.alreadyInvitedMembersList!
                                        .where((element) =>
                                            element.userId == data.id)
                                        .isNotEmpty) {
                                  provider.removeInvitedMemeber(data.id!);
                                } else {
                                  //for normal booking
                                  provider.addOrRemoveMembers(data);
                                }
                              },
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(40),
                                        child: CachedNetworkImage(
                                          fit: BoxFit.cover,
                                          height: size.height * .032,
                                          width: size.height * .032,
                                          imageUrl: data.profilePic ?? '',
                                          errorWidget: (context, url, error) {
                                            return Container(
                                              height: size.height * .032,
                                              width: size.height * .032,
                                              decoration: BoxDecoration(
                                                color: ThemeColors.primaryColor,
                                                shape: BoxShape.circle,
                                              ),
                                              alignment: Alignment.center,
                                              child: Text(
                                                data.name!
                                                    .substring(0, 1)
                                                    .toUpperCase()
                                                    .toUpperCase(),
                                                style: GoogleFonts.rubik(
                                                    fontWeight: FontWeight.w500,
                                                    color: Colors.white),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                      SizedBox(
                                        width: size.width * .0426,
                                      ),
                                      Expanded(
                                          child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            data.name ?? '',
                                            style: GoogleFonts.inter(
                                                fontWeight: FontWeight.w500,
                                                fontSize: 12),
                                          ),
                                          Text(
                                            data.designation?.first.name ?? '',
                                            style: GoogleFonts.inter(
                                                fontWeight: FontWeight.w400,
                                                fontSize: 12,
                                                color: const Color(0xff979797)),
                                          ),
                                        ],
                                      )),
                                      Checkbox(
                                          fillColor: MaterialStateProperty.all(
                                              ThemeColors.secondaryColor),
                                          value: provider.allSelectedMembers
                                                  .contains(data) ||
                                              (provider.alreadyInvitedMembersList !=
                                                      null &&
                                                  provider
                                                      .alreadyInvitedMembersList!
                                                      .where((element) =>
                                                          element.userId ==
                                                          data.id)
                                                      .isNotEmpty),
                                          onChanged: (value) {
                                            //for edit booked meeting room
                                            if (provider.alreadyInvitedMembersList !=
                                                    null &&
                                                provider
                                                    .alreadyInvitedMembersList!
                                                    .where((element) =>
                                                        element.userId ==
                                                        data.id)
                                                    .isNotEmpty) {
                                              provider.removeInvitedMemeber(
                                                  data.id!);
                                            } else {
                                              //for normal booking
                                              provider.addOrRemoveMembers(data);
                                            }
                                          })
                                    ],
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
