import 'package:e8_hr_portal/provider/booked_meeting_room_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';
import '../../../../model/booked_room_status_model.dart';
import '../../../../util/colors.dart';
import '../../../../util/styles.dart';
import 'widgets/meeting_request_card_for_admin.dart';

class MeetingRequestsScreen extends StatelessWidget {
  const MeetingRequestsScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        screenTitle: 'Meeting Requests',
        body: Column(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  _topHeadingSection(),
                  const SizedBox(height: 20),
                  Expanded(child: Consumer<BookedMeetingRoomProvider>(
                    builder: (context, provider, child) {
                      return IndexedStack(
                        index: provider.currentIndex,
                        alignment: AlignmentDirectional.topStart,
                        children: [
                          _cardSection(),
                          _cardSection(),
                          _cardSection()
                        ],
                      );
                    },
                  )),
                ],
              ),
            ),
          ],
        ));
  }

  Widget _cardSection() {
    return Consumer<BookedMeetingRoomProvider>(
      builder: (context, provider, child) {
        return provider.isRequestLoading == true
            ? const Center(child: CircularProgressIndicator())
            : provider.adminMeetingRoomRequestList.isEmpty
                ? const Center(
                    child: Text(
                      'No Data Found',
                      style: TextStyle(color: Colors.black, fontSize: 18),
                    ),
                  )
                : ListView.separated(
                    itemCount: provider.adminMeetingRoomRequestList.length,
                    padding: EdgeInsets.symmetric(horizontal: 15.0),
                    itemBuilder: (context, index) {
                      BookedRoomStatusModel item =
                          provider.adminMeetingRoomRequestList[index];

                      return MeetingRequestCardForAdmin(item: item);
                    },
                    separatorBuilder: (context, index) =>
                        SizedBox(height: 10 * h),
                  );
      },
    );
  }

  Consumer<BookedMeetingRoomProvider> _topHeadingSection() {
    return Consumer<BookedMeetingRoomProvider>(
      builder: (context, provider, child) {
        return Padding(
          padding: const EdgeInsets.only(left: 15, right: 15, top: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: InkWell(
                  onTap: () {
                    provider.currentIndex = 0;
                    provider.getMeetingRoomRequestForAdmin(status: '1');
                  },
                  child: Container(
                    height: 45,
                    decoration: BoxDecoration(
                        color: provider.currentIndex == 0
                            ? Colors.black
                            : Colors.white,
                        border: Border.all(
                          color: ThemeColors.colorDEE7FF,
                        ),
                        borderRadius: BorderRadius.circular(5)),
                    child: Center(
                      child: Text(
                        'Pending',
                        style:
                            provider.currentIndex == 0 ? tsS14FFFFF : tsS14BN,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: InkWell(
                  onTap: () {
                    provider.currentIndex = 1;
                    provider.getMeetingRoomRequestForAdmin(status: '2');
                  },
                  child: Container(
                    height: 45,
                    decoration: BoxDecoration(
                        color: provider.currentIndex == 1
                            ? Colors.black
                            : Colors.white,
                        border: Border.all(
                          color: ThemeColors.colorDEE7FF,
                        ),
                        borderRadius: BorderRadius.circular(5)),
                    child: Center(
                      child: Text(
                        'Approved',
                        style:
                            provider.currentIndex == 1 ? tsS14FFFFF : tsS14BN,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: InkWell(
                  onTap: () {
                    provider.currentIndex = 2;
                    provider.getMeetingRoomRequestForAdmin(status: '3');
                  },
                  child: Container(
                    height: 45,
                    decoration: BoxDecoration(
                        color: provider.currentIndex == 2
                            ? Colors.black
                            : Colors.white,
                        border: Border.all(
                          color: ThemeColors.colorDEE7FF,
                        ),
                        borderRadius: BorderRadius.circular(5)),
                    child: Center(
                      child: Text(
                        'Rejected',
                        style:
                            provider.currentIndex == 2 ? tsS14FFFFF : tsS14BN,
                      ),
                    ),
                  ),
                ),
              ),
              // const SizedBox(width: 8),
              // Expanded(
              //   child: InkWell(
              //     onTap: () {
              //       setState(() {
              //         index = 3;
              //       });
              //       provider.getMeetingRoomRequestForAdmin(status: '1');
              //     },
              //     child: Container(
              //       height: 45,
              //       decoration: BoxDecoration(
              //           color: index == 3 ? Colors.black : Colors.white,
              //           border: Border.all(
              //             color: ThemeColors.colorDEE7FF,
              //           ),
              //           borderRadius: BorderRadius.circular(5)),
              //       child: Center(
              //         child: Text(
              //           'Expired',
              //           style: index == 3 ? tsS14FFFFF : tsS14BN,
              //         ),
              //       ),
              //     ),
              //   ),
              // ),
            ],
          ),
        );
      },
    );
  }
}
