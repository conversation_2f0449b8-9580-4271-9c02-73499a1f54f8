import 'package:e8_hr_portal/model/login_model.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/meeting_requests/widgets/meeting_request_card.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';
import '../../../../model/booked_room_status_model.dart';
import '../../../../provider/booked_meeting_room_provider.dart';

class MeetingJoinRequestsScreen extends StatelessWidget {
  const MeetingJoinRequestsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Meeting Invite Request',
      body: Consumer<BookedMeetingRoomProvider>(
        builder: (context, provider, child) {
          return provider.isRequestLoading
              ? const Center(child: CircularProgressIndicator())
              : provider.memberMeetingInviteRequestList.isEmpty
                  ? const Center(
                      child: Text(
                        'No meeting requests',
                        style: TextStyle(color: Colors.black, fontSize: 18),
                      ),
                    )
                  : ListView.separated(
                      padding: const EdgeInsets.all(15.0),
                      itemBuilder: (context, index) {
                        BookedRoomStatusModel data =
                            provider.memberMeetingInviteRequestList[index];

                        return MeetingRequestCard(
                            userName: data.bookedBy?.user,
                            userDesignation: data.bookedBy?.designation,
                            status: data.invitedUser
                                ?.firstWhere((element) =>
                                    element.userId == (LoginModel.uid ?? 0))
                                .status,
                            description: data.description,
                            startTime: data.startTime,
                            endTime: data.endTime,
                            id: data.id.toString(),
                            invitedMembersList: data.invitedUser,
                            profileImage: data.bookedBy?.profilePic,
                            roomName: data.room?.name,
                            date: data.bookingDate,
                            roomImage: data.room?.roomImgs?.first.image);
                      },
                      separatorBuilder: (context, index) {
                        return const SizedBox(height: 15);
                      },
                      itemCount:
                          provider.memberMeetingInviteRequestList.length);
        },
      ),
    );
  }
}
