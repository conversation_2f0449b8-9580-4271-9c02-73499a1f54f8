import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/provider/book_meeting_room_provider.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/book_meeting_rooms/enum/booking_type.dart';
import 'package:e8_hr_portal/widgets/dialog_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../../../model/booked_room_status_model.dart';
import '../../../../../provider/booked_meeting_room_provider.dart';
import '../../../../../provider/meeting_room_provider.dart';
import '../../../../../util/colors.dart';
import '../../../../../util/date_formatter.dart';
import '../../../../../util/styles.dart';
import '../../../../../util/validator.dart';
import '../../book_meeting_rooms/book_meeting_screen.dart';
import '../../common_widgets/common_button.dart';
import '../../meeting_attendees_list_screen/meeting_attendees_list_screen_for_admin.dart';
import '../../widgets/status_container.dart';

class MeetingRequestCardForAdmin extends StatelessWidget {
  final BookedRoomStatusModel item;
  MeetingRequestCardForAdmin({super.key, required this.item});
  final formKey = GlobalKey<FormState>();
  final TextEditingController reasonController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    final provider = context.read<BookedMeetingRoomProvider>();
    final bookProvider = context.read<BookMeetingRoomProvider>();
    final meetingProvider = context.read<MeetingRoomProvider>();
    String roomName = item.room?.name ?? '';
    String? userDesignation = item.bookedBy?.designation;
    String? status = item.status;
    String? startTime = item.startTime;
    String? endTime = item.endTime;
    String? description = item.description;
    String? reason = item.reasonForCancel;
    int? id = item.id;
    List<InvitedUser> invitedMembersList = item.invitedUser ?? [];
    String? profilePic = item.bookedBy?.profilePic;
    String? userName = item.bookedBy?.user;
    String? date = item.bookingDate;
    String? image = item.room?.roomImgs?.first.image;
    String? reasonForRejection = item.info?.rejectedReason;
    int? roomID = item.room?.id;
    DateTime? dt = stringToDateTime(date: date!, format: 'yyyy-MM-dd');
    DateTime now = DateTime.now();
    bool? isExpired = dt?.isBefore(DateTime(now.year, now.month, now.day));
    final maxWidth = MediaQuery.of(context).size.width;
    final cardWidth = maxWidth - 30;
    final imageWidth = cardWidth * 0.36;
    final imageHeight = imageWidth + 24;
    final cardHeight = imageHeight + 30;
    final buttonHeight = cardHeight * 0.186;
    // date = '2025-06-03';
    // startTime = '02:59 PM ';
    // final tempStartTime =
    String tempDateAndTime = '$date $startTime';

    tempDateAndTime = formatDateFromString(
        tempDateAndTime, 'yyyy-MM-dd hh:mm a', 'yyyy-MM-dd HH:mm:ss');
    DateTime? tempDateTime = DateTime.tryParse(tempDateAndTime);

    // formatDateFromString(date, 'yyyy-MM-dd', 'outputFormat');
    bool isSlidable = false;
    String tempStatus = 'Pending';
    if (status != null) {
      tempStatus = status;
    }
    switch (tempStatus.toLowerCase()) {
      case 'pending':
        isSlidable = false;
        break;
      case 'approved':
        isSlidable = true;
        break;
      case 'rejected':
        isSlidable = false;
        break;
    }
    if (tempDateTime != null) {
      bool isExpired = now.isAfter(DateTime(
          tempDateTime.year,
          tempDateTime.month,
          tempDateTime.day,
          tempDateTime.hour,
          tempDateTime.minute,
          tempDateTime.second));
      // log('status = ${isExpired} - ${DateTime.now()} - $tempDateTime');
      if (isExpired && isSlidable) {
        isSlidable = false; // if expired, not slidable
      }
    }
    // item.bookingDate
    return
        //  isExpired == true
        //     ? const SizedBox()
        //     :
        Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Slidable(
          enabled: isSlidable,
          key: UniqueKey(),
          endActionPane: ActionPane(
            motion: ScrollMotion(),
            extentRatio: 0.65,
            closeThreshold: 0.1,
            dragDismissible: true,
            openThreshold: 0.2,
            children: [
              SlidableAction(
                autoClose: false,
                onPressed: (context) {
                  if (id == null) return; // ignore: unnecessary_null_awareness
                  DialogHelper.confirmDialogBoxWithTextField(
                    context: context,
                    controller: reasonController,
                    formKey: formKey,
                    hintText: 'Reason for cancelation',
                    description:
                        'Are you sure, do you want to cancel this request ?',
                    onConfirm: () {
                      if (!formKey.currentState!.validate()) return;
                      provider.cancelApprovedMeetingRoom(
                          bookingID: id, reason: reasonController.text);
                      Navigator.pop(context);
                    },
                  );
                },
                // backgroundColor: Color(0xFFFE4A49),
                // foregroundColor: Colors.white,
                icon: Icons.delete,
                label: 'Cancel',
                borderRadius: BorderRadius.circular(10.0),
              ),
              SlidableAction(
                autoClose: false,
                onPressed: (context) {
                  DialogHelper.confirmationDialogBox(
                    context: context,
                    description:
                        'Are you sure, do you want to reschedule this request ?',
                    onConfirm: () async {
                      if (id == null) return;
                      if (roomID == null) return;
                      bookProvider.selectedDate =
                          stringToDateTime(date: date, format: 'yyyy-MM-dd') ??
                              DateTime.now();
                      bookProvider.selectedStartTime =
                          bookProvider.convertStringToTimeOfDay(startTime!);
                      bookProvider.selectedEndTime =
                          bookProvider.convertStringToTimeOfDay(endTime!);
                      bookProvider.allSelectedMembers.clear();
                      bookProvider.alreadyInvitedMembersList =
                          invitedMembersList;
                      bookProvider.removedUsersIdList.clear();
                      meetingProvider.getMeetingRoomDetails(
                          meetingRoomID: (roomID).toString());
                      await Navigator.of(context).push(MaterialPageRoute(
                          builder: (context) => BookMeetingScreen(
                              bookingType: BookingType.reschedule,
                              description: description,
                              bookingID: id)));
                      provider.getBookedMeetingRoomStatus();
                      // provider.currentIndex = 0;
                      provider.getMeetingRoomRequestForAdmin(
                          status: '${provider.currentIndex + 1}');
                    },
                  );
                },
                // backgroundColor: Color(0xFF21B7CA),
                // foregroundColor: Colors.white,
                icon: Icons.schedule,
                label: 'Reschedule',
                borderRadius: BorderRadius.circular(10.0),
              ),
            ],
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.0),
              boxShadow: [
                BoxShadow(
                  blurRadius: 10,
                  offset: const Offset(2, 2),
                  color: ThemeColors.colorDEE7FF,
                ),
              ],
              color: Colors.white,
            ),
            padding: const EdgeInsets.all(10.0),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                        children: [
                          const SizedBox(width: 41),
                          Text(
                            roomName.toString(),
                            style: tsS128391B5,
                          ),
                        ],
                      ),
                      const SizedBox(height: 5),
                      Row(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(25),
                            child: CachedNetworkImage(
                              height: 36,
                              width: 36,
                              imageUrl: profilePic.toString(),
                              fit: BoxFit.cover,
                              errorWidget: (context, url, error) {
                                return Container(
                                    alignment: Alignment.center,
                                    color: ThemeColors.primaryColor,
                                    child: Text(userName!
                                        .substring(0, 1)
                                        .toUpperCase()));
                              },
                            ),
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  userName.toString(),
                                  style: tsS161E2138,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text(
                                    userDesignation ?? '',
                                    style: tsS12grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 5),
                      InkWell(
                        onTap: () async {
                          BookMeetingRoomProvider provider =
                              Provider.of<BookMeetingRoomProvider>(context,
                                  listen: false);
                          provider.alreadyInvitedMembersList =
                              invitedMembersList;
                          provider.removedUsersIdList.clear();
                          provider.allSelectedMembers.clear();
                          BookedMeetingRoomProvider provider2 =
                              Provider.of<BookedMeetingRoomProvider>(context,
                                  listen: false);
                          if (id == null) {
                            return; // ignore: unnecessary_null_awareness
                          }
                          await Navigator.of(context).push(MaterialPageRoute(
                              builder: (context) =>
                                  MeetingInviteesListScreenForAdmin(
                                    isPending:
                                        status == 'Pending' ? true : false,
                                    bookingID: id.toString(),
                                  )));
                          provider2.getMeetingRoomRequestForAdmin(status: '1');
                          provider2.currentIndex = 0;
                        },
                        child: Text(
                          '${invitedMembersList.length} Attendees',
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 14),
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        description ?? '',
                        style: const TextStyle(fontSize: 15),
                      ),
                      const SizedBox(height: 10),
                      FittedBox(
                        child: Row(
                          children: [
                            ImageIcon(
                              const AssetImage('assets/icons/calendar.png'),
                              color: ThemeColors.color7E7E7E,
                            ),
                            const SizedBox(width: 5),
                            Text(
                              '${formatDateFromString(date, 'yyyy-MM-dd', 'dd MMM yyyy')}  $startTime - $endTime',
                              style: tsS12NormalBlack,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 10),
                      if (status == 'Approved')
                        const StatusContainer(status: 'Approved')
                      else if (status == 'Cancelled')
                        const StatusContainer(status: 'Cancelled')
                      else if (status == 'Rejected')
                        const StatusContainer(status: 'Rejected')
                      else if (isExpired == true)
                        const StatusContainer(status: 'Expired')
                      else if (status == 'Pending' && isExpired == false)
                        _buttonRow(
                            item: item,
                            roomName: roomName,
                            bookingID: id.toString(),
                            buttonHeight: buttonHeight),
                      const SizedBox(
                        height: 5,
                      ),
                      if (reason != null && status == 'Cancelled')
                        Text('Reason : $reason')
                      else if (reasonForRejection != null &&
                          status == 'Rejected')
                        Text('Reason for rejection : $reasonForRejection')
                    ],
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    image: DecorationImage(
                      image: CachedNetworkImageProvider(image.toString()),
                      fit: BoxFit.cover,
                    ),
                  ),
                  height: imageHeight,
                  margin: const EdgeInsets.only(left: 10),
                  width: imageWidth,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buttonRow({
    required double buttonHeight,
    required String bookingID,
    required String roomName,
    required BookedRoomStatusModel item,
  }) {
    return Consumer<BookedMeetingRoomProvider>(
      builder: (context, provider, child) {
        return Row(
          children: [
            Expanded(
              child: CommonButton(
                hPadding: 0,
                buttonName: 'Approve',
                style: tsS12wRcWhite,
                height: buttonHeight,
                color: ThemeColors.primaryColor,
                function: () {
                  _confirmDialogBox(
                    buttonName: 'Approve',
                    context: context,
                    keyword: 'approve $roomName',
                    onPressed: () async {
                      await provider.approveOrReject(
                          item: item,
                          context: context,
                          bookingID: bookingID,
                          statusID: '2',
                          reason: reasonController.text);
                      if (!context.mounted) return;
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: CommonButton(
                  hPadding: 0,
                  buttonName: 'Reject',
                  style: GoogleFonts.rubik(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                  ),
                  height: buttonHeight,
                  color: Colors.white,
                  function: () {
                    _confirmDialogBox(
                      buttonName: 'Reject',
                      context: context,
                      keyword: 'reject $roomName',
                      onPressed: () {
                        if (formKey.currentState!.validate()) {
                          Navigator.pop(context);
                          provider.approveOrReject(
                              item: item,
                              context: context,
                              bookingID: bookingID,
                              statusID: '3',
                              reason: reasonController.text);
                        }
                      },
                    );
                  }),
            )
          ],
        );
      },
    );
  }

  _confirmDialogBox(
      {required BuildContext context,
      required String keyword,
      required String buttonName,
      required void Function()? onPressed}) {
    return showDialog(
        context: context,
        builder: (ctxt) {
          return AlertDialog(
            title: Text('Confirm $buttonName'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Are you sure you want to $keyword?'),
                if (buttonName != 'Approve')
                  Form(
                      key: formKey,
                      child: TextFormField(
                        decoration: const InputDecoration(
                            hintText: 'Reason for cancel meeting'),
                        controller: reasonController,
                        validator: (value) => Validator.text(value),
                      ))
              ],
            ),
            actions: [
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[300],
                ),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.black),
                ),
              ),
              ElevatedButton(
                onPressed: onPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ThemeColors.secondaryColor,
                ),
                child: Text(buttonName),
              )
            ],
          );
        });
  }
}

// Row statusCard({required String status}) {
//   return Row(
//     children: [
//       Container(
//         decoration: BoxDecoration(
//             color: status == 'Cancelled' || status == 'Rejected'
//                 ? Colors.red.withOpacity(.7)
//                 : status == 'Expired'
//                     ? Colors.amber.withOpacity(.7)
//                     : Colors.green.withOpacity(.7),
//             borderRadius: BorderRadius.circular(3)),
//         padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
//         child: Text(status),
//       ),
//       const Spacer()
//     ],
//   );
// }
