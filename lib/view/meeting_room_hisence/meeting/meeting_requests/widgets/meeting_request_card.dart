// ignore_for_file: unused_local_variable
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/widgets/status_container.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../../../model/booked_room_status_model.dart';
import '../../../../../provider/booked_meeting_room_provider.dart';
import '../../../../../util/colors.dart';
import '../../../../../util/date_formatter.dart';
import '../../../../../util/styles.dart';
import '../../common_widgets/common_button.dart';
import '../../meeting_attendees_list_screen/meeting_invitees_list_screen.dart';

class MeetingRequestCard extends StatelessWidget {
  final String? status;
  final String? userDesignation;
  final String? description;
  final String? startTime;
  final String? endTime;
  final String? id;
  final String? roomName;
  final String? userName;
  final String? date;
  final String? roomImage;
  final String? profileImage;
  final List<InvitedUser>? invitedMembersList;
  const MeetingRequestCard(
      {super.key,
      required this.userDesignation,
      required this.status,
      required this.description,
      required this.startTime,
      required this.endTime,
      required this.id,
      required this.profileImage,
      required this.roomName,
      required this.userName,
      required this.date,
      required this.roomImage,
      this.invitedMembersList});

  @override
  Widget build(BuildContext context) {
    DateTime? dt = stringToDateTime(date: date!, format: 'yyyy-MM-dd');
    DateTime now = DateTime.now();
    bool? isExpired = dt?.isBefore(DateTime(now.year, now.month, now.day));
    final maxWidth = MediaQuery.of(context).size.width;
    final cardWidth = maxWidth - 30;
    final imageWidth = cardWidth * 0.36;
    final imageHeight = imageWidth + 15;
    final cardHeight = imageHeight + 30;
    final buttonHeight = cardHeight * 0.186;
    final buttonWidth = cardWidth * 0.291;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        boxShadow: [
          BoxShadow(
            blurRadius: 10,
            offset: const Offset(2, 2),
            color: ThemeColors.colorDEE7FF,
          ),
        ],
        color: Colors.white,
      ),
      padding: const EdgeInsets.all(10.0),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  children: [
                    const SizedBox(width: 41),
                    Text(
                      roomName.toString(),
                      overflow: TextOverflow.ellipsis,
                      style: tsS128391B5,
                    )
                  ],
                ),
                const SizedBox(height: 5),
                Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(25),
                      child: CachedNetworkImage(
                        height: 36,
                        width: 36,
                        fit: BoxFit.fill,
                        imageUrl: profileImage.toString(),
                        errorWidget: (context, url, error) {
                          return Container(
                            color: ThemeColors.primaryColor,
                            alignment: Alignment.center,
                            child: Text(userName
                                .toString()
                                .substring(0, 1)
                                .toUpperCase()),
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 5),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            userName.toString(),
                            style: tsS161E2138,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Align(
                            alignment: Alignment.topLeft,
                            child: Text(
                              userDesignation ?? '',
                              style: tsS12grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 5),
                InkWell(
                  onTap: invitedMembersList != null &&
                          invitedMembersList!.isNotEmpty
                      ? () {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (context) => MeetingInviteesListScreen(
                                  invitedMembersList: invitedMembersList)));
                        }
                      : null,
                  child: Text(
                    '${invitedMembersList?.length ?? '0'} Attendees',
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                ),
                const SizedBox(height: 5),
                Text(
                  description ?? '',
                  style: const TextStyle(fontSize: 15),
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    ImageIcon(
                      const AssetImage('assets/icons/calendar.png'),
                      color: ThemeColors.color7E7E7E,
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        '${formatDateFromString(date!, 'yyyy-MM-dd', 'dd MMM yyyy')}  $startTime - $endTime',
                        style: tsS12NormalBlack,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                if (status == 'Approved')
                  const StatusContainer(status: 'Approved')
                else if (status == 'Cancelled')
                  const StatusContainer(status: 'Cancelled')
                else if (status == 'Rejected')
                  const StatusContainer(status: 'Rejected')
                else if (isExpired == true)
                  const StatusContainer(status: 'Expired')
                else if (status == 'Pending' && isExpired == false)
                  _buttonRow(
                      bookingID: id.toString(), buttonHeight: buttonHeight),
              ],
            ),
          ),
          Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                image: DecorationImage(
                  image: CachedNetworkImageProvider(roomImage.toString()),
                  fit: BoxFit.cover,
                ),
              ),
              height: imageHeight,
              margin: const EdgeInsets.only(left: 10),
              width: imageWidth),
        ],
      ),
    );
  }

  Widget _buttonRow({required double buttonHeight, required String bookingID}) {
    return Consumer<BookedMeetingRoomProvider>(
      builder: (context, provider, child) {
        return Row(
          children: [
            Expanded(
              child: CommonButton(
                  hPadding: 0,
                  buttonName: 'Approve',
                  style: tsS12wRcWhite,
                  height: buttonHeight,
                  color: ThemeColors.primaryColor,
                  function: () {
                    _confirmDialogBox(
                      buttonName: 'Approve',
                      context: context,
                      keyword: 'approve $roomName',
                      onPressed: () {
                        Navigator.pop(context);
                        provider.approveOrRejectInviteRequest(
                            context: context,
                            bookingID: bookingID,
                            statusID: '2');
                      },
                    );
                  }),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: CommonButton(
                  hPadding: 0,
                  buttonName: 'Reject',
                  style: GoogleFonts.rubik(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                  ),
                  height: buttonHeight,
                  color: Colors.white,
                  function: () {
                    _confirmDialogBox(
                      buttonName: 'Reject',
                      context: context,
                      keyword: 'reject $roomName',
                      onPressed: () {
                        Navigator.pop(context);
                        provider.approveOrRejectInviteRequest(
                            context: context,
                            bookingID: bookingID,
                            statusID: '3');
                      },
                    );
                  }),
            )
          ],
        );
      },
    );
  }

  _confirmDialogBox(
      {required BuildContext context,
      required String keyword,
      required String buttonName,
      required void Function()? onPressed}) {
    return showDialog(
        context: context,
        builder: (ctxt) {
          return AlertDialog(
            title: Text('Confirm $buttonName'),
            content: Text('Are you sure you want to $keyword?'),
            actions: [
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[300],
                ),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.black),
                ),
              ),
              ElevatedButton(
                onPressed: onPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ThemeColors.secondaryColor,
                ),
                child: Text(buttonName),
              )
            ],
          );
        });
  }
}
