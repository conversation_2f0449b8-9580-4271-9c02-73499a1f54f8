import 'package:e8_hr_portal/model/meeting_room_models/meeting_room_model.dart';
import 'package:e8_hr_portal/provider/booked_meeting_room_provider.dart';
import 'package:e8_hr_portal/provider/meeting_room_provider.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/meeting_rooms/widgets/meeting_card.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/meeting_rooms/widgets/room_availability_bottom_sheet.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';
import '../../../../helper/button_widget.dart';
import '../../../../model/login_model.dart';
import '../../../../provider/book_meeting_room_provider.dart';
import '../../../../util/colors.dart';
import '../../../../util/page_navigator.dart';
import '../../../../util/styles.dart';
import '../book_meeting_rooms/book_meeting_screen.dart';
import '../meeting_requests/meeting_join_requests_screen.dart';
import '../meeting_requests/meeting_request_screen.dart';
import '../meeting_room_booked_status/meeting_room_booked_status_screen.dart';
import '../new_meeting_room/add_new_meeting_room.dart';

class MeetingRoomScreen extends StatelessWidget {
  const MeetingRoomScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<MeetingRoomProvider, BookedMeetingRoomProvider>(
      builder: (context, provider, provider2, child) {
        return HisenseScaffold(
            actions: [
              PopupMenuButton(
                onSelected: (value) {
                  switch (value) {
                    case 'new':
                      provider.selectedMeetingRoomImages.clear();
                      provider.selectedAmenity = null;
                      provider.selectedAmenitiesList.clear();
                      provider.selectedUserPolicy = null;
                      provider.alreadySelectedImagesList.clear();
                      PageNavigator.push(
                        context: context,
                        route: const AddNewMeetingRoomScreen(),
                      );
                      break;
                    case 'invite':
                      provider2.getMeetingInviteRequestForMember();

                      PageNavigator.push(
                        context: context,
                        route: const MeetingJoinRequestsScreen(),
                      );
                      break;
                    case 'status':
                      provider2.getBookedMeetingRoomStatus();
                      PageNavigator.push(
                        context: context,
                        route: MeetingRoomBookedStatusScreen(),
                      );
                      break;
                    case 'requests':
                      provider2.getMeetingRoomRequestForAdmin(status: '1');
                      provider2.currentIndex = 0;
                      PageNavigator.push(
                        context: context,
                        route: const MeetingRequestsScreen(),
                      );
                      break;
                  }
                },
                icon: const Icon(Icons.more_vert),
                itemBuilder: (context) {
                  return [
                    if (LoginModel.meetingRoomPermission == true)
                      const PopupMenuItem(
                        value: 'new',
                        child: Text('New Meeting Room'),
                      ),
                    const PopupMenuItem(
                      value: 'invite',
                      child: Text('Meeting Invite Requests'),
                    ),
                    const PopupMenuItem(
                      value: 'status',
                      child: Text('Meeting Room Booked Status'),
                    ),
                    if (LoginModel.meetingRoomPermission == true)
                      const PopupMenuItem(
                        value: 'requests',
                        child: Text('Meeting Requests'),
                      ),
                  ];
                },
              )
            ],
            screenTitle: 'Meeting Rooms',
            body: provider.isMeetingRoomLoading
                ? const Center(child: CircularProgressIndicator())
                : provider.meetingRoomsList.isEmpty
                    ? const Center(
                        child: Text(
                          'No meeting rooms',
                          style: TextStyle(color: Colors.black, fontSize: 18),
                        ),
                      )
                    : Column(
                        children: [
                          Expanded(
                              child: ListView.separated(
                            padding: const EdgeInsets.all(15.0),
                            physics: const BouncingScrollPhysics(),
                            itemCount: provider.meetingRoomsList.length,
                            itemBuilder: (BuildContext context, int index) {
                              MeetingRoomModel meetingRoom =
                                  provider.meetingRoomsList[index];
                              return MeetingRoomCards(item: meetingRoom);
                            },
                            separatorBuilder:
                                (BuildContext context, int index) {
                              return const SizedBox(height: 10);
                            },
                          )),
                          _checkAvailabilityButton(context)
                        ],
                      ));
      },
    );
  }

  Padding _checkAvailabilityButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(15.0, 20.0, 15.0, 30.0),
      child: ButtonWidget(
        onPressed: () {
          BookMeetingRoomProvider provider =
              Provider.of<BookMeetingRoomProvider>(context, listen: false);

          callMeetingFunction(context);
          provider.selectedRoomForCheckingAvailability = null;
          provider.roomIsAvailable = false;
          provider.roomAvailabilityCheckResponse = null;
          descriptionController.clear();
          showModalBottomSheet<void>(
            context: context,
            useSafeArea: true,
            isDismissible: true,
            isScrollControlled: true,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(35.0),
              ),
            ),
            builder: (BuildContext context) {
              return Padding(
                padding: MediaQuery.of(context).viewInsets,
                child: const RoomAvailabilityBottomSheet(),
              );
            },
          );
        },
        title: 'Check Room Availability',
        color: ThemeColors.secondaryColor,
        textStyle: tsS16FFFFF,
      ),
    );
  }
}

void callMeetingFunction(BuildContext context) {
  BookMeetingRoomProvider bookMeetingRoomProvider =
      Provider.of(context, listen: false);
  bookMeetingRoomProvider.alreadyInvitedMembersList = null;
  bookMeetingRoomProvider.allSelectedMembers.clear();
  bookMeetingRoomProvider.selectedDate = DateTime.now();
  bookMeetingRoomProvider.selectedStartTime = TimeOfDay.now();
  TimeOfDay currentTime = TimeOfDay.now();
  //rounding the time
  if (currentTime.minute > 0 && currentTime.minute < 15) {
    bookMeetingRoomProvider.selectedStartTime =
        TimeOfDay(hour: currentTime.hour, minute: 15);
  } else if (currentTime.minute > 15 && currentTime.minute < 30) {
    bookMeetingRoomProvider.selectedStartTime =
        TimeOfDay(hour: currentTime.hour, minute: 30);
  } else if (currentTime.minute > 30 && currentTime.minute < 45) {
    bookMeetingRoomProvider.selectedStartTime =
        TimeOfDay(hour: currentTime.hour, minute: 45);
  } else {
    bookMeetingRoomProvider.selectedStartTime =
        TimeOfDay(hour: currentTime.hour + 1, minute: 0);
  }

  //
  // bookMeetingRoomProvider.selectedEndTime =
  //     bookMeetingRoomProvider.selectedStartTime.add(hour: 1);

  DateTime currentDateTime = DateTime.now();
  DateTime newDateTime = DateTime(
    currentDateTime.year,
    currentDateTime.month,
    currentDateTime.day,
    bookMeetingRoomProvider.selectedStartTime.hour,
    bookMeetingRoomProvider.selectedStartTime.minute,
  ).add(const Duration(minutes: 30));
  bookMeetingRoomProvider.selectedEndTime = TimeOfDay.fromDateTime(newDateTime);
}
