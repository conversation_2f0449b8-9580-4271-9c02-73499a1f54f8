// ignore_for_file: avoid_print, use_build_context_synchronously
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/provider/book_meeting_room_provider.dart';
import 'package:e8_hr_portal/provider/meeting_room_provider.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:provider/provider.dart';
// import 'package:time_picker_widget/time_picker_widget.dart';

import '../../../../../model/meeting_room_models/meeting_room_model.dart';
import '../../../../../util/dailoge.dart';
import '../../../../../util/validator.dart';
import '../../../../profile/widgets/profile_textfield_widget.dart';
import '../../book_meeting_rooms/book_meeting_screen.dart';

final TextEditingController descriptionController = TextEditingController();
final _formKey = GlobalKey<FormState>();

class RoomAvailabilityBottomSheet extends StatelessWidget {
  const RoomAvailabilityBottomSheet({super.key});
  final kHeight40 = const SizedBox(height: 40);
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(35.0)),
        color: Colors.white,
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: 15,
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            kHeight40,
            Align(
              alignment: Alignment.center,
              child: Text('Check Room Availability', style: tsS181E2138),
            ),
            _dateAndTimeSelectingSection(0),
            _availableRoomsSection(),
            Consumer<BookMeetingRoomProvider>(
              builder: (context, provider, child) {
                return provider.roomIsAvailable == false
                    ? Container()
                    : Form(
                        autovalidateMode: AutovalidateMode.onUserInteraction,
                        key: _formKey,
                        child: ProfileTextFieldWidget(
                          controller: descriptionController,
                          label: RichText(
                              text: TextSpan(
                                  text: 'Meeting Description',
                                  style: GoogleFonts.rubik(
                                    color: ThemeColors.titleColor,
                                  ),
                                  children: [
                                TextSpan(
                                    text: '*',
                                    style: GoogleFonts.rubik(color: Colors.red))
                              ])),
                          validator: Validator.text,
                          // focusBorder: const UnderlineInputBorder(
                          //     borderSide: BorderSide(color: Colors.grey)),
                        ),
                      );
              },
            ),
            _invitingMembersSection(context),
            _checkAvailabilityButton(),
            kHeight40,
          ],
        ),
      ),
    );
  }

  Widget _checkAvailabilityButton() {
    return Consumer<BookMeetingRoomProvider>(
      builder: (context, provider, child) {
        return provider.isCheckAvailabilityLoading
            ? const Center(child: CircularProgressIndicator.adaptive())
            : ButtonWidget(
                onPressed: () async {
                  if (provider.selectedRoomForCheckingAvailability == null) {
                    showToastText('Please select a meeting room');
                  } else {
                    if (provider.roomIsAvailable == false) {
                      provider.checkAvailability(context: context);
                    } else {
                      if (_formKey.currentState!.validate()) {
                        _confirmationDialog(context, provider);
                      }
                    }
                  }
                },
                title: provider.roomIsAvailable == false
                    ? 'Check Availability'
                    : 'Book Now',
                color: ThemeColors.secondaryColor,
                textStyle: GoogleFonts.rubik(
                    fontSize: 16, fontWeight: FontWeight.w500),
              );
      },
    );
  }

  Future<dynamic> _confirmationDialog(
      BuildContext context, BookMeetingRoomProvider provider) {
    Duration timeDifference = provider.calculateTimeDifference(
        provider.selectedStartTime, provider.selectedEndTime);
    String hour = '${timeDifference.inHours}';
    String minutes = '${timeDifference.inMinutes.remainder(60)}';
    return showDialog(
        context: context,
        builder: (ctxt) {
          return AlertDialog(
            title: const Text('Confirm Booking'),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Meeting Room : ${provider.selectedRoomForCheckingAvailability?.name}',
                  style: TextStyle(color: ThemeColors.secondaryColor),
                ),
                Text(
                  'Booking Time : ${provider.formatTimeOfDayFormat(context: context, time: provider.selectedStartTime)} - ${provider.formatTimeOfDayFormat(context: context, time: provider.selectedEndTime)}',
                  style: TextStyle(color: ThemeColors.secondaryColor),
                ),
                Text(
                  minutes == '0'
                      ? 'Duration : $hour Hour'
                      : 'Duration : $hour Hour $minutes minutes',
                  style: TextStyle(color: ThemeColors.secondaryColor),
                ),
                const SizedBox(
                  height: 10,
                ),
                const Text('Are you sure you want to continue?'),
              ],
            ),
            actions: [
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[300],
                ),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.black),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);

                  provider.bookMeetingRoom(
                      meetingRoomID: provider
                          .selectedRoomForCheckingAvailability!.id
                          .toString(),
                      description: descriptionController.text,
                      context: context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: ThemeColors.secondaryColor,
                ),
                child: const Text('Book'),
              )
            ],
          );
        });
  }

  _invitingMembersSection(BuildContext context) {
    double maxHeight = MediaQuery.of(context).size.height;
    double maxWidth = MediaQuery.of(context).size.width;
    return Consumer<BookMeetingRoomProvider>(
      builder: (context, provider, child) {
        return Column(
          children: [
            const SizedBox(height: 25),
            if (provider.roomIsAvailable)
              InkWell(
                onTap: () {
                  FocusScope.of(context).unfocus();
                  provider.textFieldCleared();
                  showEmployeeSelectList(context);
                },
                child: Container(
                  height: maxHeight * .049,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: const Color(0xff8391B5))),
                  child: Row(
                    children: [
                      SizedBox(
                        width: maxWidth * .018,
                      ),
                      Text(
                        'Invite Your Colleagues',
                        style: GoogleFonts.inter(
                            fontSize: 12, fontWeight: FontWeight.w500),
                      ),
                      const Spacer(),
                      const ImageIcon(AssetImage('assets/icons/expand.png')),
                      SizedBox(
                        width: maxWidth * .018,
                      ),
                    ],
                  ),
                ),
              ),
            if (provider.allSelectedMembers.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 10),
                  Text(
                    'Invited team members',
                    style: GoogleFonts.rubik(
                      color: ThemeColors.color8391B5,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 10),
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const BouncingScrollPhysics(),
                    itemBuilder: (context, index) {
                      final datas = provider.allSelectedMembers[index];

                      return Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: ThemeColors.colorDEE7FF,
                          ),
                          borderRadius: BorderRadius.circular(5.0),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 5,
                        ),
                        child: Row(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(35),
                              child: CachedNetworkImage(
                                height: 23,
                                width: 23,
                                fit: BoxFit.cover,
                                imageUrl: datas.profilePic.toString(),
                                errorWidget: (context, url, error) {
                                  return Container(
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        color: ThemeColors.primaryColor,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Center(
                                        child: (Text(datas.name
                                            .toString()
                                            .substring(0, 1)
                                            .toUpperCase())),
                                      ));
                                },
                              ),
                            ),
                            const SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                datas.name.toString(),
                                style: tsS12NormalBlack,
                              ),
                            ),
                            GestureDetector(
                              onTap: () => provider.addOrRemoveMembers(datas),
                              child: Icon(
                                Icons.remove_circle_outline,
                                color: ThemeColors.secondaryColor,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                    separatorBuilder: (context, index) =>
                        const SizedBox(height: 2),
                    itemCount: provider.allSelectedMembers.length,
                  ),
                ],
              ),
            const SizedBox(height: 30),
          ],
        );
      },
    );
  }

  _availableRoomsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 10),
        Consumer2<BookMeetingRoomProvider, MeetingRoomProvider>(
          builder: (context, provider1, provider2, child) {
            return DropdownButtonFormField<MeetingRoomModel>(
              items: provider2.meetingRoomsList
                  .map(
                    (e) => DropdownMenuItem(
                      value: e,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(e.name.toString()),
                          Container(
                            height: 34,
                            width: 34,
                            decoration: BoxDecoration(
                                image: DecorationImage(
                                    fit: BoxFit.cover,
                                    image: NetworkImage(
                                        e.roomImgs!.first.toString()))),
                          )
                        ],
                      ),
                    ),
                  )
                  .toList(),
              value: provider1.selectedRoomForCheckingAvailability,
              onChanged: (MeetingRoomModel? meetingRoom) {
                provider1.roomAvailabilityCheckResponse = null;
                provider1.roomIsAvailable = false;
                provider1.selectedRoomForCheckingAvailability = meetingRoom;
              },
              decoration: InputDecoration(
                labelText: 'Available Rooms',
                labelStyle: GoogleFonts.rubik(
                  color: ThemeColors.color8391B5,
                ),
              ),
            );
          },
        ),
        const SizedBox(height: 10),
        Consumer<BookMeetingRoomProvider>(
          builder: (context, provider, child) {
            return provider.selectedRoomForCheckingAvailability == null
                ? Container()
                : SizedBox(
                    height: 100,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: provider.selectedRoomForCheckingAvailability
                          ?.roomImgs?.length,
                      itemBuilder: (context, index) {
                        String? image = provider
                            .selectedRoomForCheckingAvailability
                            ?.roomImgs?[index]
                            .image;

                        return Container(
                          margin: const EdgeInsets.symmetric(horizontal: 5),
                          width: 100,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              image: DecorationImage(
                                  fit: BoxFit.fill,
                                  image: NetworkImage(image.toString()))),
                        );
                      },
                    ),
                  );
          },
        ),
      ],
    );
  }

  Widget _dateAndTimeSelectingSection(int? meetingRoomID) {
    return Column(
      children: [
        const SizedBox(height: 25),
        Align(
          alignment: Alignment.topLeft,
          child: Text('Select Meeting Date & Time', style: tsS14c8391B5),
        ),
        const SizedBox(height: 10),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Consumer<BookMeetingRoomProvider>(
                builder: (context, provider, child) {
                  String selectedDate = formatDateFromDate(
                      dateTime: provider.selectedDate, format: 'dd-MM-yyyy');
                  return GestureDetector(
                    onTap: () => selectBookingDate(
                      roomId: meetingRoomID.toString(),
                      context: context,
                      provider: provider,
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: ThemeColors.colorDEE7FF,
                        ),
                        borderRadius: BorderRadius.circular(5.0),
                      ),
                      padding: const EdgeInsets.all(10.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          const ImageIcon(
                              AssetImage('assets/icons/calendar.png'),
                              color: Colors.black
                              //  ThemeColors.color1E2138,
                              ),
                          //    const SizedBox(width: 15),
                          Text(
                            selectedDate,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              flex: 3,
              child: Consumer<BookMeetingRoomProvider>(
                builder: (context, provider, _) {
                  // String selectedBookinStartTime = formatDateFromDate(
                  //   dateTime: provider.selectedBookingStartTime,
                  //   format: 'hh:mma',
                  // );
                  // String selectedBookingEndTime = formatDateFromDate(
                  //   dateTime: provider.selectedBookingEndTime,
                  //   format: 'hh:mma',
                  // );
                  return GestureDetector(
                    onTap: () => selectBookingTimeRange(
                        context: context,
                        provider: provider,
                        roomId: meetingRoomID.toString()),
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: ThemeColors.colorDEE7FF,
                        ),
                        borderRadius: BorderRadius.circular(5.0),
                      ),
                      padding: const EdgeInsets.all(10.0),
                      child: Row(
                        children: [
                          const ImageIcon(AssetImage('assets/icons/clock.png'),
                              color: Colors.black),
                          const SizedBox(width: 15),
                          Text(
                            '${provider.formatTimeOfDayFormat(context: context, time: provider.selectedStartTime)} - ${provider.formatTimeOfDayFormat(context: context, time: provider.selectedEndTime)}',
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 5),
        Consumer<BookMeetingRoomProvider>(
          builder: (context, provider, child) {
            return provider.roomAvailabilityCheckResponse == null
                ? const SizedBox()
                : Align(
                    alignment: Alignment.topLeft,
                    child: Text(
                      '* ${provider.roomAvailabilityCheckResponse} . Please change your meeting schedule.',
                      textAlign: TextAlign.justify,
                      style: GoogleFonts.rubik(fontSize: 12, color: Colors.red),
                    ),
                  );
          },
        ),
        const SizedBox(height: 5)
      ],
    );
  }

  Future<void> selectBookingDate({
    required String? roomId,
    required BuildContext context,
    required BookMeetingRoomProvider provider,
  }) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: provider.selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime(2050),
    );
    if (picked != null) {
      provider.roomAvailabilityCheckResponse = null;
      provider.roomIsAvailable = false;
      provider.selectedDate = picked;

      ////////////////////
      // TimeOfDay d = provider.selectedStartTime;
      // String startTime = "${d.hour}:${d.minute}";
      // TimeOfDay e = provider.selectedEndTime;
      // String endTime = "${e.hour}:${e.minute}";
      // final DateFormat formatter = DateFormat('yyyy-MM-dd');
      // final String formatted = formatter.format(provider.selectedDate);

      // provider.checkRoomAvailability(
      //     date: formatted,
      //     startTime: startTime,
      //     endTime: endTime,
      //     idOfRoom: roomId);
//      provider.checkAvailableMeetingRooms(widget.meetingRoom.id);
    }
  }

  Future<void> selectBookingTimeRange(
      {required BuildContext context,
      required BookMeetingRoomProvider provider,
      required String? roomId}) async {
    TimeOfDay? startTime = await showTimePicker(
      initialEntryMode: TimePickerEntryMode.dialOnly,
      context: context,
      initialTime: provider.selectedStartTime,
      helpText: 'SELECT START TIME',
    );
    if (startTime != null) {
      provider.roomAvailabilityCheckResponse = null;
      provider.roomIsAvailable = false;
      if (provider.isValidStartTime(startTime)) {
        provider.selectedStartTime = startTime;
        provider.selectedEndTime = startTime.add(hour: 1);
        selectBookingEndTime(
            context: context, provider: provider, roomId: roomId);
      } else {
        showToastText('Please select a valid start time');
      }
    }
    // final TimeOfDay? startTime = await showCustomTimePicker(
    //   context: context,
    //   initialTime: provider.selectedStartTime,
    //   onFailValidation: (context) => print('Unavailable selection'),
    //   helpText: 'SELECT START TIME',
    //   //   selectableTimePredicate: (time) => time!.minute % 15 == 0,
    // );
    // if (startTime != null) {
    //   if (provider.isValidStartTime(startTime)) {
    //     provider.selectedStartTime = startTime;
    //     provider.selectedBookingStartTime =
    //         provider.bookingTimetoDateTime(startTime);
    //     provider.selectedBookingEndTime =
    //         provider.selectedBookingStartTime.add(const Duration(hours: 1));
    //     provider.selectedEndTime = TimeOfDay(
    //       hour: provider.selectedBookingEndTime.hour,
    //       minute: provider.selectedBookingEndTime.minute,
    //     );
    //     selectBookingEndTime(
    //         context: context, provider: provider, roomId: roomId);
    //   } else {
    //     showToastText('Please select a valid start time');
    //   }
    // }
  }

  Future<void> selectBookingEndTime(
      {required BuildContext context,
      required BookMeetingRoomProvider provider,
      required String? roomId}) async {
    final TimeOfDay? endTime = await showTimePicker(
      initialEntryMode: TimePickerEntryMode.dialOnly,
      context: context,
      initialTime: provider.selectedEndTime,
      helpText: 'SELECT END TIME',
      // selectableTimePredicate: (time) => time!.minute % 15 == 0,
    );
    if (endTime != null) {
      if (provider.isValidEndTime(endTime)) {
        provider.selectedEndTime = endTime;
        // provider.selectedBookingEndTime =
        //     provider.bookingTimetoDateTime(endTime);
      } else {
        showToastText('Please select a valid end time');
      }
    }
  }
}
