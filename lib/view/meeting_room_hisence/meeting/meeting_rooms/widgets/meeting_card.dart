import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/amenity_model.dart';
import 'package:e8_hr_portal/provider/meeting_room_provider.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/book_meeting_rooms/enum/booking_type.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:provider/provider.dart';
import '../../../../../model/login_model.dart';
import '../../../../../model/meeting_room_models/meeting_room_model.dart';
import '../../../../../util/page_navigator.dart';
import '../../../../../util/size_config.dart';
import '../../book_meeting_rooms/book_meeting_screen.dart';
import '../../common_widgets/common_button.dart';
import '../../new_meeting_room/add_new_meeting_room.dart';
import '../meeting_rooms.dart';

class MeetingRoomCards extends StatelessWidget {
  final MeetingRoomModel item;
  const MeetingRoomCards({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    final maxWidth = MediaQuery.of(context).size.width;
    final cardWidth = maxWidth - 30;
    final imageWidth = cardWidth * 0.36;
    final imageHeight = imageWidth - 1;
    final cardHeight = imageHeight + 30;
    final buttonHeight = cardHeight * 0.186;
    final buttonWidth = cardWidth * 0.291;
    MeetingRoomProvider provider =
        Provider.of<MeetingRoomProvider>(context, listen: false);
    String id = item.id.toString();
    // bool isAvailabe = item.availability ?? false;
    String name = item.name ?? '';
    int seatCount = item.seatCount ?? 0;
    List<RoomImgs> roomImgs = item.roomImgs ?? [];
    List<AmenityModel> amenities = item.amenities ?? [];
    int policyId = item.policy ?? 0;
    String roomImage = '';
    if (roomImgs.isNotEmpty) {
      roomImage =
          roomImgs.firstWhere((element) => element.image != null).image ?? '';
    }
    return InkWell(
      onTap: () {
        callMeetingFunction(context);
        if (item.id != null) {
          provider.getMeetingRoomDetails(meetingRoomID: item.id.toString());
          PageNavigator.pushSlideRight(
              context: context,
              route: BookMeetingScreen(bookingType: BookingType.booking));
        }
      },
      child: Slidable(
        closeOnScroll: true,
        enabled: LoginModel.meetingRoomPermission == true ? true : false,
        endActionPane: ActionPane(
          motion: const ScrollMotion(),
          children: [
            Expanded(
              child: Container(
                decoration: const BoxDecoration(color: Colors.white),
                alignment: Alignment.center,
                child: IconButton(
                    onPressed: () {
                      showDialog(
                          context: context,
                          builder: (ctxt) {
                            return AlertDialog(
                              title: const Text('Confirm edit'),
                              content:
                                  Text('Are you sure you want to edit $name?'),
                              actions: [
                                ElevatedButton(
                                  onPressed: () => Navigator.pop(context),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.grey[300],
                                  ),
                                  child: const Text(
                                    'Cancel',
                                    style: TextStyle(color: Colors.black),
                                  ),
                                ),
                                ElevatedButton(
                                  onPressed: () async {
                                    provider.selectedAmenitiesList = amenities;
                                    provider.selectedAmenity = provider
                                        .amenitiesList
                                        .firstWhere((element) =>
                                            element.id == amenities.last.id);
                                    provider.selectedUserPolicy = provider
                                        .policiesList
                                        .firstWhere((e) => e.id == policyId);
                                    provider.alreadySelectedImagesList =
                                        roomImgs;
                                    provider.removedImagesIdList.clear();
                                    provider.selectedMeetingRoomImages.clear();
                                    Navigator.pop(context);
                                    PageNavigator.push(
                                      context: context,
                                      route: AddNewMeetingRoomScreen(
                                          meetingRoomName: name,
                                          seatCount: seatCount.toString(),
                                          meetingRoomID: id),
                                    );
                                    provider.getMeetingRooms();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: ThemeColors.secondaryColor,
                                  ),
                                  child: const Text('Edit'),
                                )
                              ],
                            );
                          });
                    },
                    icon: const Icon(Icons.edit)),
              ),
            ),
            Expanded(
              child: Container(
                decoration: const BoxDecoration(color: Colors.white),
                alignment: Alignment.center,
                child: IconButton(
                    onPressed: () {
                      showDialog(
                          context: context,
                          builder: (ctxt) {
                            return AlertDialog(
                              title: const Text('Confirm delete'),
                              content: Text(
                                  'Are you sure you want to delete $name?'),
                              actions: [
                                ElevatedButton(
                                  onPressed: () => Navigator.pop(context),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.grey[300],
                                  ),
                                  child: const Text(
                                    'Cancel',
                                    style: TextStyle(color: Colors.black),
                                  ),
                                ),
                                ElevatedButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                    provider.deleteMeetingRoom(
                                        context: context,
                                        roomID: id.toString());
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: ThemeColors.secondaryColor,
                                  ),
                                  child: const Text('Delete'),
                                )
                              ],
                            );
                          });
                    },
                    icon: const Icon(Icons.delete)),
              ),
            ),
          ],
        ),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.0),
            boxShadow: [
              BoxShadow(
                blurRadius: 10,
                offset: const Offset(2, 2),
                color: ThemeColors.colorDEE7FF,
              ),
            ],
          ),
          padding: const EdgeInsets.all(10.0),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Align(
                      alignment: Alignment.topLeft,
                      child: Text(
                        name.toString(),
                        overflow: TextOverflow.ellipsis,
                        style: tsS161E2138,
                      ),
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 3),
                          child: SizedBox(
                            height: cardHeight * 0.096,
                            width: cardWidth * 0.041,
                            child: const ImageIcon(
                              AssetImage('assets/icons/seat.png'),
                              color: Color(0xFFB5B5B5),
                            ),
                          ),
                        ),
                        const SizedBox(width: 5),
                        Padding(
                          padding: const EdgeInsets.only(top: 3),
                          child: Text(
                            '$seatCount Seats',
                            style: GoogleFonts.rubik(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xff7E7E7E),
                            ),
                          ),
                        ),
                        Row(
                          children: amenities.map(
                            (e) {
                              return Padding(
                                padding: const EdgeInsets.only(left: 8.0),
                                child: CachedNetworkImage(
                                  height: cardHeight * 0.096,
                                  width: cardWidth * 0.041,
                                  imageUrl: e.icon.toString(),
                                  color: const Color(0xff7E7E7E),
                                  errorWidget: (context, url, error) {
                                    return const SizedBox();
                                  },
                                ),
                              );
                            },
                          ).toList(),
                        ),
                      ],
                    ),
                    SizedBox(height: h * 50),
                    CommonButton(
                      hPadding: 0,
                      buttonName: 'Book Now',
                      style: tsS12wRcWhite,
                      height: buttonHeight,
                      width: buttonWidth,
                      color: ThemeColors.secondaryColor,
                      function: () async {
                        callMeetingFunction(context);
                        if (id.isNotEmpty) {
                          provider.getMeetingRoomDetails(meetingRoomID: id);
                          PageNavigator.pushSlideRight(
                              context: context,
                              route: const BookMeetingScreen(
                                  bookingType: BookingType.booking));
                        }
                      },
                    )
                  ],
                ),
              ),
              ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: CachedNetworkImage(
                  fit: BoxFit.cover,
                  height: imageHeight,
                  width: imageWidth,
                  imageUrl: roomImage,
                  placeholder: (context, url) {
                    return Center(child: CupertinoActivityIndicator());
                  },
                  errorWidget: (context, url, error) {
                    return const SizedBox.shrink();
                  },
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
