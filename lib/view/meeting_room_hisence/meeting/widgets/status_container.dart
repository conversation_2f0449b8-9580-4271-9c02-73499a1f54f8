import 'package:flutter/material.dart';
import '../../../../util/colors.dart';
import '../../../../util/styles.dart';

class StatusContainer extends StatelessWidget {
  final String status;
  const StatusContainer({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    TextStyle? style;
    Color? color;
    switch (status) {
      case 'Approved':
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
        break;
      case 'Rejected':
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case 'Pending':
        {
          style = tsS12w600cE5B900;
          color = ThemeColors.colorFFF2E2;
        }
        break;

      case 'Cancelled':
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case 'Expired':
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      default:
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
    }
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: color,
        ),
        child: Text(
          status,
          style: style,
        ),
      ),
    );
  }
}
