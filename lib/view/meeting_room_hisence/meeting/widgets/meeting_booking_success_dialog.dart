import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import '../../../../util/colors.dart';
import '../../../../util/size_config.dart';

class MeetingBookingSuccessDialog extends StatelessWidget {
  final String message;
  const MeetingBookingSuccessDialog({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: w * 50),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(26),
      ),
      elevation: 0.0,
      backgroundColor: ThemeColors.colorFFFFFF,
      child: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Lottie.network(
                height: 150 * h,
                width: 300 * w,
                repeat: false,
                'https://lottie.host/f30e962d-242c-45e4-9f8c-c38353dc9424/nok09zYh6K.json'),
            Text(message,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 20)),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('OK'),
            ),
          ],
        ),
      ),
    );
  }
}
