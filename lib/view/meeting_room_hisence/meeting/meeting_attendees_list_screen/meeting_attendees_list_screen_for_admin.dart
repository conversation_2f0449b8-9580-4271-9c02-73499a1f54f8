import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/provider/book_meeting_room_provider.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../../helper/button_widget.dart';
import '../../../../model/booked_room_status_model.dart';
import '../../../../model/employee_model.dart';
import '../../../../util/colors.dart';
import '../book_meeting_rooms/book_meeting_screen.dart';

class MeetingInviteesListScreenForAdmin extends StatelessWidget {
  final String? bookingID;
  final bool isPending;
  const MeetingInviteesListScreenForAdmin(
      {super.key, required this.isPending, required this.bookingID});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        actions: isPending ? [_addMembersButton()] : [],
        screenTitle: 'Meeting Attendees List',
        body: Consumer<BookMeetingRoomProvider>(
          builder: (context, provider, child) {
            return (provider.alreadyInvitedMembersList != null &&
                        provider.alreadyInvitedMembersList!.isEmpty) &&
                    provider.allSelectedMembers.isEmpty
                ? const Center(
                    child: Text(
                      'No Attendees',
                      style: TextStyle(fontSize: 18),
                    ),
                  )
                : Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              if (provider.alreadyInvitedMembersList != null &&
                                  provider
                                      .alreadyInvitedMembersList!.isNotEmpty)
                                ListView.builder(
                                  padding: EdgeInsets.zero,
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: provider
                                      .alreadyInvitedMembersList?.length,
                                  itemBuilder: (context, index) {
                                    InvitedUser? member = provider
                                        .alreadyInvitedMembersList?[index];
                                    return ListTile(
                                        leading: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(50),
                                          child: CachedNetworkImage(
                                            height: 50,
                                            width: 50,
                                            fit: BoxFit.cover,
                                            imageUrl: member?.profilePic ?? '',
                                            errorWidget: (context, url, error) {
                                              return Container(
                                                height: 50,
                                                width: 50,
                                                decoration: BoxDecoration(
                                                  color:
                                                      ThemeColors.primaryColor,
                                                  shape: BoxShape.circle,
                                                ),
                                                alignment: Alignment.center,
                                                child: Text(
                                                  member?.user
                                                          ?.substring(0, 1)
                                                          .toUpperCase() ??
                                                      '',
                                                  style: GoogleFonts.rubik(
                                                    fontSize: 22,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                        title: Text(member?.user ?? ''),
                                        subtitle:
                                            Text(member?.designation ?? ''),
                                        trailing: isPending
                                            ? IconButton(
                                                onPressed: () {
                                                  provider.removeInvitedMemeber(
                                                      member!.userId!);
                                                },
                                                icon: const Icon(
                                                  Icons.remove_circle_outline,
                                                  color: Colors.red,
                                                ))
                                            : null);
                                  },
                                ),
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: provider.allSelectedMembers.length,
                                itemBuilder: (context, index) {
                                  EmployeeModel? member =
                                      provider.allSelectedMembers[index];
                                  return ListTile(
                                      leading: ClipRRect(
                                        borderRadius: BorderRadius.circular(50),
                                        child: CachedNetworkImage(
                                          height: 50,
                                          width: 50,
                                          fit: BoxFit.cover,
                                          imageUrl: member.profilePic ?? '',
                                          errorWidget: (context, url, error) {
                                            return Container(
                                              height: 50,
                                              width: 50,
                                              decoration: BoxDecoration(
                                                color: ThemeColors.primaryColor,
                                                shape: BoxShape.circle,
                                              ),
                                              alignment: Alignment.center,
                                              child: Text(
                                                member.name
                                                        ?.substring(0, 1)
                                                        .toUpperCase() ??
                                                    '',
                                                style: GoogleFonts.rubik(
                                                  fontSize: 22,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                      title: Text(member.name ?? ''),
                                      subtitle: Text(
                                          member.designation?.first.name ?? ''),
                                      trailing: isPending
                                          ? IconButton(
                                              onPressed: () {
                                                provider
                                                    .addOrRemoveMembers(member);
                                              },
                                              icon: const Icon(
                                                Icons.remove_circle_outline,
                                                color: Colors.red,
                                              ))
                                          : null);
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (isPending && provider.allSelectedMembers.isNotEmpty ||
                          provider.removedUsersIdList.isNotEmpty)
                        Padding(
                          padding:
                              const EdgeInsets.fromLTRB(15.0, 20.0, 15.0, 30.0),
                          child: provider.isEditLoading
                              ? const Center(
                                  child: CircularProgressIndicator.adaptive(),
                                )
                              : ButtonWidget(
                                  onPressed: () async {
                                    if (bookingID != null) {
                                      showDialog(
                                          context: context,
                                          builder: (ctxt) {
                                            return AlertDialog(
                                              title: const Text('Confirm edit'),
                                              content: const Text(
                                                  'Are you sure want to edit ?'),
                                              actions: [
                                                ElevatedButton(
                                                  onPressed: () =>
                                                      Navigator.pop(context),
                                                  style:
                                                      ElevatedButton.styleFrom(
                                                    backgroundColor:
                                                        Colors.grey[300],
                                                  ),
                                                  child: const Text(
                                                    'Cancel',
                                                    style: TextStyle(
                                                        color: Colors.black),
                                                  ),
                                                ),
                                                ElevatedButton(
                                                  onPressed: () {
                                                    Navigator.pop(context);
                                                    provider
                                                        .addOrRemoveInvitees(
                                                            bookingID:
                                                                bookingID!,
                                                            context: context);
                                                  },
                                                  style:
                                                      ElevatedButton.styleFrom(
                                                    backgroundColor: ThemeColors
                                                        .secondaryColor,
                                                  ),
                                                  child: const Text('Edit'),
                                                )
                                              ],
                                            );
                                          });
                                    }
                                  },
                                  title: 'Update',
                                  color: ThemeColors.secondaryColor,
                                  textStyle: GoogleFonts.rubik(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black),
                                ),
                        ),
                    ],
                  );
          },
        ));
  }

  Widget _addMembersButton() {
    return Consumer(
      builder: (context, value, child) {
        return IconButton(
            onPressed: () {
              showEmployeeSelectList(context);
            },
            icon: const Icon(Icons.add));
      },
    );
  }
}
// allSelectedMembers
