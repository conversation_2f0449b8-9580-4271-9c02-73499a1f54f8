import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../model/booked_room_status_model.dart';
import '../../../../util/colors.dart';
import '../../../../util/styles.dart';

class MeetingInviteesListScreen extends StatelessWidget {
  final List<InvitedUser>? invitedMembersList;
  final bool needToShowStatus;
  const MeetingInviteesListScreen(
      {super.key,
      required this.invitedMembersList,
      this.needToShowStatus = false});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Meeting Attendees List',
      body: ListView.builder(
        physics: const ClampingScrollPhysics(),
        itemCount: invitedMembersList?.length,
        itemBuilder: (context, index) {
          InvitedUser? member = invitedMembersList?[index];
          return ListTile(
              leading: ClipRRect(
                borderRadius: BorderRadius.circular(50),
                child: CachedNetworkImage(
                  height: 50,
                  width: 50,
                  fit: BoxFit.cover,
                  imageUrl: member?.profilePic ?? '',
                  errorWidget: (context, url, error) {
                    return Container(
                      height: 50,
                      width: 50,
                      decoration: BoxDecoration(
                        color: ThemeColors.primaryColor,
                        shape: BoxShape.circle,
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        member?.user?.substring(0, 1).toUpperCase() ?? '',
                        style: GoogleFonts.rubik(
                          fontSize: 22,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  },
                ),
              ),
              title: Text(member?.user ?? ''),
              subtitle: Text(member?.designation ?? ''),
              trailing: needToShowStatus
                  ? statusContainer(status: member?.status ?? '')
                  : null);
        },
      ),
    );
  }

  Widget statusContainer({required String status}) {
    TextStyle? style;
    Color? color;
    switch (status) {
      case 'Approved':
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
        break;
      case 'Rejected':
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case 'Pending':
        {
          style = tsS12w600cE5B900;
          color = ThemeColors.colorFFF2E2;
        }
        break;

      case 'Cancelled':
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case 'Expired':
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      default:
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: color,
      ),
      child: Text(
        status,
        style: style,
      ),
    );
  }
}
