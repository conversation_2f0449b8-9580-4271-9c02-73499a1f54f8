import 'package:e8_hr_portal/model/booked_room_status_model.dart';
import 'package:e8_hr_portal/provider/booked_meeting_room_provider.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/meeting_rooms/meeting_rooms.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../helper/button_widget.dart';
import '../../../../util/colors.dart';
import '../../../../util/styles.dart';
import 'widgets/scheduled_meeting_cards.dart';

class ScheduledMeetingScreen extends StatefulWidget {
  const ScheduledMeetingScreen({super.key});

  @override
  State<ScheduledMeetingScreen> createState() => _ScheduledMeetingScreenState();
}

class _ScheduledMeetingScreenState extends State<ScheduledMeetingScreen> {
  late BookedMeetingRoomProvider _provider;
  @override
  void initState() {
    super.initState();
    _provider = context.read<BookedMeetingRoomProvider>();
    _provider.scheduledMeetingIndex = 0;
  }

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Scheduled Meetings',
      body: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  _topHeadingSection(),
                  const SizedBox(height: 20),
                  Expanded(
                    child: Consumer<BookedMeetingRoomProvider>(
                      builder: (context, provider, _) {
                        return IndexedStack(
                          index: provider.scheduledMeetingIndex,
                          alignment: AlignmentDirectional.topStart,
                          children: [
                            today(context),
                            upComing(context),
                            previous(context),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            _bookMeetingButton(context)
          ],
        ),
      ),
    );
  }

  Padding _bookMeetingButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: ButtonWidget(
        onPressed: () {
          Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => const MeetingRoomScreen()));
        },
        title: 'Book New Meeting',
        color: ThemeColors.secondaryColor,
        textStyle: tsS16FFFFF,
      ),
    );
  }

  Consumer<BookedMeetingRoomProvider> _topHeadingSection() {
    return Consumer<BookedMeetingRoomProvider>(
      builder: (context, provider, child) {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: InkWell(
                onTap: () async {
                  // provider.onChangedScheduledMeetingIndex(index: 0);
                  provider.scheduledMeetingIndex = 0;
                  await provider.getScheduledMeetings(
                      action: 'today', context: context);
                },
                child: Container(
                  height: 45,
                  decoration: BoxDecoration(
                      color: provider.scheduledMeetingIndex == 0
                          ? Colors.black
                          : Colors.white,
                      border: Border.all(
                        color: ThemeColors.colorDEE7FF,
                      ),
                      borderRadius: BorderRadius.circular(5)),
                  child: Center(
                    child: Text(
                      'Today',
                      style: provider.scheduledMeetingIndex == 0
                          ? tsS14FFFFF
                          : tsS14BN,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: InkWell(
                onTap: () {
                  provider.scheduledMeetingIndex = 1;
                  provider.getScheduledMeetings(
                      action: 'next', context: context);
                },
                child: Container(
                  height: 45,
                  decoration: BoxDecoration(
                      color: provider.scheduledMeetingIndex == 1
                          ? Colors.black
                          : Colors.white,
                      border: Border.all(
                        color: ThemeColors.colorDEE7FF,
                      ),
                      borderRadius: BorderRadius.circular(5)),
                  child: Center(
                    child: Text(
                      'Upcoming',
                      style: provider.scheduledMeetingIndex == 1
                          ? tsS14FFFFF
                          : tsS14BN,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: InkWell(
                onTap: () {
                  provider.scheduledMeetingIndex = 2;
                  provider.getScheduledMeetings(
                      action: 'previous', context: context);
                },
                child: Container(
                  height: 45,
                  decoration: BoxDecoration(
                      color: provider.scheduledMeetingIndex == 2
                          ? Colors.black
                          : Colors.white,
                      border: Border.all(
                        color: ThemeColors.colorDEE7FF,
                      ),
                      borderRadius: BorderRadius.circular(5)),
                  child: Center(
                    child: Text(
                      'Previous',
                      style: provider.scheduledMeetingIndex == 2
                          ? tsS14FFFFF
                          : tsS14BN,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget today(BuildContext context) {
    return Consumer<BookedMeetingRoomProvider>(
      builder: (context, provider, child) {
        return provider.todayMeetingList.isEmpty
            ? const Center(
                child: Text(
                  'No Meeting Today',
                  style: TextStyle(color: Colors.black, fontSize: 18),
                ),
              )
            : ListView.separated(
                padding: const EdgeInsets.only(bottom: 10),
                physics: const ClampingScrollPhysics(),
                itemBuilder: (context, index) {
                  BookedRoomStatusModel? meeting =
                      provider.todayMeetingList[index];
                  return ScheduledMeetingCard(item: meeting);
                },
                separatorBuilder: (context, index) {
                  return const SizedBox(
                    height: 20,
                  );
                },
                itemCount: provider.todayMeetingList.length);
      },
    );
  }

  Widget upComing(BuildContext context) {
    return Consumer<BookedMeetingRoomProvider>(
      builder: (context, provider, child) {
        return provider.upcomingMeetingList.isEmpty
            ? const Center(
                child: Text(
                  'No Upcoming Meetings',
                  style: TextStyle(color: Colors.black, fontSize: 18),
                ),
              )
            : ListView.separated(
                padding: const EdgeInsets.only(bottom: 10),
                physics: const ClampingScrollPhysics(),
                itemBuilder: (context, index) {
                  BookedRoomStatusModel? meeting =
                      provider.upcomingMeetingList[index];
                  return ScheduledMeetingCard(item: meeting);
                },
                separatorBuilder: (context, index) {
                  return const SizedBox(
                    height: 20,
                  );
                },
                itemCount: provider.upcomingMeetingList.length);
      },
    );
  }

  Widget previous(BuildContext context) {
    return Consumer<BookedMeetingRoomProvider>(
      builder: (context, provider, child) {
        return provider.previousMeetingList.isEmpty
            ? const Center(
                child: Text(
                  'No Meeting History',
                  style: TextStyle(color: Colors.black, fontSize: 18),
                ),
              )
            : ListView.separated(
                padding: const EdgeInsets.only(bottom: 10),
                physics: const ClampingScrollPhysics(),
                itemBuilder: (context, index) {
                  BookedRoomStatusModel meeting =
                      provider.previousMeetingList[index];
                  return ScheduledMeetingCard(item: meeting);
                },
                separatorBuilder: (context, index) {
                  return const SizedBox(
                    height: 20,
                  );
                },
                itemCount: provider.previousMeetingList.length);
      },
    );
  }
}
