import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:provider/provider.dart';
import '../../../../../model/booked_room_status_model.dart';
import '../../../../../provider/book_meeting_room_provider.dart';
import '../../../../../provider/booked_meeting_room_provider.dart';
import '../../../../../provider/meeting_room_provider.dart';
import '../../../../../util/colors.dart';
import '../../../../../util/date_formatter.dart';
import '../../../../../util/styles.dart';
import '../../../../../widgets/dialog_helper.dart';
import '../../book_meeting_rooms/book_meeting_screen.dart';
import '../../book_meeting_rooms/enum/booking_type.dart';
import '../../meeting_attendees_list_screen/meeting_invitees_list_screen.dart';

class ScheduledMeetingCard extends StatelessWidget {
  final BookedRoomStatusModel? item;

  const ScheduledMeetingCard({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<BookedMeetingRoomProvider>();
    final bookProvider = context.read<BookMeetingRoomProvider>();
    final meetingProvider = context.read<MeetingRoomProvider>();
    int? id = item?.id;
    String? status = item?.status;
    String? reason = item?.reasonForCancel;
    String? roomName = item?.room?.name;
    String? userName = item?.bookedBy?.user;
    String? designation = item?.bookedBy?.designation;
    String? date = item?.bookingDate;
    String? profileImage = item?.bookedBy?.profilePic;
    String? startTime = item?.startTime;
    String? endTime = item?.endTime;
    String? description = item?.description;
    int? roomID = item?.room?.id;
    bool isSelfCreated = item?.selfCreated ?? false;
    List<InvitedUser>? invitedMembersList = item?.invitedUser ?? [];
    String? roomImage = item?.room?.roomImgs
        ?.firstWhere((element) => element.image != null,
            orElse: () => RoomImgs())
        .image;
    final maxWidth = MediaQuery.of(context).size.width;
    final cardWidth = maxWidth - 30;
    final imageWidth = cardWidth * 0.36;
    final imageHeight = imageWidth + 10;
    final formKey = GlobalKey<FormState>();
    final TextEditingController reasonController = TextEditingController();
    DateTime now = DateTime.now();
    log('isSelfCreated ---- $isSelfCreated');
    String tempDateAndTime = '$date $startTime';
    tempDateAndTime = formatDateFromString(
        tempDateAndTime, 'yyyy-MM-dd hh:mm a', 'yyyy-MM-dd HH:mm:ss');
    DateTime? tempDateTime = DateTime.tryParse(tempDateAndTime);
    if (tempDateTime != null) {
      bool isExpired = now.isAfter(DateTime(
          tempDateTime.year,
          tempDateTime.month,
          tempDateTime.day,
          tempDateTime.hour,
          tempDateTime.minute,
          tempDateTime.second));
      // log('status = ${isExpired} - ${DateTime.now()} - $tempDateTime');
      if (isExpired && isSelfCreated) {
        isSelfCreated = false; // if expired, not slidable
      }
    }
    log('scheduledTime - $tempDateTime');
    return Slidable(
      enabled: isSelfCreated,
      key: UniqueKey(),
      endActionPane: ActionPane(
        motion: ScrollMotion(),
        extentRatio: 0.65,
        closeThreshold: 0.1,
        dragDismissible: true,
        openThreshold: 0.2,
        children: [
          SlidableAction(
            autoClose: false,
            onPressed: (context) {
              if (id == null) return; // ignore: unnecessary_null_awareness
              DialogHelper.confirmDialogBoxWithTextField(
                context: context,
                controller: reasonController,
                formKey: formKey,
                hintText: 'Reason for cancelation',
                description:
                    'Are you sure, do you want to cancel this request ?',
                onConfirm: () {
                  if (!formKey.currentState!.validate()) return;
                  provider.cancelApprovedMeetingRoom(
                      bookingID: id, reason: reasonController.text);
                  Navigator.pop(context);
                },
              );
            },
            // backgroundColor: Color(0xFFFE4A49),
            // foregroundColor: Colors.white,
            icon: Icons.delete,
            label: 'Cancel',
            borderRadius: BorderRadius.circular(10.0),
          ),
          SlidableAction(
            autoClose: false,
            onPressed: (context) {
              DialogHelper.confirmationDialogBox(
                context: context,
                description:
                    'Are you sure, do you want to reschedule this request ?',
                onConfirm: () async {
                  if (id == null) return;
                  if (roomID == null) return;
                  bookProvider.selectedDate =
                      stringToDateTime(date: date!, format: 'yyyy-MM-dd') ??
                          DateTime.now();
                  bookProvider.selectedStartTime =
                      bookProvider.convertStringToTimeOfDay(startTime!);
                  bookProvider.selectedEndTime =
                      bookProvider.convertStringToTimeOfDay(endTime!);
                  bookProvider.allSelectedMembers.clear();
                  bookProvider.alreadyInvitedMembersList = invitedMembersList;
                  bookProvider.removedUsersIdList.clear();
                  meetingProvider.getMeetingRoomDetails(
                      meetingRoomID: (roomID).toString());
                  await Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => BookMeetingScreen(
                          bookingType: BookingType.reschedule,
                          description: description,
                          bookingID: id)));
                  provider.getBookedMeetingRoomStatus();
                  // provider.currentIndex = 0;
                  provider.getMeetingRoomRequestForAdmin(
                      status: '${provider.currentIndex + 1}');

                  switch (provider.scheduledMeetingIndex) {
                    case 0:
                      provider.getScheduledMeetings(
                          action: 'today', context: context);
                      break;
                    case 1:
                      provider.getScheduledMeetings(
                          action: 'upcoming', context: context);
                      break;
                    case 2:
                      provider.getScheduledMeetings(
                          action: 'next', context: context);
                      break;
                  }
                },
              );
            },
            // backgroundColor: Color(0xFF21B7CA),
            // foregroundColor: Colors.white,
            icon: Icons.schedule,
            label: 'Reschedule',
            borderRadius: BorderRadius.circular(10.0),
          ),
        ],
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          boxShadow: [
            BoxShadow(
              blurRadius: 10,
              offset: const Offset(2, 2),
              color: ThemeColors.colorDEE7FF,
            ),
          ],
          color: Colors.white,
        ),
        padding: const EdgeInsets.all(10.0),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 40.0),
                    child: Align(
                      alignment: Alignment.topLeft,
                      child: Text(
                        roomName.toString(),
                        style: tsS128391B5,
                      ),
                    ),
                  ),
                  Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(30),
                        child: CachedNetworkImage(
                          height: 36,
                          width: 36,
                          fit: BoxFit.cover,
                          imageUrl: profileImage.toString(),
                          errorWidget: (context, url, error) {
                            return Container(
                              color: ThemeColors.primaryColor,
                              alignment: Alignment.center,
                              child: Text(
                                userName!.substring(0, 1).toUpperCase(),
                                style: tsS18w500,
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              userName.toString(),
                              style: tsS161E2138,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Align(
                              alignment: Alignment.topLeft,
                              child: Text(
                                designation ?? '',
                                style: tsS12grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 5),
                  Text(
                    description ?? '',
                    style: const TextStyle(fontSize: 15),
                  ),
                  const SizedBox(height: 5),
                  FittedBox(
                    child: Row(
                      children: [
                        ImageIcon(
                          const AssetImage('assets/icons/calendar.png'),
                          color: ThemeColors.color7E7E7E,
                          size: 20,
                        ),
                        const SizedBox(width: 10),
                        Text(
                          '${formatDateFromString(date!, 'yyyy-MM-dd', 'dd MMM yyyy')}  $startTime - $endTime',
                          style: tsS12NormalBlack,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 5),
                  SizedBox(
                    height: 40,
                    child: Row(
                      children: [
                        Expanded(
                          child: ListView.builder(
                            itemBuilder: (BuildContext context, int index) {
                              InvitedUser? data = invitedMembersList[index];
                              return Align(
                                widthFactor: .7,
                                child: InkWell(
                                  onTap: () {
                                    if (invitedMembersList.isNotEmpty) {
                                      Navigator.of(context).push(
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  MeetingInviteesListScreen(
                                                      invitedMembersList:
                                                          invitedMembersList)));
                                    }
                                  },
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(25),
                                    child: CachedNetworkImage(
                                      height: 30,
                                      width: 30,
                                      fit: BoxFit.cover,
                                      imageUrl: data.profilePic.toString(),
                                      errorWidget: (context, url, error) {
                                        return Container(
                                          color: ThemeColors.primaryColor,
                                          alignment: Alignment.center,
                                          child: Text(
                                            data.user == null
                                                ? ''
                                                : data.user!
                                                    .substring(0, 1)
                                                    .toUpperCase(),
                                            style: const TextStyle(
                                                fontWeight: FontWeight.bold),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              );
                            },
                            itemCount: invitedMembersList.length,
                            shrinkWrap: true,
                            scrollDirection: Axis.horizontal,
                            physics: const ClampingScrollPhysics(),
                          ),
                        )
                      ],
                    ),
                  ),
                  if (status == 'Cancelled')
                    Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                              color: Colors.red.withOpacity(.7),
                              borderRadius: BorderRadius.circular(3)),
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: Text(status ?? ''),
                        ),
                        const Spacer()
                      ],
                    ),
                  const SizedBox(
                    height: 5,
                  ),
                  if (reason != null && status == 'Cancelled')
                    Text('Reason : $reason')
                ],
              ),
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                image: DecorationImage(
                  image: CachedNetworkImageProvider(roomImage.toString()),
                  fit: BoxFit.cover,
                ),
              ),
              height: imageHeight,
              margin: const EdgeInsets.only(left: 10),
              width: imageWidth,
            ),
          ],
        ),
      ),
    );
  }
}
