import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/provider/booked_meeting_room_provider.dart';
import 'package:e8_hr_portal/provider/meeting_room_provider.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/book_meeting_rooms/enum/booking_type.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/widgets/status_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:provider/provider.dart';
import '../../../../../model/booked_room_status_model.dart';
import '../../../../../provider/book_meeting_room_provider.dart';
import '../../../../../util/colors.dart';
import '../../../../../util/date_formatter.dart';
import '../../../../../util/styles.dart';
import '../../../../../util/validator.dart';
import '../../book_meeting_rooms/book_meeting_screen.dart';
import '../../meeting_attendees_list_screen/meeting_invitees_list_screen.dart';

class MeetingRoomRequestStatusCard extends StatelessWidget {
  final List<InvitedUser>? invitedMembersList;
  final String? meetingRoomId;
  final String? userDesignation;
  final String? status;
  final String? reason;
  final String? startTime;
  final String? endTime;
  final String? roomName;
  final String? userName;
  final String? date;
  final String? roomImage;
  final String? profileImage;
  final String? description;
  final int? bookingID;
  final String? reasonForRejection;
  MeetingRoomRequestStatusCard(
      {super.key,
      required this.reason,
      required this.invitedMembersList,
      required this.meetingRoomId,
      required this.userDesignation,
      required this.description,
      required this.status,
      required this.startTime,
      required this.endTime,
      required this.profileImage,
      required this.roomName,
      required this.userName,
      required this.date,
      required this.roomImage,
      required this.bookingID,
      required this.reasonForRejection});
  final formKey = GlobalKey<FormState>();
  final TextEditingController reasonController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    BookMeetingRoomProvider provider =
        Provider.of<BookMeetingRoomProvider>(context, listen: false);
    TimeOfDay time = provider.convertStringToTimeOfDay(startTime!);
    TimeOfDay timeEndsIn = provider.convertStringToTimeOfDay(endTime!);
    final maxWidth = MediaQuery.of(context).size.width;
    final cardWidth = maxWidth - 30;
    final imageWidth = cardWidth * 0.36;
    final imageHeight = imageWidth - 1;
    DateTime? formatedDate =
        stringToDateTime(date: date!, format: 'yyyy-MM-dd');
    DateTime? formatedEndDate =
        stringToDateTime(date: date!, format: 'yyyy-MM-dd');
    formatedDate =
        formatedDate?.add(Duration(hours: time.hour, minutes: time.minute));
    formatedEndDate = formatedEndDate
        ?.add(Duration(hours: timeEndsIn.hour, minutes: timeEndsIn.minute));
    DateTime now = DateTime.now();
    bool? isExpired = formatedDate?.isBefore(now);
    bool? isEndExpired = formatedEndDate?.isBefore(now);
    isExpired = isEndExpired;
    log('end.time = ${endTime}');
    return Slidable(
      // enabled: false,
      closeOnScroll: true,
      endActionPane: status == 'Pending' && isExpired == false
          ? _actionPane(context)
          : null,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          boxShadow: [
            BoxShadow(
              blurRadius: 10,
              offset: const Offset(2, 2),
              color: ThemeColors.colorDEE7FF,
            ),
          ],
          color: Colors.white,
        ),
        padding: const EdgeInsets.all(10.0),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    children: [
                      const SizedBox(width: 41),
                      Text(
                        roomName.toString(),
                        overflow: TextOverflow.ellipsis,
                        style: tsS128391B5,
                      ),
                    ],
                  ),
                  const SizedBox(height: 5),
                  Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(25),
                        child: CachedNetworkImage(
                          height: 36,
                          width: 36,
                          fit: BoxFit.cover,
                          imageUrl: profileImage.toString(),
                          errorWidget: (context, url, error) {
                            return Container(
                              color: ThemeColors.primaryColor,
                              alignment: Alignment.center,
                              child: Text(userName
                                  .toString()
                                  .substring(0, 1)
                                  .toUpperCase()),
                            );
                          },
                        ),
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              userName.toString(),
                              style: tsS161E2138,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Align(
                              alignment: Alignment.topLeft,
                              child: Text(
                                userDesignation ?? '',
                                style: tsS12grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 5),
                  InkWell(
                    onTap: invitedMembersList != null &&
                            invitedMembersList!.isNotEmpty
                        ? () {
                            Navigator.of(context).push(MaterialPageRoute(
                                builder: (context) => MeetingInviteesListScreen(
                                    invitedMembersList: invitedMembersList,
                                    needToShowStatus: true)));
                          }
                        : null,
                    child: Text(
                      '${invitedMembersList?.length ?? '0'} Attendees',
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    description ?? '',
                    style: const TextStyle(fontSize: 15),
                  ),
                  const SizedBox(height: 10),
                  Row(
                    children: [
                      ImageIcon(
                        const AssetImage('assets/icons/calendar.png'),
                        color: ThemeColors.color7E7E7E,
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Text(
                          '${formatDateFromString(date!, 'yyyy-MM-dd', 'dd MMM yyyy')}  $startTime - $endTime',
                          style: tsS12NormalBlack,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      if (status == 'Pending' && isExpired == true)
                        const StatusContainer(status: 'Expired')
                      else
                        StatusContainer(status: status ?? ''),
                      const Spacer(),
                      if (status == 'Pending' || status == 'Approved')
                        Consumer<BookedMeetingRoomProvider>(
                          builder: (context, provider, child) {
                            if (isExpired == false) {
                              return SizedBox(
                                height: 25,
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    shape: const StadiumBorder(),
                                    backgroundColor: const Color(0xffEF5E5E),
                                    foregroundColor: Colors.white,
                                  ),
                                  onPressed: () => _cancelMeetingRoomDialog(
                                    context,
                                    provider,
                                  ),
                                  child: const Text('Cancel'),
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        )
                    ],
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  if (reason != null && status == 'Cancelled')
                    Text('Reason : $reason')
                  else if (reasonForRejection != null && status == 'Rejected')
                    Text('Reason for rejection : $reasonForRejection')
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 10),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: CachedNetworkImage(
                  fit: BoxFit.cover,
                  height: imageHeight,
                  width: imageWidth,
                  imageUrl: roomImage ?? '',
                  errorWidget: (context, url, error) {
                    return Image.network(
                      'https://t4.ftcdn.net/jpg/02/71/18/23/360_F_271182313_inefaqbiMXeXTEavRDl14gPVBbe6xyyW.jpg',
                      height: imageHeight,
                      fit: BoxFit.cover,
                    );
                  },
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  ActionPane _actionPane(BuildContext context) {
    return ActionPane(motion: const ScrollMotion(), children: [
      Expanded(
        child: Container(
          decoration: const BoxDecoration(color: Colors.white),
          child: Center(
            child: IconButton(
                onPressed: () {
                  showDialog(
                      context: context,
                      builder: (ctxt) {
                        return AlertDialog(
                          title: const Text('Confirm edit'),
                          content: const Text('Are you sure you want to edit?'),
                          actions: [
                            ElevatedButton(
                              onPressed: () => Navigator.pop(context),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey[300],
                              ),
                              child: const Text(
                                'Cancel',
                                style: TextStyle(color: Colors.black),
                              ),
                            ),
                            Consumer3<BookMeetingRoomProvider,
                                MeetingRoomProvider, BookedMeetingRoomProvider>(
                              builder: (context, provider1, provider2,
                                  provider3, child) {
                                return ElevatedButton(
                                  onPressed: () async {
                                    Navigator.pop(context);

                                    provider1.selectedDate = stringToDateTime(
                                            date: date!,
                                            format: 'yyyy-MM-dd') ??
                                        DateTime.now();
                                    provider1.selectedStartTime = provider1
                                        .convertStringToTimeOfDay(startTime!);
                                    provider1.selectedEndTime = provider1
                                        .convertStringToTimeOfDay(endTime!);
                                    provider1.allSelectedMembers.clear();
                                    provider1.alreadyInvitedMembersList =
                                        invitedMembersList;
                                    provider1.removedUsersIdList.clear();
                                    provider2.getMeetingRoomDetails(
                                        meetingRoomID: meetingRoomId!);
                                    await Navigator.of(context).push(
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                BookMeetingScreen(
                                                    bookingType:
                                                        BookingType.edit,
                                                    description: description,
                                                    bookingID: bookingID)));
                                    provider3.getBookedMeetingRoomStatus();
                                    // provider.selectedCollegue = null;
                                    // provider.roomIsAvailable = false;
                                    // provider.selectedRoomForAvailabilityChecking =
                                    //     null;
                                    // provider.allSelectedColleguesList?.clear();
                                    // provider.changeAvailability(true);
                                    // DateTime? formatedDate = stringToDateTime(
                                    //     date: date.toString(),
                                    //     format: 'dd MMM yyyy');
                                    // String start = formatDateFromString(
                                    //     startTime!, 'hh:mma', 'HH:mm:ss');
                                    // String end = formatDateFromString(
                                    //     endTime!, 'hh:mma', 'HH:mm:ss');
                                    // await provider.getMeetingRoomDetailes(
                                    //     roomId: meetingRoomId,
                                    //     context: context);
                                    // Navigator.push(
                                    //     context,
                                    //     MaterialPageRoute(
                                    //         builder: (context) =>
                                    //             BookedMeetingRoomEditPage(
                                    //               startTime: start,
                                    //               meetingId:
                                    //                   id.toString(),
                                    //               endTime: end,
                                    //               date: formatedDate,
                                    //               desctription:
                                    //                   description,
                                    //               invitedMembers:
                                    //                   invitedMembersList,
                                    //             )));
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: ThemeColors.secondaryColor,
                                  ),
                                  child: const Text('Edit'),
                                );
                              },
                            )
                          ],
                        );
                      });
                },
                icon: const Icon(Icons.edit)),
          ),
        ),
      ),
    ]);
  }

  Future<dynamic> _cancelMeetingRoomDialog(
      BuildContext context, BookedMeetingRoomProvider provider) {
    return showDialog(
        context: context,
        builder: (ctxt) {
          return AlertDialog(
            title: const Text('Cancel Meeting Room'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Are you sure you want to cancel the request?'),
                Form(
                    key: formKey,
                    child: TextFormField(
                      decoration: const InputDecoration(
                          hintText: 'Reason for cancel meeting'),
                      controller: reasonController,
                      validator: (value) => Validator.text(value),
                    ))
              ],
            ),
            actions: [
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[300],
                ),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.black),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    Navigator.of(context).pop();
                    await provider.cancelBookedMeetingRoom(
                        context: context,
                        bookingID: bookingID!,
                        reason: reasonController.text);
                    reasonController.clear();
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: ThemeColors.secondaryColor,
                ),
                child: const Text('Cancel request'),
              )
            ],
          );
        });
  }
}
