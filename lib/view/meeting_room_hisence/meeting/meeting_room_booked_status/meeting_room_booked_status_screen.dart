// ignore_for_file: use_build_context_synchronously, must_be_immutable, prefer_const_constructors_in_immutables
import 'dart:developer';

import 'package:e8_hr_portal/model/booked_room_status_model.dart';
import 'package:e8_hr_portal/provider/booked_meeting_room_provider.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';
import 'widgets/meeting_room_request_status_card.dart';

class MeetingRoomBookedStatusScreen extends StatelessWidget {
  MeetingRoomBookedStatusScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Meeting Room Booked Status',
      body: Consumer<BookedMeetingRoomProvider>(
        builder: (context, provider, child) {
          return provider.isBookedRoomsStatusLoading == true
              ? const Center(child: CircularProgressIndicator())
              : provider.bookedRoomsStatusList.isEmpty
                  ? const Center(
                      child: Text(
                        'No Meeting Room Booked',
                        style: TextStyle(color: Colors.black, fontSize: 18),
                      ),
                    )
                  : ListView.separated(
                      padding: const EdgeInsets.all(15.0),
                      itemBuilder: (context, index) {
                        BookedRoomStatusModel room =
                            provider.bookedRoomsStatusList[index];
                        log('room.status - ${room.status} - ');
                        return MeetingRoomRequestStatusCard(
                          userName: room.bookedBy?.user,
                          profileImage: room.bookedBy?.profilePic,
                          reason: room.reasonForCancel,
                          startTime: room.startTime,
                          endTime: room.endTime,
                          description: room.description,
                          roomName: room.room?.name,
                          userDesignation: room.bookedBy?.designation,
                          date: room.bookingDate,
                          status: room.status,
                          roomImage: room.room?.roomImgs?.first.image,
                          meetingRoomId: room.room?.id.toString(),
                          invitedMembersList: room.invitedUser,
                          bookingID: room.id,
                          reasonForRejection: room.info?.rejectedReason,
                        );
                      },
                      separatorBuilder: (context, index) {
                        return const SizedBox(height: 15);
                      },
                      itemCount: provider.bookedRoomsStatusList.length,
                    );
        },
      ),
    );
  }
}
