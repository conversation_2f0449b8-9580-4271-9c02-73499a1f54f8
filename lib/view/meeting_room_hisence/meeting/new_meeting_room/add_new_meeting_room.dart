import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/amenity_model.dart';
import 'package:e8_hr_portal/model/user_policies_model.dart';
import 'package:e8_hr_portal/provider/meeting_room_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/new_meeting_room/widgets/image_upload_container.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/new_meeting_room/widgets/new_meeting_room_image_list.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';
import '../../../../util/colors.dart';
import '../../../../util/general_functions.dart';
import '../../../../util/styles.dart';
import '../common_widgets/common_textfeild_underline.dart';

class AddNewMeetingRoomScreen extends StatefulWidget {
  final String? meetingRoomName;
  final String? seatCount;
  final String? meetingRoomID;
  const AddNewMeetingRoomScreen(
      {super.key, this.meetingRoomName, this.seatCount, this.meetingRoomID});
  @override
  State<AddNewMeetingRoomScreen> createState() =>
      _AddNewMeetingRoomScreenState();
}

class _AddNewMeetingRoomScreenState extends State<AddNewMeetingRoomScreen> {
  final TextEditingController _meetingRoomNameController =
      TextEditingController();
  final TextEditingController _seatCapacityController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  @override
  void initState() {
    _meetingRoomNameController.text = widget.meetingRoomName ?? '';
    _seatCapacityController.text = widget.seatCount ?? '';
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        screenTitle: 'Add New Meeting Room',
        body: Consumer<MeetingRoomProvider>(
          builder: (context, provider, child) {
            return Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(15, 15, 15, 15),
                      child: Column(
                        children: [
                          const ImageUploadContainer(),
                          const NewMeetingRoomImageList(),
                          const SizedBox(height: 24),
                          Form(
                            autovalidateMode:
                                AutovalidateMode.onUserInteraction,
                            key: _formKey,
                            child: Column(
                              children: [
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text(
                                    'Meeting Room Name',
                                    style: tsS128391B5,
                                  ),
                                ),
                                CommonUndelineTextFeild(
                                  controller: _meetingRoomNameController,
                                  hintText: 'Enter Your Meeting Room Name',
                                  hintStyle: tsS14BN,
                                  textCapitalization: TextCapitalization.words,
                                  validator: (value) {
                                    return value!.isEmpty
                                        ? 'This field is required'
                                        : value.length < 4
                                            ? "Please enter a valid room name"
                                            : null;
                                  },
                                ),
                                const SizedBox(height: 10),
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text(
                                    'Set Capacity Seats',
                                    style: tsS128391B5,
                                  ),
                                ),
                                CommonUndelineTextFeild(
                                  inputFormat: [
                                    FilteringTextInputFormatter.digitsOnly
                                  ],
                                  controller: _seatCapacityController,
                                  hintText: 'Enter Capacity Seats',
                                  hintStyle: tsS14BN,
                                  keyboardType: TextInputType.number,
                                  validator: (value) {
                                    return value!.isEmpty
                                        ? 'This field is required'
                                        : value == '0'
                                            ? 'Please enter a valid seat count'
                                            : null;
                                  },
                                ),
                                const SizedBox(height: 10),
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text(
                                    'Amenities',
                                    style: tsS128391B5,
                                  ),
                                ),
                                if (provider.amenitiesList.isNotEmpty)
                                  DropdownButtonFormField<AmenityModel>(
                                    decoration: InputDecoration(
                                      hintText: 'Select amenities',
                                      hintStyle: tsS14BN,
                                      focusedBorder: const UnderlineInputBorder(
                                        borderSide:
                                            BorderSide(color: Colors.grey),
                                      ),
                                    ),
                                    items: provider.amenitiesList.map((e) {
                                      return DropdownMenuItem<AmenityModel>(
                                        value: e,
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Text(
                                              e.name.toString(),
                                            ),
                                            SizedBox(
                                              height: 20,
                                              width: 20,
                                              child: CachedNetworkImage(
                                                  imageUrl: e.icon.toString()),
                                            )
                                          ],
                                        ),
                                      );
                                    }).toList(),
                                    value: provider.selectedAmenity,
                                    onChanged: (AmenityModel? amenity) {
                                      if (amenity != null) {
                                        provider.selectedAmenity = amenity;

                                        if (provider.selectedAmenitiesList
                                            .where((e) => e.id == amenity.id)
                                            .isEmpty) {
                                          provider.addAmenity(amenity);
                                        }
                                      }
                                    },
                                    validator: (value) =>
                                        provider.selectedAmenity == null
                                            ? 'Please select amenities'
                                            : null,
                                  ),
                                const SizedBox(height: 10),
                                if (provider.selectedAmenitiesList.isNotEmpty)
                                  SizedBox(
                                    height: 40,
                                    child: Row(
                                      children: [
                                        ListView.separated(
                                          physics:
                                              const BouncingScrollPhysics(),
                                          scrollDirection: Axis.horizontal,
                                          shrinkWrap: true,
                                          itemBuilder: (context, index) {
                                            AmenityModel amenity = provider
                                                .selectedAmenitiesList[index];
                                            return SizedBox(
                                              height: 35,
                                              width: 35,
                                              child: Stack(
                                                children: [
                                                  Column(
                                                    children: [
                                                      const SizedBox(
                                                        height: 5,
                                                      ),
                                                      CachedNetworkImage(
                                                          height: 25,
                                                          width: 25, //
                                                          imageUrl:
                                                              amenity.icon ??
                                                                  ''),
                                                    ],
                                                  ),
                                                  Positioned(
                                                    top: 0,
                                                    right: 0,
                                                    child: GestureDetector(
                                                      onTap: () {
                                                        provider.removeAmenity(
                                                            index);
                                                      },
                                                      child: Container(
                                                        decoration:
                                                            const BoxDecoration(
                                                          color: Colors.black,
                                                          shape:
                                                              BoxShape.circle,
                                                        ),
                                                        height: 20,
                                                        child: Icon(
                                                          Icons.close,
                                                          color: ThemeColors
                                                              .color8391B5,
                                                          size: 15,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            );
                                          },
                                          itemCount: provider
                                              .selectedAmenitiesList.length,
                                          separatorBuilder:
                                              (BuildContext context,
                                                  int index) {
                                            return const SizedBox(width: 20);
                                          },
                                        )
                                      ],
                                    ),
                                  ),
                                const SizedBox(height: 10),
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text(
                                    'Location',
                                    style: tsS128391B5,
                                  ),
                                ),
                                if (provider.policiesList.isNotEmpty)
                                  DropdownButtonFormField<UserPoliciesModel>(
                                    decoration: InputDecoration(
                                      hintText: 'Select location',
                                      hintStyle: tsS14BN,
                                      focusedBorder: const UnderlineInputBorder(
                                        borderSide:
                                            BorderSide(color: Colors.grey),
                                      ),
                                    ),
                                    items: provider.policiesList.map((e) {
                                      return DropdownMenuItem<
                                          UserPoliciesModel>(
                                        value: e,
                                        child: Text(
                                          e.name.toString(),
                                        ),
                                      );
                                    }).toList(),
                                    value: provider.selectedUserPolicy,
                                    onChanged: (UserPoliciesModel? policy) {
                                      provider.selectedUserPolicy = policy;
                                    },
                                    validator: (value) =>
                                        provider.selectedUserPolicy == null
                                            ? 'Please select user policy'
                                            : null,
                                  ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 30),
                        ],
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15.0, 20.0, 15.0, 30.0),
                  child: provider.creatingMeetingRoom
                      ? const CircularProgressIndicator.adaptive()
                      : ButtonWidget(
                          title:
                              widget.meetingRoomID == null ? 'Save' : 'Update',
                          textStyle: tsS16FFFFF,
                          color: ThemeColors.secondaryColor,
                          onPressed: () async {
                            hideKeyboard(context);

                            if (widget.meetingRoomID == null) {
                              //meeting room creating
                              if (provider.selectedMeetingRoomImages.isEmpty) {
                                showSnackBarMessage(
                                    context: context,
                                    msg: 'Please select image');
                              } else if (_formKey.currentState!.validate()) {
                                provider.createMeetingRoom(
                                    roomName: _meetingRoomNameController.text,
                                    seatCount: _seatCapacityController.text,
                                    context: context);
                              }
                            } else {
                              //meeting room editing
                              if (provider.alreadySelectedImagesList.isEmpty &&
                                  provider.selectedMeetingRoomImages.isEmpty) {
                                showSnackBarMessage(
                                    context: context,
                                    msg: 'Please select image');
                              } else if (_formKey.currentState!.validate()) {
                                provider.editMeetingRoom(
                                    meetingRoomId: widget.meetingRoomID!,
                                    roomName: _meetingRoomNameController.text,
                                    seatCount: _seatCapacityController.text,
                                    context: context);
                              }
                            }
                          },
                        ),
                )
              ],
            );
          },
        ));
  }
}
