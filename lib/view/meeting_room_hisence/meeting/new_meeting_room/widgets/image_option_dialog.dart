import 'package:e8_hr_portal/provider/meeting_room_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class ImageOptionDialog extends StatelessWidget {
  const ImageOptionDialog({super.key});

  @override
  Widget build(BuildContext context) {
    MeetingRoomProvider provider = Provider.of(context);
    return AlertDialog(
      title: const Text('Options'),
      content: SingleChildScrollView(
        child: ListBody(
          children: [
            GestureDetector(
                child: const Text('Capture Image From Camera'),
                onTap: () => provider.pickImageFromCamera(context: context)),
            const Padding(padding: EdgeInsets.all(10)),
            GestureDetector(
                child: const Text('Take Image From Gallery'),
                onTap: () => provider.pickImageFromGallery(context: context)),
          ],
        ),
      ),
    );
  }
}
