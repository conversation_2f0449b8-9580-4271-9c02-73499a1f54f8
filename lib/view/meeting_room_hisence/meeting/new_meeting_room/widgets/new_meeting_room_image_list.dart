import 'package:e8_hr_portal/provider/meeting_room_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../../util/colors.dart';

class NewMeetingRoomImageList extends StatelessWidget {
  const NewMeetingRoomImageList({super.key});
  @override
  Widget build(BuildContext context) {
    double imageSize = MediaQuery.of(context).size.width / 4;
    return Consumer<MeetingRoomProvider>(
      builder: (context, provider, child) {
        return provider.selectedMeetingRoomImages.isEmpty &&
                provider.alreadySelectedImagesList.isEmpty
            ? const SizedBox()
            : Padding(
                padding: const EdgeInsets.only(top: 20),
                child: SizedBox(
                  height: imageSize,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        ...provider.selectedMeetingRoomImages.map((file) {
                          int index =
                              provider.selectedMeetingRoomImages.indexOf(file);
                          return Padding(
                            padding: const EdgeInsets.only(right: 7),
                            child: Stack(children: [
                              SizedBox(
                                width: imageSize,
                                height: imageSize,
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(5),
                                  child: Image.file(
                                    provider.selectedMeetingRoomImages[index],
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 2,
                                right: 5,
                                child: GestureDetector(
                                  onTap: () => provider.removeImage(index),
                                  child: Container(
                                    decoration: const BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                    ),
                                    height: 20,
                                    child: Icon(
                                      Icons.close,
                                      color: ThemeColors.color8391B5,
                                      size: 15,
                                    ),
                                  ),
                                ),
                              ),
                            ]),
                          );
                        }),
                        ...provider.alreadySelectedImagesList.map((url) {
                          int index =
                              provider.alreadySelectedImagesList.indexOf(url);
                          return Padding(
                            padding: const EdgeInsets.only(right: 7),
                            child: Stack(children: [
                              SizedBox(
                                width: imageSize,
                                height: imageSize,
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(5),
                                  child: Image.network(
                                    provider
                                        .alreadySelectedImagesList[index].image
                                        .toString(),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 2,
                                right: 5,
                                child: GestureDetector(
                                  onTap: () => provider
                                      .removeAlreadySelectedImage(index),
                                  child: Container(
                                    decoration: const BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                    ),
                                    height: 20,
                                    child: Icon(
                                      Icons.close,
                                      color: ThemeColors.color8391B5,
                                      size: 15,
                                    ),
                                  ),
                                ),
                              ),
                            ]),
                          );
                        })
                      ],
                    ),
                  ),
                ),
              );
      },
    );
  }
}

// class AlreadyImageList extends StatelessWidget {
//   const AlreadyImageList({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     CompanyActivitiesProvider provider =
//         Provider.of<CompanyActivitiesProvider>(context);
//     if (provider.imageListForEditEvent.isEmpty) {
//       return Container();
//     } else {
//       var imageSize = MediaQuery.of(context).size.width / 4;
//       return Padding(
//         padding: const EdgeInsets.only(top: 20),
//         child: SizedBox(
//           height: imageSize,
//           child: ListView.separated(
//             scrollDirection: Axis.horizontal,
//             itemBuilder: (BuildContext context, int index) {
//               return Stack(children: [
//                 SizedBox(
//                   width: imageSize,
//                   height: imageSize,
//                   child: ClipRRect(
//                     borderRadius: BorderRadius.circular(5),
//                     child: Image.network(
//                       provider.imageListForEditEvent
//                           .elementAt(index)
//                           .eventImage
//                           .toString(),
//                       fit: BoxFit.cover,
//                     ),
//                   ),
//                 ),
//                 Positioned(
//                   top: 2,
//                   right: 5,
//                   child: GestureDetector(
//                     onTap: () {
//                       provider.removeImage(index,
//                           provider.imageListForEditEvent[index].id!.toInt());
//                     },
//                     child: Container(
//                       decoration: const BoxDecoration(
//                         color: Colors.white,
//                         shape: BoxShape.circle,
//                       ),
//                       height: 20,
//                       child: Icon(
//                         Icons.close,
//                         color: ThemeColors.color8391B5,
//                         size: 15,
//                       ),
//                     ),
//                   ),
//                 ),
//               ]);
//             },
//             separatorBuilder: (BuildContext context, int index) {
//               return const SizedBox(
//                 width: 7,
//               );
//             },
//             itemCount: provider.imageListForEditEvent.length,
//           ),
//         ),
//       );
//     }
//   }
// }
