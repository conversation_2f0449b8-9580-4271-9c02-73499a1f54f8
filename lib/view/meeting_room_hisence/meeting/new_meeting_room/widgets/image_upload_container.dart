import 'package:flutter/material.dart';
import '../../../../../util/colors.dart';
import '../../../../../util/styles.dart';
import 'image_option_dialog.dart';

class ImageUploadContainer extends StatelessWidget {
  const ImageUploadContainer({super.key});

  showImageOptionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const ImageOptionDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => showImageOptionDialog(context),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: Colors.white,
          boxShadow: const [
            BoxShadow(
              offset: Offset(2, 2),
              blurRadius: 10,
              color: Color(0xFFDEE7FF),
            ),
          ],
        ),
        padding: const EdgeInsets.fromLTRB(0, 44, 0, 27),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 25,
              width: 25,
              child: ImageIcon(
                const AssetImage('assets/icons/upload_image.png'),
                color: ThemeColors.color8391B5,
              ),
            ),
            const SizedBox(
              height: 12,
            ),
            Align(
              alignment: Alignment.center,
              child: Text(
                'Upload images',
                style: tsS14w500,
              ),
            ),
            const SizedBox(
              height: 12,
            ),
            Align(
              alignment: Alignment.center,
              child: Text(
                'Only jpeg, jpg files with max size of 4 MB.',
                style: tsS12grey,
              ),
            )
          ],
        ),
      ),
    );
  }
}
