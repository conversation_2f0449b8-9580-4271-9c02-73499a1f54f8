import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/colors.dart';

class FAQTextfieldWidget extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final TextInputType? keyboardType;
  final void Function()? onTap;
  final bool readOnly;
  final TextCapitalization textCapitalization;
  final String? Function(String?)? validator;
  final bool enabled;
  const FAQTextfieldWidget({
    super.key,
    this.controller,
    this.labelText,
    this.keyboardType,
    this.onTap,
    this.readOnly = false,
    this.textCapitalization = TextCapitalization.none,
    this.validator,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      textCapitalization: textCapitalization,
      onTap: onTap,
      readOnly: readOnly,
      enabled: enabled,
      style: GoogleFonts.rubik(
        fontWeight: enabled ? FontWeight.w500 : FontWeight.normal,
        color:
            enabled ? Colors.black : ThemeColors.color8391B5.withOpacity(0.5),
      ),
      decoration: InputDecoration(
          labelText: labelText,
          labelStyle: GoogleFonts.rubik(
            color: enabled
                ? ThemeColors.color8391B5
                : ThemeColors.color8391B5.withOpacity(0.5),
          ),
          border: readOnly == true
              ? InputBorder.none
              : const UnderlineInputBorder(
                  borderSide: BorderSide(
                    color: Color(0xFFDEE7FF),
                  ),
                ),
          enabledBorder: const UnderlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xFFDEE7FF),
            ),
          ),
          disabledBorder: const UnderlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xFFDEE7FF),
              width: 0.5,
            ),
          )),
      validator: validator,
    );
  }
}
