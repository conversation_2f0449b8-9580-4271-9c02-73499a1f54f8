import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/colors.dart';

class FAQDescription<PERSON>ield extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? Function(String?)? validator;
  final TextCapitalization textCapitalization;
  const FAQDescriptionField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.validator,
    this.textCapitalization = TextCapitalization.none,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      minLines: 5,
      maxLines: 20,
      textCapitalization: textCapitalization,
      decoration: InputDecoration(
        hintText: hintText,
        labelText: labelText,
        labelStyle: GoogleFonts.rubik(
          color: ThemeColors.color8391B5,
        ),
        hintStyle: GoogleFonts.rubik(
          color: ThemeColors.color8391B5,
        ),
        border: OutlineInputBorder(
          borderSide: BorderSide(
            color: ThemeColors.colorDEE7FF,
          ),
          borderRadius: BorderRadius.circular(5.0),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ThemeColors.colorDEE7FF,
          ),
          borderRadius: BorderRadius.circular(5.0),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ThemeColors.colorDEE7FF,
          ),
          borderRadius: BorderRadius.circular(5.0),
        ),
      ),
      validator: validator,
    );
  }
}
