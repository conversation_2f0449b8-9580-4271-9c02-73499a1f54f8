import 'package:e8_hr_portal/provider/faq_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../util/colors.dart';

class ImageUploadSection extends StatelessWidget {
  const ImageUploadSection({super.key});
  @override
  Widget build(BuildContext context) {
    double imageSize = MediaQuery.of(context).size.width / 4;
    return Consumer<FAQProvider>(
      builder: (context, provider, child) {
        return Padding(
          padding: const EdgeInsets.only(top: 15),
          child: SizedBox(
            height: imageSize,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  ...provider.selectedImages.map((file) {
                    int index = provider.selectedImages.indexOf(file);
                    return Padding(
                      padding: const EdgeInsets.only(right: 7),
                      child: Stack(children: [
                        SizedBox(
                          width: imageSize,
                          height: imageSize,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(5),
                            child: Image.file(
                              provider.selectedImages[index],
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        Positioned(
                          top: 2,
                          right: 5,
                          child: GestureDetector(
                            onTap: () => provider.removeImage(index),
                            child: Container(
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                              height: 20,
                              child: Icon(
                                Icons.close,
                                color: ThemeColors.color8391B5,
                                size: 15,
                              ),
                            ),
                          ),
                        ),
                      ]),
                    );
                  }),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
