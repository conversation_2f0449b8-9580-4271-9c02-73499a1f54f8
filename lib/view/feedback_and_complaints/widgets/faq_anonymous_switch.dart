import 'package:flutter/material.dart';
import 'package:e8_hr_portal/provider/faq_provider.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:provider/provider.dart';

class FAQAnonymousSwitch extends StatelessWidget {
  const FAQAnonymousSwitch({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          'Submit as Anonymous',
          style: tsS14c8391B5,
        ),
        Consumer<FAQProvider>(builder: (context, provider, _) {
          return Switch(
            inactiveThumbColor: Colors.white,
            inactiveTrackColor: const Color(0xFF8391B5),
            activeTrackColor: Colors.black,
            activeColor: Colors.white,
            value: provider.isAnonymous,
            onChanged: (value) => provider.isAnonymous = value,
          );
        }),
      ],
    );
  }
}
