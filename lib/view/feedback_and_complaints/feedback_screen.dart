import 'package:e8_hr_portal/model/login_model.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/view/feedback_and_complaints/widgets/faq_anonymous_switch.dart';
import 'package:e8_hr_portal/view/feedback_and_complaints/widgets/faq_description_field.dart';
import 'package:e8_hr_portal/view/feedback_and_complaints/widgets/faq_textfield_widget.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';
import '../../provider/faq_provider.dart';
import '../../util/general_functions.dart';
import '../../util/styles.dart';
import '../../util/validator.dart';
import 'widgets/image_upload_section.dart';

class FeedbackScreen extends StatefulWidget {
  const FeedbackScreen({super.key});
  @override
  State<FeedbackScreen> createState() => _FeedbackScreenState();
}

class _FeedbackScreenState extends State<FeedbackScreen> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController nameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  late String? name;
  @override
  void initState() {
    nameController.text = LoginModel.name ?? '';
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Feedback & Complaints',
      body: Column(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(15.0),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const FAQAnonymousSwitch(),
                    Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Consumer<FAQProvider>(
                            builder: (context, provider, _) {
                              return FAQTextfieldWidget(
                                enabled: !provider.isAnonymous,
                                readOnly: true,
                                controller: nameController,
                                labelText: 'Name',
                              );
                            },
                          ),
                          const SizedBox(height: 10),
                          FAQTextfieldWidget(
                            controller: _titleController,
                            labelText: 'Title',
                            textCapitalization: TextCapitalization.sentences,
                            validator: Validator.text,
                          ),
                          // _textFormFieldWidget(
                          //     title: 'Title',
                          //     controller: _titleController,
                          //     validator: Validator.text,
                          //     keyboardType: TextInputType.text),
                          const SizedBox(height: 15),
                          FAQDescriptionField(
                            controller: _descriptionController,
                            hintText: 'Description',
                            textCapitalization: TextCapitalization.sentences,
                            validator: Validator.text,
                          ),
                          const SizedBox(height: 15),
                          // Align(
                          //     alignment: Alignment.centerLeft,
                          //     child: IconButton(
                          //         onPressed: () {
                          //           _addImageDialog();
                          //         },
                          //         icon: const Icon(Icons.attachment))),
                          const ImageUploadSection()
                        ],
                      ),
                    ),
                    // const SizedBox(height: 30),
                  ],
                ),
              ),
            ),
          ),
          Consumer<FAQProvider>(
            builder: (context, provider, child) {
              return Padding(
                padding: const EdgeInsets.fromLTRB(15.0, 20.0, 15.0, 30.0),
                child: provider.isLoading
                    ? const Center(
                        child: CircularProgressIndicator.adaptive(),
                      )
                    : Row(
                        children: [
                          Expanded(
                            child: ButtonWidget(
                                textStyle: tsS16FFFFF,
                                title: 'Submit',
                                onPressed: () {
                                  if (_formKey.currentState!.validate()) {
                                    hideKeyboard(context);
                                    provider.submitFeedback(
                                        context: context,
                                        title: _titleController.text,
                                        description:
                                            _descriptionController.text);
                                  }
                                }),
                          ),
                          const SizedBox(width: 5),
                          Container(
                            padding: const EdgeInsets.all(3),
                            decoration: const BoxDecoration(
                                color: Color(0xffCBCBCB),
                                shape: BoxShape.circle),
                            child: IconButton(
                                onPressed: () {
                                  _addImageDialog();
                                },
                                icon: const Icon(Icons.attachment)),
                          ),
                          const SizedBox(width: 5),
                        ],
                      ),
              );
            },
          ),
        ],
      ),
    );
  }

  _addImageDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        FAQProvider provider = Provider.of<FAQProvider>(context, listen: false);
        return AlertDialog(
          title: const Text('Options'),
          content: SingleChildScrollView(
            child: ListBody(
              children: [
                GestureDetector(
                    child: const Text('Capture Image From Camera'),
                    onTap: () {
                      Navigator.pop(context);
                      provider.pickImageFromCamera(context: context);
                    }),
                const Padding(padding: EdgeInsets.all(10)),
                GestureDetector(
                    child: const Text('Take Image From Gallery'),
                    onTap: () {
                      Navigator.pop(context);
                      provider.pickImageFromGallery(context: context);
                    }),
              ],
            ),
          ),
        );
      },
    );
  }

  // Widget _textFormFieldWidget(
  //     {required String title,
  //     String? hintText,
  //     required TextEditingController controller,
  //     required var validator,
  //     required TextInputType keyboardType}) {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       Text(title, style: tsS14w400c30292F),
  //       SizedBox(height: h * 4),
  //       TextFieldWidget(
  //         controller: controller,
  //         hintText: hintText,
  //         hintStyle: tsS14w400c9F9F9F,
  //         textStyle: tsS14w400c30292F,
  //         keyboardType: keyboardType,
  //         validator: validator,
  //         contentPadding:
  //             EdgeInsets.symmetric(vertical: h * 11, horizontal: w * 11),
  //       ),
  //       SizedBox(height: h * 15),
  //     ],
  //   );
  // }
}
