import 'package:flutter/material.dart';
import 'package:e8_hr_portal/model/logged_in_user.dart';
import 'package:e8_hr_portal/provider/settings_provider.dart';
import 'package:provider/provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: InkWell(
          onTap: () => Navigator.pop(context),
          child: const Icon(
            Icons.arrow_back_ios,
            color: Colors.white,
            size: 20,
          ),
        ),
        title: const Text('Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(15.0),
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(5.0),
              boxShadow: [
                BoxShadow(
                  blurRadius: 10,
                  offset: const Offset(2, 2),
                  color: Colors.black.withOpacity(0.16),
                ),
              ],
            ),
            child: Consumer<SettingsProvider>(builder: (context, provider, _) {
              return SwitchListTile(
                title: const Text('Work from home'),
                value: LoggedInUser.isWorkFromHome,
                onChanged: provider.changeWorkStatus,
              );
            }),
          ),
        ],
      ),
    );
  }
}
