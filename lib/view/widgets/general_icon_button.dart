import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/colors.dart';

class GeneralIconButton extends StatelessWidget {
  final double width;
  final double height;
  final void Function()? onPressed;
  final bool isDisabled;
  final bool isDisabledColor;
  final TextStyle? textStyle;
  final Widget icon;
  final Widget label;
  final Color? foregroundColor;
  const GeneralIconButton(
      {required this.height,
      required this.width,
      required this.onPressed,
      required this.icon,
      required this.label,
      this.isDisabled = false,
      this.isDisabledColor = false,
      this.textStyle,
      this.foregroundColor,
      super.key});

  @override
  Widget build(BuildContext context) {
    // Size size = MediaQuery.of(context).size;
    return Container(
      decoration: BoxDecoration(
        gradient: isDisabledColor || isDisabled
            ? null
            : LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                stops: const [0.0, 1.0],
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.secondary,
                ],
              ),
        color: isDisabled || isDisabledColor
            ? ThemeColors.disabledButtonColor
            : null,
        borderRadius: BorderRadius.circular(50),
      ),
      child: ElevatedButton.icon(
        onPressed: isDisabled ? () {} : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50),
          ),
          minimumSize: Size(width, height),
          foregroundColor: foregroundColor,
          textStyle: textStyle,
        ),
        icon: icon,
        label: label,
      ),
    ); //372x56
  }
}
