import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/colors.dart';

class GeneralButton extends StatelessWidget {
  final String title;
  final double width;
  final double height;
  final void Function()? onPressed;
  final bool isDisabled;
  final bool isDisabledColor;
  final TextStyle? textStyle;
  const GeneralButton(
      {required this.title,
      required this.height,
      required this.width,
      required this.onPressed,
      this.isDisabled = false,
      this.isDisabledColor = false,
      this.textStyle,
      super.key});

  @override
  Widget build(BuildContext context) {
    // Size size = MediaQuery.of(context).size;
    return Container(
      decoration: BoxDecoration(
        gradient: isDisabledColor || isDisabled
            ? null
            : LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                stops: const [0.0, 1.0],
                colors: [ThemeColors.primaryColor, ThemeColors.primaryColor],
              ),
        color: isDisabled || isDisabledColor
            ? ThemeColors.disabledButtonColor
            : null,
        borderRadius: BorderRadius.circular(50),
      ),
      child: ElevatedButton(
        onPressed: isDisabled ? () {} : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50),
          ),
          minimumSize: Size(width, height),
          // fixedSize: Size(size.width * 0.872, size.height * 0.0689),
        ),
        child: Text(
          title,
          style: textStyle,
        ),
      ),
    ); //372x56
  }
}
