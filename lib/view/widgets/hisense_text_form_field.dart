import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';

class HisenseTextFormField extends StatelessWidget {
  final TextEditingController controller;
  final bool enabled;
  final String? labelText;
  final String? hintText;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final TextCapitalization textCapitalization;
  final bool obscureText;
  final Color borderColor;
  final InputBorder? border;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final FocusNode? focusNode;
  final String? error;
  final void Function()? onEditingComplete;
  final void Function(String)? onChanged;
  final int? maxLines;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final EdgeInsetsGeometry? contentPadding;
  final List<TextInputFormatter>? inputFormatters;
  final void Function(String)? onFieldSubmitted;
  const HisenseTextFormField({
    super.key,
    required this.controller,
    this.labelText,
    this.contentPadding,
    this.hintText,
    this.enabled = true,
    this.validator,
    this.keyboardType,
    this.textCapitalization = TextCapitalization.words,
    this.obscureText = false,
    this.borderColor = const Color(0xFFDEE7FF),
    this.border,
    this.prefixIcon,
    this.suffixIcon,
    this.focusNode,
    this.onEditingComplete,
    this.onChanged,
    this.maxLines,
    this.error,
    this.textStyle,
    this.hintStyle,
    this.inputFormatters,
    this.onFieldSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      enabled: enabled,
      inputFormatters: inputFormatters,
      keyboardType: keyboardType,
      obscureText: obscureText,
      textCapitalization: textCapitalization,
      focusNode: focusNode,
      onEditingComplete: onEditingComplete,
      onChanged: onChanged,
      onFieldSubmitted: onFieldSubmitted,
      style: textStyle ??
          GoogleFonts.rubik(
            color: const Color(0xFF8391B5),
          ),
      maxLines: obscureText ? 1 : maxLines,
      decoration: InputDecoration(
          fillColor: enabled ? Colors.white : ThemeColors.colorE3E3E3,
          filled: true,
          labelText: labelText,
          hintText: hintText,
          hintStyle: hintStyle,
          contentPadding: contentPadding ??
              EdgeInsets.symmetric(horizontal: w * 11, vertical: h * 15),
          labelStyle: GoogleFonts.rubik(
            color: const Color(0xFF8391B5),
          ),
          border: enabled ? outlineInputBorder() : disabledInputBorder(),
          enabledBorder: enabled ? outlineInputBorder() : disabledInputBorder(),
          focusedBorder: enabled ? outlineInputBorder() : disabledInputBorder(),
          suffixIcon: suffixIcon,
          prefixIcon: prefixIcon,
          errorText: error,
          errorMaxLines: 3),
      validator: validator,
    );
  }

  InputBorder? outlineInputBorder() {
    return OutlineInputBorder(
      borderSide: BorderSide(
        width: 1,
        color: ThemeColors.colorE3E3E3,
      ),
      borderRadius: BorderRadius.circular(5),
    );
  }

  InputBorder? disabledInputBorder() {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(5),
      borderSide: const BorderSide(
        width: 0,
        style: BorderStyle.none,
      ),
    );
  }
}
