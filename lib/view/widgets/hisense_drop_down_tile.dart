import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';

class HisenseDropdownTile<T> extends StatelessWidget {
  final String? title;
  final String? hintText;
  final void Function(T?)? onChanged;
  final List<Widget> Function(BuildContext)? selectedItemBuilder;
  final List<DropdownMenuItem<T>>? items;
  final T? value;
  final String? Function(T?)? validator;
  final String? errorText;
  final bool enabled;
  final TextStyle? errorStyle;
  final TextStyle? titleStyle;
  final TextStyle? hintStyle;
  final TextStyle? labelStyle;
  final TextStyle? style;
  final EdgeInsetsGeometry? contentPadding;
  final bool isMandatory;
  const HisenseDropdownTile(
      {required this.title,
      required this.items,
      required this.onChanged,
      this.hintText,
      this.selectedItemBuilder,
      this.value,
      this.validator,
      this.errorText,
      this.errorStyle,
      this.titleStyle,
      this.enabled = true,
      this.hintStyle,
      this.labelStyle,
      this.style,
      this.contentPadding,
      this.isMandatory = false,
      super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (title != null && title!.isNotEmpty)
          RichText(
              text: TextSpan(
                  text: title,
                  style: titleStyle,
                  children: isMandatory
                      ? [
                          const TextSpan(
                              text: '*', style: TextStyle(color: Colors.red))
                        ]
                      : null)),
        if (title != null && title!.isNotEmpty) SizedBox(height: h * 4),
        HisenseDropdownWidget<T>(
          items: items,
          onChanged: onChanged,
          hintText: hintText,
          selectedItemBuilder: selectedItemBuilder,
          value: value,
          validator: validator,
          errorText: errorText,
          enabled: enabled,
          errorStyle: errorStyle,
          hintStyle: hintStyle,
          labelStyle: labelStyle,
          style: style,
          contentPadding: contentPadding,
        ),
      ],
    );
  }
}

class HisenseDropdownWidget<T> extends StatelessWidget {
  final List<DropdownMenuItem<T>>? items;
  final void Function(T?)? onChanged;
  final T? value;
  final String? hintText;
  final List<Widget> Function(BuildContext)? selectedItemBuilder;
  final String? Function(T?)? validator;
  final String? errorText;
  final bool enabled;
  final TextStyle? errorStyle;
  final String? labelText;
  final TextStyle? labelStyle;
  final TextStyle? style;
  final TextStyle? hintStyle;
  final EdgeInsetsGeometry? contentPadding;
  final void Function()? onTap;

  const HisenseDropdownWidget(
      {required this.items,
      required this.onChanged,
      super.key,
      this.value,
      this.hintText,
      this.selectedItemBuilder,
      this.validator,
      this.errorText,
      this.errorStyle,
      this.labelText,
      this.labelStyle,
      this.style,
      this.hintStyle,
      this.contentPadding,
      this.enabled = true,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<T>(
      isExpanded: true,
      isDense: true,
      iconEnabledColor: Colors.black,
      iconDisabledColor: Colors.black,
      items: items,
      onChanged: onChanged,
      icon: const Icon(Icons.keyboard_arrow_down),
      value: value,
      selectedItemBuilder: selectedItemBuilder,
      style: style,
      onTap: onTap,
      hint: Text(hintText.toString(), style: hintStyle),

      decoration: InputDecoration(
        errorStyle: errorStyle,
        filled: true,
        fillColor: ThemeColors.colorFFFFFF,
        enabled: enabled,
        contentPadding: contentPadding ??
            const EdgeInsets.only(top: 14, bottom: 10, left: 12, right: 12),
        errorText: errorText,
        // hintText: hintText,
        // labelText: labelText,
        // labelStyle: labelStyle,
        // hintStyle: hintStyle,
        isDense: true,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(
            color: ThemeColors.colorE3E3E3,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(
            color: ThemeColors.colorE3E3E3,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(
            color: ThemeColors.colorE3E3E3,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(color: ThemeColors.errorColor, width: 1.3),
        ),
      ),
      // autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: validator,
      focusColor: Colors.white,
      dropdownColor: Colors.white,
    );
  }
}
