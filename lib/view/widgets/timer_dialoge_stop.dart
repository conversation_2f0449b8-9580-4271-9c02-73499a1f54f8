// ignore_for_file: use_build_context_synchronously

import 'package:e8_hr_portal/provider/log_time_provider.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:provider/provider.dart';

class TimerDialogStop extends StatefulWidget {
  final int taskId;
  final bool isFrom;
  const TimerDialogStop(
      {required this.taskId, required this.isFrom, super.key});

  @override
  State<TimerDialogStop> createState() => _TimerDialogStopState();
}

class _TimerDialogStopState extends State<TimerDialogStop> {
  // final TextEditingController _timerCommentController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    // return Dialog(
    //   insetPadding: EdgeInsets.symmetric(horizontal: w * 16),
    //   clipBehavior: Clip.antiAlias,
    //   shape: RoundedRectangleBorder(
    //     borderRadius: BorderRadius.circular(26),
    //   ),
    //   elevation: 0.0,
    //   backgroundColor: ThemeColors.colorFFFFFF,
    //   child: Padding(
    //     padding: EdgeInsets.fromLTRB(w * 5, h * 5, w * 5, h * 25),
    //     child: Form(
    //       key: _timerDialogekey,
    //       child: Column(
    //         crossAxisAlignment: CrossAxisAlignment.start,
    //         mainAxisSize: MainAxisSize.min,
    //         children: [
    //           Align(
    //             alignment: Alignment.centerRight,
    //             child: IconButton(
    //               onPressed: () {
    //                 Navigator.pop(context);
    //               },
    //               icon: Icon(
    //                 Icons.close,
    //                 color: ThemeColors.colorD6D6D6,
    //                 size: h * 20,
    //               ),
    //             ),
    //           ),
    //           Align(
    //             alignment: Alignment.center,
    //             child: Text(
    //               "Are you sure\nyou want to stop the timer?",
    //               textAlign: TextAlign.center,
    //               style: tsS16w600c6E7079,
    //             ),
    //           ),
    //           SizedBox(height: h * 25),
    //           Padding(
    //             padding: EdgeInsets.only(left: w * 5, right: w * 5),
    //             child: Row(
    //               mainAxisAlignment: MainAxisAlignment.spaceAround,
    //               children: [
    //                 _cancelButton(
    //                     onPressed: _onCancelled,
    //                     width: w * 100,
    //                     height: h * 40,
    //                     title: "Cancel",
    //                     textStyle: tsS14w500c475366),
    //                 _stopButton(
    //                   title: "Stop",
    //                   height: h * 40,
    //                   textStyle: tsS14w600cFFFFFF,
    //                   width: w * 100,
    //                   onPressed: () {
    //                     if (_timerDialogekey.currentState!.validate()) {
    //                       _onSubmitted(
    //                         leaveId: widget.taskId,
    //                       );
    //                     }
    //                   },
    //                 ),
    //               ],
    //             ),
    //           ),
    //         ],
    //       ),
    //     ),
    //   ),
    // );

    return Container(
      height: h * 249,
      decoration: BoxDecoration(
        // color: ThemeColors.colorFFFFFF,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          SizedBox(height: h * 14),
          Container(
            height: h * 5,
            width: w * 134,
            decoration: BoxDecoration(
              color: ThemeColors.colorEAEBED,
              borderRadius: BorderRadius.circular(100),
            ),
          ),
          SizedBox(height: h * 44),
          Text("Are you sure", style: tsS22W5161616),
          SizedBox(height: h * 2),
          Text("you want to stop the timer ? ", style: tsS16w500c9F9F9F),
          SizedBox(height: h * 33),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: w * 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GeneralButton(
                  height: h * 50,
                  width: w * 164,
                  isDisabledColor: true,
                  title: "No",
                  textStyle: tsS18w600cFFFFFF,
                  onPressed: _onCancelled,
                ),
                GeneralButton(
                  height: h * 50,
                  width: w * 164,
                  title: "Yes",
                  textStyle: tsS18w600cFFFFFF,
                  // color: ThemeColors.secondaryColor,
                  onPressed: () {
                    _onSubmitted(
                      leaveId: widget.taskId,
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _onCancelled() {
    Navigator.pop(context);
  }

  void _onSubmitted({required int leaveId}) async {
    final timerProvider = context.read<LogTimeProvider>();
    // LogTimeProvider logTimeProvider =
    //     Provider.of<LogTimeProvider>(context, listen: false);

    await EasyLoading.show();
    timerProvider.stopTimer();
    if (widget.isFrom == true) {
      Navigator.pop(context);
    }

    await timerProvider.timerPlayPauseStope(
      taskId: widget.taskId,
      statusId: 3,
      context: context,
    );
    await EasyLoading.dismiss();
  }

  // Future<void> _actionForWatch({required MethodChannel channel}) async {
  //   LogTimeProvider provider =
  //       Provider.of<LogTimeProvider>(context, listen: false);
  //   String hours = provider.hours.toString().padLeft(2, '0');
  //   String minutes = provider.minutes.toString().padLeft(2, '0');
  //   String seconds = provider.seconds.toString().padLeft(2, '0');
  //   channel.invokeMethod("flutterToWatch", {
  //     "method": "sendCounterToNative",
  //     "data": "$hours:$minutes:$seconds",
  //     "taskID": "${provider.runningTask.id}",
  //     "project": "${provider.runningTask.project}",
  //     "task": "${provider.runningTask.title}",
  //     "isStopped": provider.isTimerStopped,
  //     "isPaused": provider.isTimerPaused,
  //   });
  // }

  // Widget _cancelButton(
  //     {required VoidCallback onPressed,
  //     required double width,
  //     required double height,
  //     required String title,
  //     required TextStyle textStyle}) {
  //   return InkWell(
  //     onTap: onPressed,
  //     child: Container(
  //       height: 30 * h,
  //       width: 70 * w,
  //       alignment: Alignment.center,
  //       decoration: BoxDecoration(
  //         color: ThemeColors.colorF3F3F9,
  //         borderRadius: BorderRadius.circular(50),
  //       ),
  //       child: Text(
  //         title,
  //         style: textStyle,
  //         textAlign: TextAlign.center,
  //       ),
  //     ),
  //   );
  // }

  // Widget _stopButton(
  //     {required VoidCallback onPressed,
  //     required double width,
  //     required double height,
  //     required String title,
  //     required TextStyle textStyle}) {
  //   return InkWell(
  //     onTap: onPressed,
  //     child: Container(
  //       alignment: Alignment.center,
  //       height: 30 * h,
  //       width: 70 * w,
  //       decoration: BoxDecoration(
  //         color: ThemeColors.colorFB4D3D,
  //         borderRadius: BorderRadius.circular(50),
  //       ),
  //       child: Text(
  //         title,
  //         style: textStyle,
  //         textAlign: TextAlign.center,
  //       ),
  //     ),
  //   );
  // }
}
