import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../util/colors.dart';
import '../../util/size_config.dart';
import '../../util/styles.dart';

class TypeAheadFormFieldWidget<T> extends StatelessWidget {
  final String hintText;
  final TextEditingController controller;
  final Widget Function(BuildContext, T) itemBuilder;
  final Function(T) onSelected;
  final String? Function(String?)? validator;
  final Widget? suffixIcon;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final TextStyle? textStyle;
  final FutureOr<List<T>?> Function(String) suggestionsCallback;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final FocusNode? focusNode;
  final void Function(String)? onFieldSubmitted;

  const TypeAheadFormFieldWidget({
    required this.controller,
    required this.hintText,
    required this.itemBuilder,
    required this.onSelected,
    required this.suggestionsCallback,
    this.textStyle,
    this.suffixIcon,
    this.validator,
    this.labelStyle,
    this.hintStyle,
    this.keyboardType,
    this.inputFormatters,
    this.focusNode,
    this.onFieldSubmitted,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TypeAheadField<T>(
          controller: controller,
          focusNode: focusNode,
          suggestionsCallback: suggestionsCallback,
          itemBuilder: itemBuilder,
          onSelected: onSelected,
          builder: (context, controller, focusNode) {
            return TextFormField(
              controller: controller,
              focusNode: focusNode,
              style: textStyle,
              keyboardType: keyboardType,
              inputFormatters: inputFormatters,
              onFieldSubmitted: onFieldSubmitted,
              validator: validator,
              decoration: InputDecoration(
                contentPadding:
                    EdgeInsets.symmetric(vertical: h * 14, horizontal: w * 11),
                labelStyle: labelStyle ??
                    GoogleFonts.poppins(
                      color: const Color(0xFF8391B5),
                    ),
                hintStyle: hintStyle ?? tsS14w400454444,
                suffixIcon: suffixIcon,
                fillColor: Colors.white,
                filled: true,
                hintText: hintText,
                floatingLabelStyle: tsS14w400c30292F,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(5),
                  borderSide: BorderSide(
                    color: ThemeColors.colorE3E3E3,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(5),
                  borderSide: BorderSide(
                    color: ThemeColors.colorE3E3E3,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(5),
                  borderSide: BorderSide(
                    color: ThemeColors.colorE3E3E3,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(5),
                  borderSide: const BorderSide(color: Colors.red, width: 1.3),
                ),
              ),
            );
          },
        ),
        SizedBox(height: h * 15),
      ],
    );
  }
}
