import 'package:flutter/material.dart';
import 'package:e8_hr_portal/provider/general_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:provider/provider.dart';

class DownloadingDialog extends StatefulWidget {
  final String url;
  final String fileName;
  final String extention;
  const DownloadingDialog(
      {required this.fileName,
      required this.extention,
      required this.url,
      super.key});
  @override
  State<DownloadingDialog> createState() => _DownloadingDialogState();
}

class _DownloadingDialogState extends State<DownloadingDialog> {
  @override
  void initState() {
    GeneralProvider provider =
        Provider.of<GeneralProvider>(context, listen: false);
    String date =
        formatDateFromDate(dateTime: DateTime.now(), format: "dd-MMM-yy");
    provider.pdfDownload(
        fileurl: widget.url,
        fileName: "${widget.fileName} $date${widget.extention}",
        extension: widget.extention,
        context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.black,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const CircularProgressIndicator.adaptive(),
          SizedBox(height: h * 20),
          Consumer<GeneralProvider>(
            builder: (context, provider, _) {
              return Text(
                "Downloading: ${provider.progressing}",
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 17,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
