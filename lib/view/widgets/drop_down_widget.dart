import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/styles.dart';

class DropdownWidget<T> extends StatelessWidget {
  final List<DropdownMenuItem<T>>? items;
  final void Function(T?)? onChanged;
  final T? value;
  final String? hintText;
  final List<Widget> Function(BuildContext)? selectedItemBuilder;
  final String? Function(T?)? validator;
  final String? errorText;
  final bool enabled;
  final TextStyle? errorStyle;
  final String? labelText;
  final TextStyle? labelStyle;
  const DropdownWidget({
    required this.items,
    required this.onChanged,
    super.key,
    this.value,
    this.hintText,
    this.selectedItemBuilder,
    this.validator,
    this.errorText,
    this.errorStyle,
    this.labelText,
    this.labelStyle,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<T>(
      isExpanded: true,
      iconEnabledColor: Colors.black,
      iconDisabledColor: Colors.black,
      items: items,
      onChanged: onChanged,
      icon: const Icon(Icons.keyboard_arrow_down),
      value: value,
      selectedItemBuilder: selectedItemBuilder,
      style: tsS12w400c9F9F9F,
      decoration: InputDecoration(
        errorStyle: errorStyle,
        filled: true,
        fillColor: ThemeColors.colorFFFFFF,
        enabled: enabled,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 12, vertical: 17),
        errorText: errorText,
        hintText: hintText,
        labelText: labelText,
        labelStyle: labelStyle,
        hintStyle: tsS12w400c9F9F9F,
        isDense: true,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(
            color: ThemeColors.colorE3E3E3,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(
            color: ThemeColors.colorE3E3E3,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(
            color: ThemeColors.colorE3E3E3,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: const BorderSide(color: Colors.red, width: 1.3),
        ),
      ),
      // autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: validator,
      focusColor: Colors.white,
      dropdownColor: Colors.white,
    );
  }
}
