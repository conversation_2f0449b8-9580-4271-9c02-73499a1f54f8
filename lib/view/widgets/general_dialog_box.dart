import 'dart:io';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class GeneralConfirmationDialog extends StatelessWidget {
  final TextStyle onYesStyle;
  final TextStyle onNoStyle;
  final VoidCallback onYesPressed;
  final VoidCallback onNoPressed;
  final String? title;
  final String? content;
  final String acceptLabel;
  final String cancelLabel;
  const GeneralConfirmationDialog(
      {required this.onYesStyle,
      required this.onNoStyle,
      required this.onNoPressed,
      required this.onYesPressed,
      required this.acceptLabel,
      required this.cancelLabel,
      this.content,
      this.title,
      super.key});

  @override
  Widget build(BuildContext context) {
    if (Platform.isIOS) {
      return CupertinoAlertDialog(
        title: Text(title ?? ''),
        content: Text(content ?? ''),
        actions: [
          CupertinoDialogAction(
            onPressed: onYesPressed,
            child: Text(
              acceptLabel,
              style: tsS14NormalBlack,
            ),
          ),
          CupertinoDialogAction(
            onPressed: onNoPressed,
            child: Text(
              cancelLabel,
              style: tsS14NormalBlack,
            ),
          ),
        ],
      );
    }
    return AlertDialog(
      title: Text(title ?? ''),
      content: Text(content ?? ''),
      actions: [
        TextButton(
          onPressed: onYesPressed,
          child: Text(
            acceptLabel,
            style: onYesStyle,
          ),
        ),
        TextButton(
          onPressed: onNoPressed,
          child: Text(
            cancelLabel,
            style: onNoStyle,
          ),
        ),
      ],
    );
  }
}
