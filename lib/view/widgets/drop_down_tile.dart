import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/widgets/drop_down_widget.dart';

class DropdownTile<T> extends StatelessWidget {
  final String? title;
  final String? hintText;
  final void Function(T?)? onChanged;
  final List<Widget> Function(BuildContext)? selectedItemBuilder;
  final List<DropdownMenuItem<T>>? items;
  final T? value;
  final String? Function(T?)? validator;
  final String? errorText;
  final bool enabled;
  final TextStyle? errorStyle;
  final TextStyle? titleStyle;
  const DropdownTile(
      {required this.title,
      required this.items,
      required this.onChanged,
      this.hintText,
      this.selectedItemBuilder,
      this.value,
      this.validator,
      this.errorText,
      this.errorStyle,
      this.titleStyle,
      this.enabled = true,
      super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (title != null && title!.isNotEmpty)
          Text(
            title.toString(),
            style: titleStyle,
          ),
        if (title != null && title!.isNotEmpty) SizedBox(height: h * 4),
        DropdownWidget<T>(
          items: items,
          onChanged: onChanged,
          hintText: hintText,
          selectedItemBuilder: selectedItemBuilder,
          value: value,
          validator: validator,
          errorText: errorText,
          enabled: enabled,
          errorStyle: errorStyle,
        ),
        // ),
      ],
    );
  }
}
