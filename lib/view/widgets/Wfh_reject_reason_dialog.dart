import 'package:e8_hr_portal/view/wfh/provider/wfh_requests_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:e8_hr_portal/view/widgets/hisense_text_form_field.dart';
import 'package:provider/provider.dart';
import '../../../util/colors.dart';

class WfhRejectReasonDialog extends StatefulWidget {
  final int leaveId;
  const WfhRejectReasonDialog({required this.leaveId, super.key});

  @override
  State<WfhRejectReasonDialog> createState() => _WfhRejectReasonDialogState();
}

class _WfhRejectReasonDialogState extends State<WfhRejectReasonDialog> {
  final TextEditingController _leaveRejectionReasonController =
      TextEditingController();
  final _leaveRejectionReasonFormKey = GlobalKey<FormState>();
  bool _isSubmitting = false;

  @override
  Widget build(BuildContext context) {
    final pro = Provider.of<WfhRequestsProvider>(context, listen: false);
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: w * 16),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(26),
      ),
      elevation: 0.0,
      backgroundColor: ThemeColors.colorFFFFFF,
      child: Padding(
        padding: EdgeInsets.fromLTRB(w * 20, h * 5, w * 5, h * 25),
        child: Form(
          key: _leaveRejectionReasonFormKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.centerRight,
                child: IconButton(
                  onPressed: () {
                    pro.isRejectingFromSlidable = false;
                    Navigator.pop(context);
                  },
                  icon: Icon(Icons.close,
                      color: ThemeColors.colorD6D6D6, size: h * 20),
                ),
              ),
              SizedBox(height: h * 10),
              Text("Reason For Leave Rejection", style: tsS14w500c6E7079),
              SizedBox(height: h * 6),
              Padding(
                padding: EdgeInsets.only(right: w * 15),
                child: HisenseTextFormField(
                  controller: _leaveRejectionReasonController,
                  hintText: "Enter the reason",
                  hintStyle: tsS12w400c475366,
                  maxLines: 3,
                  validator: Validator.reason,
                  inputFormatters: [],
                ),
              ),
              SizedBox(height: h * 25),
              Padding(
                padding: EdgeInsets.only(left: w * 7, right: w * 21),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _cancelButton(
                        onPressed: () {
                          _onCancelled(pro);
                        },
                        width: w * 140,
                        height: h * 40,
                        title: "Cancel",
                        textStyle: tsS14w500c475366),
                    GeneralButton(
                      title: "Submit",
                      height: h * 40,
                      textStyle: tsS14w500cFFFFFF,
                      width: w * 140,
                      onPressed: () {
                        _onSubmitted(
                            leaveId: widget.leaveId,
                            reason: _leaveRejectionReasonController.text);
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onCancelled(WfhRequestsProvider pro) {
    pro.isRejectingFromSlidable = false;
    Navigator.pop(context);
  }

  void _onSubmitted({required int leaveId, required String reason}) async {
    final pro = Provider.of<WfhRequestsProvider>(context, listen: false);
    print('clicked reject');
    if (_isSubmitting) return; // Prevent multiple taps

    if (_leaveRejectionReasonFormKey.currentState!.validate()) {
      _isSubmitting = true;

      EasyLoading.show();
      try {
        final navigator = Navigator.of(context);
        final wfhRequestsProvider =
            Provider.of<WfhRequestsProvider>(context, listen: false);

        bool isSuccess = await wfhRequestsProvider.rejectWfhRequest(
            requestId: leaveId, reason: reason);

        if (isSuccess) {
          wfhRequestsProvider.currentPageWfhRequest = 0;
          wfhRequestsProvider.pagingControllerWfhRequests?.refresh();
          if (pro.isRejectingFromSlidable == true) {
            navigator.pop();
          } else {
            navigator.pop();
            navigator.pop();
          }
        }
        pro.isRejectingFromSlidable = false;
      } finally {
        _isSubmitting = false;
        EasyLoading.dismiss();
      }
    }
  }

  Widget _cancelButton(
      {required VoidCallback onPressed,
      required double width,
      required double height,
      required String title,
      required TextStyle textStyle}) {
    return Container(
      decoration: BoxDecoration(
        color: ThemeColors.colorF3F3F9,
        borderRadius: BorderRadius.circular(50),
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50),
          ),
          minimumSize: Size(width, height),
          // fixedSize: Size(size.width * 0.872, size.height * 0.0689),
        ),
        child: Text(
          title,
          style: textStyle,
        ),
      ),
    );
  }
}
