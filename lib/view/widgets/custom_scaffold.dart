import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';

class CustomScaffold extends StatelessWidget {
  final String? screenTitle;
  final Widget body;
  final Widget? floatingActionButton;
  final List<Widget>? actions;
  final double? horizontalPadding;
  final bool? avoidBottom;
  final Widget? bottomNavigationBar;
  const CustomScaffold(
      {required this.screenTitle,
      required this.body,
      this.actions,
      this.horizontalPadding,
      this.floatingActionButton,
      this.avoidBottom = true,
      this.bottomNavigationBar,
      super.key});

  @override
  Widget build(BuildContext context) {
    Widget? title;
    if (screenTitle != null && screenTitle!.isNotEmpty) {
      title = FittedBox(
        child: Text(
          screenTitle!,
          style: tsS19w500cFFFFFF,
        ),
      );
    }
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(stops: const [
              0.4,
              0.9
            ], colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.secondary,
            ], begin: Alignment.topLeft, end: Alignment.topRight),
          ),
        ),
        Expanded(
          child: Container(
            // decoration: BoxDecoration(
            //   gradient: LinearGradient(stops: const [
            //     0.4,
            //     0.9
            //   ], colors: [
            //     Theme.of(context).colorScheme.primary,
            //     Theme.of(context).colorScheme.secondary,
            //   ], begin: Alignment.topLeft, end: Alignment.topRight),
            // ),
            child: Scaffold(
              resizeToAvoidBottomInset: avoidBottom,
              backgroundColor: Theme.of(context).colorScheme.primary,
              appBar: AppBar(
                actions: actions,
                iconTheme: IconThemeData(
                  color: ThemeColors.colorFFFFFF,
                ),
                title: title,
                backgroundColor: Colors.transparent,
                elevation: 0,
                toolbarHeight: h * 62,
                titleSpacing: 0,
                centerTitle: false,
              ),
              body: Container(
                width: w * 375,
                padding: EdgeInsets.symmetric(
                    horizontal: horizontalPadding ?? w * 16),
                decoration: BoxDecoration(
                  color: ThemeColors.colorF4F5FA,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: body,
              ),
              floatingActionButton: floatingActionButton,
              bottomNavigationBar: bottomNavigationBar,
            ),
          ),
        ),
      ],
    );
  }
}
