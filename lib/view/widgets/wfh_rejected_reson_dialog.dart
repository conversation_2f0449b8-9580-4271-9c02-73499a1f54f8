import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:e8_hr_portal/view/widgets/hisense_text_form_field.dart';
import 'package:provider/provider.dart';
import '../../../util/colors.dart';

class WfhRectionReasonDialog extends StatefulWidget {
  final int leaveId;
  const WfhRectionReasonDialog({required this.leaveId, super.key});

  @override
  State<WfhRectionReasonDialog> createState() => _WfhRectionReasonDialogState();
}

class _WfhRectionReasonDialogState extends State<WfhRectionReasonDialog> {
  final TextEditingController _leaveRejectionReasonController =
      TextEditingController();
  final _leaveRejectionReasonFormKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: w * 16),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(26),
      ),
      elevation: 0.0,
      backgroundColor: ThemeColors.colorFFFFFF,
      child: Padding(
        padding: EdgeInsets.fromLTRB(w * 20, h * 5, w * 5, h * 25),
        child: Form(
          key: _leaveRejectionReasonFormKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.centerRight,
                child: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Icon(
                    Icons.close,
                    color: ThemeColors.colorD6D6D6,
                    size: h * 20,
                  ),
                ),
              ),
              SizedBox(height: h * 10),
              Text(
                "Reason For Leave Rejection",
                style: tsS14w500c6E7079,
              ),
              SizedBox(height: h * 6),
              Padding(
                padding: EdgeInsets.only(right: w * 15),
                child: HisenseTextFormField(
                  controller: _leaveRejectionReasonController,
                  hintText: "Enter the reason",
                  hintStyle: tsS12w400c475366,
                  maxLines: 3,
                  validator: Validator.reason,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp("[a-zA-Z]"))
                  ],
                ),
              ),
              SizedBox(height: h * 25),
              Padding(
                padding: EdgeInsets.only(left: w * 7, right: w * 21),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _cancelButton(
                        onPressed: _onCancelled,
                        width: w * 140,
                        height: h * 40,
                        title: "Cancel",
                        textStyle: tsS14w500c475366),
                    GeneralButton(
                      title: "Submit",
                      height: h * 40,
                      textStyle: tsS14w500cFFFFFF,
                      width: w * 140,
                      onPressed: () {
                        _onSubmitted(
                            leaveId: widget.leaveId,
                            reason: _leaveRejectionReasonController.text);
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onCancelled() {
    Navigator.pop(context);
  }

  void _onSubmitted({required int leaveId, required String reason}) async {
    if (_leaveRejectionReasonFormKey.currentState!.validate()) {
      EasyLoading.show();
      final navigator = Navigator.of(context);
      final leaveProvider =
          Provider.of<LeaveApplicationProvider>(context, listen: false);
      await leaveProvider.leaveAcceptOrReject(
          leaveId: leaveId,
          acceptOrReject: 2,
          context: context,
          reason: reason);
      leaveProvider.getPendingLeaves();
      // await leaveProvider.getLeaveRequest();
      leaveProvider.refreshLeaveRequest();
      navigator.pop();
      navigator.pop();
      EasyLoading.dismiss();
    }
  }

  Widget _cancelButton(
      {required VoidCallback onPressed,
      required double width,
      required double height,
      required String title,
      required TextStyle textStyle}) {
    return Container(
      decoration: BoxDecoration(
        color: ThemeColors.colorF3F3F9,
        borderRadius: BorderRadius.circular(50),
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50),
          ),
          minimumSize: Size(width, height),
          // fixedSize: Size(size.width * 0.872, size.height * 0.0689),
        ),
        child: Text(
          title,
          style: textStyle,
        ),
      ),
    );
  }
}
