import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/sign_in/sign_in_screen.dart';

class WaitingForApproval extends StatelessWidget {
  const WaitingForApproval({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Your account is not active',
            textAlign: TextAlign.center,
            style: GoogleFonts.rubik(
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'Your account has not been activated.\nPlease try again later.',
            textAlign: TextAlign.center,
          ),
          Align(
            alignment: Alignment.center,
            child: ElevatedButton.icon(
              onPressed: () => PageNavigator.pushAndRemoveUntil(
                context: context,
                route: const SignInScreen(),
              ),
              icon: const Icon(
                Icons.arrow_back,
                color: Color(0xFF1E2138),
              ),
              label: Text(
                'Back to Login',
                style: GoogleFonts.rubik(color: const Color(0xFF1E2138)),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
