import 'package:e8_hr_portal/model/chat_model.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/provider/chat_provider.dart';
import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/chat_bot/widget/chatbot_scaffold.dart';
import 'package:e8_hr_portal/view/chat_bot/widget/loading_animation_dot.dart';
import 'package:e8_hr_portal/view/master/master_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class ChatBotScreen extends StatefulWidget {
  const ChatBotScreen({super.key});

  @override
  State<ChatBotScreen> createState() => _ChatBotScreenState();
}

class _ChatBotScreenState extends State<ChatBotScreen> {
  @override
  void initState() {
    var provider = context.read<ChatProvider>();
    provider.cleatChat();
    provider.chatbotMsg(message: "Hi");
    super.initState();
  }

  void scrollExtent() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    });
  }

  final messageTextContoller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  @override
  Widget build(BuildContext context) {
    return ChatScaffold(
      onTap: () {
        var pro = Provider.of<MasterProvider>(context, listen: false);
        pro.currentIndex = 0;
        PageNavigator.push(context: context, route: const MasterScreen());
      },
      screenTitle: Row(
        children: [
          Container(
            height: 38 * h,
            width: 38 * h,
            margin: const EdgeInsets.only(right: 10),
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage("assets/icons/chat_bot_online.png"),
              ),
            ),
          ),
          const Text(
            'Chatbot',
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: Consumer2<ChatProvider, ProfileProvider>(
                  builder: (context, provider, profile, _) {
                return ListView.separated(
                    // reverse: true,
                    controller: _scrollController,
                    physics: const ClampingScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 10.0, vertical: 20),
                    itemBuilder: (context, index) {
                      ChatModel chat = provider.chatList[index];
                      var chatSend = provider.sendChatBotMsg[index];

                      String time = formatDateFromDate(
                          dateTime: chat.time, format: "hh:mm a");
                      debugPrint("chatbot ${chat.time}");

                      DecorationImage? decorationImage;
                      if (profile.profilePhoto != null) {
                        decorationImage = DecorationImage(
                          image: NetworkImage(profile.profilePhoto.toString()),
                          fit: BoxFit.cover,
                        );
                      }

                      Widget imageWidget = Text(
                        LoginModel.name
                            .toString()
                            .substring(0, 1)
                            .toUpperCase()
                            .toUpperCase(),
                      );

                      if (index == 0) {
                        return staticMsgChatBot(chat: chat, time: time);
                      }
                      if (provider.chatList[index].answer == "Loading") {
                        return loadingAnimation();
                      }

                      return Column(
                        children: [
                          Container(
                            margin: const EdgeInsets.symmetric(vertical: 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(15),
                                        decoration: BoxDecoration(
                                          color: ThemeColors.colorFCD500,
                                          borderRadius: const BorderRadius.only(
                                            topLeft: Radius.circular(10),
                                            topRight: Radius.circular(10),
                                            bottomLeft: Radius.circular(10),
                                          ),
                                        ),
                                        child: Text(
                                          chatSend.toString(),
                                          style: tsS12w400Black,
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      Text(
                                        time,
                                        style: tsS10w400979797,
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  alignment: Alignment.center,
                                  margin: const EdgeInsets.only(left: 5),
                                  height: 35,
                                  width: 35,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: ThemeColors.colorFFFFFF),
                                    color: ThemeColors.colorFCD500,
                                    shape: BoxShape.circle,
                                    image: decorationImage,
                                  ),
                                  child: decorationImage == null
                                      ? imageWidget
                                      : null,
                                ),
                              ],
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.symmetric(vertical: 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  alignment: Alignment.center,
                                  margin: const EdgeInsets.only(right: 5),
                                  height: 35,
                                  width: 35,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: ThemeColors.colorFFFFFF,
                                        width: 2),
                                    color: ThemeColors.colorAD76EE,
                                    shape: BoxShape.circle,
                                    image: const DecorationImage(
                                      image: AssetImage(
                                          "assets/icons/chatbot_chat.png"),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(15),
                                        decoration: BoxDecoration(
                                            color: ThemeColors.colorFFFFFF,
                                            borderRadius:
                                                const BorderRadius.only(
                                                    topLeft:
                                                        Radius.circular(10),
                                                    topRight:
                                                        Radius.circular(10),
                                                    bottomRight:
                                                        Radius.circular(10))),
                                        child: Text(
                                          chat.answer.toString(),
                                          style: tsS12w400Black,
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      Text(
                                        time,
                                        style: tsS10w400979797,
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            ),
                          )
                        ],
                      );
                    },
                    separatorBuilder: (context, index) {
                      return const SizedBox();
                    },
                    itemCount: provider.chatList.length);
              }),
            ),
            textFieldArea()
          ],
        ),
      ),
    );
  }

  Widget textFieldArea() {
    return Container(
      height: kBottomNavigationBarHeight,
      padding: const EdgeInsets.symmetric(horizontal: 15),
      margin: const EdgeInsets.only(bottom: 10),
      child: Consumer<ChatProvider>(builder: (context, provider, _) {
        debugPrint("isLoading ---- ${provider.isLoading}");
        return Row(
          children: <Widget>[
            Expanded(
              child: TextFormField(
                controller: messageTextContoller,
                textCapitalization: TextCapitalization.sentences,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  enabledBorder: OutlineInputBorder(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(12.0),
                    ),
                    borderSide: BorderSide(color: ThemeColors.colorFFFFFF),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(12.0),
                    ),
                    borderSide: BorderSide(color: ThemeColors.colorFFFFFF),
                  ),
                  hintText: 'Type a message to chatbot...',
                  hintStyle: const TextStyle(
                    color: Color(0xFFC2C2C2),
                    fontWeight: FontWeight.w400,
                  ),
                  fillColor: ThemeColors.colorFFFFFF,
                  filled: true,
                ),
              ),
            ),
            InkWell(
              onTap: () async {
                if (messageTextContoller.text.trim().isNotEmpty) {
                  if (provider.isLoading == false) {
                    scrollExtent();

                    await provider.chatbotMsg(
                      message: messageTextContoller.text.trim(),
                    );
                    messageTextContoller.clear();

                    scrollExtent();
                  }
                }
              },
              child: Container(
                margin: const EdgeInsets.only(left: 10),
                height: 50 * h,
                width: 50 * h,
                decoration: BoxDecoration(
                    shape: BoxShape.circle, color: ThemeColors.colorFCD500),
                child: Image.asset(
                  "assets/icons/send-2.png",
                  scale: 2,
                ),
              ),
            )
          ],
        );
      }),
    );
  }

  Widget staticMsgChatBot({required ChatModel chat, required String time}) {
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                alignment: Alignment.center,
                margin: const EdgeInsets.only(right: 5),
                height: 35,
                width: 35,
                decoration: BoxDecoration(
                  border: Border.all(color: ThemeColors.colorFFFFFF, width: 2),
                  color: ThemeColors.colorAD76EE,
                  shape: BoxShape.circle,
                  image: const DecorationImage(
                    image: AssetImage("assets/icons/chatbot_chat.png"),
                  ),
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(15),
                      decoration: BoxDecoration(
                          color: ThemeColors.colorFFFFFF,
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(10),
                              topRight: Radius.circular(10),
                              bottomRight: Radius.circular(10))),
                      child: Text(
                        chat.answer.toString(),
                        style: tsS12w400Black,
                      ),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      time,
                      style: tsS10w400979797,
                    ),
                  ],
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  Widget loadingAnimation() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.center,
            margin: const EdgeInsets.only(right: 5),
            height: 35,
            width: 35,
            decoration: BoxDecoration(
              border: Border.all(color: ThemeColors.colorFFFFFF, width: 2),
              color: ThemeColors.colorAD76EE,
              shape: BoxShape.circle,
              image: const DecorationImage(
                image: AssetImage("assets/icons/chatbot_chat.png"),
              ),
            ),
          ),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flex(
                  direction: Axis.horizontal,
                  children: [LoadingAnimationDotindicator()],
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
