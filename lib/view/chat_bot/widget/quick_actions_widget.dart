import 'package:flutter/material.dart';
import '../../../util/colors.dart';
import '../../../util/size_config.dart';

class QuickActionsWidget extends StatelessWidget {
  final List<String> actions;
  final Function(String) onActionSelected;

  const QuickActionsWidget({
    super.key,
    required this.actions,
    required this.onActionSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: actions.map((action) => _buildActionChip(action)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildActionChip(String action) {
    return GestureDetector(
      onTap: () => onActionSelected(action),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: ThemeColors.colorFCD500.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: ThemeColors.colorFCD500.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getActionIcon(action),
              size: 16,
              color: ThemeColors.colorFCD500,
            ),
            const SizedBox(width: 6),
            Text(
              action,
              style: TextStyle(
                color: ThemeColors.colorFCD500,
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getActionIcon(String action) {
    String lowerAction = action.toLowerCase();
    
    if (lowerAction.contains('leave')) {
      return Icons.event_busy;
    } else if (lowerAction.contains('work from home') || lowerAction.contains('wfh')) {
      return Icons.home_work;
    } else if (lowerAction.contains('balance')) {
      return Icons.account_balance_wallet;
    } else if (lowerAction.contains('holiday')) {
      return Icons.celebration;
    } else if (lowerAction.contains('meeting')) {
      return Icons.meeting_room;
    } else if (lowerAction.contains('expense')) {
      return Icons.receipt_long;
    } else {
      return Icons.touch_app;
    }
  }
}
