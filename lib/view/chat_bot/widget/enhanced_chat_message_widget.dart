import 'package:flutter/material.dart';
import '../../../model/ai_chat_models.dart';
import '../../../provider/enhanced_chat_provider.dart';
import '../../../util/colors.dart';
import '../../../util/date_formatter.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';

class EnhancedChatMessageWidget extends StatelessWidget {
  final EnhancedChatMessage message;
  final VoidCallback? onRetry;

  const EnhancedChatMessageWidget({
    super.key,
    required this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: message.isUser 
            ? MainAxisAlignment.end 
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) _buildBotAvatar(),
          if (!message.isUser) const SizedBox(width: 8),
          Flexible(
            child: _buildMessageBubble(context),
          ),
          if (message.isUser) const SizedBox(width: 8),
          if (message.isUser) _buildUserAvatar(),
        ],
      ),
    );
  }

  Widget _buildBotAvatar() {
    return Container(
      width: 32,
      height: 32,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        image: DecorationImage(
          image: AssetImage("assets/icons/chat_bot_online.png"),
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  Widget _buildUserAvatar() {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: ThemeColors.colorFCD500,
      ),
      child: const Icon(
        Icons.person,
        color: Colors.white,
        size: 20,
      ),
    );
  }

  Widget _buildMessageBubble(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.75,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: _getBubbleColor(),
        borderRadius: BorderRadius.circular(18),
        border: _getBorder(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMessageContent(),
          if (_shouldShowTimestamp()) ...[
            const SizedBox(height: 4),
            _buildTimestamp(),
          ],
          if (_shouldShowActionButtons()) ...[
            const SizedBox(height: 8),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }

  Widget _buildMessageContent() {
    switch (message.messageType) {
      case ChatMessageType.loading:
        return _buildLoadingContent();
      case ChatMessageType.processing:
        return _buildProcessingContent();
      case ChatMessageType.success:
        return _buildSuccessContent();
      case ChatMessageType.error:
        return _buildErrorContent();
      default:
        return _buildTextContent();
    }
  }

  Widget _buildTextContent() {
    return Text(
      message.content,
      style: TextStyle(
        color: message.isUser ? Colors.white : Colors.black87,
        fontSize: 14,
      ),
    );
  }

  Widget _buildLoadingContent() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[600]!),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          message.content,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildProcessingContent() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(ThemeColors.colorFCD500),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          message.content,
          style: const TextStyle(
            color: Colors.black87,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildSuccessContent() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 2),
          child: Icon(
            Icons.check_circle,
            color: Colors.green[600],
            size: 16,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            message.content,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorContent() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 2),
          child: Icon(
            Icons.error_outline,
            color: Colors.red[600],
            size: 16,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            message.content,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimestamp() {
    return Text(
      formatDateFromDate(dateTime: message.timestamp, format: "hh:mm a"),
      style: TextStyle(
        color: message.isUser 
            ? Colors.white.withValues(alpha: 0.7)
            : Colors.grey[600],
        fontSize: 11,
      ),
    );
  }

  Widget _buildActionButtons() {
    if (message.messageType == ChatMessageType.error && onRetry != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextButton.icon(
            onPressed: onRetry,
            icon: const Icon(Icons.refresh, size: 16),
            label: const Text('Retry'),
            style: TextButton.styleFrom(
              foregroundColor: ThemeColors.colorFCD500,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ],
      );
    }
    return const SizedBox.shrink();
  }

  Color _getBubbleColor() {
    if (message.isUser) {
      return ThemeColors.colorFCD500;
    }
    
    switch (message.messageType) {
      case ChatMessageType.success:
        return Colors.green[50]!;
      case ChatMessageType.error:
        return Colors.red[50]!;
      case ChatMessageType.processing:
        return Colors.blue[50]!;
      case ChatMessageType.welcome:
        return Colors.purple[50]!;
      default:
        return Colors.grey[100]!;
    }
  }

  Border? _getBorder() {
    switch (message.messageType) {
      case ChatMessageType.success:
        return Border.all(color: Colors.green[200]!, width: 1);
      case ChatMessageType.error:
        return Border.all(color: Colors.red[200]!, width: 1);
      case ChatMessageType.processing:
        return Border.all(color: Colors.blue[200]!, width: 1);
      case ChatMessageType.welcome:
        return Border.all(color: Colors.purple[200]!, width: 1);
      default:
        return null;
    }
  }

  bool _shouldShowTimestamp() {
    return message.messageType != ChatMessageType.loading &&
           message.messageType != ChatMessageType.processing;
  }

  bool _shouldShowActionButtons() {
    return message.messageType == ChatMessageType.error && onRetry != null;
  }
}
