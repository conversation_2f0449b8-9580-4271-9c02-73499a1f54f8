// ignore_for_file: library_private_types_in_public_api

import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';

class LoadingAnimationDotindicator extends StatefulWidget {
  const LoadingAnimationDotindicator({super.key});

  @override
  _LoadingAnimationDotindicatorState createState() =>
      _LoadingAnimationDotindicatorState();
}

class _LoadingAnimationDotindicatorState
    extends State<LoadingAnimationDotindicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  final int numberOfDots = 3;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2), // Adjust the animation duration
    )..repeat(reverse: true);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 48 * w,
      height: 38 * h,
      padding: const EdgeInsets.all(8),
      decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10),
              topRight: Radius.circular(10),
              bottomRight: Radius.circular(10))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: List.generate(numberOfDots, (index) {
          return AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Dot(
                size: CurvedAnimation(
                      parent: _controller,
                      curve: Interval(
                        (1 / numberOfDots) * index,
                        (1 / numberOfDots) * index + 0.25,
                        curve: Curves.linear,
                      ),
                    ).value *
                    10,
                opacity: CurvedAnimation(
                  parent: _controller,
                  curve: Interval(
                    (1 / numberOfDots) * index,
                    (1 / numberOfDots) * index + 0.25,
                    curve: Curves.linear,
                  ),
                ).value,
              );
            },
          );
        }),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

class Dot extends StatelessWidget {
  final double size;
  final double opacity;

  const Dot({super.key, required this.size, required this.opacity});

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: opacity,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 20),
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: ThemeColors.color979797,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}
