import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/chat_bot/chat_bot_screen.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ChatBotIntroScreen extends StatelessWidget {
  const ChatBotIntroScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Icon(
              Icons.close,
              size: 32,
              color: Colors.black.withOpacity(.5),
            )),
      ),
      body: Padding(
        padding: EdgeInsets.fromLTRB(22 * w, 15 * h, 22 * w, 15 * h),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              children: [
                Align(
                  alignment: Alignment.center,
                  child: Image.asset(
                    "assets/icons/chat-bot-bubble.png",
                    height: 290 * h,
                    width: 310 * w,
                  ),
                ),
                Positioned(
                  top: 80 * h,
                  left: 60 * w,
                  child: Image.asset(
                    "assets/icons/chat-bot.png",
                    height: 212 * h,
                    width: 224 * w,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 20 * h,
            ),
            Align(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    "assets/icons/chat-bot-hand.png",
                    height: 28 * h,
                    width: 28 * w,
                  ),
                  SizedBox(
                    width: 10 * w,
                  ),
                  Text(
                    "Welcome !",
                    style: GoogleFonts.poppins(
                      fontSize: 24 * f,
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                ],
              ),
            ),
            Align(
              alignment: Alignment.center,
              child: Text(
                LoginModel.name.toString(),
                style: GoogleFonts.poppins(
                    fontSize: 34, fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(
              height: 75 * h,
            ),
            Align(
              alignment: Alignment.center,
              child: Text(
                "How can i help you",
                style: GoogleFonts.poppins(
                    fontSize: 20, fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(
              height: 7,
            ),
            Align(
              alignment: Alignment.center,
              child: Text(
                "Start a conversation with chatbot to clarify any confusion you have on our product.",
                style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: ThemeColors.color979797),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: GeneralButton(
        textStyle: GoogleFonts.poppins(
            fontSize: 14, color: Colors.white, fontWeight: FontWeight.w600),
        title: "Start Chat with Chatbot ",
        height: 50 * h,
        width: 350 * w,
        onPressed: () {
          PageNavigator.push(context: context, route: const ChatBotScreen());
        },
      ),
    );
  }
}
