import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../provider/enhanced_chat_provider.dart';
import 'enhanced_chat_screen.dart';

/// Wrapper to ensure EnhancedChatProvider is properly available
class ChatScreenWrapper extends StatelessWidget {
  const ChatScreenWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<EnhancedChatProvider>(
      builder: (context, provider, child) {
        // Ensure provider is available
        if (provider == null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('AI Assistant'),
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            body: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Error Loading AI Assistant',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Si<PERSON><PERSON><PERSON>(height: 8),
                  Text(
                    'Please try again later',
                    style: TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          );
        }
        
        // Provider is available, show the enhanced chat screen
        return const EnhancedChatScreen();
      },
    );
  }
}
