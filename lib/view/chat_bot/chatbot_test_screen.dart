import 'package:flutter/material.dart';
import '../../util/colors.dart';
import 'enhanced_chat_screen.dart';

class ChatbotTestScreen extends StatefulWidget {
  const ChatbotTestScreen({super.key});

  @override
  State<ChatbotTestScreen> createState() => _ChatbotTestScreenState();
}

class _ChatbotTestScreenState extends State<ChatbotTestScreen> {
  @override
  void initState() {
    super.initState();
    // Since configuration is hardcoded, directly navigate to chat
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _navigateToChat();
    });
  }

  void _navigateToChat() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => const EnhancedChatScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI HR Assistant'),
        backgroundColor: ThemeColors.colorFCD500,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            Sized<PERSON>ox(height: 16),
            Text(
              'Loading AI Assistant...',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
