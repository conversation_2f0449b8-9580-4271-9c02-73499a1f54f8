import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_linkify/flutter_linkify.dart';
import 'package:provider/provider.dart';

class ResponsibilitiesScreen extends StatelessWidget {
  const ResponsibilitiesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: 'Responsibilities',
      body: Consumer<ProfileProvider>(builder: (context, provider, _) {
        // if(provider.){}
        return Padding(
          padding: const EdgeInsets.only(top: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              selectRes(provider),
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.only(top: 20),
                  shrinkWrap: true,
                  itemCount: provider.selectResposnible ==
                          SelectResposnible.daily
                      ? provider.dailyItems?.length
                      : provider.selectResposnible == SelectResposnible.weekly
                          ? provider.weeklyItems?.length
                          : provider.monthlyItems?.length,
                  itemBuilder: (context, index) {
                    return Container(
                      // height: h * 80,
                      margin: EdgeInsets.only(bottom: 10 * h),
                      width: w * 343,
                      padding:
                          EdgeInsets.fromLTRB(w * 10, h * 18, w * 10, h * 17),
                      decoration: BoxDecoration(
                        color: ThemeColors.colorFFFFFF,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const ImageIcon(
                            AssetImage('assets/icons/feedback.png'),
                            size: 18,
                          ),
                          SizedBox(
                            width: 15 * w,
                          ),
                          Expanded(
                            child: Linkify(
                              onOpen: (link) {
                                launchURL(link.url);
                              },
                              text: provider.selectResposnible ==
                                      SelectResposnible.daily
                                  ? provider.dailyItems![index].responsibility
                                      .toString()
                                  : provider.selectResposnible ==
                                          SelectResposnible.weekly
                                      ? provider
                                          .weeklyItems![index].responsibility
                                          .toString()
                                      : provider
                                          .monthlyItems![index].responsibility
                                          .toString(),

                              style: tsS14NormalBlack,
                              // overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.start,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              )
            ],
          ),
        );
      }),
    );
  }
}

Widget selectRes(ProfileProvider provider) {
  return Container(
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: Colors.white,
      boxShadow: const [
        BoxShadow(
          offset: Offset(2, 2),
          blurRadius: 10,
          color: Color.fromRGBO(0, 0, 0, 0.16),
        ),
      ],
      borderRadius: BorderRadius.circular(12),
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        InkWell(
          onTap: () {
            provider.selectResposnible = SelectResposnible.daily;
          },
          child: Container(
            alignment: Alignment.center,
            width: 70 * w,
            child: Text(
              "Daily",
              style: provider.selectResposnible == SelectResposnible.daily
                  ? tsS16F9637D
                  : tsS16NormalBlack,
            ),
          ),
        ),
        InkWell(
          onTap: () {
            provider.selectResposnible = SelectResposnible.weekly;
          },
          child: Container(
            alignment: Alignment.centerLeft,
            width: 70 * w,
            child: Text(
              "Weekly",
              style: provider.selectResposnible == SelectResposnible.weekly
                  ? tsS16F9637D
                  : tsS16NormalBlack,
            ),
          ),
        ),
        InkWell(
          onTap: () {
            provider.selectResposnible = SelectResposnible.monthly;
          },
          child: Container(
            alignment: Alignment.centerLeft,
            width: 70 * w,
            child: Text(
              "Monthly",
              style: provider.selectResposnible == SelectResposnible.monthly
                  ? tsS16F9637D
                  : tsS16NormalBlack,
            ),
          ),
        ),
      ],
    ),
  );
}
