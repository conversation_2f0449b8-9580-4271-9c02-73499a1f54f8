import 'package:e8_hr_portal/view/badges/widgets/local_badge_bottomsheet.dart';
import 'package:flutter/material.dart';

import '../../../model/badge_model.dart';
import '../widgets/unlock_badge_bottomsheet.dart';

class UnlockedBadgeBottomSheetUtils {
  static void showBottomSheet(
      {required BuildContext context, required BadgeModel badgeModel}) {
    showModalBottomSheet(
      context: context,
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(12))),
      builder: (builder) => UnLockedBadgeBottomSheet(badgeModel: badgeModel),
    );
  }
}

class LocalBadgeBottomSheetUtils {
  static void showBottomSheet(
      {required BuildContext context,
      required BadgeModel badgeModel,
      required bool isLocked}) {
    showModalBottomSheet(
      context: context,
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(12))),
      builder: (builder) =>
          LocalBadgeBottomSheet(badgeModel: badgeModel, isLocked: isLocked),
    );
  }
}
