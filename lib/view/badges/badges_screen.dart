import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/badge_model.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/badges/widgets/local_badge_bottomsheet.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../provider/badge_provider.dart';
import '../../util/colors.dart';

class BadgesScreen extends StatelessWidget {
  static const route = 'badges_screen/';
  const BadgesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Badges',
      body: Container(
        width: double.infinity,
        padding: const EdgeInsets.only(top: 20),
        decoration: BoxDecoration(
          color: ThemeColors.colorF4F5FA,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
        ),
        child: Column(
          children: [
            _filterSection(),
            SizedBox(height: h * 16),
            _bageBuilder(context),
          ],
        ),
      ),
    );
  }

  Widget _filterSection() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: w * 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Consumer<BadgeProvider>(
            builder: (context, provider, _) {
              return Text(
                "${provider.badgeSelectedFilter} Badges",
                style: GoogleFonts.poppins(
                  fontSize: f * 16,
                  fontWeight: FontWeight.w500,
                ),
              );
            },
          ),
          _filterDropDownButton(),
        ],
      ),
    );
  }

  Widget _filterDropDownButton() {
    return Container(
      // height: h * 26,
      // width: w * 69,
      padding: EdgeInsets.fromLTRB(w * 8, h * 0, w * 11, h * 0),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6), color: Colors.white),
      child: DropdownButtonHideUnderline(
        child: Consumer<BadgeProvider>(
          builder: (context, provider, _) {
            return DropdownButton<String>(
              borderRadius: BorderRadius.circular(6),
              icon: Icon(Icons.keyboard_arrow_down, size: w * 18),
              dropdownColor: Colors.white,
              isDense: true, isExpanded: true, //
              value: provider.badgeSelectedFilter,
              onChanged: (String? value) async {
                provider.badgeSelectedFilter = value!;
                await provider.getBadgesWithFilter(context: context);
              },
              items: provider.badgeFilterList.map((item) {
                return DropdownMenuItem(
                  value: item,
                  child: Text(
                    item,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                );
              }).toList(),
            );
          },
        ),
      ),
    );
  }

  Widget _badgeTile(
      {required BadgeModel badgeModel, required BuildContext context}) {
    String? title = badgeModel.name;
    String? image = badgeModel.image;
    bool isLocked = badgeModel.isLocked ?? true;
    String? description = badgeModel.description;
    int? achiveCount = badgeModel.achieveCount;

    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () {
          // if (!isLocked) {
          _callLocalBadgeBottomSheet(
              context: context, badgeModel: badgeModel, isLocked: isLocked);
          // }
          // else if (!isLocked) {
          //   _callUnlockedBadgeBottomSheet(
          //       context: context, badgeModel: badgeModel);
          // }
        },
        child: Container(
          width: double.infinity,
          // height: 76 * h,
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(horizontal: w * 11, vertical: h * 13),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _circleAvatar(
                image: image ?? '',
                achiveCount: achiveCount,
                isLocked: isLocked,
                // achiveCount: 1,
                // isLocked: false,
              ),
              SizedBox(width: w * 13),
              Expanded(
                child: Opacity(
                  opacity: isLocked ? 0.5 : 1,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title ?? '',
                        style: GoogleFonts.poppins(
                          color: const Color(0xFF181818),
                          fontSize: 12 * f,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        description ?? '',
                        style: GoogleFonts.poppins(
                          color: const Color(0xFF979797),
                          fontSize: 10 * f,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _bageBuilder(BuildContext context) {
    return Consumer<BadgeProvider>(
      builder: (context, provider, _) {
        if (provider.badgeModelList.isEmpty && provider.isLoading) {
          return Column(
            children: [
              Container(
                width: double.infinity,
                alignment: Alignment.center,
                margin: EdgeInsets.symmetric(vertical: h * 150),
                child: const CircularProgressIndicator.adaptive(),
              ),
            ],
          );
        } else if (provider.badgeModelList.isEmpty && !provider.isLoading) {
          return Container(
            width: double.infinity,
            alignment: Alignment.center,
            margin: EdgeInsets.symmetric(vertical: h * 150),
            child: const Text('There is no badge for you..!'),
          );
        }
        return Expanded(
          child: ScrollConfiguration(
            behavior: const ScrollBehavior().copyWith(overscroll: false),
            child: ListView.separated(
              padding:
                  EdgeInsets.only(left: w * 16, right: w * 16, bottom: h * 20),
              itemCount: provider.badgeModelList.length,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                BadgeModel badgeModel = provider.badgeModelList[index];

                return _badgeTile(badgeModel: badgeModel, context: context);
              },
              separatorBuilder: (context, index) => SizedBox(height: h * 15),
            ),
          ),
        );
      },
    );
  }

  Widget _circleAvatar(
      {required String image, int? achiveCount, required bool isLocked}) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Opacity(
          opacity: isLocked ? 0.5 : 1,
          child: Stack(
            alignment: Alignment.topRight,
            children: [
              SizedBox(
                width: 55 * w,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      width: 2,
                      color: const Color(0xFFE6E6E6),
                    ),
                  ),
                  child: CircleAvatar(
                    radius: h * 30,
                    backgroundColor: const Color(0xFFE6E6E6),
                    foregroundImage: CachedNetworkImageProvider(
                      image,
                    ),
                    onForegroundImageError: (exception, stackTrace) {},
                  ),
                ),
              ),
              if (achiveCount != null)
                Align(
                  alignment: Alignment.topRight,
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    alignment: Alignment.center,
                    decoration: const BoxDecoration(
                        color: Color(0xFF5570F1), shape: BoxShape.circle),
                    child: Text(
                      "$achiveCount",
                      textAlign: TextAlign.center,
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 10 * f,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        if (achiveCount == null && isLocked) _lockWidget()
      ],
    );
  }

  Widget _lockWidget() => Container(
        width: 55 * w,
        padding: const EdgeInsets.only(
          left: 8,
          right: 8,
        ),
        alignment: Alignment.center,
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              'assets/icons/lock.svg',
              height: 25 * h,
              width: 25 * w,
            ),
            Text(
              "Locked",
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 10 * f,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );

  void _callLocalBadgeBottomSheet(
      {required BuildContext context,
      required BadgeModel badgeModel,
      required bool isLocked}) {
    showModalBottomSheet(
      context: context,
      isDismissible: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(12))),
      builder: (builder) =>
          LocalBadgeBottomSheet(badgeModel: badgeModel, isLocked: isLocked),
    );
  }

  // void _callUnlockedBadgeBottomSheet(
  //     {required BuildContext context, required BadgeModel badgeModel}) {
  //   showModalBottomSheet(
  //     context: context,
  //     isDismissible: true,
  //     isScrollControlled: true,
  //     backgroundColor: Colors.white,
  //     shape: const RoundedRectangleBorder(
  //         borderRadius: BorderRadius.vertical(top: Radius.circular(12))),
  //     builder: (builder) => UnLockedBadgeBottomSheet(badgeModel: badgeModel),
  //   );
  // }
}
