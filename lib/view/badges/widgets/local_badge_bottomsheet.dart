import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/badge_model.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';

class LocalBadgeBottomSheet extends StatelessWidget {
  final BadgeModel badgeModel;
  final bool isLocked;
  const LocalBadgeBottomSheet(
      {super.key, required this.badgeModel, required this.isLocked});

  @override
  Widget build(BuildContext context) {
    String? title = badgeModel.name;
    String? image = badgeModel.image;
    String? description = badgeModel.description;
    return Wrap(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: w * 16),
          decoration: const BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(12),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _topDivider(),
              _circleAvatar(image: image ?? '', isLocked: isLocked),
              _titleAndDescription(
                  description: description ?? "", title: title ?? ""),
              _achievedButton(context: context, isLocked: isLocked),
              SizedBox(height: 52 * h),
            ],
          ),
        ),
      ],
    );
  }

  Widget _topDivider() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 134 * w,
          height: 5,
          alignment: Alignment.center,
          margin: EdgeInsets.only(top: h * 14, bottom: h * 24),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100),
              color: const Color(0xffeaebed)
              // color: Colors.red),
              ),
        ),
      ],
    );
  }

  Widget _circleAvatar({required String image, required bool isLocked}) =>
      Container(
        width: 98 * w,
        margin: EdgeInsets.only(bottom: h * 11),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            width: w * 4,
            color: const Color(0xFFE6E6E6),
          ),
        ),
        child: CircleAvatar(
          radius: h * 53,
          backgroundColor: Colors.transparent,
          backgroundImage: CachedNetworkImageProvider(
            image,
          ),
          onBackgroundImageError: (exception, stackTrace) {},
        ),
      );

  Widget _titleAndDescription(
          {required String title, required String description}) =>
      Column(
        children: [
          Text(
            title,
            style: GoogleFonts.poppins(
              color: const Color(0xFF181818),
              fontSize: f * 22,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 36.0 * w),
            child: Text(
              description,
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                color: const Color(0xFF979797),
                fontSize: 12 * f,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          SizedBox(height: 28 * h),
        ],
      );

  Widget _achievedButton(
          {required BuildContext context, required bool isLocked}) =>
      Material(
        color: !isLocked ? ThemeColors.color0CA726 : ThemeColors.colorCBCBCB,
        borderRadius: BorderRadius.circular(50),
        child: InkWell(
          borderRadius: BorderRadius.circular(50),
          onTap: () => Navigator.pop(context),
          child: Container(
            height: h * 50,
            width: double.infinity,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (!isLocked)
                  Image.asset(
                    "assets/icons/tick.png",
                    color: Colors.white,
                    height: 24 * h,
                    width: 24 * w,
                  )
                else
                  SvgPicture.asset(
                    'assets/icons/lock.svg',
                    height: 25 * h,
                    width: 25 * w,
                    colorFilter:
                        const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                  ),
                SizedBox(
                  width: 7 * w,
                ),
                if (!isLocked)
                  Text(
                    'Achieved ',
                    textAlign: TextAlign.center,
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: f * 16,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                else
                  Text(
                    'Locked ',
                    textAlign: TextAlign.center,
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: f * 16,
                      fontWeight: FontWeight.w600,
                    ),
                  )
              ],
            ),
          ),
        ),
      );
}
