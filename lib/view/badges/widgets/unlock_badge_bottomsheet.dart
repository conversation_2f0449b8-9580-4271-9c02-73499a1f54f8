import 'dart:io';
import 'dart:typed_data';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/provider/badge_provider.dart';
import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/master/master_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lottie/lottie.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import '../../../helper/button_widget.dart';
import '../../../model/badge_model.dart';
import '../../../util/styles.dart';

class UnLockedBadgeBottomSheet extends StatefulWidget {
  final BadgeModel badgeModel;
  const UnLockedBadgeBottomSheet({super.key, required this.badgeModel});

  @override
  State<UnLockedBadgeBottomSheet> createState() =>
      _UnLockedBadgeBottomSheetState();
}

class _UnLockedBadgeBottomSheetState extends State<UnLockedBadgeBottomSheet> {
  final ScreenshotController _screenshotController = ScreenshotController();

  @override
  Widget build(BuildContext context) {
    String? title = widget.badgeModel.name;
    String? image = widget.badgeModel.image;
    return Wrap(
      children: [
        Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(12),
            ),
          ),
          child: Column(
            children: [
              Stack(
                children: [
                  Screenshot(
                    controller: _screenshotController,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Lottie.asset('assets/lottie/success_lottie.json',
                              animate: true,
                              repeat: true,
                              width: double.infinity),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(height: 37 * h),
                              Column(
                                children: [
                                  SizedBox(height: 30 * h),
                                  _circleAvatarWithTitle(
                                    image: image ?? '',
                                    title: title ?? '',
                                  ),
                                  _newBadgeEarnedText(),
                                  _congratulationText(),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  Align(alignment: Alignment.topCenter, child: _topDivider()),
                ],
              ),
              _shareButton(context: context),
              const SizedBox(height: 11),
              _backToHomeButton(context: context),
              SizedBox(height: 39 * h),
            ],
          ),
        ),
      ],
    );
  }

  Widget _topDivider() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 134 * w,
          height: 5,
          alignment: Alignment.center,
          margin: EdgeInsets.only(top: h * 14, bottom: h * 0),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100),
              color: const Color(0xffeaebed)
              // color: Colors.red),
              ),
        ),
      ],
    );
  }

  Widget _circleAvatarWithTitle({
    required String image,
    required String title,
  }) =>
      Column(
        children: [
          Container(
            margin: EdgeInsets.only(bottom: h * 9),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                width: w * 4,
                color: const Color(0xFFE6E6E6),
              ),
            ),
            child: CircleAvatar(
              radius: h * 57,
              backgroundColor: const Color(0xfffed800),
              foregroundImage: CachedNetworkImageProvider(
                image,
              ),
              onForegroundImageError: (exception, stackTrace) {},
            ),
          ),
          Text(
            title,
            style: GoogleFonts.poppins(
              color: const Color(0xFF181818),
              fontSize: f * 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: h * 51),
        ],
      );

  Widget _newBadgeEarnedText() => Column(
        children: [
          Text(
            'New Badge Earned!',
            style: GoogleFonts.poppins(
              color: const Color(0xFF181818),
              fontSize: f * 22,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: h * 6),
        ],
      );

  Widget _congratulationText() => Column(
        children: [
          Text(
            'Congratulations! \nyou just achieved a new badge',
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              color: const Color(0xFF979797),
              fontSize: f * 12,
              fontWeight: FontWeight.w400,
              height: 0,
            ),
          ),
          SizedBox(height: h * 44),
        ],
      );

  Widget _shareButton({required BuildContext context}) => Padding(
        padding: EdgeInsets.symmetric(horizontal: w * 16),
        child: ButtonWidget(
          title: 'Share',
          textStyle: tsS16w600cFFFFFF,
          onPressed: () async => _shareScreenShot(),
        ),
      );

  Widget _backToHomeButton({required BuildContext context}) => Padding(
        padding: EdgeInsets.symmetric(horizontal: w * 16),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(50),
          child: InkWell(
            borderRadius: BorderRadius.circular(50),
            onTap: () => _backToHomeScreen(),
            child: Container(
              height: h * 50,
              width: double.infinity,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(50),
              ),
              child: Text(
                'Back to home ',
                textAlign: TextAlign.center,
                style: GoogleFonts.poppins(
                  color: const Color(0xFFCFCFCF),
                  fontSize: f * 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      );

  Future<void> _shareScreenShot() async {
    BadgeProvider provider = Provider.of<BadgeProvider>(context, listen: false);
    _screenshotController
        .capture(delay: const Duration(milliseconds: 10), pixelRatio: 10)
        .then((capturedImage) async {
      if (capturedImage != null) {
        File file = await uint8ListToFile(capturedImage, 'Workout.jpg');
        provider.shareScreenShot = XFile(file.path);
        Share.shareXFiles([provider.shareScreenShot!]);
      }
    }).catchError((onError) {
      debugPrint(onError);
    });
  }

  void _backToHomeScreen() {
    MasterProvider provider =
        Provider.of<MasterProvider>(context, listen: false);
    provider.currentIndex = 0;
    Navigator.pushAndRemoveUntil(
        context,
        CupertinoPageRoute(builder: (context) => const MasterScreen()),
        (route) => false);
  }

  Future<File> uint8ListToFile(Uint8List data, String fileName) async {
    // Get the system temp directory.
    final tempDir = await getTemporaryDirectory();
    // Create a temporary file in the temp directory.
    final file = File('${tempDir.path}/$fileName');
    // Write the Uint8List data to the file.
    await file.writeAsBytes(data);
    // Return the file object.
    return file;
  }
}
