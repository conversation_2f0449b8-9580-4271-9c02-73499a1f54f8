import 'dart:io';
import 'package:e8_hr_portal/view/sign_in/widgets/privacy_policy.dart';
import 'package:e8_hr_portal/view/sign_in/widgets/sign_in_with_google_button.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'widgets/sign_in_with_apple_button.dart';

class LoginWithEmailScreen extends StatelessWidget {
  const LoginWithEmailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4F5FA),
      extendBody: true,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: w * 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      "assets/icons/e8_bg_white.png",
                      height: 383 * h,
                      width: 214 * w,
                    ),
                    SizedBox(height: 10 * h),
                    Align(
                      alignment: Alignment.center,
                      child: Text(
                        'Welcome!',
                        style: GoogleFonts.poppins(
                          fontSize: f * 34,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF23262D),
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.center,
                      child: Text(
                        'Let’s Get Started.',
                        style: GoogleFonts.poppins(
                          fontSize: f * 34,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF23262D),
                        ),
                      ),
                    ),
                    SizedBox(height: h * 4),
                    Align(
                      alignment: Alignment.center,
                      child: Text(
                        'Log in to your account to continue.',
                        style: GoogleFonts.poppins(
                          fontSize: f * 14,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xFF9F9F9F),
                        ),
                      ),
                    ),
                    SizedBox(height: h * 20),
                    const SignInWithGoogleButton(),
                    if (Platform.isIOS) ...[
                      SizedBox(height: h * 10),
                      const SignInWithAppleButton(),
                      SizedBox(height: h * 10),
                    ],
                  ],
                ),
              ),
              const PrivacyPolicy()
            ],
          ),
        ),
      ),
    );
  }
}
