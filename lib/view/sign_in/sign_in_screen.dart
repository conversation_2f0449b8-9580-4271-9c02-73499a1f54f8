// ignore_for_file: unused_import, unused_element, use_build_context_synchronously
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/helper/icon_button_widget.dart';
import 'package:e8_hr_portal/helper/or_devider.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/provider/sign_in_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/forgot_password/forgot_password.dart';
import 'package:e8_hr_portal/view/sign_in/login_with_email_screen.dart';
import 'package:provider/provider.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  _SignInScreenState createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  late TextEditingController _emailController;
  late TextEditingController _passwordController;
  // late SignInProvider _signInProvider;
  // final FocusNode _passwordFocusNode = FocusNode();
  // bool _hidePassword = true;
  @override
  void initState() {
    _emailController = TextEditingController();
    _passwordController = TextEditingController();
    //  _signInProvider = Provider.of<SignInProvider>(context, listen: false);
    super.initState();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email'],
    // No need to specify clientId for iOS - it will use the one from GoogleService-Info.plist
  );
  GoogleSignInAccount? _currentUser;
  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.of(context).size.height;
    // double kStatusBarHeight = MediaQuery.of(context).padding.top;
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Stack(
          children: [
            SizedBox(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              child: Column(
                children: [
                  Image.asset(
                    'assets/images/hisensehrlogo.png',
                    // color: ThemeColors.primaryColor,
                    fit: BoxFit.fill,
                  ),
                ],
              ),
            ),

            SizedBox(height: height * .25),
            Positioned(
              bottom: 0,
              child: Container(
                height: MediaQuery.of(context).size.height * 0.341,
                width: MediaQuery.of(context).size.width,
                decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(30),
                    ),
                    boxShadow: [
                      BoxShadow(
                          offset: Offset(2, 2),
                          blurRadius: 10,
                          color: Color.fromRGBO(0, 0, 0, 0.16))
                    ]),
                child: Consumer<SignInProvider>(
                  builder: (context, provider, _) {
                    return Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        LoginButtonWidget(
                          icon: Image.asset(
                            'assets/icons/google.png',
                            height: 26,
                            width: 26,
                          ),
                          title: const Text('Login'),
                          textStyle: tsS161E2138,
                          color: Colors.white,
                          isLoading: provider.isLoading,
                          onPressed: () async {
                            try {
                              await _googleSignIn.signOut();
                              _currentUser = await _googleSignIn.signIn();
                              if (_currentUser != null) {
                                String email = _currentUser!.email;

                                provider.googleLogin(
                                    email: email, context: context);
                              }
                            } catch (e) {
                              debugPrint('Error signing in $e');
                            }
                          },
                        ),
                        LoginButtonWidget(
                          title: const Text('Login'),
                          icon: Image.asset(
                            'assets/icons/mail.png',
                            color: Colors.black87,
                            height: 26,
                            width: 26,
                          ),
                          textStyle: tsS161E2138,
                          color: Colors.white,
                          isLoading: provider.isLoading,
                          onPressed: () async {
                            PageNavigator.push(
                                context: context,
                                route: const LoginWithEmailScreen());
                          },
                        ),
                        if (Platform.isIOS)
                          LoginButtonWidget(
                            title: const Text('Login'),
                            icon: Image.asset(
                              'assets/icons/apple.png',
                              height: 26,
                              width: 26,
                            ),
                            textStyle: tsS161E2138,
                            color: Colors.white,
                            isLoading: provider.isLoading,
                            onPressed: () async {
                              try {
                                provider.signInWithApple(
                                  context,
                                );
                              } catch (e) {
                                debugPrint('Error signing in $e');
                              }
                            },
                          ),
                      ],
                    );
                  },
                ),
              ),
            )
            //  _loginForm(),

            // TextButton(
            //   onPressed: () => PageNavigator.push(
            //     context: context,
            //     route: ForgotPassword(),
            //   ),
            //   style: TextButton.styleFrom(
            //     foregroundColor: const Color(0xFF1E2138),
            //     textStyle: GoogleFonts.rubik(
            //       fontSize: 14,
            //       fontWeight: FontWeight.w500,
            //     ),
            //   ),
            //   child: const Text('Forgot Password?'),
            // ),
            // const SizedBox(height: 10),
            // RichText(
            //   textAlign: TextAlign.center,
            //   text: TextSpan(children: [
            //     TextSpan(
            //       text: 'Don\'t have an account? ',
            //       style: GoogleFonts.rubik(
            //         fontSize: 12,
            //         color: const Color(0xFF1E2138),
            //       ),
            //     ),
            //     TextSpan(
            //       text: 'Sign Up',
            //       recognizer: TapGestureRecognizer()
            //         ..onTap = _gotoSignUpScreen,
            //       style: GoogleFonts.rubik(
            //         fontWeight: FontWeight.w500,
            //         fontSize: 12,
            //         color: const Color(0xFF1E2138),
            //       ),
            //     ),
            //   ]),
            // ),
            // const ORDevider(),
            //SocialSignInButtons(_signInProvider),
          ],
        ),
      ),
    );
  }

  // Widget _loginForm() {
  //   return Form(
  //     key: _formKey,
  //     child: Column(
  //       children: [
  //         TextFieldWidget(
  //           controller: _emailController,
  //           hintText: 'Email',
  //           keyboardType: TextInputType.emailAddress,
  //           prefixIcon: const Padding(
  //             padding: EdgeInsets.all(13.0),
  //             child: ImageIcon(
  //               AssetImage('assets/icons/mail.png'),
  //             ),
  //           ),
  //           validator: Validator.email,
  //           textCapitalization: TextCapitalization.none,
  //           onEditingComplete: () =>
  //               FocusScope.of(context).requestFocus(_passwordFocusNode),
  //         ),
  //         const SizedBox(height: 10),
  //         TextFieldWidget(
  //           controller: _passwordController,
  //           hintText: 'Password',
  //           prefixIcon: const Padding(
  //             padding: EdgeInsets.all(13.0),
  //             child: ImageIcon(
  //               AssetImage('assets/icons/password.png'),
  //             ),
  //           ),
  //           suffixIcon: InkWell(
  //             onTap: () => setState(() => _hidePassword = !_hidePassword),
  //             child: Padding(
  //               padding: const EdgeInsets.all(13.0),
  //               child: ImageIcon(
  //                 _hidePassword
  //                     ? const AssetImage('assets/icons/hidden.png')
  //                     : const AssetImage('assets/icons/show.png'),
  //               ),
  //             ),
  //           ),
  //           validator: (value) => Validator.textSpecific(value!, 'password'),
  //           textCapitalization: TextCapitalization.none,
  //           obscureText: _hidePassword,
  //           focusNode: _passwordFocusNode,
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // Widget _signInWithApple() {
  //   if (!Platform.isIOS) {
  //     return Container();
  //   }
  //   return Padding(
  //     padding: const EdgeInsets.only(top: 15),
  //     child: IconButtonWidget(
  //       margin: const EdgeInsets.symmetric(horizontal: 20),
  //       onPressed: () => _signInProvider.signInWithApple(context),
  //       icon: Image.asset(
  //         'assets/icons/apple.png',
  //         height: 26,
  //         width: 26,
  //       ),
  //       label: 'Sign In with Apple',
  //     ),
  //   );
  // }
}

class LoginButtonWidget extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget? title;
  final Widget? child;
  final Color? color;
  final Widget? icon;
  final bool isLoading;
  final TextStyle? textStyle;
  final EdgeInsets? margin;
  const LoginButtonWidget({
    super.key,
    required this.onPressed,
    this.title,
    this.child,
    this.isLoading = false,
    this.margin,
    this.color,
    this.icon,
    this.textStyle,
  });
  @override
  Widget build(BuildContext context) {
    return InkWell(
        onTap: isLoading ? null : onPressed,
        child: Container(
            height: 50,
            width: 50,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.white,
                // border: Border.all(color: ThemeColors.primaryColor),
                boxShadow: const [
                  BoxShadow(
                      blurRadius: 15,
                      offset: Offset(2, 2),
                      color: Color.fromRGBO(0, 0, 0, 0.16))
                ]),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [icon!, title!],
              ),
            ))

        //     ElevatedButton.icon(
        //   icon: icon!,
        //   onPressed: isLoading ? null : onPressed,
        //   style: ElevatedButton.styleFrom(
        //     backgroundColor: color,
        //     minimumSize: const Size(double.infinity, 55),
        //     side: BorderSide(
        //       width: 1.0,
        //       color: ThemeColors.primaryColor,
        //     ),
        //     shape: RoundedRectangleBorder(
        //       borderRadius: BorderRadius.circular(35),
        //     ),
        //   ),
        //   label: isLoading
        //       ? const CupertinoActivityIndicator()
        //       : child ?? Text('$title', style: textStyle),
        // ),
        );
  }
}
