import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
// import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import '../../../provider/sign_in_provider.dart';
import '../../../util/size_config.dart';
import '../../../widgets/loading_widget.dart';

class SignInWithAppleButton extends StatelessWidget {
  const SignInWithAppleButton({super.key});
  @override
  Widget build(BuildContext context) {
    return Consumer<SignInProvider>(
      builder: (context, provider, child) {
        if (provider.isLoadingGoogle) {
          return const LoadingWidget();
        }
        return Padding(
            padding: const EdgeInsets.all(0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(50),
              ),
              child: ElevatedButton.icon(
                icon: Image.asset(
                  'assets/images/apple.png',
                  scale: 2,
                ),
                // onPressed: isLoading ? null : onPressed,
                onPressed: () {
                  onPressed(context);
                },
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  backgroundColor: Colors.transparent,
                  minimumSize: const Size(double.infinity, 55),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(50),
                  ),
                ),
                label:
                    //  isLoading
                    //     ? const CupertinoActivityIndicator()
                    //     :
                    Text(
                  'Sign in with Apple',
                  style: GoogleFonts.poppins(
                    fontSize: f * 14,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                  ),
                ),
              ),
            ));
      },
    );
  }

  onPressed(BuildContext context) async {
    SignInProvider provider =
        Provider.of<SignInProvider>(context, listen: false);
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );

    if (credential.email != null) {
      provider.googleLogin(email: credential.email!, context: context);
    }
  }
}
