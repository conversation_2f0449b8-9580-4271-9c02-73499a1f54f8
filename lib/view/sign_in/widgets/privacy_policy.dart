import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class PrivacyPolicy extends StatelessWidget {
  const PrivacyPolicy({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Expanded(
              child: Align(
                alignment: Alignment.centerRight,
                child: Text(
                  "Privacy Policies",
                  style: GoogleFonts.poppins(
                    fontSize: f * 10,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xff9F9F9F),
                  ),
                ),
              ),
            ),
            Container(
              height: h * 12,
              width: w * 1,
              margin: EdgeInsets.symmetric(horizontal: w * 15),
              color: const Color(0xff9F9F9F),
            ),
            Expanded(
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  "Terms & Condition",
                  style: GoogleFonts.poppins(
                    fontSize: f * 10,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xff9F9F9F),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Align(
          alignment: Alignment.center,
          child: Text(
            "© 2023 Element8, Inc.",
            style: GoogleFonts.poppins(
              fontSize: f * 10,
              fontWeight: FontWeight.w400,
              fontStyle: FontStyle.normal,
              color: const Color(0xff9F9F9F),
            ),
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}
