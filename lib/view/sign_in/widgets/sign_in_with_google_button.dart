import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/provider/sign_in_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:provider/provider.dart';

final GoogleSignIn _googleSignIn = GoogleSignIn(
  scopes: ['email'],
  // No need to specify clientId for iOS - it will use the one from GoogleService-Info.plist
);
GoogleSignInAccount? _currentUser;

class SignInWithGoogleButton extends StatelessWidget {
  const SignInWithGoogleButton({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<SignInProvider>();
    if (provider.isLoadingGoogle) {
      return const LoadingWidget();
    }
    return GoogleButtonWidget(
      color: ThemeColors.primaryColor,
      onPressed: () => onPressed(context),
      title: 'Sign in with Google',
      textStyle: GoogleFonts.poppins(
        fontSize: f * 14,
        fontWeight: FontWeight.w400,
        color: Colors.black,
      ),
    );
  }

  void onPressed(BuildContext context) async {
    final provider = context.read<SignInProvider>();
    try {
      await _googleSignIn.signOut();
      _currentUser = await _googleSignIn.signIn();
      if (_currentUser != null && context.mounted) {
        String email = _currentUser!.email;
        provider.googleLogin(email: email, context: context);
      }
    } catch (e) {
      debugPrint("Error signing in $e");
    }
  }
}
