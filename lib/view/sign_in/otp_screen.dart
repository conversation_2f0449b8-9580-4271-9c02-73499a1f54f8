import 'dart:async';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/provider/sign_in_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/widgets/loading_widget.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:provider/provider.dart';

class OtpScreen extends StatefulWidget {
  const OtpScreen({super.key});

  @override
  State<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends State<OtpScreen> {
  final ValueNotifier<int> _seconds = ValueNotifier(59);

  @override
  void initState() {
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (_seconds.value == 0) {
        _timer?.cancel();
      } else {
        _seconds.value--;
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  final TextEditingController _otpController = TextEditingController();
  Timer? _timer;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.transparent,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
        backgroundColor: const Color(0xFFF8FAFF),
        body: SingleChildScrollView(
            child: Padding(
                padding: EdgeInsets.fromLTRB(w * 30, h * 40, w * 30, 0),
                child: Consumer<SignInProvider>(
                    builder: (context, provider, child) {
                  return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Align(
                          alignment: Alignment.topLeft,
                          child: Text(
                            "Verification",
                            style: GoogleFonts.poppins(
                              fontSize: 36,
                              fontWeight: FontWeight.w600,
                              fontStyle: FontStyle.normal,
                              color: const Color(0xff23262D),
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.topLeft,
                          child: Text(
                            "Enter the verification code sent to your email address.",
                            style: GoogleFonts.poppins(
                              fontSize: f * 14,
                              fontWeight: FontWeight.w400,
                              fontStyle: FontStyle.normal,
                              color: const Color(0xff9F9F9F),
                            ),
                          ),
                        ),
                        const SizedBox(height: 60),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: PinCodeTextField(
                            boxShadows: const [
                              BoxShadow(
                                  offset: Offset(2, 2),
                                  blurRadius: 10,
                                  color: Colors.white)
                            ],
                            hintStyle: GoogleFonts.inter(
                                fontSize: 14,
                                color: ThemeColors.color0048A5,
                                fontWeight: FontWeight.w400),
                            showCursor: true,
                            appContext: context,
                            controller: _otpController,
                            length: 4,
                            animationType: AnimationType.none,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            textStyle: const TextStyle(
                              color: Colors.black,
                            ),
                            validator: (value) {
                              return null;

                              // if (authProvider.authOTP.toString() !=
                              //     _otpController.text) {
                              //   return;
                              // }
                              // return null;
                            },
                            onChanged: (value) {
                              // if (value.length == 4) {
                              //   authProvider.isEnableSubmitButton = true;
                              // } else {
                              //   authProvider.isEnableSubmitButton = false;
                              // }
                            },
                            keyboardType: TextInputType.number,
                            inputFormatters: <TextInputFormatter>[
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            autoFocus: true,
                            enableActiveFill: true,
                            cursorColor: ThemeColors.color0048A5,
                            pinTheme: PinTheme(
                              errorBorderColor: Colors.red,
                              borderRadius: BorderRadius.circular(5),
                              shape: PinCodeFieldShape.box,
                              activeFillColor: Colors.white,
                              inactiveColor: Colors.grey,
                              inactiveFillColor: Colors.white,
                              selectedColor: ThemeColors.colorFCC400,
                              selectedFillColor: Colors.white,
                              activeColor: ThemeColors.colorFCC400,
                              borderWidth: 0,
                              fieldWidth: 70,
                              fieldHeight: 50,
                            ),
                          ),
                        ),
                        Visibility(
                          visible: provider.otpErrorMsg != null,
                          child: Padding(
                            padding:
                                const EdgeInsets.only(left: 10, bottom: 20),
                            child: Text(
                              provider.otpErrorMsg ?? '',
                              style: const TextStyle(
                                color: Colors.red,
                                fontSize: 12.0,
                              ),
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.center,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "You will receive your OTP in ",
                                style: GoogleFonts.poppins(
                                  fontSize: f * 14,
                                  fontWeight: FontWeight.w400,
                                  fontStyle: FontStyle.normal,
                                  color: const Color(0xff9F9F9F),
                                ),
                              ),
                              ValueListenableBuilder<int>(
                                valueListenable: _seconds,
                                builder: (context, seconds, _) {
                                  WidgetsBinding.instance
                                      .addPostFrameCallback((timeStamp) {
                                    if (seconds == 0) {
                                      // _buttonDisabled.value = false;
                                    }
                                    if (seconds != 0) {
                                      // _buttonDisabled.value = true;
                                    }
                                  });

                                  return Text(
                                    '0:${seconds < 10 ? '0$seconds' : seconds}',
                                    textAlign: TextAlign.center,
                                    style: GoogleFonts.poppins(
                                      fontSize: f * 14,
                                      fontWeight: FontWeight.w400,
                                      fontStyle: FontStyle.normal,
                                      color: const Color(0xff9F9F9F),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 55),
                        Align(
                          alignment: Alignment.center,
                          child: RichText(
                            text: TextSpan(
                              style: Theme.of(context).textTheme.headlineLarge,
                              children: [
                                const TextSpan(
                                    text: 'Didn’t receive a code ? '),
                                TextSpan(
                                  text: 'Resend',
                                  style: TextStyle(
                                      color: ThemeColors.colorFCC400,
                                      fontWeight: FontWeight.w400,
                                      fontSize: 14),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      restartTimer();
                                      _otpController.clear();
                                      debugPrint("clickeddddd");
                                      provider.resendOtp();
                                    },
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 40),
                        if (provider.isLoading)
                          const LoadingWidget()
                        else
                          ButtonWidget(
                            title: 'Verify',
                            textStyle: tsS161E2138w,
                            color: Theme.of(context).primaryColor,
                            onPressed: () {
                              provider.verifyOtp(
                                  otp: _otpController.text, context: context);
                              // PageNavigator.pushSlideRight(
                              //     context: context, route: OtpScreen());

                              // if (_formKey.currentState!.validate()) {
                              //   signInProvider.siginInWithEmail(
                              //     context: context,
                              //     email: _emailController.text.toLowerCase(),
                              //     pass: _passwordController.text,
                              //     fcmtoken: token.toString(),
                              //     manufacturer: Platform.isAndroid
                              //         ? _deviceData['manufacturer']
                              //         : "apple",
                              //     model: _deviceData['model'],
                              //     platform: platform.toString(),
                              //     primary: Platform.isAndroid
                              //         ? _deviceData['id']
                              //         : _deviceData["identifierForVendor"],
                              //   );
                              // }
                            },
                          ),
                      ]);
                }))));
  }

  restartTimer() {
    _seconds.value = 59;
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_seconds.value != 0) {
        _seconds.value--;
      } else {
        timer.cancel();
      }
    });
  }
}
