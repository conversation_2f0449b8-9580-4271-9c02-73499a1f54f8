import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/sign_in/sign_in_screen.dart';

class FormSubmittedForApproval extends StatelessWidget {
  const FormSubmittedForApproval({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Align(
            alignment: Alignment.center,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.green,
                  width: 2,
                ),
                shape: BoxShape.circle,
              ),
              padding: const EdgeInsets.all(20),
              child: const Icon(
                Icons.done,
                color: Colors.green,
                size: 50,
              ),
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'Thank you!',
            textAlign: TextAlign.center,
            style: GoogleFonts.rubik(
              fontSize: 25,
              fontWeight: FontWeight.w900,
            ),
          ),
          Text(
            'Your submission has been received.',
            textAlign: TextAlign.center,
            style: GoogleFonts.rubik(
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 15),
          Align(
            alignment: Alignment.center,
            child: ElevatedButton.icon(
              onPressed: () => PageNavigator.pushAndRemoveUntil(
                context: context,
                route: const SignInScreen(),
              ),
              icon: const Icon(
                Icons.arrow_back,
                color: Color(0xFF1E2138),
              ),
              label: Text(
                'Back to Login',
                style: GoogleFonts.rubik(color: const Color(0xFF1E2138)),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
              ),
            ),
          )
        ],
      ),
    );
  }
}
