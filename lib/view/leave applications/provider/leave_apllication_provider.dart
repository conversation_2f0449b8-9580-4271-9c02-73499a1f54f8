// ignore_for_file: unnecessary_brace_in_string_interps, use_build_context_synchronously
import 'dart:convert';
import 'dart:developer';
import 'package:e8_hr_portal/model/employee_leave_detailes.dart';
import 'package:e8_hr_portal/model/leave_applied_employee_model.dart';
import 'package:e8_hr_portal/model/leave_pending_model.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/provider/sign_in_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/employee_leave_history.dart';
import 'package:e8_hr_portal/model/leave_balance_model.dart';
import 'package:e8_hr_portal/model/leave_overview_for_reported_person_model.dart';
import 'package:e8_hr_portal/model/leave_overview_model.dart';
import 'package:e8_hr_portal/model/leave_requested_view.dart';
import 'package:e8_hr_portal/model/user_list_model.dart';
import 'package:e8_hr_portal/provider/company_activities_provider.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:e8_hr_portal/view/leave%20applications/model/staff_in_charge_model.dart';
import 'package:http/http.dart' as http;
import 'package:e8_hr_portal/model/employee_leave_detailes.dart' as model;
import 'package:e8_hr_portal/model/leave_request_model.dart';
import 'package:e8_hr_portal/model/leave_types_model.dart';
import 'package:e8_hr_portal/model/reporting_person_model.dart' as report;
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LeaveApplicationProvider extends ChangeNotifier {
  //leave applied employees
  List<LeaveAppliedEmployeeModel> leaveAppliedEmployeeList = [];
  Future<void> getLeaveAppliedEmployees() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Uri uri = Uri.parse('${baseUrl}upcoming-leaves/');
    http.Response response =
        await http.get(uri, headers: {'Authorization': 'Bearer $auth'});

    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      List<LeaveAppliedEmployeeModel> leave = [];
      List<LeaveAppliedEmployeeModel> absent = [];
      if (data['data'] != null) {
        leave = (data['data'] as List)
            .map((e) => LeaveAppliedEmployeeModel.fromJson(e))
            .toList();
      }
      if (data['absent'] != null) {
        absent = (data['absent'] as List)
            .map((e) => LeaveAppliedEmployeeModel.fromJson(e))
            .toList();
      }
      leaveAppliedEmployeeList = leave + absent;
      if (leaveAppliedEmployeeList.isNotEmpty) {
        leaveAppliedEmployeeList.sort(
          (a, b) => a.startDate!.compareTo(b.startDate!),
        );
      }
    } else {
      leaveAppliedEmployeeList.clear();
    }
    notifyListeners();
  }

  //
  LeaveRequestModel? leaveRequestList;
  bool isReportingPersonLoading = false;
  model.EmployeeLeaveDetailes? empLeaveDetailes;
  List<report.Data>? allSelectedReportingPersons = [];

  void addSelectedReportingPersons(report.Data name) {
    allSelectedReportingPersons?.add(name);
    notifyListeners();
  }

  void removedReportingPersons(report.Data name) {
    allSelectedReportingPersons?.remove(name);
    if (allSelectedReportingPersons!.isEmpty ||
        allSelectedReportingPersons == null) {
      selectedReportingPerson = null;
      allSelectedReportingPersons!.clear();
    }
    notifyListeners();
  }

  report.Data? selectedReportingPerson;
  num? _remainingLeaves;

  num? get remainingLeaves => _remainingLeaves;
  set remainingLeaves(num? value) {
    _remainingLeaves = value;
    notifyListeners();
  }

  num? _remainingLeavesForAdmin;
  num? get remainingLeavesForAdmin => _remainingLeavesForAdmin;
  set remainingLeavesForAdmin(num? value) {
    _remainingLeavesForAdmin = value;
    notifyListeners();
  }

  int? _selectedLeaveType;
  int? get selectedLeaveType => _selectedLeaveType;
  set selectedLeaveType(int? value) {
    _selectedLeaveType = value;
    notifyListeners();
  }

  int? _selectedDayType;
  int? get selectedDayType => _selectedDayType;
  set selectedDayType(int? value) {
    _selectedDayType = value;
    notifyListeners();
  }

  int? _selectedUserID;
  int? get selectedUserID => _selectedUserID;
  set selectedUserID(int? value) {
    _selectedUserID = value;
    notifyListeners();
  }

  LeaveTypesModel? leaveTypesList;
  report.ReportingPersonModel? reportingPersonList;
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  set isLoading(bool value) {
    _isLoading = value;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  Future<void> getLeaveTypes() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    var response = await http.get(leaveTypesUrl,
        // Uri.parse("https://workflow.e8demo.com/api/get_leave_types"),
        headers: {"Authorization": "Bearer $auth"});
    if (response.statusCode == 200) {
      var data = jsonDecode(response.body);
      leaveTypesList = LeaveTypesModel.fromJson(data);

      notifyListeners();
    }
  }

  UserListModel? userListModel;

  Future<void> getUserList() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    var response = await http.get(userlistURL,
        // Uri.parse("https://workflow.e8demo.com/api/get_leave_types"),
        headers: {"Authorization": "Bearer $auth"});

    if (response.statusCode == 200) {
      var data = jsonDecode(response.body);
      userListModel = UserListModel.fromJson(data);

      notifyListeners();
    }
  }

  LeavePendingModel? pendingEmpDetailes;
  Future<void> getPendingLeaves() async {
    pendingEmpDetailes = null;
    SharedPreferences shared = await SharedPreferences.getInstance();
    var response = await http.get(
        Uri.parse("$requestedLeaveListURL?filter_data=20&limit=6"),
        headers: {
          'Authorization': 'Bearer ${shared.getString('access_token')}',
        });
    if (response.statusCode == 200) {
      var data = jsonDecode(utf8.decode(response.bodyBytes));
      pendingEmpDetailes = LeavePendingModel.fromJson(data);
    }
    notifyListeners();
  }

  Future<bool> getReportingPerson() async {
    isReportingPersonLoading = true;
    notifyListeners();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    var response = await http.get(reportingPersonsUrl,
        // Uri.parse(
        //     "https://workflow-dev.e8demo.com/api/reporting_persons_list/"),.
        headers: {"Authorization": "Bearer $auth"});

    if (response.statusCode == 200) {
      isReportingPersonLoading = false;
      var data = jsonDecode(response.body);
      reportingPersonList = report.ReportingPersonModel.fromJson(data);
      notifyListeners();
      return true;
    } else {
      isReportingPersonLoading = false;
    }

    notifyListeners();
    return false;
  }

  List<StaffInChargeModel>? _staffInChargeList;
  List<StaffInChargeModel>? get staffInChargeList => _staffInChargeList;
  set staffInChargeList(List<StaffInChargeModel>? value) {
    _staffInChargeList = value;
    notifyListeners();
  }

// StaffInChargeModel
  Future<bool> getStaffinCharge() async {
    try {
      isReportingPersonLoading = true;
      notifyListeners();
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");
      var response = await http
          .get(staffInChagreUrl, headers: {"Authorization": "Bearer $auth"});
      isReportingPersonLoading = false;
      if (response.statusCode == 200) {
        isReportingPersonLoading = false;
        Map<String, dynamic> json = jsonDecode(response.body);

        if (json["result"] == "success") {
          if (json.containsKey("data")) {
            List data = json["data"];
            staffInChargeList =
                data.map((e) => StaffInChargeModel.fromJson(e)).toList();
          }
        }
        notifyListeners();
        return true;
      } else {
        isReportingPersonLoading = false;
      }

      return false;
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      isReportingPersonLoading = false;
      notifyListeners();
    }
    return false;
  }

  Future<bool> applyForLeave(
      {required String leavefor,
      required String fromDate,
      required String toDate,
      required String reason,
      required String noOfDays,
      required BuildContext context}) async {
    try {
      final navigator = Navigator.of(context);
      final profileProvider = context.read<ProfileProvider>();
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");
      var request = http.MultipartRequest("POST", leaveApplyUrl);
      request.headers['Authorization'] = "Bearer $auth";
      request.fields["leave_type"] = selectedLeaveType.toString();
      request.fields["start_date"] = fromDate;
      request.fields["end_date"] = toDate;
      request.fields["reason"] = reason;
      request.fields["number_of_days"] = noOfDays;
      request.fields["day_type"] = leavefor;
      request.fields["staff_incharge"] = selectedUserID.toString();

      if (finalImageFile != null && pickedFile == null) {
        var image = await http.MultipartFile.fromPath(
            "leave_doc", finalImageFile!.path);
        request.files.add(image);
      } else if (pickedFile != null) {
        var image =
            await http.MultipartFile.fromPath("leave_doc", pickedFile!.path);
        request.files.add(image);
      }

      var response = await request.send();
      var responseData = await response.stream.toBytes();
      Map<String, dynamic> json = jsonDecode(utf8.decode(responseData));

      if (response.statusCode == 200) {
        if (json["result"] == "success") {
          await getLeaveBalance(master: false, context: context);
          // await getEmployeeLeaveDetailes(master: false);
          currentPageLeaveRecords = 0;
          pagingControllerLeaveRecords?.refresh();
          pagingControllerLeaveRecords?.notifyListeners();
          profileProvider.getProfileData(context: context);
          navigator.pop();
          showToastText("Leave applied successfully");
          pickedFile = null;
          finalImageFile = null;
        }
        if (json["status"] == false) {
          EasyLoading.dismiss();
          showToastText(json["response_message"]);
        }
        EasyLoading.dismiss();
      }
      if (response.statusCode == 400) {
        EasyLoading.dismiss();
        // showToastText(data["response_message"]);
        if (json["result"] == "failed") {
          showToastText(json["message"]);
        }
      }
      // EasyLoading.dismiss();
      if (response.statusCode == 401) {
        // if (await API.refreshToken(context)) {
        //   EasyLoading.show();
        //   await profileUpdate(
        // context: context ,fromDate: fromDate , leavefor: leavefor , noOfDays: noOfDays ,reason: reason ,toDate: toDate);
        //   EasyLoading.dismiss();
        // }
      }
    } catch (e) {
      rethrow;
    }
    return false;
  }

  //api/admin_leave_request/?leave_type=1&day_type=1&staff_incharge=12&number_of_days=1&start_date=2023-12-18&end_date=2023-12-18&leave_emp=56

  Future<bool> applyEmployeeLeaveByAdmin(
      {required String leavefor,
      required String fromDate,
      required String toDate,
      required String reason,
      required String noOfDays,
      required int employeeID,
      required BuildContext context}) async {
    try {
      final navigator = Navigator.of(context);

      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");
      var request = http.MultipartRequest("POST", adminLeaveRequestApplyUrl);
      request.headers['Authorization'] = "Bearer $auth";
      request.fields["leave_type"] = selectedLeaveType.toString();
      request.fields["start_date"] = fromDate;
      request.fields["end_date"] = toDate;
      request.fields["reason"] = reason;
      request.fields["number_of_days"] = noOfDays;
      request.fields["day_type"] = leavefor;
      request.fields["staff_incharge"] = selectedUserID.toString();
      request.fields['leave_emp'] = employeeID.toString();

      if (finalImageFile != null && pickedFile == null) {
        var image = await http.MultipartFile.fromPath(
            "leave_doc", finalImageFile!.path);
        request.files.add(image);
      } else if (pickedFile != null) {
        var image =
            await http.MultipartFile.fromPath("leave_doc", pickedFile!.path);
        request.files.add(image);
      }

      var response = await request.send();
      var responseData = await response.stream.toBytes();
      Map<String, dynamic> json = jsonDecode(utf8.decode(responseData));
      if (response.statusCode == 200) {
        if (json["result"] == "success") {
          await getLeaveBalance(master: false, context: context);
          // await getEmployeeLeaveDetailes(master: false);
          currentPageLeaveRecords = 0;
          pagingControllerLeaveRecords?.refresh();
          pagingControllerLeaveRecords?.notifyListeners();

          navigator.pop();
          showToastText("Leave applied successfully");
          pickedFile = null;
          finalImageFile = null;
        }
        if (json["status"] == false) {
          EasyLoading.dismiss();
          showToastText(json["response_message"]);
        }
        EasyLoading.dismiss();
      }
      if (response.statusCode == 400) {
        EasyLoading.dismiss();
        // showToastText(data["response_message"]);
        if (json["result"] == "failed") {
          showToastText(json["message"]);
        }
      }
      // EasyLoading.dismiss();
      if (response.statusCode == 401) {
        // if (await API.refreshToken(context)) {
        //   EasyLoading.show();
        //   await profileUpdate(
        // context: context ,fromDate: fromDate , leavefor: leavefor , noOfDays: noOfDays ,reason: reason ,toDate: toDate);
        //   EasyLoading.dismiss();
        // }
      }
    } catch (e) {
      rethrow;
    }
    return false;
  }

  LeaveOverviewModel? leaveOverviewModel;
  Future<bool> getLeaveOverView({required int? leaveId}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? url = "$leaveOverViewUrl?leave_id=$leaveId";

    // try {
    EasyLoading.show();
    var response = await http.get(Uri.parse(url), headers: {
      'Authorization': 'Bearer ${shared.getString('access_token')}',
    });

    if (response.statusCode == 200) {
      var data = jsonDecode(response.body);
      leaveOverviewModel = LeaveOverviewModel.fromJson(data['data']);

      EasyLoading.dismiss();
      notifyListeners();
      return true;
    } else if (response.statusCode == 400) {
      EasyLoading.dismiss();
    }
    EasyLoading.dismiss();
    return false;
    // } catch (e) {
    //   print(e.toString());
    // }
  }

  Future<void> getLeaveOverViewReportingPerson() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    try {
      EasyLoading.show();
      var response = await http.get(leaveOverViewUrl, headers: {
        'Authorization': 'Bearer ${shared.getString('access_token')}',
      });
      if (response.statusCode == 200) {
        var data = jsonDecode(response.body);
        empLeaveDetailes = model.EmployeeLeaveDetailes.fromJson(data);
        notifyListeners();
      } else if (response.statusCode == 400) {
        // showToastText("Data not found");
      }
      EasyLoading.dismiss();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  LeaveBalanceModel? leaveBalanceModel;

  Future<void> getLeaveBalance(
      {required bool master, required BuildContext context}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    try {
      if (!master) {
        if (leaveBalanceModel == null || leaveBalanceModel!.data!.isEmpty) {
          EasyLoading.show();
        }
      }

      var response = await http.get(leaveBalanceUrl, headers: {
        'Authorization': 'Bearer ${shared.getString('access_token')}',
      });

      // "https://workflow-dev.e8demo.com/api/get_employee_leave_data/?email=$email"));

      if (response.statusCode == 200) {
        var data = jsonDecode(response.body);
        leaveBalanceModel = LeaveBalanceModel.fromJson(data);

        // empLeaveDetailes?.data?.finalData.sort(((a, b) =>
        //     DateTime.parse(a.toDate.toString())
        //         .compareTo(DateTime.parse(b.toDate.toString()))));
        notifyListeners();
      } else if (response.statusCode == 400) {
        // showToastText("Data not found");
      } else if (response.statusCode == 401) {
        context.read<SignInProvider>().updateToken(context: context);
        // PageNavigator.pushAndRemoveUntil(
        //   context: context,
        //   route: const LoginWithEmailScreen(),
        // );
      }

      EasyLoading.dismiss();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  int currentPageLeaveRecords = 0;
  PagingController<int, model.Data>? pagingControllerLeaveRecords;
  initPageLeaveRecords({required bool master}) async {
    currentPageLeaveRecords = 0;
    pagingControllerLeaveRecords = PagingController(firstPageKey: 1);
    pagingControllerLeaveRecords?.addPageRequestListener((pageKey) {
      getEmployeeLeaveDetailes(master: master, page: pageKey);
    });
  }

  refreshLeaveRecord() {
    currentPageLeaveRecords = 0;
    pagingControllerLeaveRecords?.refresh();
    pagingControllerLeaveRecords?.notifyListeners();
  }

  List<model.Data>? empLeaveDetailesRecords = [];
  String? msg;
  Future<void> getEmployeeLeaveDetailes(
      {required bool master, required int page}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    // try {
    if (currentPageLeaveRecords != page) {
      currentPageLeaveRecords = page;

      String? action = '';
      switch (selectedTypeOfLeave.toLowerCase()) {
        case 'all':
          action = '';
          break;
        case 'pending':
          action = "1";
          break;
        case 'in-progress':
          action = "2";
          break;
        case 'approved':
          action = "3";
          break;
        case 'rejected':
          action = "4";
          break;
        case 'cancelled':
          action = "5";
          break;
        case 'expired':
          action = "6";
          break;
        case 'holiday':
          action = "7";
          break;
        default:
          action = '';
      }

      if (!master) {
        // EasyLoading.show();
      }
      var response = await http.get(
          Uri.parse(
              "$leaveHistoryUrl?filter_data=$action&limit=20&page=$page/"),
          headers: {
            'Authorization': 'Bearer ${shared.getString('access_token')}',
          });
      // debugPrint("request --" + response.request.toString());

      if (response.statusCode == 200) {
        // noData = jsonDecode(response.body);
        var data = jsonDecode(response.body);

        empLeaveDetailes = EmployeeLeaveDetailes.fromJson(data);

        List json = data['data'];
        List<model.Data> temp = [];
        temp = json.map((e) => model.Data.fromJson(e)).toList();
        empLeaveDetailesRecords = temp;

        if (data['has_next']) {
          int nextPage = page + 1;
          pagingControllerLeaveRecords?.appendPage(temp, nextPage);
        } else {
          pagingControllerLeaveRecords?.appendLastPage(temp);
        }

        // empLeaveDetailes?.data?.finalData.sort(((a, b) =>
        //     DateTime.parse(a.toDate.toString())
        //         .compareTo(DateTime.parse(b.toDate.toString()))));
        notifyListeners();
      } else {
        int nextPage = 0;
        pagingControllerLeaveRecords?.appendPage([], nextPage);
        var data = jsonDecode(response.body);
        msg = data["message"];
        notifyListeners();
      }
    }
    notifyListeners();
    EasyLoading.dismiss();
    // } catch (e) {
    //   debugPrint(e.toString());
    //   debugPrint(e.toString());
    //   (pagingControllerLeaveRecords?.error);
    // }
  }

  Future<void> getLeaveRefresh() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    // try {

    String? action = '';
    switch (selectedTypeOfLeave.toLowerCase()) {
      case 'all':
        action = '';
        break;
      case 'pending':
        action = "1";
        break;
      case 'in-progress':
        action = "2";
        break;
      case 'approved':
        action = "3";
        break;
      case 'rejected':
        action = "4";
        break;
      case 'cancelled':
        action = "5";
        break;
      case 'expired':
        action = "6";
        break;
      default:
        action = '';
    }

    var response = await http.get(
        Uri.parse("$leaveHistoryUrl?filter_data=$action&limit=20&page=1/"),
        headers: {
          'Authorization': 'Bearer ${shared.getString('access_token')}',
        });
    // "https://workflow-dev.e8demo.com/api/get_employee_leave_data/?email=$email"));

    if (response.statusCode == 200) {
      msg = null;
    } else {
      var data = jsonDecode(response.body);
      msg = data["message"];
      notifyListeners();
    }
    notifyListeners();
    // EasyLoading.dismiss();
    // } catch (e) {
    //   print(e.toString());
    // }
  }

  Future<void> getLeaveRequest() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    isLoading = true;
    notifyListeners();

    EasyLoading.show();
    try {
      var response = await http
          .get(leaveHistoryUrl, headers: {"Authorization": "Bearer $auth"});
      if (response.statusCode == 200) {
        isLoading = false;
        var data = jsonDecode(response.body);
        leaveRequestList = LeaveRequestModel.fromJson(data);

        notifyListeners();
        EasyLoading.dismiss();
      }
      notifyListeners();
    } catch (e) {
      isLoading = false;
      notifyListeners();
      debugPrint(e.toString());
    }
    EasyLoading.dismiss();
    notifyListeners();
  }

//////////////////////leave request for reporting person////////////////
  int currentPageLeaveRequest = 0;
  PagingController<int, LeaveRequestViewModel>? pagingControllerLeaveRequests;
  initPageLeaveRequests() {
    pagingControllerLeaveRequests = PagingController(firstPageKey: 1);
    pagingControllerLeaveRequests!.addPageRequestListener((pageKey) {
      getLeaveRequestReporting(page: pageKey);
    });
  }

  void refreshLeaveRequest() {
    currentPageLeaveRequest = 0;
    pagingControllerLeaveRequests!.refresh();
    notifyListeners();
  }

  Future<void> getLeaveRequestReporting({
    required int page,
  }) async {
    if (currentPageLeaveRequest != page) {
      currentPageLeaveRequest = page;
      String? action = '20';
      String? actionFilter = "pending_to_me";

      switch (selectedAction.toLowerCase()) {
        case 'pending/in-progress':
          action = "20";
          break;
        case 'all':
          action = '';
          break;

        case 'pending':
          action = "1";
          break;
        case 'in-progress':
          action = "2";
          break;

        case 'approved':
          action = "3";
          break;
        case 'rejected':
          action = "4";
          break;
        case 'cancelled':
          action = "5";
          break;
        case 'expired':
          action = "6";
          break;
        case 'assigned to me':
          action = "assigned_to_me";
          break;
        default:
          action = '';
      }
      switch (mainFiltereOfLeave.toLowerCase()) {
        case 'all':
          actionFilter = null;
          break;
        case 'pending to me':
          actionFilter = "pending_to_me";
          action = "";
          break;
        case 'assigned to me':
          actionFilter = "assigned_to_me";
          break;

        default:
          actionFilter = null;
      }
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");
      isLoading = true;
      notifyListeners();

      StringBuffer url =
          StringBuffer("$requestedLeaveListURL?limit=10&page=$page");

      // log("leave module -==== $action === $actionFilter");
      // log("leave module -==== ${LoginModel.isAdmin} ========= ${LoggedInUser.isAdmin}");
      if (action.isNotEmpty && actionFilter != "All") {
        url.write("&filter_data=$action");
      }
      if (actionFilter != null && actionFilter != "All") {
        url.write("&main_filter=$actionFilter");
      }

      var response = await http.get(Uri.parse(url.toString()),
          headers: {"Authorization": "Bearer $auth"});

      log("leave Requested ${actionFilter}");
      log("leave Requested ${response.body}");
      log("leave Requested ${response.request}");

      if (response.statusCode == 200) {
        isLoading = false;
        Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
        List<LeaveRequestViewModel> tempList = [];
        tempList = (data['data'] as List)
            .map(
              (e) => LeaveRequestViewModel.fromJson(e),
            )
            .toList();
        if (data['has_next'] == true) {
          int nextPage = page + 1;

          pagingControllerLeaveRequests!.appendPage(tempList, nextPage);
        } else {
          pagingControllerLeaveRequests!.appendLastPage(tempList);
        }
      } else {
        int nextPage = 0;
        pagingControllerLeaveRequests!.appendPage([], nextPage);
      }
      notifyListeners();
      // }

      //   SharedPreferences shared = await SharedPreferences.getInstance();
      //   String? auth = shared.getString("access_token");
      //   isLoading = true;
      //   notifyListeners();
      //   var response = await http.get(
      //       Uri.parse(
      //           "$requestedLeaveListURL?filter_data=$action&limit=10&page=$page"),
      //       headers: {"Authorization": "Bearer $auth"});

      //   log("leave Requested1 ${response.request}");
      //   log("leave Requested1 ${response.body}");

      //   if (response.statusCode == 200) {
      //     isLoading = false;
      //     Map<String, dynamic> data = jsonDecode(response.body);
      //     List<LeaveRequestViewModel> tempList = [];
      //     tempList = (data['data'] as List)
      //         .map(
      //           (e) => LeaveRequestViewModel.fromJson(e),
      //         )
      //         .toList();
      //     if (data['has_next'] == true) {
      //       int nextPage = page + 1;

      //       pagingControllerLeaveRequests!.appendPage(tempList, nextPage);
      //     } else {
      //       pagingControllerLeaveRequests!.appendLastPage(tempList);
      //     }
      //   } else {
      //     pagingControllerLeaveRequests!.appendPage([], 1);
      //   }

      //   notifyListeners();
      // }
    }
  }

  List<String> mainFilterDataLeave = [
    "All",
    "Pending to me",
  ];
  List<String> mainFilterDataLeaveHR = [
    "All",
    "Pending to me",
    "Assigned to me",
  ];

//////////////////////////////////////////////////////
  Future<void> leaveAcceptOrReject(
      {required int? leaveId,
      required int? acceptOrReject,
      String? reason,
      required context}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    try {
      var response = await http.post(
        leaveApproveReject,
        headers: <String, String>{
          "Authorization": "Bearer $auth",
          "Content-Type": "application/json",
        },
        body: jsonEncode(<String, dynamic>{
          "leave_id": leaveId,
          "leave_status": acceptOrReject.toString(),
          "remark": reason ?? "",
        }),
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);
        // refreshLeaveRequest();
        currentPageLeaveRequest = 0;
        pagingControllerLeaveRequests!.refresh();
        pagingControllerLeaveRequests!.notifyListeners();

        notifyListeners();
        showToastText(data["result"]);
      }
      if (response.statusCode == 400) {}
      notifyListeners();
      // EasyLoading.dismiss();
    } catch (e) {
      // EasyLoading.dismiss();
      debugPrint(e.toString());
    }
    // EasyLoading.dismiss();
  }

//-------------------------------------------------Shithin start here------------------------------------------------------------------------//
  List<String> leaveRecordsList = [
    "All",
    "Pending",
    "In-progress",
    "Approved",
    "Rejected",
    "Cancelled",
    // "Expired"
    "Holiday"
  ];
  String _selectedTypeOfLeave = "All";
  String get selectedTypeOfLeave => _selectedTypeOfLeave;
  set selectedTypeOfLeave(String value) {
    _selectedTypeOfLeave = value;
    notifyListeners();
  }

  final List<DayTypesModel> dayTypesModel = [
    DayTypesModel("Full Day", 1),
    DayTypesModel("Forenoon", 2),
    DayTypesModel("Afternoon", 3),
  ];
  DateTime? _selectedFromDate;
  DateTime? get selectedFromDate => _selectedFromDate;
  set selectedFromDate(DateTime? value) {
    _selectedFromDate = value;
    notifyListeners();
  }

  DateTime? _selectedToDate;
  DateTime? get selectedToDate => _selectedToDate;
  set selectedToDate(DateTime? value) {
    _selectedToDate = value;

    notifyListeners();
  }

  String _formattedFromDate =
      formatDateFromDate(dateTime: DateTime.now(), format: "dd MMM yyyy");
  String get formattedFromDate => _formattedFromDate;
  set formattedFromDate(String value) {
    _formattedFromDate = value;
    notifyListeners();
  }

  String _formattedToDate =
      formatDateFromDate(dateTime: DateTime.now(), format: "dd MMM yyyy");
  String get formattedToDate => _formattedToDate;
  set formattedToDate(String value) {
    _formattedToDate = value;
    notifyListeners();
  }

  Future<void> selectFromDate({required BuildContext context}) async {
    var now = DateTime.now();
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedFromDate ?? DateTime.now(),
      firstDate: DateTime(now.year - 1),
      lastDate: DateTime(3100),
    );
    if (picked != null && picked != selectedFromDate) {
      selectedFromDate = picked;
      if (selectedFromDate != null) {
        selectedToDate = selectedFromDate;
        formattedFromDate = formatDateFromDate(
            dateTime: selectedFromDate!, format: "dd MMM yyyy");
        formattedToDate = formatDateFromDate(
            dateTime: selectedToDate!, format: "dd MMM yyyy");
        calculateDaysBetween(
            context: context,
            selectedFromDate: selectedFromDate,
            selectedToDate: selectedToDate);
      }
    }
  }

  Future<void> selectToDate({required BuildContext context}) async {
    var now = DateTime.now();
    // var tomorrow = now.add(const Duration(days: 1));
    // if (selectedFromDate != null) {
    //   tomorrow = selectedFromDate!.add(const Duration(days: 1));
    // }
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedToDate ?? now,
      firstDate: selectedFromDate ?? selectedToDate ?? DateTime(2023),
      lastDate: DateTime(3100),
    );
    if (picked != null && picked != selectedToDate) {
      selectedToDate = picked;

      formattedToDate =
          formatDateFromDate(dateTime: selectedToDate!, format: "dd MMM yyyy");
      calculateDaysBetween(
          context: context,
          selectedFromDate: selectedFromDate,
          selectedToDate: selectedToDate);
    }
  }

  Future<void> selectToDateIOS({required BuildContext ctx}) async {
    showCupertinoModalPopup(
        context: ctx,
        builder: (_) {
          DateTime now = DateTime.now();
          DateTime tomorrow = now.add(const Duration(days: 1));
          return Container(
            height: h * 250,
            color: ThemeColors.colorFFFFFF,
            padding: EdgeInsets.only(top: h * 10),
            child: CupertinoDatePicker(
              initialDateTime: selectedFromDate ?? selectedToDate ?? tomorrow,
              minimumYear: int.parse(
                  formatDateFromDate(dateTime: DateTime.now(), format: "yyyy")),
              maximumYear: 3999,
              mode: CupertinoDatePickerMode.date,
              onDateTimeChanged: (picked) {
                if (picked != selectedToDate) {
                  selectedToDate = picked;
                  formattedToDate = formatDateFromDate(
                      dateTime: selectedToDate!, format: "dd MMM yyyy");
                  calculateDaysBetween(
                      context: ctx,
                      selectedFromDate: selectedFromDate,
                      selectedToDate: selectedToDate);
                }
              },
            ),
          );
        });
  }

  Future<void> selectFromDateIOS({required BuildContext ctx}) async {
    showCupertinoModalPopup(
      context: ctx,
      builder: (_) => Container(
        height: 190,
        color: ThemeColors.colorFFFFFF,
        child: SizedBox(
          height: 180,
          child: CupertinoDatePicker(
            initialDateTime: selectedFromDate ?? DateTime.now(),
            minimumYear: int.parse(
                formatDateFromDate(dateTime: DateTime.now(), format: "yyyy")),
            maximumYear: 3999,
            mode: CupertinoDatePickerMode.date,
            onDateTimeChanged: (picked) {
              if (picked != selectedFromDate) {
                selectedFromDate = picked;
                if (selectedFromDate != null) {
                  formattedFromDate = formatDateFromDate(
                      dateTime: selectedFromDate!, format: "dd MMM yyyy");
                  calculateDaysBetween(
                      context: ctx,
                      selectedFromDate: selectedFromDate,
                      selectedToDate: selectedToDate);
                }
              }
            },
          ),
        ),
      ),
    );
  }

  int _daysBetween = 0;
  int get daysBetween => _daysBetween;
  set daysBetween(int value) {
    _daysBetween = value;
    notifyListeners();
  }

  int _daysBetweenPlus = 0;
  int get daysBetweenPlus => _daysBetweenPlus;
  set daysBetweenPlus(int value) {
    _daysBetweenPlus = value;
    notifyListeners();
  }

  List<String> getDaysInBetween(DateTime startDate, DateTime endDate) {
    List<String> days = [];
    for (int i = 0; i <= endDate.difference(startDate).inDays; i++) {
      days.add(startDate.add(Duration(days: i)).toString());
    }
    return days;
  }

  void calculateDaysBetween(
      {required BuildContext context,
      required DateTime? selectedFromDate,
      required DateTime? selectedToDate}) {
    CompanyActivitiesProvider companyActivitiesProvider =
        Provider.of<CompanyActivitiesProvider>(context, listen: false);

    // var selectedFromDateFormatted =
    //     formatDateFromDate(dateTime: selectedFromDate!, format: "dd MM yyyy");

    // for (var b in companyActivitiesProvider.holiDates) {

    // }

    daysBetween = 0;
    daysBetweenPlus = 0;

    var a = getDaysInBetween(selectedFromDate!, selectedToDate!);
    var b = companyActivitiesProvider.holiDates;

    var c = a.where((element) => b.contains(element)).toList();

    if (selectedFromDate.isBefore(selectedToDate)) {
      final duration = selectedToDate.difference(selectedFromDate);
      daysBetween = duration.inDays;

      if (c.isNotEmpty) {
        daysBetweenPlus = daysBetween + 1 - c.length;
      } else {
        daysBetweenPlus = daysBetween + 1;
      }
    }
    notifyListeners();
  }

  XFile? _croppedImageFile;
  XFile? get croppedImageFile => _croppedImageFile;
  set croppedImageFile(XFile? value) {
    _croppedImageFile = value;
    notifyListeners();
  }

  XFile? _finalImageFile;
  XFile? get finalImageFile => _finalImageFile;
  set finalImageFile(XFile? value) {
    _finalImageFile = value;
    notifyListeners();
  }

  XFile? _imageFile;
  XFile? get imageFile => _imageFile;
  set imageFile(XFile? value) {
    _imageFile = value;
    notifyListeners();
  }

  final _imgPicker = ImagePicker();
  Future<void> openCamera(BuildContext context) async {
    Navigator.pop(context);
    var imgCamera = await _imgPicker.pickImage(
        source: ImageSource.camera,
        preferredCameraDevice: CameraDevice.front,
        imageQuality: 70,
        // maxHeight: 500,
        maxWidth: 1000);
    if (imgCamera != null) {
      final bytes = (await XFile(imgCamera.path).readAsBytes()).lengthInBytes;
      final kb = bytes / 1024;
      final mb = kb / 1024;

      if (mb <= 2) {
        croppedImageFile = null;
        imageFile = null;
        imageFile = XFile(imgCamera.path);
        await cropProfilePicture(context: context);
        imageFile = null;
      } else {
        // showToastMessage("Image should be less than 2 MB");
      }
      notifyListeners();
    }
  }

  Future<void> openGallery(BuildContext context) async {
    Navigator.pop(context);
    var imgGallery = await _imgPicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 70,
        // maxHeight: 500,
        maxWidth: 1000);
    if (imgGallery != null) {
      final bytes = (await XFile(imgGallery.path).readAsBytes()).lengthInBytes;
      final kb = bytes / 1024;
      final mb = kb / 1024;

      if (mb <= 2) {
        croppedImageFile = null;
        imageFile = null;
        imageFile = XFile(imgGallery.path);
        await cropProfilePicture(context: context);
        imageFile = null;
      } else {
        // showToastMessage("Image should be less than 2 MB");
      }
      notifyListeners();
    }
  }

  PlatformFile? _pickedPlatformFile;
  PlatformFile? get pickedPlatformFile => _pickedPlatformFile;
  set pickedPlatformFile(PlatformFile? value) {
    _pickedPlatformFile = value;
    notifyListeners();
  }

  XFile? _pickedFile;
  XFile? get pickedFile => _pickedFile;
  set pickedFile(XFile? value) {
    _pickedFile = value;
    notifyListeners();
  }

  String? _pickedFileFilePath;
  String? get pickedFileFilePath => _pickedFileFilePath;
  set pickedFileFilePath(String? value) {
    _pickedFileFilePath = value;
    notifyListeners();
  }

  Future<void> pickFile({required BuildContext context}) async {
    Navigator.pop(context);
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowCompression: true,
      withData: true,
      allowedExtensions: ['jpg', 'jpeg', 'png', 'pdf'],
    );

    if (result != null) {
      // The user picked a file.
      PlatformFile file = result.files.first;
      XFile? xFile = XFile(result.files.single.path!);
      // if (xFile != null) {
      // final bytes = (await File(xFile.path).readAsBytes()).lengthInBytes;

      if (file.size <= 2097152) {
        pickedFile = XFile(xFile.path);
      } else {
        showToastText("File should be less than 2 MB");
      }
      notifyListeners();
      // }

      if (file.size > 2097152) {
        showToastText("File size exceeded 2 MB");
      }
      if (file.size <= 2097152) {
        pickedPlatformFile = file;
        pickedFileFilePath = file.path;
      }
      // Use the file object for further processing.
    } else {
      // The user canceled the picker.
    }
  }

  cropProfilePicture({required BuildContext context}) async {
    CroppedFile? croppedFile = await ImageCropper().cropImage(
      sourcePath: imageFile!.path,
      uiSettings: [
        AndroidUiSettings(
          statusBarColor: Colors.black,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false,
        ),
        IOSUiSettings(),
      ],
    );
    if (croppedFile != null) {
      finalImageFile = XFile(croppedFile.path);
      pickedFile = finalImageFile;
      pickedFileFilePath = finalImageFile?.path;
    }
  }

  showAlertDialog(BuildContext context) {
    Widget okButton = TextButton(
      child: Padding(
        padding: const EdgeInsets.only(bottom: 15.0, right: 8),
        child: Container(
            decoration: BoxDecoration(
                color: ThemeColors.secondaryColor,
                borderRadius: BorderRadius.circular(10)),
            child: const Padding(
              padding:
                  EdgeInsets.only(left: 25.0, right: 25, top: 6, bottom: 6),
              child: Text(
                "OK",
                style:
                    TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            )),
      ),
      onPressed: () {
        Navigator.of(context).pop();
      },
    );

    // set up the AlertDialog
    AlertDialog alert = AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      content: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.info_outlined,
            color: Colors.yellow,
            size: 70,
          ),
          const SizedBox(
            height: 10,
          ),
          Text(
            "Something went wrong,",
            style: tsS14w500,
          ),
          Text(
            "Please contact HR administrator",
            style: tsS14w500,
          ),
        ],
      ),
      actions: [
        okButton,
      ],
      buttonPadding: EdgeInsets.zero,
    );

    // show the dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  String _selectedAction = '';
  String get selectedAction => _selectedAction;
  set selectedAction(String value) {
    _selectedAction = value;
    notifyListeners();
  }

  LeaveRequestFilterModel? _selectedFilter;
  LeaveRequestFilterModel? get selectedFilter => _selectedFilter;
  set selectedFilter(LeaveRequestFilterModel? value) {
    _selectedFilter = value;
    selectedAction = value!.text;
    notifyListeners();
  }

  init() {
    selectedFilter = leaveFilterModel.first;
  }

  List<LeaveRequestFilterModel> leaveFilterModel = [
    LeaveRequestFilterModel("Pending/In-progress", 20),
    LeaveRequestFilterModel("All", 1),
    LeaveRequestFilterModel("Approved", 2),
    LeaveRequestFilterModel("Rejected", 3),
    LeaveRequestFilterModel("Pending", 4),
    LeaveRequestFilterModel("In-progress", 5),
    // LeaveRequestFilterModel("assigned to me", 7),
    // LeaveRequestFilterModel("Expired", 6),
  ];
  LeaveOverviewForReportedPersonModel? _leaveOverviewForReportingPerson;
  LeaveOverviewForReportedPersonModel? get leaveOverviewForReportingPerson =>
      _leaveOverviewForReportingPerson;
  set leaveOverviewForReportingPerson(
      LeaveOverviewForReportedPersonModel? value) {
    _leaveOverviewForReportingPerson = value;
    notifyListeners();
  }

  String _mainFiltereOfLeave = "Pending to me";
  String get mainFiltereOfLeave => _mainFiltereOfLeave;
  set mainFiltereOfLeave(String value) {
    _mainFiltereOfLeave = value;
    notifyListeners();
  }

  Future<bool> getRequestedLeaveOverviewForReportedPerson(
      {required int leaveId}) async {
    try {
      if (isLoading) return false;
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");

      isLoading = true;
      var response = await http.get(
          Uri.parse("${baseUrl}requested_leave_over_view/?leave_id=$leaveId"),
          headers: {"Authorization": "Bearer $auth"});
      isLoading = false;
      log('getRequestedLeaveOverviewForReportedPerson - ${response.body} - ${response.request}');
      if (response.statusCode == 200) {
        Map<String, dynamic> result = jsonDecode(response.body);
        if (result["result"] == "success") {
          _leaveOverviewForReportingPerson =
              LeaveOverviewForReportedPersonModel.fromJson(result);
          return true;
        }
      }
      return false;
    } catch (e) {
      isLoading = false;
    } finally {
      isLoading = false;
    }
    return false;
  }

  Future<bool> cancelLeaveRequest(
      {required int leaveId, required BuildContext context}) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString("access_token");
    try {
      var response = await http.patch(
        Uri.parse("${baseUrl}cancel_leave/?leave_id=$leaveId"),
        headers: {"Authorization": "Bearer $token"},
      );

      if (response.statusCode == 200) {
        //await getLeaveOverView(leaveId: leaveId);
        // getEmployeeLeaveDetailes(master: false);
        await getLeaveBalance(master: false, context: context);
        currentPageLeaveRecords = 0;
        pagingControllerLeaveRecords?.refresh();
        pagingControllerLeaveRecords?.notifyListeners();
        currentPageLeaveTypes = 0;
        pagingControllerLeaveTypes?.refresh();
        pagingControllerLeaveTypes?.notifyListeners();

        return true;
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return false;
  }

  Future<bool> editAndApplyForLeave(
      {required String leaveID,
      required String leavefor,
      required String startDate,
      required String endDate,
      required String reason,
      required String noOfDays,
      required BuildContext context}) async {
    // try {
    final navigator = Navigator.of(context);

    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    Uri uri = Uri.parse("${baseUrl}edit_leave/?leave_id=$leaveID");
    var request = http.MultipartRequest("PATCH", uri
        //https://hisense-hrportal.e8demo.com/api/edit_leave/
        );
    request.headers['Authorization'] = "Bearer $auth";
    request.fields["leave_type"] = selectedLeaveType.toString();
    request.fields["start_date"] = startDate;
    request.fields["end_date"] = endDate;
    request.fields["reason"] = reason;
    request.fields["number_of_days"] = noOfDays;
    request.fields["day_type"] = leavefor;
    request.fields["staff_incharge"] = selectedUserID.toString();

    // if (finalImageFile != null) {
    //   var image =
    //       await http.MultipartFile.fromPath("leave_doc", finalImageFile!.path);
    //   request.files.add(image);
    // }
    if (finalImageFile != null && pickedFile == null) {
      var image =
          await http.MultipartFile.fromPath("leave_doc", finalImageFile!.path);
      request.files.add(image);
    } else if (pickedFile != null) {
      var image =
          await http.MultipartFile.fromPath("leave_doc", pickedFile!.path);
      request.files.add(image);
    }

    var response = await request.send();

    var responseData = await response.stream.toBytes();
    // var responseString = String.fromCharCodes(responseData);

    Map<String, dynamic> json = jsonDecode(utf8.decode(responseData));

    if (response.statusCode == 200) {
      if (json["result"] == "success") {
        await getLeaveBalance(master: false, context: context);
        // await getEmployeeLeaveDetailes(master: false);
        currentPageLeaveRecords = 0;
        pagingControllerLeaveRecords?.refresh();
        pagingControllerLeaveRecords?.notifyListeners();

        navigator.pop();
        showToastText("Leave applied successfully");
        return true;
      }
      if (json["status"] == false) {
        EasyLoading.dismiss();
        showToastText(json["response_message"]);
      }
      EasyLoading.dismiss();
    }
    if (response.statusCode == 400) {
      EasyLoading.dismiss();
      // showToastText(data["response_message"]);
      showToastText(json["message"]);
    }
    EasyLoading.dismiss();
    if (response.statusCode == 401) {
      // if (await API.refreshToken(context)) {
      //   EasyLoading.show();
      //   await profileUpdate(
      // context: context ,fromDate: fromDate , leavefor: leavefor , noOfDays: noOfDays ,reason: reason ,toDate: toDate);
      //   EasyLoading.dismiss();
      // }
    }
    // } catch (e) {
    //   rethrow;
    // }
    return false;
  }

  // Future<void> getEmployeeLeaveHistory({required int employeeId}) async {
  //   SharedPreferences shared = await SharedPreferences.getInstance();
  //   String? employeeID = shared.getString("uid");
  //   try {
  //     Uri uri = Uri.parse("${baseUrlHisense}emp_leave_history/$employeeId/");
  //     EasyLoading.show();
  //     var response = await http.get(uri, headers: {
  //       'Authorization': 'Bearer ${shared.getString('access_token')}',
  //     });

  //     //emp_leave_history/47/
  //     // if (response.statusCode == 200) {
  //     //   var data = jsonDecode(response.body);
  //     //   empLeaveDetailes = EmployeeLeaveDetailes.fromJson(data);
  //     //   // empLeaveDetailes?.data?.finalData.sort(((a, b) =>
  //     //   //     DateTime.parse(a.toDate.toString())
  //     //   //         .compareTo(DateTime.parse(b.toDate.toString()))));
  //     //   notifyListeners();
  //     // } else if (response.statusCode == 400) {
  //     //   // showToastText("Data not found");
  //     // }

  //     EasyLoading.dismiss();
  //   } catch (e) {
  //     print(e.toString());
  //   }
  // }

  int currentPage = 0;
  late PagingController<int, EmployeeLeaveHostory> pagingController;
  initPage({required BuildContext context, required int employeeID}) async {
    pagingController = PagingController(firstPageKey: 1);
    pagingController.addPageRequestListener((pageKey) {
      getEmployeeLeaveHistory(
          page: pageKey, context: context, employeeID: employeeID);
    });
  }

  refreshPage({required BuildContext context}) {
    try {
      currentPage = 0;
      pagingController.refresh();
    } catch (e) {
      rethrow;
    }
  }

  EmployeeLeaveHostory? _employeeLeaveHostory;
  EmployeeLeaveHostory? get employeeLeaveHostory => _employeeLeaveHostory;
  set employeeLeaveHostory(EmployeeLeaveHostory? value) {
    _employeeLeaveHostory = value;
    notifyListeners();
  }

  List<EmployeeLeaveHostory>? _employeeLeaveHistoryList;
  List<EmployeeLeaveHostory>? get employeeLeaveHistoryList =>
      _employeeLeaveHistoryList;
  set employeeLeaveHistoryList(List<EmployeeLeaveHostory>? value) {
    _employeeLeaveHistoryList = value;
    notifyListeners();
  }

  Future<void> getEmployeeLeaveHistory(
      {required int page,
      required BuildContext context,
      required int employeeID}) async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();

      if (currentPage != page) {
        currentPage = page;
        Uri url = Uri.parse(
            "${baseUrl}emp_leave_history/$employeeID/?limit=9&page=$page");
        var response = await http.get(url, headers: {
          'Authorization': 'Bearer ${shared.getString('access_token')}',
        });

        if (response.statusCode == 200) {
          var json = jsonDecode(response.body);
          if (json["result"] == "success") {
            List data = json['data'];
            List<EmployeeLeaveHostory> temp = [];
            temp = data.map((e) => EmployeeLeaveHostory.fromJson(e)).toList();
            employeeLeaveHostory = EmployeeLeaveHostory.fromJson(json);
            employeeLeaveHistoryList = temp;
            if (json['has_next']) {
              int nextPage = page + 1;
              pagingController.appendPage(temp, nextPage);
            } else {
              pagingController.appendLastPage(temp);
            }
            notifyListeners();
          }
        } else if (response.statusCode == 401) {
          int nextPage = 0;
          pagingController.appendPage([], nextPage);
          // if (await ApiServices.refreshToken(context)) {
          //   Loading.show();
          //   await getEmployeeLeaveHistory(context: context, page: page);
          //   Loading.dismiss();
          // }
        } else {
          int nextPage = 0;
          pagingController.appendPage([], nextPage);
        }
        notifyListeners();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  int currentPageLeaveTypes = 0;
  PagingController<int, model.Data>? pagingControllerLeaveTypes;
  initPageLeaveTypes({required String leaveTypeId}) async {
    pagingControllerLeaveTypes = PagingController(firstPageKey: 1);
    pagingControllerLeaveTypes?.addPageRequestListener((pageKey) {
      getLeaveHistory(leaveTypeId: leaveTypeId, page: pageKey);
    });
  }

  model.EmployeeLeaveDetailes? leaveHistoryList;
  List<model.Data>? leaveHistoryListtypes = [];

  Future<void> getLeaveHistory(
      {required String leaveTypeId, required int page}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();

    if (currentPageLeaveTypes != page) {
      currentPageLeaveTypes = page;

      var response = await http.get(
          Uri.parse(
              "${baseUrl}leave_history_list/?leave_type=$leaveTypeId&limit=20&page=$page"),
          headers: {
            'Authorization': 'Bearer ${shared.getString('access_token')}',
          });

      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);

        List json = data['data'];
        List<model.Data> temp = [];
        temp = json.map((e) => model.Data.fromJson(e)).toList();

        leaveHistoryListtypes = temp;

        if (data['has_next']) {
          int nextPage = page + 1;
          pagingControllerLeaveTypes?.appendPage(temp, nextPage);
        } else {
          pagingControllerLeaveTypes?.appendLastPage(temp);
        }
      } else {
        int nextPage = 0;
        pagingControllerLeaveTypes?.appendPage([], nextPage);
      }
      notifyListeners();
    }
  }

  Future<void> remainigLeavesWithDate(
      {required String leaveType, required String startDate}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    try {
      var response = await http.get(
          Uri.parse(
              "${remainingLeavesNext}?leave_type=$leaveType&from_date=$startDate"),
          headers: {
            'Authorization': 'Bearer ${shared.getString('access_token')}',
          });
      if (response.statusCode == 200) {
        var data = jsonDecode(response.body);

        remainingLeaves = data["remaining_leave"];
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> adminRemainigLeavesWithDate(
      {required String leaveType,
      required String startDate,
      required int employeeID}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    try {
      String fromDate =
          formatDateFromString(startDate, 'yyyy-MM-dd HH:mm:ss', 'yyyy-MM-dd');
      var response = await http.get(
          Uri.parse(
              "${remainingLeavesNext}?leave_type=$leaveType&from_date=$fromDate&leave_emp=$employeeID"),
          headers: {
            'Authorization': 'Bearer ${shared.getString('access_token')}'
          });
      remainingLeavesForAdmin = null;
      if (response.statusCode == 200) {
        var data = jsonDecode(response.body);
        remainingLeavesForAdmin = data["remaining_leave"];
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<bool> leaveForward({required int leaveID}) async {
    // try {
    SharedPreferences shared = await SharedPreferences.getInstance();
    if (selectedUserID == null) {
      showToastText('Please select Reporting person');
      return false;
    }

    Map<String, dynamic> data = {};
    data['leave_request'] = leaveID.toString();
    data['forwarded_to'] = selectedUserID.toString();
    log('message');
    final response = await http.post(
      body: data,
      leaveForwardUrl,
      headers: {'Authorization': 'Bearer ${shared.getString('access_token')}'},
    );
    log('leaveForward ==> ${response.statusCode} - ${response.body}');
    remainingLeavesForAdmin = null;
    if (response.statusCode == 200) {
      var data = jsonDecode(response.body);
      if (data['result'] == 'success') {
        Map<String, dynamic> result = data;
        if (result.containsKey('message')) {
          showToastText(result['message']);
        }
      }
      return true;
    }
    // } catch (e) {
    //   debugPrint(e.toString());
    // }
    return false;
  }
}

class LeaveRequestFilterModel {
  final String text;
  final int id;
  LeaveRequestFilterModel(this.text, this.id);
}
// https://hisense-hrportal.e8demo.com/api/emp_leave_history/user_id(leave)/
