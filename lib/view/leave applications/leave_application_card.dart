// ignore_for_file: use_build_context_synchronously

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/other_screens/overview/screens/overview_screen.dart';
import 'package:provider/provider.dart';
import 'package:e8_hr_portal/model/employee_leave_detailes.dart' as req;

class LeaveApplicationCard extends StatelessWidget {
  final String? createDate;
  final int? leaveId;
  final String? fromLeaveDay;
  final String? reason;
  final String? endDate;
  final String? noOfDays;
  final String? date;
  final String? leaveType;
  final int? leaveDay;
  final String? status;
  final String? topDate;
  final bool showDate;
  final String? toleaveDay;
  final int leaveID;
  final String dayType;
  final num dayCount;
  final List<req.ReportingPersonList>? reportingPersonList;
  final req.HrStatus? hrStatus;
  const LeaveApplicationCard({
    super.key,
    required this.createDate,
    required this.fromLeaveDay,
    required this.reason,
    required this.showDate,
    required this.topDate,
    required this.noOfDays,
    required this.date,
    required this.leaveType,
    required this.leaveDay,
    required this.endDate,
    required this.status,
    required this.toleaveDay,
    required this.leaveId,
    required this.dayType,
    required this.leaveID,
    required this.dayCount,
    required this.reportingPersonList,
    required this.hrStatus,
  });

  @override
  Widget build(BuildContext context) {
    String? leaveDate;
    String? dateEnd;
    if (date != null) {
      leaveDate = date;
    }
    if (endDate != null) {
      dateEnd = endDate;
    }
    String text = status ?? 'Pending';

    String leaveTypeIcon;
    switch (leaveType.toString().trim()) {
      case 'Annual Leave':
        leaveTypeIcon = 'assets/icons/calendar-2.png';
        break;
      case 'Unpaid Leave':
        leaveTypeIcon = 'assets/icons/calculator.png';
        break;
      case 'Sick Leave':
        leaveTypeIcon = 'assets/icons/hospital.png';
        break;
      default:
        leaveTypeIcon = 'assets/icons/work_update.png';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        showDate
            ? Padding(
                padding: const EdgeInsets.only(left: 13.0, top: 20),
                child: Align(
                    alignment: Alignment.topLeft,
                    child: Text(
                      topDate.toString(),
                      style: tsS14c8391B5,
                    )),
              )
            : Container(),
        const SizedBox(height: 9),
        InkWell(
          onTap: () async {
            final provid =
                Provider.of<LeaveApplicationProvider>(context, listen: false);
            EasyLoading.show();
            bool isGo = await provid.getLeaveOverView(leaveId: leaveId);
            bool isGetRepPerson = await provid.getReportingPerson();
            EasyLoading.dismiss();
            if (isGo && isGetRepPerson) {
              PageNavigator.push(
                  context: context, route: const OverViewScreen());
            }
          },
          child: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(8)),
            height: 100 * h,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Container(
                        height: 45 * h,
                        width: 45 * w,
                        margin: const EdgeInsets.only(right: 10),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: ThemeColors.colorFCC400.withOpacity(0.10),
                        ),
                        child: Center(
                          child: ImageIcon(
                            AssetImage(leaveTypeIcon),
                            color: ThemeColors.colorFCC400,
                          ),
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Align(
                              alignment: Alignment.topLeft,
                              child: Text(
                                leaveType ?? '',
                                style: tsS14w500Black,
                              ),
                            ),
                            const SizedBox(
                              height: 7,
                            ),
                            Row(
                              children: [
                                ImageIcon(
                                  const AssetImage(
                                      'assets/icons/calendar-3.png'),
                                  color: ThemeColors.colorFCC400,
                                  size: 12,
                                ),
                                const SizedBox(width: 5),
                                Text(
                                  '$leaveDate - $dateEnd • ',
                                  style: tsS10w400979797,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Expanded(
                                  child: Text(
                                    noOfDays == '0.5'
                                        ? 'Half day'
                                        : noOfDays == '1.0'
                                            ? '$noOfDays day'
                                            : '$noOfDays days',
                                    style: tsS10w400979797,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                )
                              ],
                            ),
                            SizedBox(height: h * 3.52),
                            Row(
                              children: [
                                if (reportingPersonList != null)
                                  _reportingPersonsWidget(
                                      data: reportingPersonList),
                                if (hrStatus != null)
                                  _hrApprovalWidget(
                                      profilePic: hrStatus?.profilePic ?? '',
                                      name: hrStatus?.name ?? '',
                                      isApprove: hrStatus?.isApprove ?? ''),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    SizedBox(height: h * 8),
                    Container(
                      padding: const EdgeInsets.fromLTRB(10, 4, 10, 4),
                      decoration: BoxDecoration(
                          color: text == 'Pending'
                              ? ThemeColors.colorFFF2EF
                              : text == 'Rejected'
                                  ? ThemeColors.colorF64D44.withOpacity(0.14)
                                  : text == 'Approved'
                                      ? ThemeColors.color03AD9E
                                          .withOpacity(0.10)
                                      : text == 'Expired' ||
                                              text == 'Holiday' ||
                                              text == 'Cancelled'
                                          ? ThemeColors.colorF64D44
                                              .withOpacity(0.14)
                                          : text == 'In-progress'
                                              ? ThemeColors.colorFFF2EF
                                              : null,
                          borderRadius: BorderRadius.circular(8)),
                      child: Text(text,
                          style: text == 'Pending'
                              ? tsS12W6FE5B900
                              : text == 'In-progress'
                                  ? tsS12W6FE5B900
                                  : text == 'Rejected'
                                      ? tsS12w600cF64D44
                                      : text == 'Approved'
                                          ? tsS12w600c519C66
                                          : text == 'Cancelled'
                                              ? tsS12w600cF64D44
                                              : text == 'Expired' ||
                                                      text == 'Holiday'
                                                  ? tsS12w600cF64D44
                                                  : null),
                    ),
                    SizedBox(height: h * 5),
                    Text(
                      createDate ?? '',
                      overflow: TextOverflow.ellipsis,
                      style: GoogleFonts.poppins(
                          fontSize: 10,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xff979797)),
                    )
                  ],
                ),
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget _hrApprovalWidget(
      {required String profilePic,
      required String name,
      required String isApprove}) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: ClipRRect(
              borderRadius: BorderRadius.circular(5.0),
              child: ClipOval(
                child: Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: profilePic.toString(),
                      width: 30 * w,
                      height: 30 * h,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) {
                        return Container(
                          height: 30,
                          width: 30,
                          decoration: BoxDecoration(
                            color: ThemeColors.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            name.substring(0, 1).toUpperCase(),
                            style: GoogleFonts.rubik(
                                fontSize: 22,
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                          ),
                        );
                      },
                    ),
                    if (isApprove.toLowerCase().trim() == 'pending')
                      Container(
                        height: 30 * h,
                        width: 30 * w,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                  ],
                ),
              )),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: Column(
            children: [
              if (isApprove.toLowerCase().trim() == 'pending') const SizedBox(),
              if (isApprove.toLowerCase().trim() == 'true')
                ImageIcon(
                  const AssetImage(
                    'assets/icons/tick.png',
                  ),
                  size: 13,
                  color: ThemeColors.color06AA37,
                ),
              if (isApprove.toLowerCase().trim() == 'false')
                ImageIcon(
                  const AssetImage(
                    'assets/icons/close_red.png',
                  ),
                  size: 13,
                  color: ThemeColors.colorB80000,
                ),
            ],
          ),
        )
      ],
    );
  }

  Widget _reportingPersonsWidget(
      {required List<req.ReportingPersonList>? data}) {
    return Row(
      children: data!.map((e) {
        return Stack(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(5.0),
                  child: ClipOval(
                    child: Stack(
                      children: [
                        CachedNetworkImage(
                          imageUrl: e.profilePic.toString(),
                          width: 30 * w,
                          height: 30 * h,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) {
                            return Container(
                              height: 30,
                              width: 30,
                              decoration: BoxDecoration(
                                color: ThemeColors.primaryColor,
                                shape: BoxShape.circle,
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                e.name?.substring(0, 1).toUpperCase() ?? '',
                                style: GoogleFonts.rubik(
                                    fontSize: 22,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.white),
                              ),
                            );
                          },
                        ),
                        if (e.isApprove?.toLowerCase().trim() == 'pending')
                          Container(
                            height: 30 * h,
                            width: 30 * w,
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                      ],
                    ),
                  )),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              child: Column(
                children: [
                  if (e.isApprove?.toLowerCase().trim() == 'pending')
                    const SizedBox(),
                  if (e.isApprove?.toLowerCase().trim() == 'true')
                    ImageIcon(
                      const AssetImage(
                        'assets/icons/tick.png',
                      ),
                      size: 13,
                      color: ThemeColors.color06AA37,
                    ),
                  if (e.isApprove?.toLowerCase().trim() == 'false')
                    ImageIcon(
                      const AssetImage(
                        'assets/icons/close_red.png',
                      ),
                      size: 13,
                      color: ThemeColors.colorB80000,
                    ),
                ],
              ),
            )
          ],
        );
      }).toList(),
    );
  }
}
