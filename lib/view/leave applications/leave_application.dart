// ignore_for_file: use_build_context_synchronously

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/leave%20applications/leave_application_card.dart';
import 'package:e8_hr_portal/view/other_screens/new_leave/new_leave.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import 'leave_history_screen.dart';
import 'package:e8_hr_portal/model/employee_leave_detailes.dart' as model;

class LeaveApplicationScreen extends StatefulWidget {
  const LeaveApplicationScreen({super.key});

  @override
  State<LeaveApplicationScreen> createState() => _LeaveApplicationScreenState();
}

class _LeaveApplicationScreenState extends State<LeaveApplicationScreen> {
  late LeaveApplicationProvider leaveApplicationProvider;
  final ScrollController _scrollController = ScrollController();
  String? firstDate;

  @override
  void initState() {
    leaveApplicationProvider = context.read<LeaveApplicationProvider>();
    leaveApplicationProvider.getLeaveTypes();
    leaveApplicationProvider.getUserList();
    leaveApplicationProvider.initPageLeaveRecords(master: false);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 20),
      decoration: BoxDecoration(
        color: ThemeColors.colorF4F5FA,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
      ),
      child: Consumer<LeaveApplicationProvider>(
        builder: (context, provider, _) {
          if (provider.leaveBalanceModel == null) {
            return const SizedBox();
          }
          return Stack(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
                child: provider.msg == "Data not found"
                    ? Column(
                        children: [
                          if (provider.leaveBalanceModel?.message != null)
                            SizedBox(
                              height: h * 256,
                              child: Center(
                                child: Text(
                                  "${provider.leaveBalanceModel?.message}",
                                ),
                              ),
                            ),
                          if (provider.leaveBalanceModel?.message == null)
                            Column(
                              children: [
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text(
                                    "Leave Overview",
                                    style: tsS16w500,
                                  ),
                                ),
                                const SizedBox(height: 15),
                                if (provider.leaveBalanceModel != null &&
                                    provider.leaveBalanceModel?.data != null)
                                  Consumer<LeaveApplicationProvider>(
                                    builder: (context, provider, _) {
                                      return Wrap(
                                        runSpacing: h * 15,
                                        spacing: w * 15,
                                        children: provider
                                            .leaveBalanceModel!.data!
                                            .map((e) {
                                          String? leaveType = e.leaveType?.name;

                                          String leaveTypeIcon;
                                          switch (leaveType.toString().trim()) {
                                            case "Annual Leave":
                                              leaveTypeIcon =
                                                  "assets/icons/calendar-2.png";
                                              break;
                                            case "Unpaid Leave":
                                              leaveTypeIcon =
                                                  "assets/icons/calculator.png";

                                              break;
                                            case "Sick Leave":
                                              leaveTypeIcon =
                                                  "assets/icons/hospital.png";

                                              break;
                                            default:
                                              leaveTypeIcon =
                                                  "assets/icons/work_update.png";
                                          }
                                          String leaveBalance =
                                              "${e.leavesTaken}/${e.totalLeave}";
                                          if (leaveType == "Unpaid Leave") {
                                            leaveBalance = "${e.leavesTaken}";
                                          } else if (leaveType ==
                                              "Annual Leave") {
                                            leaveBalance =
                                                "${e.availableLeave}";
                                          }
                                          Color iconColor = Theme.of(context)
                                              .colorScheme
                                              .secondary;
                                          return InkWell(
                                            onTap: () async {
                                              if (e.leaveType?.id != null) {
                                                Navigator.of(context).push(
                                                  MaterialPageRoute(
                                                    builder: (context) =>
                                                        LeaveHistoryScreen(
                                                      leaveType:
                                                          e.leaveType?.name ??
                                                              "",
                                                      leaveTypeIcon:
                                                          leaveTypeIcon,
                                                      leaveId: e.leaveType!.id
                                                          .toString(),
                                                    ),
                                                  ),
                                                );
                                              }
                                            },
                                            child: Container(
                                              height: 90 * h,
                                              width: 164 * w,
                                              padding: const EdgeInsets.all(9),
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                              child: Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Container(
                                                    height: 45 * h,
                                                    width: 45 * w,
                                                    margin:
                                                        const EdgeInsets.only(
                                                            right: 8),
                                                    decoration: BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      color: iconColor
                                                          .withOpacity(0.10),
                                                    ),
                                                    child: Center(
                                                        child: ImageIcon(
                                                      AssetImage(leaveTypeIcon),
                                                      color: iconColor,
                                                    )),
                                                  ),
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        Text(
                                                          leaveBalance,
                                                          style: tsS14w500Black,
                                                        ),
                                                        Text(
                                                          "${e.leaveType?.name}",
                                                          style:
                                                              tsS12w400979797,
                                                        )
                                                      ],
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                          );
                                        }).toList(),
                                      );
                                    },
                                  ),
                              ],
                            ),
                          const SizedBox(
                            height: 20,
                          ),
                        ],
                      )
                    : NestedScrollView(
                        physics: const ClampingScrollPhysics(),
                        controller: _scrollController,
                        headerSliverBuilder: (context, value) {
                          return [
                            SliverToBoxAdapter(
                                child: Column(
                              children: [
                                // if (provider.leaveBalanceModel?.message != null)
                                //   SizedBox(
                                //     height: h * 256,
                                //     child: Center(
                                //         child: Text(
                                //             "${provider.leaveBalanceModel?.message}")),
                                //   ),
                                // if (provider.leaveBalanceModel?.message == null)
                                Column(
                                  children: [
                                    Align(
                                      alignment: Alignment.topLeft,
                                      child: Text(
                                        "Leave Overview",
                                        style: tsS16w500,
                                      ),
                                    ),
                                    const SizedBox(height: 15),
                                    if (provider.leaveBalanceModel != null &&
                                        provider.leaveBalanceModel?.data !=
                                            null)
                                      Consumer<LeaveApplicationProvider>(
                                        builder: (context, provider, _) {
                                          return Wrap(
                                            runSpacing: h * 15,
                                            spacing: w * 15,
                                            children: provider
                                                .leaveBalanceModel!.data!
                                                .map((e) {
                                              String? leaveType =
                                                  e.leaveType?.name;

                                              String leaveTypeIcon;
                                              switch (
                                                  leaveType.toString().trim()) {
                                                case "Annual Leave":
                                                  leaveTypeIcon =
                                                      "assets/icons/calendar-2.png";
                                                  break;
                                                case "Unpaid Leave":
                                                  leaveTypeIcon =
                                                      "assets/icons/calculator.png";

                                                  break;
                                                case "Sick Leave":
                                                  leaveTypeIcon =
                                                      "assets/icons/hospital.png";

                                                  break;
                                                default:
                                                  leaveTypeIcon =
                                                      "assets/icons/work_update.png";
                                              }
                                              String leaveBalance =
                                                  "${e.leavesTaken}/${e.totalLeave}";
                                              if (leaveType == "Unpaid Leave") {
                                                leaveBalance =
                                                    "${e.leavesTaken}";
                                              } else if (leaveType ==
                                                  "Annual Leave") {
                                                leaveBalance =
                                                    "${e.availableLeave}";
                                              }
                                              return InkWell(
                                                onTap: () async {
                                                  if (e.leaveType?.id != null) {
                                                    Navigator.of(context).push(
                                                      MaterialPageRoute(
                                                        builder: (context) =>
                                                            LeaveHistoryScreen(
                                                          leaveType: e.leaveType
                                                                  ?.name ??
                                                              "",
                                                          leaveTypeIcon:
                                                              leaveTypeIcon,
                                                          leaveId: e
                                                              .leaveType!.id
                                                              .toString(),
                                                        ),
                                                      ),
                                                    );
                                                  }
                                                },
                                                child: Container(
                                                  height: 90 * h,
                                                  width: 164 * w,
                                                  padding:
                                                      const EdgeInsets.all(9),
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12),
                                                  ),
                                                  child: Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      Container(
                                                        height: 45 * h,
                                                        width: 45 * w,
                                                        margin: const EdgeInsets
                                                            .only(right: 8),
                                                        decoration:
                                                            BoxDecoration(
                                                          shape:
                                                              BoxShape.circle,
                                                          color: ThemeColors
                                                              .colorFCC400
                                                              .withOpacity(
                                                                  0.10),
                                                        ),
                                                        child: Center(
                                                            child: ImageIcon(
                                                          AssetImage(
                                                              leaveTypeIcon),
                                                          color: ThemeColors
                                                              .colorFCC400,
                                                        )),
                                                      ),
                                                      Expanded(
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .center,
                                                          children: [
                                                            Text(
                                                              leaveBalance,
                                                              style:
                                                                  tsS14w500Black,
                                                            ),
                                                            Text(
                                                              "${e.leaveType?.name}",
                                                              style:
                                                                  tsS12w400979797,
                                                            )
                                                          ],
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                              );
                                            }).toList(),
                                          );
                                        },
                                      ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                              ],
                            )),
                          ];
                        },
                        body: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (provider.empLeaveDetailesRecords != null &&
                                provider.empLeaveDetailesRecords!.isNotEmpty)
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child:
                                        Text("Leave Records", style: tsS16w500),
                                  ),
                                  Container(
                                    padding:
                                        const EdgeInsets.fromLTRB(10, 5, 5, 5),
                                    width: 90 * w,
                                    height: 30 * h,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: Consumer<LeaveApplicationProvider>(
                                      builder: (context, provider, _) {
                                        return DropdownButton(
                                          isExpanded: true,
                                          underline: const SizedBox(),
                                          value: provider.selectedTypeOfLeave,
                                          items: provider.leaveRecordsList
                                              .map((item) {
                                            return DropdownMenuItem<String>(
                                              value: item,
                                              child: FittedBox(
                                                child: Text(
                                                  item.toString(),
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                          onChanged: (String? value) {
                                            if (value != null) {
                                              provider.selectedTypeOfLeave =
                                                  value;
                                              provider.currentPageLeaveRecords =
                                                  0;
                                              provider
                                                  .pagingControllerLeaveRecords
                                                  ?.refresh();
                                              // provider
                                              //     .pagingControllerLeaveRecords
                                              //     ?.notifyListeners();
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  )
                                ],
                              ),
                            Expanded(
                              child: Consumer<LeaveApplicationProvider>(
                                builder: (context, provider, _) {
                                  return PagedListView.separated(
                                    physics: const ClampingScrollPhysics(),
                                    shrinkWrap: true,
                                    padding: EdgeInsets.only(
                                        top: h * 15, bottom: h * 75),
                                    pagingController:
                                        provider.pagingControllerLeaveRecords!,
                                    builderDelegate:
                                        PagedChildBuilderDelegate<model.Data>(
                                      noItemsFoundIndicatorBuilder: (_) {
                                        if (provider
                                                .leaveBalanceModel?.message !=
                                            null) {
                                          return const SizedBox();
                                        }
                                        return const Padding(
                                          padding: EdgeInsets.only(top: 80.0),
                                          child: Center(
                                              child: Text("No Data Found")),
                                        );
                                      },
                                      // noMoreItemsIndicatorBuilder: (context) => const Text("End"),
                                      newPageProgressIndicatorBuilder: (_) {
                                        return Center(
                                          child: CircularProgressIndicator(
                                              color: ThemeColors.color06AA37),
                                        );
                                      },
                                      firstPageProgressIndicatorBuilder: (_) {
                                        return const SizedBox();
                                      },
                                      itemBuilder: (context, data, index) {
                                        String? leaveDate;
                                        if (data.leaveDetails?.startDate !=
                                            null) {
                                          leaveDate = formatDateFromString(
                                              data.leaveDetails!.startDate!,
                                              "dd MMM,yyyy",
                                              "MMM ,yyyy");
                                        }
                                        bool showDate = false;
                                        if (firstDate != leaveDate) {
                                          firstDate = leaveDate;
                                          showDate = true;
                                        }

                                        // List<Data>? filteredList =
                                        //     provider.empLeaveDetailesRecords?.where((e) {
                                        //   return e.status == provider.selectedTypeOfLeave;
                                        // }).toList();
                                        if (provider.selectedTypeOfLeave
                                                .toLowerCase() ==
                                            'all') {
                                          return LeaveApplicationCard(
                                            createDate:
                                                data.leaveDetails?.createdAt,
                                            leaveId: data.leaveDetails?.id,
                                            fromLeaveDay:
                                                data.leaveDetails?.startDate,
                                            toleaveDay: data
                                                .leaveDetails?.endDate
                                                .toString(),
                                            reason: data.leaveDetails?.reason
                                                .toString(),
                                            endDate: data.leaveDetails?.endDate
                                                .toString(),
                                            topDate: leaveDate.toString(),
                                            date: data.leaveDetails?.startDate,
                                            leaveDay:
                                                data.leaveDetails?.dayType ==
                                                        "Full Day"
                                                    ? 1
                                                    : 2,
                                            noOfDays: data
                                                .leaveDetails?.dayCount
                                                .toString(),
                                            leaveType:
                                                data.leaveDetails?.leaveType,
                                            showDate: showDate,
                                            status: data.leaveDetails?.status
                                                .toString(),
                                            leaveID: data.leaveDetails?.id ?? 0,
                                            dayType:
                                                data.leaveDetails?.dayType ??
                                                    "Full Day",
                                            dayCount:
                                                data.leaveDetails?.dayCount ??
                                                    1,
                                            reportingPersonList:
                                                data.reportingPersonList,
                                            hrStatus: data.hrStatus,
                                          );
                                        }
                                        return LeaveApplicationCard(
                                          createDate:
                                              data.leaveDetails?.createdAt,
                                          leaveId: data.leaveDetails?.id,
                                          fromLeaveDay:
                                              data.leaveDetails?.startDate,
                                          toleaveDay: data.leaveDetails?.endDate
                                              .toString(),
                                          reason: data.leaveDetails?.reason
                                              .toString(),
                                          endDate: data.leaveDetails?.endDate
                                              .toString(),
                                          topDate: leaveDate.toString(),
                                          date: data.leaveDetails?.startDate,
                                          leaveDay:
                                              data.leaveDetails?.dayType ==
                                                      "Full Day"
                                                  ? 1
                                                  : 2,
                                          noOfDays: data.leaveDetails?.dayCount
                                              .toString(),
                                          leaveType:
                                              data.leaveDetails?.leaveType,
                                          showDate: showDate,
                                          status: data.leaveDetails?.status
                                              .toString(),
                                          leaveID: data.leaveDetails?.id ?? 0,
                                          dayType: data.leaveDetails?.dayType ??
                                              "Full Day",
                                          dayCount:
                                              data.leaveDetails?.dayCount ?? 1,
                                          reportingPersonList:
                                              data.reportingPersonList,
                                          hrStatus: data.hrStatus,
                                        );
                                      },
                                    ),
                                    separatorBuilder: (context, index) =>
                                        SizedBox(height: h * 10),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
              Positioned(
                bottom: 10 * h,
                left: 15 * w,
                child: GeneralButton(
                  title: "Apply Leave",
                  textStyle: tsS16w600FFFFFF,
                  height: h * 56,
                  width: w * 343,
                  isDisabled: isLoading,
                  onPressed: isLoading
                      ? null
                      : () async {
                          isLoading = true;
                          // Provider.of<ListLeaveProviders>(context,
                          //         listen: false)
                          //     .loadListLeaves();
                          LeaveApplicationProvider provider =
                              Provider.of<LeaveApplicationProvider>(context,
                                  listen: false);
                          provider.leaveOverviewModel = null;
                          EasyLoading.show();
                          if (provider.leaveTypesList == null) {
                            await provider.getLeaveTypes();
                          }
                          if (provider.userListModel == null) {
                            await provider.getUserList();
                          }
                          if (provider.staffInChargeList == null ||
                              provider.staffInChargeList!.isEmpty) {
                            await provider.getStaffinCharge();
                          }
                          await provider.getLeaveBalance(
                              master: false, context: context);
                          provider.formattedToDate = formatDateFromDate(
                              dateTime: DateTime.now(), format: "dd MMM yyyy");
                          provider.formattedFromDate = formatDateFromDate(
                              dateTime: DateTime.now(), format: "dd MMM yyyy");
                          provider.selectedFromDate = DateTime(
                              DateTime.now().year,
                              DateTime.now().month,
                              DateTime.now().day);
                          provider.selectedToDate = DateTime(
                              DateTime.now().year,
                              DateTime.now().month,
                              DateTime.now().day);
                          provider.pickedFile = null;
                          provider.remainingLeaves = null;
                          provider.selectedLeaveType = null;
                          provider.selectedUserID = null;
                          if (!mounted) return;
                          PageNavigator.pushSlideup(
                            context: context,
                            route: const NewLeaveScreens(),
                          );
                          EasyLoading.dismiss();
                          isLoading = false;
                        },
                ),
              )
            ],
          );
        },
      ),
    );
  }

  bool _isLoading = false;
  bool get isLoading => _isLoading;
  set isLoading(bool value) {
    setState(() => _isLoading = value);
  }
}
