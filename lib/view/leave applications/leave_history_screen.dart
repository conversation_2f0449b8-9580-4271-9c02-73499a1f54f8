// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import '../../util/colors.dart';
import '../../util/page_navigator.dart';
import '../../util/size_config.dart';
import '../../util/styles.dart';
import '../other_screens/overview/screens/overview_screen.dart';
import 'package:e8_hr_portal/model/employee_leave_detailes.dart' as model;

class LeaveHistoryScreen extends StatefulWidget {
  final String leaveTypeIcon;
  final String leaveType;
  final String leaveId;
  const LeaveHistoryScreen(
      {super.key,
      required this.leaveTypeIcon,
      required this.leaveType,
      required this.leaveId});

  @override
  State<LeaveHistoryScreen> createState() => _LeaveHistoryScreenState();
}

class _LeaveHistoryScreenState extends State<LeaveHistoryScreen> {
  @override
  void initState() {
    var provider =
        Provider.of<LeaveApplicationProvider>(context, listen: false);
    provider.initPageLeaveTypes(leaveTypeId: widget.leaveId);
    provider.currentPageLeaveTypes = 0;
    provider.pagingControllerLeaveTypes?.refresh();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        screenTitle: 'Leave History',
        body: Padding(
          padding: const EdgeInsets.all(15.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "${widget.leaveType} History",
                style: tsS16w500,
              ),
              const SizedBox(height: 5),
              Expanded(child: Consumer<LeaveApplicationProvider>(
                builder: (context, provider, child) {
                  return PagedListView.separated(
                    padding: const EdgeInsets.only(top: 10, bottom: 40),
                    pagingController: provider.pagingControllerLeaveTypes!,
                    builderDelegate: PagedChildBuilderDelegate<model.Data>(
                      noItemsFoundIndicatorBuilder: (context) {
                        return const Center(
                          child: Text("No Leave History"),
                        );
                      },
                      newPageProgressIndicatorBuilder: (_) {
                        return Center(
                          child: CircularProgressIndicator(
                              color: ThemeColors.color06AA37),
                        );
                      },
                      firstPageProgressIndicatorBuilder: (_) {
                        return Center(
                          child: CircularProgressIndicator(
                              color: ThemeColors.primaryColor),
                        );
                      },
                      itemBuilder: (context, item, index) {
                        return InkWell(
                          onTap: () async {
                            final provid =
                                Provider.of<LeaveApplicationProvider>(context,
                                    listen: false);
                            EasyLoading.show();
                            bool isGo =
                                await provid.getLeaveOverView(leaveId: item.id);
                            bool isGetRepPerson =
                                await provid.getReportingPerson();
                            EasyLoading.dismiss();

                            if (isGo && isGetRepPerson) {
                              PageNavigator.push(
                                  context: context,
                                  route: const OverViewScreen());
                            }
                          },
                          child: LeaveHistoryCard(
                            leaveTypeIcon: widget.leaveTypeIcon,
                            leaveType: item.leaveDetails?.leaveType,
                            leaveDate: item.leaveDetails?.startDate,
                            dateEnd: item.leaveDetails?.endDate,
                            noOfDays: item.leaveDetails?.dayCount.toString(),
                            createDate: item.leaveDetails?.createdAt,
                            status: item.leaveDetails?.status,
                          ),
                        );
                      },
                    ),
                    separatorBuilder: (context, index) {
                      return SizedBox(height: h * 10);
                    },
                  );

                  // ListView.separated(
                  //     separatorBuilder: (context, index) {
                  //       return const SizedBox(
                  //         height: 9,
                  //       );
                  //     },
                  //     itemCount:
                  //         provider.leaveHistoryList?.data?.length ?? 0,
                  //     itemBuilder: (context, index) {
                  //       final data =
                  //           provider.leaveHistoryList?.data?[index];
                  //       return InkWell(
                  //         onTap: () async {
                  //           final provid =
                  //               Provider.of<LeaveApplicationProvider>(
                  //                   context,
                  //                   listen: false);
                  //           EasyLoading.show();
                  //           bool isGo = await provid.getLeaveOverView(
                  //               leaveId: data?.id);
                  //           bool isGetRepPerson =
                  //               await provid.getReportingPerson();
                  //           EasyLoading.dismiss();

                  //           if (isGo && isGetRepPerson) {
                  //             // ignore: use_build_context_synchronously
                  //             PageNavigator.push(
                  //                 context: context,
                  //                 route: OverViewScreen());
                  //           }
                  //         },
                  //         child: LeaveHistoryCard(
                  //           leaveTypeIcon: widget.leaveTypeIcon,
                  //           leaveType: data?.leaveType,
                  //           leaveDate: data?.startDate,
                  //           dateEnd: data?.endDate,
                  //           noOfDays: data?.dayCount.toString(),
                  //           createDate: data?.createdAt,
                  //           status: data?.status,
                  //         ),
                  //       );
                  //     },
                  //   );
                },
              ))
            ],
          ),
        ));
  }
}

class LeaveHistoryCard extends StatelessWidget {
  final String leaveTypeIcon;
  final String? leaveType;
  final String? leaveDate;
  final String? dateEnd;
  final String? noOfDays;
  final String? createDate;
  final String? status;
  const LeaveHistoryCard(
      {super.key,
      required this.leaveTypeIcon,
      required this.leaveType,
      required this.leaveDate,
      required this.dateEnd,
      required this.noOfDays,
      required this.createDate,
      this.status = "Pending"});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // showDate
          //     ? Padding(
          //         padding: const EdgeInsets.only(left: 13.0, top: 20),
          //         child: Align(
          //             alignment: Alignment.topLeft,
          //             child: Text(
          //               topDate.toString(),
          //               style: tsS14c8391B5,
          //             )),
          //       )
          //     : Container(),
          // const SizedBox(
          //   height: 9,
          // ),
          InkWell(
            // onTap: () async {
            //   final provid =
            //       Provider.of<LeaveApplicationProvider>(context, listen: false);
            //   EasyLoading.show();
            //   bool isGo = await provid.getLeaveOverView(leaveId: leaveId);
            //   bool isGetRepPerson = await provid.getReportingPerson();
            //   EasyLoading.dismiss();

            //   if (isGo && isGetRepPerson) {

            //     PageNavigator.push(
            //         context: context,
            //         route: OverViewScreen(

            //             ));
            //   }
            // },
            child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(8)),
              height: 85,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          height: 45 * h,
                          width: 45 * w,
                          margin: const EdgeInsets.only(right: 10),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ThemeColors.colorFCC400.withOpacity(0.10),
                          ),
                          child: Center(
                            child: ImageIcon(
                              AssetImage(leaveTypeIcon),
                              color: ThemeColors.colorFCC400,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Align(
                                alignment: Alignment.topLeft,
                                child: Text(
                                  leaveType ?? "",
                                  style: tsS14w500Black,
                                ),
                              ),
                              const SizedBox(
                                height: 7,
                              ),
                              Row(
                                children: [
                                  ImageIcon(
                                    const AssetImage(
                                        "assets/icons/calendar-3.png"),
                                    color: ThemeColors.colorFCC400,
                                    size: 12,
                                  ),
                                  const SizedBox(width: 5),
                                  Text(
                                    "$leaveDate - $dateEnd • ",
                                    style: tsS12w400979797,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Expanded(
                                    child: Text(
                                      noOfDays == "0.5"
                                          ? "Half day"
                                          : noOfDays == "1.0"
                                              ? "$noOfDays day"
                                              : "$noOfDays days",
                                      style: tsS12w400979797,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  )
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      SizedBox(height: h * 8),
                      Container(
                        padding: const EdgeInsets.fromLTRB(10, 4, 10, 4),
                        decoration: BoxDecoration(
                            color: status == "Pending"
                                ? ThemeColors.colorFFF2EF
                                : status == "Rejected"
                                    ? ThemeColors.colorF64D44.withOpacity(0.14)
                                    : status == "Approved"
                                        ? ThemeColors.color03AD9E
                                            .withOpacity(0.10)
                                        : status == "Cancelled"
                                            ? ThemeColors.colorF64D44
                                                .withOpacity(0.14)
                                            : status == "Expired"
                                                ? ThemeColors.colorF64D44
                                                    .withOpacity(0.14)
                                                : status == "In-progress"
                                                    ? ThemeColors.colorFFF2EF
                                                    : null,
                            borderRadius: BorderRadius.circular(8)),
                        child: Text(status ?? "Pending",
                            style: status == "Pending"
                                ? tsS12W6FE5B900
                                : status == "Rejected"
                                    ? tsS12w600cF64D44
                                    : status == "Approved"
                                        ? tsS12w600c519C66
                                        : status == "Cancelled"
                                            ? tsS12w600cF64D44
                                            : status == "Expired"
                                                ? tsS12w600cF64D44
                                                : status == "In-progress"
                                                    ? tsS12W6FE5B900
                                                    : null),
                      ),
                      SizedBox(height: h * 5),
                      Text(
                        createDate ?? "",
                        overflow: TextOverflow.ellipsis,
                        style: GoogleFonts.poppins(
                            fontSize: 10,
                            fontWeight: FontWeight.w400,
                            color: const Color(0xff979797)),
                      )
                    ],
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
