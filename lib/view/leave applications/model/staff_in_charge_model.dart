class StaffInChargeModel {
  int? id;
  String? firstName;
  String? lastName;
  ProfileDetails? profileDetails;

  StaffInChargeModel(
      {this.id, this.firstName, this.lastName, this.profileDetails});

  StaffInChargeModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    profileDetails = json['profile_details'] != null
        ? ProfileDetails.fromJson(json['profile_details'])
        : null;
  }
}

class ProfileDetails {
  String? employeeId;
  String? designation;
  String? profilePic;

  ProfileDetails({this.employeeId, this.designation, this.profilePic});

  ProfileDetails.fromJson(Map<String, dynamic> json) {
    employeeId = json['employee_id'];
    designation = json['designation'];
    profilePic = json['profile_pic'];
  }
}
