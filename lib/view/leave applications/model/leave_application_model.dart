// To parse this JSON data, do
//
//     final leaveApplication = leaveApplicationFromJson(jsonString);

import 'dart:convert';

LeaveApplicationModel leaveApplicationFromJson(String str) =>
    LeaveApplicationModel.fromJson(json.decode(str) as Map<String, dynamic>);

String leaveApplicationToJson(LeaveApplicationModel data) =>
    json.encode(data.toJson());

class LeaveApplicationModel {
  LeaveApplicationModel({
    required this.status,
    required this.finalData,
  });

  bool status;
  FinalData finalData;

  factory LeaveApplicationModel.fromJson(Map<String, dynamic> json) =>
      LeaveApplicationModel(
        status: json["status"],
        finalData: FinalData.fromJson(json["final_data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "final_data": finalData.toJson(),
      };
}

class FinalData {
  FinalData({
    required this.leaveList,
    required this.leaveCounts,
  });

  List<LeaveList> leaveList;
  LeaveCounts leaveCounts;

  factory FinalData.fromJson(Map<String, dynamic> json) => FinalData(
        leaveList: List<LeaveList>.from(
            json["leave_list"].map((x) => LeaveList.fromJson(x))),
        leaveCounts: LeaveCounts.fromJson(json["leave_counts"]),
      );

  Map<String, dynamic> toJson() => {
        "leave_list": List<dynamic>.from(leaveList.map((x) => x.toJson())),
        "leave_counts": leaveCounts.toJson(),
      };
}

class LeaveCounts {
  LeaveCounts({
    required this.availableLeaves,
    required this.sickLeaves,
    required this.used,
  });

  int availableLeaves;
  int sickLeaves;
  int used;

  factory LeaveCounts.fromJson(Map<String, dynamic> json) => LeaveCounts(
        availableLeaves: json["available_leaves"],
        sickLeaves: json["sick_leaves"],
        used: json["used"],
      );

  Map<String, dynamic> toJson() => {
        "available_leaves": availableLeaves,
        "sick_leaves": sickLeaves,
        "used": used,
      };
}

class LeaveList {
  LeaveList({
    required this.firstname,
    required this.lastname,
    required this.username,
    required this.id,
    required this.userId,
    required this.fromDateSmallestPto,
    required this.toDateSmallestPto,
    required this.curUserid,
    required this.leaveType,
    required this.leaveFor,
    required this.fromDate,
    required this.toDate,
    required this.preYearleave,
    required this.noOfLeaveDays,
    required this.noOfDaysWithOut,
    required this.totalLeave,
    required this.reason,
    required this.status,
    required this.adminComment,
    required this.appliedDate,
    required this.officename,
  });

  String firstname;
  String lastname;
  String username;
  String id;
  String userId;
  String fromDateSmallestPto;
  String toDateSmallestPto;
  String curUserid;
  String leaveType;
  String leaveFor;
  DateTime fromDate;
  DateTime toDate;
  String preYearleave;
  String noOfLeaveDays;
  String noOfDaysWithOut;
  String totalLeave;
  String reason;
  String status;
  dynamic adminComment;
  DateTime appliedDate;
  String officename;

  factory LeaveList.fromJson(Map<String, dynamic> json) => LeaveList(
        firstname: json["firstname"],
        lastname: json["lastname"],
        username: json["username"],
        id: json["id"],
        userId: json["user_id"],
        fromDateSmallestPto: json["from_date_smallest_pto"],
        toDateSmallestPto: json["to_date_smallest_pto"],
        curUserid: json["cur_userid"],
        leaveType: json["leave_type"],
        leaveFor: json["leave_for"].toString(),
        fromDate: DateTime.parse(json["from_date"]),
        toDate: DateTime.parse(json["to_date"]),
        preYearleave: json["pre_yearleave"],
        noOfLeaveDays: json["no_of_leave_days"],
        noOfDaysWithOut: json["no_of_days_with_out"],
        totalLeave: json["total_leave"],
        reason: json["reason"],
        status: json["status"],
        adminComment: json["admin_comment"],
        appliedDate: DateTime.parse(json["applied_date"]),
        officename: json["officename"],
      );

  Map<String, dynamic> toJson() => {
        "firstname": firstname,
        "lastname": lastname,
        "username": username,
        "id": id,
        "user_id": userId,
        "from_date_smallest_pto": fromDateSmallestPto,
        "to_date_smallest_pto": toDateSmallestPto,
        "cur_userid": curUserid,
        "leave_type": leaveType,
        "leave_for": leaveFor,
        "from_date":
            "${fromDate.year.toString().padLeft(4, '0')}-${fromDate.month.toString().padLeft(2, '0')}-${fromDate.day.toString().padLeft(2, '0')}",
        "to_date":
            "${toDate.year.toString().padLeft(4, '0')}-${toDate.month.toString().padLeft(2, '0')}-${toDate.day.toString().padLeft(2, '0')}",
        "pre_yearleave": preYearleave,
        "no_of_leave_days": noOfLeaveDays,
        "no_of_days_with_out": noOfDaysWithOut,
        "total_leave": totalLeave,
        "reason": reason,
        "status": status,
        "admin_comment": adminComment,
        "applied_date":
            "${appliedDate.year.toString().padLeft(4, '0')}-${appliedDate.month.toString().padLeft(2, '0')}-${appliedDate.day.toString().padLeft(2, '0')}",
        "officename": officename,
      };
}
