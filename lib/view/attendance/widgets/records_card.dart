import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/view/attendance/widgets/punch_column_widget.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';

class RecordsCard extends StatefulWidget {
  final String? date;
  final String? punchinTime;
  final String? punchoutTime;
  final String? status;
  final String? colorKey;
  final bool isDisabled;
  const RecordsCard(
      {super.key,
      required this.punchinTime,
      required this.punchoutTime,
      required this.status,
      required this.date,
      required this.colorKey,
      required this.isDisabled});

  @override
  State<RecordsCard> createState() => _RecordsCardState();
}

class _RecordsCardState extends State<RecordsCard> {
  Color backgroundcolor = const Color(0xff4CD964).withOpacity(.15);

  Color textColor = const Color(0xff4CD964);

  @override
  void initState() {
    split(widget.date ?? "01 Wed");
    super.initState();
  }

  List dateList = [];
  void split(String date) {
    dateList = date.trim().split(" ");
  }

  @override
  Widget build(BuildContext context) {
    // if (widget.status?.toLowerCase() == 'holiday' ||
    //     widget.status?.toLowerCase() == 'leave') {
    //   isDisabled = true;
    // }
    switch (widget.colorKey) {
      case 'green':
        backgroundcolor = const Color(0xff4CD964).withOpacity(.15);

        textColor = const Color(0xff4CD964);

        break;
      case 'warning':
        backgroundcolor = const Color(0xffFFF2E2);

        textColor = const Color(0xffE5B900);

        break;
      case 'red':
        backgroundcolor = const Color(0xffF64D44).withOpacity(.15);

        textColor = const Color(0xffF64D44);

        break;
      case 'blue':
        backgroundcolor = const Color(0xffE4E8FD);

        textColor = const Color(0xff5570F1);

        break;
      case 'purple':
        backgroundcolor = const Color(0xffFF09C9).withOpacity(.15);

        textColor = const Color(0xffFF08C9);

        break;
    }
    return Container(
      height: 66 * h,
      decoration: BoxDecoration(
        color: widget.isDisabled ? Colors.white.withOpacity(.49) : Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            alignment: Alignment.center,
            width: 46 * w,
            margin: const EdgeInsets.only(
              left: 10,
              right: 15,
              top: 10,
              bottom: 10,
            ),
            decoration: BoxDecoration(
              color: ThemeColors.colorF8F8F8,
              shape: BoxShape.circle,
            ),
            child: Text(
              "${dateList.first}\n${dateList.last}",
              style: widget.isDisabled
                  ? GoogleFonts.poppins(
                      fontSize: 12 * f,
                      fontWeight: FontWeight.w500,
                      color: ThemeColors.primaryColor,
                    )
                  : tsS12w500c03AD9E,
              textAlign: TextAlign.center,
            ),
          ),
          PunchColumnWidget(
            title: "Punch In",
            time: widget.punchinTime ?? "",
            isDisabled: widget.isDisabled,
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 8 * w, vertical: 19),
            child: VerticalDivider(
              thickness: 1,
              color: const Color(0xff979797).withOpacity(.2),
            ),
          ),
          PunchColumnWidget(
            title: "Punch Out",
            time: widget.punchoutTime ?? "",
            isDisabled: widget.isDisabled,
          ),
          const Spacer(),
          if (widget.status != null)
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.25,
              child: Align(
                alignment: Alignment.centerRight,
                child: Container(
                  decoration: BoxDecoration(
                    color: backgroundcolor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 7,
                      vertical: 4,
                    ),
                    child: Text(
                      widget.status ?? "",
                      overflow: TextOverflow.ellipsis,
                      style: GoogleFonts.poppins(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: textColor,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          const SizedBox(width: 8)
        ],
      ),
    );
  }
}
