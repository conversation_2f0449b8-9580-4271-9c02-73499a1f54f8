import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/colors.dart';

import '../../../util/size_config.dart';
import '../../../util/styles.dart';

class OverviewCard extends StatelessWidget {
  final String icon;
  final String? title;
  final String? subtitle;
  const OverviewCard(
      {super.key,
      required this.icon,
      required this.title,
      required this.subtitle});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.only(right: 3),
        height: h * 75,
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(12)),
        child: Row(
          children: [
            Container(
              height: 46 * h,
              width: 46 * w,
              margin: const EdgeInsets.only(
                  left: 15, right: 9, top: 15, bottom: 15),
              decoration: BoxDecoration(
                color: ThemeColors.colorF8F8F8,
                shape: BoxShape.circle,
              ),
              child: ImageIcon(
                AssetImage(
                  icon,
                ),
                color: ThemeColors.primaryColor,
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title ?? "",
                    style: tsS14w500Black,
                  ),
                  Text(
                    subtitle ?? "",
                    style: tsS10w400979797,
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
