import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../util/styles.dart';

class PunchColumnWidget extends StatelessWidget {
  final bool isDisabled;
  final String title;
  final String time;
  const PunchColumnWidget({
    super.key,
    required this.isDisabled,
    required this.title,
    required this.time,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          title,
          style: isDisabled
              ? GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black.withOpacity(.2))
              : tsS12w500Black,
        ),
        Text(
          time,
          style: isDisabled
              ? GoogleFonts.poppins(
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xff979797).withOpacity(.15))
              : tsS10w400979797,
        )
      ],
    );
  }
}
