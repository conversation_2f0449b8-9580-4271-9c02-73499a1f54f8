// ignore_for_file: must_be_immutable, use_build_context_synchronously

import 'package:e8_hr_portal/view/attendance/widgets/attendence_vie_log.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/attendance_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';
import '../../../util/colors.dart';
import '../../../util/page_navigator.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../leave applications/provider/leave_apllication_provider.dart';
import '../../other_screens/new_leave/new_leave.dart';
import '../../widgets/general_button.dart';
import '../../widgets/hisense_text_form_field.dart';

class AttendanceOverviewScreen extends StatelessWidget {
  const AttendanceOverviewScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AttendanceProvider>(
      builder: (context, provider, child) {
        final data = provider.attendanceDetails?.records;

        return HisenseScaffold(
            actions: provider.attendanceDetails?.records?.buttonVisibility ==
                        true ||
                    provider.attendanceDetails?.records?.isLeaveButtonShow ==
                        true
                ? [
                    PopupMenuButton(
                      icon: const Icon(
                        Icons.more_vert,
                        color: Colors.white,
                      ),
                      onSelected: (value) async {
                        if (value == "request") {
                          showDialog(
                            context: context,
                            builder: (context) {
                              return DialogBoxForRequest(
                                id: data?.id,
                                attendanceDate: data?.attendanceDate,
                              );
                            },
                          );
                        } else if (value == "leave") {
                          LeaveApplicationProvider provider =
                              Provider.of<LeaveApplicationProvider>(context,
                                  listen: false);
                          await provider.getLeaveTypes();
                          await provider.getUserList();
                          await provider.getStaffinCharge();
                          await provider.getLeaveBalance(
                              master: false, context: context);
                          provider.formattedToDate = formatDateFromDate(
                              dateTime: DateTime.now(), format: "dd MMM yyyy");
                          provider.formattedFromDate = formatDateFromDate(
                              dateTime: DateTime.now(), format: "dd MMM yyyy");
                          provider.selectedFromDate = DateTime(
                              DateTime.now().year,
                              DateTime.now().month,
                              DateTime.now().day);
                          provider.selectedToDate = DateTime(
                              DateTime.now().year,
                              DateTime.now().month,
                              DateTime.now().day);
                          provider.pickedFile = null;
                          provider.remainingLeaves = null;
                          provider.selectedLeaveType = null;
                          provider.selectedUserID = null;
                          PageNavigator.push(
                            context: context,
                            route: const NewLeaveScreens(),
                          );
                        } else if (value == 'view_logs') {
                          // if (provider.attendanceViewLogModel?.attendanceLog!
                          //     .isNotEmpty) {
                          await provider.getAttendenceViewLog(
                              date: data!.attendanceDate);
                          showDialog(
                            context: context,
                            builder: (context) {
                              return const AttendenceViewLogDialog();
                            },
                          );
                          // } else {
                          //   showToastText("No records found");
                          // }
                        } else if (value == 'viewRequest') {
                          showDialog(
                            context: context,
                            builder: (context) {
                              return DialogBoxForViewRequest(
                                id: data?.id,
                                attendanceDate: data?.attendanceDate,
                                explanation: data?.requestExplanation,
                                reason: data?.requestReason,
                              );
                            },
                          );
                        }
                      },
                      itemBuilder: (context) {
                        return [
                          if (data?.status?.content.toString().toLowerCase() !=
                              "absent")
                            const PopupMenuItem(
                              value: 'view_logs',
                              child: Text("View Log"),
                            ),
                          if (provider.attendanceDetails?.records
                                  ?.buttonVisibility ==
                              true)
                            const PopupMenuItem(
                              value: 'request',
                              child: Text("Send Reason"),
                            ),
                          if (provider.attendanceDetails?.records
                                  ?.isLeaveButtonShow ==
                              true)
                            const PopupMenuItem(
                              value: 'leave',
                              child: Text("Apply Leave"),
                            ),
                          if (provider
                                  .attendanceDetails?.records?.requestReason !=
                              null)
                            const PopupMenuItem(
                              value: 'viewRequest',
                              child: Text("View Reason"),
                            ),
                        ];
                      },
                    )
                  ]
                : null,
            screenTitle: 'Overview',
            body: ListView(
              physics: const ClampingScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              children: [
                Text(
                  "Attendance Details",
                  style: tsS16w500,
                ),
                SizedBox(
                  height: h * 15,
                ),
                Container(
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      HeaderRowWidget(
                        date: data?.day,
                        month: data?.attendanceDate,
                        status: data?.status?.content,
                        colorKey: data?.status?.color,
                      ),
                      SizedBox(height: h * 24),
                      rowWidget(title: "Punch In", content: data?.punchin),
                      rowWidget(title: "Punch Out", content: data?.punchout),
                      rowWidget(title: "Arrival", content: data?.arrival),
                      rowWidget(
                          title: "Gross Hour",
                          content: data?.grossHours,
                          showDivider:
                              data?.status?.content?.toLowerCase() == "rejected"
                                  ? true
                                  : false),
                      if (data?.status?.content?.toLowerCase() == "rejected")
                        rowWidget(
                          showDivider: true,
                          title: "Rejected by",
                          content: data?.rejectedBy,
                        ),
                      if (data?.status?.content?.toLowerCase() == "rejected")
                        rowWidget(
                          showDivider: false,
                          title: "Reject Reasons",
                          content: data?.rejectedComment,
                        ),
                    ],
                  ),
                )
              ],
            ));
      },
    );
  }
}

class HeaderRowWidget extends StatefulWidget {
  final String? date;
  final String? month;
  final String? status;
  final String? colorKey;
  const HeaderRowWidget({
    super.key,
    required this.date,
    required this.month,
    required this.status,
    required this.colorKey,
  });

  @override
  State<HeaderRowWidget> createState() => _HeaderRowWidgetState();
}

class _HeaderRowWidgetState extends State<HeaderRowWidget> {
  @override
  void initState() {
    split(widget.date ?? "01 Wed");
    super.initState();
  }

  List dateList = [];
  void split(String date) {
    dateList = date.trim().split(" ");
  }

  Color backgroundcolor = const Color(0xff4CD964).withOpacity(.15);

  Color textColor = const Color(0xff4CD964);

  @override
  Widget build(BuildContext context) {
    switch (widget.colorKey) {
      case 'green':
        backgroundcolor = const Color(0xff4CD964).withOpacity(.15);

        textColor = const Color(0xff4CD964);

        break;
      case 'warning':
        backgroundcolor = const Color(0xffFFF2E2);

        textColor = const Color(0xffE5B900);

        break;
      case 'red':
        backgroundcolor = const Color(0xffF64D44).withOpacity(.15);

        textColor = const Color(0xffF64D44);

        break;
      case 'blue':
        backgroundcolor = const Color(0xffE4E8FD);

        textColor = const Color(0xff5570F1);

        break;
      case 'purple':
        backgroundcolor = const Color(0xffFF09C9).withOpacity(.15);

        textColor = const Color(0xffFF08C9);

        break;
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
            alignment: Alignment.center,
            height: 45 * h,
            width: 45 * w,
            margin: const EdgeInsets.only(right: 13),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: ThemeColors.colorF8F8F8,
            ),
            child: Text(
              "${dateList.first}\n${dateList.last}",
              style: tsS12w500c03AD9E,
              textAlign: TextAlign.center,
            )),
        Expanded(
            child: Text(
          widget.month != null
              ? formatDateFromString(widget.month!, "yyyy-MM-dd", "MMMM yyyy")
              : "",
          style: tsS12w500Black,
        )),
        if (widget.status != null)
          Container(
            decoration: BoxDecoration(
                color: backgroundcolor, borderRadius: BorderRadius.circular(8)),
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 10, right: 10, top: 4, bottom: 4),
              child: Text(
                widget.status ?? "",
                style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: textColor),
              ),
            ),
          ),
      ],
    );
  }
}

Widget rowWidget(
    {required String title,
    required String? content,
    bool showDivider = true}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        title,
        style: tsS12w400949494,
      ),
      const SizedBox(
        height: 3,
      ),
      Text(
        content ?? "",
        style: tsS14w500Black,
      ),
      const SizedBox(
        height: 5,
      ),
      if (showDivider)
        Divider(
          thickness: 1,
          color: ThemeColors.colorD9D9D9,
        ),
    ],
  );
}

// Widget _requestNewButton(
//     {required BuildContext context,
//     required int? id,
//     required String? attendanceDate}) {
//   return Padding(
//     padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
//     child: InkWell(
//       borderRadius: BorderRadius.circular(8),
//       radius: 10,
//       onTap: () async {
//         showDialog(
//           context: context,
//           builder: (context) {
//             return DialogBoxForRequest(
//               id: id,
//               attendanceDate: attendanceDate,
//             );
//           },
//         );
//       },
//       child: Align(
//         alignment: Alignment.center,
//         child: Text(
//           "Request",
//           style: tsS14w500cFFFFFF,
//         ),
//       ),
//     ),
//   );
// }

class DialogBoxForRequest extends StatelessWidget {
  final String? attendanceDate;
  final int? id;
  DialogBoxForRequest(
      {super.key, required this.id, required this.attendanceDate});
  final formKey = GlobalKey<FormState>();
  final TextEditingController reasonController = TextEditingController();
  final TextEditingController detailedReasonController =
      TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: w * 16),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(26),
      ),
      elevation: 0.0,
      backgroundColor: ThemeColors.colorFFFFFF,
      child: Padding(
        padding: EdgeInsets.fromLTRB(w * 20, h * 5, w * 5, h * 25),
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Attendance Request",
                    style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xff3E4259)),
                  ),
                  IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: Icon(
                      Icons.close,
                      color: ThemeColors.colorD6D6D6,
                      size: h * 20,
                    ),
                  ),
                ],
              ),
              SizedBox(height: h * 25),
              Text(
                "Reason",
                style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xff6E7079)),
              ),
              SizedBox(
                height: h * 6,
              ),
              Padding(
                padding: EdgeInsets.only(right: w * 15),
                child: HisenseTextFormField(
                  controller: reasonController,
                  hintStyle: tsS12w400c475366,
                  maxLines: 1,
                  validator: Validator.text,
                ),
              ),
              SizedBox(height: h * 15),
              Text(
                "Explanation",
                style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xff6E7079)),
              ),
              SizedBox(
                height: h * 6,
              ),
              Padding(
                padding: EdgeInsets.only(right: w * 15),
                child: HisenseTextFormField(
                  controller: detailedReasonController,
                  hintStyle: tsS12w400c475366,
                  hintText: 'Enter the reason',
                  maxLines: 3,
                  validator: Validator.text,
                ),
              ),
              SizedBox(
                height: 25 * h,
              ),
              Padding(
                padding: EdgeInsets.only(left: w * 7, right: w * 21),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _cancelButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        width: w * 140,
                        height: h * 40,
                        title: "Cancel",
                        textStyle: tsS14w500c475366),
                    Consumer<AttendanceProvider>(
                      builder: (context, provider, child) {
                        return GeneralButton(
                          title: "Submit",
                          height: h * 40,
                          textStyle: tsS14w500cFFFFFF,
                          width: w * 140,
                          onPressed: () {
                            if (formKey.currentState!.validate()) {
                              if (attendanceDate != null || id != null) {
                                provider.attendanceRequest(
                                    context: context,
                                    reason: reasonController.text,
                                    explanation: detailedReasonController.text,
                                    id: id,
                                    attendanceDate: attendanceDate.toString());
                                Navigator.pop(context);
                              }
                            }
                          },
                        );
                      },
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class DialogBoxForViewRequest extends StatelessWidget {
  final String? attendanceDate;
  final String? reason;
  final String? explanation;
  final int? id;
  DialogBoxForViewRequest(
      {super.key,
      required this.id,
      required this.attendanceDate,
      required this.reason,
      required this.explanation});
  final formKey = GlobalKey<FormState>();
  final TextEditingController reasonController = TextEditingController();
  final TextEditingController detailedReasonController =
      TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: w * 16),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(26),
      ),
      elevation: 0.0,
      backgroundColor: ThemeColors.colorFFFFFF,
      child: Padding(
        padding: EdgeInsets.fromLTRB(w * 20, h * 5, w * 5, h * 25),
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Attendance Request",
                    style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xff3E4259)),
                  ),
                  IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: Icon(
                      Icons.close,
                      color: ThemeColors.colorD6D6D6,
                      size: h * 20,
                    ),
                  ),
                ],
              ),
              SizedBox(height: h * 25),
              Text(
                "Reason",
                style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xff6E7079)),
              ),
              SizedBox(
                height: h * 6,
              ),
              Padding(
                padding: EdgeInsets.only(right: w * 15),
                child: HisenseTextFormField(
                  enabled: false,
                  hintText: reason,
                  controller: reasonController,
                  hintStyle: tsS12w400c475366,
                  maxLines: 1,
                  validator: Validator.text,
                ),
              ),
              SizedBox(height: h * 15),
              Text(
                "Explanation",
                style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xff6E7079)),
              ),
              SizedBox(
                height: h * 6,
              ),
              Padding(
                padding: EdgeInsets.only(right: w * 15),
                child: HisenseTextFormField(
                  enabled: false,
                  controller: detailedReasonController,
                  hintStyle: tsS12w400c475366,
                  hintText: explanation,
                  maxLines: 3,
                  validator: Validator.text,
                ),
              ),
              SizedBox(
                height: 25 * h,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Widget _cancelButton(
    {required VoidCallback onPressed,
    required double width,
    required double height,
    required String title,
    required TextStyle textStyle}) {
  return Container(
    decoration: BoxDecoration(
      color: ThemeColors.colorF3F3F9,
      borderRadius: BorderRadius.circular(50),
    ),
    child: ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.transparent,
        shadowColor: Colors.transparent,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(50),
        ),
        minimumSize: Size(width, height),
        // fixedSize: Size(size.width * 0.872, size.height * 0.0689),
      ),
      child: Text(
        title,
        style: textStyle,
      ),
    ),
  );
}
