import 'package:e8_hr_portal/model/attendence_view_log_model.dart';
import 'package:e8_hr_portal/provider/attendance_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AttendenceViewLogDialog extends StatelessWidget {
  const AttendenceViewLogDialog({super.key});

  @override
  Widget build(BuildContext context) {
    var provider = Provider.of<AttendanceProvider>(context);
    List<AttendanceLog> attendanceLog =
        provider.attendanceViewLogModel?.attendanceLog ?? [];
    return Dialog(
        insetPadding: EdgeInsets.symmetric(horizontal: w * 16),
        clipBehavior: Clip.antiAlias,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(26),
        ),
        elevation: 0.0,
        backgroundColor: ThemeColors.colorFFFFFF,
        child: Container(
          // constraints: BoxConstraints(minHeight: 150 * h),
          padding:
              EdgeInsets.only(left: 20 * w, right: 20, top: 20, bottom: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "Attendance logs",
                style: tsS16BN600,
              ),
              const SizedBox(
                height: 10,
              ),
              Table(
                children: [
                  TableRow(
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.3),
                      ),
                      children: [
                        TableCell(
                          child: Text(
                            'Punch In',
                            style: tsS14w500Black,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        TableCell(
                          child: Text(
                            'Punch Out',
                            style: tsS14w500Black,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ]),
                  const TableRow(children: [
                    SizedBox(
                      height: 10,
                    ),
                    SizedBox(
                      height: 10,
                    )
                  ]),
                  ...attendanceLog.map((e) {
                    return TableRow(children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          e.punchin ?? '',
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          e.punchout ?? '',
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ]);
                  })
                ],
              ),
            ],
          ),
        ));
  }
}



//  ListView.separated(
//               shrinkWrap: true,
//               itemBuilder: (context, index) {
//                 var data =
//                     provider.attendanceViewLogModel?.attendanceLog?[index];
//                 return Row(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   mainAxisAlignment: MainAxisAlignment.start,
//                   children: [
//                     SizedBox(
//                       width: 150 * w,
//                       child: Text(
//                         "Punch in : ${data!.punchin}",
//                       ),
//                     ),
//                     SizedBox(
//                       width: 20 * w,
//                     ),
//                     if (data.punchout != null)
//                       SizedBox(child: Text("Punch out : ${data.punchout}")),
//                   ],
//                 );
//               },
//               separatorBuilder: (context, index) {
//                 return const SizedBox(
//                   height: 10,
//                 );
//               },
//               itemCount:
//                   provider.attendanceViewLogModel!.attendanceLog!.length),