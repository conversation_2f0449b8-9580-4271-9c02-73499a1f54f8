import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/view/attendance/widgets/attendence_vie_log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/provider/attendance_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/attendance/widgets/overview_card.dart';
import 'package:e8_hr_portal/view/attendance/widgets/records_card.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:month_picker_dialog/month_picker_dialog.dart';
import 'package:provider/provider.dart';
import '../../provider/punchin_provider.dart';
import '../../util/size_config.dart';
import '../ble_attendance/widgets/punch_in_out_widget.dart';
import '../ble_attendance/work_from_home_section/my_location_screen.dart';
import '../certificate_requests/salary_certificate/widget/salary_cert_rejected_commend.dart';
import 'widgets/attendance_overview_screen.dart';

class AttendanceScreen extends StatelessWidget {
  const AttendanceScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Attendance',
      // actions: [_punchInOutButton(context: context)],
      actions: getActions(context),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: [
            const PunchInOutWidget(),
            const SizedBox(height: 15),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [overviewSection(), recordSection(context)],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  List<Widget> getActions(BuildContext context) {
    final profileProvider = context.read<ProfileProvider>();
    bool? isWorkFromHome =
        profileProvider.userDetailesModel?.profileDetails?.wfhStatus;
    return [
      if (isWorkFromHome == true)
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 10 * w),
          child: Consumer<PunchInProvider>(
            builder: (context, provider, child) {
              return TextButton(
                onPressed: () {
                  provider.getLocationRequests();
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const MyLocationScreen(),
                    ),
                  );
                },
                child: Text('My Location', style: tsS12w500FFFFF),
              );

              //  provider.attendanceList.isEmpty
              //     ? const Center(child: Text("No data Found"))
              //     : ListView.separated(
              //         key: UniqueKey(),
              //         shrinkWrap: true,
              //         itemBuilder: (context, index) {
              //           final data = provider.attendanceList[index];

              //           return InkWell(
              //             onTap: () async {
              //               debugPrint("ATTENDANCE${data.attendanceDate}");
              //               debugPrint("ATTENDANCE${data.data}");
              //               debugPrint("ATTENDANCE${data.rejectedComment}");
              //               if (data.attendanceDate != null &&
              //                   data.data == false) {
              //                 await provider.getAttendenceViewLog(
              //                     date: data.attendanceDate);

              //                 if (provider.attendanceViewLogModel
              //                             ?.attendanceLog !=
              //                         null &&
              //                     provider.attendanceViewLogModel!
              //                         .attendanceLog!.isNotEmpty &&
              //                     context.mounted) {
              //                   showDialog(
              //                     context: context,
              //                     builder: (context) {
              //                       return const AttendenceViewLogDialog();
              //                     },
              //                   );
              //                 } else {
              //                   showToastText("No records found");
              //                 }
              //               } else if (data.attendanceDate != null &&
              //                   data.data == true) {
              //                 await provider.getAttendanceDetails(
              //                   id: data.attendanceDate.toString(),
              //                 );
              //                 if (context.mounted) {
              //                   Navigator.of(context).push(
              //                     MaterialPageRoute(
              //                       builder: (context) =>
              //                           const AttendanceOverviewScreen(),
              //                     ),
              //                   );
              //                 }
              //               } else if (data.attendanceDate != null &&
              //                   data.data == false &&
              //                   data.rejectedComment != null) {
              //                 showDialog(
              //                   context: context,
              //                   builder: (context) {
              //                     return RequestRejectedCommentDialog(
              //                       comment: data.rejectedComment.toString(),
              //                       rejectedPerson: data.rejectedBy,
              //                     );
              //                   },
              //                 );
              //               }
              //             },
              //             child: RecordsCard(
              //               isDisabled: data.status?.isDisabled ?? false,
              //               date: data.date,
              //               punchinTime: data.punchin,
              //               punchoutTime: data.punchout,
              //               status: data.status?.content,
              //               colorKey: data.status?.color,
              //             ),
              //           );
              //         },
              //         separatorBuilder: (context, index) {
              //           return SizedBox(
              //             height: 10 * h,
              //           );
              //         },
              //         itemCount: provider.attendanceList.length,
              //       );
            },
          ),
        )
      // else if (LoginModel.isAdmin == true)
      //   const AddDeviceActionButton()
    ];
  }
  // Widget _punchInOutButton({required BuildContext context}) => GestureDetector(
  //       onTap: () async => _onPunchInOutPressed(context: context),
  //       child: Padding(
  //         padding: EdgeInsets.only(right: 15 * w, bottom: 5 * h),
  //         child: Column(
  //           mainAxisAlignment: MainAxisAlignment.end,
  //           children: [
  //             Container(
  //               height: 34 * h,
  //               decoration: const BoxDecoration(
  //                 shape: BoxShape.circle,
  //                 color: Color(0xFFF9637D),
  //               ),
  //               child: Image.asset(
  //                 'assets/icons/thumb_impression.png',
  //                 scale: 2,
  //                 height: 30,
  //               ),
  //             ),
  //             Text(
  //               'Punch IN/OUT',
  //               style: GoogleFonts.poppins(
  //                   color: Colors.white,
  //                   fontSize: 10 * f,
  //                   fontWeight: FontWeight.w500,
  //                   height: 0),
  //             )
  //           ],
  //         ),
  //       ),
  //     );
  // _onPunchInOutPressed({required BuildContext context}) async {
  //   EasyLoading.show();
  //   BLEAttendanceProvider provider =
  //       Provider.of<BLEAttendanceProvider>(context, listen: false);
  //   await provider.getAttendanceLogs(context: context);
  //   EasyLoading.dismiss();
  //   if (!context.mounted) return;
  //   Navigator.push(context,
  //       CupertinoPageRoute(builder: (context) => const BLEAttendanceScreen()));
  // }
}

Widget recordSection(BuildContext ctx) {
  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            "Records",
            style: tsS16w500,
          ),
          Consumer<AttendanceProvider>(
            builder: (context, provider, child) {
              String selectedMonth = formatDateFromDate(
                  dateTime: provider.selectedMonth, format: 'MMMM yyyy');
              return InkWell(
                onTap: () {
                  _selectDate(ctx, provider);
                },
                child: Container(
                  height: 26 * h,
                  decoration: BoxDecoration(
                    // border: Border.all(color: Colors.black),
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Text(
                          selectedMonth,
                          style: tsS10w400495057,
                        ),
                        const ImageIcon(
                          AssetImage("assets/icons/ocalendar2.png"),
                        )
                      ],
                    ),
                  ),
                ),
              );
            },
          )
        ],
      ),
      SizedBox(
        height: 15 * h,
      ),
      Consumer<AttendanceProvider>(
        builder: (context, provider, child) {
          return provider.attendanceList.isEmpty
              ? const Center(child: Text("No data Found"))
              : ListView.separated(
                  physics: const NeverScrollableScrollPhysics(),
                  key: UniqueKey(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    final data = provider.attendanceList[index];

                    return InkWell(
                      onTap: () async {
                        // debugPrint(
                        //     "ATTENDANCE" + data.attendanceDate.toString());
                        // debugPrint("ATTENDANCE" + data.data.toString());
                        if (data.attendanceDate != null && data.data == false) {
                          await provider.getAttendenceViewLog(
                              date: data.attendanceDate);

                          if (provider.attendanceViewLogModel?.attendanceLog !=
                                  null &&
                              provider.attendanceViewLogModel!.attendanceLog!
                                  .isNotEmpty &&
                              context.mounted) {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return const AttendenceViewLogDialog();
                              },
                            );
                          } else {
                            showToastText("No records found");
                          }
                        } else if (data.attendanceDate != null &&
                            data.data == true) {
                          await provider.getAttendanceDetails(
                            id: data.attendanceDate.toString(),
                          );
                          if (context.mounted) {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) =>
                                    const AttendanceOverviewScreen(),
                              ),
                            );
                          }
                        } else if (data.attendanceDate != null &&
                            data.data == false &&
                            data.rejectedComment != null) {
                          showDialog(
                            context: context,
                            builder: (context) {
                              return RequestRejectedCommentDialog(
                                comment: data.rejectedComment.toString(),
                                rejectedPerson: data.rejectedBy,
                              );
                            },
                          );
                        }
                      },
                      child: RecordsCard(
                        isDisabled: data.status?.isDisabled ?? false,
                        date: data.date,
                        punchinTime: data.punchin,
                        punchoutTime: data.punchout,
                        status: data.status?.content,
                        colorKey: data.status?.color,
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 10 * h,
                    );
                  },
                  itemCount: provider.attendanceList.length,
                );
        },
      )
    ],
  );
}

Widget overviewSection() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        "Attendance Overview",
        style: tsS16w500,
      ),
      SizedBox(
        height: 10 * h,
      ),
      Consumer<AttendanceProvider>(
        builder: (context, provider, child) {
          return Column(
            children: [
              Row(
                children: [
                  OverviewCard(
                    icon: 'assets/icons/oclock.png',
                    title: provider.onTimeArrival,
                    subtitle: "On time arrival",
                  ),
                  SizedBox(
                    width: 13 * w,
                  ),
                  OverviewCard(
                    icon: 'assets/icons/ocalendar.png',
                    title: provider.assignedDays,
                    subtitle: "Assigned days (${provider.workingDays ?? ""})",
                  )
                ],
              ),
            ],
          );
        },
      ),
      SizedBox(height: 21 * h),
    ],
  );
}

Future<void> _selectDate(
    BuildContext context, AttendanceProvider provider) async {
  DateTime? picked = await showMonthPicker(
    monthPickerDialogSettings: MonthPickerDialogSettings(
      dialogSettings: PickerDialogSettings(
          dismissible: true,
          customWidth: w * 600,
          customHeight: h * 235,
          dialogRoundedCornersRadius: 10),
      buttonsSettings:
          const PickerButtonsSettings(unselectedMonthsTextColor: Colors.black),
      // TODO: upgrade
      // actionBarSettings: PickerActionBarSettings(
      //   confirmWidget: Text(
      //     'OK',
      //     style: TextStyle(color: ThemeColors.primaryColor),
      //   ),
      //   cancelWidget: Text(
      //     'CANCEL',
      //     style: TextStyle(color: Colors.black),
      //   ),
      // ),
    ),
    context: context,
    initialDate: provider.selectedMonth,
    firstDate: DateTime(2018),
    lastDate: DateTime(2035),
  );

  // DateTime? picked = await showMonthYearPicker(
  //   // textDirection: TextDirection.,
  //   context: context,
  //   initialDate: provider.initialDate,
  //   firstDate: DateTime(2018),
  //   lastDate: DateTime(2035),
  // );
  if (picked != null) {
    EasyLoading.show();
    // String selectedYear = picked.year.toString();
    // String selectedMonth =
    //     await formatDateFromDate(dateTime: picked, format: "MMM");
    // provider.initialDate = picked;
    // provider.selectedYear = selectedYear;
    // provider.selectedMonth = selectedMonth;
    // provider.isDateSelected = true;
    provider.selectedMonth = picked;
    if (context.mounted) {
      await provider.getAttendanceList(master: false, context: context);
    }
    EasyLoading.dismiss();
  }
}
