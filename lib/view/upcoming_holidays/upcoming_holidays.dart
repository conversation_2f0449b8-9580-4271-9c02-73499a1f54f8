import 'package:e8_hr_portal/model/user_policies_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/model/holidays_model.dart';
import 'package:e8_hr_portal/provider/company_activities_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:provider/provider.dart';

///Users/<USER>/shithin_workspace/hisense/lib/model/holidays_model.dart
class UpcomingHilodays extends StatelessWidget {
  const UpcomingHilodays({super.key});

  @override
  Widget build(BuildContext context) {
    // CompanyActivitiesProvider provider =
    //     Provider.of<CompanyActivitiesProvider>(context, listen: false);
    return CustomScaffold(
      screenTitle: "Holidays",
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SizedBox(height: h * 33),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Holidays List",
                style: tsS18w500c181818,
              ),
              Container(
                padding: const EdgeInsets.fromLTRB(10, 5, 5, 5),
                width: 90 * w,
                height: 30 * h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Consumer<CompanyActivitiesProvider>(
                  builder: (context, provider, _) {
                    return DropdownButton<UserPoliciesModel>(
                        isExpanded: true,
                        underline: const SizedBox(),
                        value: provider.selectedPolicesType,
                        items: provider.holidaysPolicies.map((item) {
                          return DropdownMenuItem<UserPoliciesModel>(
                            value: item,
                            child: FittedBox(
                              child: Text(
                                item.name.toString(),
                              ),
                            ),
                          );
                        }).toList(),
                        onChanged: (UserPoliciesModel? value) {
                          if (value != null) {
                            EasyLoading.show();
                            debugPrint("inside value");
                            provider.selectedPolicesType = value;
                            provider.getHolidays();
                            EasyLoading.dismiss();

                            // provider.currentPageLeaveRecords = 0;
                            // provider.pagingControllerLeaveRecords?.refresh();
                            // provider.pagingControllerLeaveRecords
                            //     ?.notifyListeners();
                          }
                        });
                  },
                ),
              )
            ],
          ),
          Consumer<CompanyActivitiesProvider>(
              builder: (context, provider, child) {
            if (provider.indianHolidaysList.isEmpty) {
              return SizedBox(
                  height: 500 * h,
                  child: const Center(child: Text("Data not found")));
            }

            return Expanded(
              child: ListView.separated(
                itemCount: provider.indianHolidaysList.length,
                padding: EdgeInsets.only(top: h * 15, bottom: h * 90),
                physics: const BouncingScrollPhysics(),
                itemBuilder: (context, index) {
                  return _holidayTile(data: provider.indianHolidaysList[index]);
                },
                separatorBuilder: (context, index) => SizedBox(height: w * 10),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _holidayTile({required HolidaysModel data}) {
    String? date = data.startDate;
    int? days = data.days;
    String? formattedStartDate;
    String? formattedEndDate;
    String? startFromDate;
    String? event = data.occassion;
    bool isConfirm = data.isConfirm ?? false;
    bool dateBefor = false;

    if (date != null) {
      formattedStartDate =
          formatDateFromString(date, "yyyy-MM-ddThh:mm:ssZ", "dd MMM yy EEE");
      startFromDate =
          formatDateFromString(date, "yyyy-MM-ddThh:mm:ssZ", "dd\nMMM");
      DateTime dateTime = DateTime.parse(date.toString());

      if (days != null) {
        DateTime endDate = dateTime.add(Duration(days: days - 1));
        formattedEndDate =
            formatDateFromDate(dateTime: endDate, format: "dd MMM yy EEE");
      }
      var da = DateTime.parse(date);
      dateBefor = DateTime.now().isBefore(da);
    }
    return Container(
      padding: EdgeInsets.symmetric(vertical: 2 * h),
      width: w * 343,
      // height: 70,
      decoration: BoxDecoration(
        color: dateBefor
            ? ThemeColors.colorFFFFFF
            : ThemeColors.color9F9F9F.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  height: h * 45,
                  width: w * 45,
                  alignment: Alignment.center,
                  margin: EdgeInsets.fromLTRB(w * 10, h * 10, w * 11, h * 11),
                  decoration: BoxDecoration(
                      shape: BoxShape.circle, color: ThemeColors.colorF8F8F8),
                  child: Text(
                    startFromDate ?? "01\nJan",
                    style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: ThemeColors.colorFCC400,
                        height: 1),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // SizedBox(height: h * 10),
                      Text(
                        event ?? "New Year",
                        style: tsS14w500c161616,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: h * 4),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Image.asset(
                            "assets/icons/calendar_black.png",
                            color: ThemeColors.colorF9637D,
                            height: h * 15,
                            width: w * 15,
                          ),
                          SizedBox(width: w * 5.25),
                          Expanded(
                            child: formattedStartDate != formattedEndDate
                                ? Text(
                                    "${formattedStartDate ?? "01 Jan 23"} - ${formattedEndDate ?? "01 Sep 23"} • ${days ?? "1"} Day${(days ?? 1) > 1 ? 's' : ''}",
                                    style: tsS12w400c979797,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  )
                                : Text(
                                    "${formattedStartDate ?? "01 Jan 23"}  • ${days ?? "1"} Day${(days ?? 1) > 1 ? 's' : ''}",
                                    style: tsS12w400c979797,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                          )
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Container(
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(horizontal: w * 11, vertical: h * 4),
            margin: EdgeInsets.only(right: w * 11, top: h * 12),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: isConfirm
                    ? const Color.fromRGBO(50, 147, 111, 0.16)
                    : const Color.fromRGBO(246, 77, 68, 0.15)),
            child: Text(
              isConfirm ? "Confirmed" : "Not Confirmed",
              style: isConfirm ? tsS12w600c519C66 : tsS12w600cF64D44,
              maxLines: 1,
            ),
          )
        ],
      ),
    );
  }
}
