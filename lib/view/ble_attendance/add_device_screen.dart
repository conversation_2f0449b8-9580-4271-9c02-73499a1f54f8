// ignore_for_file: use_build_context_synchronously

import 'package:e8_hr_portal/provider/add_device_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../helper/button_widget.dart';
import '../../util/styles.dart';

class AddDeviceScreen extends StatelessWidget {
  const AddDeviceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<AddDeviceProvider>();
    return HisenseScaffold(
      screenTitle: 'Add Device',
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 24, top: 8),
          child: InkWell(
            onTap: () {
              provider.scanNearbyDevices();
            },
            splashColor: Colors.transparent,
            child: Column(
              children: [
                SvgPicture.asset(
                  'assets/icons/refresh.svg',
                  height: 30 * h,
                  width: 30 * w,
                ),
                Text(
                  'Refresh',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        )
      ],
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${provider.bleList.length} Devices Found',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                  color: ThemeColors.color181818,
                ),
              ),
              if (provider.isScanning || provider.isDeviceAddingLoading)
                const CircularProgressIndicator.adaptive()
            ],
          ),
          SizedBox(height: 15 * h),
          if (provider.bleList.isNotEmpty)
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: 10 * w,
                vertical: 10 * h,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.separated(
                padding: EdgeInsets.zero,
                physics: const PageScrollPhysics(),
                itemBuilder: (context, index) {
                  var ble = provider.bleList[index];
                  return InkWell(
                    onTap: () async {
                      bool isAdded = await provider.addBlEDevice(
                          ble.id, ble.name, context);
                      if (isAdded) {
                        await successBottonSheet(context: context);
                        Navigator.pop(context);
                      }
                    },
                    child: Row(
                      children: [
                        Container(
                          height: 34 * h,
                          width: 34 * w,
                          margin: EdgeInsets.only(right: 10 * w),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ThemeColors.colorF8F8F8,
                          ),
                          padding: EdgeInsets.symmetric(
                            horizontal: 10 * w,
                            vertical: 8 * h,
                          ),
                          child: Image.asset('assets/icons/bluetooth-icon.png'),
                        ),
                        Expanded(
                          child: Text(
                            ble.name,
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: ThemeColors.color181818,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
                separatorBuilder: (context, index) {
                  return const Divider();
                },
                itemCount: provider.bleList.length,
                shrinkWrap: true,
              ),
            ),
        ],
      ),
    );
  }
}

Future successBottonSheet({required BuildContext context}) {
  return showModalBottomSheet(
      context: context,
      isDismissible: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(12))),
      builder: (builder) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                ),
                child: Column(
                  children: [
                    Container(
                      width: 134 * w,
                      height: 5,
                      margin: EdgeInsets.only(top: 14 * h, bottom: 51 * h),
                      decoration: ShapeDecoration(
                        color: const Color(0xFFEAEBED),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(100),
                        ),
                      ),
                    ),
                    SvgPicture.asset('assets/images/success_green_check.svg'),
                    SizedBox(height: 20 * h),
                    Text(
                      'Device Added',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFF181818),
                        fontSize: 22 * f,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      'You are now added new BLE Device.',
                      textAlign: TextAlign.center,
                      style: GoogleFonts.poppins(
                        color: const Color(0xFF979797),
                        fontSize: 12 * f,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    SizedBox(height: 28 * h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.0 * w),
                      child: ButtonWidget(
                          title: 'Close',
                          textStyle: tsS16w600cFFFFFF,
                          onPressed: () {
                            Navigator.pop(context);
                          }),
                    ),
                    SizedBox(height: 52 * h),
                  ],
                ),
              ),
            ],
          ));
}
