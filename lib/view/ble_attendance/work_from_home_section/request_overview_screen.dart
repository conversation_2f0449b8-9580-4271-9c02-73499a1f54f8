import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/location_model.dart';
import 'package:e8_hr_portal/provider/punchin_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import 'my_location_screen.dart';

class RequestOverviewScreen extends StatelessWidget {
  const RequestOverviewScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        screenTitle: 'Update Location Request',
        body: Consumer<PunchInProvider>(
          builder: (context, provider, child) {
            LocationModel? locationDetails = provider.locationDetail;
            if (provider.isLocationDetailsLoading) {
              return const Center(child: CircularProgressIndicator.adaptive());
            }
            return Padding(
                padding: const EdgeInsets.fromLTRB(16, 25, 16, 0),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Request Overview', style: tsS16w500),
                            SizedBox(height: 15 * h),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 13, vertical: 15),
                              // height: 200,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  color: Colors.white),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Container(
                                        height: 45 * h,
                                        width: 45 * w,
                                        margin: EdgeInsets.only(right: 11 * w),
                                        decoration: const BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: Color(0xffF8F8F8),
                                            image: DecorationImage(
                                                image: AssetImage(
                                                    'assets/images/gps2.png'),
                                                scale: 1.3)),
                                      ),
                                      Expanded(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              locationDetails?.locationName ??
                                                  '',
                                              style: tsS12w500c181818,
                                            ),
                                            SizedBox(height: 4 * h),
                                            Row(
                                              children: [
                                                Text(
                                                  locationDetails?.latitude ??
                                                      '',
                                                  style: tsS10w400979797,
                                                ),
                                                Container(
                                                  width: 3,
                                                  height: 3,
                                                  margin: EdgeInsets.symmetric(
                                                      horizontal: 5 * w),
                                                  decoration:
                                                      const BoxDecoration(
                                                          color:
                                                              Color(0xff979797),
                                                          shape:
                                                              BoxShape.circle),
                                                ),
                                                Expanded(
                                                  child: Text(
                                                    locationDetails
                                                            ?.longitude ??
                                                        '',
                                                    style: tsS10w400979797,
                                                  ),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                      ),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          requestStatusWidget(
                                              status: locationDetails?.status ??
                                                  ''),
                                          SizedBox(height: 3 * h),
                                          Text(
                                            locationDetails?.requestedDate ??
                                                '',
                                            style: tsS10w400c4D4D4D,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 15 * h),
                                  Text('Note', style: tsS10w400949494),
                                  SizedBox(height: 3 * h),
                                  Text(
                                    locationDetails?.description ?? '',
                                    style: tsS12w400c454444,
                                  ),
                                  if (locationDetails?.reason != null) ...[
                                    // SizedBox(height: 10 * h),
                                    SizedBox(height: 5 * h),
                                    Divider(
                                      thickness: 1,
                                      color: const Color(0xffD9D9D9)
                                          .withOpacity(.6),
                                    ),
                                    SizedBox(height: 5 * h),
                                    Text('Reason', style: tsS10w400949494),
                                    SizedBox(height: 3 * h),
                                    Text(
                                      locationDetails?.reason ?? '',
                                      style: tsS12w400c454444,
                                    ),
                                  ],
                                  const SizedBox(height: 10),
                                  if (locationDetails?.status?.toLowerCase() !=
                                      'pending') ...[
                                    Divider(
                                      thickness: 1,
                                      color: const Color(0xffD9D9D9)
                                          .withOpacity(.6),
                                    ),
                                    Text(
                                      'Approved by / Rejected by',
                                      style: tsS10w400949494,
                                    ),
                                    const SizedBox(height: 13),
                                    Stack(
                                      children: [
                                        Stack(
                                          children: [
                                            CachedNetworkImage(
                                              imageUrl: locationDetails
                                                      ?.addedBy?.profilePic ??
                                                  '',
                                              imageBuilder:
                                                  (context, imageProvider) {
                                                return Container(
                                                  width: 45 * w,
                                                  height: 45 * h,
                                                  decoration: BoxDecoration(
                                                      color: ThemeColors
                                                          .primaryColor,
                                                      shape: BoxShape.circle,
                                                      image: DecorationImage(
                                                          image: NetworkImage(
                                                              locationDetails
                                                                      ?.addedBy
                                                                      ?.profilePic ??
                                                                  ''),
                                                          fit: BoxFit.cover)),
                                                );
                                              },
                                              errorWidget:
                                                  (context, url, error) {
                                                return Container(
                                                  width: 45 * w,
                                                  height: 45 * h,
                                                  decoration: BoxDecoration(
                                                    color: ThemeColors
                                                        .primaryColor,
                                                    shape: BoxShape.circle,
                                                  ),
                                                  alignment: Alignment.center,
                                                  child: Text(
                                                    locationDetails
                                                            ?.addedBy?.name
                                                            ?.substring(0, 1)
                                                            .toUpperCase() ??
                                                        '',
                                                    style: GoogleFonts.rubik(
                                                        fontSize: 22,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        color: Colors.white),
                                                  ),
                                                );
                                              },
                                            ),
                                            if (locationDetails?.status
                                                    ?.toLowerCase() ==
                                                'pending')
                                              Container(
                                                height: 45 * h,
                                                width: 45 * w,
                                                color: Colors.white
                                                    .withValues(alpha: 0.8),
                                              ),
                                          ],
                                        ),
                                        Positioned(
                                          bottom: 2 * h,
                                          right: 2 * w,
                                          child: Column(
                                            children: [
                                              if (locationDetails?.status
                                                      ?.toLowerCase() ==
                                                  'approved')
                                                ImageIcon(
                                                  const AssetImage(
                                                    'assets/icons/tick.png',
                                                  ),
                                                  size: 13,
                                                  color:
                                                      ThemeColors.color06AA37,
                                                )
                                              else if (locationDetails?.status
                                                      ?.toLowerCase() ==
                                                  'rejected')
                                                ImageIcon(
                                                  const AssetImage(
                                                    'assets/icons/close_red.png',
                                                  ),
                                                  size: 13,
                                                  color:
                                                      ThemeColors.colorB80000,
                                                ),
                                            ],
                                          ),
                                        )
                                      ],
                                    )
                                  ]
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      // if (locationDetails?.status?.toLowerCase() == 'pending')
                      //   ElevatedButton(
                      //       onPressed: () {},
                      //       style: ElevatedButton.styleFrom(
                      //         elevation: 0,
                      //         backgroundColor: const Color(0xffEF5E5E),
                      //         minimumSize: Size(double.infinity, 56 * w),
                      //         shape: RoundedRectangleBorder(
                      //           borderRadius: BorderRadius.circular(50),
                      //         ),
                      //       ),
                      //       child: Text(
                      //         'Cancel Request',
                      //         style: tsS18w600cFFFFFF,
                      //       )),
                      SizedBox(height: 38 * h),
                    ]));
          },
        ));
  }
}
