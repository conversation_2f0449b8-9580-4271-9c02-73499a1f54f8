// ignore_for_file: use_build_context_synchronously

import 'package:e8_hr_portal/model/location_model.dart';
import 'package:e8_hr_portal/provider/punchin_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import '../../../helper/button_widget.dart';
import '../../../util/styles.dart';
import '../widgets/punch_in_out_widget.dart';
import 'location_request_screen.dart';
import 'request_overview_screen.dart';

class MyLocationScreen extends StatelessWidget {
  const MyLocationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        screenTitle: 'My Location',
        body: Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
            child: Consumer<PunchInProvider>(
              builder: (context, provider, child) {
                List<LocationModel> requestList = provider.locationRequestsList
                    .where((element) =>
                        element.status?.toLowerCase() != 'approved')
                    .toList();
                List<LocationModel> approvedList = provider.locationRequestsList
                    .where((element) =>
                        element.status?.toLowerCase() == 'approved')
                    .toList();
                return Column(
                  children: [
                    Expanded(
                      child: provider.isLocationListLoading
                          ? const Center(
                              child: CircularProgressIndicator.adaptive())
                          : provider.locationRequestsList.isEmpty
                              ? const Center(
                                  child: Text('No data Found'),
                                )
                              : SingleChildScrollView(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      if (approvedList.isNotEmpty) ...[
                                        Text('Location Details',
                                            style: tsS16w500),
                                        SizedBox(height: 15 * h),
                                        ListView.separated(
                                          padding: EdgeInsets.zero,
                                          shrinkWrap: true,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemCount: approvedList.length,
                                          itemBuilder: (context, index) {
                                            LocationModel request =
                                                approvedList[index];
                                            return _locationCard(
                                                id: request.id,
                                                title: request.locationName,
                                                latitude: request.latitude,
                                                longitude: request.longitude,
                                                status: request.status,
                                                context: context,
                                                isShowStatus: true);
                                          },
                                          separatorBuilder: (context, index) {
                                            return SizedBox(height: 10 * h);
                                          },
                                        ),
                                      ],
                                      if (requestList.isNotEmpty) ...[
                                        SizedBox(height: 20 * h),
                                        Text('Request List', style: tsS16w500),
                                        SizedBox(height: 15 * h),
                                        ListView.separated(
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          shrinkWrap: true,
                                          itemCount: requestList.length,
                                          itemBuilder: (context, index) {
                                            LocationModel request =
                                                requestList[index];
                                            return _locationCard(
                                                id: request.id,
                                                title: request.locationName,
                                                latitude: request.latitude,
                                                longitude: request.longitude,
                                                status: request.status,
                                                context: context,
                                                isShowStatus: true);
                                          },
                                          separatorBuilder: (context, index) {
                                            return SizedBox(height: 10 * h);
                                          },
                                        ),
                                      ],
                                      SizedBox(height: 15 * h),
                                    ],
                                  ),
                                ),
                    ),
                    ButtonWidget(
                        title: 'Request Location Update',
                        textStyle: tsS16w600cFFFFFF,
                        onPressed: () {
                          getLocation(
                              context: context,
                              function: () async {
                                EasyLoading.show();
                                Position position =
                                    await Geolocator.getCurrentPosition(
                                        desiredAccuracy:
                                            LocationAccuracy.bestForNavigation);

                                EasyLoading.dismiss();
                                Navigator.of(context).push(MaterialPageRoute(
                                    builder: (context) => LocationRequestScreen(
                                          latitude:
                                              position.latitude.toString(),
                                          longitude:
                                              position.longitude.toString(),
                                        )));
                              });
                        }),
                    SizedBox(height: 38 * h),
                  ],
                );
              },
            )));
  }

  Widget _locationCard({
    required int? id,
    required String? title,
    required String? latitude,
    required String? longitude,
    required String? status,
    required bool isShowStatus,
    required BuildContext context,
  }) {
    return Container(
      height: 66 * h,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8), color: Colors.white),
      child: InkWell(
        onTap: () {
          if (id != null) {
            Provider.of<PunchInProvider>(context, listen: false)
                .getLocationRequestDetails(locationID: id);
            Navigator.of(context).push(MaterialPageRoute(
                builder: (context) => const RequestOverviewScreen()));
          }
        },
        child: Row(
          children: [
            Container(
              height: 45 * h,
              width: 45 * w,
              margin: EdgeInsets.only(right: 11 * w),
              decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: Color(0xffF8F8F8),
                  image: DecorationImage(
                      image: AssetImage('assets/images/gps2.png'), scale: 1.3)),
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    title ?? '',
                    style: tsS12w500c181818,
                  ),
                  SizedBox(height: 4 * h),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          latitude ?? '',
                          style: tsS10w400979797,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Container(
                        width: 3,
                        height: 3,
                        margin: EdgeInsets.symmetric(horizontal: 5 * w),
                        decoration: const BoxDecoration(
                            color: Color(0xff979797), shape: BoxShape.circle),
                      ),
                      Expanded(
                        child: Text(
                          longitude ?? '',
                          style: tsS10w400979797,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
            if (isShowStatus) requestStatusWidget(status: status ?? '')
          ],
        ),
      ),
    );
  }
}

Widget requestStatusWidget({required String status}) {
  TextStyle? style;
  Color? color;
  switch (status) {
    case 'Approved':
      {
        style = tsS12w600c519C66;
        color = ThemeColors.color32936F.withOpacity(0.16);
      }
      break;
    case 'Rejected':
      {
        style = tsS12w600cF64D44;
        color = ThemeColors.colorF64D44.withOpacity(0.15);
      }
      break;
    case 'Pending':
      {
        style = tsS12w600cE5B900;
        color = ThemeColors.colorFFF2E2;
      }
      break;
    case 'In-progress':
      {
        style = tsS12W6FE5B900;
        color = ThemeColors.colorFFF2E2;
      }
      break;
    case 'Cancelled':
      {
        style = tsS12w600cF64D44;
        color = ThemeColors.colorF64D44.withOpacity(0.15);
      }
      break;

    default:
      {
        style = tsS12w600c519C66;
        color = ThemeColors.color32936F.withOpacity(0.16);
      }
  }
  return Align(
    alignment: Alignment.topCenter,
    child: Container(
      padding: EdgeInsets.symmetric(horizontal: 11 * w, vertical: 3 * h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: color,
      ),
      child: Text(
        status,
        style: style,
      ),
    ),
  );
}
