import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/styles.dart';

class WFHMmemberCard extends StatelessWidget {
  final String? id;
  final String? name;
  final String? designation;
  final String? profilePic;

  final bool isForListing;
  const WFHMmemberCard(
      {super.key,
      required this.name,
      required this.designation,
      required this.profilePic,
      required this.id,
      this.isForListing = false});

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      elevation: 0,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(10, 8, 10, 8),
        child: Column(
          children: [
            Row(
              children: [
                const SizedBox(
                  width: 10,
                ),
                ClipRRect(
                  borderRadius: BorderRadius.circular(5.0),
                  child: <PERSON>lip<PERSON>val(
                    child: CachedNetworkImage(
                      imageUrl: profilePic ?? '',
                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) {
                        return Container(
                          height: 50,
                          width: 50,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            shape: BoxShape.circle,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            name?.substring(0, 1).toUpperCase().toUpperCase() ??
                                '',
                            style: GoogleFonts.rubik(
                              fontSize: 22,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width - 240,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              name ?? '',
                              overflow: TextOverflow.ellipsis,
                              style: tsS14w500,
                            ),
                            Align(
                              alignment: Alignment.topLeft,
                              child: FittedBox(
                                child: Text(
                                  designation ?? '',
                                  overflow: TextOverflow.ellipsis,
                                  style: tsS12grey,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                if (isForListing == false)
                  Transform.scale(
                    scale: .8,
                    child: CupertinoSwitch(
                      value: false,
                      onChanged: (value) {},
                      activeColor: Theme.of(context).primaryColor,
                    ),
                  )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
