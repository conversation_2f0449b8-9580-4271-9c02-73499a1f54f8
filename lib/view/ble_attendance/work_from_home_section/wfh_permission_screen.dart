import 'package:e8_hr_portal/model/wfh_permission_model.dart';
import 'package:e8_hr_portal/provider/wfh_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/ble_attendance/work_from_home_section/wfh_permission_card.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../helper/search_textfield.dart';
import '../../../util/styles.dart';

class WFHPermissionScreen extends StatelessWidget {
  WFHPermissionScreen({super.key});
  final TextEditingController _searchController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        screenTitle: 'Employee WFH Status',
        body: Consumer<WFHProvider>(
          builder: (context, provider, child) {
            return Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Employee List', style: tsS16w500),
                  SizedBox(height: 10 * h),
                  SearchTextFieldWidget(
                    onChanged: (value) {
                      provider.filter(value);
                    },
                    controller: _searchController,
                    prefixIcon: IconButton(
                      onPressed: () {},
                      icon: const ImageIcon(
                        AssetImage('assets/icons/search.png'),
                      ),
                    ),
                    hintText: 'Search Employee...',
                  ),
                  // const SizedBox(height: 10),
                  // if (provider.employeesListToShow.isNotEmpty)
                  //   Row(
                  //     mainAxisAlignment: MainAxisAlignment.end,
                  //     children: [
                  //       Checkbox(
                  //           value: provider.isAllWFH,
                  //           onChanged: (value) {
                  //             provider.isAllWFH = !provider.isAllWFH;
                  //             provider.setWfhPermission(
                  //                 isAllWFH: provider.isAllWFH);
                  //           }),
                  //       const Text('Select All')
                  //     ],
                  //   ),
                  const SizedBox(height: 10),
                  Expanded(
                    child: provider.isLoading
                        ? const Center(
                            child: CircularProgressIndicator.adaptive(),
                          )
                        : provider.employeesListToShow.isEmpty
                            ? const Center(
                                child: Text('No Result Found'),
                              )
                            : ListView.builder(
                                physics: const ClampingScrollPhysics(),
                                scrollDirection: Axis.vertical,
                                shrinkWrap: true,
                                padding: EdgeInsets.only(bottom: 30),
                                itemCount: provider.employeesListToShow.length,
                                itemBuilder: (context, index) {
                                  WFHPermissonModel? item =
                                      provider.employeesListToShow[index];
                                  return WFHPermissionCard(item: item);
                                },
                              ),
                  )
                ],
              ),
            );
          },
        ));
  }
}
