import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/provider/punchin_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class LocationRequestScreen extends StatelessWidget {
  final String latitude;
  final String longitude;
  LocationRequestScreen(
      {super.key, required this.latitude, required this.longitude});
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _noteController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        avoidBottom: true,
        screenTitle: 'Update Location Request',
        body: Padding(
            padding: const EdgeInsets.fromLTRB(16, 25, 16, 0),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Expanded(
                  child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Location Details', style: tsS16w500),
                      SizedBox(height: 15 * h),
                      Row(
                        children: [
                          _coordinatesCard(title: 'Latitude', value: latitude),
                          SizedBox(width: 15 * w),
                          _coordinatesCard(
                              title: 'Longitude', value: longitude),
                        ],
                      ),
                      SizedBox(height: 15 * w),
                      _textFormField(
                          controller: _titleController,
                          // hintText: 'Home',
                          title: 'Location title',
                          validator: Validator.text),
                      SizedBox(height: 15 * w),
                      _textFormField(
                          controller: _noteController,
                          title: 'Note',
                          maxLines: 6,
                          validator: Validator.text),
                    ],
                  ),
                ),
              )),
              Consumer<PunchInProvider>(
                builder: (context, provider, child) {
                  if (provider.isLocationRequestLoading) {
                    return const Center(
                      child: CircularProgressIndicator.adaptive(),
                    );
                  }
                  return ButtonWidget(
                      title: 'Request Location Update',
                      textStyle: tsS16w600cFFFFFF,
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          provider.sentLocationChangeRequest(
                              title: _titleController.text,
                              note: _noteController.text,
                              latitude: latitude,
                              longitude: longitude,
                              context: context);
                        }
                      });
                },
              ),
              SizedBox(height: 38 * h),
            ])));
  }

  Expanded _coordinatesCard({required String title, required String value}) {
    return Expanded(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: tsS12w400c30292F),
        SizedBox(height: 4 * h),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.only(left: 12),
          height: 42 * h,
          decoration: BoxDecoration(
              color: const Color(0xffE3E1E1),
              borderRadius: BorderRadius.circular(1)),
          alignment: Alignment.centerLeft,
          child: Text(
            value,
            style: tsS12w400c454444,
          ),
        )
      ],
    ));
  }

  Widget _textFormField({
    required TextEditingController controller,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    bool? enabled,
    String? hintText,
    String? errorText,
    String? title,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) Text(title, style: tsS12w400c30292F),
        SizedBox(height: 4 * h),
        TextFieldWidget(
          enabled: enabled ?? true,
          controller: controller,
          textStyle: tsS14w400c30292F,
          hintStyle: GoogleFonts.poppins(
              fontSize: 12 * f,
              fontWeight: FontWeight.w400,
              color: const Color(0xff454444)),
          hintText: hintText,
          error: errorText,
          errorStyle: tsS11w400cerrorColor,
          keyboardType: keyboardType,
          borderColor: ThemeColors.colorE3E3E3,

          contentPadding:
              EdgeInsets.symmetric(vertical: h * 14, horizontal: w * 11),
          border: OutlineInputBorder(
            borderSide: BorderSide(
              width: 1,
              color: ThemeColors.colorE3E3E3,
            ),
            borderRadius: BorderRadius.circular(5),
          ),
          // error: provider.loginErrorEmail,
          validator: validator,
          maxLines: maxLines,
        ),
      ],
    );
  }
}
