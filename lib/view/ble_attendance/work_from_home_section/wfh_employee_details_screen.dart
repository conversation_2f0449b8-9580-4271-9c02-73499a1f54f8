// ignore_for_file: must_be_immutable, use_build_context_synchronously
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/wfh_member_model.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../util/colors.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../employee_relations/widget/photo_view.dart';

class WFHEmployeeDetailsScreen extends StatelessWidget {
  final WFHMemberModel wfhMemberModel;
  const WFHEmployeeDetailsScreen({super.key, required this.wfhMemberModel});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        screenTitle: 'Overview',
        body: ListView(
          physics: const ClampingScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          children: [
            // Text(
            //   "Attendance Details",
            //   style: tsS16w500,
            // ),
            SizedBox(
              height: h * 15,
            ),
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(12)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  HeaderRowWidget(wfhMemberModel: wfhMemberModel),
                  SizedBox(height: h * 24),
                  if (wfhMemberModel.attLog != null &&
                      wfhMemberModel.attLog!.isNotEmpty &&
                      wfhMemberModel.attLog?.first.date != null)
                    rowWidget(
                        title: 'Date',
                        content: formatDateFromString(
                            wfhMemberModel.attLog?.first.date ?? '',
                            'yyyy-MM-dd',
                            'MMM dd, yyyy')),
                  Text(
                    'Location',
                    style: tsS12w400949494,
                  ),
                  const SizedBox(
                    height: 3,
                  ),
                  InkWell(
                      onTap: () {
                        navigateTo(11.251975427771779, 75.82824757179577);
                      },
                      child: const Icon(Icons.location_on_outlined, size: 30)),
                  const SizedBox(height: 5),
                  Divider(
                    thickness: 1,
                    color: ThemeColors.colorD9D9D9,
                  ),
                  Text(
                    'Attendance Logs',
                    style: tsS12w400949494,
                  ),
                  const SizedBox(height: 10),
                  if (wfhMemberModel.attLog != null &&
                      wfhMemberModel.attLog!.isNotEmpty)
                    _tableSection(context),
                ],
              ),
            ),
            const SizedBox(height: 30),
          ],
        ));
  }

  Table _tableSection(BuildContext context) {
    return Table(
      children: [
        TableRow(
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.3),
            ),
            children: [
              TableCell(
                child: Text(
                  'Time',
                  style: tsS14w500Black,
                  textAlign: TextAlign.center,
                ),
              ),
              TableCell(
                child: Text(
                  'Status',
                  style: tsS14w500Black,
                  textAlign: TextAlign.center,
                ),
              ),
              TableCell(
                child: Text(
                  'Image',
                  style: tsS14w500Black,
                  textAlign: TextAlign.center,
                ),
              ),
            ]),
        const TableRow(children: [
          SizedBox(
            height: 10,
          ),
          SizedBox(
            height: 10,
          ),
          SizedBox(
            height: 10,
          )
        ]),
        ...wfhMemberModel.attLog!.map((e) {
          return TableRow(children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                e.time ?? '',
                textAlign: TextAlign.center,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                e.status?.capitalize() ?? '',
                textAlign: TextAlign.center,
              ),
            ),
            Padding(
                padding: const EdgeInsets.all(8.0),
                child: InkWell(
                    onTap: () {
                      PageNavigator.push(
                        context: context,
                        route: PhotoViewScreen(
                          image: e.userImg,
                          extension: 'jpeg',
                        ),
                      );
                    },
                    child: const Icon(Icons.image))),
          ]);
        })
      ],
    );
  }
}

void navigateTo(double lat, double lng) async {
  var uri =
      Uri.parse('https://www.google.com/maps/search/?api=1&query=$lat,$lng');
  if (await canLaunchUrl(uri)) {
    await launchUrl(uri);
  } else {
    throw 'Could not launch ${uri.toString()}';
  }
}

class HeaderRowWidget extends StatelessWidget {
  final WFHMemberModel wfhMemberModel;
  const HeaderRowWidget({
    super.key,
    required this.wfhMemberModel,
  });

  @override
  Widget build(BuildContext context) {
    // Color backgroundcolor = const Color(0xff4CD964).withOpacity(.15);
    // Color textColor = const Color(0xff4CD964);
    // String status =
    //     wfhMemberModel.attLog != null && wfhMemberModel.attLog!.length.isOdd
    //         ? 'Punch-in'
    //         : 'Punch-out';
    // switch (status) {
    //   case 'Punch-in':
    //     backgroundcolor = const Color(0xff4CD964).withOpacity(.15);
    //     textColor = const Color(0xff4CD964);

    //     break;
    //   case 'Punch-out':
    //     backgroundcolor = const Color(0xffF64D44).withOpacity(.15);
    //     textColor = const Color(0xffF64D44);
    //     break;
    // }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CachedNetworkImage(
          imageUrl: wfhMemberModel.profilePic ?? '',
          imageBuilder: (context, imageProvider) {
            return Container(
              alignment: Alignment.center,
              height: 45 * h,
              width: 45 * w,
              margin: const EdgeInsets.only(right: 13),
              decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: ThemeColors.colorF8F8F8,
                  image: DecorationImage(
                      image: NetworkImage(wfhMemberModel.profilePic ?? ''),
                      fit: BoxFit.cover)),
            );
          },
          errorWidget: (context, url, error) {
            return Container(
                alignment: Alignment.center,
                height: 45 * h,
                width: 45 * w,
                margin: const EdgeInsets.only(right: 13),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Theme.of(context).primaryColor,
                ),
                child: Text(
                  wfhMemberModel.fullName
                          ?.substring(0, 1)
                          .toUpperCase()
                          .toUpperCase() ??
                      '',
                  style: GoogleFonts.rubik(
                      fontSize: 22,
                      fontWeight: FontWeight.w500,
                      color: Colors.white),
                  textAlign: TextAlign.center,
                ));
          },
        ),
        Expanded(
            child: Text(
          wfhMemberModel.fullName ?? '',
          style: tsS12w500Black,
        )),
        // Container(
        //   decoration: BoxDecoration(
        //       color: backgroundcolor, borderRadius: BorderRadius.circular(8)),
        //   child: Padding(
        //     padding:
        //         const EdgeInsets.only(left: 10, right: 10, top: 4, bottom: 4),
        //     child: Text(
        //       status,
        //       style: GoogleFonts.poppins(
        //           fontSize: 12, fontWeight: FontWeight.w600, color: textColor),
        //     ),
        //   ),
        // ),
      ],
    );
  }
}

Widget rowWidget(
    {required String title,
    required String? content,
    bool showDivider = true}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        title,
        style: tsS12w400949494,
      ),
      const SizedBox(height: 3),
      Text(
        content ?? '',
        style: tsS14w500Black,
      ),
      const SizedBox(height: 5),
      if (showDivider)
        Divider(
          thickness: 1,
          color: ThemeColors.colorD9D9D9,
        ),
    ],
  );
}

extension StringExtensions on String {
  String capitalize() {
    return '${this[0].toUpperCase()}${substring(1)}';
  }
}
