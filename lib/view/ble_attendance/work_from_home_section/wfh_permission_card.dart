import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/wfh_permission_model.dart';
import 'package:e8_hr_portal/provider/wfh_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:provider/provider.dart';

import '../../widgets/Wfh_reject_reason_dialog.dart';
import '../../widgets/wfh_accept_reason_dialog.dart';

class WFHPermissionCard extends StatelessWidget {
  final WFHPermissonModel item;

  const WFHPermissionCard({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    int? id = item.userId;
    String name = item.fullName ?? '';
    String designation = item.designation ?? '';
    String profilePic = item.profilePic ?? '';

    return InkWell(
      onTap: () {
        log('message -> $id');
      },
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        elevation: 0,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(10, 8, 10, 8),
          child: Column(
            children: [
              Row(
                children: [
                  const SizedBox(
                    width: 10,
                  ),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(5.0),
                    child: ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: profilePic,
                        width: 50,
                        height: 50,
                        fit: BoxFit.cover,
                        errorWidget: (context, url, error) {
                          return Container(
                            height: 50,
                            width: 50,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              shape: BoxShape.circle,
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              name.substring(0, 1).toUpperCase().toUpperCase(),
                              style: GoogleFonts.rubik(
                                fontSize: 22,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          width: MediaQuery.of(context).size.width - 240,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                name,
                                overflow: TextOverflow.ellipsis,
                                style: tsS14w500,
                              ),
                              Align(
                                alignment: Alignment.topLeft,
                                child: FittedBox(
                                  child: Text(
                                    designation,
                                    overflow: TextOverflow.ellipsis,
                                    style: tsS12grey,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Consumer<WFHProvider>(
                    builder: (context, provider, _) {
                      bool regularLeaveRequests =
                          item.regularLeaveRequests ?? false;
                      return Transform.scale(
                        scale: .8,
                        child: CupertinoSwitch(
                          value: regularLeaveRequests,
                          onChanged: (value) {
                            if (id != null) {
                              provider.changePermissionStatically(item: item);
                              if (regularLeaveRequests) {
                                provider.setWfhPermission(userID: id);
                              }
                            }
                          },
                          activeColor: Theme.of(context).primaryColor,
                        ),
                      );
                    },
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  _onWfhAccepted(
      {required int requestId, required BuildContext context}) async {
    showDialog(
      context: context,
      builder: (context) {
        return WfhAcceptReasonDialog(leaveId: requestId);
      },
    );
  }

  _onWfhRejected(
      {required int requestId, required BuildContext context}) async {
    showDialog(
      context: context,
      builder: (context) {
        return WfhRejectReasonDialog(leaveId: requestId);
      },
    );
  }
}
