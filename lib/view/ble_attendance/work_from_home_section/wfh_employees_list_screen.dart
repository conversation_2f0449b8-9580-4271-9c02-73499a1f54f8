import 'package:e8_hr_portal/model/wfh_member_model.dart';
import 'package:e8_hr_portal/provider/wfh_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/view/ble_attendance/work_from_home_section/wfh_employee_details_screen.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import 'wfh_member_card.dart';

class WFHEmployeesListScreen extends StatelessWidget {
  const WFHEmployeesListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        screenTitle: 'WFH Employees',
        body: Padding(
          padding: const EdgeInsets.fromLTRB(16, 10, 16, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('WFH Employees', style: tsS16w500),
                  Consumer<WFHProvider>(
                    builder: (context, provider, child) {
                      return InkWell(
                        onTap: () async {
                          DateTime? pickedDate = await showDatePicker(
                              context: context,
                              initialDate: provider.selectedDate,
                              firstDate: DateTime(2023),
                              lastDate: DateTime.now());
                          if (pickedDate != null) {
                            provider.selectedDate = pickedDate;
                          }
                        },
                        child: Container(
                          height: 26 * h,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Text(
                                  formatDateFromDate(
                                      dateTime: provider.selectedDate,
                                      format: 'dd MMM'),
                                  style: tsS10w400495057,
                                ),
                                SizedBox(width: 4 * w),
                                const ImageIcon(
                                  AssetImage("assets/icons/ocalendar2.png"),
                                )
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  )
                ],
              ),
              const SizedBox(height: 15),
              Consumer<WFHProvider>(
                builder: (context, provider, child) {
                  return Expanded(
                    child: provider.isLoading
                        ? const Center(
                            child: CircularProgressIndicator.adaptive(),
                          )
                        : provider.wfhEmployeesList.isEmpty
                            ? const Center(
                                child: Text('No Result Found'),
                              )
                            : ListView.builder(
                                physics: const ClampingScrollPhysics(),
                                scrollDirection: Axis.vertical,
                                shrinkWrap: true,
                                itemCount: provider.wfhEmployeesList.length,
                                itemBuilder: (context, index) {
                                  WFHMemberModel? member =
                                      provider.wfhEmployeesList[index];
                                  return InkWell(
                                    onTap: () {
                                      Navigator.of(context).push(
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  WFHEmployeeDetailsScreen(
                                                      wfhMemberModel: member)));
                                    },
                                    child: WFHMmemberCard(
                                      id: member.employeeId,
                                      name: member.fullName,
                                      designation: member.designationTitle,
                                      profilePic: member.profilePic,
                                      isForListing: true,
                                    ),
                                  );
                                },
                              ),
                  );
                },
              ),
            ],
          ),
        ));
  }
}
