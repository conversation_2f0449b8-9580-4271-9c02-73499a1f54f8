// ignore_for_file: use_build_context_synchronously

import 'dart:io';
import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/provider/camera_provider.dart';
import 'package:e8_hr_portal/provider/checklist_provider.dart';
import 'package:e8_hr_portal/provider/punchin_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/ble_attendance/widgets/jumping_dot.dart';
import 'package:e8_hr_portal/view/checklist/checklist_signout_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_svg/svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../camera_screen/camera_screen.dart';
import 'location_request_screen.dart';
import 'photo_confirmation_screen.dart';

class MatchingLocationBottomSheet extends StatelessWidget {
  final String action;
  const MatchingLocationBottomSheet({super.key, required this.action});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: double.infinity,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(12),
            ),
          ),
          child: Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 50),
              child: Consumer<PunchInProvider>(
                builder: (context, provider, child) {
                  return Column(
                    children: [
                      Container(
                        width: 134 * w,
                        height: 5,
                        margin: EdgeInsets.only(top: 14 * h, bottom: 4),
                        decoration: ShapeDecoration(
                          color: const Color(0xFFEAEBED),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(100),
                          ),
                        ),
                      ),
                      if (provider.locationStatus == LocationStatus.loading)
                        _matchingLocationWidget()
                      else if (provider.locationStatus ==
                          LocationStatus.success)
                        _locationMatchedWidget(context: context)
                      else if (provider.locationStatus == LocationStatus.failed)
                        _locationNotMatchedWidget(context: context)
                    ],
                  );
                },
              )),
        ),
      ],
    );
  }

  _matchingLocationWidget() {
    return Column(
      children: [
        Image.asset(
          'assets/images/location.png',
          height: 114 * h,
          width: 164 * w,
        ),
        SizedBox(height: 8 * h),
        Text(
          'Matching Location',
          style: GoogleFonts.poppins(
            color: const Color(0xFF181818),
            fontSize: 22 * f,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
              text:
                  'Be patient ! We’re matching your current location\nwith your provided location. Be inside your\nlocations',
              style: GoogleFonts.poppins(
                color: const Color(0xFF979797),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
              children: [
                TextSpan(
                  text: ' 100m ',
                  style: GoogleFonts.poppins(
                    color: Colors.black,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextSpan(
                  text: 'radius.',
                  style: GoogleFonts.poppins(
                    color: const Color(0xFF979797),
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                )
              ]),
        ),
        const SizedBox(height: 50),
        const AnimatedDottedProgressIndicator(),
      ],
    );
  }

  Widget _locationMatchedWidget({required BuildContext context}) {
    final cameraProvider = context.read<CameraProvider>();
    return Column(
      children: [
        SizedBox(height: 12 * h),
        SvgPicture.asset(
          'assets/images/success_green_check.svg',
          height: 90 * h,
          width: 90 * w,
          fit: BoxFit.cover,
        ),
        SizedBox(height: 20 * h),
        Text(
          'Location Matched',
          style: GoogleFonts.poppins(
            color: const Color(0xFF181818),
            fontSize: 22 * f,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 2 * h),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: Text(
            'Please open your camera and take a photo and upload it to complete punch in / punch out',
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              color: const Color(0xFF979797),
              fontSize: 12 * f,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        SizedBox(height: 30 * h),
        Consumer<PunchInProvider>(
          builder: (context, provider, _) {
            return ButtonWidget(
                title: 'Take a photo',
                textStyle: tsS16w600cFFFFFF,
                onPressed: () async {
                  // provider.pickImage(context: context);
                  EasyLoading.show();

                  await EasyLoading.dismiss();
                  cameraProvider.image = null;
                  await Navigator.of(context).push(
                      MaterialPageRoute(builder: (context) => CameraScreen()));

                  File? image = cameraProvider.image;
                  if (image != null) {
                    Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) =>
                            PhotoConfirmationScreen(image, action)));
                  }
                });
          },
        )
      ],
    );
  }

  Widget _locationNotMatchedWidget({required BuildContext context}) {
    return Column(
      children: [
        Image.asset(
          'assets/images/location_mismatch.png',
          height: 106 * h,
          width: 164 * w,
        ),
        SizedBox(height: 8 * h),
        Consumer<PunchInProvider>(
          builder: (context, provider, child) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: Text(
                provider.errorMessage ?? "Oh No ! Your location\ndoesn't match",
                textAlign: TextAlign.center,
                style: GoogleFonts.poppins(
                  color: const Color(0xFF181818),
                  fontSize: 22 * f,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          },
        ),
        const SizedBox(height: 2),
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
              text: 'Please be within',
              style: GoogleFonts.poppins(
                color: const Color(0xFF979797),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
              children: [
                TextSpan(
                  text: ' 100m ',
                  style: GoogleFonts.poppins(
                    color: Colors.black,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextSpan(
                  text: 'radius of your\n provided location. then try again',
                  style: GoogleFonts.poppins(
                    color: const Color(0xFF979797),
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                )
              ]),
        ),
        const SizedBox(height: 24),
        Consumer<PunchInProvider>(
          builder: (context, punchInProvider, child) {
            return ButtonWidget(
                title: 'Try Again',
                textStyle: tsS16w600cFFFFFF,
                onPressed: () async {
                  punchInProvider.locationStatus = LocationStatus.loading;

                  Position position = await Geolocator.getCurrentPosition(
                      desiredAccuracy: LocationAccuracy.bestForNavigation);
                  punchInProvider.checkLocationMatching(
                      latitude: position.latitude,
                      longitude: position.longitude);
                });
          },
        ),
        const SizedBox(height: 16),
        InkWell(
          onTap: () async {
            EasyLoading.show();
            Position position = await Geolocator.getCurrentPosition(
                desiredAccuracy: LocationAccuracy.bestForNavigation);
            EasyLoading.dismiss();
            Navigator.pop(context);
            Navigator.of(context).push(MaterialPageRoute(
                builder: (context) => LocationRequestScreen(
                      latitude: position.latitude.toString(),
                      longitude: position.longitude.toString(),
                    )));
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/images/gps.png',
                height: 24 * h,
                width: 24 * w,
                fit: BoxFit.cover,
              ),
              SizedBox(width: 7 * w),
              Text(
                'Update Location',
                style: tsS16w500cCFCFCF,
              )
            ],
          ),
        )
      ],
    );
  }
}
