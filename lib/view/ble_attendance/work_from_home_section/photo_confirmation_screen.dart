import 'dart:developer';
import 'dart:io';
import 'package:e8_hr_portal/main.dart';
import 'package:e8_hr_portal/provider/attendance_provider.dart';
import 'package:e8_hr_portal/provider/ble_attendance_provider.dart';
import 'package:e8_hr_portal/provider/punchin_provider.dart';
import 'package:e8_hr_portal/view/ble_attendance/widgets/scanning_ble_bottomsheet_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class PhotoConfirmationScreen extends StatelessWidget {
  final File image;
  final String action;
  const PhotoConfirmationScreen(this.image, this.action, {super.key});
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Center(
              child: Image.file(
                image,
                width: size.width,
                // filterQuality: FilterQuality.high,
                fit: BoxFit.cover,
              ),
            ),
            // const Spacer(),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(bottom: 30.0),
        child: Row(
          children: [
            Expanded(
              child: TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                style: TextButton.styleFrom(
                  foregroundColor: Colors.white,
                ),
                child: const Text('Cancel'),
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Consumer3<PunchInProvider, BLEAttendanceProvider,
                  AttendanceProvider>(
                builder: (context, provider, bleAttendanceProvider,
                    attendanceProvider, _) {
                  return
                      //  provider.punchinLoading
                      //     ? const Center(
                      //         child: CircularProgressIndicator.adaptive(
                      //             backgroundColor: Colors.white),
                      //       )
                      //     :
                      TextButton(
                    onPressed: () async {
                      bool isSuccess = await provider.wfhPunchinPunchout(
                          image: image, context: context, action: action);

                      log('isSuccess -$isSuccess');
                      if (isSuccess) {
                        // if (!context.mounted) return;

                        bleAttendanceProvider.getAttendanceLogs();
                        attendanceProvider.getAttendanceList(
                            master: false,
                            context:
                                NavigationService.navigatorKey.currentContext ??
                                    context);
                        Navigator.pop(
                            NavigationService.navigatorKey.currentContext ??
                                context);
                        Navigator.pop(
                            NavigationService.navigatorKey.currentContext ??
                                context);
                        if (action == 'punch-in') {
                          MorningHuddleDialog.showMorningHuddleDialog(
                              NavigationService.navigatorKey.currentContext ??
                                  context);
                        }
                      }
                    },
                    style: TextButton.styleFrom(foregroundColor: Colors.white),
                    child: const Text('Done'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
