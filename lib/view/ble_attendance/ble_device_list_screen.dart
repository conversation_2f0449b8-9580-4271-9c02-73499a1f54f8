import 'package:e8_hr_portal/model/ble_device_model.dart';
import 'package:e8_hr_portal/provider/add_device_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/view/ble_attendance/add_device_screen.dart';
import 'package:e8_hr_portal/view/widgets/general_icon_button.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../provider/ble_attendance_provider.dart';
import '../../util/size_config.dart';

class BLEDeviceListScreen extends StatelessWidget {
  const BLEDeviceListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Devices',
      body: Consumer<BLEAttendanceProvider>(
        builder: (context, provider, child) {
          if (provider.isDeviceListLoading) {
            return const Center(
              child: CircularProgressIndicator.adaptive(),
            );
          } else if (provider.bleDeviceList.isEmpty) {
            return const Center(
              child: Text('No device found'),
            );
          }

          return ListView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            children: [
              Text(
                'Added Devices',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                  color: ThemeColors.color181818,
                ),
              ),
              SizedBox(height: 15 * h),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 10 * w,
                  vertical: 10 * h,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.separated(
                  padding: EdgeInsets.zero,
                  physics: const PageScrollPhysics(),
                  itemBuilder: (context, index) {
                    BLEDeviceModel? device = provider.bleDeviceList[index];
                    return Row(
                      children: [
                        Container(
                          height: 34 * h,
                          width: 34 * w,
                          margin: EdgeInsets.only(right: 10 * w),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ThemeColors.colorF8F8F8,
                          ),
                          padding: EdgeInsets.symmetric(
                            horizontal: 10 * w,
                            vertical: 8 * h,
                          ),
                          child: Image.asset('assets/icons/bluetooth-icon.png'),
                        ),
                        Expanded(
                          child: Text(
                            device.name ?? device.rsId ?? '',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: ThemeColors.color181818,
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  separatorBuilder: (context, index) {
                    return const Divider();
                  },
                  itemCount: provider.bleDeviceList.length,
                  shrinkWrap: true,
                ),
              ),
            ],
          );
        },
      ),
      bottomNavigationBar: BottomAppBar(
        color: Colors.transparent,
        elevation: 0,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 13),
          child: GeneralIconButton(
            height: 50,
            width: double.infinity,
            onPressed: () async {
              final provider = context.read<AddDeviceProvider>();
              provider.scanNearbyDevices();
              await Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const AddDeviceScreen()),
              );
              provider.stopScanning();
            },
            textStyle: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            foregroundColor: Colors.white,
            icon: SvgPicture.asset('assets/icons/add-new-device.svg'),
            label: const Text('Add New Device'),
          ),
        ),
      ),
    );
  }
}
