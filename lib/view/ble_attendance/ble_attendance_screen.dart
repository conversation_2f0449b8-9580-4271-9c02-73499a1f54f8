import 'package:e8_hr_portal/provider/punchin_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/ble_attendance/widgets/punch_in_out_widget.dart';
import 'package:e8_hr_portal/view/ble_attendance/widgets/recent_logs.dart';
import 'package:e8_hr_portal/view/ble_attendance/widgets/recent_logs_filter.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../provider/profile_provider.dart';
import 'work_from_home_section/my_location_screen.dart';

class BLEAttendanceScreen extends StatelessWidget {
  static const route = 'ble_attendance_screen';
  const BLEAttendanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Attendance',
      actions: getActions(context),
      body: const Padding(
        padding: EdgeInsets.fromLTRB(16, 25, 16, 0),
        child: Column(
          children: [
            PunchInOutWidget(),
            SizedBox(height: 30),
            RecentLogsFilter(),
            SizedBox(height: 15),
            RecentLogs(),
          ],
        ),
      ),
    );
  }

  List<Widget> getActions(BuildContext context) {
    final profileProvider = context.read<ProfileProvider>();
    bool? isWorkFromHome =
        profileProvider.userDetailesModel?.profileDetails?.wfhStatus;
    return [
      if (isWorkFromHome == true)
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 10 * w),
          child: Consumer<PunchInProvider>(
            builder: (context, provider, child) {
              return TextButton(
                onPressed: () {
                  provider.getLocationRequests();
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const MyLocationScreen(),
                    ),
                  );
                },
                child: Text('My Location', style: tsS12w500FFFFF),
              );
            },
          ),
        )
      // else if (LoginModel.isAdmin == true)
      //   const AddDeviceActionButton()
    ];
  }
}
