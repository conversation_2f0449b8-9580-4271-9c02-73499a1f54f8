import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/provider/ble_attendance_provider.dart';
import 'package:e8_hr_portal/provider/checklist_provider.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/provider/punchin_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/ble_attendance/widgets/scanning_ble_bottomsheet_widget.dart';
import 'package:e8_hr_portal/view/checklist/checklist_signout_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../work_from_home_section/matching_location_botton_sheet.dart';

class PunchInOutWidget extends StatelessWidget {
  const PunchInOutWidget({super.key});

  final _kHeight10 = const SizedBox(height: 10);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(15, 12, 15, 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _dateWidget(),
          _kHeight10,
          _divider(),
          _kHeight10,
          _punchInOutWidget(context),
        ],
      ),
    );
  }

  Widget _dateWidget() {
    String currentDate =
        formatDateFromDate(dateTime: DateTime.now(), format: 'dd MMM yyyy');
    return Row(
      children: [
        Image.asset(
          "assets/icons/calendar_black.png",
          color: ThemeColors.primaryColor,
          height: 22 * h,
          width: 22 * w,
        ),
        SizedBox(width: 8 * w),
        Text(
          currentDate,
          style: GoogleFonts.poppins(
            fontSize: 13,
            fontWeight: FontWeight.w500,
            color: ThemeColors.color2C2D33,
          ),
        )
      ],
    );
  }

  Widget _divider() {
    return Divider(
      color: ThemeColors.colorF9F9F9,
      height: 0,
      thickness: 1,
    );
  }

  Widget _punchInWidget(BuildContext context) {
    return Column(
      children: [
        Text(
          'Punch In',
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: ThemeColors.color505050,
          ),
        ),
        Consumer<BLEAttendanceProvider>(
          builder: (context, provider, _) {
            String date = '';
            bool isPuchIn = provider.punchinPunchoutLocalList.length % 2 == 1;
            if (provider.punchinPunchoutLocalList.isNotEmpty) {
              date = formatDateFromString(
                  provider.punchinPunchoutLocalList.first,
                  'hh:mm:ss',
                  'hh:mma');
            }
            if (isPuchIn) {
              return Text(
                date,
                textAlign: TextAlign.center,
                style: GoogleFonts.poppins(
                  color: const Color(0xFF0BA626),
                  fontSize: 22 * f,
                  fontWeight: FontWeight.w500,
                ),
              );
            }
            return ElevatedButton(
              onPressed: () async {
                BLEAttendanceProvider provider =
                    Provider.of<BLEAttendanceProvider>(context, listen: false);
                ProfileProvider profileProvider =
                    Provider.of<ProfileProvider>(context, listen: false);

                if (provider.attendanceStatus != AttendanceStatus.deviceFound) {
                  // provider.attendanceStatus = AttendanceStatus.none;
                }

                //WFH
                if (profileProvider
                        .userDetailesModel?.profileDetails?.wfhStatus ==
                    true) {
                  _onWFHEmployeePressed(context, "punch-in");
                } else {
                  showModalBottomSheet(
                    context: context,
                    isDismissible: true,
                    shape: const RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(12))),
                    builder: (builder) => const ScanningBleBottomSheetWidget(
                      action: "punch-in",
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: ThemeColors.color0CA726,
                disabledBackgroundColor:
                    ThemeColors.color0CA726.withOpacity(0.3),
                minimumSize: const Size(100, 30),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                // padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 7),
                textStyle: GoogleFonts.poppins(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
                foregroundColor: Colors.white,
              ),
              child: const Text('Punch In'),
            );
          },
        ),
      ],
    );
  }

  Widget _punchOutWidget() {
    debugPrint("policeeeee  ---- ${LoginModel.policyId}");
    return Column(
      children: [
        Text(
          'Punch Out',
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: ThemeColors.color505050,
          ),
        ),
        Consumer2<BLEAttendanceProvider, ProfileProvider>(
          builder: (context, provider, profileProvider, _) {
            String date = '';
            if (provider.punchinPunchoutLocalList.isNotEmpty) {
              date = formatDateFromString(
                provider.punchinPunchoutLocalList.last,
                'hh:mm:ss',
                'hh:mma',
              );
            }
            bool isPunchOut = provider.punchinPunchoutLocalList.length % 2 != 1;
            bool isDisable = provider.punchinPunchoutLocalList.isEmpty;
            if (isPunchOut && !isDisable) {
              return Text(
                date,
                style: GoogleFonts.poppins(
                  color: const Color(0xFFC51414),
                  fontSize: 22 * f,
                  fontWeight: FontWeight.w500,
                  height: 0,
                ),
              );
            }
            if (LoginModel.policyId == 2 &&
                isDisable &&
                profileProvider.userDetailesModel?.profileDetails?.wfhStatus ==
                    false) {
              return ElevatedButton(
                onPressed: () async {
                  var data = await showDialog(
                      context: context,
                      builder: (ctxt) {
                        return AlertDialog(
                          title: const Text('Punch-Out'),
                          content:
                              const Text('Do you really want to Punch-Out?'),
                          actions: [
                            ElevatedButton(
                              onPressed: () => Navigator.pop(context, false),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey[300],
                              ),
                              child: const Text(
                                'Cancel',
                                style: TextStyle(color: Colors.black),
                              ),
                            ),
                            ElevatedButton(
                              onPressed: () async {
                                Navigator.pop(context, true);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: ThemeColors.secondaryColor,
                              ),
                              child: const Text('Yes'),
                            )
                          ],
                        );
                      });

                  if (data) {
                    var checkListProvider =
                        Provider.of<ChecklistProvider>(context, listen: false);
                    await checkListProvider.getShedulers();
                    await checkListProvider.getOnSignOutChecklist();

                    if (checkListProvider.onSiginOutCheckListModel.isNotEmpty) {
                      bool isDone = await Navigator.push<bool>(
                            context,
                            MaterialPageRoute(
                                builder: (context) => ChecklistSignOutScreen()),
                          ) ??
                          false;

                      if (isDone) {
                        ProfileProvider profileProvider =
                            Provider.of<ProfileProvider>(context,
                                listen: false);

                        if (profileProvider
                                .userDetailesModel?.profileDetails?.wfhStatus ==
                            true) {
                          _onWFHEmployeePressed(context, "punch-out");
                        } else {
                          showModalBottomSheet(
                            context: context,
                            isDismissible: true,
                            shape: const RoundedRectangleBorder(
                                borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(12))),
                            builder: (builder) =>
                                const ScanningBleBottomSheetWidget(
                              action: "punch-out",
                            ),
                          );
                        }
                      }
                    } else {
                      ProfileProvider profileProvider =
                          Provider.of<ProfileProvider>(context, listen: false);

                      if (profileProvider
                              .userDetailesModel?.profileDetails?.wfhStatus ==
                          true) {
                        _onWFHEmployeePressed(context, "punch-out");
                      } else {
                        showModalBottomSheet(
                          context: context,
                          isDismissible: true,
                          shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(12))),
                          builder: (builder) =>
                              const ScanningBleBottomSheetWidget(
                            action: "punch-out",
                          ),
                        );
                      }
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: ThemeColors.colorCD1000,
                  disabledBackgroundColor:
                      ThemeColors.colorCD1000.withOpacity(0.3),
                  minimumSize: const Size(100, 30),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  disabledForegroundColor: Colors.white,
                  // padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 7),
                  textStyle: GoogleFonts.poppins(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                  foregroundColor: Colors.white,
                ),
                child: const Text('Punch Out'),
              );
            }

            return ElevatedButton(
              onPressed: isDisable
                  ? null
                  : () async {
                      var data = await showDialog(
                          context: context,
                          builder: (ctxt) {
                            return AlertDialog(
                              title: const Text('Punch-Out'),
                              content: const Text(
                                  'Do you really want to Punch-Out?'),
                              actions: [
                                ElevatedButton(
                                  onPressed: () =>
                                      Navigator.pop(context, false),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.grey[300],
                                  ),
                                  child: const Text(
                                    'Cancel',
                                    style: TextStyle(color: Colors.black),
                                  ),
                                ),
                                ElevatedButton(
                                  onPressed: () async {
                                    Navigator.pop(context, true);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: ThemeColors.secondaryColor,
                                  ),
                                  child: const Text('Yes'),
                                )
                              ],
                            );
                          });

                      if (data) {
                        var checkListProvider = Provider.of<ChecklistProvider>(
                            context,
                            listen: false);
                        await checkListProvider.getShedulers();
                        await checkListProvider.getOnSignOutChecklist();

                        if (checkListProvider
                            .onSiginOutCheckListModel.isNotEmpty) {
                          bool isDone = await Navigator.push<bool>(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        ChecklistSignOutScreen()),
                              ) ??
                              false;

                          if (isDone) {
                            ProfileProvider profileProvider =
                                Provider.of<ProfileProvider>(context,
                                    listen: false);

                            if (profileProvider.userDetailesModel
                                    ?.profileDetails?.wfhStatus ==
                                true) {
                              _onWFHEmployeePressed(context, "punch-out");
                            } else {
                              showModalBottomSheet(
                                context: context,
                                isDismissible: true,
                                shape: const RoundedRectangleBorder(
                                    borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(12))),
                                builder: (builder) =>
                                    const ScanningBleBottomSheetWidget(
                                  action: "punch-out",
                                ),
                              );
                            }
                          }
                        } else {
                          ProfileProvider profileProvider =
                              Provider.of<ProfileProvider>(context,
                                  listen: false);

                          if (profileProvider.userDetailesModel?.profileDetails
                                  ?.wfhStatus ==
                              true) {
                            _onWFHEmployeePressed(context, "punch-out");
                          } else {
                            showModalBottomSheet(
                              context: context,
                              isDismissible: true,
                              shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(12))),
                              builder: (builder) =>
                                  const ScanningBleBottomSheetWidget(
                                action: "punch-out",
                              ),
                            );
                          }
                        }
                      }
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: ThemeColors.colorCD1000,
                disabledBackgroundColor:
                    ThemeColors.colorCD1000.withOpacity(0.3),
                minimumSize: const Size(100, 30),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                disabledForegroundColor: Colors.white,
                // padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 7),
                textStyle: GoogleFonts.poppins(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
                foregroundColor: Colors.white,
              ),
              child: const Text('Punch Out'),
            );
          },
        ),
      ],
    );
  }

  Future<void> _onWFHEmployeePressed(BuildContext context, String action) {
    PunchInProvider punchInProvider =
        Provider.of<PunchInProvider>(context, listen: false);
    return getLocation(
        context: context,
        function: () async {
          punchInProvider.locationStatus = LocationStatus.loading;
          showModalBottomSheet(
              context: context,
              isDismissible: true,
              shape: const RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(12))),
              builder: (builder) =>
                  MatchingLocationBottomSheet(action: action));
          Position position = await Geolocator.getCurrentPosition(
              desiredAccuracy: LocationAccuracy.bestForNavigation);
          punchInProvider.checkLocationMatching(
            latitude: position.latitude,
            longitude: position.longitude,
          );
          // punchInProvider.checkLocationMatchingStatic();
        });
  }

  Widget _punchInOutWidget(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _punchInWidget(context),
        _verticalDivider(),
        _punchOutWidget(),
      ],
    );
  }

  Widget _verticalDivider() {
    return SizedBox(
      height: 37,
      child: VerticalDivider(
        width: 0,
        color: ThemeColors.colorF3F3F3,
        thickness: 1,
      ),
    );
  }
}

// Function to request location permission
Future<void> requestPermission() async {
  LocationPermission permission = await Geolocator.requestPermission();
  if (permission == LocationPermission.denied ||
      permission == LocationPermission.deniedForever) {
    await Geolocator.openAppSettings();
  }
}

// Function to check and handle GPS (location services) status
Future<void> checkLocationService() async {
  bool isLocationServiceEnabled = await Geolocator.isLocationServiceEnabled();

  if (!isLocationServiceEnabled) {
    // Handle the case where location services (GPS) are disabled
    await Geolocator.openLocationSettings();
  }
}

// Function to get the current location
Future<void> getLocation(
    {required BuildContext context, required Function function}) async {
  EasyLoading.show();
  await requestPermission(); // Request location permission
  LocationPermission permission = await Geolocator.checkPermission();
  EasyLoading.dismiss();
  if (permission != LocationPermission.denied &&
      permission != LocationPermission.deniedForever) {
    EasyLoading.show();
    await checkLocationService(); // Check GPS (location services) status

    bool isLocationServiceEnabled = await Geolocator.isLocationServiceEnabled();
    EasyLoading.dismiss();
    if (isLocationServiceEnabled) {
      // EasyLoading.show();
      // Position position = await Geolocator.getCurrentPosition(
      //     desiredAccuracy: LocationAccuracy.high);
      // EasyLoading.dismiss();

      function();
    }
  }
}
