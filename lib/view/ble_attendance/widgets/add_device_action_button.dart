import 'package:e8_hr_portal/provider/ble_attendance_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/ble_attendance/ble_device_list_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class AddDeviceActionButton extends StatelessWidget {
  const AddDeviceActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 24),
      child: InkWell(
        onTap: () {
          final provider = context.read<BLEAttendanceProvider>();
          provider.getBLEDeviceList();
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const BLEDeviceListScreen(),
            ),
          );
        },
        splashColor: Colors.transparent,
        child: Column(
          children: [
            SvgPicture.asset(
              'assets/icons/add-ble-device.svg',
              height: 30 * h,
              width: 30 * w,
            ),
            Text(
              'Devices',
              style: GoogleFonts.poppins(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
