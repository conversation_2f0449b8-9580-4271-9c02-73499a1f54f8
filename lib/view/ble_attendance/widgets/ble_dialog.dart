import 'package:e8_hr_portal/provider/ble_attendance_provider.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class BLEDialog extends StatelessWidget {
  const BLEDialog({super.key});

  @override
  Widget build(BuildContext context) {
    var status = context.watch<BLEAttendanceProvider>().attendanceStatus;
    return Dialog(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 20),
          const Center(
            child: CircularProgressIndicator.adaptive(),
          ),
          const SizedBox(height: 20),
          // Text(
          //   'Please Wait',
          //   style: GoogleFonts.nunito(
          //     fontWeight: FontWeight.w600,
          //     fontSize: 16,
          //   ),
          // ),
          Text(
            status == AttendanceStatus.scanning
                ? 'Scanning for Nearby BLE Devices'
                : 'Device Found!',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.w400,
              fontSize: 14,
            ),
          ),
          if (status == AttendanceStatus.requesting)
            Text(
              'Please wait for a moment while we confirm your attendance',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w400,
                fontSize: 14,
              ),
            ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
