import 'package:e8_hr_portal/provider/attendance_provider.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/attendance/widgets/attendance_overview_screen.dart';
import 'package:e8_hr_portal/view/attendance/widgets/attendence_vie_log.dart';
import 'package:e8_hr_portal/view/attendance/widgets/records_card.dart';
import 'package:e8_hr_portal/view/certificate_requests/salary_certificate/widget/salary_cert_rejected_commend.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class RecentLogs extends StatelessWidget {
  const RecentLogs({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Consumer<AttendanceProvider>(
        builder: (context, provider, child) {
          if (provider.attendanceList
              .where((e) => e.id != null)
              .toList()
              .isEmpty) {
            return const Center(
              child: Text('No data Found'),
            );
          }
          return ListView.separated(
            key: Unique<PERSON>ey(),
            shrinkWrap: true,
            itemBuilder: (context, index) {
              final data = provider.attendanceList[index];
              if ((data.punchin == null || data.punchin!.isEmpty)) {
                return const SizedBox();
              }
              return InkWell(
                onTap: () async {
                  debugPrint(data.attendanceDate);
                  debugPrint(data.data.toString());
                  if (data.attendanceDate != null && data.data == false) {
                    await provider.getAttendenceViewLog(
                      date: data.attendanceDate,
                    );

                    if (provider.attendanceViewLogModel?.attendanceLog !=
                            null &&
                        provider.attendanceViewLogModel!.attendanceLog!
                            .isNotEmpty &&
                        context.mounted) {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return const AttendenceViewLogDialog();
                        },
                      );
                    } else {
                      showToastText('No records found');
                    }
                  } else if (data.attendanceDate != null && data.data == true) {
                    await provider.getAttendanceDetails(
                      id: data.attendanceDate.toString(),
                    );
                    if (context.mounted) {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) =>
                              const AttendanceOverviewScreen(),
                        ),
                      );
                    }
                  } else if (data.attendanceDate != null &&
                      data.data == false &&
                      data.rejectedComment != null) {
                    showDialog(
                      context: context,
                      builder: (context) {
                        return RequestRejectedCommentDialog(
                          comment: data.rejectedComment.toString(),
                          rejectedPerson: data.rejectedBy,
                        );
                      },
                    );
                  }
                },
                child: RecordsCard(
                  isDisabled: data.status?.isDisabled ?? false,
                  date: data.date,
                  punchinTime: data.punchin,
                  punchoutTime: data.punchout,
                  status: data.status?.content,
                  colorKey: data.status?.color,
                ),
              );
            },
            separatorBuilder: (context, index) {
              final data = provider.attendanceList[index];
              if ((data.punchin == null || data.punchin!.isEmpty)) {
                return const SizedBox();
              }
              return SizedBox(height: 10 * h);
            },
            itemCount: provider.attendanceList.length,
          );
        },
      ),
    );
  }
}
