import 'package:e8_hr_portal/provider/attendance_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/material.dart';
import 'package:month_picker_dialog/month_picker_dialog.dart';
import 'package:provider/provider.dart';

class RecentLogsFilter extends StatelessWidget {
  const RecentLogsFilter({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Recent Logs',
          style: tsS16w500,
        ),
        Consumer<AttendanceProvider>(
          builder: (context, provider, child) {
            String month = formatDateFromDate(
                dateTime: provider.selectedMonth, format: 'MMMM yyyy');
            return InkWell(
              onTap: () => _filterMonth(context),
              child: Container(
                height: 26 * h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Text(
                        month,
                        style: tsS10w400495057,
                      ),
                      const ImageIcon(
                        AssetImage("assets/icons/ocalendar2.png"),
                      )
                    ],
                  ),
                ),
              ),
            );
          },
        )
      ],
    );
  }

  Future<void> _filterMonth(BuildContext context) async {
    var provider = context.read<AttendanceProvider>();
    DateTime? picked = await showMonthPicker(
      // confirmWidget: Text(
      //   "OK",
      //   style: TextStyle(color: ThemeColors.primaryColor),
      // ),
      // cancelWidget: const Text(
      //   "CANCEL",
      //   style: TextStyle(color: Colors.black),
      // ),
      monthPickerDialogSettings: MonthPickerDialogSettings(
        dialogSettings: PickerDialogSettings(
            dismissible: true,
            customWidth: w * 600,
            customHeight: h * 235,
            dialogRoundedCornersRadius: 10),
        buttonsSettings: const PickerButtonsSettings(
            unselectedMonthsTextColor: Colors.black),

        // ae.element8.hrportal
        // actionBarSettings: PickerActionBarSettings(
        //   confirmWidget: Text(
        //     'OK',
        //     style: TextStyle(color: ThemeColors.primaryColor),
        //   ),
        //   cancelWidget: Text(
        //     'CANCEL',
        //     style: TextStyle(color: Colors.black),
        //   ),
        // ),
      ),
      context: context,
      initialDate: provider.selectedMonth,
      firstDate: DateTime(2018),
      lastDate: DateTime(2035),
    );
    if (picked != null) {
      provider.selectedMonth = picked;
      if (context.mounted) {
        provider.getAttendanceList(
          master: false,
          context: context,
        );
      }
    }
  }
}
