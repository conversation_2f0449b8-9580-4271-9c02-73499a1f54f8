// ignore_for_file: use_build_context_synchronously

import 'package:e8_hr_portal/provider/attendance_provider.dart';
import 'package:e8_hr_portal/provider/ble_attendance_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/ble_attendance/widgets/jumping_dot.dart';
import 'package:e8_hr_portal/view/morning_huddle/view/create_morning_huddle_screen.dart';
import 'package:e8_hr_portal/view/morning_huddle/view/morning_huddle_listing_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../helper/button_widget.dart';

class ScanningBleBottomSheetWidget extends StatefulWidget {
  final String action;
  const ScanningBleBottomSheetWidget({super.key, required this.action});

  @override
  State<ScanningBleBottomSheetWidget> createState() =>
      _ScanningBleBottomSheetWidgetState();
}

class _ScanningBleBottomSheetWidgetState
    extends State<ScanningBleBottomSheetWidget> {
  @override
  void initState() {
    super.initState();
    // final provider = context.read<BLEAttendanceProvider>();
    BLEAttendanceProvider provider =
        Provider.of<BLEAttendanceProvider>(context, listen: false);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      debugPrint(
          'rebuilding occured-----------------------------------${widget.action}');
      // provider.emitNumbers(true);
      Future.delayed(const Duration(milliseconds: 500));
      // provider.emitNumbers(false);
      provider.scanNearbyDevices();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: double.infinity,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(12),
            ),
          ),
          child: Column(
            children: [
              Container(
                width: 134 * w,
                height: 5,
                margin: EdgeInsets.only(top: 14 * h, bottom: 51 * h),
                decoration: ShapeDecoration(
                  color: const Color(0xFFEAEBED),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(100),
                  ),
                ),
              ),
              Consumer<BLEAttendanceProvider>(
                builder: (context, provider, _) {
                  debugPrint(
                      'provider.attendanceStatus-----------${provider.attendanceStatus}');
                  if (provider.attendanceStatus !=
                      AttendanceStatus.deviceFound) {
                    return _lookingForDeviceWidget();
                  }
                  return _deviceConnectedWidget(
                      provider: provider,
                      context: context,
                      action: widget.action);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _lookingForDeviceWidget() {
    return Column(
      children: [
        Image.asset('assets/images/scanning-ble.png', scale: 2),
        const SizedBox(height: 10),
        const AnimatedDottedProgressIndicator(),
        const SizedBox(height: 5),
        Text(
          'Looking for device',
          style: GoogleFonts.poppins(
            color: const Color(0xFF181818),
            fontSize: 22 * f,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          "Be patient ! We're trying to connect \nyou to BLE Device.",
          textAlign: TextAlign.center,
          style: GoogleFonts.poppins(
            color: const Color(0xFF979797),
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
        SizedBox(height: 120 * h),
      ],
    );
  }

  Widget _deviceConnectedWidget(
          {required BLEAttendanceProvider provider,
          required BuildContext context,
          required String action}) =>
      Column(
        children: [
          SvgPicture.asset('assets/images/success_green_check.svg'),
          SizedBox(height: 20 * h),
          Text(
            'Device Connected',
            style: GoogleFonts.poppins(
              color: const Color(0xFF181818),
              fontSize: 22 * f,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'You are now connected to a BLE Device.',
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              color: const Color(0xFF979797),
              fontSize: 12 * f,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(height: 28 * h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0 * w),
            child: ButtonWidget(
              title: 'Continue',
              textStyle: tsS16w600cFFFFFF,
              onPressed: () async => await _onContinuePressed(
                  action: action, provider: provider, context: context),
            ),
          ),
          SizedBox(height: 52 * h),
        ],
      );

  _onContinuePressed(
      {required BLEAttendanceProvider provider,
      required BuildContext context,
      required String action}) async {
    AttendanceProvider attendanceProvider =
        Provider.of<AttendanceProvider>(context, listen: false);
    await EasyLoading.show();

    await provider.sendPunchInOutRequest(context: context, action: action);
    if (!mounted) return;
    await provider.getAttendanceLogs();
    if (!mounted) return;
    await attendanceProvider.getAttendanceList(master: false, context: context);
    if (!mounted) return;
    Navigator.pop(context);
    if (action == 'punch-out') return;

    MorningHuddleDialog.showMorningHuddleDialog(context);
    // }
  }
}

class MorningHuddleDialog {
  static void showMorningHuddleDialog(BuildContext context) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? lastHuddleDialogDate = prefs.getString('last_huddle_dialog_date');
    String today =
        formatDateFromDate(dateTime: DateTime.now(), format: 'dd-MM-yyyy');
    if (lastHuddleDialogDate != today) {
      showDialog(
        context: context,
        barrierDismissible: false, // Prevents dismissal by tapping outside
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(
              "Morning Huddle",
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            content: Text("Submit your daily task allocation"),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Skip pressed
                },
                child: Text("Skip"),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Continue pressed
                  // Add your continue logic here
                  PageNavigator.push(
                      context: context, route: MorningHuddleListingScreen());
                  PageNavigator.push(
                      context: context, route: CreateMorningHuddleScreen());
                },
                child: Text("Continue"),
              ),
            ],
          );
        },
      );
    }
  }
}
