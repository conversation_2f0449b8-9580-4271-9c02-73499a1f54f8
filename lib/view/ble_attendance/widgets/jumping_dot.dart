// ignore_for_file: library_private_types_in_public_api

import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';

class AnimatedDottedProgressIndicator extends StatefulWidget {
  const AnimatedDottedProgressIndicator({super.key});

  @override
  _AnimatedDottedProgressIndicatorState createState() =>
      _AnimatedDottedProgressIndicatorState();
}

class _AnimatedDottedProgressIndicatorState
    extends State<AnimatedDottedProgressIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  final int numberOfDots = 4;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2), // Adjust the animation duration
    )..repeat(reverse: true);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 51 * w,
      height: 12 * h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List.generate(numberOfDots, (index) {
          return AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Dot(
                size: CurvedAnimation(
                      parent: _controller,
                      curve: Interval(
                        (1 / numberOfDots) * index,
                        (1 / numberOfDots) * index + 0.25,
                        curve: Curves.linear,
                      ),
                    ).value *
                    10,
                opacity: CurvedAnimation(
                  parent: _controller,
                  curve: Interval(
                    (1 / numberOfDots) * index,
                    (1 / numberOfDots) * index + 0.25,
                    curve: Curves.linear,
                  ),
                ).value,
              );
            },
          );
        }),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

class Dot extends StatelessWidget {
  final double size;
  final double opacity;

  const Dot({super.key, required this.size, required this.opacity});

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: opacity,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 20),
        width: size,
        height: size,
        decoration: const BoxDecoration(
          color: Colors.amber,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}
