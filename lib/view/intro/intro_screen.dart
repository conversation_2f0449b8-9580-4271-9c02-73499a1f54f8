import 'package:e8_hr_portal/view/intro/widgets/carousel_builder.dart';
import 'package:e8_hr_portal/view/intro/widgets/get_started_button.dart';
import 'package:e8_hr_portal/view/intro/widgets/indicator_builder.dart';
import 'package:flutter/material.dart';

class IntroScreen extends StatefulWidget {
  const IntroScreen({super.key});

  @override
  State<IntroScreen> createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen> {
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Column(
        children: [
          CarouselB<PERSON>er(),
          Indicator<PERSON><PERSON>er(),
          GetStartedButton(),
        ],
      ),
    );
  }
}
