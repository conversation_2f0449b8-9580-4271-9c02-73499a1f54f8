import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/sign_in/login_with_email_screen.dart';
import 'package:flutter/material.dart';

class GetStartedButton extends StatelessWidget {
  const GetStartedButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(w * 16, h * 20, w * 16, h * 40),
      child: ButtonWidget(
        title: 'Get Started',
        textStyle: tsS16w600FFFFFF,
        color: ThemeColors.primaryColor,
        onPressed: () => onPressed(context),
      ),
    );
  }

  void onPressed(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const LoginWithEmailScreen(),
      ),
    );
  }
}
