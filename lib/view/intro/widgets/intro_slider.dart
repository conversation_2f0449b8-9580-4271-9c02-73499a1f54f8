import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';

class IntroSlider extends StatelessWidget {
  final String image;
  final String? text1;
  final String? text2;
  final String? subText;
  const IntroSlider({
    required this.image,
    this.text1,
    this.subText,
    super.key,
    this.text2,
  });
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: w * 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          CachedNetworkImage(
            height: 260 * h,
            imageUrl: image,
            errorWidget: errorWidget(),
          ),
          Text(
            "$text1\n$text2",
            style: tsS34w60023262D,
          ),
          Si<PERSON><PERSON><PERSON>(height: h * 14),
          Text(
            subText ?? "",
            style: tsS14w4009F9F9F,
          ),
        ],
      ),
    );
  }

  Widget Function(BuildContext, String, dynamic)? errorWidget() {
    return (context, url, error) {
      return Container(
        height: 260 * h,
        decoration: const BoxDecoration(
          image: DecorationImage(
            fit: BoxFit.cover,
            image: CachedNetworkImageProvider(
              "https://hisense-hrportal.e8demo.com/media/splash_img/undraw_working_re_ddwy_1.png",
            ),
          ),
        ),
      );
    };
  }
}
