import 'package:carousel_slider/carousel_slider.dart';
import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/intro/widgets/intro_slider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

final CarouselSliderController introCarouselController =
    CarouselSliderController();

class CarouselBuilder extends StatelessWidget {
  const CarouselBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<MasterProvider>();
    return Expanded(
      child: CarouselSlider.builder(
        itemCount: provider.splashList.length,
        itemBuilder: (context, index, realIndex) {
          final data = provider.splashList[index];
          return IntroSlider(
            text1: data.title1,
            text2: data.title2,
            subText: data.description,
            image: data.splashScreenImg ?? '',
          );
        },
        carouselController: introCarouselController,
        options: CarouselOptions(
          autoPlay: false,
          enlargeCenterPage: false,
          viewportFraction: 1,
          scrollDirection: Axis.horizontal,
          aspectRatio: h * 0.65,
          initialPage: 0,
          onPageChanged: (index, reason) {
            provider.getStartedSliderIndex = index;
          },
        ),
      ),
    );
  }
}
