import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/view/intro/widgets/carousel_builder.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class IndicatorBuilder extends StatelessWidget {
  const IndicatorBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<MasterProvider>();
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: provider.splashList.asMap().entries.map((entry) {
        Color color = ThemeColors.colorE5E4E3;
        if (provider.getStartedSliderIndex == entry.key) {
          color = ThemeColors.colorF9637D;
        }
        return GestureDetector(
          onTap: () => introCarouselController.animateToPage(entry.key),
          child: Container(
            width: 8.0,
            height: 8.0,
            margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: color,
            ),
          ),
        );
      }).toList(),
    );
  }
}
