import 'package:e8_hr_portal/provider/log_time_provider.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/view/chat_bot/chat_screen_wrapper.dart';
import 'package:e8_hr_portal/view/dashboard/sections/leave_applied_employees_section.dart';
import 'package:e8_hr_portal/view/dashboard/sections/login_to_web_section.dart';
import 'package:e8_hr_portal/view/dashboard/sections/upcoming_holidays_section.dart';
import 'package:e8_hr_portal/view/dashboard/widgets/attendance_card.dart';
import 'package:e8_hr_portal/view/dashboard/widgets/current_task_card.dart';
import 'package:e8_hr_portal/view/dashboard/widgets/employee_card.dart';
import 'package:e8_hr_portal/view/dashboard/widgets/leaves_card.dart';
import 'package:e8_hr_portal/view/feedback_and_complaints/feedback_screen.dart';
import 'package:e8_hr_portal/view/task_management/task_management.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/provider/attendance_provider.dart';
import 'package:e8_hr_portal/provider/company_activities_provider.dart';
import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/provider/medical_insurance_provider.dart';
import 'package:e8_hr_portal/provider/team_members_provider.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/birthday_anniversary/birthday_anniversary_section.dart';
import 'package:e8_hr_portal/view/employee_relations/medical_insurance.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/view/recent_activites/recent_activities_section_dashboard.dart';
import 'package:e8_hr_portal/view/upcoming_holidays/upcoming_holidays.dart';
import 'package:provider/provider.dart';
import '../../provider/birthday_anniversary_provider.dart';
import '../../provider/faq_provider.dart';
import '../../provider/notification_provider.dart';
import '../../provider/profile_provider.dart';
import '../ble_attendance/widgets/punch_in_out_widget.dart';
import 'sections/pending_leaves_section.dart';
import 'sections/updates_section.dart';
import 'widgets/navigation_button_builder.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});
  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen>
    with TickerProviderStateMixin {
  late UpdatesProvider updatesProvider;
  int? totalMembers;
  int? newMembersCount;
  int currentIndex = 0;
  final int itemSize = 70;
  late final AnimationController _animationController;
  late final AnimationController updatedAnimationController;
  late final Animation<double> _animation;
  // late final Animation<Offset> _offsetAnimation;
  late final Animation<Offset> updateAnimation;
  late final Animation<Offset> updateAnimation2;
  // Tween<double>(begin: 1, end: 1.05)
  //     .animate(CurvedAnimation(
  //         parent: _animationController, curve: Curves.easeInOut));
  final scrollController = ScrollController();

  double opacity = 0;
  double scale = 0;
  double opacity2 = 0;
  double scale2 = 0;
  double opacity3 = 0;
  double scale3 = 0;
  double opacity4 = 0;
  double scale4 = 0;
  double opacity5 = 0;
  double scale5 = 0;
  double opacity6 = 0;
  double scale6 = 0;
  double opacity7 = 0;
  double scale7 = 0;
  double opacity8 = 0;
  double scale8 = 0;
  double opacity9 = 0;
  double scale9 = 0;
  void onListen() {
    setState(() {});
  }

  @override
  void initState() {
    anim();
    scrollController.addListener(onListen);
    init();
    super.initState();
  }

  @override
  void dispose() {
    _animationController.dispose();
    scrollController.removeListener(onListen);
    super.dispose();
  }

  double wrapWidth = 0;
  @override
  Widget build(BuildContext context) {
    var attendancePercentage =
        Provider.of<AttendanceProvider>(context).onTimeArrivaldash;
    totalMembers = Provider.of<TeamMembersProvider>(context).totalCount;
    newMembersCount = Provider.of<TeamMembersProvider>(context).newMembersCount;
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: RefreshIndicator(
        onRefresh: () async => await refresh(),
        child: Container(
          padding: const EdgeInsets.only(top: 20),
          decoration: BoxDecoration(
            color: ThemeColors.colorF4F5FA,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
          ),
          child: Consumer2<MasterProvider, LeaveApplicationProvider>(
            builder: (context, provider, leaveProvider, child) {
              return ListView(
                padding: const EdgeInsets.all(15.0),
                controller: scrollController,
                physics: const BouncingScrollPhysics(
                  parent: AlwaysScrollableScrollPhysics(),
                  decelerationRate: ScrollDecelerationRate.normal,
                ),
                dragStartBehavior: DragStartBehavior.start,
                children: [
                  AnimatedBuilder(
                      animation: _animationController,
                      builder: (BuildContext context, Widget? child) {
                        return SlideTransition(
                          position: updateAnimation,
                          child: const Column(
                            children: [
                              UpdatesSection(),
                              SizedBox(height: 15.0),
                              CurrentTaskCard(),
                              PunchInOutWidget(),
                              SizedBox(height: 15.0),
                            ],
                          ),
                        );
                      }),
                  LayoutBuilder(builder: (context, constarints) {
                    animation(constarints, provider);
                    return AnimatedBuilder(
                        animation: _animationController,
                        builder: (BuildContext context, Widget? child) {
                          return FadeTransition(
                            opacity: _animation,
                            child: Wrap(
                              runSpacing: 15,
                              spacing: 15,
                              children: [
                                AttendanceCard(
                                  attendance: attendancePercentage,
                                ),

                                const LeavesCard(),
                                // _certificatesAndLettersCard(),
                                // _trainigCard(),
                                const EmployeeCard(),
                                _holidaysCard(provider),
                                // _recruitmentsCard(provider),
                                _taskManagementCard(provider),
                                _medicalInsuranceCard(provider),
                                _feedbackCard(provider)
                              ],
                            ),
                          );
                        });
                  }),
                  Opacity(
                    opacity: provider.isLastReached ? 1 : opacity6,
                    child: Transform.scale(
                      scale: provider.isLastReached ? 1 : opacity6,
                      child: const RecentActivitiesSectionDashboard(),
                    ),
                  ),
                  const SizedBox(height: 20),
                  if (leaveProvider.pendingEmpDetailes?.data != null)
                    Opacity(
                      opacity: provider.isLastReached ? 1 : opacity8,
                      child: Transform.scale(
                          scale: provider.isLastReached ? 1 : opacity8,
                          child: const PendingLeavesSection()),
                    ),
                  Opacity(
                    opacity: provider.isLastReached ? 1 : opacity8,
                    child: Transform.scale(
                        scale: provider.isLastReached ? 1 : opacity8,
                        // transform: Matrix4.identity()..scale(opacity4, 1.0),
                        child: const BirthdayAnniversarySection()),
                  ),
                  Opacity(
                      opacity: provider.isLastReached ? 1 : opacity8,
                      child: const LeaveAppliedEmployeesSection()),
                  Opacity(
                    opacity: provider.isLastReached ? 1 : opacity8,
                    child: Transform.scale(
                        scale: provider.isLastReached ? 1 : opacity8,
                        // transform: Matrix4.identity()..scale(opacity4, 1.0),
                        child: const UpcomingHolidaysSection()),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  const LoginToWebSection(),
                ],
              );
            },
          ),
        ),
      ),
      floatingActionButton: InkWell(
        onTap: () {
          PageNavigator.push(
              context: context, route: const ChatScreenWrapper());
        },
        child: Image.asset(
          'assets/icons/chat-bot.png',
          height: 70,
          width: 70,
        ),
      ),
    );
  }

  Widget _feedbackCard(MasterProvider pro) {
    return Opacity(
      opacity: pro.isLastReached ? 1 : opacity5,
      child: Transform.scale(
        scale: pro.isLastReached ? 1 : opacity5,
        child: NavigationButtonBuilder(
          bgcolor2: ThemeColors.colorFFC483,
          bgcolor1: ThemeColors.colorFF8600,
          iconBgColor: Colors.white.withOpacity(0.10),
          icon: const ImageIcon(
            AssetImage('assets/icons/certificate.png'),
            color: Colors.white,
          ),
          title: 'Feedback & Complaints',
          // subtitle: '4 Requests Approved',
          iconBg: const ImageIcon(
            AssetImage('assets/icons/certificate_cut.png'),
            color: Colors.white,
          ),
          onPressed: () async {
            FAQProvider provider =
                Provider.of<FAQProvider>(context, listen: false);
            provider.selectedImages.clear();

            PageNavigator.pushSlideup(
                context: context, route: const FeedbackScreen());
          },
        ),
      ),
    );
  }

  Widget _holidaysCard(MasterProvider provider) {
    return Consumer<CompanyActivitiesProvider>(
      builder: (context, provider, child) {
        return NavigationButtonBuilder(
          bgcolor1: ThemeColors.color4D8A0B,
          bgcolor2: ThemeColors.color4CD964,
          iconBgColor: ThemeColors.color4CD964.withOpacity(0.50),
          icon: const ImageIcon(
            AssetImage('assets/icons/holidays.png'),
            color: Colors.white,
          ),
          title: 'Holidays',
          subtitle: provider.upcomingHoliday ?? '',
          iconBg: const ImageIcon(
            AssetImage('assets/icons/holidays_cut.png'),
            color: Colors.white,
          ),
          onPressed: _onHolidaysPressed,
        );
      },
    );
  }

  Widget _taskManagementCard(MasterProvider pro) {
    var provider = context.read<LogTimeProvider>();

    return Opacity(
      opacity: pro.isLastReached ? 1 : opacity4,
      child: Transform.scale(
        scale: pro.isLastReached ? 1 : opacity4,
        child: NavigationButtonBuilder(
          bgcolor2: const Color(0xff9f90fc),
          bgcolor1: const Color(0xff6e14c8),
          iconBgColor: Colors.white.withOpacity(0.10),
          icon: const ImageIcon(
            AssetImage('assets/icons/clipboard_task_management_small.png'),
            color: Colors.white,
          ),
          title: 'Task Management',
          subtitle: '${provider.userCurrentTaskList.length} Task Running',
          iconBg: const ImageIcon(
            AssetImage('assets/icons/clipboard_task_management.png'),
            color: Colors.white,
          ),
          onPressed: _onViewMoreAddTaskButtonPressed,
        ),
      ),
    );
  }

  Widget _medicalInsuranceCard(MasterProvider provider) {
    return Opacity(
      opacity: provider.isLastReached ? 1 : opacity4,
      child: Transform.scale(
        scale: provider.isLastReached ? 1 : opacity4,
        child: NavigationButtonBuilder(
          bgcolor1: ThemeColors.color0F8E78,
          bgcolor2: ThemeColors.color90FCA1,
          iconBgColor: Colors.white.withOpacity(0.10),
          icon: const ImageIcon(
            AssetImage('assets/icons/performance.png'),
            color: Colors.white,
          ),
          title: 'Medical Insurance',
          // subtitle: 'KPI',
          iconBg: const ImageIcon(
            AssetImage('assets/icons/performance2.png'),
            color: Colors.white,
          ),
          // onPressed: () => _onPerformancePressed()),
          onPressed: () async {
            var provider = context.read<MedicalInsuranceProvider>();
            await EasyLoading.show();

            await provider.medicalInsurance();
            await provider.medicalInsuranceDownload();

            await EasyLoading.dismiss();
            if (!mounted) return;
            PageNavigator.pushSlideup(
              context: context,
              route: const MedicalInsuranceScreen(),
            );
          },
        ),
      ),
    );
  }

  _onViewMoreAddTaskButtonPressed() async {
    final logTimeProvider = context.read<LogTimeProvider>();
    if (!logTimeProvider.isLoading) {
      bool isGranded = await logTimeProvider.getTotalWorkingHours();
      await logTimeProvider.getUserTimeLogList(master: false);
      logTimeProvider.selectedDate = DateTime.now();
      logTimeProvider.getCompletedTasks();
      if (mounted && isGranded) {
        Navigator.push(
          context,
          CupertinoPageRoute(
            builder: (context) => const TaskManagementScreen(),
          ),
        );
      } else {
        if (!mounted) return;
        showSnackBarMessage(context: context, msg: 'Something went wrong');
      }
    }
  }

  _onHolidaysPressed() async {
    EasyLoading.show();
    CompanyActivitiesProvider activitiesProvider =
        Provider.of<CompanyActivitiesProvider>(context, listen: false);
    if (activitiesProvider.indianHolidaysList.isEmpty) {
      await activitiesProvider.getHolidays();
    }
    if (!mounted) return;
    PageNavigator.pushSlideup(
      context: context,
      route: const UpcomingHilodays(),
    );
    EasyLoading.dismiss();
  }

  void init() async {
    // _logTimeProvider = Provider.of<LogTimeProvider>(context, listen: false);
    var updatesProvider = context.read<UpdatesProvider>();
    var provider = context.read<CompanyActivitiesProvider>();
    var timelog = context.read<LogTimeProvider>();

    provider.getHolidays();
    updatesProvider.getUpdates(context: context);
    provider.getUserPoliciesHolidays();
    provider.getUpcomingHolidays();
    timelog.getUserTimeLogList(master: true);
  }

  void anim() async {
    _animationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..forward();
    updatedAnimationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..forward();
    updateAnimation = Tween<Offset>(
      begin: const Offset(-1.5, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: updatedAnimationController,
      curve: Curves.fastLinearToSlowEaseIn,
    ));
    updateAnimation2 = Tween<Offset>(
      begin: const Offset(
        1.5,
        0.0,
      ),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: updatedAnimationController,
      curve: Curves.elasticIn,
    ));
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInCubic,
    );
    // _offsetAnimation = Tween<Offset>(
    //   begin: const Offset(-2, 0),
    //   end: Offset.zero,
    // ).animate(CurvedAnimation(
    //   parent: _animationController,
    //   curve: Curves.elasticIn,
    // ));
  }

  void animation(BoxConstraints constarints, MasterProvider provider) {
    wrapWidth = constarints.maxWidth;

    final itemsOffset = 0 * itemSize;
    final difference = scrollController.offset - itemsOffset;
    final percent = (difference / (itemSize / 2));

    if (percent > 15) {
      // selected = true;
      provider.isLastReached = true;
    }

    scale = percent;
    opacity = percent;
    if (opacity > 1.0) opacity = 1;
    if (opacity < 0) opacity = 0;

    final itemsOffset2 = 1 * itemSize;
    final difference2 = scrollController.offset - itemsOffset2;
    final percent2 = (difference2 / (itemSize / 2));

    scale2 = percent2;
    opacity2 = percent2;
    if (opacity2 > 1.0) opacity2 = 1;
    if (opacity2 < 0) opacity2 = 0;

    final itemsOffset3 = 2 * itemSize;
    final difference3 = scrollController.offset - itemsOffset3;
    final percent3 = (difference3 / (itemSize / 2));

    scale3 = percent3;
    opacity3 = percent3;
    if (opacity3 > 1.0) opacity3 = 1;
    if (opacity3 < 0.0) opacity3 = 0.0;

    final itemsOffset4 = 3 * itemSize;
    final difference4 = scrollController.offset - itemsOffset4;
    final percent4 = (difference4 / (itemSize / 2));

    scale4 = percent4;
    opacity4 = percent4;
    if (opacity4 > 1.0) opacity4 = 1;
    if (opacity4 < 0.0) opacity4 = 0.0;

    final itemsOffset5 = 4 * itemSize;
    final difference5 = scrollController.offset - itemsOffset5;
    final percent5 = (difference5 / (itemSize / 2));

    scale5 = percent5;
    opacity5 = percent5;
    if (opacity5 > 1.0) opacity5 = 1;
    if (opacity5 < 0.0) opacity5 = 0.0;

    final itemsOffset6 = 5 * itemSize;
    final difference6 = scrollController.offset - itemsOffset6;
    final percent6 = (difference6 / (itemSize / 2));
    scale6 = percent6;
    opacity6 = percent6;
    if (opacity6 > 1.0) {
      opacity6 = 1;
      // selected = true;
    }
    if (opacity6 < 0.0) {
      opacity6 = 0.0;
      // selected = false;
    }

    final itemsOffset7 = 6 * itemSize;
    final difference7 = scrollController.offset - itemsOffset7;
    final percent7 = (difference7 / (itemSize / 2));
    scale7 = percent7;
    opacity7 = percent7;
    if (opacity7 > 1.0) {
      opacity7 = 1;
    }
    if (opacity7 < 0.0) {
      opacity7 = 0.0;
    }
    final itemsOffset8 = 7 * itemSize;
    final difference8 = scrollController.offset - itemsOffset8;
    final percent8 = (difference8 / (itemSize / 2));
    scale8 = percent8;
    opacity8 = percent8;
    if (opacity8 > 1.0) {
      opacity8 = 1;
    }
    if (opacity8 < 0.0) {
      opacity8 = 0.0;
    }
    final itemsOffset9 = 8 * itemSize;
    final difference9 = scrollController.offset - itemsOffset9;
    final percent9 = (difference9 / (itemSize / 2));
    scale9 = percent9;
    opacity9 = percent9;
    if (opacity9 > 1.0) {
      opacity9 = 1;
    }
    if (opacity9 < 0.0) {
      opacity9 = 0.0;
    }
  }

  Future<void> refresh() async {
    Provider.of<LeaveApplicationProvider>(context, listen: false)
        .getPendingLeaves();
    Provider.of<LeaveApplicationProvider>(context, listen: false)
        .getLeaveAppliedEmployees();
    Provider.of<NotificationProvider>(context, listen: false)
        .getNotificationCount();
    Provider.of<ProfileProvider>(context, listen: false)
        .getProfileData(context: context);
    Provider.of<BirthdayAnniversaryProvider>(context, listen: false)
        .getBirthdayAnniversary(master: true, context: context);
    Provider.of<CompanyActivitiesProvider>(context, listen: false)
        .getRecentActivity(master: true, context: context);
    Provider.of<LogTimeProvider>(context, listen: false)
        .getUserTimeLogList(master: true);
    return Provider.of<UpdatesProvider>(context, listen: false)
        .getUpdates(context: context);
  }
}

String secondsToHHMMSS(int seconds) {
  int hours = seconds ~/ 3600;
  int minutes = (seconds ~/ 60) % 60;
  int remainingSeconds = seconds % 60;

  String hh = hours.toString().padLeft(2, '0');
  String mm = minutes.toString().padLeft(2, '0');
  String ss = remainingSeconds.toString().padLeft(2, '0');

  return '$hh:$mm:$ss';
}

// _onTrainingPressed() async {
//   showDialog(
//     context: context,
//     builder: (context) {
//       return AlertDialog(
//         title: const Text("Training"),
//         content: const Text("Are you sure want to open Training in Web?"),
//         actions: [
//           ElevatedButton(
//             onPressed: () => Navigator.pop(context),
//             style: ElevatedButton.styleFrom(
//               backgroundColor: Colors.grey[300],
//             ),
//             child: const Text(
//               'Cancel',
//               style: TextStyle(color: Colors.black),
//             ),
//           ),
//           ElevatedButton(
//             onPressed: () async {
//               Navigator.pop(context);
//               if (!await launchUrl(
//                 Uri.parse("$baseURL/training-library"),
//               )) {
//                 throw Exception('Could not launch');
//               }
//             },
//             style: ElevatedButton.styleFrom(
//               backgroundColor: ThemeColors.secondaryColor,
//             ),
//             child: const Text('Yes'),
//           ),
//         ],
//       );
//     },
//   );
// }

// _onRecruitmentsPressed() async {
//   showDialog(
//     context: context,
//     builder: (context) {
//       return AlertDialog(
//         title: const Text("Recruitments"),
//         content: const Text("Are you sure want to open Recruitments in Web?"),
//         actions: [
//           ElevatedButton(
//             onPressed: () => Navigator.pop(context),
//             style: ElevatedButton.styleFrom(
//               backgroundColor: Colors.grey[300],
//             ),
//             child: const Text(
//               'Cancel',
//               style: TextStyle(color: Colors.black),
//             ),
//           ),
//           ElevatedButton(
//             onPressed: () async {
//               // Navigator.pop(context);
//               // Navigator.pop(context);
//               if (!await launchUrl(
//                 Uri.parse("$baseURL/recruitment/"),
//               )) {
//                 throw Exception('Could not launch');
//               }
//             },
//             style: ElevatedButton.styleFrom(
//               backgroundColor: ThemeColors.secondaryColor,
//             ),
//             child: const Text('Yes'),
//           ),
//         ],
//       );
//     },
//   );
// }

// _onPerformancePressed() async {
//   showDialog(
//     context: context,
//     builder: (context) {
//       return AlertDialog(
//         title: const Text("My Performance/KPI"),
//         content: const Text(
//             "Are you sure want to open My Performance/KPI in Web?"),
//         actions: [
//           ElevatedButton(
//             onPressed: () => Navigator.pop(context),
//             style: ElevatedButton.styleFrom(
//               backgroundColor: Colors.grey[300],
//             ),
//             child: const Text(
//               'Cancel',
//               style: TextStyle(color: Colors.black),
//             ),
//           ),
//           ElevatedButton(
//             onPressed: () async {
//               // Navigator.pop(context);
//               // Navigator.pop(context);
//               if (!await launchUrl(
//                 Uri.parse("$baseURL/my-kpi/"),
//               )) {
//                 throw Exception('Could not launch');
//               }
//             },
//             style: ElevatedButton.styleFrom(
//               backgroundColor: ThemeColors.secondaryColor,
//             ),
//             child: const Text('Yes'),
//           ),
//         ],
//       );
//     },
//   );
// }

// _onMeetingRoomOpened() async {
//   showDialog(
//     context: context,
//     builder: (context) {
//       return AlertDialog(
//         title: const Text("Meeting Room"),
//         content: const Text("Are you sure want to open Meeting room in Web?"),
//         actions: [
//           ElevatedButton(
//             onPressed: () => Navigator.pop(context),
//             style: ElevatedButton.styleFrom(
//               backgroundColor: Colors.grey[300],
//             ),
//             child: const Text(
//               'Cancel',
//               style: TextStyle(color: Colors.black),
//             ),
//           ),
//           ElevatedButton(
//             onPressed: () async {
//               // Navigator.pop(context);
//               // Navigator.pop(context);
//               if (!await launchUrl(
//                 Uri.parse("https://26474.humly.cloud/hcp/login"),
//               )) {
//                 throw Exception('Could not launch');
//               }
//             },
//             style: ElevatedButton.styleFrom(
//               backgroundColor: ThemeColors.secondaryColor,
//             ),
//             child: const Text('Yes'),
//           ),
//         ],
//       );
//     },
//   );
// if (LoggedInUser.enableMeetingModule) {
//   // provider.currentIndex = 1;
//   Provider.of<MeetingRoomBookingProvider>(context, listen: false)
//       .getMeetingRoomsList(context: context);
//   Provider.of<MeetingRoomBookingProvider>(context, listen: false)
//       .getCollegues(context: context);
//   Provider.of<NewMeetingRoomProvider>(context, listen: false)
//       .getAllAmenities();
//   PageNavigator.push(
//     context: context,
//     route: const MeetingRoomScreen(),
//   );
// } else {
//   showToastText('You don`t have permission to access');
// }
// }
