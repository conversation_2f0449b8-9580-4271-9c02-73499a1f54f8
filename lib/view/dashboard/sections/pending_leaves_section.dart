import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../leave applications/provider/leave_apllication_provider.dart'
    as leave;
import '../../leave_request_for_reporting_person/leave_request_for_reporting_person.dart';
import '../../leave_request_for_reporting_person/widget/leave_requested_cards.dart';
import '../widgets/title_text_widget.dart';

class PendingLeavesSection extends StatelessWidget {
  const PendingLeavesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<leave.LeaveApplicationProvider>(
      builder: (context, provider, child) {
        List? data = provider.pendingEmpDetailes?.data;
        if (data == null || data.isEmpty) {
          return const SizedBox();
        }
        return Column(
          children: [
            const SizedBox(
              height: 15,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const TitleTextWidget('Pending Leave Requests'),
                InkWell(
                  onTap: () async {
                    provider.selectedAction = 'pending/in-progress';
                    provider.currentPageLeaveRequest = 0;
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) =>
                            const LeaveRequestForReportingPerson(),
                      ),
                    );
                  },
                  child: Text(
                    'View All',
                    style: GoogleFonts.poppins(
                        color: ThemeColors.colorF9637D,
                        fontSize: 12,
                        fontWeight: FontWeight.w400),
                  ),
                )
              ],
            ),
            const SizedBox(
              height: 10,
            ),
            ListView.separated(
              shrinkWrap: true,
              physics: const ClampingScrollPhysics(),
              itemBuilder: (ctx, index) {
                return LeaveRequestedTilesCard(
                  data: provider.pendingEmpDetailes?.data?[index],
                );
              },
              separatorBuilder: (ctx, index) {
                return SizedBox(
                  height: 10 * h,
                );
              },
              itemCount: provider.pendingEmpDetailes?.data?.length ?? 0,
            ),
            const SizedBox(height: 15)
          ],
        );
      },
    );
  }
}
