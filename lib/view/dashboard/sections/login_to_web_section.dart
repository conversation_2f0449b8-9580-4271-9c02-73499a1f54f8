import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class LoginToWebSection extends StatelessWidget {
  const LoginToWebSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 20, bottom: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.secondary,
          ],
        ),
      ),
      child: Column(children: [
        Text(
          "Want to Explore More",
          style: tsS16FFFFF,
        ),
        const SizedBox(
          height: 8,
        ),
        Text(
          "Login to our web portal",
          style: tsS12wRcWhite,
        ),
        const SizedBox(
          height: 8,
        ),
        InkWell(
          onTap: () async {
            if (!await launchUrl(
              Uri.parse(baseUrl),
            )) {
              throw Exception('Could not launch');
            }
          },
          child: Container(
            height: 34,
            width: 134,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.white, width: 1),
              borderRadius: BorderRadius.circular(28),
            ),
            child: Center(
              child: Text(
                'Login Now',
                style: tsS12wRcWhite,
              ),
            ),
          ),
        )
      ]),
    );
  }
}
