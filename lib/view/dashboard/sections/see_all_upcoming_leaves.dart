import 'package:flutter/material.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';
import '../../../model/leave_applied_employee_model.dart';
import '../../../util/size_config.dart';
import '../../leave applications/provider/leave_apllication_provider.dart';
import '../widgets/upcoming_leaves_card.dart';

class SeeAllUpcomingLeavesScreen extends StatelessWidget {
  const SeeAllUpcomingLeavesScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        screenTitle: 'Upcoming Leaves',
        body: Consumer<LeaveApplicationProvider>(
          builder: (context, provider, child) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: h * 10,
                        ),
                        ListView.separated(
                          physics: const ClampingScrollPhysics(),
                          shrinkWrap: true,
                          padding: const EdgeInsets.only(bottom: 20),
                          itemBuilder: (context, index) {
                            LeaveAppliedEmployeeModel? data =
                                provider.leaveAppliedEmployeeList[index];
                            String? startDate = data.startDate;
                            String? endDate = data.endDate;
                            return UpcomingLeavesCard(
                              name: data.userDetails?.name,
                              profilePic: data.userDetails?.profilePic,
                              startDate: startDate,
                              endDate: endDate,
                              leaveType: data.leaveType,
                              dayType: data.dayType,
                            );
                          },
                          separatorBuilder: (context, index) {
                            return const SizedBox(height: 15);
                          },
                          itemCount: provider.leaveAppliedEmployeeList.length,
                        )
                      ],
                    )),
                  ),
                ],
              ),
            );
          },
        ));
  }
}
