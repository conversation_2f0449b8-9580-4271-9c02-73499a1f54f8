import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/dashboard/sections/see_all_upcoming_leaves.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../model/leave_applied_employee_model.dart';
import '../../../util/colors.dart';
import '../../leave applications/provider/leave_apllication_provider.dart';

class LeaveAppliedEmployeesSection extends StatelessWidget {
  const LeaveAppliedEmployeesSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<LeaveApplicationProvider>(
      builder: (context, provider, child) {
        return provider.leaveAppliedEmployeeList.isEmpty
            ? const SizedBox()
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Upcoming Leaves',
                        style: tsS16w500,
                      ),
                      if (provider.leaveAppliedEmployeeList.length > 3)
                        InkWell(
                          onTap: () {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        const SeeAllUpcomingLeavesScreen()));
                          },
                          child: Text(
                            'View All',
                            style: GoogleFonts.poppins(
                              color: ThemeColors.colorF9637D,
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 25),
                  SizedBox(
                    height: h * 185,
                    child: ListView.builder(
                      physics: const ClampingScrollPhysics(),
                      scrollDirection: Axis.horizontal,
                      itemCount:
                          provider.leaveAppliedEmployeeList.take(3).length,
                      itemBuilder: (context, index) {
                        LeaveAppliedEmployeeModel? employee =
                            provider.leaveAppliedEmployeeList[index];

                        String? startDate = employee.startDate;
                        String? endDate = employee.endDate;
                        return Container(
                          margin: const EdgeInsets.only(right: 10),
                          width: w * 120,
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Column(
                            children: [
                              SizedBox(
                                height: h * 20,
                              ),
                              ClipRRect(
                                borderRadius: BorderRadius.circular(60),
                                child: CachedNetworkImage(
                                  width: w * 62,
                                  height: h * 62,
                                  fit: BoxFit.cover,
                                  imageUrl:
                                      employee.userDetails?.profilePic ?? '',
                                  errorWidget: (context, url, error) {
                                    return Container(
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).primaryColor,
                                        shape: BoxShape.circle,
                                      ),
                                      alignment: Alignment.center,
                                      child: Text(
                                        employee.userDetails?.name
                                                ?.substring(0, 1)
                                                .toUpperCase()
                                                .toUpperCase() ??
                                            '',
                                        style: GoogleFonts.rubik(
                                          fontSize: 22,
                                          fontWeight: FontWeight.w500,
                                          color: Colors.white,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                              SizedBox(height: h * 14),
                              Text(
                                textAlign: TextAlign.center,
                                employee.userDetails?.name ?? '',
                                maxLines: 1,
                                style: tsS14w500Black,
                              ),
                              const SizedBox(height: 3),
                              Text(
                                employee.leaveType ?? '',
                                style: GoogleFonts.poppins(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w400,
                                  color: employee.leaveType == 'Absent'
                                      ? Colors.red
                                      : const Color(0xffA01BC0),
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 3),
                              if (startDate != null &&
                                  endDate != null &&
                                  startDate != endDate)
                                Text(
                                  '${formatDateFromString(startDate, 'dd MMM,yyyy', 'dd MMM')} - ${formatDateFromString(endDate, 'dd MMM,yyyy', 'dd MMM')}',
                                  style: GoogleFonts.poppins(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w400,
                                    color: const Color(0xffA01BC0),
                                  ),
                                  textAlign: TextAlign.center,
                                )
                              else if (startDate != null)
                                Text(
                                  formatDateFromString(
                                      startDate, 'dd MMM,yyyy', 'dd MMM'),
                                  style: GoogleFonts.poppins(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w400,
                                    color: const Color(0xffA01BC0),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              const SizedBox(height: 3),
                              Text(
                                employee.dayType ?? '',
                                style: GoogleFonts.poppins(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xffA01BC0),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              );
      },
    );
  }
}
