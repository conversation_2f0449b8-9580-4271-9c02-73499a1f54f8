// ignore_for_file: use_build_context_synchronously

import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../../model/holidays_model.dart';
import '../../../provider/company_activities_provider.dart';
import '../../../util/size_config.dart';
import '../../upcoming_holidays/upcoming_holidays.dart';

class UpcomingHolidaysSection extends StatelessWidget {
  const UpcomingHolidaysSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CompanyActivitiesProvider>(builder: (context, birthPro, _) {
      if (birthPro.upcomingHolidaysList.isEmpty) {
        return const SizedBox();
      }
      return Container(
        margin: EdgeInsets.only(top: 20 * h),
        // width: 343 * w,
        // height: 237 * h,
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Upcoming Holidays',
                  style: tsS16w500,
                ),
                InkWell(
                  onTap: () async {
                    EasyLoading.show();
                    CompanyActivitiesProvider activitiesProvider =
                        Provider.of<CompanyActivitiesProvider>(context,
                            listen: false);
                    await activitiesProvider.getHolidays();

                    PageNavigator.push(
                      context: context,
                      route: const UpcomingHilodays(),
                    );
                    EasyLoading.dismiss();
                  },
                  child: Text(
                    'View All',
                    style: GoogleFonts.poppins(
                        color: ThemeColors.colorF9637D,
                        fontSize: 12,
                        fontWeight: FontWeight.w400),
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 10,
            ),
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(12)),
              child: ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: birthPro.upcomingHolidaysList.length,
                itemBuilder: (context, index) {
                  HolidaysModel upcomingHoly =
                      birthPro.upcomingHolidaysList[index];
                  String? date = upcomingHoly.startDate;
                  int? days = upcomingHoly.days;
                  String? formattedStartDate;
                  String? formattedEndDate;
                  String? startFromDate;
                  // String? event = upcomingHoly.occassion;
                  bool isConfirm = upcomingHoly.isConfirm ?? false;
                  // bool dateBefor = false;

                  if (date != null) {
                    formattedStartDate = formatDateFromString(
                        date, 'yyyy-MM-ddThh:mm:ssZ', 'dd MMM yy EEE');
                    startFromDate = formatDateFromString(
                        date, 'yyyy-MM-ddThh:mm:ssZ', 'dd\nMMM');
                    DateTime dateTime = DateTime.parse(date.toString());

                    if (days != null) {
                      DateTime endDate = dateTime.add(Duration(days: days - 1));
                      formattedEndDate = formatDateFromDate(
                          dateTime: endDate, format: 'dd MMM yy EEE');
                    }
                    // var da = DateTime.parse(date);
                    // dateBefor = DateTime.now().isBefore(da);
                  }
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        height: 50 * h,
                        width: 50 * w,
                        decoration: BoxDecoration(
                            color: ThemeColors.colorF8F8F8,
                            borderRadius: BorderRadius.circular(8)),
                        child: Text(
                          startFromDate ?? '',
                          style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: ThemeColors.colorFCD900,
                              height: 1),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 10.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              SizedBox(
                                width: 165 * w,
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        upcomingHoly.name ?? '',
                                        style: tsS14w500Black,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    Container(
                                      margin:
                                          const EdgeInsets.fromLTRB(5, 2, 5, 2),
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                          color: ThemeColors.color979797
                                              .withOpacity(0.2),
                                          borderRadius:
                                              BorderRadius.circular(2)),
                                      child: Text(
                                          upcomingHoly.holidayPolicy ?? ''),
                                    ),
                                  ],
                                ),
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Image.asset(
                                    'assets/icons/calendar_black.png',
                                    color: ThemeColors.colorF9637D,
                                    scale: 2,
                                  ),
                                  SizedBox(
                                    width: 8 * w,
                                  ),
                                  formattedStartDate != formattedEndDate
                                      ? Expanded(
                                          child: Text(
                                            '${formattedStartDate ?? '01 Jan 23'} - ${formattedEndDate ?? '01 Sep 23'} • ${days ?? '1'} Day${(days ?? 1) > 1 ? 's' : ''}',
                                            style: tsS12w400c979797,
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        )
                                      : Text(
                                          '${formattedStartDate ?? '01 Jan 23'}  • ${days ?? '1'} Day${(days ?? 1) > 1 ? 's' : ''}',
                                          style: tsS12w400c979797,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      Container(
                        width: 80,
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(
                            horizontal: w * 6, vertical: h * 4),
                        margin: EdgeInsets.only(top: h * 12),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: isConfirm
                                ? const Color.fromRGBO(50, 147, 111, 0.16)
                                : const Color.fromRGBO(246, 77, 68, 0.15)),
                        child: FittedBox(
                          child: Text(
                            isConfirm ? 'Confirmed' : 'Not Confirmed',
                            style:
                                isConfirm ? tsS12w600c519C66 : tsS12w600cF64D44,
                            textAlign: TextAlign.center,
                            maxLines: 2,
                          ),
                        ),
                      ),
                    ],
                  );
                },
                separatorBuilder: (context, index) {
                  return Padding(
                    padding: EdgeInsets.only(top: 5.0 * h, bottom: 5 * h),
                    child: Divider(
                      height: 1,
                      thickness: 2,
                      color: ThemeColors.colorF9F9F9,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    });
  }
}
