import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../util/colors.dart';
import '../../../util/page_navigator.dart';
import '../../latest_updates/latest_updates_screen.dart';
import '../../latest_updates/survey_section/survey_post_card_dashboard.dart';
import '../widgets/latest_updates.dart';
import '../widgets/see_more_posts.dart';
import '../widgets/title_text_widget.dart';
import 'write_a_post_section.dart';

class UpdatesSection extends StatelessWidget {
  const UpdatesSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<UpdatesProvider>(
      builder: (context, provider, child) {
        if (provider.scrollList.isEmpty) {
          return const WriteAPostSection();
        }
        return Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const TitleTextWidget('Latest Updates'),
                InkWell(
                  onTap: () async {
                    final provider = context.read<UpdatesProvider>();
                    provider.getUpdates(context: context);

                    PageNavigator.pushSlideup(
                      context: context,
                      route: const LatestUpdatesScreen(),
                    );
                  },
                  child: Text(
                    'See All',
                    style: GoogleFonts.poppins(
                      color: ThemeColors.colorF9637D,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15.0),
            AnimatedContainer(
              duration: const Duration(seconds: 1), // Animation duration
              curve: Curves.easeInOut, // Easing curv
              height: provider.tempList.isEmpty ? 190 * h : 252 * h,
              width: 380 * w,
              child: provider.tempList.isEmpty
                  ? const SeeMorePosts()
                  : SizedBox(
                      height: 250 * h,
                      width: 380 * w,
                      child: Stack(
                        alignment: Alignment.center,
                        textDirection: TextDirection.rtl,
                        fit: StackFit.loose,
                        clipBehavior: Clip.hardEdge,
                        children: provider.tempList.map(
                          (data) {
                            int index = provider.tempList.indexOf(data);
                            if (data.type == 'post') {
                              return LatestUpdates(
                                provider: provider,
                                index: index,
                                post: data,
                                currentLength: provider.tempList.length,
                              );
                            }
                            return SurveyPostCardDashboard(
                              postModel: data,
                              index: index,
                              currentLength: provider.tempList.length,
                            );
                          },
                        ).toList(),
                      ),
                    ),
            ),
          ],
        );
      },
    );
  }
}
