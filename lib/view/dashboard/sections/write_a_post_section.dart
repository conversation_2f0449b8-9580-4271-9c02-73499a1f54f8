import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../../provider/profile_provider.dart';
import '../../../provider/updates_provider.dart';
import '../../../util/styles.dart';
import '../../latest_updates/post_new_updates_screen.dart';

class WriteAPostSection extends StatelessWidget {
  const WriteAPostSection({super.key});

  @override
  Widget build(BuildContext context) {
    final profileProvider = Provider.of<ProfileProvider>(context);
    return InkWell(
      onTap: () {
        Navigator.of(context).push(MaterialPageRoute(
            builder: (context) => const PostNewUpdatesScreen()));
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        height: 240 * h,
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(12)),
        child: Column(
          children: [
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(50),
                  child: CachedNetworkImage(
                    height: 50,
                    width: 50,
                    fit: BoxFit.fill,
                    imageUrl: profileProvider.profilePic.toString(),
                    errorWidget: (context, url, error) {
                      return Container(
                        height: 50,
                        width: 50,
                        decoration: BoxDecoration(
                          color: ThemeColors.primaryColor,
                          shape: BoxShape.circle,
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          profileProvider.firstName == null
                              ? ""
                              : profileProvider.firstName!
                                  .substring(0, 1)
                                  .toUpperCase(),
                          style: GoogleFonts.rubik(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(profileProvider.firstName ?? "",
                        style: tsS14w500Black),
                    Text(
                      profileProvider.designation ?? "",
                      style: tsS12w400979797,
                    )
                  ],
                ),
              ],
            ),
            const SizedBox(height: 5),
            Expanded(
              child: Consumer<UpdatesProvider>(builder: (context, provider, _) {
                return Form(
                  child: Column(
                    children: [
                      TextFormField(
                        readOnly: true,
                        style: GoogleFonts.poppins(
                            fontSize: 16, fontWeight: FontWeight.w400),
                        decoration: InputDecoration(
                          hintText: 'Write something...',
                          hintStyle: GoogleFonts.poppins(
                              fontSize: 16, fontWeight: FontWeight.w400),
                          border: InputBorder.none,
                        ),
                        validator: (value) {
                          return value!.isEmpty
                              ? "Type something you want to share"
                              : null;
                        },
                      ),
                    ],
                  ),
                );
              }),
            ),
            Consumer<UpdatesProvider>(builder: (context, provider, _) {
              return Row(
                children: [
                  ImageIcon(
                    const AssetImage("assets/icons/gallery.png"),
                    color: ThemeColors.color979797,
                  ),
                  const SizedBox(
                    width: 30,
                  ),
                  ImageIcon(
                    const AssetImage("assets/icons/video.png"),
                    color: ThemeColors.color979797,
                  ),
                ],
              );
            })
          ],
        ),
      ),
    );
  }
}
