import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../util/colors.dart';
import '../../../util/styles.dart';

class UpcomingLeavesCard extends StatelessWidget {
  final String? name;
  final String? profilePic;
  final String? startDate;
  final String? endDate;
  final String? leaveType;
  final String? dayType;
  const UpcomingLeavesCard(
      {super.key,
      required this.name,
      required this.profilePic,
      required this.startDate,
      required this.endDate,
      required this.leaveType,
      required this.dayType});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: h * 65,
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8)),
      child: Row(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 10),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(50),
              child: Image.network(profilePic ?? '',
                  height: h * 42, width: w * 42, fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                return Container(
                    height: h * 42,
                    width: w * 42,
                    decoration: BoxDecoration(
                      color: ThemeColors.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      name?.substring(0, 1).toUpperCase().toUpperCase() ?? '',
                      style: GoogleFonts.rubik(
                          fontSize: 22,
                          fontWeight: FontWeight.w500,
                          color: Colors.white),
                    ));
              }),
              // child: CachedNetworkImage(
              //   memCacheWidth: 100,
              //   height: h * 42,
              //   width: w * 42,
              //   fit: BoxFit.cover,
              //   imageUrl: profilePic ?? '',
              //   errorWidget: (context, url, error) {
              //     return Container(
              //       decoration: BoxDecoration(
              //         color: ThemeColors.primaryColor,
              //         shape: BoxShape.circle,
              //       ),
              //       alignment: Alignment.center,
              //       child: Text(
              //         name?.substring(0, 1).toUpperCase().toUpperCase() ?? '',
              //         style: GoogleFonts.rubik(
              //             fontSize: 22,
              //             fontWeight: FontWeight.w500,
              //             color: Colors.white),
              //       ),
              //     );
              //   },
              // ),
            ),
          ),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                name ?? '',
                style: tsS12w500Black,
              ),
              Row(
                children: [
                  Text(
                    leaveType ?? '',
                    style: GoogleFonts.poppins(
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                        color: leaveType == 'Absent'
                            ? Colors.red
                            : const Color(0xffA01BC0)),
                  ),
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 5),
                    width: 4,
                    height: 4,
                    decoration: const BoxDecoration(
                        color: Color(0xffA01BC0), shape: BoxShape.circle),
                  ),
                  if (startDate != null &&
                      endDate != null &&
                      startDate != endDate)
                    Text(
                      '${formatDateFromString(startDate!, 'dd MMM,yyyy', 'dd MMM')} - ${formatDateFromString(endDate!, 'dd MMM,yyyy', 'dd MMM')}',
                      style: GoogleFonts.poppins(
                          fontSize: 10,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xffA01BC0)),
                    )
                  else if (startDate != null)
                    Text(
                        formatDateFromString(
                            startDate!, 'dd MMM,yyyy', 'dd MMM'),
                        style: GoogleFonts.poppins(
                            fontSize: 10,
                            fontWeight: FontWeight.w400,
                            color: const Color(0xffA01BC0))),
                  if (dayType != null) ...[
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 5),
                      width: 4,
                      height: 4,
                      decoration: const BoxDecoration(
                          color: Color(0xffA01BC0), shape: BoxShape.circle),
                    ),
                    Text(dayType ?? '',
                        style: GoogleFonts.poppins(
                            fontSize: 10,
                            fontWeight: FontWeight.w400,
                            color: const Color(0xffA01BC0)))
                  ]
                ],
              )
            ],
          ))
        ],
      ),
    );
  }
}
