import 'package:e8_hr_portal/provider/log_time_provider.dart';
import 'package:e8_hr_portal/util/capitalize.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/dashboard/widgets/add_new_task_button.dart';
import 'package:e8_hr_portal/view/dashboard/widgets/task_action_button.dart';
import 'package:e8_hr_portal/view/dashboard/widgets/view_more_task_button.dart';
import 'package:e8_hr_portal/view/widgets/timer_dialoge_stop.dart';
import 'package:e8_hr_portal/view/widgets/timer_stope_dialoge.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:html/parser.dart' as html;

class CurrentTaskCard extends StatelessWidget {
  const CurrentTaskCard({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<LogTimeProvider>();

    if (provider.runningTask.id == null) {
      return const SizedBox();
    }

    String hours = provider.hours.toString().padLeft(2, '0');
    String minutes = provider.minutes.toString().padLeft(2, '0');
    String seconds = provider.seconds.toString().padLeft(2, '0');
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(w * 15),
      margin: EdgeInsets.only(bottom: h * 15),
      decoration: ShapeDecoration(
        color: const Color(0xFFFFEAEE),
        shape: RoundedRectangleBorder(
          side: const BorderSide(width: 1, color: Color(0xFFF9637D)),
          borderRadius: BorderRadius.circular(12),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x19F9637D),
            blurRadius: 10,
            offset: Offset(0, 4),
            spreadRadius: 4,
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      formatDateFromString(
                        provider.runningTask.createdAt.toString(),
                        "yyyy-MM-dd hh:mm:ss",
                        "dd MMMM yyyy",
                      ),
                      style: GoogleFonts.poppins(
                        color: const Color(0xFF666666),
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: "$hours:$minutes:$seconds",
                            style: GoogleFonts.poppins(
                              color: const Color(0xFF343434),
                              fontSize: 30,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: 'HOURS',
                            style: GoogleFonts.poppins(
                              color: const Color(0xFF343434),
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  stopButton(context),
                  const SizedBox(width: 8),
                  playPauseButton(context),
                ],
              ),
            ],
          ),
          Container(
            margin: EdgeInsets.only(top: h * 15, bottom: h * 12),
            decoration: const ShapeDecoration(
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  width: 0.50,
                  strokeAlign: BorderSide.strokeAlignCenter,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              provider.runningTask.project.toString().capitalize(),
              style: GoogleFonts.poppins(
                color: const Color(0xFF161616),
                fontSize: f * 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(height: h * 2),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              getTaskTitle(provider),
              style: GoogleFonts.poppins(
                color: const Color(0xFF666666),
                fontSize: f * 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          SizedBox(height: h * 14),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              const AddNewTaskButton(),
              if (provider.userCurrentTaskList.length > 1) ...[
                Container(
                  height: 27 * h,
                  width: .5,
                  color: const Color(0xffFFC9D2),
                ),
                const ViewMoreTaskButton(),
              ]
            ],
          ),
        ],
      ),
    );
  }

  Widget stopButton(BuildContext context) {
    return TaskActionButton(
      color: const Color(0xFFC51414),
      icon: Icons.stop_rounded,
      onTap: () => onStopButtonPressed(context),
    );
  }

  Widget playPauseButton(BuildContext context) {
    final provider = context.watch<LogTimeProvider>();
    if (provider.isTimerPaused) {
      return TaskActionButton(
        color: const Color(0xFF387FE9),
        icon: Icons.play_arrow_rounded,
        onTap: () => onPlayButtonPressed(context),
      );
    }
    return TaskActionButton(
      color: const Color(0xFF0CA726),
      icon: Icons.pause_rounded,
      onTap: () => onPauseButtonPressed(context),
    );
  }

  onStopButtonPressed(BuildContext context) {
    final provider = context.read<LogTimeProvider>();
    if (provider.runningTask.id != null) {
      showModalBottomSheet(
        context: context,
        isDismissible: true,
        backgroundColor: ThemeColors.colorFFFFFF,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        builder: (context) {
          return TimerDialogStop(
            taskId: provider.runningTask.id!,
            isFrom: true,
          );
        },
      );
    }
  }

  onPlayButtonPressed(BuildContext context) {
    final provider = context.read<LogTimeProvider>();
    if (provider.timer?.isActive == false) {
      provider.startTimer();
    }
    provider.timerPlayPauseStope(
      taskId: provider.runningTask.id!,
      statusId: 1,
      context: context,
    );
  }

  onPauseButtonPressed(BuildContext context) {
    final provider = context.read<LogTimeProvider>();
    if (provider.runningTask.id != null) {
      showDialog(
        context: context,
        builder: (context) {
          return TimerDialog(taskId: provider.runningTask.id!);
        },
      );
    }
  }

  String getTaskTitle(LogTimeProvider provider) {
    if (provider.runningTask.note != null) {
      final note = html.parse(provider.runningTask.note.toString());
      var documentElement = html.parse(note.body?.text).documentElement;
      if (documentElement != null) {
        String parsedString = documentElement.text;
        if (parsedString.isNotEmpty) {
          return "${provider.runningTask.title} - $parsedString";
        }
      }
    }
    return provider.runningTask.title ?? '';
  }
}
