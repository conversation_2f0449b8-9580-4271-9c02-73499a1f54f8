import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/model/leave_balance_model.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'navigation_button_builder.dart';

class LeavesCard extends StatelessWidget {
  const LeavesCard({super.key});

  @override
  Widget build(BuildContext context) {
    final leaveProvider = context.watch<LeaveApplicationProvider>();
    return NavigationButtonBuilder(
      bgcolor1: ThemeColors.color9D16BE,
      bgcolor2: ThemeColors.colorEFABFF,
      iconBgColor: Colors.white.withOpacity(0.10),
      icon: const ImageIcon(
        AssetImage('assets/icons/leave_applications.png'),
        color: Colors.white,
      ),
      iconBg: const ImageIcon(
        AssetImage('assets/icons/leave_application_cut.png'),
        color: Colors.white,
      ),
      title: 'Leaves',
      subtitle: subtitle(leaveProvider),
      onPressed: () async {
        final masterProvider = context.read<MasterProvider>();
        leaveProvider.currentPageLeaveRecords = 0;
        leaveProvider.pagingControllerLeaveRecords?.refresh();
        await leaveProvider.getLeaveBalance(master: false, context: context);
        masterProvider.currentIndex = 2;
      },
    );
  }

  num? getTakenLeaves(leaveProvider) {
    num? leavesTaken = leaveProvider.leaveBalanceModel?.data!
        .firstWhere((element) => element.leaveType?.id == 1,
            orElse: () => Data())
        .leavesTaken;
    return leavesTaken;
  }

  num? getTotalLeave(leaveProvider) {
    num? totalLeave = leaveProvider.leaveBalanceModel?.data!
        .firstWhere((element) => element.leaveType?.id == 1,
            orElse: () => Data())
        .totalLeave;
    return totalLeave;
  }

  String subtitle(leaveProvider) {
    num? leavesTaken = getTakenLeaves(leaveProvider);
    num? totalLeave = getTotalLeave(leaveProvider);
    if (leavesTaken != null) {
      return '$leavesTaken/$totalLeave ${leavesTaken <= 1 ? "Leave" : "Leaves"}';
    }
    return '';
  }
}
