import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../util/size_config.dart';

class NavigationButtonBuilder extends StatelessWidget {
  final Widget icon;
  final Widget iconBg;
  final String title;
  final void Function() onPressed;
  final String? subtitle;
  final Color iconBgColor;
  final Color bgcolor1;
  final Color bgcolor2;

  const NavigationButtonBuilder(
      {super.key,
      required this.iconBg,
      required this.icon,
      required this.title,
      required this.onPressed,
      required this.bgcolor1,
      required this.bgcolor2,
      required this.iconBgColor,
      this.subtitle});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Stack(
        children: [
          Card(
            margin: const EdgeInsets.only(bottom: 1),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            child: Container(
              // duration: const Duration(seconds: 1),
              // curve: Curves.bounceIn,
              width: 164 * w,
              height: 152 * h,
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    colors: [bgcolor1, bgcolor2],
                  ),
                  borderRadius: BorderRadius.circular(12)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      decoration: BoxDecoration(
                          shape: BoxShape.circle, color: iconBgColor),
                      child: icon,
                    ),
                  ),
                  const SizedBox(height: 15),
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 12 * f,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (subtitle != null)
                    Text(
                      subtitle!,
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 10 * f,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                ],
              ),
            ),
          ),
          Positioned(
            right: 0,
            bottom: 0,
            child: SizedBox(height: 65, width: 65, child: iconBg),
          ),
        ],
      ),
    );
  }
}
