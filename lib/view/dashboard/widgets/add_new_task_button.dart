// ignore_for_file: use_build_context_synchronously

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../provider/log_time_provider.dart';
import '../../../util/size_config.dart';
import '../../add_new_task/add_new_task.dart';

class AddNewTaskButton extends StatelessWidget {
  const AddNewTaskButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: BorderRadius.circular(8),
      color: const Color(0xFFFFEBEE),
      child: InkWell(
        onTap: () async {
          LogTimeProvider provider =
              Provider.of<LogTimeProvider>(context, listen: false);
          provider.selectedTaskType = null;
          provider.selectedProject = null;
          provider.selectedTask = null;
          provider.selectedStage = null;
          provider.projectList.clear();
          provider.taskList.clear();
          provider.stageList.clear();
          await provider.getTaskType();
          provider.selectedTaskType = '1';
          provider.getProjectTypes();

          Navigator.push(context,
              CupertinoPageRoute(builder: (context) => AddNewTaskScreen()));
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.all(10),
          child: Text(
            'Add New Task',
            style: TextStyle(
              color: const Color(0xFFF9637D),
              fontSize: f * 12,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
