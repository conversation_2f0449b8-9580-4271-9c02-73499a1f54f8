import 'package:e8_hr_portal/model/post_model.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class CommentSectionInLatestPostDashbord extends StatefulWidget {
  final PostModel post;
  const CommentSectionInLatestPostDashbord({super.key, required this.post});

  @override
  State<CommentSectionInLatestPostDashbord> createState() =>
      _CommentSectionInLatestPostDashbordState();
}

class _CommentSectionInLatestPostDashbordState
    extends State<CommentSectionInLatestPostDashbord> {
  @override
  Widget build(BuildContext context) {
    // PostModel post = widget.post;
    return Row(
      // mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Consumer<UpdatesProvider>(
          builder: (context, provider, child) {
            return InkWell(
              onTap: () {
                // if (widget.post.userLiked == false) {
                //   debugPrint("inside like");
                //   provider.postLikeId.add(widget.post.id);
                // } else {
                //   debugPrint("inside unlike");
                //   provider.postLikeId.remove(widget.post.id);
                // }
                provider.likePost(
                  postId: widget.post.id.toString(),
                  context: context,
                  like: widget.post.userLiked == true ? false : true,
                );
                provider.getUpdates(context: context);
              },
              child: Row(
                children: [
                  Icon(
                    widget.post.userLiked == true
                        ? Icons.favorite
                        : Icons.favorite_outline_rounded,
                    // provider.postLikeId.contains(widget.post.id)
                    //     ? Icons.favorite
                    //     : Icons.favorite_outline_rounded,
                    size: 15,
                    color: widget.post.userLiked == true
                        ? Colors.red
                        : Colors.black,
                    // color: provider.postLikeId.contains(widget.post.id)
                    // ? Colors.red
                    // : Colors.black,
                  ),
                  const SizedBox(width: 5),
                  Text(
                    '${widget.post.like ?? 0} ${(widget.post.like == 1 || widget.post.like == 0) ? "Like" : "Likes"}',
                    style: GoogleFonts.rubik(
                      fontSize: 12,
                      color: const Color(0xFF666666),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        const Spacer(),
        const ImageIcon(
          AssetImage('assets/icons/comments.png'),
          size: 15,
          color: Colors.black,
        ),
        const SizedBox(width: 5),
        Text(
          '${widget.post.comments ?? 0} ${widget.post.comments == 1 ? "Comment" : "Comments"}',
          style: GoogleFonts.rubik(
            fontSize: 12,
            color: const Color(0xFF666666),
          ),
        ),
        const Spacer(),
        GestureDetector(
          onTap: () async {
            await share(
                title: "title",
                text: widget.post.description.toString(),
                linkUrl: widget.post.postLink.toString());
          },
          child: Row(
            children: [
              const ImageIcon(
                AssetImage('assets/icons/share-icon.png'),
                size: 15,
              ),
              const SizedBox(width: 5),
              Text(
                'Share',
                style: GoogleFonts.rubik(
                  fontSize: 12,
                  color: const Color(0xFF1E2138),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }
}
