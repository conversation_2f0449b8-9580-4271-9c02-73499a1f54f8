import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/provider/team_members_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'navigation_button_builder.dart';

class EmployeeCard extends StatelessWidget {
  const EmployeeCard({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<TeamMembersProvider>();
    String subtitle = '${provider.newMembersCount ?? 0} New Members';
    if (provider.newMembersCount == 1) {
      subtitle = '${provider.newMembersCount ?? 0} New Member';
    }
    return NavigationButtonBuilder(
      bgcolor1: ThemeColors.color0048A5,
      bgcolor2: ThemeColors.color4A8FE7,
      iconBgColor: Colors.white.withOpacity(0.10),
      iconBg: const ImageIcon(
        AssetImage('assets/icons/member_cut.png'),
        color: Colors.white,
      ),
      icon: const ImageIcon(
        AssetImage('assets/icons/members.png'),
        color: Colors.white,
      ),
      title: 'Employees (${provider.totalCount ?? 0})',
      subtitle: subtitle,
      onPressed: () => onEmployeeCardPressed(context),
    );
  }

  onEmployeeCardPressed(BuildContext context) {
    final masterProvider = context.read<MasterProvider>();
    final provider = context.read<TeamMembersProvider>();
    provider.getTeamMembers(
      isFilter: false,
      master: false,
      context: context,
    );
    provider.clearSelectedFilter();
    masterProvider.currentIndex = 1;
  }
}
