import 'package:dotted_border/dotted_border.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/latest_updates/latest_updates_screen.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class SeeMorePosts extends StatelessWidget {
  const SeeMorePosts({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: InkWell(
        onTap: () async {
          context.read<UpdatesProvider>().getUpdates(context: context);

          PageNavigator.pushSlideup(
            context: context,
            route: const LatestUpdatesScreen(),
          );
        },
        child: DottedBorder(
          dashPattern: const [5, 5, 5, 5],
          borderType: BorderType.RRect,
          color: ThemeColors.colorD7D7D7,
          strokeWidth: 1,
          radius: Radius.circular(15 * h),
          child: Container(
            height: 188 * h,
            width: 350 * w,
            padding: EdgeInsets.only(top: 30 * h),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12 * h),
              boxShadow: const [
                BoxShadow(
                  offset: Offset(1, 1),
                  color: Color.fromRGBO(0, 0, 0, 0.10),
                  blurRadius: 10,
                ),
              ],
            ),
            child: Column(
              children: [
                Image.asset(
                  "assets/icons/no_post_grey.png",
                  height: 52 * h,
                  width: 52 * w,
                ),
                SizedBox(
                  height: 10 * h,
                ),
                Text(
                  "Explore more posts",
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                    color: ThemeColors.color161616,
                  ),
                ),
                Text(
                  "let's explore more from our posts.",
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: ThemeColors.color9F9F9F,
                  ),
                ),
                SizedBox(height: 10 * h),
                Text(
                  "Click to explore",
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: ThemeColors.colorF9637D,
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
