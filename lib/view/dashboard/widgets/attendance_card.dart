import 'package:e8_hr_portal/provider/attendance_provider.dart';
import 'package:e8_hr_portal/provider/ble_attendance_provider.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/attendance/attendance_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';

import 'navigation_button_builder.dart';

class AttendanceCard extends StatelessWidget {
  final String? attendance;
  const AttendanceCard({this.attendance, super.key});

  @override
  Widget build(BuildContext context) {
    return NavigationButtonBuilder(
      bgcolor2: ThemeColors.colorFB4D3D,
      bgcolor1: ThemeColors.colorCD1000,
      iconBgColor: Colors.white.withOpacity(0.10),
      icon: const ImageIcon(
        AssetImage('assets/icons/attendance.png'),
        color: Colors.white,
      ),
      title: 'Attendance',
      subtitle: attendance != null ? '$attendance On time ' : null,
      iconBg: const ImageIcon(
        AssetImage('assets/icons/attendance2.png'),
        color: Colors.white,
      ),
      onPressed: () => onAttendancePressed(context),
    );
  }

  onAttendancePressed(BuildContext context) async {
    final profileProvider = context.read<ProfileProvider>();
    final attendanceProvider = context.read<AttendanceProvider>();
    final bleAttendanceProvider = context.read<BLEAttendanceProvider>();
    profileProvider.getProfileData(context: context);
    attendanceProvider.selectedMonth = DateTime.now();
    attendanceProvider.getAttendanceList(master: false, context: context);
    EasyLoading.show();
    await bleAttendanceProvider.getAttendanceLogs();
    EasyLoading.dismiss();
    if (context.mounted) {
      PageNavigator.pushSlideup(
        context: context,
        route: const AttendanceScreen(),
      );
    }
  }
}
