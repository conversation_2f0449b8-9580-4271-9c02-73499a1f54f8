// ignore_for_file: use_build_context_synchronously

import 'package:e8_hr_portal/model/post_model.dart';
import 'package:e8_hr_portal/view/dashboard/widgets/comment_section.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/latest_updates/edit_update_screen.dart';
import 'package:e8_hr_portal/view/latest_updates/update_detailed_screen.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';

class LatestUpdates extends StatefulWidget {
  final int? index;
  final UpdatesProvider provider;
  final int currentLength;
  final PostModel post;
  const LatestUpdates({
    super.key,
    required this.index,
    required this.provider,
    required this.currentLength,
    required this.post,
  });

  @override
  State<LatestUpdates> createState() => _LatestUpdatesState();
}

class _LatestUpdatesState extends State<LatestUpdates> {
  VideoPlayerController? videoPlayerController;
  String? uint8list;

  @override
  void initState() {
    super.initState();
  }

  // thumbnails(List<PostFile>? thumpnail) async {
  //   uint8list = await VideoThumbnail.thumbnailFile(
  //     video: thumpnail!.first.file!,
  //     // video:
  //     //     "https://flutter.github.io/assets-for-api-docs/assets/videos/butterfly.mp4",
  //     thumbnailPath: (await getTemporaryDirectory()).path,
  //     imageFormat: ImageFormat.PNG,
  //     maxHeight:
  //         100, //specify the height of the thumbnail, let the width auto-scaled to keep the source aspect ratio
  //     maxWidth: 360,
  //     quality: 100,
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    PostModel post = widget.post;
    double height = 242 * h;
    double width = 350 * w;

    if (widget.currentLength == 3) {
      switch (widget.index) {
        case 0:
          height = 232 * h;
          width = 350 * w;
          break;
        case 1:
          height = 242 * h;
          width = 335 * w;
          break;
        default:
          height = 262 * h;
          width = 320 * w;
          break;
      }
    } else if (widget.currentLength == 2) {
      switch (widget.index) {
        case 0:
          height = 242 * h;
          width = 350 * w;
          break;
        default:
          height = 262 * h;
          width = 335 * w;
          break;
      }
    } else if (widget.currentLength == 1) {
      switch (widget.index) {
        default:
          height = 262 * h;
          width = 350 * w;
          break;
      }
    }
    return Consumer<UpdatesProvider>(builder: (context, provid, child) {
      return InkWell(
        onDoubleTap: () {
          provid.likePost(
            postId: post.id.toString(),
            context: context,
            like: post.userLiked == true ? false : true,
          );
          provid.getUpdates(context: context);
        },
        onTap: () async {
          EasyLoading.show();
          var pvd = Provider.of<UpdatesProvider>(context, listen: false);
          await pvd.getPostDetails(postId: post.id!.toInt());
          await pvd.getComments(postId: post.id!.toInt());
          EasyLoading.dismiss();
          if (!mounted) return;
          PageNavigator.pushSlideup(
            context: context,
            route: UpdateDetailedScreen(
              color: Colors.white,
              post: post,
            ),
          );
        },
        child: Dismissible(
          key: GlobalKey(),
          onDismissed: (direction) {
            widget.provider.removeListScroll(index: widget.index!);
          },
          direction: DismissDirection.endToStart,
          child: Align(
            alignment: Alignment.centerLeft,
            child: AnimatedContainer(
              duration: const Duration(seconds: 2),
              curve: Curves.fastOutSlowIn,
              height: height,
              width: width,
              padding: const EdgeInsets.fromLTRB(15, 13, 15, 18),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: const [
                  BoxShadow(
                    offset: Offset(1, 1),
                    color: Color.fromRGBO(0, 0, 0, 0.10),
                    blurRadius: 10,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  profileViewPost(),
                  SizedBox(
                    height: ((post.file != null) && (post.file!.isNotEmpty)
                        ? 29 * h
                        : post.description.toString().length >= 80
                            ? 90 * h
                            : 120 * h),
                    child: Html(
                      data: post.description,
                      shrinkWrap: true,
                    ),
                  ),
                  if (post.description.toString().length >= 80)
                    Text(
                      "see more",
                      style: ts12second.copyWith(
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                    ),
                  if (post.file != null &&
                      post.file!.isNotEmpty &&
                      post.file!.first.fileType == "video")
                    videoSection(),
                  if (post.file != null &&
                      post.file!.isNotEmpty &&
                      post.file!.first.fileType == "image")
                    imageSection(),
                  const Spacer(),
                  const Divider(
                    color: Color(0xFFF3F3F3),
                    thickness: 1,
                    height: 0,
                  ),
                  const SizedBox(height: 8),
                  CommentSectionInLatestPostDashbord(
                    post: post,
                  )
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget videoSection() {
    return SizedBox(
      height: 105 * h,
      width: 360 * w,
      child: Stack(
        children: [
          SizedBox(
            height: 110 * h,
            width: 360 * w,
            child: Image.network(
              widget.post.file!.first.thumbnail.toString(),
              // fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return const SizedBox();
              },
            ),
          ),
          const Center(
            child: Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 30,
            ),
          ),
        ],
      ),
    );
  }

  Widget imageSection() {
    return SizedBox(
      height: 105 * h,
      width: 360 * w,
      child: FittedBox(
        clipBehavior: Clip.antiAlias,
        fit: BoxFit.cover,
        // alignment: Alignment.center,
        child: Image.network(
          widget.post.file!.first.file.toString(),
          // fit: BoxFit.cover,
          height: 110 * h,
          width: 360 * w,
          errorBuilder: (context, error, stackTrace) {
            return const SizedBox();
          },
        ),
      ),
    );
  }

  Widget profileViewPost() {
    PostModel post = widget.post;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        SizedBox(
          child: Row(
            children: [
              Container(
                height: 35 * h,
                width: 35 * w,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  shape: BoxShape.circle,
                  image: post.profilePhoto != null
                      ? DecorationImage(
                          image: NetworkImage(post.profilePhoto.toString()),
                          fit: BoxFit.cover,
                        )
                      : null,
                ),
                child: post.profilePhoto == null
                    ? Center(
                        child: Text(
                          post.name!.substring(0, 1).toUpperCase(),
                          style: tsS14FFFFF,
                        ),
                      )
                    : null,
              ),
              SizedBox(
                width: 5 * w,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Align(
                    alignment: Alignment.topLeft,
                    child: Text(
                      post.name ?? '',
                      style: tsS14w500Black,
                    ),
                  ),
                  SizedBox(
                    height: 1 * h,
                  ),
                  Row(
                    children: [
                      Align(
                        alignment: Alignment.topLeft,
                        child: Text(
                          post.designation ?? '',
                          style: tsS12Normalf66666,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 5 * w),
                        height: 5,
                        width: 5,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: ThemeColors.color808080,
                        ),
                      ),
                      Text(
                        post.createdAt.toString(),
                        style: GoogleFonts.rubik(
                          color: ThemeColors.color808080,
                          fontSize: 12,
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                    ],
                  ),
                ],
              )
            ],
          ),
        ),
        if (post.isOwner != null && post.isOwner!)
          PopupMenuButton(
            icon: Icon(
              Icons.more_vert,
              color: ThemeColors.color979797,
            ),
            onSelected: (value) async {
              switch (value) {
                case 'delete':
                  showDialog(
                      context: context,
                      builder: (ctxt) {
                        return AlertDialog(
                          title: const Text('Confirm delete'),
                          content:
                              const Text('Are you sure you want to delete?'),
                          actions: [
                            ElevatedButton(
                              onPressed: () => Navigator.pop(context),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey[300],
                              ),
                              child: const Text(
                                'Cancel',
                                style: TextStyle(color: Colors.black),
                              ),
                            ),
                            Consumer<UpdatesProvider>(
                              builder: (context, provider, child) {
                                return ElevatedButton(
                                  onPressed: () {
                                    provider.deleteUpdate(
                                      postId: post.id.toString(),
                                      context: context,
                                    );
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: ThemeColors.secondaryColor,
                                  ),
                                  child: const Text('Delete'),
                                );
                              },
                            )
                          ],
                        );
                      });
                  break;
                case 'edit':
                  showDialog(
                      context: context,
                      builder: (ctxt) {
                        return AlertDialog(
                          title: const Text('Confirm edit'),
                          content:
                              const Text('Are you sure want to edit the post?'),
                          actions: [
                            ElevatedButton(
                              onPressed: () => Navigator.pop(context),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey[300],
                              ),
                              child: const Text(
                                'Cancel',
                                style: TextStyle(color: Colors.black),
                              ),
                            ),
                            Consumer<UpdatesProvider>(
                              builder: (context, provider, child) {
                                return ElevatedButton(
                                  onPressed: () async {
                                    Navigator.pop(context);
                                    provider.selectedPostsImages.clear();
                                    provider.removedImageIdList.clear();
                                    provider.selectedPostsVideo == null;
                                    provider.alreadyPostImageList?.clear();
                                    provider.isVideoRemoved = false;
                                    provider.getUserPolicies();
                                    await Navigator.of(context)
                                        .push(MaterialPageRoute(
                                      builder: (context) => EditUpdatesScreen(
                                          postId: post.id?.toInt(),
                                          file: post.file,
                                          content: post.description.toString()),
                                    ));
                                    if (!mounted) return;
                                    provider.getUpdates(context: context);
                                    provider.getUserPolicies();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: ThemeColors.secondaryColor,
                                  ),
                                  child: const Text('Edit'),
                                );
                              },
                            )
                          ],
                        );
                      });
                  break;
              }
            },
            itemBuilder: (context) {
              return [
                const PopupMenuItem(
                  value: 'edit',
                  child: Text('Edit'),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Text('Delete'),
                ),
              ];
            },
          ),
      ],
    );
  }
}
