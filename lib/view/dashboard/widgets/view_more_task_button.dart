import 'package:e8_hr_portal/provider/log_time_provider.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/task_management/task_management.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class ViewMoreTaskButton extends StatelessWidget {
  const ViewMoreTaskButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: BorderRadius.circular(8),
      color: const Color(0xFFFFEBEE),
      child: InkWell(
        onTap: () => onViewMoreButtonPressed(context),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.all(10),
          child: Text(
            'View More',
            style: TextStyle(
              color: const Color(0xFFF9637D),
              fontSize: f * 12,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  onViewMoreButtonPressed(BuildContext context) async {
    final logTimeProvider = context.read<LogTimeProvider>();
    if (!logTimeProvider.isLoading) {
      bool isGranded = await logTimeProvider.getTotalWorkingHours();
      await logTimeProvider.getUserTimeLogList(master: false);
      if (context.mounted && isGranded) {
        Navigator.push(
          context,
          CupertinoPageRoute(
            builder: (context) => const TaskManagementScreen(),
          ),
        );
      } else {
        if (!context.mounted) return;
        showSnackBarMessage(context: context, msg: 'Something went wrong');
      }
    }
  }
}
