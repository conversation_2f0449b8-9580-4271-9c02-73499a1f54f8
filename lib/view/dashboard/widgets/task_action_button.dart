import 'package:flutter/material.dart';

class TaskActionButton extends StatelessWidget {
  final Color? color;
  final void Function()? onTap;
  final IconData? icon;
  const TaskActionButton({this.color, this.onTap, this.icon, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 48.0,
      height: 48.0,
      decoration: BoxDecoration(
        boxShadow: const [
          BoxShadow(
            color: Colors.white10,
            offset: Offset(0.0, 0.0),
            blurRadius: 1.0,
          ),
        ],
        color: color,
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 3),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          customBorder: const CircleBorder(),
          onTap: onTap,
          child: Icon(
            icon,
            color: Colors.white,
            size: 30,
          ),
        ),
      ),
    );
  }
}
