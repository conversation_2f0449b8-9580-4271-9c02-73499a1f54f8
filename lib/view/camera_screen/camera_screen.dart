import 'dart:developer';

import 'package:camera/camera.dart';
import 'package:e8_hr_portal/main.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../provider/camera_provider.dart';

class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  late CameraProvider _provider;
  @override
  void initState() {
    super.initState();
    _provider = context.read<CameraProvider>();
    log('cameras.length -- ${cameras.length}');
    if (cameras.length >= 2) {
      _provider.selectedCameraIndex = 1;
    } else if (cameras.length == 1) {
      _provider.selectedCameraIndex = 0;
    } else {}
    _provider.initializeCamera();
  }

  @override
  void dispose() {
    // Don't dispose controller here anymore since Provider manages it
    super.dispose();
    _provider.controller?.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CameraProvider>(
      builder: (context, provider, _) {
        if (!provider.isInitialized || provider.controller == null) {
          return const Center(child: CircularProgressIndicator());
        }

        return Scaffold(
          backgroundColor: Color(0xFF000000),
          body: Stack(
            children: [
              // Camera Preview
              Center(
                child: CameraPreview(provider.controller!),
              ),

              // Take Picture Button
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: FloatingActionButton(
                    onPressed: () async {
                      final navigator = Navigator.of(context);
                      await provider.takePicture();
                      if (provider.image != null) {
                        navigator.pop();
                      }
                    },
                    child: const Icon(Icons.camera),
                  ),
                ),
              ),
              Positioned(
                top: 80,
                left: 20,
                child: InkWell(
                  onTap: () => Navigator.pop(context),
                  child: Icon(
                    Icons.arrow_back_ios,
                    color: Colors.white,
                  ),
                ),
              ),

              // Switch Camera Button
              Align(
                alignment: Alignment.bottomRight,
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: FloatingActionButton(
                    onPressed: () async => await provider.switchCamera(),
                    child: const Icon(Icons.flip_camera_android),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
