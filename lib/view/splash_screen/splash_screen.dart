import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/provider/theme_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/fcm.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/theme_type.dart';
import 'package:e8_hr_portal/view/intro/intro_screen.dart';
import 'package:e8_hr_portal/view/master/master_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});
  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    PaintingBinding.instance.imageCache.clear();
    LoginModel.getUserDetails();
    checkLoginStatus();
    super.initState();
  }

  Future<void> checkLoginStatus() async {
    final masterProvider = context.read<MasterProvider>();
    await Future.delayed(const Duration(seconds: 3));
    SharedPreferences prefs = await SharedPreferences.getInstance();
    bool? logged = prefs.getBool("isLoggedIn");
    if (logged == true) {
      setUserPreferredTheme();
      checkFCMToken(prefs.getString("fcmToken"));
      if (!mounted) return;
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const MasterScreen(),
        ),
      );
    } else {
      await masterProvider.getSplashScreen();
      if (!mounted) return;
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const IntroScreen(),
        ),
      );
    }
  }

  checkFCMToken(String? oldToken) async {
    final masterProvider = context.read<MasterProvider>();
    String? token = await FCM.generateToken();
    if (oldToken != token) {
      await masterProvider.tokenUpdate();
    }
  }

  setUserPreferredTheme() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int selectedTheme = prefs.getInt('theme') ?? 0;
    if (!mounted) return;
    final themeProvider = context.read<ThemeProvider>();
    themeProvider.selectedTheme = ThemeType.values[selectedTheme];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [ThemeColors.colorFCC500, ThemeColors.colorFCD900],
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Padding(
          padding: EdgeInsets.only(left: 81.0 * h, right: 81 * w, top: 170 * h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                height: 383 * h,
                width: 214 * w,
                child: Image.asset(
                  "assets/images/e8_splash_logo.png",
                  fit: BoxFit.fill,
                ),
              ),
              SizedBox(height: 31 * h),
              SizedBox(
                child: Image.asset(
                  "assets/images/e8_hrportal_name.png",
                  fit: BoxFit.fitWidth,
                  width: 215 * w,
                  height: 53 * h,
                ),
              ),
              Container(
                height: 27 * h,
                width: 114 * w,
                margin: EdgeInsets.only(
                  left: 12 * w,
                  right: 12 * w,
                  top: 90 * h,
                ),
                alignment: Alignment.center,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Image.asset("assets/icons/logo_moon.png"),
                    Image.asset("assets/icons/element8-logo.png"),
                    Image.asset("assets/icons/nuox-bx.png"),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
