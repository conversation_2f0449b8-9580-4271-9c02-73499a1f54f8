import 'package:e8_hr_portal/model/get_notification_model.dart';
import 'package:e8_hr_portal/provider/notification_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/material.dart';

class NotificationRequestTile extends StatefulWidget {
  final GetNotificationModel notification;
  final NotificationProvider provider;
  const NotificationRequestTile(
      {super.key, required this.notification, required this.provider});

  @override
  State<NotificationRequestTile> createState() =>
      _NotificationRequestTileState();
}

class _NotificationRequestTileState extends State<NotificationRequestTile>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    String? message = widget.notification.message;
    // bool isRead = ((widget.notification.read) || (widget.provider.notificationRead.contains(widget.notification.id)) ?? false);
    bool isRead =
        widget.provider.notificationRead.contains(widget.notification.id);
    String? image = widget.notification.userDetails?.profilePic;
    return Container(
      width: w * 343,
      padding: EdgeInsets.fromLTRB(w * 10, h * 18, w * 10, h * 17),
      decoration: BoxDecoration(
        color: !isRead ? ThemeColors.colorE3E3E3 : ThemeColors.colorFFFFFF,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          if (image != null)
            Container(
              height: h * 45,
              width: w * 45,
              alignment: Alignment.center,
              padding: const EdgeInsets.all(13),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ThemeColors.colorF8F8F8,
                image: DecorationImage(
                    image: NetworkImage(image), fit: BoxFit.cover),
              ),
            )
          else
            Container(
              height: h * 45,
              width: w * 45,
              alignment: Alignment.center,
              padding: const EdgeInsets.all(13),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ThemeColors.colorF8F8F8,
              ),
              child: Image.asset(
                message!.toLowerCase().contains("certificate") ||
                        message.toLowerCase().contains("noc") ||
                        message.toLowerCase().contains("letter")
                    ? 'assets/icons/recent/certificate.png'
                    : message.toLowerCase().contains("sick")
                        ? "assets/images/recent_activities.png"
                        : 'assets/icons/notification.png',
                color: ThemeColors.primaryColor,
              ),
            ),
          SizedBox(width: w * 11),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  message ?? 'John doe commented on your post "Nice one"',
                  style: tsS14w400c181818,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                Text(
                  widget.notification.newDateTime.toString(),
                  style: tsS12w500c979797,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
