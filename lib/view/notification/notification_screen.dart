// ignore_for_file: use_build_context_synchronously
import 'dart:developer';

import 'package:e8_hr_portal/provider/booked_meeting_room_provider.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/view/flight_tickets/flight_ticket_details_view_new.dart';
import 'package:e8_hr_portal/view/latest_updates/update_detailed_screen.dart';
import 'package:e8_hr_portal/view/notification/widgets/notification_request_tile.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_hr_overview_model.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_overview_model.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_record_model.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_requests_model.dart';
import 'package:e8_hr_portal/view/wfh/provider/wfh_overview_provider.dart';
import 'package:e8_hr_portal/view/wfh/view/wfh_overview_screen.dart';
import 'package:e8_hr_portal/view/wfh/view/wfh_requests_overview_screen_for_reporting_person.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/get_notification_model.dart';
import 'package:e8_hr_portal/provider/notification_provider.dart';
import 'package:e8_hr_portal/provider/tickets_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/view/leave_request_for_reporting_person/leave_request_overview.dart';
import 'package:e8_hr_portal/view/other_screens/overview/screens/overview_screen.dart';
import 'package:e8_hr_portal/view/tickets_screens/tickets_overview.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import '../../provider/attendance_provider.dart';
import '../../provider/ble_attendance_provider.dart';
import '../../provider/experience_certifcate_provider.dart';
import '../../provider/flight_ticket_provider.dart';
import '../../provider/book_meeting_room_provider.dart';
import '../../provider/noc_provider.dart';
import '../../provider/profile_provider.dart';
import '../../provider/reimbursment_provider.dart';
import '../../provider/salary_certificate_provider.dart';
import '../../provider/salary_transfer_letter_provider.dart';
import '../../provider/updates_provider.dart';
import '../attendance/attendance_screen.dart';
import '../certificate_requests/experience_certificate/experience_certificate.dart';
import '../certificate_requests/non_objection_certificates/non_objection_certificates.dart';
import '../certificate_requests/salary_certificate/salary_certificate.dart';
import '../certificate_requests/salary_transfer_certificate/salary_transfer_certificate.dart';
import '../flight_tickets/flight_tickets_list_screen.dart';
import '../latest_updates/latest_updates_screen.dart';
import '../meeting_room_hisence/meeting/meeting_requests/meeting_join_requests_screen.dart';
import '../meeting_room_hisence/meeting/meeting_requests/meeting_request_screen.dart';
import '../meeting_room_hisence/meeting/meeting_room_booked_status/meeting_room_booked_status_screen.dart';
import '../reimbursment/reporting_person/reimbursement_request_details_screen_for_reporting_person.dart';
import '../reimbursment/user/reimbursment_details_screen.dart';
import 'no_item_widget.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  late NotificationProvider notificationProvider;
  @override
  void initState() {
    notificationProvider =
        Provider.of<NotificationProvider>(context, listen: false);
    notificationProvider.init();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    String? notificationDate;
    // String? notiDateStatus;

    return Consumer<NotificationProvider>(
      builder: (context, provider, child) {
        return CustomScaffold(
            screenTitle: 'Notifications',
            actions:
                provider.showClearButton == true ? [_clearAllButton()] : [],
            body: Column(
              children: [
                Expanded(
                  child: PagedListView.separated(
                    physics: const BouncingScrollPhysics(),
                    shrinkWrap: true,
                    padding: EdgeInsets.only(top: h * 15, bottom: h * 60),
                    pagingController: provider.pagingController,
                    builderDelegate:
                        PagedChildBuilderDelegate<GetNotificationModel>(
                      noItemsFoundIndicatorBuilder: (_) {
                        provider.showClearButton = false;
                        return const NoItemWidget(
                          iconImage: 'assets/images/notificationEmpty.png',
                          text1: 'No Notifications Yet',
                          text2: 'You have no notifications right now.',
                          text3: 'Comeback later',
                        );
                      },
                      firstPageProgressIndicatorBuilder: (_) {
                        return Center(
                          child: CircularProgressIndicator(
                              color: ThemeColors.primaryColor),
                        );
                      },
                      itemBuilder: (context, notify, index) {
                        String? createdAt = notify.createdAt;

                        DateTime date1 = DateTime.parse(createdAt!);
                        String date = formatDateFromDate(
                            dateTime: date1, format: 'dd MMM yyyy');
                        // String date = formatDateFromString(
                        //     date1!, 'yyyy-MM-ddThh:mm:ss', 'dd MMM yyyy');
                        DateTime now = DateTime.now();
                        String today = formatDateFromDate(
                            dateTime: DateTime(now.year, now.month, now.day),
                            format: 'dd MMM yyyy');
                        provider.showDate = false;

                        if (notificationDate != date) {
                          notificationDate = date;
                          provider.showDate = true;

                          // notiDateStatus = notificationDate;
                          // if (date == dates) {
                          //   notiDateStatus = 'New';
                          // }
                        }

                        return Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (provider.showDate == true)
                              Padding(
                                padding: EdgeInsets.only(bottom: h * 15),
                                child: Text(
                                  notificationDate == today
                                      ? 'New'
                                      : notificationDate.toString(),
                                  style: tsS18w500c181818,
                                ),
                              ),
                            InkWell(
                                onTap: () async {
                                  String? action = notify.info?.action;
                                  int? actionId = notify.info?.actionId;
                                  // action = 'wfh_request';
                                  // actionId = 1572;
                                  log('message - ${action} - $actionId');
                                  if (notify.id != null) {
                                    provider.notificationRead.add(notify.id);
                                  }

                                  provider.readNotification(
                                      id: notify.id.toString());
                                  // if (action == 'leave' && actionId != null) {
                                  //   final provid =
                                  //       Provider.of<LeaveApplicationProvider>(
                                  //           context,
                                  //           listen: false);

                                  //   await provid.getLeaveOverView(
                                  //       leaveId: notify.info?.actionId);
                                  //   await provid.getReportingPerson();

                                  //   PageNavigator.push(
                                  //       context: context,
                                  //       route: OverViewScreen());
                                  // } else
                                  if (action == 'ticket_status' &&
                                      actionId != null) {
                                    var pro = Provider.of<TicketsProvider>(
                                        context,
                                        listen: false);
                                    pro.ticketOverview = null;
                                    await pro.getTicketOverview(
                                        ticketID: actionId);
                                    if (pro.ticketOverview != null) {
                                      PageNavigator.push(
                                          context: context,
                                          route: const TicketsOverview());
                                    }
                                  } else if (action == 'leave_status_user' &&
                                      actionId != null) {
                                    final provider =
                                        Provider.of<LeaveApplicationProvider>(
                                            context,
                                            listen: false);

                                    EasyLoading.show();
                                    bool isGo = await provider.getLeaveOverView(
                                        leaveId: actionId);
                                    EasyLoading.dismiss();
                                    if (isGo) {
                                      PageNavigator.push(
                                        context: context,
                                        route: const OverViewScreen(),
                                      );
                                    }
                                  } else if (action == 'leave_request' &&
                                      actionId != null) {
                                    final provider =
                                        Provider.of<LeaveApplicationProvider>(
                                            context,
                                            listen: false);

                                    EasyLoading.show();
                                    bool isGo = await provider
                                        .getRequestedLeaveOverviewForReportedPerson(
                                            leaveId: actionId);
                                    EasyLoading.dismiss();
                                    if (isGo) {
                                      PageNavigator.push(
                                        context: context,
                                        route: const LeaveRequestOverView(),
                                      );
                                    }
                                  } else if (action == 'salary_certificates') {
                                    Provider.of<SalaryCertificateProvider>(
                                            context,
                                            listen: false)
                                        .getSalaryCertificate();
                                    Navigator.of(context).push(
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                const SalaryCertificate()));
                                  } else if (action ==
                                      'salary_transfer_letter') {
                                    SalaryTransferLetterProvider provider =
                                        Provider.of<
                                                SalaryTransferLetterProvider>(
                                            context,
                                            listen: false);
                                    provider.fetchPreviousRequests();
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            const SalaryTransferCertificate(),
                                      ),
                                    );
                                  } else if (action ==
                                      'experience_certificates') {
                                    var provider = Provider.of<
                                            ExperienceCertifcateProvider>(
                                        context,
                                        listen: false);

                                    provider.fetchPreviousRequests();

                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                          builder: (context) =>
                                              const ExperienceCertificate()),
                                    );
                                  } else if (action ==
                                      'nonobjection_certificates') {
                                    Provider.of<FlightTicketProvider>(context,
                                            listen: false)
                                        .getFlightTicketPersonalInfo();
                                    final navigator = Navigator.of(context);
                                    NocProvider provider =
                                        Provider.of<NocProvider>(context,
                                            listen: false);

                                    provider.getNocCertificateList();

                                    navigator.push(MaterialPageRoute(
                                        builder: (context) =>
                                            const NonObjectionCertificates()));
                                  } else if (action == 'flight_tickets') {
                                    var provider =
                                        Provider.of<FlightTicketProvider>(
                                            context,
                                            listen: false);
                                    provider.getAllCountries();
                                    // bool isGo =
                                    //     await provider.getFlightTicketList();
                                    provider.currentPage = 0;
                                    provider.pagingController?.refresh();
                                    EasyLoading.show();

                                    EasyLoading.dismiss();
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            const FlightTicketsListScreen(),
                                      ),
                                    );
                                  } else if (action == 'attendance_request') {
                                    Provider.of<ProfileProvider>(context,
                                            listen: false)
                                        .getProfileData(context: context);
                                    var provider =
                                        context.read<AttendanceProvider>();
                                    provider.selectedMonth = DateTime.now();
                                    provider.getAttendanceList(
                                        master: false, context: context);
                                    EasyLoading.show();
                                    BLEAttendanceProvider provider1 =
                                        Provider.of<BLEAttendanceProvider>(
                                            context,
                                            listen: false);
                                    await provider1.getAttendanceLogs();
                                    EasyLoading.dismiss();
                                    PageNavigator.push(
                                      context: context,
                                      route: const AttendanceScreen(),
                                    );
                                  } else if (action == 'post' &&
                                      actionId != null) {
                                    final provider =
                                        Provider.of<UpdatesProvider>(context,
                                            listen: false);
                                    await provider.getPostDetails(
                                        postId: actionId.toInt());
                                    await provider.getComments(
                                        postId: actionId.toInt());
                                    if (provider.postList.isNotEmpty) {
                                      var post = provider.postList.first;
                                      PageNavigator.push(
                                        context: context,
                                        route: UpdateDetailedScreen(
                                          color: Colors.white,
                                          post: post,
                                        ),
                                      );
                                    }
                                  } else if (action ==
                                      'meeting_room_invitation') {
                                    BookedMeetingRoomProvider provider =
                                        Provider.of<BookedMeetingRoomProvider>(
                                            context,
                                            listen: false);
                                    provider.getMeetingInviteRequestForMember();
                                    PageNavigator.push(
                                      context: context,
                                      route: const MeetingJoinRequestsScreen(),
                                    );
                                  } else if (action == 'survey') {
                                    UpdatesProvider provider =
                                        Provider.of<UpdatesProvider>(context,
                                            listen: false);
                                    provider.getUpdates(context: context);

                                    PageNavigator.pushSlideup(
                                      context: context,
                                      route: const LatestUpdatesScreen(),
                                    );
                                  } else if (action == 'meeting_room_request' ||
                                      action == 'meeting_room_cancel') {
                                    BookMeetingRoomProvider provider1 =
                                        Provider.of<BookMeetingRoomProvider>(
                                            context,
                                            listen: false);
                                    provider1.getTeamMembers(context: context);
                                    BookedMeetingRoomProvider provider =
                                        Provider.of<BookedMeetingRoomProvider>(
                                            context,
                                            listen: false);
                                    provider.getMeetingRoomRequestForAdmin(
                                        status: '1');
                                    provider.currentIndex = 0;
                                    PageNavigator.push(
                                      context: context,
                                      route: const MeetingRequestsScreen(),
                                    );
                                  } else if (action == 'meeting_room') {
                                    BookMeetingRoomProvider provider1 =
                                        Provider.of<BookMeetingRoomProvider>(
                                            context,
                                            listen: false);
                                    provider1.getTeamMembers(context: context);
                                    BookedMeetingRoomProvider provider =
                                        Provider.of<BookedMeetingRoomProvider>(
                                            context,
                                            listen: false);
                                    provider.getBookedMeetingRoomStatus();
                                    PageNavigator.push(
                                      context: context,
                                      route: MeetingRoomBookedStatusScreen(),
                                    );
                                  } else if (action ==
                                      'flight_tkt_upload_req') {
                                    var flightTicketProvider =
                                        context.read<FlightTicketProvider>();
                                    if (isRedundentClick(DateTime.now())) {
                                      return;
                                    }
                                    // provider.getFlightTicketOverview();
                                    await flightTicketProvider
                                        .getFlightTicketOverview(
                                            ticketId: actionId.toString());

                                    bool isGo = await flightTicketProvider
                                        .getFlightTicketPersonalInfo(
                                      userId: flightTicketProvider
                                          .flightTicketOverviewModel
                                          ?.data
                                          ?.userId,
                                    );

                                    await flightTicketProvider
                                        .getFlightTicketLeveList();
                                    await flightTicketProvider
                                        .getFlightTicketCount(
                                            userId: flightTicketProvider
                                                .flightTicketOverviewModel
                                                ?.data
                                                ?.userId);
                                    // provider.selectedFlightTicketLeaveModel = '21'
                                    if (isGo) {
                                      if (!context.mounted) return;
                                      PageNavigator.push(
                                        context: context,
                                        route: FlightTicketDetailsViewNew(
                                            isForEdit: true,
                                            isFromLevels: true,
                                            flightTicketOverviewModel:
                                                flightTicketProvider
                                                    .flightTicketOverviewModel),
                                      );
                                    }
                                  } else if (action ==
                                      'flight_ticket_request') {
                                    if (isRedundentClick(DateTime.now())) {
                                      return;
                                    }
                                    var flightTicketProvider =
                                        context.read<FlightTicketProvider>();
                                    // provider.getFlightTicketOverview();
                                    await flightTicketProvider
                                        .getFlightTicketOverview(
                                            ticketId: actionId.toString());

                                    bool isGo = await flightTicketProvider
                                        .getFlightTicketPersonalInfo(
                                      userId: flightTicketProvider
                                          .flightTicketOverviewModel
                                          ?.data
                                          ?.userId,
                                    );

                                    await flightTicketProvider
                                        .getFlightTicketLeveList();
                                    await flightTicketProvider
                                        .getFlightTicketCount(
                                            userId: flightTicketProvider
                                                .flightTicketOverviewModel
                                                ?.data
                                                ?.userId);

                                    // provider.selectedFlightTicketLeaveModel = '21'
                                    if (isGo) {
                                      if (!context.mounted) return;
                                      PageNavigator.push(
                                        context: context,
                                        route: FlightTicketDetailsViewNew(
                                            isForEdit: true,
                                            isFromLevels: true,
                                            flightTicketOverviewModel:
                                                flightTicketProvider
                                                    .flightTicketOverviewModel),
                                      );
                                    }
                                  } else if (action == 'flight_tkt_approval') {
                                    if (isRedundentClick(DateTime.now())) {
                                      return;
                                    }
                                    var flightTicketProvider =
                                        context.read<FlightTicketProvider>();
                                    // provider.getFlightTicketOverview();
                                    await flightTicketProvider
                                        .getFlightTicketOverview(
                                            ticketId: actionId.toString());

                                    bool isGo = await flightTicketProvider
                                        .getFlightTicketPersonalInfo(
                                      userId: flightTicketProvider
                                          .flightTicketOverviewModel
                                          ?.data
                                          ?.userId,
                                    );

                                    await flightTicketProvider
                                        .getFlightTicketLeveList();
                                    await flightTicketProvider
                                        .getFlightTicketCount(
                                            userId: flightTicketProvider
                                                .flightTicketOverviewModel
                                                ?.data
                                                ?.userId);

                                    // provider.selectedFlightTicketLeaveModel = '21'
                                    if (isGo) {
                                      if (!context.mounted) return;
                                      PageNavigator.push(
                                        context: context,
                                        route: FlightTicketDetailsViewNew(
                                            isForEdit: true,
                                            isFromLevels: true,
                                            flightTicketOverviewModel:
                                                flightTicketProvider
                                                    .flightTicketOverviewModel),
                                      );
                                    }
                                  } else if (action == 'flight_tkt_reject') {
                                    if (isRedundentClick(DateTime.now())) {
                                      return;
                                    }
                                    var flightTicketProvider =
                                        context.read<FlightTicketProvider>();
                                    // provider.getFlightTicketOverview();
                                    await flightTicketProvider
                                        .getFlightTicketOverview(
                                            ticketId: actionId.toString());

                                    bool isGo = await flightTicketProvider
                                        .getFlightTicketPersonalInfo();

                                    await flightTicketProvider
                                        .getFlightTicketLeveList();
                                    await flightTicketProvider
                                        .getFlightTicketCount();

                                    // provider.selectedFlightTicketLeaveModel = '21'
                                    if (isGo) {
                                      if (!context.mounted) return;
                                      PageNavigator.push(
                                        context: context,
                                        route: FlightTicketDetailsViewNew(
                                            isForEdit: true,
                                            flightTicketOverviewModel:
                                                flightTicketProvider
                                                    .flightTicketOverviewModel),
                                      );
                                    }
                                  } else if (action == 'flight_tkt_upload') {
                                    if (isRedundentClick(DateTime.now())) {
                                      return;
                                    }
                                    var flightTicketProvider =
                                        context.read<FlightTicketProvider>();
                                    await flightTicketProvider
                                        .getFlightTicketOverview(
                                            ticketId: actionId.toString());

                                    bool isGo = await flightTicketProvider
                                        .getFlightTicketPersonalInfo();

                                    await flightTicketProvider
                                        .getFlightTicketLeveList();
                                    await flightTicketProvider
                                        .getFlightTicketCount();

                                    // provider.selectedFlightTicketLeaveModel = '21'
                                    if (isGo) {
                                      if (!context.mounted) return;
                                      PageNavigator.push(
                                        context: context,
                                        route: FlightTicketDetailsViewNew(
                                            isForEdit: true,
                                            flightTicketOverviewModel:
                                                flightTicketProvider
                                                    .flightTicketOverviewModel),
                                      );
                                    }
                                  } else if (notify.info?.type ==
                                      'reimbursement_emp_request') {
                                    ReimbursementProvider provider =
                                        context.read<ReimbursementProvider>();
                                    await provider.fetchReimbursmentDetails(
                                        id: notify.info?.actionId);
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                ReimbursementRequestDetailsScreenForReportingPerson()));
                                  } else if (notify.info?.type ==
                                          'reimbursement_emp_approved' ||
                                      notify.info?.type ==
                                          'reimbursement_emp_rejected') {
                                    ReimbursementProvider provider =
                                        context.read<ReimbursementProvider>();
                                    await provider.fetchReimbursmentDetails(
                                        id: notify.info?.actionId);
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                const ReimbursmentDetailsScreen()));
                                  } else if (action == 'checklist') {
                                    if (isRedundentClick(DateTime.now())) {
                                      return;
                                    }

                                    // var provider =
                                    //     context.read<ChecklistProvider>();
                                    // provider.checkListModel.clear();
                                    // await provider.getShedulers();
                                    // await provider.getChecklist();
                                    // PageNavigator.push(
                                    //     context: context,
                                    //     route: const ChecklistScreen());
                                  } else if (action == 'checklist_history') {
                                    if (isRedundentClick(DateTime.now())) {
                                      return;
                                    }
                                    // final provider =
                                    //     context.read<ChecklistProvider>();
                                    // EasyLoading.show();
                                    // await provider.getChecklistHistory();
                                    // PageNavigator.push(
                                    //     context: context,
                                    //     route: const ChecklistHistoryScreen());
                                  } else if (action == 'wfh_records') {
                                    final provider =
                                        context.read<WfhOverviewProvider>();
                                    EasyLoading.show();
                                    WfhOverviewModel? item = await provider
                                        .fetchOverview(actionId ?? 0);
                                    EasyLoading.dismiss();
                                    if (item != null) {
                                      PageNavigator.push(
                                          context: context,
                                          route: WfhOverviewScreen(
                                              item: WFHRecordsModel(
                                                  hrStatus: item.hrStatus,
                                                  leaveDetails:
                                                      item.leaveDetails,
                                                  reportingPersonList: item
                                                      .reportingPersonList)));
                                    }
                                  } else if (action == 'wfh_request') {
                                    final provider =
                                        context.read<WfhOverviewProvider>();
                                    EasyLoading.show();
                                    WfhHrOverviewModel? item = await provider
                                        .fetchWfhReportingPersonOverview(
                                            actionId ?? 0);
                                    EasyLoading.dismiss();
                                    if (item != null) {
                                      PageNavigator.push(
                                          context: context,
                                          route:
                                              WfhRequestOverviewScreenForReportingPerson(
                                                  data: item));
                                    }
                                  }
                                },
                                child: NotificationRequestTile(
                                    notification: notify, provider: provider)),
                          ],
                        );
                      },
                    ),
                    separatorBuilder: (context, index) {
                      return const SizedBox(
                        height: 10,
                      );
                    },
                  ),
                ),
              ],
            ));
      },
    );
  }

  // Widget _requesTile({required GetNotificationModel notification}) {
  //   String? message = notification.message;
  //   bool isRead = notification.read ?? false;
  //   String? image = notification.userDetails?.profilePic;
  //   // var date = DateTime.parse(notification.createdAt!);
  //   // var d = formatDateFromDate(dateTime: date, format: 'dd EEE hh:mm a');
  //   return Container(
  //     // height: h * 80,
  //     width: w * 343,
  //     padding: EdgeInsets.fromLTRB(w * 10, h * 18, w * 10, h * 17),
  //     decoration: BoxDecoration(
  //       color: !isRead ? ThemeColors.colorE3E3E3 : ThemeColors.colorFFFFFF,
  //       borderRadius: BorderRadius.circular(8),
  //     ),
  //     child: Row(
  //       children: [
  //         if (image != null)
  //           Container(
  //             height: h * 45,
  //             width: w * 45,
  //             alignment: Alignment.center,
  //             padding: const EdgeInsets.all(13),
  //             decoration: BoxDecoration(
  //               shape: BoxShape.circle,
  //               color: ThemeColors.colorF8F8F8,
  //               image: DecorationImage(
  //                   image: NetworkImage(image), fit: BoxFit.cover),
  //             ),
  //           )
  //         else
  //           Container(
  //             height: h * 45,
  //             width: w * 45,
  //             alignment: Alignment.center,
  //             padding: const EdgeInsets.all(13),
  //             decoration: BoxDecoration(
  //               shape: BoxShape.circle,
  //               color: ThemeColors.colorF8F8F8,
  //             ),
  //             child: Image.asset(
  //               message!.toLowerCase().contains('certificate') ||
  //                       message.toLowerCase().contains('noc') ||
  //                       message.toLowerCase().contains('letter')
  //                   ? 'assets/icons/recent/certificate.png'
  //                   : message.toLowerCase().contains('sick')
  //                       ? 'assets/images/recent_activities.png'
  //                       : 'assets/icons/notification.png',
  //               color: ThemeColors.primaryColor,
  //             ),
  //           ),
  //         SizedBox(width: w * 11),
  //         Expanded(
  //           child: Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               Text(
  //                 message ?? 'John doe commented on your post 'Nice one'',
  //                 style: tsS14w400c181818,
  //                 overflow: TextOverflow.ellipsis,
  //                 maxLines: 2,
  //               ),
  //               Text(
  //                 notification.newDateTime.toString(),
  //                 style: tsS12w500c979797,
  //               ),
  //             ],
  //           ),
  //         )
  //       ],
  //     ),
  //   );
  // }

  Widget _clearAllButton() {
    return Consumer<NotificationProvider>(
      builder: (context, provider, child) {
        return Padding(
          padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
          child: TextButton(
            onPressed: provider.isClearing
                ? null
                : () async {
                    await provider.clearNotification(context: context);
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: ThemeColors.colorTransparent,
              minimumSize: const Size(10, 36),
            ),
            child: Text(
              'Clear all',
              style: tsS14w500cFFFFFF,
            ),
          ),
        );
      },
    );
  }
}
