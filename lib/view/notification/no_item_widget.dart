import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../util/size_config.dart';

class NoItemWidget extends StatelessWidget {
  final String iconImage;
  final String text1;
  final String text2;
  final String text3;
  const NoItemWidget(
      {super.key,
      required this.iconImage,
      required this.text1,
      required this.text2,
      required this.text3});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 17),
      height: 637 * h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          SizedBox(
            height: h * 80,
          ),
          Container(
            width: w * 121,
            height: h * 151,
            decoration: BoxDecoration(
                image: DecorationImage(image: AssetImage(iconImage))),
          ),
          SizedBox(
            height: h * 39,
          ),
          Text(
            text1,
            style:
                GoogleFonts.poppins(fontWeight: FontWeight.w500, fontSize: 20),
          ),
          SizedBox(
            height: h * 6,
          ),
          Text(
            "$text2\n$text3",
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
                fontWeight: FontWeight.w400,
                fontSize: 14,
                color: const Color(0xff9F9F9F)),
          )
        ],
      ),
    );
  }
}
