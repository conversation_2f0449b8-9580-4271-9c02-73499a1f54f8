import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/model/notification_model.dart';
import 'package:e8_hr_portal/provider/notification_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/model/leave_application_model.dart';
import 'package:provider/provider.dart';

class LeaveRequestNotification extends StatelessWidget {
  final NotificationModel notification;
  const LeaveRequestNotification(this.notification, {super.key});

  @override
  Widget build(BuildContext context) {
    String? profilePhoto = notification.leaveApplicationModel?.profilePhoto;
    String? employeeName = notification.leaveApplicationModel?.name;
    LeaveApplicationModel? leave = notification.leaveApplicationModel;
    String? fromDate = formatDateFromDate(
      dateTime: leave!.fromDate!.toDate(),
      format: 'dd MMM yyyy',
    );
    String? toDate = formatDateFromDate(
      dateTime: leave.toDate!.toDate(),
      format: 'dd MMM yyyy',
    );
    String status = 'Pending';
    Color statusBgColor = const Color(0xFFFFECC1);
    Color statusColor = const Color(0xFFFFB100);
    switch (notification.status!.toLowerCase()) {
      case 'pending':
        status = 'Pending';
        statusBgColor = const Color(0xFFFFECC1);
        statusColor = const Color(0xFFFFB100);
        break;
      case 'approved':
        status = 'Approved';
        statusBgColor = const Color(0xFFB9FFCE);
        statusColor = const Color(0xFF06AA37);
        break;
      case 'rejected':
        status = 'Rejected';
        break;
    }
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: const [
          BoxShadow(
            blurRadius: 10,
            color: Color.fromRGBO(0, 0, 0, 0.16),
            offset: Offset(2, 2),
          ),
        ],
        borderRadius: BorderRadius.circular(5.0),
      ),
      padding: const EdgeInsets.all(10.0),
      child: Row(
        children: [
          Container(
            height: 50,
            width: 50,
            decoration: BoxDecoration(
              color: profilePhoto == null ? ThemeColors.primaryColor : null,
              shape: BoxShape.circle,
              image: profilePhoto != null && profilePhoto.isNotEmpty
                  ? DecorationImage(
                      image: CachedNetworkImageProvider(profilePhoto),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            alignment: Alignment.center,
            child: profilePhoto == null
                ? Text(
                    '${employeeName?.substring(0, 1).toUpperCase()}',
                    style: GoogleFonts.rubik(
                        fontSize: 22,
                        fontWeight: FontWeight.w500,
                        color: Colors.white),
                  )
                : null,
          ),
          const SizedBox(width: 15.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${notification.body}',
                  style: tsS161E2138,
                ),
                Text(
                  '${leave.leaveType} $fromDate - $toDate',
                  style: tsS14BN,
                ),
                if (notification.status == 'pending')
                  Row(
                    children: [
                      Consumer<NotificationProvider>(
                        builder: (context, provider, child) {
                          return ElevatedButton(
                            onPressed: () {
                              if (!provider.isLoading) {
                                provider.approveLeave(
                                  notificationId: notification.id!,
                                  leaveId: notification.leaveId!,
                                  employeeId: leave.uid!,
                                );
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: ThemeColors.color06AA37,
                            ),
                            child: const Text('Approve'),
                          );
                        },
                      ),
                      const SizedBox(width: 10.0),
                      Consumer<NotificationProvider>(
                        builder: (context, provider, child) {
                          return ElevatedButton(
                            onPressed: () {
                              if (!provider.isLoading) {
                                provider.rejectLeave(
                                  notificationId: notification.id!,
                                  leaveId: notification.leaveId!,
                                  employeeId: leave.uid!,
                                );
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: ThemeColors.secondaryColor,
                            ),
                            child: const Text('Reject'),
                          );
                        },
                      )
                    ],
                  ),
                if (notification.status != 'pending') const SizedBox(height: 7),
                if (notification.status != 'pending')
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 15.0,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3),
                      color: statusBgColor,
                    ),
                    child: Text(
                      status,
                      style: GoogleFonts.rubik(
                        fontSize: 10,
                        color: statusColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
