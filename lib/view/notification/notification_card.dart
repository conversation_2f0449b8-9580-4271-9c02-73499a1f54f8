// import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:e8_hr_portal/model/get_notification_model.dart';
// import 'package:e8_hr_portal/util/colors.dart';
// import 'package:e8_hr_portal/util/date_formatter.dart';

// import 'package:e8_hr_portal/util/styles.dart';

// class NotificationCard extends StatelessWidget {
//   final GetNotificationModel notification;
//   const NotificationCard(this.notification, {Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     var height = MediaQuery.of(context).size.height;
//     return Container(
//         // width: 375,
//         height: height * 0.0998,
//         decoration: BoxDecoration(
//           color: Colors.white,
//           boxShadow: const [
//             BoxShadow(
//               blurRadius: 10,
//               color: Color.fromRGBO(0, 0, 0, 0.16),
//               offset: Offset(2, 2),
//             ),
//           ],
//           borderRadius: BorderRadius.circular(5.0),
//         ),
//         padding: const EdgeInsets.all(10.0),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             Container(
//               height: height * 0.0512,
//               width: height * 0.0512,
//               margin: const EdgeInsets.only(right: 10),
//               decoration: BoxDecoration(
//                   color: ThemeColors.color03AD9E.withOpacity(0.10),
//                   shape: BoxShape.circle,
//                   image: DecorationImage(
//                       image: NetworkImage(
//                         notification.userDetails!.profilePic.toString(),
//                       ),
//                       fit: BoxFit.cover)),
//             ),
//             Expanded(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   Text(
//                     '${notification.message}',
//                     style: GoogleFonts.poppins(
//                       fontSize: 12,
//                       color: Colors.black,
//                       fontWeight: FontWeight.w400,
//                     ),
//                     // overflow: TextOverflow,
//                   ),
//                   Text(
//                       formatDateFromString(notification.createdAt!,
//                           "yyyy-MM-ddThh:mm:ssz", "dd EEE hh:mm"),
//                       // formatDateFromDate(
//                       //     dateTime: notification.saveTime!.toDate(),
//                       //     format: 'dd MMM, yyyy'),
//                       style: tsS12grey),
//                   // Text(
//                   //   '${notification.message}',
//                   //   style: tsS14BN,
//                   // ),
//                 ],
//               ),
//             ),
//           ],
//         ));
//   }
// }
