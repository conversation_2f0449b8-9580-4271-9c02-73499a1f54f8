import 'package:e8_hr_portal/model/project_model.dart';
import 'package:e8_hr_portal/view/morning_huddle/models/morning_huddle_entry_model.dart';
import 'package:e8_hr_portal/view/morning_huddle/providers/morning_huddle_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../widgets/custom_scaffold.dart';
import '../../widgets/type_ahead_form_field_widget.dart';
import '../../widgets/hisense_text_form_field.dart';
import '../../../util/colors.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';

class CreateMorningHuddleScreen extends StatefulWidget {
  static const String routeName = '/create-morning-huddle-screen';
  const CreateMorningHuddleScreen({super.key});

  @override
  State<CreateMorningHuddleScreen> createState() =>
      _CreateMorningHuddleScreenState();
}

class _CreateMorningHuddleScreenState extends State<CreateMorningHuddleScreen> {
  late MorningHuddleProvider _provider;
  late Future<List<ProjectModel>> _projectFuture;
  final _formKey = GlobalKey<FormState>();

  final List<String> _timeOptions = ['1', '2', '4', '8'];

  @override
  void initState() {
    super.initState();
    _provider = context.read<MorningHuddleProvider>();
    _provider.huddleEntries.clear();
    _provider.huddleEntries.add(
      MorningHuddleEntryModel(
        projectController: TextEditingController(),
        remarkController: TextEditingController(),
        taskController: TextEditingController(),
        timeController: TextEditingController(),
        projectFocusNode: FocusNode(),
        remarkFocusNode: FocusNode(),
        taskFocusNode: FocusNode(),
        timeFocusNode: FocusNode(),
        reportingPerson: null,
      ),
    );
    _provider.enableNoTaskToday = false;
    _projectFuture = _provider.getProjectTypes();
    // if (widget.editingItem != null) {
    //   final item = widget.editingItem;
    //   if (item?.project != null) {
    //     _provider.huddleEntries[0].projectController.text = item?.project ?? '';
    //     String? projectCordinator = _provider.projectList
    //         .firstWhere(
    //             (element) =>
    //                 element.title != null &&
    //                 element.projectCordinator == item?.reportingPerson,
    //             orElse: () => ProjectModel())
    //         .projectCordinator;
    //     if (projectCordinator != null) {
    //       _provider.huddleEntries[0].reportingPerson = projectCordinator;
    //     }
    //   }
    //   if (item?.plannedTask != null) {
    //     _provider.huddleEntries[0].taskController.text =
    //         item?.plannedTask ?? '';
    //   }
    //   if (item?.estimatedTime != null) {
    //     _provider.huddleEntries[0].timeController.text =
    //         item?.estimatedTime ?? '';
    //   }
    //   if (item?.project != null) {
    //     _provider.huddleEntries[0].remarkController.text = item?.remarks ?? '';
    //   }
    // }
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      setState(() {});
    });
  }

  @override
  void dispose() {
    for (var entry in _provider.huddleEntries) {
      entry.dispose();
    }
    _provider.huddleEntries.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: 'Morning Huddle',
      body: SafeArea(
        child: FutureBuilder(
          future: _projectFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(child: CircularProgressIndicator());
            }
            return Stack(
              children: [
                Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: w * 0, vertical: h * 16),
                      child: _buildNoTaskToggle(),
                    ),
                    Consumer<MorningHuddleProvider>(
                      builder: (context, provider, _) {
                        if (!provider.enableNoTaskToday) {
                          return Expanded(
                            child: ListView(
                              padding: EdgeInsets.symmetric(vertical: h * 16),
                              children: [
                                ...List.generate(provider.huddleEntries.length,
                                    (index) {
                                  return Padding(
                                    padding: EdgeInsets.only(bottom: h * 16),
                                    child: _buildEntryCard(index),
                                  );
                                }),
                                _buildAddButton(),
                                SizedBox(height: h * 80),
                              ],
                            ),
                          );
                        }
                        return const SizedBox();
                      },
                    ),
                  ],
                ),
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Container(
                    color: Colors.white,
                    child: _buildSubmitButton(),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildEntryCard(int index) {
    final entry = _provider.huddleEntries[index];
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: EdgeInsets.all(w * 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Huddle ${index + 1}',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF30292F),
                  ),
                ),
                if (_provider.huddleEntries.length > 1)
                  IconButton(
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('Delete Huddle'),
                          content: const Text(
                              'Are you sure you want to delete this huddle? This action cannot be undone.'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: const Text('Cancel'),
                            ),
                            ElevatedButton(
                              onPressed: () {
                                _provider.removeHuddle(index: index);
                                Navigator.pop(context);
                              },
                              child: const Text('Delete'),
                            ),
                          ],
                        ),
                      );
                    },
                    icon: const Icon(Icons.delete_outline, color: Colors.red),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
              ],
            ),
            if (!_provider.enableNoTaskToday)
              Form(
                key: _formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(height: h * 16),
                    _buildProjectField(entry: entry),
                    _buildTaskField(entry: entry),
                    SizedBox(height: h * 16),
                    _buildTimeDropdown(entry: entry),
                    SizedBox(height: h * 16),
                    _buildRemarkField(entry: entry),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddButton() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: h * 16),
      child: Center(
        child: OutlinedButton.icon(
          onPressed: () => _provider.addNewHuddle(),
          icon: const Icon(Icons.add),
          label: Text(
            'Add Another Entry',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          style: OutlinedButton.styleFrom(
            foregroundColor: ThemeColors.primaryColor,
            side: BorderSide(color: ThemeColors.primaryColor),
            padding: EdgeInsets.symmetric(horizontal: w * 24, vertical: h * 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoTaskToggle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'No Task Today?',
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF30292F),
          ),
        ),
        Consumer<MorningHuddleProvider>(
          builder: (context, provider, _) {
            return CupertinoSwitch(
              value: provider.enableNoTaskToday,
              onChanged: (value) {
                if (value) {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: Text(
                        'No Tasks Today',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF30292F),
                        ),
                      ),
                      content: Text(
                        'Are you sure you have no tasks for today? This will clear all your current entries.',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xFF30292F),
                        ),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: Text(
                            'Cancel',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF8391B5),
                            ),
                          ),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            _provider.toggleNoTaskToday();
                            _provider.clearAllhuddles();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ThemeColors.primaryColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(5),
                            ),
                          ),
                          child: Text(
                            'Confirm',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                } else {
                  _provider.toggleNoTaskToday();
                }
              },
              activeColor: ThemeColors.primaryColor,
            );
          },
        ),
      ],
    );
  }

  Widget _buildProjectField({required MorningHuddleEntryModel entry}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Project',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF30292F),
              ),
            ),
            Text(
              ' *',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.red,
              ),
            ),
          ],
        ),
        SizedBox(height: h * 4),
        FutureBuilder<List<ProjectModel>>(
          future: _projectFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            final projects = snapshot.data ?? [];

            return TypeAheadFormFieldWidget(
              controller: entry.projectController,
              hintText: 'Select Project',
              textStyle: tsS14w400c000000,
              hintStyle: GoogleFonts.poppins(
                fontSize: 13 * f,
                fontWeight: FontWeight.w300,
                color: const Color(0xFF8391B5),
              ),
              itemBuilder: (context, ProjectModel item) {
                return ListTile(
                  title: Text(item.title ?? '', style: tsS14w400454444),
                );
              },
              validator: (value) {
                if ((value == null || value.trim().isEmpty)) {
                  return 'Please select a project';
                }
                return null;
              },
              onSelected: (ProjectModel item) {
                entry.projectController.text = item.title ?? '';
                entry.reportingPerson = item.projectCordinator;
              },
              suggestionsCallback: (pattern) {
                return projects
                    .where((item) => (item.title ?? '')
                        .toLowerCase()
                        .contains(pattern.toLowerCase()))
                    .toList();
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildTaskField({required MorningHuddleEntryModel entry}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Planned Task',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF30292F),
              ),
            ),
            Text(
              ' *',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.red,
              ),
            ),
          ],
        ),
        SizedBox(height: h * 4),
        HisenseTextFormField(
          hintStyle: GoogleFonts.poppins(
            fontSize: 13 * f,
            fontWeight: FontWeight.w300,
            color: const Color(0xFF8391B5),
          ),
          textStyle: tsS14w400c000000,
          controller: entry.taskController,
          hintText: 'Enter your planned task',
          validator: (value) {
            if ((value == null || value.trim().isEmpty)) {
              return 'Please enter a planned task';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildTimeDropdown({required MorningHuddleEntryModel entry}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Estimated Time for today',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF30292F),
              ),
            ),
            Text(
              ' *',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.red,
              ),
            ),
          ],
        ),
        SizedBox(height: h * 4),
        TypeAheadFormFieldWidget(
          controller: entry.timeController,
          hintText: 'Enter time (e.g:- 1.5 hr, 2 hr, 4 hr, 8 hr)',
          textStyle: tsS14w400c000000,
          hintStyle: GoogleFonts.poppins(
            fontSize: 13 * f,
            fontWeight: FontWeight.w300,
            color: const Color(0xFF8391B5),
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            // FilteringTextInputFormatter.digitsOnly,
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            LengthLimitingTextInputFormatter(5),
          ],
          itemBuilder: (context, suggestion) {
            return ListTile(
              title: Text(
                '$suggestion hr',
                style: tsS14w400454444,
              ),
            );
          },
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter estimated time';
            }
            // final hours = double.tryParse(value);
            // if (hours == null || hours <= 0 || hours > 24) {
            //   return 'Please enter a valid time (0.01-24 hours)';
            // }

            return null;
          },
          onSelected: (suggestion) {
            entry.timeController.text = suggestion.toString();
          },
          suggestionsCallback: (pattern) {
            if (pattern.isEmpty) {
              return _timeOptions;
            }
            return _timeOptions
                .where((item) =>
                    item.toLowerCase().contains(pattern.toLowerCase()))
                .toList();
          },
        ),
      ],
    );
  }

  Widget _buildRemarkField({required MorningHuddleEntryModel entry}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Remark',
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF30292F),
          ),
        ),
        SizedBox(height: h * 4),
        HisenseTextFormField(
          textStyle: tsS14w400c000000,
          controller: entry.remarkController,
          hintText: 'e.g:- Waiting for client feedback, Blocked by X',
          hintStyle: GoogleFonts.poppins(
            fontSize: 13 * f,
            fontWeight: FontWeight.w300,
            color: const Color(0xFF8391B5),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Padding(
        padding: EdgeInsets.only(bottom: w * 16),
        child: Consumer<MorningHuddleProvider>(
          builder: (context, provider, _) {
            return ElevatedButton(
              onPressed: () async {
                if (!provider.enableNoTaskToday) {
                  if (_formKey.currentState!.validate()) {
                    bool isSuccess = await _provider.submitHuddles();
                    if (isSuccess) {
                      _provider.huddleCurrentPage = 0;
                      _provider.huddlePaginationController.refresh();
                      Navigator.pop(context);
                    }
                  }
                } else {
                  bool isSuccess = await _provider.submitHuddles();
                  if (isSuccess) {
                    _provider.huddleCurrentPage = 0;
                    _provider.huddlePaginationController.refresh();
                    Navigator.pop(context);
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: ThemeColors.primaryColor,
                minimumSize: Size(double.infinity, h * 48),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5),
                ),
              ),
              child: Text(
                'Submit',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
