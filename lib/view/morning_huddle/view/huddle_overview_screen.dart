import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/morning_huddle/models/morning_huddle_model.dart';
import 'package:e8_hr_portal/view/morning_huddle/providers/morning_huddle_provider.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

class HuddleOverviewScreen extends StatefulWidget {
  static const route = '/huddle_overview_screen';

  final int huddleID;
  const HuddleOverviewScreen({super.key, required this.huddleID});

  @override
  State<HuddleOverviewScreen> createState() => _HuddleOverviewScreenState();
}

class _HuddleOverviewScreenState extends State<HuddleOverviewScreen> {
  late MorningHuddleProvider _provider;
  @override
  void initState() {
    super.initState();
    _provider = context.read<MorningHuddleProvider>();
    _provider.getHuddleOverview(huddleID: widget.huddleID);
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {},
    );
  }

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Morning Huddle',
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 10 * w),
            child: Consumer<MorningHuddleProvider>(
              builder: (context, provider, _) {
                if (provider.morningHuddleModel == null &&
                    !provider.isLoading) {
                  return const Center(
                    child: Text('No Data Found'),
                  );
                }
                MorningHuddleModel? item = provider.morningHuddleModel;
                if (provider.isLoading) {
                  return Skeletonizer(
                    enabled: true,
                    child: _taskCard(
                      context: context,
                      item: MorningHuddleModel(
                          date: '10 Tue,25',
                          estimatedTime: '2 hr',
                          id: 0,
                          plannedTask: 'Planneeeef',
                          project: 'Test prject',
                          remarks: 'Test frdv',
                          reportingPerson: ''),
                    ),
                  );
                }

                if (item != null) {
                  return _taskCard(item: item, context: context);
                }
                return const SizedBox();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _keyValueWidget(
      {required String key,
      required String value,
      double? fontSize,
      Color? fontColor,
      Color? valueFontColor,
      FontWeight? fontWeight,
      bool enableDivider = false}) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Container(
                // color: Colors.red,
                child: Text(key,
                    style: GoogleFonts.inter(
                        color: fontColor,
                        fontSize: fontSize,
                        fontWeight: fontWeight)),
              ),
            ),
            Text(': ',
                style: GoogleFonts.inter(
                    color: fontColor,
                    fontSize: fontSize,
                    fontWeight: fontWeight)),
            Expanded(
              flex: 2,
              child: Text(value,
                  style: GoogleFonts.inter(
                    color: valueFontColor ?? ThemeColors.color121417,
                    fontSize: fontSize,
                    fontWeight: fontWeight,
                  )),
            ),
          ],
        ),
        if (enableDivider) ...[
          SizedBox(height: 20),
          Container(
            height: 1,
            width: double.infinity,
            color: Color(0xFFE5E8E8),
          ),
        ]
      ],
    );
  }

  Widget _taskCard(
      {required MorningHuddleModel item, required BuildContext context}) {
    String project = item.project ?? '';
    String plannedTask = item.plannedTask ?? '';
    String estimatedTime = item.estimatedTime ?? '';
    String remarks = item.remarks ?? '';
    String date = item.date ?? '';
    String reportingPerson = item.reportingPerson ?? '';
    return Container(
      width: double.infinity,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _keyValueWidget(
              key: 'Project',
              value: project,
              fontColor: Color(0xFF121417),
              fontSize: 16,
              fontWeight: FontWeight.w600),
          SizedBox(height: 20),
          _keyValueWidget(
              key: 'Planned Tasks',
              value: plannedTask,
              fontColor: Color(0xFF61758A),
              fontSize: 14,
              fontWeight: FontWeight.w400,
              enableDivider: true),
          SizedBox(height: 10),
          _keyValueWidget(
              key: 'Estimated Time',
              value: estimatedTime,
              fontColor: Color(0xFF61758A),
              fontSize: 14,
              fontWeight: FontWeight.w400,
              enableDivider: true),
          SizedBox(height: 10),
          _keyValueWidget(
              key: 'Date',
              value: date,
              fontColor: Color(0xFF61758A),
              fontSize: 14,
              fontWeight: FontWeight.w400,
              enableDivider: true),
          if (reportingPerson.isNotEmpty) ...[
            SizedBox(height: 10),
            _keyValueWidget(
                key: 'Reporting',
                value: reportingPerson,
                fontColor: Color(0xFF61758A),
                fontSize: 14,
                fontWeight: FontWeight.w400,
                enableDivider: true),
          ],
          if (remarks.isNotEmpty) ...[
            SizedBox(height: 10),
            _keyValueWidget(
                key: 'Remarks',
                value: remarks,
                fontColor: Color(0xFF61758A),
                fontSize: 14,
                fontWeight: FontWeight.w400,
                enableDivider: true),
          ],
        ],
      ),
    );
  }
}
