import 'package:e8_hr_portal/view/morning_huddle/models/morning_huddle_model.dart';
import 'package:e8_hr_portal/view/morning_huddle/providers/morning_huddle_provider.dart';
import 'package:e8_hr_portal/view/morning_huddle/view/huddle_overview_screen.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../../util/colors.dart';
import '../../../util/page_navigator.dart';
import 'create_morning_huddle_screen.dart';
import 'edit_huddle_screen.dart';

class MorningHuddleListingScreen extends StatefulWidget {
  static const String routeName = '/morning-huddle-listing-screen';
  const MorningHuddleListingScreen({super.key});

  @override
  State<MorningHuddleListingScreen> createState() =>
      _MorningHuddleListingScreenState();
}

class _MorningHuddleListingScreenState
    extends State<MorningHuddleListingScreen> {
  late MorningHuddleProvider _provider;

  @override
  void initState() {
    super.initState();
    _provider = context.read<MorningHuddleProvider>();
    _provider.initGetHuddles();
  }

  @override
  void dispose() {
    super.dispose();
    _provider.huddlePaginationController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: 'Morning Huddle',
      actions: [
        IconButton(
          onPressed: () {
            PageNavigator.push(
                context: context, route: CreateMorningHuddleScreen());
          },
          icon: Icon(Icons.add),
        ),
      ],
      body: Consumer<MorningHuddleProvider>(builder: (context, provider, _) {
        return PagedListView.separated(
          padding: EdgeInsets.only(top: 20),
          pagingController: _provider.huddlePaginationController,
          builderDelegate: PagedChildBuilderDelegate<MorningHuddleModel>(
            itemBuilder: (context, item, index) {
              return _taskCard(item: item, index: index);
            },
            noItemsFoundIndicatorBuilder: (context) {
              return const Center(child: Text('No Data Found'));
            },
            firstPageProgressIndicatorBuilder: (context) {
              return Skeletonizer(
                enabled: true,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _taskCard(
                        index: 0,
                        item: MorningHuddleModel(
                            date: '10 Tue,25',
                            estimatedTime: '2 hr',
                            id: 0,
                            plannedTask: 'Planneeeef',
                            project: 'Test prject',
                            remarks: 'Test frdv',
                            reportingPerson: '')),
                  ],
                ),
              );
            },
          ),
          separatorBuilder: (context, index) => const SizedBox(height: 4),
        );
      }),
    );
  }

  Widget _taskCard({required MorningHuddleModel item, required int index}) {
    String project = item.project ?? 'No Project';
    String plannedTask = item.plannedTask ?? 'No Task';
    String estimatedTime = item.estimatedTime ?? '';
    String remarks = item.remarks ?? '';
    String date = item.date ?? '';
    int? id = item.id;
    return Slidable(
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: [
          SlidableAction(
            onPressed: (_) {
              // Show confirmation dialog for delete
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text(
                    'Delete Huddle',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF30292F),
                    ),
                  ),
                  content: Text(
                    'Are you sure you want to delete this huddle? This action cannot be undone.',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: const Color(0xFF30292F),
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        'Cancel',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF8391B5),
                        ),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        if (id != null) {
                          bool isSuccess = await _provider.deleteHuddle(
                              huddleID: id, index: index);
                          if (isSuccess) {
                            // _provider.huddleCurrentPage = 0;
                            // _provider.huddlePaginationController.refresh();
                          }
                          Navigator.of(context).pop();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ThemeColors.primaryColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5),
                        ),
                      ),
                      child: Text(
                        'Confirm',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
            icon: Icons.delete_outline,
            label: 'Delete',
            backgroundColor: Colors.transparent,
            foregroundColor: Colors.black,
          ),
          SlidableAction(
            onPressed: (_) {
              PageNavigator.push(
                  context: context,
                  route: EditHuddleScreen(editingItem: item, index: index));
            },
            backgroundColor: Colors.transparent,
            foregroundColor: Colors.black,
            icon: Icons.edit_outlined,
            label: 'Edit',
          ),
        ],
      ),
      child: InkWell(
        onTap: () async {
          // _provider.getHuddleOverview(huddleID: id);
          if (id != null) {
            PageNavigator.push(
                context: context, route: HuddleOverviewScreen(huddleID: id));
          }
        },
        child: Card(
          elevation: 4,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            padding: const EdgeInsets.all(16.0),
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                        child: Text("Project: $project",
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold),
                            overflow: TextOverflow.ellipsis)),
                    Text(date)
                  ],
                ),
                SizedBox(height: 8),
                Text("Planned Task: $plannedTask"),
                SizedBox(height: 4),
                Text("Estimated Time: $estimatedTime"),
                if (remarks.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Text(
                      "Remark: $remarks",
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _noTaskTodayCard() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.do_not_disturb, color: Colors.red),
            SizedBox(width: 8),
            Text(
              "No Task Today",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
