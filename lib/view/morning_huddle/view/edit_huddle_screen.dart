import 'package:e8_hr_portal/view/morning_huddle/models/morning_huddle_model.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../../model/project_model.dart';
import '../../../util/colors.dart';
import '../../../util/size_config.dart';
import '../../../util/styles.dart';
import '../../widgets/hisense_text_form_field.dart';
import '../../widgets/type_ahead_form_field_widget.dart';
import '../models/morning_huddle_entry_model.dart';
import '../providers/morning_huddle_provider.dart';

class EditHuddleScreen extends StatefulWidget {
  final MorningHuddleModel editingItem;
  final int index;
  const EditHuddleScreen(
      {super.key, required this.editingItem, required this.index});

  @override
  State<EditHuddleScreen> createState() => _EditHuddleScreenState();
}

class _EditHuddleScreenState extends State<EditHuddleScreen> {
  late MorningHuddleProvider _provider;
  late Future<List<ProjectModel>> _projectFuture;
  final _formKey = GlobalKey<FormState>();

  final List<String> _timeOptions = ['1', '2', '4', '8'];

  @override
  void initState() {
    super.initState();
    _provider = context.read<MorningHuddleProvider>();
    _provider.editHuddleEntry = MorningHuddleEntryModel(
        projectController: TextEditingController(),
        remarkController: TextEditingController(),
        taskController: TextEditingController(),
        timeController: TextEditingController());
    _provider.enableNoTaskToday = false;
    _projectFuture = _provider.getProjectTypes();

    final item = widget.editingItem;
    if (item.project != null) {
      _provider.editHuddleEntry?.projectController.text = item.project ?? '';
      String? projectCordinator = _provider.projectList
          .firstWhere(
              (element) =>
                  element.title != null &&
                  element.projectCordinator == item.reportingPerson,
              orElse: () => ProjectModel())
          .projectCordinator;
      if (projectCordinator != null) {
        _provider.editHuddleEntry?.reportingPerson = projectCordinator;
      }
    }
    if (item.plannedTask != null) {
      _provider.editHuddleEntry?.taskController.text = item.plannedTask ?? '';
    }
    if (item.estimatedTime != null) {
      _provider.editHuddleEntry?.timeController.text = item.estimatedTime ?? '';
    }
    if (item.project != null) {
      _provider.editHuddleEntry?.remarkController.text = item.remarks ?? '';
    }

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      setState(() {});
    });
  }

  @override
  void dispose() {
    super.dispose();
    if (_provider.editHuddleEntry != null) {
      _provider.editHuddleEntry?.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;
    return HisenseScaffold(
      screenTitle: 'Edit Huddle',
      body: FutureBuilder(
        future: _projectFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            padding: EdgeInsets.only(
                left: 12 * w,
                right: 12 * w,
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Form(
              key: _formKey,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  _cardWidget(),
                  // if (!isKeyboardVisible)
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingWidget: _buildSubmitButton(isKeyboardVisible: isKeyboardVisible),
      avoidBottom: false,
    );
  }

  Widget _buildSubmitButton({required bool isKeyboardVisible}) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Padding(
        padding: EdgeInsets.only(bottom: w * 16, left: 12 * w, right: 12 * w),
        child: ElevatedButton(
          onPressed: () async {
            if (_formKey.currentState?.validate() ?? false) {
              if (widget.editingItem.id != null) {
                bool isSuccess = await _provider.updateHuddle(
                    huddleID: widget.editingItem.id!);
                if (isSuccess) {
                  _provider.huddleCurrentPage = 0;
                  _provider.huddlePaginationController.refresh();
                  Navigator.pop(context);
                }
              }
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: ThemeColors.primaryColor,
            minimumSize: Size(double.infinity, h * 48),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5),
            ),
          ),
          child: Text(
            'Submit',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _cardWidget() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: EdgeInsets.all(w * 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Huddle ${widget.index + 1}',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF30292F),
                  ),
                ),
              ],
            ),
            if (!_provider.enableNoTaskToday &&
                _provider.editHuddleEntry != null) ...[
              SizedBox(height: h * 16),
              _buildProjectField(entry: _provider.editHuddleEntry!),
              _buildTaskField(entry: _provider.editHuddleEntry!),
              SizedBox(height: h * 16),
              _buildTimeDropdown(entry: _provider.editHuddleEntry!),
              SizedBox(height: h * 16),
              _buildRemarkField(entry: _provider.editHuddleEntry!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProjectField({required MorningHuddleEntryModel entry}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Project',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF30292F),
              ),
            ),
            Text(
              ' *',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.red,
              ),
            ),
          ],
        ),
        SizedBox(height: h * 4),
        FutureBuilder<List<ProjectModel>>(
          future: _projectFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            final projects = snapshot.data ?? [];

            return TypeAheadFormFieldWidget(
              controller: entry.projectController,
              focusNode: entry.projectFocusNode,
              hintText: 'Select Project',
              textStyle: tsS14w400c000000,
              hintStyle: GoogleFonts.poppins(
                fontSize: 13 * f,
                fontWeight: FontWeight.w300,
                color: const Color(0xFF8391B5),
              ),
              itemBuilder: (context, ProjectModel item) {
                return ListTile(
                  title: Text(item.title ?? '', style: tsS14w400454444),
                );
              },
              validator: (value) {
                if ((value == null || value.trim().isEmpty)) {
                  return 'Please select a project';
                }
                return null;
              },
              onSelected: (ProjectModel item) {
                entry.projectController.text = item.title ?? '';
                entry.reportingPerson = item.projectCordinator;
                FocusScope.of(context).requestFocus(entry.taskFocusNode);
              },
              suggestionsCallback: (pattern) {
                return projects
                    .where((item) => (item.title ?? '')
                        .toLowerCase()
                        .contains(pattern.toLowerCase()))
                    .toList();
              },
              onFieldSubmitted: (_) {
                FocusScope.of(context).requestFocus(entry.taskFocusNode);
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildTaskField({required MorningHuddleEntryModel entry}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Planned Task',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF30292F),
              ),
            ),
            Text(
              ' *',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.red,
              ),
            ),
          ],
        ),
        SizedBox(height: h * 4),
        HisenseTextFormField(
          hintStyle: GoogleFonts.poppins(
              fontSize: 13 * f,
              fontWeight: FontWeight.w300,
              color: const Color(0xFF8391B5)),
          textStyle: tsS14w400c000000,
          controller: entry.taskController,
          focusNode: entry.taskFocusNode,
          hintText: 'Enter your planned task',
          maxLines: 1,
          validator: (value) {
            if ((value == null || value.trim().isEmpty)) {
              return 'Please enter a planned task';
            }
            return null;
          },
          onFieldSubmitted: (_) {
            FocusScope.of(context).requestFocus(entry.timeFocusNode);
          },
        ),
      ],
    );
  }

  Widget _buildTimeDropdown({required MorningHuddleEntryModel entry}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Estimated Time for today',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF30292F),
              ),
            ),
            Text(
              ' *',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.red,
              ),
            ),
          ],
        ),
        SizedBox(height: h * 4),
        TypeAheadFormFieldWidget(
          controller: entry.timeController,
          focusNode: entry.timeFocusNode,
          hintText: 'Enter time (e.g:- 1.5 hr, 2 hr, 4 hr, 8 hr)',
          textStyle: tsS14w400c000000,
          hintStyle: GoogleFonts.poppins(
            fontSize: 13 * f,
            fontWeight: FontWeight.w300,
            color: const Color(0xFF8391B5),
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            LengthLimitingTextInputFormatter(5),
          ],
          itemBuilder: (context, suggestion) {
            return ListTile(
              title: Text(
                '$suggestion hr',
                style: tsS14w400454444,
              ),
            );
          },
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter estimated time';
            }
            // final hours = double.tryParse(value);
            // if (hours == null || hours <= 0 || hours > 24) {
            //   return 'Please enter a valid time (0.01-24 hours)';
            // }

            return null;
          },
          onSelected: (suggestion) {
            entry.timeController.text = suggestion.toString();
            FocusScope.of(context).requestFocus(entry.remarkFocusNode);
          },
          suggestionsCallback: (pattern) {
            if (pattern.isEmpty) {
              return _timeOptions;
            }
            return _timeOptions
                .where((item) =>
                    item.toLowerCase().contains(pattern.toLowerCase()))
                .toList();
          },
          onFieldSubmitted: (_) {
            FocusScope.of(context).requestFocus(entry.remarkFocusNode);
          },
        ),
      ],
    );
  }

  Widget _buildRemarkField({required MorningHuddleEntryModel entry}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Remark',
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF30292F),
          ),
        ),
        SizedBox(height: h * 4),
        HisenseTextFormField(
          textStyle: tsS14w400c000000,
          controller: entry.remarkController,
          focusNode: entry.remarkFocusNode,
          hintText: 'e.g:- Waiting for client feedback, Blocked by X',
          hintStyle: GoogleFonts.poppins(
            fontSize: 13 * f,
            fontWeight: FontWeight.w300,
            color: const Color(0xFF8391B5),
          ),
          maxLines: 3,
          onFieldSubmitted: (_) {
            FocusScope.of(context).unfocus();
          },
        ),
      ],
    );
  }
}
