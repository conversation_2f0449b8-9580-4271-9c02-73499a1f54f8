class MorningHuddleModel {
  int? id;
  String? project;
  String? plannedTask;
  String? reportingPerson;
  String? estimatedTime;
  String? remarks;
  String? date;

  MorningHuddleModel(
      {this.id,
      this.project,
      this.plannedTask,
      this.reportingPerson,
      this.estimatedTime,
      this.remarks,
      this.date});

  MorningHuddleModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    project = json['project'];
    plannedTask = json['planned_task'];
    reportingPerson = json['reporting_person'];
    estimatedTime = json['estimated_time'];
    remarks = json['remarks'];
    date = json['date'];
  }
}
