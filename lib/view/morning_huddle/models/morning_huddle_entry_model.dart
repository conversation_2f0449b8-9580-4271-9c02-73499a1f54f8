import 'package:flutter/material.dart';

class MorningHuddleEntryModel {
  final TextEditingController projectController;
  final TextEditingController taskController;
  final TextEditingController remarkController;
  final TextEditingController timeController;
  final FocusNode projectFocusNode;
  final FocusNode taskFocusNode;
  final FocusNode remarkFocusNode;
  final FocusNode timeFocusNode;
  String? reportingPerson;

  MorningHuddleEntryModel({
    required this.projectController,
    required this.remarkController,
    required this.taskController,
    required this.timeController,
    FocusNode? projectFocusNode,
    FocusNode? taskFocusNode,
    FocusNode? remarkFocusNode,
    FocusNode? timeFocusNode,
    this.reportingPerson,
  })  : projectFocusNode = projectFocusNode ?? FocusNode(),
        taskFocusNode = taskFocusNode ?? FocusNode(),
        remarkFocusNode = remarkFocusNode ?? FocusNode(),
        timeFocusNode = timeFocusNode ?? FocusNode();

  void dispose() {
    projectController.dispose();
    taskController.dispose();
    remarkController.dispose();
    timeController.dispose();
    projectFocusNode.dispose();
    taskFocusNode.dispose();
    remarkFocusNode.dispose();
    timeFocusNode.dispose();
  }

  void reset() {
    projectController.clear();
    taskController.clear();
    remarkController.clear();
    timeController.clear();
    projectFocusNode.unfocus();
    taskFocusNode.unfocus();
    remarkFocusNode.unfocus();
    timeFocusNode.unfocus();
  }
}
