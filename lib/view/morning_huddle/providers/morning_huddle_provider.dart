import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:e8_hr_portal/view/morning_huddle/models/morning_huddle_model.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../model/booked_room_status_model.dart';
import '../../../model/login_model.dart';
import '../../../model/project_model.dart';
import '../../../util/constants.dart';
import '../../../util/date_formatter.dart';
import '../models/morning_huddle_entry_model.dart';

class MorningHuddleProvider extends ChangeNotifier {
  bool isLoading = false;
  MorningHuddleEntryModel? editHuddleEntry;
  final List<MorningHuddleEntryModel> _huddleEntries = [];
  List<MorningHuddleEntryModel> get huddleEntries => _huddleEntries;

  void addNewHuddle() {
    _huddleEntries.add(MorningHuddleEntryModel(
        projectController: TextEditingController(),
        remarkController: TextEditingController(),
        taskController: TextEditingController(),
        timeController: TextEditingController()));
    notifyListeners();
  }

  void removeHuddle({required int index}) {
    if (_huddleEntries.length > 1 &&
        index >= 0 &&
        index < _huddleEntries.length) {
      _huddleEntries[index].dispose();
      _huddleEntries.removeAt(index);
      notifyListeners();
    }
  }

  void clearAllhuddles() {
    // Dispose all entries except the first one
    for (var i = _huddleEntries.length - 1; i > 0; i--) {
      _huddleEntries[i].dispose();
      _huddleEntries.removeAt(i);
    }
    // Reset the first entry
    _huddleEntries[0].reset();
    notifyListeners();
  }

  bool enableNoTaskToday = false;
  void toggleNoTaskToday() {
    enableNoTaskToday = !enableNoTaskToday;
    notifyListeners();
  }

  @override
  void dispose() {
    for (var entry in _huddleEntries) {
      entry.dispose();
    }
    super.dispose();
  }

  final Dio _dio = Dio();
  // Future<void> getProjects() async {
  //   try {
  //     final response = await _dio.get('https://api.example.com/projects',
  //         options: Options(headers: await getHeaders()));
  //   } catch (e) {
  //     log(e.toString());
  //   }
  // }

  List<ProjectModel> projectList = [];
  // Future<void> getProjectTypes() async {
  //   final response = await _dio.get('$workflowURL/user-timelog-task-project/',
  //       options: Options(headers: await getHeaders()));
  //   if (response.statusCode == 200) {
  //     Map<String, dynamic> data = response.data;
  //     projectList = (data['records'] as List)
  //         .map((e) => ProjectModel.fromJson(e))
  //         .toList();
  //   }
  // }
  Future<List<ProjectModel>> getProjectTypes() async {
    Uri uri = Uri.parse('$workflowURL/user-timelog-task-project/');
    http.Response response = await http.post(uri, headers: {
      "client-id": workflowClientID,
      "email": LoginModel.email.toString(),
    });
    // log('getProjectTypes == ${response.statusCode} - ${response.request} - ${response.headers} - ${response.body}');
    log('getProjectTypes ==  ${response.body} ');
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      if (data.containsKey('records')) {
        return projectList = (data['records'] as List)
            .map((e) => ProjectModel.fromJson(e))
            .toList();
      }
    }

    // notifyListeners();
    return [];
  }

  Future<bool> submitHuddles() async {
    if (isLoading) return false;
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      Map<String, dynamic> data = {};
      if (!enableNoTaskToday) {
        if (huddleEntries.isNotEmpty) {
          huddleEntries.map(
            (e) {
              int index = huddleEntries.indexOf(e);
              data['project[$index]'] = e.projectController.text.trim();
              data['planned_task[$index]'] = e.taskController.text.trim();
              if (e.reportingPerson != null) {
                data['reporting_person[$index]'] = e.reportingPerson?.trim();
              }
              data['estimated_time[$index]'] = e.timeController.text.trim();
              if (e.timeController.text.trim().toLowerCase().contains('hr')) {
                data['estimated_time[$index]'] = e.timeController.text
                    .trim()
                    .toLowerCase()
                    .split('hr')
                    .first
                    .trim();
              }
              if (e.remarkController.text.isNotEmpty) {
                data['remarks[$index]'] = e.remarkController.text.trim();
              } else {
                data['remarks[$index]'] = null;
              }
              data['no_task_today[$index]'] = false;
            },
          ).toList();
        } else {
          return false;
        }
      } else {
        data['no_task_today[0]'] = true;
      }
      log('data = $data');
      FormData formData = FormData.fromMap(data);
      isLoading = true;
      final response = await _dio.post('$dailyTaskAllocationURL',
          data: formData,
          options: Options(
              headers: await getHeaders(), validateStatus: (status) => true));
      isLoading = false;
      log('submitHuddles - ${response.data} - ${response.realUri} - ${response.statusCode} - ${await getHeaders()}');
      if (response.statusCode == 200) {
        log('200');
        if (response.data['result'] == 'success') {
          log('success');
          Map<String, dynamic> json = response.data;
          if (json.containsKey('message')) {
            log('message');
            showToastText(json['message']);
          }
          String today = formatDateFromDate(
              dateTime: DateTime.now(), format: 'dd-MM-yyyy');
          prefs.setString('last_huddle_dialog_date', today);
        }
        return true;
      }
      if (response.statusCode == 400) {
        return false;
      }
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      isLoading = false;
    }
    return false;
  }

  // List<MorningHuddleModel> morningHuddles = [];
  // Future<void> getHuddles() async {
  //   try {
  //     morningHuddles.clear();
  //     isLoading = true;
  //     final response = await _dio.get('$dailyTaskAllocationListURL',
  //         options: Options(
  //             headers: await getHeaders(), validateStatus: (status) => true));
  //     isLoading = false;
  //     log('getHuddles = ${jsonEncode(response.data)} - ${response.statusCode} - ${response.realUri}');
  //     if (response.statusCode == 200) {
  //       if (response.data['result'] == 'success') {
  //         Map<String, dynamic> json = response.data;
  //         if (json.containsKey('data')) {
  //           List data = json['data'];
  //           morningHuddles =
  //               data.map((e) => MorningHuddleModel.fromJson(e)).toList();
  //         }
  //       }
  //     }
  //     if (response.statusCode == 400) {
  //       isLoading = false;
  //       morningHuddles.clear();
  //     }
  //   } catch (e) {
  //     debugPrint(e.toString());
  //   } finally {
  //     notifyListeners();
  //   }
  // }

  late PagingController<int, MorningHuddleModel> huddlePaginationController;
  int huddleCurrentPage = 0;
  initGetHuddles() {
    huddleCurrentPage = 0;
    huddlePaginationController = PagingController(firstPageKey: 1);
    huddlePaginationController.addPageRequestListener((pageKey) {
      getHuddles(page: pageKey);
    });
  }

  bool huddleHasNext = false;
  Future<void> getHuddles({required int page}) async {
    if (huddleCurrentPage != page) {
      huddleCurrentPage = page;
      Response response = await _dio.get(
          '$dailyTaskAllocationListURL?page=$page&limit=10',
          options: Options(
              headers: await getHeaders(), validateStatus: (status) => true));
      log('getHuddles - ${response.data} - ${response.realUri} - ${response.statusCode}');
      if (response.statusCode == 200) {
        Map<String, dynamic> data = response.data;
        List<MorningHuddleModel> tempList = (data['data'] as List)
            .map((e) => MorningHuddleModel.fromJson(e))
            .toList();
        if (data['has_next']) {
          huddleHasNext = true;
          huddlePaginationController.appendPage(tempList, page + 1);
        } else {
          huddleHasNext = false;
          huddlePaginationController.appendLastPage(tempList);
        }
      } else {
        huddlePaginationController.appendLastPage([]);
      }
    }
  }

  MorningHuddleModel? morningHuddleModel;
  Future<void> getHuddleOverview({required int huddleID}) async {
    try {
      isLoading = true;
      Response response = await _dio.get(
          '$dailyTaskAllocationURL$huddleID/overview/',
          options: Options(
              headers: await getHeaders(), validateStatus: (status) => true));
      isLoading = false;

      log('getHuddleOverview - ${response.data} - ${response.realUri} - ${response.statusCode}');
      if (response.statusCode == 200) {
        Map<String, dynamic> json = response.data;
        if (json.containsKey('data')) {
          morningHuddleModel = MorningHuddleModel.fromJson(json['data']);
        }
      } else {
        morningHuddleModel = null;
      }
    } catch (e) {
      morningHuddleModel = null;
      debugPrint(e.toString());
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> updateHuddle({required int huddleID}) async {
    try {
      Map<String, dynamic> data = {};
      if (editHuddleEntry != null) {
        if (editHuddleEntry!.projectController.text.isEmpty) {
          showToastText('Please select a project');
          return false;
        }
        if (editHuddleEntry!.taskController.text.isEmpty) {
          showToastText('Please select planned task');
          return false;
        }
        if (editHuddleEntry!.timeController.text.isEmpty) {
          showToastText('Please enter estimated time');
          return false;
        }

        data['project'] = editHuddleEntry?.projectController.text.trim();
        data['planned_task'] = editHuddleEntry?.taskController.text.trim();
        if (editHuddleEntry?.reportingPerson != null) {
          data['reporting_person'] = editHuddleEntry?.reportingPerson?.trim();
        }
        data['estimated_time'] = editHuddleEntry?.timeController.text.trim();
        if (editHuddleEntry!.timeController.text
            .trim()
            .toLowerCase()
            .contains('hr')) {
          data['estimated_time'] = editHuddleEntry?.timeController.text
              .trim()
              .toLowerCase()
              .split('hr')
              .first
              .trim();
        }
        if (editHuddleEntry!.remarkController.text.isNotEmpty) {
          data['remarks'] = editHuddleEntry?.remarkController.text.trim();
        }
      }
      log('data = $data');
      FormData formData = FormData.fromMap(data);
      isLoading = true;
      Response response = await _dio.put(
          data: formData,
          '$dailyTaskAllocationURL$huddleID/update/',
          options: Options(
              headers: await getHeaders(), validateStatus: (status) => true));
      isLoading = false;
      log('updateHuddle - ${response.data} - ${response.realUri} - ${response.statusCode}');
      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          Map<String, dynamic> data = response.data;
          if (data.containsKey('message')) {
            showToastText(data['message']);
          }
        }
        return true;
      } else {
        return false;
      }
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      isLoading = false;
    }
    return false;
  }

  Future<bool> deleteHuddle({required int huddleID, required int index}) async {
    if (isLoading) return false;
    try {
      isLoading = true;
      Response response = await _dio.patch(
          '$dailyTaskAllocationURL$huddleID/delete/',
          options: Options(
              headers: await getHeaders(), validateStatus: (status) => true));
      isLoading = false;

      log('deleteHuddle - ${response.data} - ${response.realUri} - ${response.statusCode}');
      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          Map<String, dynamic> data = response.data;
          if (data.containsKey('message')) {
            showToastText(data['message']);
          }
        }
        if (huddlePaginationController.itemList != null &&
            huddlePaginationController.itemList!.isNotEmpty) {
          if (huddleHasNext &&
              huddlePaginationController.itemList?.length == 10) {
            getHuddles(page: huddleCurrentPage + 1);
          }
          huddlePaginationController.itemList?.removeAt(index);
        }
        notifyListeners();
        return true;
      } else {}
    } catch (e) {
      debugPrint(e.toString());
      return false;
    } finally {
      isLoading = false;
    }
    return false;
  }
}
