// ignore_for_file: use_build_context_synchronously
import 'dart:async';
import 'dart:io';
import 'package:app_links/app_links.dart';
import 'package:e8_hr_portal/model/badge_model.dart';
import 'package:e8_hr_portal/provider/badge_provider.dart';
import 'package:e8_hr_portal/provider/ble_attendance_provider.dart';
import 'package:e8_hr_portal/provider/booked_meeting_room_provider.dart';
import 'package:e8_hr_portal/provider/checklist_provider.dart';
import 'package:e8_hr_portal/provider/version_provider.dart';
import 'package:e8_hr_portal/view/badges/badges_screen.dart';
import 'package:e8_hr_portal/view/badges/utils/unlocked_badge_bottom_sheet_util.dart';
import 'package:e8_hr_portal/view/flight_tickets/flight_ticket_details_view_new.dart';
import 'package:e8_hr_portal/view/flight_tickets/flight_ticket_upload_new_screen.dart';
import 'package:e8_hr_portal/view/latest_updates/update_detailed_screen.dart';
import 'package:e8_hr_portal/view/master/widgets/app_bar_title.dart';
import 'package:e8_hr_portal/view/master/widgets/master_action_button.dart';
import 'package:e8_hr_portal/view/master/widgets/master_bottom_bar.dart';
import 'package:e8_hr_portal/view/master/widgets/menu_dawer_button.dart';
import 'package:e8_hr_portal/view/team_member/team_member_screen.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/provider/birthday_anniversary_provider.dart';
import 'package:e8_hr_portal/provider/company_activities_provider.dart';
import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/provider/notification_provider.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/provider/salary_certificate_provider.dart';
import 'package:e8_hr_portal/provider/team_members_provider.dart';
import 'package:e8_hr_portal/provider/tickets_provider.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/provider/user_status_provider.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/dashboard/dashboard_screen.dart';
import 'package:e8_hr_portal/view/latest_updates/latest_updates_screen.dart';
import 'package:e8_hr_portal/view/leave%20applications/leave_application.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/view/leave_request_for_reporting_person/leave_request_overview.dart';
import 'package:e8_hr_portal/view/notification/notification_screen.dart';
import 'package:e8_hr_portal/view/other_screens/company_activities/company_activities.dart';
import 'package:provider/provider.dart';
import 'package:e8_hr_portal/view/master/menu_drawer.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:upgrader/upgrader.dart';
import '../../provider/attendance_provider.dart';
import '../../provider/experience_certifcate_provider.dart';
import '../../provider/flight_ticket_provider.dart';
import '../../provider/book_meeting_room_provider.dart';
import '../../provider/noc_provider.dart';
import '../../provider/salary_transfer_letter_provider.dart';
import '../attendance/attendance_screen.dart';
import '../certificate_requests/experience_certificate/experience_certificate.dart';
import '../certificate_requests/non_objection_certificates/non_objection_certificates.dart';
import '../certificate_requests/salary_certificate/salary_certificate.dart';
import '../certificate_requests/salary_transfer_certificate/salary_transfer_certificate.dart';
import '../flight_tickets/flight_tickets_list_screen.dart';
import '../meeting_room_hisence/meeting/meeting_requests/meeting_join_requests_screen.dart';
import '../meeting_room_hisence/meeting/meeting_requests/meeting_request_screen.dart';
import '../meeting_room_hisence/meeting/meeting_room_booked_status/meeting_room_booked_status_screen.dart';
import '../other_screens/overview/screens/overview_screen.dart';
import '../tickets_screens/tickets_overview.dart';
import 'package:e8_hr_portal/model/leave_overview_model.dart';

class MasterScreen extends StatefulWidget {
  const MasterScreen({super.key});

  @override
  State<MasterScreen> createState() => _MasterScreenState();
}

class _MasterScreenState extends State<MasterScreen> {
  final _scaffoldKey = GlobalKey<ScaffoldState>();
  late MasterProvider _masterProvider;
  @override
  void initState() {
    order();
    _handleIncomingLinks();
    _handleInitialUri();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<MasterProvider>();
    Widget? leading;
    if (provider.currentIndex != 0) {
      leading = MenuDrawerButton(_scaffoldKey.currentState);
    }
    // UpgradeDialogStyle dialogStyle = UpgradeDialogStyle.material;
    if (Platform.isIOS) {
      // dialogStyle = UpgradeDialogStyle.cupertino;
    }
    return Stack(
      children: [
        Container(
          height: 250,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              stops: const [0.0, 1.0],
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.secondary
              ],
            ),
          ),
        ),
        WillPopScope(
          onWillPop: () async {
            exitApp(context);
            return false;
          },
          child: UpgradeAlert(
            upgrader: Upgrader(
                // // debugDisplayAlways: true,
                // // debugDisplayOnce: true,
                // shouldPopScope: () => true,
                // canDismissDialog: false,
                // showIgnore: false,
                // showLater: false,
                // durationUntilAlertAgain: const Duration(days: 1),
                // upgraderDevice: UpgraderDevice(  ),
                // dialogStyle: dialogStyle,
                ),
            child: Scaffold(
              key: _scaffoldKey,
              backgroundColor: Colors.transparent,
              drawer: MenuDrawer(),
              appBar: AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                actions: const [MasterActionButton()],
                toolbarHeight: provider.currentIndex == 0 ? 150 : 80,
                automaticallyImplyLeading: false,
                leading: leading,
                title: AppBarTitle(_scaffoldKey.currentState),
              ),
              body: IndexedStack(
                index: provider.currentIndex,
                children: const [
                  DashboardScreen(),
                  TeamMemberScreen(),
                  LeaveApplicationScreen(),
                  LatestUpdatesScreen(isFromHome: true)
                ],
              ),
              bottomNavigationBar: const MasterBottomBar(),
            ),
          ),
        ),
      ],
    );
  }

  order() async {
    EasyLoading.show();
    final profileProvider = context.read<ProfileProvider>();
    final attendanceProvider = context.read<AttendanceProvider>();
    final bleAttendanceProvider = context.read<BLEAttendanceProvider>();
    final checklistPorvider = context.read<ChecklistProvider>();
    final versionProvider = context.read<VersionProvider>();
    await versionProvider.getAppInfo();
    profileProvider.getProfileData(context: context);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      attendanceProvider.selectedMonth = DateTime.now();
      attendanceProvider.getAttendanceList(master: true, context: context);
    });

    await bleAttendanceProvider.getAttendanceLogs();

    await checklistPorvider.getChecklistCount();

    var userStatus = Provider.of<UserStatusProvider>(context, listen: false);
    userStatus.getUserStatus(master: true);
    await Provider.of<UpdatesProvider>(context, listen: false)
        .getFromSharedPref();
    await Provider.of<UpdatesProvider>(context, listen: false)
        .getUpdates(context: context);
    await Provider.of<TeamMembersProvider>(context, listen: false)
        .getTeamMembers(isFilter: false, master: true, context: context);
    Provider.of<TeamMembersProvider>(context, listen: false)
        .clearSelectedFilter();
    await Provider.of<NotificationProvider>(context, listen: false)
        .getNotificationCount();
    await Provider.of<TeamMembersProvider>(context, listen: false)
        .getDesignation(master: true);
    await Provider.of<LeaveApplicationProvider>(context, listen: false)
        .getPendingLeaves();

    await Provider.of<LeaveApplicationProvider>(context, listen: false)
        .initPageLeaveRecords(master: true);
    await Provider.of<LeaveApplicationProvider>(context, listen: false)
        .getLeaveBalance(master: true, context: context);
    await Provider.of<LeaveApplicationProvider>(context, listen: false)
        .getLeaveAppliedEmployees();

    await Provider.of<TeamMembersProvider>(context, listen: false)
        .getBranchList(master: true);
    await Provider.of<BirthdayAnniversaryProvider>(context, listen: false)
        .getBirthdayAnniversary(master: true, context: context);
    await Provider.of<CompanyActivitiesProvider>(context, listen: false)
        .getRecentActivity(master: true, context: context);

    userStatus.selectedUserStatus = null;
    _masterProvider = Provider.of<MasterProvider>(context, listen: false);
    _masterProvider.init();
    initMessaging(context);

    await EasyLoading.dismiss();

    // SVProgressHUD.dismiss();
  }

  /// Handle incoming links - the ones that the app will recieve from the OS
  /// while already started.
  void _handleIncomingLinks() {
    if (!kIsWeb) {
      AppLinks appLinks = AppLinks();
      // It will handle app links while the app is already started - be it in
      // the foreground or in the background.
      appLinks.uriLinkStream.listen((Uri? uri) async {
        debugPrint('got uri: $uri');
        if (!mounted) return;
        debugPrint('got uri: $uri');

        if (uri != null) {
          if (uri.toString().contains('/?post=')) {
            openPostDetails(uri);
          }

          debugPrint('got initial uri: $uri');
        }
      }, onError: (Object err) {
        if (!mounted) return;
        debugPrint('got err: $err');
      });
    }
  }

  /// Handle the initial Uri - the one the app was started with
  ///
  /// **ATTENTION**: `getInitialLink`/`getInitialUri` should be handled
  /// ONLY ONCE in your app's lifetime, since it is not meant to change
  /// throughout your app's life.
  ///
  /// We handle all exceptions, since it is called from initState.
  Future<void> _handleInitialUri() async {
    // In this example app this is an almost useless guard, but it is here to
    // show we are not going to call getInitialUri multiple times, even if this
    // was a weidget that will be disposed of (ex. a navigation route change).
    // if (!_initialUriIsHandled) {
    //   _initialUriIsHandled = true;
    //   // _showSnackBar('_handleInitialUri called');
    //   try {
    //     final uri = await getInitialUri();
    //     // debugPrint('got uri: $uri');
    //     if (uri == null) {
    //       // debugPrint('no initial uri');
    //     } else {
    //       if (uri.toString().contains('/?post=')) {
    //         openPostDetails(uri);
    //       }
    //       debugPrint('got initial uri: $uri');
    //     }
    //     if (!mounted) return;
    //   } on PlatformException {
    //     // Platform messages may fail but we ignore the exception
    //     debugPrint('falied to get initial uri');
    //   } on FormatException catch (err) {
    //     if (!mounted) return;
    //     debugPrint('malformed initial uri $err');
    //   }
    // }
  }

  openPostDetails(Uri? uri) async {
    List<String> split = uri.toString().split('/?post=');
    var postId = int.parse(split.last);
    var provider = Provider.of<UpdatesProvider>(context, listen: false);
    await provider.getPostDetails(postId: postId);
    if (provider.postList.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UpdateDetailedScreen(
            post: provider.postList.first,
          ),
        ),
      );
    }
  }

  // getSharedDate() async {
  //   SharedPreferences shared = await SharedPreferences.getInstance();
  //   setState(() {
  //     name = shared.getString('name');
  //   });
  // }

  void initMessaging(BuildContext context) async {
    await getFirebaseMessages();
    FirebaseMessaging.onMessage.listen(sendLocalNotification);
    // FirebaseMessaging.onBackgroundMessage((message) => null)
    // FirebaseMessaging.onMessageOpenedApp.listen(sendLocalNotification);
    FirebaseMessaging.onMessageOpenedApp
        .listen((RemoteMessage message) async => await _handleMessage(message));
  }

  getFirebaseMessages() async {
    RemoteMessage? initialMsg =
        await FirebaseMessaging.instance.getInitialMessage();
    if (initialMsg != null) {
      await _handleMessage(initialMsg);
    }
  }

  sendLocalNotification(RemoteMessage message) async {
    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();
    AndroidInitializationSettings initializationSettingsAndroid =
        const AndroidInitializationSettings('@mipmap/ic_launcher');
    final DarwinInitializationSettings initializationSettingsDarwin =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestSoundPermission: true,
      // onDidReceiveLocalNotification: (id, title, body, payload) =>
      //     _handleMessage(message),
    );
    final InitializationSettings initializationSettings =
        InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsDarwin);
    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (details) => _handleMessage(message),
    );

    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'high_importance_channel', // id
      'High Importance Notifications', // titledescription
      importance: Importance.max,
    );
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
    RemoteNotification notification = message.notification!;

    AndroidNotificationDetails? androidNotificationDetails;
    DarwinNotificationDetails? iOSNotificationDetails;
    if (Platform.isAndroid) {
      AndroidNotification android = message.notification!.android!;
      androidNotificationDetails = AndroidNotificationDetails(
        channel.id,
        channel.name,
        icon: android.smallIcon,
      );
    } else if (Platform.isIOS) {
      iOSNotificationDetails = const DarwinNotificationDetails();
    }

    // If `onMessage` is triggered with a notification, construct our own
    // local notification to show to users using the created channel.
    flutterLocalNotificationsPlugin.show(
      notification.hashCode,
      notification.title,
      notification.body,
      NotificationDetails(
        android: androidNotificationDetails,
        iOS: iOSNotificationDetails,
      ),
    );
  }

  FirebaseMessaging messaging = FirebaseMessaging.instance;
  _handleMessage(RemoteMessage message) async {
    if (message.data.containsKey('action')) {
      String screen = message.data['action'].toString().toLowerCase();
      int? actionId;
      if (message.data.containsKey('action_id')) {
        actionId = int.parse(message.data['action_id']);
      }
      switch (screen) {
        case 'post':
          final provider = Provider.of<UpdatesProvider>(context, listen: false);
          provider.getPostDetails(postId: actionId!.toInt());
          provider.getComments(postId: actionId.toInt());
          if (provider.postList.isNotEmpty) {
            var post = provider.postList.first;
            PageNavigator.push(
              context: context,
              route: UpdateDetailedScreen(
                color: Colors.white,
                post: post,
              ),
            );
          }

          break;
        case 'leave_status_user':
          final provider =
              Provider.of<LeaveApplicationProvider>(context, listen: false);

          EasyLoading.show();
          bool isGo = await provider.getLeaveOverView(leaveId: actionId);
          EasyLoading.dismiss();
          if (isGo) {
            PageNavigator.push(
              context: context,
              route: const OverViewScreen(),
            );
          }

          break;
        case 'leave_request':
          final provider =
              Provider.of<LeaveApplicationProvider>(context, listen: false);
          if (actionId != null) {
            EasyLoading.show();
            bool isGo = await provider
                .getRequestedLeaveOverviewForReportedPerson(leaveId: actionId);
            EasyLoading.dismiss();
            if (isGo) {
              PageNavigator.push(
                context: context,
                route: const LeaveRequestOverView(),
              );
            }
          }
          break;
        case 'approved':
          if (actionId != null) {
            final provid =
                Provider.of<LeaveApplicationProvider>(context, listen: false);
            EasyLoading.show();
            bool isGo = await provid.getLeaveOverView(leaveId: actionId);
            bool isGetRepPerson = await provid.getReportingPerson();
            EasyLoading.dismiss();

            if (isGo && isGetRepPerson) {
              PageNavigator.push(
                  context: context, route: const OverViewScreen());
            }
          }
          break;
        case 'rejected':
          if (actionId != null) {
            final provid =
                Provider.of<LeaveApplicationProvider>(context, listen: false);
            EasyLoading.show();
            bool isGo = await provid.getLeaveOverView(leaveId: actionId);
            bool isGetRepPerson = await provid.getReportingPerson();
            EasyLoading.dismiss();

            if (isGo && isGetRepPerson) {
              PageNavigator.push(
                  context: context, route: const OverViewScreen());
            }
          }

          break;
        case 'leave_request_cancelled':
          final provider =
              Provider.of<LeaveApplicationProvider>(context, listen: false);
          if (actionId != null) {
            EasyLoading.show();
            bool isGo = await provider
                .getRequestedLeaveOverviewForReportedPerson(leaveId: actionId);
            EasyLoading.dismiss();
            if (isGo) {
              PageNavigator.push(
                context: context,
                route: const LeaveRequestOverView(),
              );
            }
          }
          break;
        case 'attendance_request':
          Provider.of<ProfileProvider>(context, listen: false)
              .getProfileData(context: context);
          var provider = context.read<AttendanceProvider>();
          provider.selectedMonth = DateTime.now();
          provider.getAttendanceList(master: false, context: context);
          EasyLoading.show();
          BLEAttendanceProvider provider1 =
              Provider.of<BLEAttendanceProvider>(context, listen: false);
          await provider1.getAttendanceLogs();
          EasyLoading.dismiss();
          PageNavigator.push(
            context: context,
            route: const AttendanceScreen(),
          );
          break;
        case 'ticket_status':
          TicketsProvider provider =
              Provider.of<TicketsProvider>(context, listen: false);
          if (actionId != null) {
            bool isGo = await provider.getTicketOverview(ticketID: actionId);

            if (isGo) {
              PageNavigator.push(
                context: context,
                route: const TicketsOverview(),
              );
            }
          }
          break;
        case 'salary_certificates':
          Provider.of<SalaryCertificateProvider>(context, listen: false)
              .getSalaryCertificate();
          Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => const SalaryCertificate()));
          break;
        case 'salary_transfer_letter':
          SalaryTransferLetterProvider provider =
              Provider.of<SalaryTransferLetterProvider>(context, listen: false);
          provider.fetchPreviousRequests();
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const SalaryTransferCertificate(),
            ),
          );
          break;
        case 'experience_certificates':
          var provider =
              Provider.of<ExperienceCertifcateProvider>(context, listen: false);

          provider.fetchPreviousRequests();

          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const ExperienceCertificate(),
            ),
          );
          break;
        case 'nonobjection_certificates':
          Provider.of<FlightTicketProvider>(context, listen: false)
              .getFlightTicketPersonalInfo();
          final navigator = Navigator.of(context);
          NocProvider provider =
              Provider.of<NocProvider>(context, listen: false);

          provider.getNocCertificateList();

          navigator.push(MaterialPageRoute(
              builder: (context) => const NonObjectionCertificates()));
          break;
        case 'flight_tickets':
          var provider =
              Provider.of<FlightTicketProvider>(context, listen: false);
          provider.getAllCountries();
          // bool isGo = await provider.getFlightTicketList();
          provider.currentPage = 0;
          provider.pagingController?.refresh();

          EasyLoading.show();
          // if (isGo) {
          EasyLoading.dismiss();
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const FlightTicketsListScreen(),
            ),
          );
          // }
          break;

        case 'meeting_room_request':
          BookedMeetingRoomProvider provider =
              Provider.of<BookedMeetingRoomProvider>(context, listen: false);
          provider.currentIndex = 0;
          provider.getMeetingRoomRequestForAdmin(status: '1');
          BookMeetingRoomProvider provider1 =
              Provider.of<BookMeetingRoomProvider>(context, listen: false);
          provider1.getTeamMembers(context: context);
          PageNavigator.push(
            context: context,
            route: const MeetingRequestsScreen(),
          );
          break;
        case 'meeting_room_user':
          BookedMeetingRoomProvider provider =
              Provider.of<BookedMeetingRoomProvider>(context, listen: false);
          provider.getBookedMeetingRoomStatus();
          BookMeetingRoomProvider provider1 =
              Provider.of<BookMeetingRoomProvider>(context, listen: false);
          provider1.getTeamMembers(context: context);
          PageNavigator.push(
              context: context, route: MeetingRoomBookedStatusScreen());
          break;
        case 'meeting_room_cancel':
          BookedMeetingRoomProvider provider =
              Provider.of<BookedMeetingRoomProvider>(context, listen: false);
          provider.currentIndex = 0;
          provider.getMeetingRoomRequestForAdmin(status: '1');
          BookMeetingRoomProvider provider1 =
              Provider.of<BookMeetingRoomProvider>(context, listen: false);
          provider1.getTeamMembers(context: context);
          PageNavigator.push(
            context: context,
            route: const MeetingRequestsScreen(),
          );
          break;
        case 'meeting_room_invitation':
          BookedMeetingRoomProvider provider =
              Provider.of<BookedMeetingRoomProvider>(context, listen: false);
          provider.getMeetingInviteRequestForMember();

          PageNavigator.push(
            context: context,
            route: const MeetingJoinRequestsScreen(),
          );

          break;
        case 'notifications':
          PageNavigator.push(
            context: context,
            route: const NotificationScreen(),
          );
          break;
        case 'news_feed':
          PageNavigator.push(
            context: context,
            route: const LatestUpdatesScreen(),
          );
          break;

        case 'permission':
          Provider.of<MasterProvider>(context, listen: false).currentIndex = 3;
          PageNavigator.push(
            context: context,
            route: const MasterScreen(),
          );
          break;
        case 'birthday_wish':
          PageNavigator.push(
            context: context,
            route: const CompanyActivities(),
          );
          break;
        case 'survey':
          UpdatesProvider provider =
              Provider.of<UpdatesProvider>(context, listen: false);
          provider.getUpdates(context: context);
          PageNavigator.pushSlideup(
            context: context,
            route: const LatestUpdatesScreen(),
          );
          break;
        case 'badge':
          BadgeProvider provider =
              Provider.of<BadgeProvider>(context, listen: false);
          provider.badgeSelectedFilter = provider.badgeFilterList.first;
          await provider.getBadgesWithFilter(context: context);
          Navigator.push(context,
              CupertinoPageRoute(builder: (context) => const BadgesScreen()));
          if (actionId != null) {
            await provider.getSingleBadge(id: actionId, context: context);
            BadgeModel? badgeModel = provider.badgeModel;
            if (badgeModel != null) {
              UnlockedBadgeBottomSheetUtils.showBottomSheet(
                  context: context, badgeModel: badgeModel);
            }
          }
          break;
        case 'flight_tkt_upload_req':
          var flightTicketProvider = context.read<FlightTicketProvider>();
          if (isRedundentClick(DateTime.now())) {
            return;
          }
          // provider.getFlightTicketOverview();
          await flightTicketProvider.getFlightTicketOverview(
              ticketId: actionId.toString());

          bool isGo = await flightTicketProvider.getFlightTicketPersonalInfo(
            userId:
                flightTicketProvider.flightTicketOverviewModel?.data?.userId,
          );

          await flightTicketProvider.getFlightTicketLeveList();
          await flightTicketProvider.getFlightTicketCount(
              userId:
                  flightTicketProvider.flightTicketOverviewModel?.data?.userId);
          // provider.selectedFlightTicketLeaveModel = '21'
          if (isGo) {
            if (!context.mounted) return;
            PageNavigator.push(
              context: context,
              route: FLightTicketUploadNewScreen(
                  flightTicketOverviewModel:
                      flightTicketProvider.flightTicketOverviewModel),
            );
          }
          break;
        case 'flight_ticket_request':
          var flightTicketProvider = context.read<FlightTicketProvider>();
          // provider.getFlightTicketOverview();
          await flightTicketProvider.getFlightTicketOverview(
              ticketId: actionId.toString());

          bool isGo = await flightTicketProvider.getFlightTicketPersonalInfo(
            userId:
                flightTicketProvider.flightTicketOverviewModel?.data?.userId,
          );

          await flightTicketProvider.getFlightTicketLeveList();
          await flightTicketProvider.getFlightTicketCount(
              userId:
                  flightTicketProvider.flightTicketOverviewModel?.data?.userId);

          // provider.selectedFlightTicketLeaveModel = '21'
          if (isGo) {
            if (!context.mounted) return;
            PageNavigator.push(
              context: context,
              route: FlightTicketDetailsViewNew(
                  isForEdit: true,
                  isFromLevels: true,
                  flightTicketOverviewModel:
                      flightTicketProvider.flightTicketOverviewModel),
            );
          }
          break;

        case 'flight_tkt_approval':
          var flightTicketProvider = context.read<FlightTicketProvider>();
          // provider.getFlightTicketOverview();
          await flightTicketProvider.getFlightTicketOverview(
              ticketId: actionId.toString());

          bool isGo = await flightTicketProvider.getFlightTicketPersonalInfo(
            userId:
                flightTicketProvider.flightTicketOverviewModel?.data?.userId,
          );

          await flightTicketProvider.getFlightTicketLeveList();
          await flightTicketProvider.getFlightTicketCount(
              userId:
                  flightTicketProvider.flightTicketOverviewModel?.data?.userId);

          // provider.selectedFlightTicketLeaveModel = '21'
          if (isGo) {
            if (!context.mounted) return;
            PageNavigator.push(
              context: context,
              route: FlightTicketDetailsViewNew(
                  isForEdit: true,
                  isFromLevels: true,
                  flightTicketOverviewModel:
                      flightTicketProvider.flightTicketOverviewModel),
            );
          }
          break;
        case 'flight_tkt_reject':
          var flightTicketProvider = context.read<FlightTicketProvider>();
          // provider.getFlightTicketOverview();
          await flightTicketProvider.getFlightTicketOverview(
              ticketId: actionId.toString());

          bool isGo = await flightTicketProvider.getFlightTicketPersonalInfo();

          await flightTicketProvider.getFlightTicketLeveList();
          await flightTicketProvider.getFlightTicketCount();

          if (isGo) {
            if (!context.mounted) return;
            PageNavigator.push(
              context: context,
              route: FlightTicketDetailsViewNew(
                  isForEdit: true,
                  flightTicketOverviewModel:
                      flightTicketProvider.flightTicketOverviewModel),
            );
          }
          break;
        case 'flight_tkt_upload':
          var flightTicketProvider = context.read<FlightTicketProvider>();
          await flightTicketProvider.getFlightTicketOverview(
              ticketId: actionId.toString());

          bool isGo = await flightTicketProvider.getFlightTicketPersonalInfo();

          await flightTicketProvider.getFlightTicketLeveList();
          await flightTicketProvider.getFlightTicketCount();

          // provider.selectedFlightTicketLeaveModel = '21'
          if (isGo) {
            if (!context.mounted) return;
            PageNavigator.push(
              context: context,
              route: FlightTicketDetailsViewNew(
                  isForEdit: true,
                  flightTicketOverviewModel:
                      flightTicketProvider.flightTicketOverviewModel),
            );
          }
          break;
        case 'checklist':
          if (isRedundentClick(DateTime.now())) {
            return;
          }
          // log('message');
          // var provider = context.read<ChecklistProvider>();
          // await provider.getShedulers();
          // await provider.getChecklist();
          // PageNavigator.push(context: context, route: const ChecklistScreen());
          break;
        case 'checklist_history':
          // final provider = context.read<ChecklistProvider>();
          // EasyLoading.show();
          // await provider.getChecklistHistory();
          // PageNavigator.push(
          //     context: context, route: const ChecklistHistoryScreen());

          break;
      }
    }
  }
}
