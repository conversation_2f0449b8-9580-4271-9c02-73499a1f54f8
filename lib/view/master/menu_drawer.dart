// ignore_for_file: use_build_context_synchronously
import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/main.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/provider/ble_attendance_provider.dart';
import 'package:e8_hr_portal/provider/flight_ticket_provider.dart';
import 'package:e8_hr_portal/provider/log_time_provider.dart';
import 'package:e8_hr_portal/provider/book_meeting_room_provider.dart';
import 'package:e8_hr_portal/provider/booked_meeting_room_provider.dart';
import 'package:e8_hr_portal/provider/meeting_room_provider.dart';
import 'package:e8_hr_portal/provider/reimbursment_provider.dart';
import 'package:e8_hr_portal/provider/wfh_provider.dart';
import 'package:e8_hr_portal/util/capitalize.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:e8_hr_portal/view/ble_attendance/work_from_home_section/wfh_employees_list_screen.dart';
import 'package:e8_hr_portal/view/ble_attendance/work_from_home_section/wfh_permission_screen.dart';
import 'package:e8_hr_portal/view/checklist/checklist_screen.dart';
import 'package:e8_hr_portal/view/flight_tickets/flight_ticket_approved_list.dart';
import 'package:e8_hr_portal/view/flight_tickets/flight_ticket_request_level_new.dart';
import 'package:e8_hr_portal/view/master/widgets/menu_list_tile.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/meeting_rooms/meeting_rooms.dart';
import 'package:e8_hr_portal/view/meeting_room_hisence/meeting/scheduled_meetings/scheduled_meeting_screen.dart';
import 'package:e8_hr_portal/view/other_screens/admin_leave_request_screen/admin_leave_reqest.dart';
import 'package:e8_hr_portal/view/profile/profile_screen.dart';
import 'package:e8_hr_portal/view/reimbursment/reporting_person/reimbursement_requests_screen_for_reporting_person.dart';
import 'package:e8_hr_portal/view/reimbursment/user/reimbursment_listing_screen.dart';
import 'package:e8_hr_portal/view/task_management/task_management.dart';
import 'package:e8_hr_portal/view/wfh/view/apply_wfh_screen.dart';
import 'package:e8_hr_portal/view/chat_bot/chat_screen_wrapper.dart';
import 'package:e8_hr_portal/view/wfh/view/wfh_records_screen.dart';
import 'package:e8_hr_portal/view/work_updates.dart/work_status_bottomsheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:e8_hr_portal/provider/company_activities_provider.dart';
import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/provider/medical_insurance_provider.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/provider/sign_in_provider.dart';
import 'package:e8_hr_portal/provider/team_members_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/employee_relations/medical_insurance.dart';
import 'package:e8_hr_portal/view/flight_tickets/flight_tickets_list_screen.dart';
import 'package:e8_hr_portal/view/leave_request_for_reporting_person/leave_request_for_reporting_person.dart';
import 'package:e8_hr_portal/view/other_screens/new_leave/new_leave.dart';
import 'package:e8_hr_portal/view/sign_in/login_with_email_screen.dart';
import 'package:e8_hr_portal/view/upcoming_holidays/upcoming_holidays.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../provider/attendance_provider.dart';
import '../../provider/faq_provider.dart';
import '../../provider/version_provider.dart';
import '../attendance/attendance_screen.dart';
import '../feedback_and_complaints/feedback_screen.dart';
import '../leave applications/provider/leave_apllication_provider.dart';
import '../morning_huddle/view/morning_huddle_listing_screen.dart';
import '../reimbursment/hr/reimbursement_requests_screen_for_hr.dart';
import '../wfh/view/wfh_requests_screen.dart';

class MenuDrawer extends StatelessWidget {
  MenuDrawer({super.key});
  final _divider = const Padding(
    padding: EdgeInsets.symmetric(vertical: 5),
    child: Divider(
      color: Color(0xFFF3F3F3),
      thickness: 1,
      height: 0,
      endIndent: 20,
      indent: 20,
    ),
  );
  final GoogleSignIn _googleSignIn = GoogleSignIn(scopes: ['email']);
  @override
  Widget build(BuildContext context) {
    final versionProvider = context.read<VersionProvider>();
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    // final provider = Provider.of<ProfileProvider>(context, listen: false);

    return Drawer(
      backgroundColor: Colors.white,
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child: ListView(
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 25 * h),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.vertical(
                          bottom: Radius.circular(25.0),
                        ),
                      ),
                      child: Consumer<ProfileProvider>(
                          builder: (context, provider, child) {
                        return Column(
                          children: [
                            SizedBox(
                                height: MediaQuery.of(context).padding.top),
                            InkWell(
                              onTap: () {
                                Provider.of<ProfileProvider>(context,
                                        listen: false)
                                    .getProfileData(context: context);
                                Navigator.pop(context);
                                Navigator.of(context).push(MaterialPageRoute(
                                    builder: (context) =>
                                        const ProfileScreen()));
                              },
                              child: Stack(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(10),
                                    child: ClipOval(
                                      child: SizedBox.fromSize(
                                        size: const Size.fromRadius(48),
                                        child: CachedNetworkImage(
                                          width: 105 * w,
                                          height: 105 * h,
                                          imageUrl:
                                              '${provider.profilePic}?${DateTime.now().millisecondsSinceEpoch}',
                                          fit: BoxFit.cover,
                                          errorWidget: (context, url, error) {
                                            return Container(
                                              width: 105 * w,
                                              height: 105 * w,
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                    color: Colors.white),
                                                color: Theme.of(context)
                                                    .primaryColor,
                                              ),
                                              child: Text(
                                                provider.firstName != null
                                                    ? provider.firstName!
                                                        .substring(0, 1)
                                                        .toUpperCase()
                                                        .toUpperCase()
                                                    : '',
                                                style: GoogleFonts.poppins(
                                                    fontSize: 60,
                                                    fontWeight: FontWeight.w700,
                                                    color: Colors.white),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                  if (provider.userDetailesModel?.profileDetails
                                          ?.empStatus?.colorCode !=
                                      null)
                                    Positioned(
                                      right: 0,
                                      bottom: 10 * h,
                                      child: Container(
                                        height: 18 * h,
                                        width: 18 * w,
                                        decoration: BoxDecoration(
                                            boxShadow: [
                                              BoxShadow(
                                                  offset: Offset(2, 2),
                                                  blurRadius: 15,
                                                  spreadRadius: 0,
                                                  color: Color.fromRGBO(
                                                      0, 0, 0, 0.3))
                                            ],
                                            shape: BoxShape.circle,
                                            color: stringToColor(provider
                                                    .userDetailesModel
                                                    ?.profileDetails
                                                    ?.empStatus
                                                    ?.colorCode ??
                                                ""),
                                            border: Border.all(
                                                color: Colors.white, width: 1)),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 15),
                            FutureBuilder<String?>(
                              future: _getName(),
                              builder: (context, snapshot) {
                                if (snapshot.hasData && snapshot.data != null) {
                                  return Text(
                                    snapshot.data!,
                                    textAlign: TextAlign.center,
                                    overflow: TextOverflow.ellipsis,
                                    style: GoogleFonts.poppins(
                                      fontSize: 22,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.black,
                                    ),
                                  );
                                }
                                return const SizedBox();
                              },
                            ),
                            if (provider.designation != null)
                              Text(
                                provider.designation.toString(),
                                style: tsS12w400c656464,
                              ),
                            if (provider.uid != null)
                              Padding(
                                padding: const EdgeInsets.only(top: 5.0),
                                child: RichText(
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: 'EMP ID : ',
                                        style: GoogleFonts.poppins(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black,
                                        ),
                                      ),
                                      TextSpan(
                                        text: provider.employeeId.toString(),
                                        style: GoogleFonts.poppins(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            const SizedBox(height: 15),
                            Consumer<ProfileProvider>(
                              builder: (context, pro, child) {
                                return InkWell(
                                  onTap: () {
                                    pro.isChangeIcon = true;
                                    showModalBottomSheet<void>(
                                      useSafeArea: true,
                                      context: context,
                                      isDismissible: true,
                                      isScrollControlled: true,
                                      shape: const RoundedRectangleBorder(
                                        borderRadius: BorderRadius.vertical(
                                          top: Radius.circular(12),
                                        ),
                                      ),
                                      builder: (BuildContext context) {
                                        return const WorkStatusUpdateBottomSheet();
                                      },
                                    ).whenComplete(
                                      () {
                                        pro.isChangeIcon = false;
                                      },
                                    );
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16),
                                    height: h * 30,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(50),
                                      gradient: LinearGradient(
                                        colors: [
                                          Theme.of(context).colorScheme.primary,
                                          Theme.of(context)
                                              .colorScheme
                                              .secondary,
                                        ],
                                      ),
                                    ),
                                    child: DropdownButton(
                                      underline: const SizedBox(),
                                      items: null,
                                      onChanged: (value) {},
                                      hint: Text(
                                        pro.userDetailesModel?.profileDetails
                                                ?.empStatus?.name ??
                                            '',
                                        style: tsS12w500FFFFF,
                                      ),
                                      icon: !pro.isChangeIcon
                                          ? const ImageIcon(
                                              AssetImage(
                                                  'assets/icons/arrow.png'),
                                              color: Colors.white,
                                              size: 25)
                                          : Transform.rotate(
                                              angle: pi,
                                              child: const ImageIcon(
                                                  AssetImage(
                                                      'assets/icons/arrow.png'),
                                                  color: Colors.white,
                                                  size: 25),
                                            ),
                                    ),
                                  ),
                                );
                              },
                            ),
                            const SizedBox(height: 15),
                            Container(
                              height: 1 * h,
                              width: 258 * w,
                              color: ThemeColors.colorC9C9C9,
                            )
                          ],
                        );
                      }),
                    ),
                    // if (LoggedInUser.enableMeetingModule)
                    // ListTile(
                    //   onTap: () => PageNavigator.push(
                    //     context: context,
                    //     // route: const MeetingRequestsScreen(),
                    //     route: const ScheduledMeetingRoomScreen(),
                    //   ),
                    //   leading: ImageIcon(
                    //     const AssetImage('assets/icons/my_meetings.png'),
                    //     color: ThemeColors.color8391B5,
                    //   ),
                    //   title: Text(
                    //     'My Meetings',
                    //     style: GoogleFonts.poppins(
                    //       fontSize: 16,
                    //       color: ThemeColors.color8391B5,
                    //     ),
                    //   ),
                    // ),
                    // _divider,

                    Consumer<ProfileProvider>(
                        builder: (context, provider, child) {
                      return Theme(
                        data: ThemeData()
                            .copyWith(dividerColor: Colors.transparent),
                        child: ExpansionTile(
                          childrenPadding: const EdgeInsets.only(
                            left: 60,
                            bottom: 20,
                          ),
                          backgroundColor: Colors.white,
                          leading: Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: colorScheme.secondary.withOpacity(0.10),
                              // color: ThemeColors.colorFCC400.withOpacity(0.10),
                            ),
                            child: ImageIcon(
                              const AssetImage(
                                'assets/icons/leave_applications.png',
                              ),
                              color: colorScheme.secondary,
                            ),
                          ),
                          title:
                              Text('Employee Relations', style: tsS14w500Black),
                          children: <Widget>[
                            _submenuItem(
                              title: 'Morning Huddle',
                              onTap: () => _onMorningHuddleOpened(context),
                            ),
                            _submenuItem(
                              title: 'Apply Leave',
                              onTap: () => _onApplyLeaveOpened(context),
                            ),
                            _submenuItem(
                              title: 'Leave Records',
                              onTap: () => _onLeaveRecordsOpened(context),
                            ),
                            if (provider.userDetailesModel?.isReportingPerson ==
                                true)
                              _submenuItem(
                                title: 'Leave Requests',
                                onTap: () => _onLeaveRequestOpened(context),
                              ),
                            if (LoginModel.isAdmin == true)
                              _submenuItem(
                                title: 'Admin Leave Request',
                                onTap: () =>
                                    _onAdminLeaveRequestOpened(context),
                              ),
                            // _submenuItem(
                            //   title: 'Certificate & Letters',
                            //   onTap: () => _onCertificatesOpened(context),
                            // ),
                            _submenuItem(
                              title: 'Apply WFH',
                              onTap: () => _onApplyWFH(context: context),
                            ),
                            _submenuItem(
                              title: 'WFH Records',
                              onTap: () => _onWFHRecods(context: context),
                            ),
                            // if (provider.userDetailesModel?.isReportingPerson ==
                            //     true)
                            _submenuItem(
                              title: 'WFH Requests',
                              onTap: () => _onWFHRequests(context: context),
                            ),
                            _submenuItem(
                              title: 'Flight Tickets',
                              onTap: () {
                                _onFlightTicketsOpened(context);
                              },
                            ),
                            _submenuItem(
                              title: 'Flight Tickets request',
                              onTap: () {
                                _onFlightTicketsOpenedRequest(context);
                              },
                            ),
                            if (LoginModel.isAdmin != null &&
                                LoginModel.isAdmin!)
                              _submenuItem(
                                title: 'Flight Tickets upload',
                                onTap: () {
                                  _onFlightTicketsOpenedUpload(context);
                                },
                              ),
                            _submenuItem(
                              title: 'Attendance',
                              onTap: () async {
                                //
                                EasyLoading.show();
                                Provider.of<ProfileProvider>(context,
                                        listen: false)
                                    .getProfileData(context: context);
                                Navigator.pop(context);
                                var provider =
                                    context.read<AttendanceProvider>();
                                provider.selectedMonth = DateTime.now();
                                provider.getAttendanceList(
                                  master: false,
                                  context: context,
                                );

                                BLEAttendanceProvider provider1 =
                                    Provider.of<BLEAttendanceProvider>(context,
                                        listen: false);
                                await provider1.getAttendanceLogs();
                                EasyLoading.dismiss();
                                BuildContext? ctxt = NavigationService
                                    .navigatorKey.currentContext;
                                if (ctxt != null) {
                                  Navigator.of(ctxt).push(
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          const AttendanceScreen(),
                                    ),
                                  );
                                }
                              },
                            ),
                            // _submenuItem(
                            //   title: 'Attendance',
                            //   onTap: () {
                            //     Navigator.pop(context);
                            //     AttendanceProvider provider =
                            //         Provider.of<AttendanceProvider>(context,
                            //             listen: false);
                            //     provider.selectedMonth = formatDateFromDate(
                            //         dateTime: DateTime.now(), format: 'MMM');
                            //     provider.selectedYear =
                            //         DateTime.now().year.toString();
                            //     provider.initialDate = DateTime.now();
                            //     provider.isDateSelected = false;
                            //     provider.getAttendanceList(
                            //         master: false, context: context);

                            //     Navigator.of(context).push(MaterialPageRoute(
                            //         builder: (context) =>
                            //             const AttendanceScreen()));
                            //   },
                            // ),
                            _submenuItem(
                              title: 'Medical Insurance',
                              onTap: () => _onMedicalInsuranceOpened(context),
                            ),
                            _submenuItem(
                              title: 'Reimbursement',
                              onTap: () => _onReimbursmentOpened(context),
                            ),
                            if (LoginModel.isReimbursementReportingPerson ==
                                true)
                              _submenuItem(
                                title: 'Reimbursement Approval',
                                onTap: () =>
                                    _onReimbursmentApprovalOpened(context),
                              ),
                            if (LoginModel.isAdmin == true)
                              _submenuItem(
                                  title: 'Reimbursement Final Approval',
                                  onTap: () =>
                                      _onReimbursmentHRApprovalOpened(context)),
                            if (LoginModel.isAdmin == true ||
                                provider.designation == 'Project Manager')
                              _submenuItem(
                                  title: 'WFH Permission',
                                  onTap: () {
                                    Navigator.of(context).pop();
                                    WFHProvider provider =
                                        Provider.of<WFHProvider>(context,
                                            listen: false);
                                    provider.getAllEmployees();
                                    Navigator.of(context).push(
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                WFHPermissionScreen()));
                                  }),

                            if (LoginModel.isAdmin == true ||
                                provider.designation == 'Project Manager')
                              _submenuItem(
                                  title: 'WFH Employees',
                                  onTap: () {
                                    Navigator.of(context).pop();
                                    WFHProvider provider =
                                        Provider.of<WFHProvider>(context,
                                            listen: false);
                                    provider.selectedDate = DateTime.now();
                                    // provider.getAllWFHEmployees();
                                    Navigator.of(context).push(MaterialPageRoute(
                                        builder: (context) =>
                                            const WFHEmployeesListScreen()));
                                  }),
                          ],
                        ),
                      );
                    }),
                    // _divider,
                    _divider,
                    Consumer<MasterProvider>(
                      builder: (context, provider, child) {
                        return MenuListTile(
                          onTap: () => onEmployeeDirectoryTap(context),
                          assetIcon: 'assets/icons/members.png',
                          title: 'Employee Directory',
                        );
                      },
                    ),
                    _divider,
                    MenuListTile(
                      onTap: () => onTaskManagementTap(context),
                      assetIcon:
                          'assets/icons/clipboard_task_management_small.png',
                      title: 'Task Management',
                    ),
                    _divider,
                    MenuListTile(
                      onTap: () => onChecklistTap(context),
                      assetIcon: 'assets/icons/checklists.png',
                      title: 'Checklists',
                    ),
                    _divider,
                    MenuListTile(
                      onTap: () => onHolidaysTap(context),
                      assetIcon: 'assets/icons/holidays.png',
                      title: 'Holidays',
                    ),
                    _divider,
                    MenuListTile(
                      onTap: () => onMeetingRoomTap(context),
                      assetIcon: 'assets/icons/building.png',
                      title: 'Meeting Room',
                    ),
                    _divider,
                    MenuListTile(
                      onTap: () => onScheduledMeetingTap(context),
                      assetIcon: 'assets/icons/building.png',
                      title: 'Scheduled Meetings',
                    ),
                    _divider,
                    MenuListTile(
                      onTap: () => onProfileTap(context),
                      assetIcon: 'assets/icons/profile.png',
                      title: 'Profile',
                    ),
                    _divider,
                    MenuListTile(
                      onTap: () => onAIChatbotTap(context),
                      assetIcon: 'assets/icons/chat_bot_online.png',
                      title: 'AI Assistant',
                    ),
                    _divider,
                    MenuListTile(
                      onTap: () => onFeedbackComplaintsTap(context),
                      assetIcon: 'assets/icons/feedback.png',
                      title: 'Feedback & Complaints',
                    ),
                    _divider,
                    MenuListTile(
                      onTap: () => logout(context: context),
                      assetIcon: 'assets/icons/logout.png',
                      title: 'Logout',
                    ),
                    _divider,
                    SizedBox(
                      height: 20 * h,
                    ),
                    // ---------------Login web-------------------

                    Container(
                      margin: const EdgeInsets.only(
                          top: 5, left: 10, right: 10, bottom: 10),
                      padding: const EdgeInsets.only(top: 20, bottom: 20),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.primary,
                            Theme.of(context).colorScheme.secondary,
                          ],
                        ),
                      ),
                      child: Column(children: [
                        Text(
                          'Want to Explore More',
                          style: tsS16FFFFF,
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        Text(
                          'Login to our web portal',
                          style: tsS12wRcWhite,
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        InkWell(
                          onTap: () async {
                            if (!await launchUrl(
                              Uri.parse(baseUrl),
                            )) {
                              throw Exception('Could not launch');
                            }
                          },
                          child: Container(
                            height: 34,
                            width: 134,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.white, width: 1),
                              borderRadius: BorderRadius.circular(28),
                            ),
                            child: Center(
                              child: Text(
                                'Login Now',
                                style: tsS12wRcWhite,
                              ),
                            ),
                          ),
                        )
                      ]),
                    ),
                    SizedBox(height: h * 20),

                    Center(
                        child: Text(
                            'Version : ${versionProvider.packageInfo?.version ?? ''}  ${versionProvider.packageInfo?.buildNumber != null ? ('(${versionProvider.packageInfo?.buildNumber ?? ''})') : ''}')),

                    SizedBox(height: h * 20),
                  ],
                ),
              ),
            ],
          ),
          Positioned(
            right: 5,
            top: 50,
            child: InkWell(
              onTap: () => Navigator.pop(context),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0),
                ),
                padding: const EdgeInsets.all(5.0),
                child: const Icon(
                  Icons.arrow_back_outlined,
                  color: Colors.black,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  void onEmployeeDirectoryTap(BuildContext context) {
    final provider = context.read<TeamMembersProvider>();
    final masterProvider = context.read<MasterProvider>();
    provider.getTeamMembers(isFilter: false, master: false, context: context);
    provider.clearSelectedFilter();
    masterProvider.currentIndex = 1;
    Navigator.pop(context);
  }

  Future<void> onTaskManagementTap(BuildContext context) async {
    final logTimeProvider = context.read<LogTimeProvider>();
    if (!logTimeProvider.isLoading) {
      bool isGranded = await logTimeProvider.getTotalWorkingHours();
      await logTimeProvider.getUserTimeLogList(master: false);
      logTimeProvider.selectedDate = DateTime.now();
      logTimeProvider.getCompletedTasks();
      if (isGranded) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const TaskManagementScreen(),
          ),
        );
      }
    }
  }

  Future<void> onHolidaysTap(BuildContext context) async {
    EasyLoading.show();
    final activitiesProvider = context.read<CompanyActivitiesProvider>();
    await activitiesProvider.getHolidays();

    PageNavigator.push(
      context: context,
      route: const UpcomingHilodays(),
    );
    EasyLoading.dismiss();
  }

  Future<void> onChecklistTap(BuildContext context) async {
    if (isRedundentClick(DateTime.now())) {
      return;
    }

    // var provider = context.read<ChecklistProvider>();
    // provider.checkListModel.clear();
    // await provider.getShedulers();
    // await provider.getChecklist();
    PageNavigator.push(context: context, route: const ChecklistScreen());
  }

  Future<void> onMeetingRoomTap(BuildContext context) async {
    EasyLoading.dismiss();
    final provider = context.read<BookMeetingRoomProvider>();
    provider.getTeamMembers(context: context);
    MeetingRoomProvider meetingRoomProvider =
        Provider.of(context, listen: false);
    meetingRoomProvider.getMeetingRooms();
    meetingRoomProvider.getAmenities();
    meetingRoomProvider.getPolicies();

    Navigator.pop(context);
    PageNavigator.push(context: context, route: const MeetingRoomScreen());
  }

  Future<void> onScheduledMeetingTap(BuildContext context) async {
    final provider = context.read<BookedMeetingRoomProvider>();
    provider.getScheduledMeetings(action: 'today', context: context);
    Navigator.pop(context);
    PageNavigator.push(
      context: context,
      route: const ScheduledMeetingScreen(),
    );
  }

  void onProfileTap(BuildContext context) {
    var provider = context.read<ProfileProvider>();
    provider.getProfileData(context: context);
    Navigator.pop(context);
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ProfileScreen(),
      ),
    );
  }

  void onAIChatbotTap(BuildContext context) {
    Navigator.pop(context);
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ChatScreenWrapper(),
      ),
    );
  }

  void onFeedbackComplaintsTap(BuildContext context) {
    final provider = context.read<FAQProvider>();
    provider.selectedImages.clear();
    Navigator.pop(context);
    PageNavigator.push(context: context, route: const FeedbackScreen());
  }

  Future<String?> _getName() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('name')?.capitalize();
  }

  Future<void> logout({context}) async {
    try {
      await Provider.of<SignInProvider>(context, listen: false).logOut();
      await _googleSignIn.signOut();
    } catch (e) {
      debugPrint(e.toString());
    }
    SharedPreferences shared = await SharedPreferences.getInstance();
    await shared.clear();

    Provider.of<MasterProvider>(context, listen: false).currentIndex = 0;

    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginWithEmailScreen()),
        (route) => false);
  }

  // Future<void> _signout(BuildContext context) async {
  //   FirebaseFirestore.instance
  //       .collection('users')
  //       .doc(LoggedInUser.uid)
  //       .update({
  //     'fcmToken': null,
  //   });
  //   FirebaseMessaging.instance.unsubscribeFromTopic('all');
  //   FirebaseAuth auth = FirebaseAuth.instance;
  //   auth.signOut();
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   prefs.clear();
  //   PageNavigator.pushAndRemoveUntil(
  //     context: context,
  //     route: const SignInScreen(),
  //   );
  // }

  // final SizedBox _kGap = SizedBox(height: h * 15);

  Widget _submenuItem({void Function()? onTap, required String? title}) {
    return SizedBox(
      height: 40,
      child: ListTile(
        onTap: onTap,
        title:
            Text('$title', style: tsS12w400Black, textAlign: TextAlign.start),
      ),
    );
    // return InkWell(
    //   onTap: onTap,
    //   child: Text('$title', style: tsS12w400Black, textAlign: TextAlign.start),
    // );
  }

  _onReimbursmentApprovalOpened(BuildContext context) async {
    ReimbursementProvider provider = context.read<ReimbursementProvider>();
    provider.startDate = null;
    provider.endDate = null;
    provider.searchKey = '';
    provider.selectedFilter = 'Pending';
    provider.selectedDepartment = null;
    provider.selectedDesignation = null;
    EasyLoading.show();
    provider.fetchDepartmentList();
    provider.fetchDesignationList();
    await provider.fetchReportingPersonRequestList();
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) =>
                ReimbursementRequestsScreenForReportingPerson()));
  }

  _onReimbursmentHRApprovalOpened(BuildContext context) async {
    ReimbursementProvider provider = context.read<ReimbursementProvider>();
    provider.startDate = null;
    provider.endDate = null;
    provider.searchKey = '';
    provider.selectedFilter = 'Pending';
    provider.selectedDepartment = null;
    provider.selectedDesignation = null;
    EasyLoading.show();
    provider.fetchDepartmentList();
    provider.fetchDesignationList();
    await provider.fetchHRRequestList();
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => ReimbursementRequestsScreenForHR()));
  }

  _onReimbursmentOpened(BuildContext context) async {
    ReimbursementProvider provider = context.read<ReimbursementProvider>();
    provider.startDate = null;
    provider.endDate = null;
    provider.selectedFilter = 'Pending';
    provider.fetchReimbursmentList();
    provider.titleList = await provider.getDropdownData(action: 'title');
    provider.currencyList = await provider.getDropdownData(action: 'currency');

    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => const ReimbursmentListingScreen()));
  }

  _onMedicalInsuranceOpened(BuildContext context) async {
    await EasyLoading.show();
    var provider =
        Provider.of<MedicalInsuranceProvider>(context, listen: false);

    await provider.medicalInsurance();
    await provider.medicalInsuranceDownload();

    await EasyLoading.dismiss();

    PageNavigator.pushSlideRight(
      context: context,
      route: const MedicalInsuranceScreen(),
    );
  }

  _onFlightTicketsOpened(BuildContext context) async {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const FlightTicketsListScreen(),
      ),
    );
  }

  _onFlightTicketsOpenedRequest(BuildContext context) async {
    Navigator.of(context).push(
      MaterialPageRoute(
        // builder: (context) => const FlightTicketRequestLevelScreen(),
        builder: (context) => const FlightTicketRequestNewLevelScreen(),
      ),
    );
  }

  _onFlightTicketsOpenedUpload(BuildContext context) async {
    var flightTicket = context.read<FlightTicketProvider>();
    flightTicket.getDepartmentList();
    flightTicket.getDesignationList();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const FlightTicketApprovedListScreen(),
      ),
    );
  }

  _onLeaveRecordsOpened(BuildContext context) async {
    var provider = Provider.of<MasterProvider>(context, listen: false);
    var leaveProvider =
        Provider.of<LeaveApplicationProvider>(context, listen: false);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      leaveProvider.selectedTypeOfLeave = 'All';
    });
    // await leaveProvider.getEmployeeLeaveDetailes(master: false);
    leaveProvider.currentPageLeaveRecords = 0;
    leaveProvider.pagingControllerLeaveRecords?.refresh();
    await leaveProvider.getLeaveBalance(master: false, context: context);
    provider.currentIndex = 2;
    Navigator.pop(context);
  }

  _onLeaveRequestOpened(BuildContext context) async {
    var provider = Provider.of<MasterProvider>(context, listen: false);
    var leaveProvider =
        Provider.of<LeaveApplicationProvider>(context, listen: false);

    // await leaveProvider.getLeaveRequest();
    // await leaveProvider.getLeaveRequestReporting();
    leaveProvider.selectedAction = 'pending/in-progress';
    leaveProvider.currentPageLeaveRequest = 0;

    Navigator.pop(context);
    provider.currentIndex = 0;
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LeaveRequestForReportingPerson(),
      ),
    );
  }

  _onMorningHuddleOpened(BuildContext context) async {
    PageNavigator.push(context: context, route: MorningHuddleListingScreen());
  }

  _onApplyLeaveOpened(BuildContext context) async {
    EasyLoading.show();
    var leaveProvider =
        Provider.of<LeaveApplicationProvider>(context, listen: false);
    leaveProvider.leaveOverviewModel = null;
    await leaveProvider.getUserList();
    await leaveProvider.getLeaveTypes();
    bool isGetStaffInCharge = await leaveProvider.getStaffinCharge();
    EasyLoading.dismiss();
    if (isGetStaffInCharge) {
      Navigator.pop(context);
      Provider.of<MasterProvider>(context, listen: false).currentIndex = 2;
      PageNavigator.push(
        context: context,
        route: const NewLeaveScreens(),
      );
    }
  }

  _onAdminLeaveRequestOpened(BuildContext context) async {
    var leaveProvider =
        Provider.of<LeaveApplicationProvider>(context, listen: false);
    TeamMembersProvider teamProvider =
        Provider.of<TeamMembersProvider>(context, listen: false);
    leaveProvider.leaveOverviewModel = null;
    await leaveProvider.getUserList();
    await leaveProvider.getLeaveTypes();
    await teamProvider.getTeamMembers(
        isFilter: false, master: false, context: context);
    bool isGetStaffInCharge = await leaveProvider.getStaffinCharge();

    if (isGetStaffInCharge) {
      Navigator.pop(context);
      PageNavigator.push(
        context: context,
        route: const AdminLeaveRequest(),
      );
    }
  }

  _onApplyWFH({required BuildContext context}) async {
    PageNavigator.push(context: context, route: ApplyWfhScreen());
  }

  _onWFHRecods({required BuildContext context}) async {
    PageNavigator.push(context: context, route: WfhRecordsScreen());
  }

  _onWFHRequests({required BuildContext context}) async {
    PageNavigator.push(context: context, route: WFHRequestsScreen());
  }

  // _onCertificatesOpened(BuildContext context) {
  //   PageNavigator.push(
  //     context: context,
  //     route: const CertificateRequests(),
  //   );
  // }
// - package info details ----- --
// void package() async {
//     PackageInfo packageInfo = PackageInfo.fromPlatform();

//     String appName = packageInfo.appName;
//     String packageName = packageInfo.packageName;
//     String version = packageInfo.version;
//     String buildNumber = packageInfo.buildNumber;
//   }
}
