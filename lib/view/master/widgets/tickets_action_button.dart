import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/tickets_screens/tickets.dart';
import 'package:flutter/material.dart';

class TicketsActionButton extends StatelessWidget {
  const TicketsActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () =>
          PageNavigator.push(context: context, route: const Tickets()),
      icon: const ImageIcon(
        AssetImage('assets/icons/chat_icon.png'),
        color: Colors.white,
      ),
    );
  }
}
