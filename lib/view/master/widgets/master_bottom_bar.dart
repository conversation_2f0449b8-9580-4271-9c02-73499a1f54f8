// ignore_for_file: use_build_context_synchronously

import 'package:e8_hr_portal/model/bottom_bar_item.dart';
import 'package:e8_hr_portal/model/user_detailes_model.dart';
import 'package:e8_hr_portal/provider/company_activities_provider.dart';
import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/provider/team_members_provider.dart';
import 'package:e8_hr_portal/provider/user_status_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class MasterBottomBar extends StatelessWidget {
  const MasterBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<MasterProvider>();
    return Container(
      color: ThemeColors.colorF4F5FA,
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(25),
          topLeft: Radius.circular(25),
        ),
        child: BottomAppBar(
          elevation: 1,
          notchMargin: 20,
          color: Colors.white,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: bottomBarItems.map((e) {
              int index = bottomBarItems.indexOf(e);
              Color? color = ThemeColors.titleColor;
              if (index == provider.currentIndex) {
                // color = BottomNavigationBarTheme.of(context).selectedItemColor;
                color = ThemeColors.colorF9637D;
              }
              return IconButton(
                onPressed: () => onChanged(context, index: index),
                icon: Column(
                  children: [
                    ImageIcon(
                      AssetImage(e.icon),
                    ),
                    if (provider.currentIndex == index)
                      Container(
                        margin: const EdgeInsets.only(top: 4),
                        width: 4,
                        height: 4,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: color,
                        ),
                      )
                  ],
                ),
                color: color,
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  onChanged(BuildContext context, {required int index, mounted = true}) async {
    final provider = context.read<MasterProvider>();
    provider.currentIndex = index;
    final leaveApplicationProvider = context.read<LeaveApplicationProvider>();
    switch (index) {
      case 0:
        leaveApplicationProvider.currentPageLeaveRecords = 0;
        leaveApplicationProvider.pagingControllerLeaveRecords?.refresh();
        // leaveApplicationProvider.pagingControllerLeaveRecords?.notifyListeners();
        await leaveApplicationProvider.getLeaveBalance(
            master: false, context: context);
        if (!mounted) return;
        var companyActivitiesProvider =
            context.read<CompanyActivitiesProvider>();
        companyActivitiesProvider.getRecentActivity(
          master: false,
          context: context,
        );
        break;
      case 1:
        final teamMembersProvider = context.read<TeamMembersProvider>();
        teamMembersProvider.getTeamMembers(
          isFilter: false,
          master: false,
          context: context,
        );
        teamMembersProvider.clearSelectedFilter();
        break;
      case 2:
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          leaveApplicationProvider.selectedTypeOfLeave = "All";
        });
        await leaveApplicationProvider.refreshLeaveRecord();
        if (!mounted) return;
        leaveApplicationProvider.getLeaveBalance(
          master: false,
          context: context,
        );
        break;
      case 3:
        var profile = context.read<ProfileProvider>();
        var status = context.read<UserStatusProvider>();
        await profile.getProfileData(context: context);
        await status.getUserStatus(master: false);
        await status.clear();

        EmpStatus? selected =
            profile.userDetailesModel?.profileDetails?.empStatus;
        if (selected != null && status.userStatus.isNotEmpty) {
          final empStatus =
              status.userStatus.firstWhere((e) => e.id == selected.id);
          status.selectedUserStatus = EmpStatus(
              createdAt: empStatus.createdAt,
              id: empStatus.id,
              isActive: empStatus.isActive,
              isShowActions: false,
              name: empStatus.name,
              updatedAt: empStatus.updatedAt
              // subAction: empStatus.subAction!.firstWhere((element) =>
              //     selected.subAction!.toLowerCase() ==
              //     element.value?.toLowerCase()),
              );
        }
        break;
    }
  }
}

List<BottomBarItem> bottomBarItems = [
  BottomBarItem(title: 'Home', icon: 'assets/icons/homee.png'),
  BottomBarItem(title: 'Team Members', icon: 'assets/icons/members.png'),
  BottomBarItem(title: 'Leave', icon: 'assets/icons/leave_applications.png'),
  BottomBarItem(title: 'Feeds', icon: 'assets/icons/updates.png'),
];
