import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/util/capitalize.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AppBarTitle extends StatelessWidget {
  final ScaffoldState? currentState;
  const AppBarTitle(this.currentState, {super.key});

  final List<String> _titleList = const [
    '',
    'Employees',
    'Leaves',
    'Latest Posts',
  ];

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<MasterProvider>();
    if (provider.currentIndex == 0) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          IconButton(
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
            icon: const Icon(
              Icons.menu,
              color: Colors.white,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: Row(
              children: [
                Image.asset(
                  "assets/icons/handwave2.png",
                  scale: 1.2,
                ),
                const SizedBox(
                  width: 10,
                ),
                Text(
                  "Hello!",
                  style: tsS22FFFFF,
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: Text(
              LoginModel.name?.capitalize() ?? '',
              style: tsS22W5FFFFF,
            ),
          )
        ],
      );
    }
    return Text(
      _titleList[provider.currentIndex],
      overflow: TextOverflow.ellipsis,
    );
  }
}
