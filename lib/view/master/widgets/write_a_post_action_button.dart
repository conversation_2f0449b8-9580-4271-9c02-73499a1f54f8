import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/latest_updates/post_new_updates_screen.dart';
import 'package:e8_hr_portal/view/latest_updates/widgets/post_option_bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';

class WriteAPostActionButton extends StatelessWidget {
  const WriteAPostActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        EasyLoading.show();
        final provider = context.read<UpdatesProvider>();
        provider.selectedPostsImages.clear();
        provider.selectedPostsVideo = null;
        provider.alreadyPostImageList?.clear();
        await provider.getUserPolicies();
        provider.selectedUserPoliciesList.clear();
        provider.selectedUserPoliciesListIds.clear();
        provider.validationSub = false;
        provider.selectedPostType = null;
        if (LoginModel.isAdmin == true && context.mounted) {
          await showModalBottomSheet<void>(
            useSafeArea: false,
            context: context,
            isDismissible: true,
            isScrollControlled: true,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(12),
              ),
            ),
            builder: (BuildContext context) {
              EasyLoading.dismiss();
              return PostOptionBottomSheet();
            },
          );
        } else {
          await EasyLoading.dismiss();
          if (context.mounted) {
            PageNavigator.pushSlideRight(
              context: context,
              route: const PostNewUpdatesScreen(),
            );
          }
        }
        await EasyLoading.dismiss();
      },
      child: Center(
        child: Padding(
          padding: const EdgeInsets.only(right: 15),
          child: Text(
            'Write a post',
            style: tsS14FFFFF,
          ),
        ),
      ),
    );
  }
}
