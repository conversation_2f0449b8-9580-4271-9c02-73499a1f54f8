import 'package:flutter/material.dart';

class MenuDrawerButton extends StatelessWidget {
  final ScaffoldState? currentState;
  const MenuDrawerButton(this.currentState, {super.key});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () => currentState?.openDrawer(),
      icon: const Icon(
        Icons.menu,
        color: Colors.white,
      ),
    );
  }
}
