import 'package:e8_hr_portal/provider/notification_provider.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/notification/notification_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class NotificationActionButton extends StatelessWidget {
  const NotificationActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<NotificationProvider>();
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: IconButton(onPressed: () {
        provider.showClearButton = true;
        PageNavigator.push(
          context: context,
          route: const NotificationScreen(),
        );
      }, icon: Builder(
        builder: (context) {
          if (provider.notificationCount == 0) {
            return const ImageIcon(
              AssetImage('assets/icons/notification.png'),
              color: Colors.white,
            );
          }
          return Badge(
            label: Text(
              "${provider.notificationCount > 10 ? "10+" : provider.notificationCount}",
              style: const TextStyle(color: Colors.white),
            ),
            child: const ImageIcon(
              AssetImage('assets/icons/notification.png'),
              color: Colors.white,
            ),
          );
        },
      )),
    );
  }
}
