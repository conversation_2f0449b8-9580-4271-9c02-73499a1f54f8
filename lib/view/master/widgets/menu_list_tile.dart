import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/material.dart';

class MenuListTile extends StatelessWidget {
  final void Function()? onTap;
  final String assetIcon;
  final String title;
  const MenuListTile({
    required this.assetIcon,
    required this.title,
    this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return ListTile(
      onTap: onTap,
      leading: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: colorScheme.secondary.withOpacity(0.10),
        ),
        child: ImageIcon(
          AssetImage(assetIcon),
          color: colorScheme.secondary,
        ),
      ),
      title: Text(
        title,
        style: tsS14w500Black,
      ),
    );
  }
}
