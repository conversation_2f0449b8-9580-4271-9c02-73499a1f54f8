import 'package:e8_hr_portal/provider/team_members_provider.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/team_member/team_members_search.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class SearchActionButton extends StatelessWidget {
  const SearchActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: IconButton(
        onPressed: () {
          final teamMembersProvider = context.read<TeamMembersProvider>();
          teamMembersProvider.isRecentSearchShow = true;
          PageNavigator.push(
            context: context,
            route: const TeamMembersSearchScreen(),
          );
        },
        icon: const ImageIcon(
          AssetImage(
            'assets/icons/search.png',
          ),
          color: Colors.white,
        ),
      ),
    );
  }
}
