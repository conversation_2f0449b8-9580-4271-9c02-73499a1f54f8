import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/master/widgets/checklist_action_button.dart';
import 'package:e8_hr_portal/view/master/widgets/notification_action_button.dart';
import 'package:e8_hr_portal/view/master/widgets/search_action_button.dart';
import 'package:e8_hr_portal/view/master/widgets/tickets_action_button.dart';
import 'package:e8_hr_portal/view/master/widgets/write_a_post_action_button.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class MasterActionButton extends StatelessWidget {
  const MasterActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    final masterProvider = context.watch<MasterProvider>();
    switch (masterProvider.currentIndex) {
      case 0:
        return Padding(
          padding: EdgeInsets.only(top: 17.0 * h),
          child: const Column(
            children: [
              Row(
                children: [
                  ChecklistActionButton(),
                  Tickets<PERSON><PERSON><PERSON><PERSON>on(),
                  NotificationActionButton(),
                ],
              ),
            ],
          ),
        );
      case 1:
        return const SearchActionButton();
      case 3:
        return const WriteAPostActionButton();
      default:
        return Container();
    }
  }
}
