import 'dart:developer';

import 'package:e8_hr_portal/provider/checklist_provider.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/checklist/checklist_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class ChecklistActionButton extends StatelessWidget {
  const ChecklistActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ChecklistProvider>(
      builder: (context, pro, _) {
        log('unread count - ${pro.unReadCount}');
        return IconButton(
          onPressed: () async {
            if (isRedundentClick(DateTime.now())) {
              return;
            }
            PageNavigator.push(
                context: context, route: const ChecklistScreen());
          },
          icon: Badge(
            alignment: Alignment.topRight,
            isLabelVisible: pro.unReadCount > 0,
            child: const ImageIcon(
              AssetImage('assets/icons/checklist_home.png'),
              color: Colors.white,
            ),
          ),
        );
      },
    );
  }
}
