// ignore_for_file: use_build_context_synchronously

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/birthday_anniversary_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:provider/provider.dart';
import '../../util/colors.dart';
import '../../util/size_config.dart';
import '../../util/styles.dart';
import 'birthday_anniversary_screen.dart';

class BirthdayAnniversarySection extends StatelessWidget {
  final bool mounted = true;
  const BirthdayAnniversarySection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<BirthdayAnniversaryProvider>(
      builder: (context, provider, child) {
        return provider.birthdayList.isEmpty
            ? const SizedBox()
            : Padding(
                padding: const EdgeInsets.only(top: 10.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Birthdays & Anniversaries",
                          style: tsS16w500,
                        ),
                        InkWell(
                          onTap: () async {
                            var provid =
                                context.read<BirthdayAnniversaryProvider>();

                            bool isGo = await provid.getBirthdayAnniversary(
                              master: false,
                              context: context,
                            );

                            if (isGo && !provid.click) {
                              if (!mounted) return;
                              PageNavigator.pushSlideup(
                                context: context,
                                route: const BirthdayAnniversaryScreen(),
                              );
                              provid.click = true;
                            }
                          },
                          child: Text(
                            'View All',
                            style: GoogleFonts.poppins(
                              color: ThemeColors.colorF9637D,
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    SizedBox(
                      height: h * 150,
                      child: ListView.builder(
                        physics: const ClampingScrollPhysics(),
                        scrollDirection: Axis.horizontal,
                        itemCount: provider.birthdayList.length > 2
                            ? 3
                            : provider.birthdayList.length,
                        itemBuilder: (context, index) {
                          final data = provider.birthdayList[index];
                          String? dob;
                          if (data.dateOfBirth != null) {
                            dob = formatDateFromString(
                              data.dateOfBirth.toString(),
                              'yyyy-MM-dd',
                              'dd MMM',
                            );
                          }
                          return Container(
                            margin: const EdgeInsets.only(right: 10),
                            width: w * 120,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Column(
                              children: [
                                SizedBox(
                                  height: h * 20,
                                ),
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(60),
                                  child: CachedNetworkImage(
                                    width: w * 62,
                                    height: h * 62,
                                    fit: BoxFit.cover,
                                    imageUrl: data.profilePic ?? "",
                                    errorWidget: (context, url, error) {
                                      return Container(
                                        decoration: BoxDecoration(
                                          color: Theme.of(context).primaryColor,
                                          shape: BoxShape.circle,
                                        ),
                                        alignment: Alignment.center,
                                        child: Text(
                                          data.name
                                                  ?.substring(0, 1)
                                                  .toUpperCase()
                                                  .toUpperCase() ??
                                              "",
                                          style: GoogleFonts.rubik(
                                            fontSize: 22,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.white,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                SizedBox(
                                  height: h * 14,
                                ),
                                Text(
                                  textAlign: TextAlign.center,
                                  data.name ?? "",
                                  maxLines: 1,
                                  style: tsS14w500Black,
                                ),
                                if (data.model == "Anniversary")
                                  Column(
                                    children: [
                                      Text(
                                        getAnniversaryTitle(data.year),
                                        style: GoogleFonts.poppins(
                                          fontSize: 10,
                                          fontWeight: FontWeight.w400,
                                          color: const Color(0xffA01BC0),
                                        ),
                                      ),
                                      Text(
                                        " ${formatDateFromString(data.dateOfJoining!, "yyyy-MM-dd", "MMM dd")}",
                                        style: GoogleFonts.poppins(
                                          fontSize: 10,
                                          fontWeight: FontWeight.w400,
                                          color: const Color(0xffA01BC0),
                                        ),
                                      ),
                                    ],
                                  )
                                else
                                  Column(
                                    children: [
                                      Text(
                                        "Birthday . ${dob ?? ""}",
                                        style: GoogleFonts.poppins(
                                          fontSize: 10,
                                          fontWeight: FontWeight.w400,
                                          color: const Color(0xff519C66),
                                        ),
                                      ),
                                    ],
                                  )
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 30),
                  ],
                ),
              );
      },
    );
  }

  String getAnniversaryTitle(int? year) {
    switch (year) {
      case 1:
        return "1st Work anniversary";
      case 2:
        return "2nd Work anniversay";
      case 3:
        return "3rd Work anniversary";
      default:
        return "${year}th Work anniversary";
    }
  }
}
