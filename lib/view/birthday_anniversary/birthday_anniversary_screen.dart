import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/birthday_anniversary_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';
import '../../util/colors.dart';
import '../../util/size_config.dart';

class BirthdayAnniversaryScreen extends StatelessWidget {
  const BirthdayAnniversaryScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        screenTitle: 'Birthdays & Anniversaries',
        onTap: () async {
          var provider =
              Provider.of<BirthdayAnniversaryProvider>(context, listen: false);

          provider.click = false;
        },
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                    child: Consumer<BirthdayAnniversaryProvider>(
                  builder: (context, provider, child) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: h * 10,
                        ),
                        todaySection(),
                        const SizedBox(
                          height: 10,
                        ),
                        upcomingSection()
                      ],
                    );
                  },
                )),
              ),
            ],
          ),
        ));
  }
}

Widget upcomingSection() {
  return Consumer<BirthdayAnniversaryProvider>(
    builder: (context, provider, child) {
      if (provider.upcomingList.isEmpty) {
        return Container();
      }
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Upcoming',
            style: tsS16w500,
          ),
          SizedBox(
            height: h * 15,
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            separatorBuilder: (context, index) => const SizedBox(
              height: 15,
            ),
            itemCount: provider.upcomingList.length,
            padding: EdgeInsets.zero,
            itemBuilder: (context, index) {
              final data = provider.upcomingList[index];
              int now = DateTime.now().year;
              String? dateOfBirth;
              if (data.dateOfBirth != null) {
                dateOfBirth = formatDateFromString(
                    data.dateOfBirth.toString(), 'yyyy-MM-dd', 'dd MMM');
                dateOfBirth = '$dateOfBirth $now';
              }
              return CommonCard(
                name: data.name,
                model: data.model,
                profilePic: data.profilePic,
                dob: dateOfBirth,
                year: data.year,
                dateOfjoining: data.dateOfJoining,
              );
            },
          ),
        ],
      );
    },
  );
}

Widget todaySection() {
  return Consumer<BirthdayAnniversaryProvider>(
    builder: (context, provider, child) {
      if (provider.todayList.isEmpty) {
        return Container();
      }
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Today',
            style: tsS16w500,
          ),
          SizedBox(
            height: h * 15,
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            separatorBuilder: (context, index) => const SizedBox(
              height: 15,
            ),
            itemCount: provider.todayList.length,
            padding: EdgeInsets.zero,
            itemBuilder: (context, index) {
              final data = provider.todayList[index];
              DateTime now = DateTime.now();
              String? dateOfBirth;
              if (data.dateOfBirth != null) {
                dateOfBirth = formatDateFromString(
                  now.toString(),
                  'yyyy-MM-dd',
                  'dd MMM yyyy',
                );
              }
              return CommonCard(
                name: data.name,
                model: data.model,
                profilePic: data.profilePic,
                dob: dateOfBirth,
                year: data.year,
                dateOfjoining: data.dateOfJoining,
              );
            },
          ),
        ],
      );
    },
  );
}

class CommonCard extends StatelessWidget {
  final String? dob;
  final String? name;
  final String? profilePic;
  final String? model;
  final int? year;
  final String? dateOfjoining;
  const CommonCard(
      {super.key,
      required this.name,
      required this.model,
      required this.profilePic,
      required this.dob,
      required this.dateOfjoining,
      this.year});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: h * 65,
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8)),
      child: Row(
        children: [
          // Container(
          //   margin: const EdgeInsets.symmetric(horizontal: 10),
          //   decoration:
          //       const BoxDecoration(color: Colors.red, shape: BoxShape.circle),
          // ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 10),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(50),
              child: CachedNetworkImage(
                height: h * 42,
                width: w * 42,
                fit: BoxFit.cover,
                imageUrl: profilePic ?? '',
                errorWidget: (context, url, error) {
                  return Container(
                    decoration: BoxDecoration(
                      color: ThemeColors.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      name?.substring(0, 1).toUpperCase().toUpperCase() ?? '',
                      style: GoogleFonts.rubik(
                          fontSize: 22,
                          fontWeight: FontWeight.w500,
                          color: Colors.white),
                    ),
                  );
                },
              ),
            ),
          ),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                name ?? '',
                style: tsS12w500Black,
              ),
              Row(
                children: [
                  Text(
                    model == 'Birthday'
                        ? 'Birthday'
                        : year == 1
                            ? '1st Work anniversary'
                            : year == 2
                                ? '2nd Work anniversay'
                                : year == 3
                                    ? '3rd Work anniversary'
                                    : year == 21
                                        ? '21st Work anniversary'
                                        : '${year}th Work anniversary',
                    style: GoogleFonts.poppins(
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                        color: model == 'Birthday'
                            ? const Color(0xff519C66)
                            : const Color(0xffA01BC0)),
                  ),
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 5),
                    width: 4,
                    height: 4,
                    decoration: const BoxDecoration(
                        color: Color(0xffA01BC0), shape: BoxShape.circle),
                  ),
                  Text(
                    model == 'Birthday'
                        ? '$dob'
                        : formatDateFromString(
                            dateOfjoining!, 'yyyy-MM-dd', 'MMM dd'),
                    style: GoogleFonts.poppins(
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                        color: model == 'Birthday'
                            ? const Color(0xff519C66)
                            : const Color(0xffA01BC0)),
                  ),
                ],
              )
            ],
          ))
        ],
      ),
    );
  }
}
