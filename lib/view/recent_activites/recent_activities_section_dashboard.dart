// ignore_for_file: use_build_context_synchronously

import 'package:e8_hr_portal/provider/ble_attendance_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/model/recent_activity_model.dart';
import 'package:e8_hr_portal/provider/company_activities_provider.dart';
import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/view/certificate_requests/non_objection_certificates/non_objection_certificates.dart';
import 'package:e8_hr_portal/view/certificate_requests/salary_certificate/salary_certificate.dart';
import 'package:e8_hr_portal/view/recent_activites/recent_activites.dart';
import 'package:e8_hr_portal/view/recent_activites/widgets/post_detailes_screen_from_recent.dart';
import 'package:provider/provider.dart';
import '../../provider/attendance_provider.dart';
import '../../provider/driving_license_provider.dart';
import '../../provider/experience_certifcate_provider.dart';
import '../../provider/flight_ticket_provider.dart';
import '../../provider/noc_provider.dart';
import '../../provider/notification_provider.dart';
import '../../provider/profile_provider.dart';
import '../../provider/salary_certificate_provider.dart';
import '../../provider/salary_transfer_letter_provider.dart';
import '../../provider/tickets_provider.dart';
import '../../provider/updates_provider.dart';
import '../../provider/user_status_provider.dart';
import '../../util/colors.dart';
import '../../util/page_navigator.dart';
import '../../util/styles.dart';
import '../attendance/attendance_screen.dart';
import '../certificate_requests/certificate_requests.dart';
import '../certificate_requests/driving_license_certificates/driving_license_certificates.dart';
import '../certificate_requests/experience_certificate/experience_certificate.dart';
import '../certificate_requests/salary_transfer_certificate/salary_transfer_certificate.dart';
import '../dashboard/widgets/title_text_widget.dart';
import '../flight_tickets/flight_tickets_list_screen.dart';
import '../leave applications/provider/leave_apllication_provider.dart';
import '../leave_request_for_reporting_person/leave_request_overview.dart';
import '../notification/notification_screen.dart';
import '../other_screens/overview/screens/overview_screen.dart';
import '../tickets_screens/tickets_overview.dart';

class RecentActivitiesSectionDashboard extends StatelessWidget {
  const RecentActivitiesSectionDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CompanyActivitiesProvider>(
      builder: (context, provider, child) {
        return provider.recentActivity.isEmpty
            ? const SizedBox()
            : Column(
                children: [
                  const SizedBox(
                    height: 15,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const TitleTextWidget('Recent Activities'),
                      Consumer<CompanyActivitiesProvider>(
                        builder: (context, provider, child) {
                          return InkWell(
                            onTap: () async {
                              provider.showClearButton = true;
                              PageNavigator.pushSlideup(
                                context: context,
                                route: const RecentActivities(),
                              );
                            },
                            child: Text(
                              'View All',
                              style: GoogleFonts.poppins(
                                  color: ThemeColors.colorF9637D,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400),
                            ),
                          );
                        },
                      )
                    ],
                  ),
                  const SizedBox(height: 15.0),
                  Container(
                    padding: const EdgeInsets.all(14),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ListView.separated(
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemCount: provider.recentActivity.length > 3
                          ? 3
                          : provider.recentActivity.length,
                      itemBuilder: (context, index) {
                        final data = provider.recentActivity[index];
                        return InkWell(
                          onTap: () async {
                            onTilePressed(data: data, context: context);
                          },
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(15),
                                decoration: const BoxDecoration(
                                    color: Color(0xFFF8F8F8),
                                    shape: BoxShape.circle),
                                child: ImageIcon(
                                  AssetImage(
                                    getActivityIcon(
                                      data.message.toString().toLowerCase(),
                                    ),
                                  ),
                                  size: 22,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text(
                                      data.message ?? "",
                                      style: tsS14w400Black,
                                    ),
                                    // RichText(
                                    //     text: TextSpan(children: [
                                    //   TextSpan(
                                    //       text: "Sick Leave Request on 23 Dec 2022 is ",
                                    //       style: tsS14w400Black),
                                    //   TextSpan(text: "Approved", style: tsS14w4C4CD964),
                                    //   TextSpan(
                                    //       text: " By HR Manager.",
                                    //       style: tsS14w400Black)
                                    // ])),
                                    if (data.newDate != null)
                                      Text(
                                        data.newDate.toString(),
                                        style: tsS12w4f666666,
                                      )
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      separatorBuilder: (context, index) {
                        return Container(
                          margin: const EdgeInsets.symmetric(
                              horizontal: 15, vertical: 12),
                          height: 2,
                          color: ThemeColors.colorF3F3F3,
                        );
                      },
                    ),
                  ),
                ],
              );
      },
    );
  }

  String getActivityIcon(String message) {
    if (message.contains('sick')) {
      return 'assets/icons/recent/hospital.png';
    }
    if (message.contains('annual')) {
      return 'assets/icons/recent/annual.png';
    }
    if (message.contains('unpaid')) {
      return 'assets/icons/recent/calendar.png';
    }
    if (message.contains('it')) {
      return 'assets/icons/recent/ticket.png';
    }
    if (message.contains('notification')) {
      return 'assets/icons/notification.png';
    }
    if (message.contains('certificate') ||
        message.contains('noc') ||
        message.contains('letter')) {
      return 'assets/icons/recent/certificate.png';
    }
    if (message.contains('post')) {
      return 'assets/icons/recent/post.png';
    }
    if (message.contains('profile') || message.contains('status')) {
      return 'assets/icons/profile.png';
    }
    if (message.contains('attendance')) {
      return 'assets/icons/attendance.png';
    }
    return 'assets/icons/recent/common.png';
  }

  void onTilePressed(
      {required BuildContext context,
      required RecentActivityModel data}) async {
    if (data.moduleName?.toLowerCase() == "user_status" ||
        data.moduleName?.toLowerCase() == "profile") {
      await EasyLoading.show();
      var profile = context.read<ProfileProvider>();
      await profile.getProfileData(context: context);
      var status = context.read<UserStatusProvider>();
      await status.getUserStatus(master: false);
      await status.clear();
      Provider.of<MasterProvider>(context, listen: false).currentIndex = 3;
      await EasyLoading.dismiss();
    } else if (data.moduleName?.toLowerCase() == "leave_status" &&
        data.dbData?.id != null) {
      final leaveProvider =
          Provider.of<LeaveApplicationProvider>(context, listen: false);

      await EasyLoading.show();
      bool isGo =
          await leaveProvider.getRequestedLeaveOverviewForReportedPerson(
              leaveId: int.parse(data.dbData!.id.toString()));

      if (isGo) {
        PageNavigator.pushSlideup(
          context: context,
          route: const LeaveRequestOverView(),
        );
      }
      await EasyLoading.dismiss();
    } else if (data.moduleName?.toLowerCase() == "leave_update" &&
        data.dbData?.id != null) {
      await EasyLoading.show();
      final provid =
          Provider.of<LeaveApplicationProvider>(context, listen: false);

      bool isGo = await provid.getLeaveOverView(
          leaveId: int.parse(data.dbData!.id.toString()));
      bool isGetRepPerson = await provid.getReportingPerson();

      if (isGo && isGetRepPerson) {
        PageNavigator.pushSlideup(
            context: context, route: const OverViewScreen());
      }
      await EasyLoading.dismiss();
    } else if (data.moduleName?.toLowerCase() == "ticket_update" &&
        data.dbData?.id != null) {
      await EasyLoading.show();
      TicketsProvider prov =
          Provider.of<TicketsProvider>(context, listen: false);

      bool isGo = await prov.getTicketOverview(
          ticketID: int.parse(data.dbData!.id.toString()));

      if (isGo) {
        PageNavigator.pushSlideup(
          context: context,
          route: const TicketsOverview(),
        );
      }
      await EasyLoading.dismiss();
    } else if (data.moduleName?.toLowerCase() == "attendance") {
      await EasyLoading.show();
      Provider.of<ProfileProvider>(context, listen: false)
          .getProfileData(context: context);
      AttendanceProvider provider =
          Provider.of<AttendanceProvider>(context, listen: false);
      provider.selectedMonth = DateTime.now();
      provider.getAttendanceList(master: false, context: context);
      EasyLoading.show();
      BLEAttendanceProvider provider1 =
          Provider.of<BLEAttendanceProvider>(context, listen: false);
      await provider1.getAttendanceLogs();
      EasyLoading.dismiss();
      PageNavigator.pushSlideup(
        context: context,
        route: const AttendanceScreen(),
      );
      await EasyLoading.dismiss();
    } else if (data.moduleName?.toLowerCase() == "certificates_requests") {
      await EasyLoading.show();
      PageNavigator.pushSlideup(
        context: context,
        route: const CertificateRequests(),
      );
      await EasyLoading.dismiss();
    } else if (data.moduleName?.toLowerCase() == "flight_ticket") {
      var provider = Provider.of<FlightTicketProvider>(context, listen: false);
      await EasyLoading.show();
      provider.getAllCountries();
      // bool isGo = await provider.getFlightTicketList();
      provider.currentPage = 0;
      provider.pagingController?.refresh();

      // if (isGo) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const FlightTicketsListScreen(),
        ),
      );
      // }
      await EasyLoading.dismiss();
    } else if (data.moduleName?.toLowerCase() == "salary_certificate") {
      await EasyLoading.show();
      final provider =
          Provider.of<SalaryCertificateProvider>(context, listen: false);
      provider.getSalaryCertificate();
      Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const SalaryCertificate()));
      await EasyLoading.dismiss();
    } else if (data.moduleName?.toLowerCase() == "experience_certificate") {
      await EasyLoading.show();
      var provider =
          Provider.of<ExperienceCertifcateProvider>(context, listen: false);

      await provider.fetchPreviousRequests();

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const ExperienceCertificate(),
        ),
      );
      await EasyLoading.dismiss();
    } else if (data.moduleName?.toLowerCase() == "driving_licence") {
      final navigator = Navigator.of(context);
      DrivingLicenseProvider provider =
          Provider.of<DrivingLicenseProvider>(context, listen: false);
      await provider.getDrivingLicenseList();

      navigator.push(
          MaterialPageRoute(builder: (context) => const DrivingCertificates()));
    } else if (data.moduleName?.toLowerCase() == "salary_transfer_letter") {
      await EasyLoading.show();
      var provider =
          Provider.of<SalaryTransferLetterProvider>(context, listen: false);

      await provider.fetchPreviousRequests();

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const SalaryTransferCertificate(),
        ),
      );
      await EasyLoading.dismiss();
    } else if (data.moduleName?.toLowerCase() == "noc") {
      await EasyLoading.show();
      Provider.of<FlightTicketProvider>(context, listen: false)
          .getFlightTicketPersonalInfo();
      final navigator = Navigator.of(context);
      NocProvider provider = Provider.of<NocProvider>(context, listen: false);
      // bool isGo =
      await provider.getNocCertificateList();
      // if (isGo) {

      navigator.push(MaterialPageRoute(
          builder: (context) => const NonObjectionCertificates()));
      await EasyLoading.dismiss();
    } else if (data.moduleName?.toLowerCase() == "post_update" &&
        data.dbData?.postId != null) {
      await EasyLoading.show();
      var pvd = Provider.of<UpdatesProvider>(context, listen: false);
      await pvd.getPostDetails(postId: data.dbData!.postId!.toInt());

      // await
      pvd.getComments(postId: data.dbData!.postId!.toInt());

      if (pvd.postList.isNotEmpty)
        // ignore: curly_braces_in_flow_control_structures
        PageNavigator.pushSlideup(
          context: context,
          route: const UpdateDetailedScreenFromRecent(),
        );
      await EasyLoading.dismiss();
    } else if (data.moduleName?.toLowerCase() == "clear_notification") {
      Provider.of<NotificationProvider>(context, listen: false)
          .showClearButton = true;
      PageNavigator.pushSlideup(
        context: context,
        route: const NotificationScreen(),
      );
    }
  }
}
