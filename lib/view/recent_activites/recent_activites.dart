// ignore_for_file: use_build_context_synchronously

import 'dart:io' as io;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/post_model.dart';
import 'package:e8_hr_portal/provider/ble_attendance_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/model/recent_activity_model.dart';
import 'package:e8_hr_portal/provider/company_activities_provider.dart';
import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/recent_activites/widgets/post_detailes_screen_from_recent.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:video_player/video_player.dart';
// import 'package:video_thumbnail/video_thumbnail.dart';
import '../../provider/attendance_provider.dart';
import '../../provider/driving_license_provider.dart';
import '../../provider/experience_certifcate_provider.dart';
import '../../provider/flight_ticket_provider.dart';
import '../../provider/noc_provider.dart';
import '../../provider/notification_provider.dart';
import '../../provider/profile_provider.dart';
import '../../provider/salary_certificate_provider.dart';
import '../../provider/salary_transfer_letter_provider.dart';
import '../../provider/tickets_provider.dart';
import '../../provider/updates_provider.dart';
import '../../provider/user_status_provider.dart';
import '../../util/general_functions.dart';
import '../../util/page_navigator.dart';
import '../../widgets/hisense_scaffold.dart';
import '../attendance/attendance_screen.dart';
import '../certificate_requests/certificate_requests.dart';
import '../certificate_requests/driving_license_certificates/driving_license_certificates.dart';
import '../certificate_requests/experience_certificate/experience_certificate.dart';
import '../certificate_requests/non_objection_certificates/non_objection_certificates.dart';
import '../certificate_requests/salary_certificate/salary_certificate.dart';
import '../certificate_requests/salary_transfer_certificate/salary_transfer_certificate.dart';
import '../flight_tickets/flight_tickets_list_screen.dart';
import '../latest_updates/post_new_updates_screen.dart';
import '../leave applications/provider/leave_apllication_provider.dart';
import '../leave_request_for_reporting_person/leave_request_overview.dart';
import '../notification/no_item_widget.dart';
import '../notification/notification_screen.dart';
import '../other_screens/overview/screens/overview_screen.dart';
import '../tickets_screens/tickets_overview.dart';

class RecentActivities extends StatefulWidget {
  const RecentActivities({super.key});

  @override
  State<RecentActivities> createState() => _RecentActivitiesState();
}

class _RecentActivitiesState extends State<RecentActivities> {
  @override
  void initState() {
    Provider.of<CompanyActivitiesProvider>(context, listen: false).init();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CompanyActivitiesProvider>(
      builder: (context, provider, child) {
        return CustomScaffold(
            screenTitle: "Recent Activities",
            actions: provider.showClearButton == true
                ? [
                    _clearAllButton(),
                  ]
                : [],
            body: Column(
              children: [
                // SizedBox(
                //   height: h * 20,
                // ),
                Expanded(
                  child: PagedListView.separated(
                    padding: const EdgeInsets.only(top: 25),
                    pagingController: provider.pagingController,
                    builderDelegate:
                        PagedChildBuilderDelegate<RecentActivityModel>(
                      noItemsFoundIndicatorBuilder: (context) {
                        provider.showClearButton = false;
                        return const NoItemWidget(
                          iconImage: "assets/images/recent_empty.png",
                          text1: "No Recent Activities",
                          text2: "It seems no activity happened yet.",
                          text3: "",
                        );
                      },
                      newPageProgressIndicatorBuilder: (_) {
                        return Center(
                          child: CircularProgressIndicator(
                              color: ThemeColors.color06AA37),
                        );
                      },
                      firstPageProgressIndicatorBuilder: (_) {
                        return Center(
                          child: CircularProgressIndicator(
                              color: ThemeColors.primaryColor),
                        );
                      },
                      itemBuilder: (context, item, index) {
                        // String status = index == 0 ? "Approved" : "Rejected";
                        // String assignee = "HR Manager";
                        // String date = "23 Dec 2022";
                        // String requestType = "Sick Leave Request";
                        // String time = "1 min";
                        return _requesTile(provider: item
                            // requestType: requestType,
                            // date: date,
                            // status: status,
                            // statusStyle: index == 0 ? null : tsS14w400cFA0000,
                            // assignee: assignee,
                            // time: time
                            );
                      },
                    ),
                    separatorBuilder: (context, index) {
                      return SizedBox(height: h * 10);
                    },
                  ),
                ),
              ],
            )

            // widget: SingleChildScrollView(
            //   physics: const BouncingScrollPhysics(),
            //   scrollDirection: Axis.vertical,
            //   child: Column(
            //     children: [

            // ListView.separated(
            //   shrinkWrap: true,
            //   physics: const BouncingScrollPhysics(),
            //   scrollDirection: Axis.vertical,
            //   padding: EdgeInsets.fromLTRB(0, h * 32, 0, h * 100),
            //   itemCount: 16,
            //   itemBuilder: (context, index) {
            //     String status = index == 0 ? "Approved" : "Rejected";
            //     String assignee = "HR Manager";
            //     String date = "23 Dec 2022";
            //     String requestType = "Sick Leave Request";
            //     String time = "1 min";
            //     return _requesTile(
            //         requestType: requestType,
            //         date: date,
            //         status: status,
            //         statusStyle: index == 0 ? null : tsS14w400cFA0000,
            //         assignee: assignee,
            //         time: time);
            //   },
            //   separatorBuilder: (context, index) => SizedBox(height: h * 10),
            // )
            //     ],
            //   ),
            // ),
            );
      },
    );
  }

  Widget _requesTile({required RecentActivityModel provider
      //   required String requestType,
      // required String date,
      // required String status,
      // required String assignee,
      // required String time,
      // TextStyle? statusStyle

      }) {
    return Consumer<MasterProvider>(
      builder: (context, masterProvider, child) {
        return InkWell(
          onTap: () async {
            onTilePressed(
                provider: provider,
                masterProvider: masterProvider,
                context: context);
          },
          child: Container(
            // height: h * 80,
            width: w * 343,
            padding: EdgeInsets.fromLTRB(w * 10, h * 18, w * 10, h * 17),
            decoration: BoxDecoration(
              color: ThemeColors.colorFFFFFF,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Container(
                  height: h * 45,
                  width: w * 45,
                  alignment: Alignment.center,
                  padding: const EdgeInsets.all(13),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color.fromRGBO(3, 173, 158, 0.1),
                  ),
                  child: ImageIcon(
                    AssetImage(getActivityIcon(provider.message!)),
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                SizedBox(width: w * 11),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        provider.message ?? "",
                        style: tsS14w400c181818,
                      ),
                      Text(
                        // date.toString(),
                        provider.newDate.toString(),
                        style: tsS12w500c979797,
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  String getActivityIcon(String message) {
    if (message.contains('sick')) {
      return 'assets/icons/recent/hospital.png';
    }
    if (message.contains('annual')) {
      return 'assets/icons/recent/annual.png';
    }
    if (message.contains('unpaid')) {
      return 'assets/icons/recent/calendar.png';
    }
    if (message.contains('it')) {
      return 'assets/icons/recent/ticket.png';
    }
    if (message.contains('notification')) {
      return 'assets/icons/notification.png';
    }
    if (message.contains('certificate') ||
        message.contains('noc') ||
        message.contains('letter')) {
      return 'assets/icons/recent/certificate.png';
    }
    if (message.contains('post')) {
      return 'assets/icons/recent/post.png';
    }
    if (message.contains('profile') || message.contains('status')) {
      return 'assets/icons/profile.png';
    }
    if (message.contains('attendance')) {
      return 'assets/icons/attendance.png';
    }
    return 'assets/icons/recent/common.png';
  }

  Widget _clearAllButton() {
    return Padding(
        padding: EdgeInsets.symmetric(vertical: h * 0, horizontal: w * 10),
        child: Consumer<CompanyActivitiesProvider>(
          builder: (context, provider, child) {
            return TextButton(
              onPressed: provider.isClearing
                  ? null
                  : () {
                      provider.clearRecentActivity(context: context);
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: ThemeColors.colorTransparent,
                minimumSize: const Size(10, 36),
              ),
              child: Text(
                "Clear all",
                style: tsS14w500cFFFFFF,
              ),
            );
          },
        ));
  }
}

void onTilePressed(
    {required RecentActivityModel provider,
    required MasterProvider masterProvider,
    required BuildContext context}) async {
  if (provider.moduleName?.toLowerCase() == "user_status" ||
      provider.moduleName?.toLowerCase() == "profile") {
    EasyLoading.show();
    var profile = Provider.of<ProfileProvider>(context, listen: false);
    await profile.getProfileData(context: context);
    var status = Provider.of<UserStatusProvider>(context, listen: false);
    await status.getUserStatus(master: false);
    await status.clear();
    Navigator.pop(context);
    masterProvider.currentIndex = 3;
    EasyLoading.dismiss();
  } else if (provider.moduleName?.toLowerCase() == "leave_status" &&
      provider.dbData?.id != null) {
    final leaveProvider =
        Provider.of<LeaveApplicationProvider>(context, listen: false);

    EasyLoading.show();
    bool isGo = await leaveProvider.getRequestedLeaveOverviewForReportedPerson(
        leaveId: int.parse(provider.dbData!.id.toString()));
    EasyLoading.dismiss();
    if (isGo) {
      PageNavigator.pushSlideRight(
        context: context,
        route: const LeaveRequestOverView(),
      );
    }
  } else if (provider.moduleName?.toLowerCase() == "leave_update" &&
      provider.dbData?.id != null) {
    final provid =
        Provider.of<LeaveApplicationProvider>(context, listen: false);
    EasyLoading.show();
    bool isGo = await provid.getLeaveOverView(
        leaveId: int.parse(provider.dbData!.id.toString()));
    bool isGetRepPerson = await provid.getReportingPerson();
    EasyLoading.dismiss();

    if (isGo && isGetRepPerson) {
      PageNavigator.pushSlideRight(
          context: context, route: const OverViewScreen());
    }
  } else if (provider.moduleName?.toLowerCase() == "ticket_update" &&
      provider.dbData?.id != null) {
    // TicketsProvider provider =
    //     Provider.of<TicketsProvider>(context, listen: false);
    // await provider.getTickets();

    // PageNavigator.push(context: context, route: const Tickets());
    TicketsProvider prov = Provider.of<TicketsProvider>(context, listen: false);

    bool isGo = await prov.getTicketOverview(
        ticketID: int.parse(provider.dbData!.id.toString()));

    if (isGo) {
      PageNavigator.pushSlideRight(
        context: context,
        route: const TicketsOverview(),
      );
    }
    // else {
    //   // showToastText("No details found");
    // }
  } else if (provider.moduleName?.toLowerCase() == "attendance") {
    Provider.of<ProfileProvider>(context, listen: false)
        .getProfileData(context: context);
    AttendanceProvider provider =
        Provider.of<AttendanceProvider>(context, listen: false);
    provider.selectedMonth = DateTime.now();
    provider.getAttendanceList(master: false, context: context);
    EasyLoading.show();
    BLEAttendanceProvider provider1 =
        Provider.of<BLEAttendanceProvider>(context, listen: false);
    await provider1.getAttendanceLogs();
    EasyLoading.dismiss();
    PageNavigator.pushSlideRight(
      context: context,
      route: const AttendanceScreen(),
    );
  } else if (provider.moduleName?.toLowerCase() == "certificates_requests") {
    PageNavigator.pushSlideRight(
      context: context,
      route: const CertificateRequests(),
    );
  } else if (provider.moduleName?.toLowerCase() == "flight_ticket") {
    var provider = Provider.of<FlightTicketProvider>(context, listen: false);
    provider.getAllCountries();
    // bool isGo = await provider.getFlightTicketList();
    provider.currentPage = 0;
    provider.pagingController?.refresh();
    EasyLoading.show();

    PageNavigator.pushSlideRight(
      context: context,
      route: const FlightTicketsListScreen(),
    );

    EasyLoading.dismiss();
  } else if (provider.moduleName?.toLowerCase() == "salary_certificate") {
    final provider =
        Provider.of<SalaryCertificateProvider>(context, listen: false);
    bool isGo = await provider.getSalaryCertificate();
    if (isGo) {
      Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const SalaryCertificate()));
    }
  } else if (provider.moduleName?.toLowerCase() == "experience_certificate") {
    var provider =
        Provider.of<ExperienceCertifcateProvider>(context, listen: false);
    EasyLoading.show();
    bool isGo = await provider.fetchPreviousRequests();
    EasyLoading.dismiss();
    if (isGo) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const ExperienceCertificate(),
        ),
      );
    }
  } else if (provider.moduleName?.toLowerCase() == "driving_licence") {
    final navigator = Navigator.of(context);
    DrivingLicenseProvider provider =
        Provider.of<DrivingLicenseProvider>(context, listen: false);
    bool isGo = await provider.getDrivingLicenseList();
    if (isGo) {
      navigator.push(
          MaterialPageRoute(builder: (context) => const DrivingCertificates()));
    }
  } else if (provider.moduleName?.toLowerCase() == "salary_transfer_letter") {
    var provider =
        Provider.of<SalaryTransferLetterProvider>(context, listen: false);
    EasyLoading.show();
    bool isGo = await provider.fetchPreviousRequests();
    EasyLoading.dismiss();
    if (isGo) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const SalaryTransferCertificate(),
        ),
      );
    }
  } else if (provider.moduleName?.toLowerCase() == "noc") {
    Provider.of<FlightTicketProvider>(context, listen: false)
        .getFlightTicketPersonalInfo();
    final navigator = Navigator.of(context);
    NocProvider provider = Provider.of<NocProvider>(context, listen: false);
    // bool isGo =
    await provider.getNocCertificateList();
    // if (isGo) {

    navigator.push(MaterialPageRoute(
        builder: (context) => const NonObjectionCertificates()));
  } else if (provider.moduleName?.toLowerCase() == "post_update" &&
      provider.dbData?.postId != null) {
    await EasyLoading.show();
    var pvd = Provider.of<UpdatesProvider>(context, listen: false);
    await pvd.getPostDetails(postId: provider.dbData!.postId!.toInt());

    // await
    pvd.getComments(postId: provider.dbData!.postId!.toInt());

    if (pvd.postList.isNotEmpty)
      // ignore: curly_braces_in_flow_control_structures
      PageNavigator.pushSlideRight(
        context: context,
        route: const UpdateDetailedScreenFromRecent(),
      );
    await EasyLoading.dismiss();
  } else if (provider.moduleName?.toLowerCase() == "clear_notification") {
    Provider.of<NotificationProvider>(context, listen: false).showClearButton =
        true;
    PageNavigator.pushSlideRight(
      context: context,
      route: const NotificationScreen(),
    );
  }
}

class EditUpdatesScreenFromRecent extends StatefulWidget {
  final String content;
  final List<PostFile>? file;
  final int? postId;
  // final String? uint8list;
  const EditUpdatesScreenFromRecent({
    super.key,
    required this.content,
    this.file,
    required this.postId,
    // this.uint8list,
  });

  @override
  State<EditUpdatesScreenFromRecent> createState() =>
      _EditUpdatesScreenFromRecentState();
}

class _EditUpdatesScreenFromRecentState
    extends State<EditUpdatesScreenFromRecent> {
  final TextEditingController _contentEditController = TextEditingController();
  late UpdatesProvider _updatesProvider;

  String? thumbnail;
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _contentEditController.text = widget.content;
      _updatesProvider = Provider.of<UpdatesProvider>(context, listen: false);
      if (widget.file != null && widget.file!.isEmpty) {
        _updatesProvider.isVideoRemoved = true;
      }
      if (widget.file != null && widget.file?.first.fileType == 'image') {
        _updatesProvider.isVideoRemoved = true;
        _updatesProvider.alreadyPostImageListFromRecent = widget.file;
      } else {
        _updatesProvider.isVideoRemoved = false;
      }
      if (_updatesProvider.selectedPostsVideo != null) {}
    });
    super.initState();
  }

  @override
  void dispose() {
    _updatesProvider.videoPlayerController?.dispose();

    _contentEditController.dispose();
    super.dispose();
  }

  final QuillController quillController = QuillController.basic();
  final _formKey = GlobalKey<FormState>();
  bool enable = false;
  bool bold = false;
  bool playPause = false;

  @override
  Widget build(BuildContext context) {
    final profileProvider = Provider.of<ProfileProvider>(context);
    final UpdatesProvider updateProvider =
        Provider.of<UpdatesProvider>(context);

    return HisenseScaffold(
      screenTitle: 'Edit Post',
      onTap: updateProvider.removeSelectedPosts,
      actions: [
        Consumer<UpdatesProvider>(builder: (context, provider, child) {
          return InkWell(
            onTap: provider.isCreating == true
                ? null
                : () async {
                    hideKeyboard(context);
                    if (_formKey.currentState!.validate()) {
                      provider.postEdit(
                        postId: widget.postId.toString(),
                        update: quillController.document.toPlainText(),
                        context: context,
                      );
                    }
                  },
            child: Center(
              child: Text(
                "Update",
                style:
                    //!enable ?
                    //  tsS14w500cFFFFFFwop60
                    //:
                    tsS14w500cFFFFFF,
              ),
            ),
          );
        }),
        const SizedBox(
          width: 20,
        )
      ],
      body: Padding(
        padding: const EdgeInsets.fromLTRB(15.0, 15.0, 15.0, 0),
        child: Column(
          children: [
            Expanded(
              child: ListView(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    height: 620 * h,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12)),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(50),
                              child: CachedNetworkImage(
                                height: 50,
                                width: 50,
                                fit: BoxFit.fill,
                                imageUrl: "${profileProvider.profilePic}",
                                errorWidget: (context, url, error) {
                                  return Container(
                                    height: 50,
                                    width: 50,
                                    decoration: BoxDecoration(
                                      color: ThemeColors.primaryColor,
                                      shape: BoxShape.circle,
                                    ),
                                    alignment: Alignment.center,
                                    child: Text(
                                      profileProvider.firstName!
                                          .substring(0, 1)
                                          .toUpperCase(),
                                      style: GoogleFonts.rubik(
                                        color: Colors.white,
                                        fontSize: 20,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                            const SizedBox(width: 10),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(profileProvider.firstName ?? "Username",
                                    style: tsS14w500Black),
                                Text(
                                  profileProvider.designation ?? "",
                                  style: tsS12w400979797,
                                )
                                // const SizedBox(height: 2),
                                // Text(
                                //     profileProvider.data?.firstName.toString() ??
                                //         "Username",
                                //     style: tsS14w500Black),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 5),
                        Expanded(
                          child: Consumer<UpdatesProvider>(
                              builder: (context, provider, _) {
                            return Form(
                              key: _formKey,
                              child: Column(
                                children: [
                                  // Toolbar removed for simplicity
                                  Expanded(
                                    // height: provider.selectedPostsImages
                                    //             .isNotEmpty ||
                                    //         provider.selectedPostsVideo != null
                                    //     ? 100
                                    //     : null,
                                    child: QuillEditor.basic(
                                      controller: quillController,
                                    ),
                                  ),
                                  Column(
                                    children: [
                                      if (widget.file != null &&
                                          widget.file!.isNotEmpty &&
                                          widget.file!.first.fileType ==
                                              "image")
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(top: 8.0),
                                          child: Wrap(
                                            spacing: 10,
                                            runSpacing: 10,
                                            children: provider
                                                        .alreadyPostImageListFromRecent ==
                                                    null
                                                ? []
                                                : provider
                                                    .alreadyPostImageListFromRecent!
                                                    .map((e) {
                                                    int index = provider
                                                        .alreadyPostImageListFromRecent!
                                                        .indexOf(e);

                                                    int urlCount = provider
                                                                .alreadyPostImageListFromRecent ==
                                                            null
                                                        ? 0
                                                        : provider
                                                                .alreadyPostImageListFromRecent!
                                                                .length -
                                                            3;

                                                    return index > 3
                                                        ? const SizedBox()
                                                        : InkWell(
                                                            onTap: () {
                                                              Navigator.of(
                                                                      context)
                                                                  .push(MaterialPageRoute(
                                                                      builder:
                                                                          (context) =>
                                                                              const ImageFullViewScreenFromRecent()));
                                                            },
                                                            child: SizedBox(
                                                              height: index == 0
                                                                  ? 120 * h
                                                                  : 90 * w,
                                                              width: index == 0
                                                                  ? double
                                                                      .infinity
                                                                  : 100,
                                                              child: index <= 2
                                                                  ? Image
                                                                      .network(
                                                                      e.file
                                                                          .toString(),
                                                                      fit: BoxFit
                                                                          .cover,
                                                                    )
                                                                  : Container(
                                                                      height:
                                                                          90 *
                                                                              h,
                                                                      width:
                                                                          90 *
                                                                              w,
                                                                      color: Colors
                                                                          .white,
                                                                      child:
                                                                          Center(
                                                                        child: Text(
                                                                            "+ $urlCount"),
                                                                      ),
                                                                    ),
                                                            ),
                                                          );
                                                  }).toList(),
                                          ),
                                        )
                                      else if (widget.file != null &&
                                          widget.file!.isNotEmpty &&
                                          widget.file!.first.fileType ==
                                              "video" &&
                                          provider.isVideoRemoved == false)
                                        _videoThumbnail(),
                                      const SizedBox(
                                        height: 10,
                                      ),
                                      if (provider.selectedPostsVideo != null)
                                        InkWell(
                                          onTap: () {
                                            if (!playPause) {
                                              provider.videoPlayerController!
                                                  .play();
                                              setState(() {
                                                playPause = !playPause;
                                              });
                                            } else {
                                              provider.videoPlayerController!
                                                  .pause();
                                              setState(() {
                                                playPause = !playPause;
                                              });
                                            }
                                          },
                                          child: Stack(
                                            children: [
                                              SizedBox(
                                                height: 200 * h,
                                                width: 360 * w,
                                                child: AspectRatio(
                                                  aspectRatio: provider
                                                      .videoPlayerController!
                                                      .value
                                                      .aspectRatio,
                                                  child: VideoPlayer(provider
                                                      .videoPlayerController!),
                                                ),
                                              ),
                                              Align(
                                                  alignment: Alignment.topRight,
                                                  child: Container(
                                                    margin:
                                                        const EdgeInsets.all(4),
                                                    width: 35,
                                                    height: 35,
                                                    decoration:
                                                        const BoxDecoration(
                                                            shape:
                                                                BoxShape.circle,
                                                            color:
                                                                Colors.white),
                                                    child: Center(
                                                      child: IconButton(
                                                          onPressed: () {
                                                            provider
                                                                .removeVideo();
                                                          },
                                                          icon: const Icon(
                                                            Icons.delete,
                                                            size: 18,
                                                          )),
                                                    ),
                                                  ))
                                            ],
                                          ),
                                        ),
                                      Wrap(
                                        spacing: 10,
                                        runSpacing: 10,
                                        children: provider.selectedPostsImages
                                            .map((e) {
                                          int index = provider
                                              .selectedPostsImages
                                              .indexOf(e);

                                          return index > 3
                                              ? const SizedBox()
                                              : InkWell(
                                                  onTap: () {
                                                    Navigator.of(context).push(
                                                        MaterialPageRoute(
                                                            builder: (context) =>
                                                                const ImageFullViewScreenXFile()));
                                                  },
                                                  child: SizedBox(
                                                    height:
                                                        index == 0 ? 120 : 90,
                                                    width: index == 0
                                                        ? double.infinity
                                                        : 100,
                                                    child: index <= 2
                                                        ? Image.file(
                                                            io.File(e.path),
                                                            fit: BoxFit.cover,
                                                          )
                                                        : Container(
                                                            height: 90,
                                                            width: 90,
                                                            color: Colors.white,
                                                            child: Center(
                                                              child: Text(
                                                                  "+ ${provider.selectedPostsImages.length - 3}"),
                                                            ),
                                                            // decoration: BoxDecoration(
                                                            //     image: DecorationImage(
                                                            //         image: FileImage(
                                                            //             File(e.path)))),
                                                          ),
                                                  ),
                                                );
                                        }).toList(),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            );
                          }),
                        ),
                        Consumer<UpdatesProvider>(
                            builder: (context, provider, _) {
                          return Row(
                            children: [
                              InkWell(
                                onTap: provider.selectedPostsVideo == null &&
                                        provider.isVideoRemoved == true
                                    ? () async {
                                        await provider.addImages(context);
                                      }
                                    : null,
                                child: ImageIcon(
                                  const AssetImage("assets/icons/gallery.png"),
                                  color: ThemeColors.color979797,
                                ),
                              ),
                              const SizedBox(
                                width: 30,
                              ),
                              InkWell(
                                onTap:
                                    provider.selectedPostsImages.isNotEmpty ||
                                            provider.alreadyPostImageList!
                                                .isNotEmpty ||
                                            thumbnail != null ||
                                            provider.isVideoRemoved == false ||
                                            provider.selectedPostsVideo != null
                                        ? () {}
                                        : () async {
                                            await provider.addVideos(context);
                                          },
                                child: ImageIcon(
                                  const AssetImage("assets/icons/video.png"),
                                  color: ThemeColors.color979797,
                                ),
                              ),
                            ],
                          );
                        })
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _videoThumbnail() {
    return SizedBox(
      height: 200 * h,
      width: 360 * w,
      child: Stack(
        children: [
          FutureBuilder<String?>(
            future: _getVideoThumbnail(widget.file!.first.file!),
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                return Consumer<UpdatesProvider>(
                  builder: (context, provider, child) {
                    return Stack(
                      children: [
                        Image.file(
                          io.File(snapshot.data!),
                          height: 200 * h,
                          width: 360 * w,
                          fit: BoxFit.fill,
                        ),
                        Align(
                            alignment: Alignment.topRight,
                            child: Container(
                              margin: const EdgeInsets.all(4),
                              width: 35,
                              height: 35,
                              decoration: const BoxDecoration(
                                  shape: BoxShape.circle, color: Colors.white),
                              child: Center(
                                child: IconButton(
                                    onPressed: () {
                                      provider.isVideoRemoved = true;
                                      provider.removedImageIdList.clear();
                                      provider.addRemoveImageID(
                                          widget.file!.first.id!.toInt());
                                    },
                                    icon: const Icon(
                                      Icons.delete,
                                      size: 18,
                                    )),
                              ),
                            ))
                      ],
                    );
                  },
                );
              }
              return const Center(
                child: CircularProgressIndicator(),
              );
            },
          ),
          const Center(
            child: Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 30,
            ),
          )
        ],
      ),
    );
  }
}

Future<String?> _getVideoThumbnail(String? video) async {
  String? uint8list;
//  uint8list = await VideoThumbnail.thumbnailFile(
//     video: video ?? '',
//     thumbnailPath: (await getTemporaryDirectory()).path,
//     imageFormat: ImageFormat.PNG,
//     maxHeight:
//         100, // specify the height of the thumbnail, let the width auto-scaled to keep the source aspect ratio
//     maxWidth: 360,
//     quality: 100,
//   );
  return uint8list;
}

class ImageFullViewScreenFromRecent extends StatefulWidget {
  const ImageFullViewScreenFromRecent({super.key});

  @override
  State<ImageFullViewScreenFromRecent> createState() =>
      _ImageFullViewScreenFromRecentState();
}

class _ImageFullViewScreenFromRecentState
    extends State<ImageFullViewScreenFromRecent> {
  int currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Consumer<UpdatesProvider>(
      builder: (context, provider, child) {
        return Scaffold(
            backgroundColor: Colors.black,
            body: PageView.builder(
              onPageChanged: (value) {
                setState(() {
                  currentIndex = value;
                });
              },
              itemCount: provider.alreadyPostImageListFromRecent?.length ?? 0,
              itemBuilder: (context, index) {
                final data =
                    provider.alreadyPostImageListFromRecent?[index].file;
                return SafeArea(
                  child: Column(
                    children: [
                      SizedBox(
                        height: 60 * h,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                                icon: const Icon(
                                  Icons.arrow_back_ios,
                                  color: Colors.white,
                                )),
                            IconButton(
                                onPressed: () {
                                  provider.removeAlreadySelectedImageFromRecent(
                                      index, context);
                                },
                                icon: const Icon(
                                  Icons.delete,
                                  color: Colors.white,
                                ))
                          ],
                        ),
                      ),
                      Expanded(child: Image.network(data.toString())),
                    ],
                  ),
                );
              },
            ));
      },
    );
  }
}
