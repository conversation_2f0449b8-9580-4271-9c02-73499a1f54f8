// ignore_for_file: use_build_context_synchronously

import 'dart:ui';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:chewie/chewie.dart';
import 'package:e8_hr_portal/model/post_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/recent_activites/recent_activites.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';
import '../../../util/page_navigator.dart';
import 'dart:io' as io;

class UpdateDetailedScreenFromRecent extends StatefulWidget {
  const UpdateDetailedScreenFromRecent({
    super.key,
  });

  @override
  State<UpdateDetailedScreenFromRecent> createState() =>
      _UpdateDetailedScreenFromRecentState();
}

class _UpdateDetailedScreenFromRecentState
    extends State<UpdateDetailedScreenFromRecent> {
  final TextEditingController _commentController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
        screenTitle: 'Latest Posts',
        body: Consumer<UpdatesProvider>(
          builder: (context, provider, child) {
            final post = provider.postList.first;
            return provider.commentsLoading == true ||
                    provider.isUpdatesLoading == true
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : Padding(
                    padding: const EdgeInsets.fromLTRB(15, 15.0, 15.0, 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                const SizedBox(height: 15),
                                SeeAllUpdateCardDetailsFromRecent(
                                  postId: post.id,
                                  isLiked: post.userLiked.toString(),
                                  profileImage: post.profilePhoto,
                                  name: post.name,
                                  content: post.description,
                                  designation: post.designation,
                                  date: post.createdAt,
                                  likeCount: post.like,
                                  commentCount: post.comments,
                                  color: Colors.white,
                                  isOwner: post.isOwner,
                                  file: post.file,
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                          0, 30, 15, 0),
                                      child: Text(
                                        // 'Comments ($comcount)',
                                        'Comments ',
                                        style: GoogleFonts.poppins(
                                          fontSize: 17,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 20),
                                    if (provider.commentsList != null &&
                                        provider
                                            .commentsList!.records!.isNotEmpty)
                                      Container(
                                        padding: const EdgeInsets.all(10),
                                        decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(12)),
                                        child: ListView.separated(
                                          shrinkWrap: true,
                                          physics:
                                              const BouncingScrollPhysics(),
                                          itemCount: provider.commentsList
                                                  ?.records?.length ??
                                              0,
                                          separatorBuilder: (context, index) {
                                            return const SizedBox(
                                              height: 08,
                                            );
                                          },
                                          itemBuilder: (context, index) {
                                            final data = provider
                                                .commentsList?.records?[index];

                                            // DateTime date = DateTime.parse(
                                            //     data!.createdAt.toString());

                                            return Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  children: [
                                                    Expanded(
                                                      child: Row(
                                                        children: [
                                                          ClipRRect(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        50),
                                                            child:
                                                                CachedNetworkImage(
                                                              height: 50,
                                                              width: 50,
                                                              fit: BoxFit.fill,
                                                              imageUrl: data!
                                                                  .profilePhoto
                                                                  .toString(),
                                                              errorWidget:
                                                                  (context, url,
                                                                      error) {
                                                                return Container(
                                                                  height: 50,
                                                                  width: 50,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: ThemeColors
                                                                        .primaryColor,
                                                                    shape: BoxShape
                                                                        .circle,
                                                                  ),
                                                                  alignment:
                                                                      Alignment
                                                                          .center,
                                                                  child: Text(
                                                                    data.name!
                                                                        .substring(
                                                                            0,
                                                                            1),
                                                                    style: GoogleFonts
                                                                        .poppins(
                                                                      color: Colors
                                                                          .white,
                                                                      fontSize:
                                                                          16,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w700,
                                                                    ),
                                                                  ),
                                                                );
                                                              },
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                              width: 15),
                                                          Expanded(
                                                            child: Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Text(
                                                                  data.name ??
                                                                      '',
                                                                  style:
                                                                      GoogleFonts
                                                                          .rubik(
                                                                    color: const Color(
                                                                        0xFF1E2138),
                                                                    fontSize:
                                                                        16,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w500,
                                                                  ),
                                                                ),
                                                                const SizedBox(
                                                                  height: 2,
                                                                ),
                                                                Row(
                                                                  // crossAxisAlignment:
                                                                  //     CrossAxisAlignment
                                                                  //         .start,
                                                                  // mainAxisAlignment:
                                                                  //     MainAxisAlignment
                                                                  //         .start,
                                                                  children: [
                                                                    ///////////////////////////////////////
                                                                    if (data.designation !=
                                                                        null)
                                                                      Text(
                                                                          data.designation!.length > 18
                                                                              ? data.designation.toString().substring(0, 18)
                                                                              : data.designation.toString(),
                                                                          style: tsS12w400979797),
                                                                    Container(
                                                                      margin: const EdgeInsets
                                                                          .only(
                                                                          left:
                                                                              6.0),
                                                                      width:
                                                                          3.5 *
                                                                              w,
                                                                      height:
                                                                          3.5 *
                                                                              h,
                                                                      decoration: BoxDecoration(
                                                                          shape: BoxShape
                                                                              .circle,
                                                                          color:
                                                                              ThemeColors.color979797),
                                                                    ),
                                                                    Expanded(
                                                                      child:
                                                                          Padding(
                                                                        padding: const EdgeInsets
                                                                            .only(
                                                                            left:
                                                                                6.0),
                                                                        child:
                                                                            Text(
                                                                          // timeago.format(date),
                                                                          data.createdAt
                                                                              .toString(),
                                                                          style:
                                                                              GoogleFonts.rubik(
                                                                            fontWeight:
                                                                                FontWeight.w400,
                                                                            fontSize:
                                                                                12,
                                                                            color:
                                                                                ThemeColors.titleColor,
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ],
                                                                )
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    if (data.isOwner == true)
                                                      PopupMenuButton(
                                                        icon: const Icon(
                                                          Icons.more_vert,
                                                        ),
                                                        onSelected: (value) {
                                                          if (value ==
                                                              'delete') {
                                                            showDialog(
                                                                context:
                                                                    context,
                                                                builder:
                                                                    (ctxt) {
                                                                  return AlertDialog(
                                                                    title: const Text(
                                                                        'Confirm delete'),
                                                                    content:
                                                                        const Text(
                                                                            'Are you sure you want to delete?'),
                                                                    actions: [
                                                                      ElevatedButton(
                                                                        onPressed:
                                                                            () =>
                                                                                Navigator.pop(context),
                                                                        style: ElevatedButton
                                                                            .styleFrom(
                                                                          backgroundColor:
                                                                              Colors.grey[300],
                                                                        ),
                                                                        child:
                                                                            const Text(
                                                                          'Cancel',
                                                                          style:
                                                                              TextStyle(color: Colors.black),
                                                                        ),
                                                                      ),
                                                                      ElevatedButton(
                                                                        onPressed:
                                                                            () async {
                                                                          Navigator.pop(
                                                                              context);
                                                                          provider.deleteComment(
                                                                              commentId: data.id.toString(),
                                                                              postId: post.id!.toInt(),
                                                                              context: ctxt);

                                                                          //comment delete function
                                                                        },
                                                                        style: ElevatedButton
                                                                            .styleFrom(
                                                                          backgroundColor:
                                                                              ThemeColors.secondaryColor,
                                                                        ),
                                                                        child: const Text(
                                                                            'Delete'),
                                                                      ),
                                                                    ],
                                                                  );
                                                                });
                                                          }
                                                        },
                                                        itemBuilder: (context) {
                                                          return [
                                                            const PopupMenuItem(
                                                              value: 'delete',
                                                              child: Text(
                                                                  'Delete comment'),
                                                            ),
                                                          ];
                                                        },
                                                      )
                                                  ],
                                                ),
                                                const SizedBox(height: 13),
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 69),
                                                  child: Container(
                                                    width: 260 * w,
                                                    // height: 40 * h,
                                                    padding:
                                                        const EdgeInsets.all(
                                                            10),
                                                    decoration: BoxDecoration(
                                                        color: ThemeColors
                                                            .colorF8F8F8,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(6)),
                                                    child: Align(
                                                      alignment:
                                                          Alignment.topLeft,
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            data.description ??
                                                                'comment',
                                                            style: GoogleFonts
                                                                .rubik(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w400,
                                                              fontSize: 14,
                                                              color: const Color(
                                                                  0xFF1E2138),
                                                            ),
                                                          ),
                                                          const Divider(),
                                                          InkWell(
                                                            onTap: () async {
                                                              await provider.likeComment(
                                                                  commentId: data
                                                                      .id
                                                                      .toString(),
                                                                  postId: post
                                                                      .id
                                                                      .toString(),
                                                                  context:
                                                                      context,
                                                                  like: data.userLiked ==
                                                                          true
                                                                      ? false
                                                                      : true);
                                                            },
                                                            child: Row(
                                                              children: [
                                                                Icon(
                                                                  data.userLiked ==
                                                                          true
                                                                      ? Icons
                                                                          .favorite
                                                                      : Icons
                                                                          .favorite_outline_rounded,
                                                                  size: 15,
                                                                  color: data.userLiked ==
                                                                          true
                                                                      ? Colors
                                                                          .red
                                                                      : Colors
                                                                          .black,
                                                                ),
                                                                const SizedBox(
                                                                    width: 5),
                                                                Text(
                                                                  '${data.like ?? 0} ${data.like == 1 ? 'Like' : 'Likes'}',
                                                                  style:
                                                                      GoogleFonts
                                                                          .rubik(
                                                                    fontSize:
                                                                        12,
                                                                    color: const Color(
                                                                        0xFF1E2138),
                                                                  ),
                                                                )
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            );
                                          },
                                        ),
                                      ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 20,
                                )
                              ],
                            ),
                          ),
                        ),
                        Container(
                          height: 56 * h,
                          margin: const EdgeInsets.only(bottom: 30),
                          child: Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _commentController,
                                  textCapitalization:
                                      TextCapitalization.sentences,
                                  decoration: InputDecoration(
                                      border: OutlineInputBorder(
                                          borderSide: BorderSide.none,
                                          borderRadius:
                                              BorderRadius.circular(212)),
                                      enabledBorder: OutlineInputBorder(
                                          borderSide: BorderSide.none,
                                          borderRadius:
                                              BorderRadius.circular(212)),
                                      focusedBorder: OutlineInputBorder(
                                          borderSide: BorderSide.none,
                                          borderRadius:
                                              BorderRadius.circular(25)),
                                      hintText: 'Write your comment here',
                                      hintStyle: const TextStyle(
                                        color: Color(0xFFC2C2C2),
                                        fontSize: 16,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      fillColor: Colors.white,
                                      filled: true,
                                      suffixIcon: TextButton(
                                        onPressed: () async {
                                          if (_commentController
                                              .text.isNotEmpty) {
                                            hideKeyboard(context);
                                            await provider.postComment(
                                                context: context,
                                                postId: post.id!.toInt(),
                                                comment:
                                                    _commentController.text);
                                            _commentController.clear();
                                          }
                                        },
                                        child: Text(
                                          'Post',
                                          style: tsS14w500F03AD9E,
                                        ),
                                      )),
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  );
          },
        ));
  }
}

class SeeAllUpdateCardDetailsFromRecent extends StatelessWidget {
  final String? isLiked;
  final int? postId;

  final Color color;
  final String? name;
  final String? designation;
  final String? content;
  final int? likeCount;
  final int? commentCount;
  final String? date;
  final String? profileImage;
  final bool? isOwner;
  final List<PostFile>? file;

  const SeeAllUpdateCardDetailsFromRecent(
      {super.key,
      required this.profileImage,
      required this.name,
      required this.isLiked,
      required this.designation,
      required this.content,
      required this.likeCount,
      required this.commentCount,
      required this.date,
      required this.color,
      this.file,
      // required this.userId,
      required this.postId,
      required this.isOwner});

  final bool mounted = true;

  @override
  Widget build(BuildContext context) {
    int? length;
    double? height;
    double? width;
    if (file != null && file!.isNotEmpty) {
      length = file!.length;
    }

    return IntrinsicHeight(
      child: Card(
        color: color,
        margin: const EdgeInsets.all(0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(50),
                        child: CachedNetworkImage(
                          height: 50,
                          width: 50,
                          fit: BoxFit.fill,
                          imageUrl: profileImage.toString(),
                          errorWidget: (context, url, error) {
                            return Container(
                              height: 50,
                              width: 50,
                              decoration: BoxDecoration(
                                color: ThemeColors.primaryColor,
                                shape: BoxShape.circle,
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                name?.substring(0, 1).toUpperCase() ?? '',
                                style: GoogleFonts.rubik(
                                    fontSize: 22,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.white),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (name != null)
                              Text(
                                name.toString(),
                                overflow: TextOverflow.ellipsis,
                                style: GoogleFonts.rubik(
                                  color: const Color(0xFF1E2138),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            if (designation != null)
                              Text(
                                designation.toString(),
                                style: GoogleFonts.rubik(
                                  color: const Color(0xFF1E2138),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w300,
                                ),
                              ),
                          ],
                        ),
                      ),
                      if (isOwner == true)
                        PopupMenuButton(
                          icon: Icon(
                            Icons.more_vert_outlined,
                            color: ThemeColors.color979797,
                          ),
                          onSelected: (value) {
                            if (value == 'delete') {
                              showDialog(
                                  context: context,
                                  builder: (ctxt) {
                                    return AlertDialog(
                                      title: const Text('Confirm delete'),
                                      content: const Text(
                                          'Are you sure you want to delete?'),
                                      actions: [
                                        ElevatedButton(
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.grey[300],
                                          ),
                                          child: const Text(
                                            'Cancel',
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        ),
                                        Consumer<UpdatesProvider>(
                                          builder: (context, provider, child) {
                                            return ElevatedButton(
                                              onPressed: () {
                                                provider.deleteUpdateFromRecent(
                                                    postId: postId.toString(),
                                                    context: context);
                                                Navigator.pop(context);
                                                showSnackBarMessage(
                                                    context: context,
                                                    msg:
                                                        'Update deleted successfully');
                                                Navigator.pop(context);
                                              },
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor:
                                                    ThemeColors.secondaryColor,
                                              ),
                                              child: const Text('Delete'),
                                            );
                                          },
                                        )
                                      ],
                                    );
                                  });
                            } else if (value == 'edit') {
                              showDialog(
                                  context: context,
                                  builder: (ctxt) {
                                    return AlertDialog(
                                      title: const Text('Confirm edit'),
                                      content: const Text(
                                          'Are you sure  want to edit the post?'),
                                      actions: [
                                        ElevatedButton(
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.grey[300],
                                          ),
                                          child: const Text(
                                            'Cancel',
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        ),
                                        Consumer<UpdatesProvider>(
                                          builder: (context, provider, child) {
                                            return ElevatedButton(
                                              onPressed: () {
                                                Navigator.pop(context);
                                                Navigator.pop(context);
                                                provider.selectedPostsImages
                                                    .clear();
                                                provider.removedImageIdList
                                                    .clear();
                                                provider.selectedPostsVideo ==
                                                    null;
                                                provider
                                                    .alreadyPostImageListFromRecent
                                                    ?.clear();
                                                provider.isVideoRemoved = false;
                                                Navigator.of(context)
                                                    .push(MaterialPageRoute(
                                                  builder: (context) =>
                                                      EditUpdatesScreenFromRecent(
                                                          postId:
                                                              postId?.toInt(),
                                                          file: file,
                                                          content: content
                                                              .toString()),
                                                ));
                                              },
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor:
                                                    ThemeColors.secondaryColor,
                                              ),
                                              child: const Text('Edit'),
                                            );
                                          },
                                        )
                                      ],
                                    );
                                  });
                            }
                          },
                          itemBuilder: (context) {
                            return [
                              const PopupMenuItem(
                                value: 'edit',
                                child: Text('Edit'),
                              ),
                              const PopupMenuItem(
                                value: 'delete',
                                child: Text('Delete'),
                              ),
                            ];
                          },
                        )
                    ],
                  ),
                  const SizedBox(height: 9),
                  if (content != null)
                    SizedBox(
                      height: ((file != null) &&
                              (file!.isNotEmpty) &&
                              (content.toString().length >= 200))
                          ? 200 * h
                          : ((file != null) &&
                                  (file!.isNotEmpty) &&
                                  (content.toString().length <= 200)
                              ? 90 * h
                              : 120 * h),
                      child: ListView(shrinkWrap: true, children: [
                        Html(
                          data: content,
                          shrinkWrap: true,
                        ),
                      ]),
                    ),
                  // Text(
                  //   parsedString.toString(),
                  //   overflow: TextOverflow.clip,
                  //   style: tsS12w400979797,
                  // ),
                ],
              ),
            ),
            if (file != null && file!.isNotEmpty)
              Container(
                padding: const EdgeInsets.only(left: 8.0),
                child: Wrap(
                  runSpacing: 1,
                  spacing: 1,
                  children: file!.map((e) {
                    int index = file!.indexOf(e);

                    if (length! == 1) {
                      height = 170 * h;
                      width = 329 * w;
                    } else if (length == 2) {
                      height = 170 * h;
                      width = 160 * w;
                    } else if (length == 3) {
                      if (index == 0) {
                        height = 170 * h;
                        width = 329 * w;
                      } else {
                        height = 86 * h;
                        width = 164 * w;
                      }
                    } else if (length >= 4) {
                      if (index == 0) {
                        height = 170 * h;
                        width = 329 * w;
                      } else {
                        height = 86 * h;
                        width = 109 * w;
                      }
                    }
                    if (file!.first.fileType == 'video') {
                      return InkWell(
                        onTap: () {
                          PageNavigator.push(
                            context: context,
                            route: VideoViewScreenFromRecent(file: file),
                          );
                        },
                        child: SizedBox(
                          height: 200 * h,
                          width: 360 * w,
                          child: Stack(
                            children: [
                              FutureBuilder<String?>(
                                future: _getVideoThumbnail(file!.first.file!),
                                builder: (context, snapshot) {
                                  if (snapshot.hasData) {
                                    return Image.file(
                                      io.File(snapshot.data!),
                                      height: 200 * h,
                                      width: 360 * w,
                                      fit: BoxFit.fill,
                                    );
                                  }
                                  return const Center(
                                    child: CircularProgressIndicator(),
                                  );
                                },
                              ),
                              const Center(
                                child: Icon(
                                  Icons.play_arrow,
                                  color: Colors.white,
                                  size: 30,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }

                    return index > 3
                        ? const SizedBox()
                        : InkWell(
                            onTap: () async {
                              PageNavigator.push(
                                  context: context,
                                  route: PostImageViewerFromRecent(
                                    fileView: file,
                                  ));
                            },
                            child: SizedBox(
                                // height: (index == 0) ? 170 * h : 86 * h,
                                // width: (index == 0) ? 329 * w : 106 * w,
                                height: height,
                                width: width,
                                child: index <= 2
                                    ? Image.network(
                                        e.file.toString(),
                                        fit: BoxFit.cover,
                                      )
                                    : Container(
                                        height: 70 * h,
                                        width: 106 * w,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          image: DecorationImage(
                                              image: NetworkImage(
                                                e.file.toString(),
                                              ),
                                              fit: BoxFit.fill),
                                        ),
                                        child: ClipRRect(
                                          // make sure we apply clip it properly
                                          child: BackdropFilter(
                                            filter: ImageFilter.blur(
                                                sigmaX: 2, sigmaY: 2),
                                            child: Container(
                                              alignment: Alignment.center,
                                              color:
                                                  Colors.grey.withOpacity(0.1),
                                              child: Text(
                                                '+ ${file!.length - 3}',
                                                style: tsS12w600FFFFF,
                                              ),
                                            ),
                                          ),
                                        ),
                                      )),
                          );
                  }).toList(),
                ),
              ),
            const Spacer(),
            // const SizedBox(height: 15),
            const Divider(
              color: Color(0xFFF3F3F3),
              thickness: 1,
              height: 0,
              endIndent: 20,
              indent: 20,
            ),
            const SizedBox(height: 15),
            Padding(
              padding: const EdgeInsets.fromLTRB(15, 0, 15, 15),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Consumer<UpdatesProvider>(
                    builder: (context, provider, child) {
                      return InkWell(
                        onTap: () async {
                          await EasyLoading.show();
                          if (!mounted) return;
                          await provider.likePost(
                              postId: postId.toString(),
                              context: context,
                              like: isLiked == 'true' ? false : true);

                          await provider.getPostDetails(postId: postId!);
                          await EasyLoading.dismiss();
                        },
                        child: Row(
                          children: [
                            Icon(
                              isLiked == 'true'
                                  ? Icons.favorite
                                  : Icons.favorite_outline_rounded,
                              size: 15,
                              color:
                                  isLiked == 'true' ? Colors.red : Colors.black,
                            ),
                            const SizedBox(width: 5),
                            Text(
                              '${likeCount ?? 0} ${likeCount == 1 ? 'Like' : 'Likes'}',
                              style: GoogleFonts.rubik(
                                fontSize: 12,
                                color: const Color(0xFF1E2138),
                              ),
                            )
                          ],
                        ),
                      );
                    },
                  ),
                  const Spacer(),
                  const ImageIcon(
                    AssetImage('assets/icons/comments.png'),
                    size: 15,
                  ),
                  const SizedBox(width: 5),
                  Text(
                    '${commentCount ?? 0} ${commentCount == 1 ? 'Comment' : 'Comments'}',
                    style: GoogleFonts.rubik(
                      fontSize: 12,
                      color: const Color(0xFF1E2138),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    // formatDateFromString(
                    //     date.toString(), 'yyyy-MM-dd', 'dd MMM yyyy'),
                    date.toString(),
                    style: GoogleFonts.rubik(
                      fontSize: 12,
                      color: const Color(0xFF1E2138),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<String?> _getVideoThumbnail(String? video) async {
    String? uint8list;
    //  uint8list = await VideoThumbnail.thumbnailFile(
    //     video: video ?? '',
    //     thumbnailPath: (await getTemporaryDirectory()).path,
    //     imageFormat: ImageFormat.PNG,
    //     maxHeight:
    //         100, // specify the height of the thumbnail, let the width auto-scaled to keep the source aspect ratio
    //     maxWidth: 360,
    //     quality: 100,
    //   );

    return uint8list;
  }
}

class PostImageViewerFromRecent extends StatelessWidget {
  final List<PostFile>? fileView;
  const PostImageViewerFromRecent({super.key, required this.fileView});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.black,
        leading: InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: const Icon(
            Icons.arrow_back_sharp,
            color: Colors.white,
          ),
        ),
      ),
      body: PhotoViewGallery.builder(
        itemCount: fileView!.length,
        scrollPhysics: const ClampingScrollPhysics(),
        builder: (context, index) {
          return PhotoViewGalleryPageOptions(
            imageProvider: NetworkImage(fileView![index].file.toString()),
            // initialScale: PhotoViewComputedScale.contained * 0.8,
            // heroAttributes:
            //     PhotoViewHeroAttributes(tag: fileView![index].file.toString()),
          );
        },
      ),
    );
  }
}

class VideoViewScreenFromRecent extends StatefulWidget {
  final List<PostFile>? file;
  const VideoViewScreenFromRecent({super.key, required this.file});

  @override
  State<VideoViewScreenFromRecent> createState() =>
      _VideoViewScreenFromRecentState();
}

class _VideoViewScreenFromRecentState extends State<VideoViewScreenFromRecent> {
  VideoPlayerController? videoPlayerController;
  ChewieController? chewieController;
  @override
  void initState() {
    if (widget.file != null && widget.file!.isNotEmpty) {
      videoPlayerController =
          VideoPlayerController.networkUrl(Uri.parse(widget.file!.first.file!));
      videoPlayerController!.addListener(() {});
      videoPlayerController!.setLooping(true);
      videoPlayerController!.initialize();
      videoPlayerController!.play();
      chewieController = ChewieController(
          fullScreenByDefault: true,
          videoPlayerController: videoPlayerController!,
          autoPlay: true,
          looping: true,
          aspectRatio: 16 / 9);
    }
    super.initState();
  }

  @override
  void dispose() {
    videoPlayerController!.dispose();
    chewieController!.dispose();
    super.dispose();
  }

  bool playPause = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Container(
          alignment: Alignment.center,
          color: Colors.black,
          child: widget.file!.first.fileType == 'video'
              ?

              // final chewieController = ChewieController(
              //   videoPlayerController: videoPlayerController,
              //   autoPlay: true,
              //   looping: true,
              //   aspectRatio: 16 / 8,
              // );
              InkWell(
                  onTap: () {
                    debugPrint(
                        '========================= clicked ${videoPlayerController!.dataSource}');
                    // if (!playPause) {
                    //   videoPlayerController.play();

                    //   playPause = !playPause;
                    // } else {
                    //   videoPlayerController.pause();

                    //   playPause = !playPause;
                    // }
                  },
                  child: SizedBox(
                    height: 200 * h,
                    width: 360 * w,
                    child: AspectRatio(
                      aspectRatio: videoPlayerController!.value.aspectRatio,
                      child: Stack(
                        children: [
                          Chewie(controller: chewieController!)
                          // VideoPlayer(videoPlayerController!),
                          // _ControlsOverlay(
                          //   controller: videoPlayerController,
                          // ),
                          // Align(
                          //   alignment: Alignment.bottomCenter,
                          //   child: VideoProgressIndicator(
                          //       videoPlayerController,
                          //       padding: const EdgeInsets.fromLTRB(
                          //           10, 0, 20, 10),
                          //       colors: const VideoProgressColors(
                          //           backgroundColor: Colors.grey,
                          //           playedColor: Colors.white),
                          //       allowScrubbing: true),
                          // ),
                        ],
                      ),
                    ),
                  ),
                )
              : const SizedBox()),
    );
  }
}
