import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/team_member/widgets/filter_screen_sections/button_row_section.dart';
import 'package:e8_hr_portal/view/team_member/widgets/filter_screen_sections/country_section.dart';
import 'package:e8_hr_portal/view/team_member/widgets/filter_screen_sections/designation_section.dart';
import 'package:e8_hr_portal/view/team_member/widgets/filter_screen_sections/work_status_section.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import '../../util/size_config.dart';

class TeamMembersFilterScreen extends StatelessWidget {
  const TeamMembersFilterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Filter',
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
        child: Column(
          children: [
            Expanded(
              child: ListView(
                physics: const ClampingScrollPhysics(),
                children: [
                  const CountrySection(),
                  DesignationSection(),
                  const WorkStatusSection(),
                ],
              ),
            ),
            SizedBox(
              height: h * 60,
            )
          ],
        ),
      ),
      floatingWidget: Padding(
        padding: EdgeInsets.only(left: 28.0 * w),
        child: const ButtonRowSection(),
      ),
    );
  }
}

class Heading extends StatelessWidget {
  final String title;
  const Heading({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: tsS16w500,
    );
  }
}
