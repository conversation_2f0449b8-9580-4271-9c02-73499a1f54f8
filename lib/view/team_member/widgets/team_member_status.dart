import 'package:e8_hr_portal/util/colors.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class TeamMemberStatus extends StatelessWidget {
  final String? status;
  final String? subAction;
  const TeamMemberStatus({this.status, this.subAction, super.key});

  @override
  Widget build(BuildContext context) {
    if (status != null) {
      Color? color;
      switch (status) {
        case 'Active':
          color = ThemeColors.color32936F;
          break;
        case 'At Work':
          color = ThemeColors.color5570F1;
          break;
        case 'Work From Home':
          color = ThemeColors.colorE5B900;
          break;
        default:
          color = ThemeColors.colorF64D44;
      }
      return Align(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
          decoration: BoxDecoration(
            color: color.withOpacity(0.15),
            borderRadius: BorderRadius.circular(6),
          ),
          child: FittedBox(
            child: Column(
              children: [
                Text(
                  status ?? '',
                  style: GoogleFonts.poppins(
                    color: color,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (subAction != null)
                  Text(
                    '($subAction)',
                    style: GoogleFonts.poppins(
                      color: color,
                      fontSize: 8,
                      fontWeight: FontWeight.w600,
                    ),
                  )
              ],
            ),
          ),
        ),
      );
    }
    return const SizedBox();
  }
}
