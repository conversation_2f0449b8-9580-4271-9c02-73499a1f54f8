import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/view/team_member/widgets/employee_badge_section.dart';
import 'package:e8_hr_portal/view/team_member/widgets/team_member_status.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/model/team_member_model.dart';

class TeamMemberCards extends StatelessWidget {
  final TeamMemberModel teamMemberModel;

  const TeamMemberCards({
    super.key,
    required this.teamMemberModel,
  });

  @override
  Widget build(BuildContext context) {
    String? designation;
    String? status;
    String? subAction;
    if (teamMemberModel.designation != null &&
        teamMemberModel.designation!.isNotEmpty) {
      designation = teamMemberModel.designation!.first.name;
    }
    if (teamMemberModel.userStatus != null &&
        teamMemberModel.userStatus!.isNotEmpty) {
      status = teamMemberModel.userStatus!.first.name;
      subAction = teamMemberModel.userStatus!.first.subAction;
    }

    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      elevation: 0,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(10, 8, 10, 8),
        child: Column(
          children: [
            Row(
              children: [
                const SizedBox(width: 10),
                ClipRRect(
                  borderRadius: BorderRadius.circular(5.0),
                  child: ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: teamMemberModel.profilePic.toString(),
                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) {
                        return Container(
                          height: 50,
                          width: 50,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            shape: BoxShape.circle,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            teamMemberModel.name
                                    ?.substring(0, 1)
                                    .toUpperCase()
                                    .toUpperCase() ??
                                '',
                            style: GoogleFonts.rubik(
                              fontSize: 22,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width - 240,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              teamMemberModel.name.toString(),
                              overflow: TextOverflow.ellipsis,
                              style: tsS14w500,
                            ),
                            if (designation != null)
                              Align(
                                alignment: Alignment.topLeft,
                                child: FittedBox(
                                  child: Text(
                                    designation.toString(),
                                    overflow: TextOverflow.ellipsis,
                                    style: tsS12grey,
                                  ),
                                ),
                              ),
                            Align(
                              alignment: Alignment.centerLeft,
                              child: EmployeeBadgeSectionTeamMember(
                                badgeList: teamMemberModel.badge ?? [],
                              ),
                            ),
                          ],
                        ),
                      ),
                      TeamMemberStatus(
                          status: status,
                          subAction: subAction,
                          key: UniqueKey()),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
