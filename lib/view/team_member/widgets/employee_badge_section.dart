import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/model/team_member_model.dart' as badge;
import '../../../util/colors.dart';
import '../../../util/size_config.dart';

class EmployeeBadgeSection extends StatelessWidget {
  final List<badge.Badge> badgeList;
  const EmployeeBadgeSection({super.key, required this.badgeList});

  @override
  Widget build(BuildContext context) {
    return badgeList.isEmpty
        ? const SizedBox()
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 15),
              Text(
                'Badges',
                style: GoogleFonts.rubik(
                  fontSize: 14,
                  color: ThemeColors.titleColor,
                ),
              ),
              SizedBox(
                height: 130 * h,
                child: ListView.separated(
                    padding: const EdgeInsets.only(top: 12),
                    shrinkWrap: true,
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (context, index) {
                      badge.Badge badgeModel = badgeList[index];
                      String image = badgeModel.badgeImg ?? '';
                      String name = badgeModel.badgeName ?? '';
                      int? achiveCount = badgeModel.achievedCount;
                      return SizedBox(
                        width: 70 * w,
                        child: Column(
                          children: [
                            Stack(
                              alignment: Alignment.topRight,
                              children: [
                                CachedNetworkImage(
                                  imageUrl: image,
                                  imageBuilder: (context, imageProvider) {
                                    return Container(
                                      height: h * 64,
                                      width: h * 64,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: ThemeColors.colorE6E6E6,
                                          width: 2,
                                        ),
                                        image: DecorationImage(
                                          fit: BoxFit.cover,
                                          image: CachedNetworkImageProvider(
                                            image,
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                  errorWidget: (context, url, error) {
                                    return Container(
                                      height: h * 64,
                                      width: h * 64,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: ThemeColors.colorE6E6E6,
                                          width: 2,
                                        ),
                                        image: const DecorationImage(
                                            fit: BoxFit.cover,
                                            image: AssetImage(
                                                'assets/images/no_badge_image.png')),
                                      ),
                                    );
                                  },
                                ),
                                // Text('data')
                                if (achiveCount != null)
                                  Positioned(
                                    right: 10 * w,
                                    child: Align(
                                      alignment: Alignment.topRight,
                                      child: Container(
                                        // height: 30,
                                        // width: 20,
                                        padding: const EdgeInsets.all(5),
                                        alignment: Alignment.center,
                                        decoration: const BoxDecoration(
                                            color: Color(0xFF5570F1),
                                            shape: BoxShape.circle),
                                        child: Text(
                                          '$achiveCount',
                                          textAlign: TextAlign.center,
                                          style: GoogleFonts.poppins(
                                            color: Colors.white,
                                            fontSize: 10,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            SizedBox(height: 8 * w),
                            Text(
                              name,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              style: GoogleFonts.poppins(
                                fontSize: 10 * f,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                    separatorBuilder: (context, index) {
                      return SizedBox(width: 18 * w);
                    },
                    itemCount: badgeList.length),
              )
            ],
          );
  }
}

class EmployeeBadgeSectionTeamMember extends StatelessWidget {
  final List<badge.Badge> badgeList;
  const EmployeeBadgeSectionTeamMember({super.key, required this.badgeList});

  @override
  Widget build(BuildContext context) {
    return badgeList.isEmpty
        ? const SizedBox()
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 23 * h,
                child: ListView.separated(
                    padding: const EdgeInsets.only(top: 2),
                    shrinkWrap: true,
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (context, index) {
                      badge.Badge badgeModel = badgeList[index];
                      String image = badgeModel.badgeImg ?? '';
                      int? achiveCount = badgeModel.achievedCount;
                      return SizedBox(
                        // width: 70 * w,
                        child: Column(
                          children: [
                            Stack(
                              alignment: Alignment.topRight,
                              children: [
                                SizedBox(
                                  height: 20,
                                  width: 25,
                                  child: CachedNetworkImage(
                                    imageUrl: image,
                                    imageBuilder: (context, imageProvider) {
                                      return Container(
                                        height: h * 20,
                                        width: h * 20,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: ThemeColors.colorE6E6E6,
                                            width: 1,
                                          ),
                                          image: DecorationImage(
                                            fit: BoxFit.cover,
                                            image: CachedNetworkImageProvider(
                                              image,
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                    errorWidget: (context, url, error) {
                                      return Container(
                                        height: h * 20,
                                        width: h * 20,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: ThemeColors.colorE6E6E6,
                                            width: 2,
                                          ),
                                          image: const DecorationImage(
                                              fit: BoxFit.cover,
                                              image: AssetImage(
                                                  'assets/images/no_badge_image.png')),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                // Text('data')
                                if (achiveCount != null)
                                  Positioned(
                                    right: 0 * w,
                                    top: 0,
                                    child: Align(
                                      alignment: Alignment.topRight,
                                      child: Container(
                                        // height: 30,
                                        // width: 20,
                                        padding: const EdgeInsets.all(1.5),
                                        alignment: Alignment.center,
                                        decoration: const BoxDecoration(
                                            color: Color(0xFF5570F1),
                                            shape: BoxShape.circle),
                                        child: Text(
                                          '$achiveCount',
                                          textAlign: TextAlign.center,
                                          style: GoogleFonts.poppins(
                                            color: Colors.white,
                                            fontSize: 10,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            // SizedBox(height: 3 * w),
                            // Text(
                            //   name,
                            //   overflow: TextOverflow.ellipsis,
                            //   textAlign: TextAlign.center,
                            //   maxLines: 2,
                            //   style: GoogleFonts.poppins(
                            //     fontSize: 10 * f,
                            //     fontWeight: FontWeight.w400,
                            //   ),
                            // ),
                          ],
                        ),
                      );
                    },
                    separatorBuilder: (context, index) {
                      return SizedBox(width: 4 * w);
                    },
                    itemCount: badgeList.length),
              )
            ],
          );
  }
}
