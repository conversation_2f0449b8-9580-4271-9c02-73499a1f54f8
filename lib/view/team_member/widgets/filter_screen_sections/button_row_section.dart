import 'package:flutter/material.dart';
import 'package:e8_hr_portal/provider/team_members_provider.dart';
import 'package:provider/provider.dart';
import '../../../../util/colors.dart';
import '../../../../util/size_config.dart';
import '../../../../util/styles.dart';
import '../../../widgets/general_button.dart';

class ButtonRowSection extends StatelessWidget {
  const ButtonRowSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _resetButton(context: context)),
        SizedBox(
          width: w * 15,
        ),
        Expanded(
          child: <PERSON><PERSON><PERSON><PERSON>(
              title: "Apply Filter",
              onPressed: () async {
                Provider.of<TeamMembersProvider>(context, listen: false)
                    .getTeamMembers(
                        isFilter: true, master: false, context: context);
                Navigator.of(context).pop();
              },
              isDisabled: false,
              textStyle: tsS18w600cFFFFFF,
              height: h * 50,
              width: w * 164),
        ),
      ],
    );
  }
}

Widget _resetButton({required BuildContext context}) {
  return ElevatedButton(
    onPressed: () {
      Provider.of<TeamMembersProvider>(context, listen: false)
          .clearSelectedFilter();
    },
    style: ElevatedButton.styleFrom(
      elevation: 0,
      backgroundColor: ThemeColors.disabledButtonColor,
      disabledBackgroundColor: Colors.transparent,
      shadowColor: Colors.transparent,
      minimumSize: Size(w * 164, h * 50),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(50),
      ),
    ),
    child: Text(
      "Reset",
      style: tsS18w600cFFFFFF,
    ),
  );
}
