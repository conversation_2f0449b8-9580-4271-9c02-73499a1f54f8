import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/team_members_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:provider/provider.dart';

import '../../../../provider/user_status_provider.dart';
import '../../../../util/size_config.dart';
import '../../team_members_filter_screen.dart';

class WorkStatusSection extends StatelessWidget {
  const WorkStatusSection({super.key});

  @override
  Widget build(BuildContext context) {
    final statusProvider = Provider.of<UserStatusProvider>(context).userStatus;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Heading(
          title: "Work Status",
        ),
        SizedBox(
          height: h * 15,
        ),
        Consumer<TeamMembersProvider>(
          builder: (context, provider, child) {
            return statusProvider.isEmpty
                ? const SizedBox()
                : Wrap(
                    spacing: 10,
                    runSpacing: 10,
                    direction: Axis.horizontal,
                    children: List.generate(statusProvider.length, (index) {
                      final data = statusProvider[index];
                      return InkWell(
                        onTap: () {
                          provider.selectedWorkstatus.contains(data.id)
                              ? provider.removeFromSelectedWorkStatus(data.id)
                              : provider.addtoSelectedWorkStatus(data.id);
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 15, vertical: 11),
                          constraints: BoxConstraints(
                            minWidth: w * 109,
                          ),
                          decoration: BoxDecoration(
                            color: provider.selectedWorkstatus.contains(data.id)
                                ? ThemeColors.colorF9637D
                                : Colors.white,
                            borderRadius: BorderRadius.circular(65),
                          ),
                          child: Text(data.name ?? "",
                              textAlign: TextAlign.center,
                              style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: provider.selectedWorkstatus
                                          .contains(data.id)
                                      ? const Color(0xffFFFFFF)
                                      : Colors.black)),
                        ),
                      );
                    }),
                  );
          },
        )
      ],
    );
  }
}
