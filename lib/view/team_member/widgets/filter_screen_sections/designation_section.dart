import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/team_members_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:provider/provider.dart';
import '../../../../util/size_config.dart';
import '../../team_members_filter_screen.dart';

class DesignationSection extends StatelessWidget {
  DesignationSection({super.key});
  final List designaion = [
    "Accounts Lead",
    "Accounts Manager",
    "Business Manager",
    "Engineer"
  ];
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Heading(
          title: "Designation",
        ),
        SizedBox(
          height: h * 15,
        ),
        Consumer<TeamMembersProvider>(
          builder: (context, provider, child) {
            return provider.designationList == null ||
                    provider.designationList!.data!.isEmpty
                ? const SizedBox()
                : Wrap(
                    spacing: 10,
                    runSpacing: 10,
                    direction: Axis.horizontal,
                    children: List.generate(
                        provider.designationList!.data!.length, (index) {
                      final data = provider.designationList!.data![index];
                      return InkWell(
                        onTap: () {
                          provider.selectedDesignation.contains(data.id)
                              ? provider.removeFromSelectedDesignation(data.id)
                              : provider.addtoSelectedDesignaion(data.id);
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 15, vertical: 11),
                          constraints: BoxConstraints(
                            minWidth: w * 109,
                          ),
                          decoration: BoxDecoration(
                            color:
                                provider.selectedDesignation.contains(data.id)
                                    ? ThemeColors.colorF9637D
                                    : Colors.white,
                            borderRadius: BorderRadius.circular(65),
                          ),
                          child: Text(data.name ?? "",
                              textAlign: TextAlign.center,
                              style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: provider.selectedDesignation
                                          .contains(data.id)
                                      ? const Color(0xffFFFFFF)
                                      : Colors.black)),
                        ),
                      );
                    }),
                  );
          },
        ),
        SizedBox(
          height: h * 20,
        ),
      ],
    );
  }
}
