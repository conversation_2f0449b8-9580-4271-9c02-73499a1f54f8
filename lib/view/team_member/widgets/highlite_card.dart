import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/material.dart';

class HighliteCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  const HighliteCard({required this.title, this.subtitle, super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        height: 83 * h,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.secondary.withOpacity(0.10),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Align(
              alignment: Alignment.center,
              child: Text(
                title,
                style: tsS12w500c505050,
              ),
            ),
            subtitleWidget,
          ],
        ),
      ),
    );
  }

  Widget get subtitleWidget {
    if (subtitle != null) {
      return Align(
        alignment: Alignment.center,
        child: Text(
          subtitle!,
          style: tsS14w500,
          textAlign: TextAlign.center,
        ),
      );
    }
    return const SizedBox();
  }
}
