import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/employee_relations/widget/photo_view.dart';
import 'package:e8_hr_portal/view/team_member/widgets/employee_badge_section.dart';
import 'package:e8_hr_portal/view/team_member/widgets/highlite_card.dart';
import 'package:e8_hr_portal/view/team_member/widgets/team_member_status.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:e8_hr_portal/model/team_member_model.dart';

class TeamMemberDetailsScreen extends StatelessWidget {
  final String? designationMy;
  final TeamMemberModel teamMemberModel;

  const TeamMemberDetailsScreen({
    super.key,
    this.designationMy,
    required this.teamMemberModel,
  });

  @override
  Widget build(BuildContext context) {
    String? designation;
    String? status;
    if (teamMemberModel.designation != null &&
        teamMemberModel.designation!.isNotEmpty) {
      designation = teamMemberModel.designation!.first.name;
    }
    if (teamMemberModel.userStatus != null &&
        teamMemberModel.userStatus!.isNotEmpty) {
      status = teamMemberModel.userStatus!.first.name;
    }
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 25 * w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            margin: const EdgeInsets.symmetric(vertical: 9),
            height: 5 * h,
            width: 134 * w,
            decoration: BoxDecoration(color: ThemeColors.colorEAEBED),
          ),
          InkWell(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () => onProfileTap(context),
            child: Align(
              alignment: Alignment.center,
              child: Container(
                margin: const EdgeInsets.only(top: 12),
                width: 100 * w,
                height: 100 * h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  shape: BoxShape.circle,
                  image: image,
                ),
                child: profileChild,
              ),
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Align(
            alignment: Alignment.center,
            child: Text(
              teamMemberModel.name ?? "",
              style: tsS20BN,
            ),
          ),
          SizedBox(height: 6 * h),
          Align(
            alignment: Alignment.center,
            child: Text(
              designation ?? "",
              style: tsS14w400979797,
            ),
          ),
          SizedBox(height: 4 * h),
          employeeId,
          SizedBox(height: 10 * h),
          TeamMemberStatus(status: status, key: UniqueKey()),
          SizedBox(height: 15 * h),
          Row(
            children: [
              HighliteCard(
                title: 'Join Date',
                subtitle: teamMemberModel.dateOfJoining,
                key: UniqueKey(),
              ),
              SizedBox(width: 10 * w),
              HighliteCard(
                title: 'Department',
                subtitle: teamMemberModel.department,
                key: UniqueKey(),
              ),
            ],
          ),
          const SizedBox(height: 25),
          GestureDetector(
            onTap: () => _makeEmail(teamMemberModel.email.toString()),
            child: UserDetailTile(
              title: 'Email Address',
              subtitle: '${teamMemberModel.email}',
            ),
          ),
          UserDetailTile(
            title: 'Branch',
            subtitle: '${teamMemberModel.branch}',
          ),
          UserDetailTile(
            title: 'Reporting Person',
            subtitle: teamMemberModel.reportingPerson?.name ?? "",
          ),
          emergencyContact,
          Align(
            alignment: Alignment.centerLeft,
            child: EmployeeBadgeSection(badgeList: teamMemberModel.badge ?? []),
          ),
          bottomGap,
        ],
      ),
    );
  }

  Widget get employeeId {
    if (teamMemberModel.employeeId != null) {
      return RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          children: [
            TextSpan(
              text: 'EMP ID: ',
              style: GoogleFonts.rubik(
                fontSize: 13,
                color: ThemeColors.titleColor,
              ),
            ),
            TextSpan(
              text: teamMemberModel.employeeId.toString(),
              style: GoogleFonts.rubik(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF1E2138),
              ),
            ),
          ],
        ),
      );
    }
    return const SizedBox();
  }

  Widget get emergencyContact {
    if (designationMy?.toLowerCase() == 'hr manager' ||
        designationMy?.toLowerCase() == 'project manager' ||
        LoginModel.isAdmin == true) {
      return InkWell(
        onTap: () {
          if (teamMemberModel.emergencyContact != null &&
              teamMemberModel.emergencyContact!.isNotEmpty) {
            _makePhoneCall(teamMemberModel.emergencyContact!);
          }
        },
        child: UserDetailTile(
          title: 'Emergency Contact',
          subtitle: teamMemberModel.emergencyContact ?? '',
        ),
      );
    }
    return const SizedBox();
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    await launchUrl(launchUri);
  }

  SizedBox get bottomGap {
    if (teamMemberModel.badge != null && teamMemberModel.badge!.isNotEmpty) {
      return const SizedBox(height: 15);
    }
    return const SizedBox(height: 40);
  }

  DecorationImage? get image {
    if (teamMemberModel.profilePic != null) {
      return DecorationImage(
        image: CachedNetworkImageProvider(
          teamMemberModel.profilePic.toString(),
        ),
        fit: BoxFit.cover,
      );
    }
    return null;
  }

  Widget? get profileChild {
    if (teamMemberModel.profilePic == null) {
      return Text(
        '${teamMemberModel.name?.substring(0, 1).toUpperCase()}',
        style: GoogleFonts.rubik(
          fontSize: 60,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      );
    }
    return null;
  }

  void onProfileTap(BuildContext context) {
    if (teamMemberModel.profilePic != null) {
      PageNavigator.push(
        context: context,
        route: PhotoViewScreen(
          image: teamMemberModel.profilePic,
          extension: "jpeg",
        ),
      );
    }
  }
}

class UserDetailTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final bool showDivider;
  const UserDetailTile({
    super.key,
    required this.title,
    required this.subtitle,
    this.showDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    if (subtitle != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SizedBox(height: 7 * h),
          Text(
            title,
            style: GoogleFonts.rubik(
              fontSize: 14,
              color: ThemeColors.titleColor,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 5.0),
            child: Text(
              subtitle!,
              style: GoogleFonts.rubik(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF1E2138),
              ),
            ),
          ),
          SizedBox(
            height: 5 * h,
          ),
          if (showDivider)
            const Divider(
              color: Color(0xFFDEE7FF),
              thickness: 1,
              height: 3,
            ),
        ],
      );
    }
    return const SizedBox();
  }
}

Future<void> _makeEmail(String email) async {
  final Uri launchUri = Uri(
    scheme: 'mailto',
    path: email,
  );
  await launchUrl(launchUri);
}
