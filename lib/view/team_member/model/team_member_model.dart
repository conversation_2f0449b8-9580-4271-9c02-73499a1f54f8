// class TeamMemberModel {
//   String? status;
//   String? name;
//   String? email;
//   String? uid;
//   String? appStatus;
//   String? designation;
//   String? whatsapp;
//   String? phone;
//   bool? isWorkFromHome;
//   String? profilePhoto;

//   TeamMemberModel.fromjson(Map<String, dynamic> json) {
//     status = json["status"];
//     name = json["name"];
//     email = json["email"];
//     appStatus = json["approval_status"];
//     designation = json["designation_name"];
//     whatsapp = json["whatsapp"];
//     phone = json["phone"];
//     uid = json['uid'];
//     isWorkFromHome = json['isWorkFromHome'];
//     profilePhoto = json['profilePhoto'];
//   }
// }
