// ignore_for_file: use_build_context_synchronously

import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/team_member_model.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/helper/search_textfield.dart';
import 'package:e8_hr_portal/provider/search_provider.dart';
import 'package:e8_hr_portal/provider/team_members_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/team_member/widgets/team_member_card.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';
import 'team_members_details.dart';

class TeamMembersSearchScreen extends StatefulWidget {
  static const route = 'TeamMembers';
  const TeamMembersSearchScreen({super.key});

  @override
  State<TeamMembersSearchScreen> createState() =>
      _TeamMembersSearchScreenState();
}

class _TeamMembersSearchScreenState extends State<TeamMembersSearchScreen> {
  @override
  void initState() {
    var provider = Provider.of<SearchProvider>(context, listen: false);
    provider.fetchRecentSearchList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final TextEditingController searchController = TextEditingController();
    return HisenseScaffold(
      screenTitle: 'Search',
      avoidBottom: false,
      body: Container(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
        decoration: BoxDecoration(
          color: ThemeColors.colorF4F5FA,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
        ),
        child: Consumer<TeamMembersProvider>(
          builder: (context, provider, child) {
            return Column(
              children: [
                const SizedBox(height: 11),
                SearchTextFieldWidget(
                  onChanged: (value) {
                    provider.isRecentSearchShow = false;
                    provider.getSearchResult(value);
                  },
                  controller: searchController,
                  prefixIcon: IconButton(
                    onPressed: () {},
                    icon: const ImageIcon(
                      AssetImage(
                        "assets/icons/search.png",
                      ),
                    ),
                  ),
                  hintText: "Search Employee...",
                ),
                const SizedBox(height: 20),
                if (provider.isRecentSearchShow == true)
                  const RecentSearchesSection()
                else
                  const SearchResultSection()
              ],
            );
          },
        ),
      ),
    );
  }
}

class RecentSearchesSection extends StatelessWidget {
  const RecentSearchesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<SearchProvider, TeamMembersProvider>(
      builder: (context, provider, provider2, child) {
        if (provider.recentSearchList.isEmpty) {
          return const SizedBox();
        }
        return Expanded(
          child: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(9),
            ),
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Align(
                      alignment: Alignment.center,
                      child: Text(
                        "Recent Searches",
                        style: tsS18w500,
                      ),
                    ),
                    InkWell(
                      onTap: provider.clearSearch,
                      child: Align(
                        alignment: Alignment.center,
                        child: Text(
                          "Clear All",
                          style: tsS12w500c5c5c5c,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Expanded(
                  child: ListView.separated(
                    physics: const ClampingScrollPhysics(),
                    itemBuilder: (context, index) {
                      var employee = provider.recentSearchList.elementAt(index);
                      return InkWell(
                        onTap: () => showEmployeeDetails(
                          context: context,
                          employeeId: employee.id,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            SizedBox(
                              child: Row(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(5.0),
                                    child: ClipOval(
                                      child: CachedNetworkImage(
                                        imageUrl: employee.image ?? '',
                                        width: 50,
                                        height: 50,
                                        fit: BoxFit.cover,
                                        errorWidget: errorWidget(employee.name),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          employee.name.toString(),
                                          style: tsS14w400Black,
                                        ),
                                        Text(
                                          employee.designation.toString(),
                                          style: tsS12w400979797,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              onPressed: () {
                                provider.deleteSearch(id: employee.id?.toInt());
                              },
                              icon: const Icon(
                                Icons.close_outlined,
                                color: Colors.grey,
                              ),
                            )
                          ],
                        ),
                      );
                    },
                    separatorBuilder: (context, index) {
                      return Divider(
                        thickness: 1,
                        color: ThemeColors.colorD9D9D9,
                      );
                    },
                    itemCount: provider.recentSearchList.length,
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  Widget Function(BuildContext, String, Object)? errorWidget(String? name) {
    return (context, url, error) {
      return Container(
        height: 50,
        width: 50,
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          shape: BoxShape.circle,
        ),
        alignment: Alignment.center,
        child: Text(
          name?.substring(0, 1).toUpperCase() ?? "",
          style: GoogleFonts.rubik(
            fontSize: 22,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),
      );
    };
  }

  showEmployeeDetails({required BuildContext context, int? employeeId}) async {
    final provider = context.read<TeamMembersProvider>();
    await provider.getRecentEmpDetails(id: '$employeeId');
    showModalBottomSheet<void>(
      useSafeArea: true,
      context: context,
      isDismissible: true,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(35.0),
        ),
      ),
      builder: (BuildContext context) {
        return TeamMemberDetailsScreen(
          teamMemberModel: provider.recentSearchEmpDetails ?? TeamMemberModel(),
          designationMy: provider.designationMy,
        );
      },
    );
  }
}

class SearchResultSection extends StatelessWidget {
  const SearchResultSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<TeamMembersProvider>(
      builder: (context, provider, child) {
        return Expanded(
          child: provider.searchResultList.isEmpty
              ? Column(
                  children: [
                    SizedBox(
                      height: 150 * h,
                    ),
                    const Text("No Result found"),
                  ],
                )
              : ListView.builder(
                  shrinkWrap: true,
                  itemCount: provider.searchResultList.length,
                  itemBuilder: (context, index) {
                    final data = provider.searchResultList[index];
                    return InkWell(
                      onTap: () {
                        var recentProvider =
                            Provider.of<SearchProvider>(context, listen: false);
                        recentProvider.addInRecentSearchList(data);
                        showModalBottomSheet<void>(
                          useSafeArea: true,
                          context: context,
                          isDismissible: true,
                          isScrollControlled: true,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.vertical(
                              top: Radius.circular(35.0),
                            ),
                          ),
                          builder: (BuildContext context) {
                            return TeamMemberDetailsScreen(
                              teamMemberModel: data,
                              designationMy: provider.designationMy,
                            );
                          },
                        );
                      },
                      child: TeamMemberCards(teamMemberModel: data),
                    );
                  },
                ),
        );
      },
    );
  }
}
