import 'package:flutter/material.dart';
import 'package:e8_hr_portal/provider/team_members_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/team_member/widgets/team_member_card.dart';
import 'package:e8_hr_portal/view/team_member/team_members_details.dart';
import 'package:e8_hr_portal/view/team_member/team_members_filter_screen.dart';
import 'package:provider/provider.dart';

import '../../provider/user_status_provider.dart';

bool _isLoading = false;

class TeamMemberScreen extends StatelessWidget {
  const TeamMemberScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
      decoration: BoxDecoration(
        color: ThemeColors.colorF4F5FA,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
      ),
      child: Column(
        children: [
          const SizedBox(
            height: 11,
          ),
          Consumer<TeamMembersProvider>(builder: (context, provider, child) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    'Team Members(${provider.teamMembersList.length})',
                    style: tsS16w500,
                  ),
                ),
                InkWell(
                  onTap: () async {
                    if (!_isLoading) {
                      _isLoading = true;
                      provider.getDesignation(master: false);
                      Provider.of<UserStatusProvider>(context, listen: false)
                          .getUserStatus(master: false);
                      provider.getBranchList(master: false);
                      Navigator.of(context).push(MaterialPageRoute(
                          builder: (context) =>
                              const TeamMembersFilterScreen()));
                      _isLoading = false;
                    }
                  },
                  child: const ImageIcon(
                    AssetImage(
                      'assets/icons/filter.png',
                    ),
                  ),
                ),
              ],
            );
          }),
          const SizedBox(
            height: 15,
          ),
          Consumer<TeamMembersProvider>(
            builder: (context, provider, child) {
              return Expanded(
                child: provider.teamMembersList.isEmpty
                    ? const Center(
                        child: Text('No Result Found'),
                      )
                    : ListView.builder(
                        physics: const BouncingScrollPhysics(),
                        scrollDirection: Axis.vertical,
                        shrinkWrap: true,
                        itemCount: provider.teamMembersList.length,
                        itemBuilder: (context, index) {
                          final data = provider.teamMembersList[index];

                          return InkWell(
                            onTap: () {
                              showModalBottomSheet<void>(
                                useSafeArea: true,
                                context: context,
                                isDismissible: true,
                                isScrollControlled: true,
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(12),
                                  ),
                                ),
                                builder: (BuildContext context) {
                                  return TeamMemberDetailsScreen(
                                    designationMy: provider.designationMy,
                                    teamMemberModel: data,
                                  );
                                },
                              );
                            },
                            child: TeamMemberCards(teamMemberModel: data),
                          );
                        },
                      ),
              );
            },
          ),
        ],
      ),
    );
  }
}
