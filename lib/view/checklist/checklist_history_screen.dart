import 'dart:math';
import 'package:e8_hr_portal/model/checklist_shecduler_model.dart';
import 'package:e8_hr_portal/model/monsth_model.dart';
import 'package:e8_hr_portal/provider/checklist_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/checklist/widget/bottom_sheet_filter_checklist.dart';
import 'package:e8_hr_portal/view/checklist/widget/custom_expanision_daily_widget.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class ChecklistHistoryScreen extends StatelessWidget {
  static const route = '/checklist_history_screen';
  const ChecklistHistoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var checklistPro = context.watch<ChecklistProvider>();
    return HisenseScaffold(
      screenTitle: "Checklist History",

      // onTap: () {},
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16 * w),
        child: ListView(
          physics: const ClampingScrollPhysics(),
          children: [
            SizedBox(height: h * 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  onTap: () async {
                    await checklistPro.selectFromDate(context: context);
                  },
                  child: Row(
                    children: [
                      Row(
                        children: [
                          Image.asset(
                            "assets/icons/calendar_black.png",
                            scale: 1.6,
                            color: ThemeColors.primaryColor,
                          ),
                          SizedBox(
                            width: 05 * w,
                          ),
                          Text(
                            formatDateFromDate(
                                dateTime: checklistPro.selectedDate,
                                format: "yyyy"),
                            style: tsS14BN,
                          ),
                          SizedBox(
                            width: 05 * w,
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Transform.rotate(
                          angle: -pi / 2,
                          child: const Icon(
                            Icons.arrow_back_ios,
                            size: 15,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                InkWell(
                  onTap: () {
                    showModalBottomSheet(
                      context: context,
                      isDismissible: true,
                      isScrollControlled: true,
                      backgroundColor: Colors.white,
                      shape: const RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.vertical(top: Radius.circular(12))),
                      builder: (builder) =>
                          const BottomSheetFilterForCheckList(),
                    );
                  },
                  child: Image.asset(
                    "assets/icons/textalign-left.png",
                    scale: 2,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 20 * h,
            ),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: Month.months.map(
                  (e) {
                    int index = Month.months.indexOf(e);

                    return InkWell(
                      onTap: () {
                        if (checklistPro.selectedMonth == null) {
                          checklistPro.selectedMonth = e.id;
                          checklistPro.selectedIndex = index;
                          debugPrint(checklistPro.selectedMonth.toString());
                          debugPrint(checklistPro.selectedIndex.toString());
                          checklistPro.getChecklistHistory();
                        } else if (checklistPro.selectedIndex == index) {
                          checklistPro.selectedMonth = null;
                          checklistPro.selectedIndex = null;

                          checklistPro.getChecklistHistory();
                        } else {
                          checklistPro.selectedMonth = e.id;
                          checklistPro.selectedIndex = index;
                          debugPrint(checklistPro.selectedMonth.toString());
                          debugPrint(checklistPro.selectedIndex.toString());
                          checklistPro.getChecklistHistory();
                        }
                      },
                      child: Container(
                        padding: EdgeInsets.all(8 * h),
                        decoration: BoxDecoration(
                          gradient: checklistPro.selectedIndex == index
                              ? const LinearGradient(colors: [
                                  Color(0xffbafcc500),
                                  Color(0xffbafcc300)
                                ], begin: Alignment.centerRight)
                              : null,
                          color: checklistPro.selectedIndex == index
                              ? null
                              : Colors.white,
                          borderRadius: BorderRadius.circular(5),
                        ),
                        margin: EdgeInsets.only(right: 8 * w),
                        child: Text(
                          e.shortName,
                          style: checklistPro.selectedIndex == index
                              ? tsS12w500FFFFF
                              : tsS12NormalBlack,
                        ),
                      ),
                    );
                  },
                ).toList(),
              ),
            ),
            SizedBox(
              height: 20 * h,
            ),
            Consumer<ChecklistProvider>(builder: (context, pro, _) {
              if (pro.isLoading) {
                return const Padding(
                  padding: EdgeInsets.only(
                    top: 50,
                  ),
                  child: Center(child: CircularProgressIndicator()),
                );
              }
              if (pro.checkListHistoryModel.isEmpty) {
                return Padding(
                  padding: const EdgeInsets.only(
                    top: 50,
                  ),
                  child: Center(
                    child: Text(
                      pro.errorMsg,
                      style: tsS14BN,
                    ),
                  ),
                );
              }
              return Column(
                children: [
                  if (pro.checkListHistoryModelonce.isNotEmpty) ...[
                    CustomExpContainerForDailyWidget(
                      provider: pro,
                      heading: "Once",
                      listOfModel: pro.checkListHistoryModelonce,
                      color: pro.listOfsheduler
                          .firstWhere(
                            (element) => element.id == 1,
                            orElse: () =>
                                CheckListShedulerModel(color: ("#000000")),
                          )
                          .color!,
                    ),
                    SizedBox(height: 10 * h),
                  ],
                  if (pro.checkListHistoryModelDialy.isNotEmpty) ...[
                    CustomExpContainerForDailyWidget(
                      provider: pro,
                      heading: "Daily",
                      listOfModel: pro.checkListHistoryModelDialy,
                      color: pro.listOfsheduler
                          .firstWhere(
                            (element) => element.id == 2,
                            orElse: () =>
                                CheckListShedulerModel(color: ("#000000")),
                          )
                          .color!,
                    ),
                    SizedBox(
                      height: 10 * h,
                    ),
                  ],
                  if (pro.checkListHistoryModelWeekly.isNotEmpty) ...[
                    CustomExpContainerForDailyWidget(
                      provider: pro,
                      heading: "Weekly",
                      listOfModel: pro.checkListHistoryModelWeekly,
                      color: pro.listOfsheduler
                          .firstWhere(
                            (element) => element.id == 3,
                            orElse: () =>
                                CheckListShedulerModel(color: ("#000000")),
                          )
                          .color!,
                    ),
                    SizedBox(
                      height: 10 * h,
                    ),
                  ],
                  if (pro.checkListHistoryModelMonthley.isNotEmpty) ...[
                    CustomExpContainerForDailyWidget(
                      provider: pro,
                      heading: "Monthly",
                      listOfModel: pro.checkListHistoryModelMonthley,
                      color: pro.listOfsheduler
                          .firstWhere(
                            (element) => element.id == 4,
                            orElse: () =>
                                CheckListShedulerModel(color: ("#000000")),
                          )
                          .color!,
                    ),
                    SizedBox(height: 10 * h),
                  ],
                  if (pro.checkListHistoryModelQuarterly.isNotEmpty) ...[
                    CustomExpContainerForDailyWidget(
                      provider: pro,
                      heading: "Quarterly",
                      listOfModel: pro.checkListHistoryModelQuarterly,
                      color: pro.listOfsheduler
                          .firstWhere(
                            (element) => element.id == 5,
                            orElse: () =>
                                CheckListShedulerModel(color: ("#000000")),
                          )
                          .color!,
                    ),
                    SizedBox(
                      height: 10 * h,
                    ),
                  ],
                  if (pro.checkListHistoryModelHalfyearly.isNotEmpty) ...[
                    CustomExpContainerForDailyWidget(
                      provider: pro,
                      heading: "Half-Yearly",
                      listOfModel: pro.checkListHistoryModelHalfyearly,
                      color: pro.listOfsheduler
                          .firstWhere(
                            (element) => element.id == 6,
                            orElse: () =>
                                CheckListShedulerModel(color: ("#000000")),
                          )
                          .color!,
                    ),
                    SizedBox(
                      height: 10 * h,
                    ),
                  ],
                  if (pro.checkListHistoryModelyearly.isNotEmpty) ...[
                    CustomExpContainerForDailyWidget(
                      provider: pro,
                      heading: "Yearly",
                      listOfModel: pro.checkListHistoryModelyearly,
                      color: pro.listOfsheduler
                          .firstWhere(
                            (element) => element.id == 7,
                            orElse: () =>
                                CheckListShedulerModel(color: ("#000000")),
                          )
                          .color!,
                    ),
                    SizedBox(
                      height: 10 * h,
                    ),
                  ],
                ],
              );
            }),
            SizedBox(
              height: 20 * h,
            ),
          ],
        ),
      ),
    );
  }
}
