import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';

class CommonSmallDropdownButton<T> extends StatelessWidget {
  final String? hintText;
  final void Function(T?)? onChanged;
  final T? value;
  final List<DropdownMenuItem<T>>? items;
  final bool isExpanded;
  const CommonSmallDropdownButton(
      {super.key,
      this.hintText,
      this.onChanged,
      this.value,
      this.items,
      this.isExpanded = true});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(10, 5, 5, 5),
      // width: 90 * w,
      height: 30 * h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
      ),
      child: DropdownButton(
        hint: hintText != null ? Text(hintText ?? '') : const SizedBox.shrink(),
        isExpanded: isExpanded,
        underline: const SizedBox(),
        value: value,
        items: items,
        onChanged: onChanged,
      ),
    );
  }
}
