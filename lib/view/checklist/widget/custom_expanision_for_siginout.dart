import 'dart:developer';

import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/model/checklist_onsiginout_model.dart';
import 'package:e8_hr_portal/model/checklist_shecduler_model.dart';
import 'package:e8_hr_portal/provider/checklist_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/checklist/widget/custom_radio_button_onsiginin.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../util/validator.dart';

class CustomExpansionContainerSiginOut extends StatefulWidget {
  final OnsignoutChecklistModel item;
  const CustomExpansionContainerSiginOut({required this.item, super.key});

  @override
  State<CustomExpansionContainerSiginOut> createState() =>
      _CustomExpansionContainerSiginOutState();
}

class _CustomExpansionContainerSiginOutState
    extends State<CustomExpansionContainerSiginOut>
    with SingleTickerProviderStateMixin {
  bool isExpanded = true;
  final TextEditingController _remarkController = TextEditingController();
  final TextEditingController _responseController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  late ChecklistProvider _provider;
  @override
  void initState() {
    super.initState();
    _provider = context.read<ChecklistProvider>();
    // if (widget.item.schedule != null) {
    //   _remarkController.text = widget.item.schedule!;
    // }
    // if (widget.item.details != null) {
    //   _responseController.text = widget.item.details ?? '';
    // }
  }

  @override
  void dispose() {
    super.dispose();
    _remarkController.dispose();
    _responseController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.0), // Rounded corners
      ),
      child: Column(
        children: [
          // Header section
          GestureDetector(
            onTap: () {
              setState(() {
                isExpanded = !isExpanded;
              });
            },
            child: Container(
              padding: EdgeInsets.only(
                  top: 15.0, left: 15, right: 15, bottom: !isExpanded ? 15 : 5),
              decoration: BoxDecoration(
                color: Colors.white, // Header background color
                borderRadius: BorderRadius.circular(10.0),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 18 * w,
                        height: 18 * h,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle, // Circular shape

                          color: Colors.grey.withOpacity(0.19),
                        ),
                        child: null, // Show checkmark when checked
                      ),
                      SizedBox(
                        width: 6 * h,
                      ),
                      SizedBox(
                        width: 260 * w,
                        child: Text(
                          widget.item.point ?? "",
                          style: tsS14w500,
                          // overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: Colors.black, // Custom expand/collapse icon color
                  ),
                ],
              ),
            ),
          ),

          // Animated body section
          AnimatedSize(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeIn,
            child: SizedBox(
              height: isExpanded
                  ? (widget.item.responseType == 'yes_no' ? 180 * h : 220 * h)
                  : 0,
              child: isExpanded
                  ? Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      child: Padding(
                        padding: EdgeInsets.only(left: 35 * w, top: 0),
                        child: Column(
                          children: [
                            dateWidget(
                                sheduledId: widget.item.scheduleId ?? "1",
                                type: widget.item.schedule ?? "Weekly",
                                dueDate: widget.item.dueDate ?? "14 Dec 2024"),
                            SizedBox(height: 15 * h),
                            _yesOrNoWidget(),
                            SizedBox(height: 10 * h),
                            Align(
                              alignment: Alignment.topLeft,
                              child: Text("Remark", style: tsS14w400Black),
                            ),
                            SizedBox(height: 4 * h),
                            Container(
                              margin: EdgeInsets.only(right: 15 * w),
                              height: 60 * h,
                              child: TextFieldWidget(
                                contentPadding: EdgeInsets.all(5 * h),
                                controller: _remarkController,
                                hintText: "Type....",
                                hintStyle: tsS12w400979797,
                                borderColor: ThemeColors.colorF8F8F8,
                                fillColor: ThemeColors.colorF8F8F8,
                                maxLines: 3,
                                textCapitalization:
                                    TextCapitalization.sentences,
                                suffixIcon: InkWell(
                                  onTap: () async => _onSubmitPressed(),
                                  child: IconTheme(
                                    data: IconThemeData(
                                      color: ThemeColors
                                          .primaryColor, // Set your desired color here
                                    ),
                                    child: Container(
                                      width: 30 * w,
                                      height: double.infinity,
                                      decoration: BoxDecoration(
                                          color: ThemeColors.primaryColor,
                                          borderRadius: const BorderRadius.only(
                                              topRight: Radius.circular(5),
                                              bottomRight: Radius.circular(5))),
                                      child: Center(
                                          child: Icon(
                                        Icons.check,
                                        color: Colors.black,
                                        size: 16 * h,
                                      )),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: 20 * h),
                          ],
                        ),
                      ),
                    )
                  : SizedBox(), // Placeholder when not expanded
            ),
          ),
        ],
      ),
    );
  }

  Widget _yesOrNoWidget() {
    if (widget.item.responseType == 'yes_no') {
      return Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CustomRadioButtonSiginOutChecklist(
            item: widget.item,
            provider: _provider,
            borderColor: ThemeColors.color979797,
            onChanged: () async {
              widget.item.updatedStatus = true;
              widget.item.status = "Yes";

              setState(() {});
            },
          ),
          SizedBox(width: 20 * w),
          CustomRadioButtonNoSignoutChecklist(
            item: widget.item,
            provider: _provider,
            borderColor: ThemeColors.color979797,
            onChanged: () async {
              widget.item.updatedStatus = true;
              widget.item.status = "No";

              setState(() {});
            },
          ),
        ],
      );
    } else if (widget.item.responseType == 'textbox' ||
        widget.item.responseType == 'number') {
      String hintText = 'Write Your Respose';
      TextInputType? keyboardType = TextInputType.text;
      List<TextInputFormatter> inputFormatters = [];
      if (widget.item.responseType == 'number') {
        hintText = 'Enter Number';
        keyboardType = TextInputType.number;
        inputFormatters = [FilteringTextInputFormatter.digitsOnly];
      }
      return Padding(
        padding: EdgeInsets.only(right: 16 * w),
        child: Form(
          key: _formKey,
          child: TextFieldWidget(
              controller: _responseController,
              hintStyle: tsS12w400c9F9F9F,
              textStyle: tsS14w400454444,
              borderColor: ThemeColors.colorE3E3E3,
              contentPadding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              validator: Validator.text,
              hintText: hintText,
              maxLines: 1,
              keyboardType: keyboardType,
              inputFormatters: inputFormatters),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget dateWidget({
    required String type,
    required String dueDate,
    required String sheduledId,
  }) {
    var colorString = Colors.blue[400];
    //  _provider.listOfsheduler.firstWhere(
    //   (element) {
    //     int sheduled = int.parse(sheduledId);
    //     return element.id == sheduled;
    //   },
    //   orElse: () => CheckListShedulerModel(color: ("#000000")),
    // ).color!;

    return Row(
      children: [
        Image.asset(
          "assets/icons/calendar_violet.png",
          scale: 1.4,
          color: colorString,
        ),
        SizedBox(
          width: 04 * w,
        ),
        Text(
          type,
          style: tsS12w400979797,
        ),
        Container(
          height: 3 * h,
          width: 3 * w,
          margin: EdgeInsets.symmetric(horizontal: 7 * w),
          decoration: BoxDecoration(
              shape: BoxShape.circle, color: ThemeColors.color979797),
        ),
        Text("Due : $dueDate", style: tsS12w400979797),
      ],
    );
  }

  _onSubmitPressed() async {
    String responseType = widget.item.responseType ?? '';
    log("res === ${_remarkController.text}");
    if (responseType == 'yes_no') {
      if (widget.item.status != null) {
        FocusManager.instance.primaryFocus?.unfocus();

        // await _provider.submitChecklist(
        //   assigneeID: widget.item.assigneeId.toString(),
        //   // yesOrNo: widget.item.status,
        //   remark: _remarkController.text,
        //   details: widget.item.details ?? '',
        //   dueDate: widget.item.dueDate ?? '',
        // );

        await _provider.onSignOutChecklistUPDATE(
          assigneeId: widget.item.assigneeId.toString(),
          status: widget.item.status,
          remarks: _remarkController.text,
          details: widget.item.details,
          // responseString: _responseController.text,
        );
      } else {
        showToastText("Please choose yes or no");
      }
    } else {
      // if (_remarkController.text.isNotEmpty) {
      if (!_formKey.currentState!.validate()) {
        return;
      }
      FocusManager.instance.primaryFocus?.unfocus();
      await _provider.onSignOutChecklistUPDATE(
        assigneeId: widget.item.assigneeId.toString(),
        responseString: _responseController.text,
        remarks: _remarkController.text,
        details: widget.item.details,
      );
    }
  }
}
