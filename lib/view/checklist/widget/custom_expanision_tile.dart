import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/model/checklist_history_model.dart';
import 'package:e8_hr_portal/provider/checklist_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/checklist/widget/custom_radiobutton.dart';
import 'package:flutter/material.dart';

class CustomExpansionPanelState extends StatefulWidget {
  final CheckListHistoryModel item;
  final ChecklistProvider provider;
  const CustomExpansionPanelState(
      {required this.provider, super.key, required this.item});

  @override
  State<CustomExpansionPanelState> createState() =>
      _CustomExpansionPanelState();
}

class _CustomExpansionPanelState extends State<CustomExpansionPanelState> {
  bool isExpanded = false;
  final TextEditingController remark = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10.0), // Ensure rounded corners
      child: ExpansionPanelList(
        elevation: 2,
        expandIconColor: Colors
            .black, // Remove elevation since we are applying custom border
        expansionCallback: (int index, bool expanded) {
          setState(() {
            isExpanded = !isExpanded;
          });
        },
        children: [
          ExpansionPanel(
            backgroundColor: Colors.white, // Customize background color

            headerBuilder: (BuildContext context, bool isExpanded) {
              return Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      widget.provider.isChecked = !widget.provider.isChecked;
                    },
                    child: Container(
                      margin: EdgeInsets.only(left: 15 * w, right: 5 * w),
                      width: 18 * w,
                      height: 18 * h,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle, // Circular shape
                        border: Border.all(
                          color: widget.provider.isChecked
                              ? Colors.green
                              : Colors.grey,
                          width: 2.0,
                        ),
                        color: widget.provider.isChecked
                            ? Colors.green
                            : Colors.transparent,
                      ),
                      child: widget.provider.isChecked
                          ? Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 13 * h,
                            )
                          : null, // Show checkmark when checked
                    ),
                  ),
                  Text(
                    "Recruitment",
                    style: tsS16w500,
                  )
                ],
              );
            },
            body: Padding(
              padding: EdgeInsets.only(left: 35 * w, top: 0),
              child: Column(
                children: [
                  dateWidget(),
                  SizedBox(height: 15 * h),
                  _yesOrNoWidget(),
                  SizedBox(height: 10 * h),
                  Align(
                    alignment: Alignment.topLeft,
                    child: Text(
                      "Remark",
                      style: tsS14w400Black,
                    ),
                  ),
                  SizedBox(height: 4 * h),
                  Container(
                    margin: EdgeInsets.only(right: 15 * w),
                    height: 60 * h,
                    child: TextFieldWidget(
                      contentPadding: EdgeInsets.all(5 * h),
                      controller: remark,
                      hintText: "Type....",
                      hintStyle: tsS12w400979797,
                      borderColor: ThemeColors.colorF8F8F8,
                      fillColor: ThemeColors.colorF8F8F8,
                      maxLines: 3,
                      suffixIcon: IconTheme(
                        data: IconThemeData(
                          color: ThemeColors
                              .primaryColor, // Set your desired color here
                        ),
                        child: Container(
                          width: 30 * w,
                          height: double.infinity,
                          decoration: BoxDecoration(
                              color: ThemeColors.primaryColor,
                              borderRadius: const BorderRadius.only(
                                  topRight: Radius.circular(5),
                                  bottomRight: Radius.circular(5))),
                          child: Center(
                            child: Icon(
                              Icons.check,
                              color: Colors.black,
                              size: 16 * h,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 20 * h,
                  ),
                ],
              ),
            ),
            isExpanded: isExpanded,
            canTapOnHeader: true, // Allow tapping on header to toggle expansion
          ),
        ],
      ),
    );
  }

  Widget _yesOrNoWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CustomRadioButton(
          item: widget.item,
          provider: widget.provider,
          borderColor: ThemeColors.color979797,
          onChanged: () {
            widget.provider.radioButtonValue =
                !widget.provider.radioButtonValue;
          },
        ),
        SizedBox(
          width: 20 * w,
        ),
        CustomRadioButtonNo(
          item: widget.item,
          provider: widget.provider,
          borderColor: ThemeColors.color979797,
          onChanged: () {
            widget.provider.radioButtonValue =
                !widget.provider.radioButtonValue;
          },
        ),
      ],
    );
  }

  Widget dateWidget() {
    return Row(
      children: [
        Image.asset(
          "assets/icons/calendar_violet.png",
          scale: 1.4,
        ),
        SizedBox(
          width: 04 * w,
        ),
        Text(
          "Weekly",
          style: tsS12w400979797,
        ),
        Container(
          height: 3 * h,
          width: 3 * w,
          margin: EdgeInsets.symmetric(horizontal: 7 * w),
          decoration: BoxDecoration(
              shape: BoxShape.circle, color: ThemeColors.color979797),
        ),
        Text(
          "Due : 14 Dec 2024",
          style: tsS12w400979797,
        ),
      ],
    );
  }
}
