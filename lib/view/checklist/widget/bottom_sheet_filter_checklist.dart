import 'dart:developer';

import 'package:e8_hr_portal/provider/checklist_provider.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

enum Filter { today, tomorrow, week }

class BottomSheetFilterForCheckList extends StatefulWidget {
  const BottomSheetFilterForCheckList({super.key});

  @override
  State<BottomSheetFilterForCheckList> createState() =>
      _BottomSheetFilterForCheckListState();
}

class _BottomSheetFilterForCheckListState
    extends State<BottomSheetFilterForCheckList> {
  Filter? filterValue;

  @override
  void initState() {
    var checklistProvider = context.read<ChecklistProvider>();
    log("filter value = of  === ${checklistProvider.filterValue}");
    if (checklistProvider.filterValue == "today") {
      filterValue = Filter.today;
    } else if (checklistProvider.filterValue == "tomorrow") {
      filterValue = Filter.tomorrow;
    } else if (checklistProvider.filterValue == "this_week") {
      filterValue = Filter.week;
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var checklistProvider = context.read<ChecklistProvider>();
    return Padding(
      padding: const EdgeInsets.all(15.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 5 * h,
            width: 134 * w,
            decoration: BoxDecoration(
              color: ThemeColors.colorEAEBED,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          SizedBox(
            height: 15 * h,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Align(
                alignment: Alignment.topLeft,
                child: Text(
                  "Change Filter",
                  style: tsS16w4009F9F9F,
                ),
              ),
              InkWell(
                onTap: () async {
                  checklistProvider.filterValue = null;
                  await checklistProvider.getChecklistHistory();
                  Navigator.pop(context);
                },
                child: Align(
                  alignment: Alignment.topRight,
                  child: Text(
                    "Reset",
                    style: tsS164A8FE7,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 30 * h,
          ),
          GestureDetector(
            onTap: () async {
              setState(() {
                filterValue = Filter.today;
              });
              checklistProvider.filterValue = "today";
              await checklistProvider.getChecklistHistory();
              Navigator.pop(context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    "Today",
                    style: Filter.today == filterValue
                        ? tsS14w500cF9637D
                        : tsS14w500c2C2D33,
                  ),
                ),
                Container(
                  width: 20 * h,
                  height: 20 * w,
                  padding: const EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    color: Filter.today == filterValue
                        ? ThemeColors.colorF9637D
                        : null,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      Filter.today == filterValue ? Icons.check : Icons.close,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 10 * h,
          ),
          const Divider(
            thickness: 1,
          ),
          SizedBox(
            height: 10 * h,
          ),
          GestureDetector(
            onTap: () async {
              setState(() {
                filterValue = Filter.tomorrow;
              });
              checklistProvider.filterValue = "tomorrow";
              await checklistProvider.getChecklistHistory();
              Navigator.pop(context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    "Tomorrow",
                    style: Filter.tomorrow == filterValue
                        ? tsS14w500cF9637D
                        : tsS14w500c2C2D33,
                  ),
                ),
                Container(
                  width: 20 * h,
                  height: 20 * w,
                  padding: const EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    color: Filter.tomorrow == filterValue
                        ? ThemeColors.colorF9637D
                        : null,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      Filter.tomorrow == filterValue
                          ? Icons.check
                          : Icons.close,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 10 * h,
          ),
          const Divider(
            thickness: 1,
          ),
          SizedBox(
            height: 10 * h,
          ),
          GestureDetector(
            onTap: () async {
              setState(() {
                filterValue = Filter.week;
              });
              checklistProvider.filterValue = "this_week";
              await checklistProvider.getChecklistHistory();
              Navigator.pop(context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    "This Week",
                    style: Filter.week == filterValue
                        ? tsS14w500cF9637D
                        : tsS14w500c2C2D33,
                  ),
                ),
                Container(
                  width: 20 * h,
                  height: 20 * w,
                  padding: const EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    color: Filter.week == filterValue
                        ? ThemeColors.colorF9637D
                        : null,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      Filter.week == filterValue ? Icons.check : Icons.close,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 25 * h,
          ),
        ],
      ),
    );
  }
}
