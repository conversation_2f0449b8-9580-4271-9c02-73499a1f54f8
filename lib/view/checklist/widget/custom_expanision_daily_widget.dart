import 'package:e8_hr_portal/model/checklist_history_model.dart';
import 'package:e8_hr_portal/provider/checklist_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../util/date_formatter.dart';

class CustomExpContainerForDailyWidget extends StatefulWidget {
  final String heading;

  final String color;
  final List<CheckListHistoryModel> listOfModel;
  final ChecklistProvider provider;

  const CustomExpContainerForDailyWidget({
    required this.heading,
    required this.provider,
    required this.color,
    required this.listOfModel,
    super.key,
  });

  @override
  State<CustomExpContainerForDailyWidget> createState() =>
      _CustomExpContainerForDailyWidgetState();
}

class _CustomExpContainerForDailyWidgetState
    extends State<CustomExpContainerForDailyWidget>
    with SingleTickerProviderStateMixin {
  bool isExpanded = false;
  final TextEditingController remark = TextEditingController();
  late ChecklistProvider provider;

  String? subTitle;
  DateTime? dueDate;
  void checkDate({required List<CheckListHistoryModel> items}) {
    String currentFormattedDate = formatDateFromDate(
        dateTime: DateTime.now(), format: 'yyyy-MM-dd 00:00:00.000');

    bool isTitleEnabled = items.any((item) {
      String formattedDueDate = formatDateFromString(
          item.dueDate ?? '', "dd MMM yyyy", "yyyy-MM-dd 00:00:00");

      // formattedDueDate = '2024-12-31 00:00:00'; // For testing
      dueDate = DateTime.tryParse(formattedDueDate);

      return dueDate != null &&
          (dueDate ?? DateTime.now())
                  .compareTo(DateTime.parse(currentFormattedDate)) <=
              1 &&
          !(item.updatedStatus ?? false);
    });

    if (isTitleEnabled && dueDate != null) {
      DateTime currentDateTime = DateTime.parse(currentFormattedDate);
      Duration difference =
          (dueDate ?? DateTime.now()).difference(currentDateTime);

      if (difference.inDays == 0) {
        subTitle = "Checklist will expire today";
      } else if (difference.inDays < 0) {
        subTitle = "You have missed the checklist";
      } else {
        subTitle = null;
      }
    }
  }

  @override
  void initState() {
    super.initState();
    provider = context.read<ChecklistProvider>();
    switch (widget.heading) {
      case 'Once':
        checkDate(items: provider.checkListHistoryModelonce);
        break;
      case 'Daily':
        checkDate(items: provider.checkListHistoryModelDialy);
        break;
      case 'Weekly':
        checkDate(items: provider.checkListHistoryModelWeekly);
        break;
      case 'Monthly':
        checkDate(items: provider.checkListHistoryModelMonthley);
        break;
      case 'Quarterly':
        checkDate(items: provider.checkListHistoryModelQuarterly);
        break;
      case 'Half-Yearly':
        checkDate(items: provider.checkListHistoryModelHalfyearly);
        break;
      case 'Yearly':
        checkDate(items: provider.checkListHistoryModelyearly);
        break;
      default:
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(width: 05 * w, color: stringToColor(widget.color)),
        ),
        borderRadius: BorderRadius.circular(10.0), // Rounded corners
      ),
      child: Column(
        children: [
          // Header section
          GestureDetector(
            onTap: () {
              setState(() {
                isExpanded = !isExpanded;
              });
            },
            child: Container(
              padding: EdgeInsets.only(
                  top: 15.0, left: 15, right: 15, bottom: !isExpanded ? 15 : 5),
              decoration: BoxDecoration(
                color: Colors.white, // Header background color
                borderRadius: BorderRadius.circular(10.0),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Image.asset(
                        "assets/icons/calendar_red.png",
                        height: 22 * h,
                        width: 22 * w,
                        color: stringToColor(widget.color),
                      ),
                      SizedBox(
                        width: 8 * w,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            widget.heading,
                            style: tsS16w500,
                          ),
                          if (subTitle != null)
                            Text(subTitle ?? '', style: tsS12w4cE61212)
                        ],
                      ),
                    ],
                  ),
                  Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: Colors.grey, // Custom expand/collapse icon color
                  ),
                ],
              ),
            ),
          ),

          // Animated body section
          AnimatedSize(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeIn,
            child: SizedBox(
              // height: (widget.listOfModel.length == 1 && isExpanded)
              //     ? 80 * h
              //     : widget.listOfModel.length > 1 && isExpanded
              //         ? 260 * h
              //         : 0,
              child: isExpanded
                  ? Container(
                      alignment: Alignment.topLeft,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      padding: EdgeInsets.only(top: 14 * h),
                      child: Padding(
                          padding: EdgeInsets.only(left: 16 * w, top: 0),
                          child: ListView.separated(
                            physics: const ClampingScrollPhysics(),
                            padding: EdgeInsets.only(bottom: 20 * h),
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              return report(
                                  item: widget.listOfModel[index],
                                  color: stringToColor(widget.color));
                            },
                            itemCount: widget.listOfModel.length,
                            separatorBuilder: (context, index) {
                              return SizedBox(
                                height: 10 * h,
                              );
                            },
                          )),
                    )
                  : const SizedBox(), // Placeholder when not expanded
            ),
          ),
        ],
      ),
    );
  }

  Widget report({required CheckListHistoryModel item, required Color color}) {
    bool isStatusUpdated = item.updatedStatus == true &&
        (item.status != null || item.response != null);
    return Container(
      padding: EdgeInsets.all(8 * h),
      margin: EdgeInsets.only(right: 15 * w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.06),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  item.point ?? "",
                  style: tsS12w500c636363,
                ),
                Text(
                  item.details ?? "",
                  style: tsS12w400979797,
                )
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              // provider.radioButtonHistoryValue =
              //     !provider.radioButtonHistoryValue;
            },
            child: Container(
              width: 20 * h,
              height: 20 * w,
              padding: const EdgeInsets.all(1),
              decoration: BoxDecoration(
                color: isStatusUpdated ? Colors.green : Colors.red,
                // color: item.updatedStatus == false && item.status != null
                //     ? Colors.red
                //     : item.updatedStatus == true && item.status != null
                //         ? Colors.green
                //         : null,
                shape: BoxShape.circle,
                border: item.status == 'null'
                    ? null
                    : Border.all(
                        color: ThemeColors.color979797,
                        // Set border color
                        width: 1.0, // Reduced border thickness
                      ),
              ),
              child: Center(
                child: Icon(
                  isStatusUpdated ? Icons.check : Icons.close,
                  color: Colors.white,
                  size: 16,
                ),
                // child: item.updatedStatus == false && item.status != null
                //     ? const Icon(
                //         Icons.close,
                //         color: Colors.white,
                //         size: 16,
                //       )
                //     : item.updatedStatus == true && item.status != null
                //         ? const Icon(
                //             Icons.check,
                //             color: Colors.white,
                //             size: 16,
                //           )
                //         : null
              ),
            ),
          ),
        ],
      ),
    );
  }
}
