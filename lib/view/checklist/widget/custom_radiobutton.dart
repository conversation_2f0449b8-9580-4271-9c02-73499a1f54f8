import 'package:e8_hr_portal/model/checklist_history_model.dart';
import 'package:e8_hr_portal/provider/checklist_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/material.dart';

class CustomRadioButton extends StatelessWidget {
  final ChecklistProvider provider;
  final CheckListHistoryModel item;

  final Color borderColor; // Parameter for border color
  final void Function()? onChanged;

  const CustomRadioButton({
    super.key,
    required this.item,
    required this.provider,
    required this.borderColor, // Accept border color
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onChanged,
      child: Row(
        children: [
          Container(
              width: 20 * h,
              height: 20 * w,
              padding: const EdgeInsets.all(3),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: item.updatedStatus == false && item.status == null
                      ? borderColor
                      : item.updatedStatus == true &&
                              item.status?.toLowerCase() == "yes"
                          ? ThemeColors.primaryColor
                          : borderColor, // Set border color
                  width: 1.0, // Reduced border thickness
                ),
              ),
              child: item.updatedStatus == false && item.status == null
                  ? null
                  : item.updatedStatus == true &&
                          item.status?.toLowerCase() == "yes"
                      ? Container(
                          height: 16 * h,
                          width: 16 * w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: item.updatedStatus == true &&
                                    item.status?.toLowerCase() == "yes"
                                ? ThemeColors.primaryColor
                                : borderColor, // Fill color for selected
                          ),
                        )
                      : null
              // No icon if not selected
              ),
          SizedBox(width: 4 * w),
          Text(
            "Yes",
            style: tsS14w400c979797, // Your text style
          ),
        ],
      ),
    );
  }
}

class CustomRadioButtonNo extends StatelessWidget {
  final ChecklistProvider provider;
  final CheckListHistoryModel item;

  final Color borderColor;
  final void Function()? onChanged;

  const CustomRadioButtonNo({
    super.key,
    required this.item,
    required this.provider,
    required this.borderColor,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onChanged,
      child: Row(
        children: [
          Container(
            width: 20 * h,
            height: 20 * w,
            padding: const EdgeInsets.all(3),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: item.updatedStatus == false && item.status == null
                    ? borderColor
                    : item.updatedStatus == true &&
                            item.status?.toLowerCase() == "no"
                        ? ThemeColors.primaryColor
                        : borderColor,
                width: 1.0, // Reduced border thickness
              ),
            ),
            child: item.updatedStatus == false && item.status == null
                ? null
                : item.updatedStatus == true &&
                        item.status?.toLowerCase() == "no"
                    ? Container(
                        height: 16 * h,
                        width: 16 * w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: item.updatedStatus == true &&
                                  item.status?.toLowerCase() == "no"
                              ? ThemeColors.primaryColor
                              : borderColor, // Fill color for selected
                        ),
                      )
                    : null,
          ),
          SizedBox(width: 4 * w),
          Text(
            "No",
            style: tsS14w400c979797, // Your text style
          ),
        ],
      ),
    );
  }
}
