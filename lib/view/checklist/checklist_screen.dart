import 'package:e8_hr_portal/provider/checklist_provider.dart';
import 'package:e8_hr_portal/provider/date_picker_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/checklist/checklist_history_screen.dart';
import 'package:e8_hr_portal/view/checklist/widget/cutom_expani.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';
import 'model/checklist_status_model.dart';
import 'widget/common_small_dropdown_button.dart';

class ChecklistScreen extends StatefulWidget {
  const ChecklistScreen({super.key});

  @override
  State<ChecklistScreen> createState() => _ChecklistScreenState();
}

class _ChecklistScreenState extends State<ChecklistScreen> {
  late ChecklistProvider _provider;
  late DatePickerProvider _dateProvider;
  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<bool> _onWillPop() async {
    await _provider.getChecklistCount();
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: HisenseScaffold(
        screenTitle: "Checklists",
        actions: [checklistHistoryWidget(context)],
        onTap: () => _onWillPop(),
        body: SizedBox(
          width: double.infinity,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16 * w),
            child: ListView(
              children: [
                Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Consumer<ChecklistProvider>(
                            builder: (context, provider, _) {
                              return CommonSmallDropdownButton(
                                value: provider.selectedChecklistStatus,
                                items: provider.checklistStatusList.map(
                                  (item) {
                                    return DropdownMenuItem<ChecklistTypeModel>(
                                      value: item,
                                      child: FittedBox(
                                        child: Text(
                                          item.name ?? '',
                                        ),
                                      ),
                                    );
                                  },
                                ).toList(),
                                onChanged: (val) async {
                                  if (val == null) return;
                                  try {
                                    EasyLoading.show();
                                    provider.onChangedSelectedStatus(item: val);
                                    _dateProvider.selectedDate = DateTime.now();
                                    _provider.filterDate = DateTime.now();
                                    await _provider.getChecklist();
                                    EasyLoading.dismiss();
                                  } catch (e) {
                                    EasyLoading.dismiss();
                                    debugPrint(e.toString());
                                  }
                                },
                              );
                            },
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Consumer<ChecklistProvider>(
                            builder: (context, provider, _) {
                              // if (provider.selectedChecklistStatus?.id == 1) {
                              //   return const SizedBox.shrink();
                              // }
                              return InkWell(
                                onTap: () async {
                                  try {
                                    _provider.filterDate = await _dateProvider
                                        .monthAndYearPicker(context: context);
                                    if (_provider.filterDate != null) {
                                      EasyLoading.show();
                                      await _provider.getChecklist();
                                      EasyLoading.dismiss();
                                    }
                                  } catch (e) {
                                    EasyLoading.dismiss();
                                    debugPrint(e.toString());
                                  }
                                },
                                child: Container(
                                  padding:
                                      const EdgeInsets.fromLTRB(5, 0, 5, 0),
                                  // width: 90 * w,
                                  height: 30 * h,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Consumer<ChecklistProvider>(
                                          builder: (context, provider, _) {
                                            if (provider.filterDate != null) {
                                              final date = formatDateFromDate(
                                                  dateTime:
                                                      provider.filterDate ??
                                                          DateTime.now(),
                                                  format: 'MMM yyyy');
                                              return Text(date,
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis);
                                            }
                                            return Text('Select Date',
                                                maxLines: 1,
                                                overflow:
                                                    TextOverflow.ellipsis);
                                          },
                                        ),
                                      ),
                                      Icon(Icons.arrow_drop_down)
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(width: 10),
                        // const Spacer(),
                        Flexible(
                          child: Consumer<ChecklistProvider>(
                            builder: (context, provider, _) {
                              // if (provider.selectedChecklistStatus?.id == 1) {
                              //   return const SizedBox.shrink();
                              // }
                              return CommonSmallDropdownButton(
                                hintText: 'Type',
                                value: provider.selectedChecklistType,
                                items: provider.checklistTypeList.map(
                                  (item) {
                                    return DropdownMenuItem<ChecklistTypeModel>(
                                      value: item,
                                      child: FittedBox(
                                        child: Text(item.name ?? ''),
                                      ),
                                    );
                                  },
                                ).toList(),
                                onChanged: (val) async {
                                  if (val == null) return;
                                  try {
                                    EasyLoading.show();
                                    provider.onChangedSelectedType(item: val);
                                    await _provider.getChecklist();
                                    EasyLoading.dismiss();
                                  } catch (e) {
                                    EasyLoading.dismiss();
                                    debugPrint(e.toString());
                                  }
                                },
                                // isExpanded: false,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 16 * h),
                    Consumer<ChecklistProvider>(
                      builder: (context, pro, _) {
                        // if (pro.isLoading == true) {
                        //   return const Padding(
                        //     padding: EdgeInsets.only(top: 50.0),
                        //     child: Center(
                        //       child: CircularProgressIndicator(),
                        //     ),
                        //   );
                        // }
                        if (pro.checkListModel.isEmpty &&
                            pro.isLoading == false) {
                          return Padding(
                            padding: EdgeInsets.only(top: 250.0 * h),
                            child: const Center(
                              child: Text("No assigned checklist"),
                            ),
                          );
                        }
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: pro.checkListModel
                              .map((e) => Padding(
                                    key: UniqueKey(),
                                    padding: EdgeInsets.only(bottom: 10 * h),
                                    child: CustomExpansionContainer(item: e),
                                  ))
                              .toList(),
                          // const SizedBox(
                          //   height: 10,
                          // ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget checklistHistoryWidget(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
      child: GestureDetector(
        onTap: () async {
          if (isRedundentClick(DateTime.now())) {
            return;
          }

          try {
            var provider = context.read<ChecklistProvider>();
            EasyLoading.show();
            provider.clearStartFromChecklist();
            await provider.getChecklistHistory();
            EasyLoading.dismiss();
            if (!context.mounted) return;
            PageNavigator.push(
                context: context, route: const ChecklistHistoryScreen());
          } catch (e) {
            debugPrint(e.toString());
          }
        },
        child: Image.asset(
          "assets/icons/checklist_clock.png",
          height: 24 * h,
          width: 24 * w,
        ),
      ),
    );
  }

  _init() async {
    _provider = context.read<ChecklistProvider>();
    _dateProvider = context.read<DatePickerProvider>();
    _provider.selectedChecklistStatus = _provider.checklistStatusList.first;

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      _provider.filterDate = DateTime.now();
      _dateProvider.selectedDate = DateTime.now();
      _provider.selectedChecklistStatus = _provider.checklistStatusList.first;

      _provider.checkListModel.clear();
      try {
        EasyLoading.show();
        await _provider.getShedulers();
        await _provider.getChecklistType();
        _provider.selectedChecklistType = _provider.checklistTypeList
            .firstWhere((element) => element.id == 0,
                orElse: () => ChecklistTypeModel(id: 0, name: 'All'));
        await _provider.getChecklist();
        EasyLoading.dismiss();
      } catch (e) {
        EasyLoading.dismiss();
        debugPrint(e.toString());
      }
    });
  }
}



  // SizedBox(height: h * 32),
                    // CustomExpansionPanelState(
                    //   provider: pro,
                    // ),
                    // const SizedBox(
                    //   height: 10,
                    // ),



 // ExpansionPanelList(
                    //   expansionCallback: (int index, bool isExpanded) {
                    //     pro.isExpanded = !pro.isExpanded;
                    //   },
                    //   children: [
                    //     ExpansionPanel(
                    //       headerBuilder:
                    //           (BuildContext context, bool isExpanded) {
                    //         return const ListTile(
                    //           title: Text("item.headerValue"),
                    //         );
                    //       },
                    //       body: ListTile(
                    //           title: const Text("item.expandedValue"),
                    //           subtitle: const Text(
                    //               'To delete this panel, tap the trash can icon'),
                    //           trailing: const Icon(Icons.delete),
                    //           onTap: () {
                    //             pro.isExpanded = !pro.isExpanded;
                    //           }),
                    //       isExpanded: pro.isExpanded,
                    //     ),
                    //   ],
                    // ),
                    // SizedBox(
                    //   height: 10 * h,
                    // ),
                    // ExpansionPanelList(
                    //     expansionCallback: (int index, bool isExpanded) {},
                    //     children: [
                    //       ExpansionPanel(
                    //         headerBuilder:
                    //             (BuildContext context, bool isExpanded) {
                    //           return const ListTile(
                    //             title: Text("item.headerValue"),
                    //           );
                    //         },
                    //         body: ListTile(
                    //             title: const Text("item.expandedValue"),
                    //             subtitle: const Text(
                    //                 'To delete this panel, tap the trash can icon'),
                    //             trailing: const Icon(Icons.delete),
                    //             onTap: () {}),
                    //         isExpanded: true,
                    //       ),
                    //     ])