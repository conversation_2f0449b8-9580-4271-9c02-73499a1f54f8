import 'package:e8_hr_portal/provider/checklist_provider.dart';
import 'package:e8_hr_portal/provider/date_picker_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/checklist/widget/custom_expanision_for_siginout.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';
import 'model/checklist_status_model.dart';

class ChecklistSignOutScreen extends StatefulWidget {
  const ChecklistSignOutScreen({super.key});

  @override
  State<ChecklistSignOutScreen> createState() => _ChecklistSignOutScreenState();
}

class _ChecklistSignOutScreenState extends State<ChecklistSignOutScreen> {
  late ChecklistProvider _provider;
  late DatePickerProvider _dateProvider;
  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<bool> _onWillPop() async {
    await _provider.getChecklistCount();

    return true;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: HisenseScaffold(
        screenTitle: "Checklist Punch-Out",
        onTap: () => _onWillPop(),
        body: SizedBox(
          width: double.infinity,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16 * w),
            child: ListView(
              children: [
                Column(
                  children: [
                    Consumer<ChecklistProvider>(
                      builder: (context, pro, _) {
                        if (pro.onSiginOutCheckListModel.isEmpty &&
                            pro.isLoading == false) {
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            if (pro.onSiginOutCheckListModel.isEmpty) {
                              Navigator.pop(context, true);
                            }
                          });
                          return Padding(
                            padding: EdgeInsets.only(top: 250.0 * h),
                            child: const Center(
                              child: Text("No assigned checklist"),
                            ),
                          );
                        }
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: pro.onSiginOutCheckListModel
                              .map((e) => Padding(
                                    key: UniqueKey(),
                                    padding: EdgeInsets.only(bottom: 10 * h),
                                    child: CustomExpansionContainerSiginOut(
                                        item: e),
                                  ))
                              .toList(),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _init() async {
    _provider = context.read<ChecklistProvider>();
    _dateProvider = context.read<DatePickerProvider>();
    _provider.selectedChecklistStatus = _provider.checklistStatusList.first;

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      _provider.filterDate = DateTime.now();
      _dateProvider.selectedDate = DateTime.now();
      _provider.selectedChecklistStatus = _provider.checklistStatusList.first;

      _provider.checkListModel.clear();
      try {
        EasyLoading.show();
        await _provider.getShedulers();
        await _provider.getChecklistType();
        _provider.selectedChecklistType = _provider.checklistTypeList
            .firstWhere((element) => element.id == 0,
                orElse: () => ChecklistTypeModel(id: 0, name: 'All'));
        await _provider.getChecklist();
        EasyLoading.dismiss();
      } catch (e) {
        EasyLoading.dismiss();
        debugPrint(e.toString());
      }
    });
  }
}



  // SizedBox(height: h * 32),
                    // CustomExpansionPanelState(
                    //   provider: pro,
                    // ),
                    // const SizedBox(
                    //   height: 10,
                    // ),



 // ExpansionPanelList(
                    //   expansionCallback: (int index, bool isExpanded) {
                    //     pro.isExpanded = !pro.isExpanded;
                    //   },
                    //   children: [
                    //     ExpansionPanel(
                    //       headerBuilder:
                    //           (BuildContext context, bool isExpanded) {
                    //         return const ListTile(
                    //           title: Text("item.headerValue"),
                    //         );
                    //       },
                    //       body: ListTile(
                    //           title: const Text("item.expandedValue"),
                    //           subtitle: const Text(
                    //               'To delete this panel, tap the trash can icon'),
                    //           trailing: const Icon(Icons.delete),
                    //           onTap: () {
                    //             pro.isExpanded = !pro.isExpanded;
                    //           }),
                    //       isExpanded: pro.isExpanded,
                    //     ),
                    //   ],
                    // ),
                    // SizedBox(
                    //   height: 10 * h,
                    // ),
                    // ExpansionPanelList(
                    //     expansionCallback: (int index, bool isExpanded) {},
                    //     children: [
                    //       ExpansionPanel(
                    //         headerBuilder:
                    //             (BuildContext context, bool isExpanded) {
                    //           return const ListTile(
                    //             title: Text("item.headerValue"),
                    //           );
                    //         },
                    //         body: ListTile(
                    //             title: const Text("item.expandedValue"),
                    //             subtitle: const Text(
                    //                 'To delete this panel, tap the trash can icon'),
                    //             trailing: const Icon(Icons.delete),
                    //             onTap: () {}),
                    //         isExpanded: true,
                    //       ),
                    //     ])