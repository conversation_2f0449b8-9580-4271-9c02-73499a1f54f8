import 'dart:io';

import 'package:flutter/material.dart';
import 'package:e8_hr_portal/helper/circula_loading_widget.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:provider/provider.dart';

class ConfirmProfilePhoto extends StatelessWidget {
  final File profilePhoto;
  const ConfirmProfilePhoto(this.profilePhoto, {super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(child: Center(child: Image.file(profilePhoto))),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Consumer<ProfileProvider>(
                    builder: (context, provider, _) {
                      return provider.isUploadingProfilePhoto
                          ? const Center(
                              child: CircularLoadingWidget(),
                            )
                          : TextButton(
                              onPressed: () {
                                provider.updateProfilePic(
                                  image: profilePhoto,
                                  context: context,
                                );
                              },
                              style: TextButton.styleFrom(
                                foregroundColor: Colors.white,
                              ),
                              child: const Text('Done'),
                            );
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
