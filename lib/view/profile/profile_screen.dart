import 'package:e8_hr_portal/view/profile/widgets/explore_web_portal.dart';
import 'package:e8_hr_portal/view/profile/widgets/my_responsibilities_tile.dart';
import 'package:e8_hr_portal/view/profile/widgets/profile_info_tile.dart';
import 'package:e8_hr_portal/view/profile/widgets/profile_picture_card.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:provider/provider.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Profile',
      body: Container(
        padding: const EdgeInsets.only(top: 20),
        decoration: BoxDecoration(
          color: ThemeColors.colorF4F5FA,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
        ),
        child: Consumer<ProfileProvider>(
          builder: (context, provider, _) {
            final user = provider.userDetailesModel;
            final profile = user?.profileDetails;
            final reportingPerson = profile?.reportingPerson;
            return ListView(
              padding: const EdgeInsets.all(15.0),
              children: [
                if (user != null) ProfilePictureCard(user: user),
                const MyResponsibilitiesTile(),
                if (user != null)
                  Container(
                    width: w * 343,
                    padding: EdgeInsets.fromLTRB(w * 13, h * 5, w * 13, h * 5),
                    decoration: BoxDecoration(
                      color: ThemeColors.colorFFFFFF,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        emailAddressTile(user.email),
                        contactNumberTile(profile?.contactNumber),
                        departmentTile(profile?.department),
                        branchTile(profile?.branch),
                        reportingPersonTile(reportingPerson?.name),
                      ],
                    ),
                  ),
                SizedBox(height: 20 * h),
                const ExploreWebPortal(),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget emailAddressTile(String? email) {
    return ProfileInfoTile(title: 'Email Address', subtitle: email);
  }

  Widget contactNumberTile(String? contactNumber) {
    return ProfileInfoTile(title: 'Contact Number', subtitle: contactNumber);
  }

  Widget departmentTile(String? department) {
    return ProfileInfoTile(title: 'Department', subtitle: department);
  }

  Widget branchTile(String? branch) {
    return ProfileInfoTile(title: 'Branch', subtitle: branch);
  }

  Widget reportingPersonTile(String? reportingPerson) {
    if (reportingPerson != null) {
      return ProfileInfoTile(
        title: "Reporting Person",
        subtitle: reportingPerson,
        showDivider: false,
      );
    }
    return const SizedBox.shrink();
  }
}
