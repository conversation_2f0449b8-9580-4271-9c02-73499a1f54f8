import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/user_status_provider.dart';
import 'package:intl/intl.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/profile/widgets/profile_avatar_widget.dart';
import 'package:e8_hr_portal/view/profile/widgets/profile_textfield_widget.dart';
import 'package:provider/provider.dart';

class EditProfile extends StatefulWidget {
  const EditProfile({super.key});
  @override
  // ignore: library_private_types_in_public_api
  _EditProfileState createState() => _EditProfileState();
}

class _EditProfileState extends State<EditProfile> {
  // late ProfileProvider _profileProvider;
  late TextEditingController _employeeIdController;
  late TextEditingController _emailController;
  late TextEditingController _phoneContrller;
  late TextEditingController _dateOfBirthController;
  late TextEditingController _dateOfJoinController;
  // late TextEditingController _skillsController;
  late TextEditingController _designationController;
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  // String? _selectedDesignation;
  // List<String> _skillList = [];
  final _formKey = GlobalKey<FormState>();
  @override
  void initState() {
    final profileProvider =
        Provider.of<ProfileProvider>(context, listen: false);

    // final statusProvider =
    //     Provider.of<UserStatusProvider>(context, listen: false);
    // statusProvider.clear();

    _employeeIdController = TextEditingController(
        text: profileProvider.userDetailesModel?.profileDetails?.employeeId
            .toString());
    _lastNameController = TextEditingController(
        text: profileProvider.userDetailesModel?.lastName.toString());
    _firstNameController = TextEditingController(
        text: profileProvider.userDetailesModel?.firstName.toString());
    _designationController = TextEditingController(
        text: profileProvider.userDetailesModel?.profileDetails?.designation
            .toString());
    _emailController =
        TextEditingController(text: profileProvider.userDetailesModel?.email);
    _phoneContrller = TextEditingController(
        text: profileProvider.userDetailesModel?.profileDetails?.contactNumber);
    if (profileProvider.userDetailesModel?.profileDetails?.dateOfBirth !=
        null) {
      DateTime birthdate = DateTime.parse(profileProvider
          .userDetailesModel!.profileDetails!.dateOfBirth
          .toString());
      final DateFormat formatter = DateFormat('yyyy-MM-dd');
      final String dob = formatter.format(birthdate);
      _dateOfBirthController = TextEditingController(text: dob);
    } else {
      _dateOfBirthController = TextEditingController();
    }
    if (profileProvider.userDetailesModel?.profileDetails?.dateOfJoining !=
        null) {
      DateTime dateofjoin = DateTime.parse(profileProvider
          .userDetailesModel!.profileDetails!.dateOfJoining
          .toString());
      final DateFormat formatter = DateFormat('yyyy-MM-dd');
      final String doj = formatter.format(dateofjoin);
      _dateOfJoinController = TextEditingController(text: doj);
    } else {
      _dateOfJoinController = TextEditingController();
    }

    // _selectedDesignation = profileProvider?.data?.profileDetails?.designation;
    // _skillList = LoggedInUser.skills.map((e) => e.toString()).toList();
    // _skillsController = TextEditingController();

    super.initState();
  }

  @override
  void dispose() {
    _employeeIdController.clear();
    _firstNameController.clear();
    _emailController.clear();
    _phoneContrller.clear();
    _dateOfBirthController.clear();
    _dateOfJoinController.clear();

    // _skillsController.clear();
    _lastNameController.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // final UserStatusProvider userStatusProvider =
    //     Provider.of<UserStatusProvider>(context, listen: false);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit profile'),
        leading: InkWell(
          onTap: () => Navigator.pop(context),
          child: const Icon(
            Icons.arrow_back_ios,
            color: Colors.white,
            size: 20,
          ),
        ),
      ),
      body: Consumer2<ProfileProvider, UserStatusProvider>(
          builder: (context, provider, status, _) {
        return Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(15.0),
            children: [
              const ProfileAvatarWidget(),
              const SizedBox(height: 35),
              // SizedBox(
              //   height: 15,
              // ),

              // Text(
              //   "Status",
              //   style: GoogleFonts.rubik(
              //     color: ThemeColors.titleColor,
              //   ),
              // ),
              // Consumer<UserStatusProvider>(builder: (context, status, child) {
              //   print('************************* STATUS');
              //   print(status.userStatus.length);
              //   return DropdownButtonFormField<EmployeeStatus?>(
              //     elevation: 0,
              //     decoration: InputDecoration(
              //       hintText: "Select the current status",
              //       hintStyle: GoogleFonts.rubik(
              //         fontSize: 14,
              //         color: ThemeColors.titleColor,
              //       ),
              //       border: const UnderlineInputBorder(
              //         borderSide: BorderSide(color: Color(0xFFDEE7FF)),
              //       ),
              //       enabledBorder: const UnderlineInputBorder(
              //         borderSide: BorderSide(color: Color(0xFFDEE7FF)),
              //       ),
              //       focusedBorder: const UnderlineInputBorder(
              //         borderSide: BorderSide(color: Colors.grey),
              //       ),
              //     ),
              //     items: status.userStatus.map((e) {
              //       return DropdownMenuItem<EmployeeStatus>(
              //         value: e,
              //         child: Row(
              //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //           crossAxisAlignment: CrossAxisAlignment.center,
              //           children: [
              //             Text(
              //               e!.name.toString(),
              //             ),
              //           ],
              //         ),
              //       );
              //     }).toList(),
              //     selectedItemBuilder: (BuildContext context) {
              //       return status.userStatus.map<Widget>((item) {
              //         return Text(item!.name.toString());
              //       }).toList();
              //     },
              //     value: status.selectedUserStatus,
              //     onChanged: (EmployeeStatus? userStatusModel) {
              //       if (status.selectedUserStatus == null) {
              //         status.selectedUserStatus = userStatusModel;
              //       } else {
              //         status.selectedUserStatus = null;
              //         status.selectedUserStatus = userStatusModel;
              //       }
              //       // if (!provider.selectedAmenitiess.contains(userStatusModel)) {
              //       //   provider.addAmenities(amenitiesModel!);
              //       // }
              //     },
              //   );
              // }),
              ProfileTextFieldWidget(
                controller: _employeeIdController,
                labelText: 'Employee ID',
                validator: Validator.text,
                readOnly: true,
              ),

              const SizedBox(height: 5),
              ProfileTextFieldWidget(
                controller: _firstNameController,
                labelText: 'First Name',
                validator: Validator.text,
              ),
              const SizedBox(height: 5),
              ProfileTextFieldWidget(
                controller: _lastNameController,
                labelText: 'Last Name',
                validator: Validator.text,
              ),
              const SizedBox(height: 5),
              // DropdownButtonFormField(
              //   items: provider.destinationList?.data?.map((e) {
              //     return DropdownMenuItem<String>(
              //       value: e,
              //       child: Text(e),
              //     );
              //   }).toList(),
              //   onChanged: (String? value) {
              //     setState(() {
              //       _selectedDesignation = value;
              //     });
              //   },
              //   value: _selectedDesignation,
              //   decoration: InputDecoration(
              //     labelText: 'Designation',
              //     labelStyle: GoogleFonts.rubik(
              //       color: ThemeColors.titleColor,
              //     ),
              //     border: const UnderlineInputBorder(
              //       borderSide: BorderSide(
              //         color: Color(0xFFDEE7FF),
              //       ),
              //     ),
              //     enabledBorder: const UnderlineInputBorder(
              //       borderSide: BorderSide(
              //         color: Color(0xFFDEE7FF),
              //       ),
              //     ),
              //   ),
              ProfileTextFieldWidget(
                controller: _designationController,
                labelText: 'Designation',
                keyboardType: TextInputType.emailAddress,
                readOnly: true,
              ),
              ProfileTextFieldWidget(
                controller: _emailController,
                labelText: 'Email Address',
                keyboardType: TextInputType.emailAddress,
                readOnly: true,
              ),
              const SizedBox(height: 5),
              ProfileTextFieldWidget(
                controller: _phoneContrller,
                labelText: 'Contact Number',
                keyboardType: TextInputType.phone,
                validator: Validator.text,
                maxlength: 10,
              ),
              const SizedBox(height: 5),
              ProfileTextFieldWidget(
                controller: _dateOfBirthController,
                labelText: 'Date of Birth',
                // onTap: () => _selectDateOfBirth(context),
                readOnly: true,
                validator: Validator.text,
              ),
              const SizedBox(height: 5),
              ProfileTextFieldWidget(
                controller: _dateOfJoinController,
                labelText: 'Date of Join',
                // onTap: () => _selectDateOfJoin(context),
                readOnly: true,
                validator: Validator.text,
              ),
              const SizedBox(height: 5),
              // Row(
              //   children: [
              //     Expanded(
              //       child: ProfileTextFieldWidget(
              //         controller: _skillsController,
              //         labelText: 'Skills',
              //         textCapitalization: TextCapitalization.words,
              //       ),
              //     ),
              //     IconButton(
              //       onPressed: () {
              //         if (_skillsController.text.isNotEmpty) {
              //           hideKeyboard(context);
              //           setState(() {
              //             _skillList.add(_skillsController.text);
              //           });
              //           _skillsController.clear();
              //         }
              //       },
              //       icon: const Icon(Icons.add_circle_outline),
              //     ),
              //   ],
              // ),
              // const SizedBox(height: 5),
              // Wrap(
              //   spacing: 5,
              //   runSpacing: 5,
              //   children: _skillList
              //       .map(
              //         (e) => Chip(
              //           label: Text(e),
              //           onDeleted: () {
              //             setState(() {
              //               _skillList.remove(e);
              //             });
              //           },
              //         ),
              //       )
              //       .toList(),
              // ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () async {
                  if (_formKey.currentState!.validate()) {
                    await provider.editProfile(
                        context: context,
                        firstName: _firstNameController.text,
                        lastName: _lastNameController.text,
                        number: _phoneContrller.text,
                        dob: _dateOfBirthController.text,
                        email: _emailController.text,
                        dateOfJoin: _dateOfJoinController.text);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: ThemeColors.secondaryColor,
                  textStyle: GoogleFonts.rubik(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  minimumSize: const Size(double.infinity, 55),
                ),
                child: const Text('Update'),
              ),
            ],
          ),
        );
      }),
    );
  }

  // Future<void> updateProfile() async {
  //   if (_formKey.currentState!.validate()) {
  //     Map<String, dynamic> json = {
  //       'employeeId': _employeeIdController.text,
  //       'name': _nameController.text,
  //       'designation_name': _selectedDesignation,
  //       'phone': _phoneContrller.text,
  //       'birthdate': dateOfBirth,
  //       'start_date': dateOfJoin,
  //       'skills': _skillList,
  //     };
  //     if (dateOfBirth != null) {
  //       json['upcomingBirthday'] = getUpcomingBirthday(dateOfBirth!);
  //     }
  //     if (dateOfJoin != null) {
  //       json['upcomingWorkAnniversary'] =
  //           getUpcomingWorkAnniversary(dateOfJoin!);
  //     }
  //     EasyLoading.show();
  //     await _profileProvider.updateProfile(json);
  //     EasyLoading.dismiss();
  //     Navigator.pop(context);
  //   }
  // }

  // DateTime? dateOfBirth;
  // Future<void> _selectDateOfBirth(BuildContext context) async {
  //   DateTime initialDate = DateTime(DateTime.now().year - 15);
  //   DateTime firstDate = DateTime(DateTime.now().year - 100);
  //   DateTime lastDate = DateTime(DateTime.now().year - 15);
  //   final DateTime? picked = await showDatePicker(
  //     context: context,
  //     initialDate: initialDate,
  //     firstDate: firstDate,
  //     lastDate: lastDate,
  //   );
  //   if (picked != null) {
  //     String selectedDateOfBirth =
  //         formatDateFromDate(dateTime: picked, format: 'yyyy-MM-dd');
  //     _dateOfBirthController.text = selectedDateOfBirth;
  //     dateOfBirth = picked;
  //   }
  // }

  // DateTime? dateOfJoin;
  // Future<void> _selectDateOfJoin(BuildContext context) async {
  //   DateTime initialDate = DateTime.now();
  //   DateTime firstDate = DateTime(DateTime.now().year - 100);
  //   DateTime lastDate = DateTime.now();
  //   final DateTime? picked = await showDatePicker(
  //     context: context,
  //     initialDate: initialDate,
  //     firstDate: firstDate,
  //     lastDate: lastDate,
  //   );
  //   if (picked != null) {
  //     String selectedDateOfJoin =
  //         formatDateFromDate(dateTime: picked, format: 'yyyy-MM-dd');
  //     _dateOfJoinController.text = selectedDateOfJoin;
  //     dateOfJoin = picked;
  //   }
  // }
}
