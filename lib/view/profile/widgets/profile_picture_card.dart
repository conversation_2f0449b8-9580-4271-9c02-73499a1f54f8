import 'dart:math';

import 'package:e8_hr_portal/model/user_detailes_model.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/profile/widgets/badges_section.dart';
import 'package:e8_hr_portal/view/profile/widgets/profile_picture_avatar.dart';
import 'package:e8_hr_portal/view/work_updates.dart/work_status_bottomsheet.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class ProfilePictureCard extends StatelessWidget {
  final UserDetailesModel user;
  const ProfilePictureCard({required this.user, super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ProfileProvider>();
    return Container(
      width: w * 343,
      padding: EdgeInsets.only(bottom: h * 10),
      decoration: BoxDecoration(
        color: ThemeColors.colorFFFFFF,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          ProfilePictureAvatar(
            name: user.firstName,
            photo: user.profileDetails?.profilePic,
            color: user.profileDetails?.empStatus?.colorCode != null
                ? stringToColor(user.profileDetails?.empStatus?.colorCode ?? "")
                : null,
          ),
          Text(
            "${user.firstName ?? ""} ${user.lastName ?? ""}",
            style: tsS20w500c161616,
          ),
          Text(
            user.profileDetails!.designation.toString(),
            style: tsS14w400c979797,
          ),
          SizedBox(height: h * 4),
          Text(
            "EMP ID : ${user.profileDetails!.employeeId}",
            style: tsS14w400Black,
          ),
          SizedBox(
            height: h * 13,
          ),
          InkWell(
            onTap: () {
              provider.isChangeIcon = true;
              showModalBottomSheet<void>(
                useSafeArea: true,
                context: context,
                isDismissible: true,
                isScrollControlled: true,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                ),
                builder: (BuildContext context) {
                  return const WorkStatusUpdateBottomSheet();
                },
              ).whenComplete(
                () => provider.isChangeIcon = false,
              );
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              height: h * 30,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(50),
                gradient: LinearGradient(
                  colors: [ThemeColors.colorFCC400, ThemeColors.colorFCD800],
                ),
              ),
              child: DropdownButton(
                underline: const SizedBox(),
                items: null,
                onChanged: (value) {},
                hint: Text(
                  user.profileDetails?.empStatus?.name ?? "",
                  style: tsS12w500FFFFF,
                ),
                icon: statusDropdownIcon(provider),
              ),
            ),
          ),
          SizedBox(height: h * 32),
          const BadgesSection(),
        ],
      ),
    );
  }

  Widget statusDropdownIcon(ProfileProvider provider) {
    if (provider.isChangeIcon) {
      return Transform.rotate(
        angle: pi,
        child: const ImageIcon(
          AssetImage("assets/icons/arrow.png"),
          color: Colors.white,
        ),
      );
    }
    return const ImageIcon(
      AssetImage("assets/icons/arrow.png"),
      color: Colors.white,
    );
  }
}
