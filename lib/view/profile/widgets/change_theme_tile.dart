import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/profile/widgets/change_theme_bottom_sheet.dart';
import 'package:flutter/material.dart';

class ChangeThemeTile extends StatelessWidget {
  const ChangeThemeTile({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onTap(context),
      child: Container(
        margin: EdgeInsets.only(bottom: h * 15),
        padding: EdgeInsets.fromLTRB(w * 13, h * 15, w * 13, h * 15),
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Change Theme",
              style: tsS14NormalBlack,
            ),
            const Icon(Icons.arrow_circle_right_outlined)
          ],
        ),
      ),
    );
  }

  void onTap(BuildContext context) {
    showModalBottomSheet<void>(
      useSafeArea: true,
      context: context,
      isDismissible: true,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(12),
        ),
      ),
      builder: (BuildContext context) {
        return const ChangeThemeBottomSheet();
      },
    );
  }
}
