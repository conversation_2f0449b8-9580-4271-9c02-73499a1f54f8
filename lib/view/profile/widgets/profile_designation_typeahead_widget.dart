// import 'package:flutter/material.dart';
// import 'package:flutter_typeahead/flutter_typeahead.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:e8_hr_portal/provider/profile_provider.dart';
// import 'package:e8_hr_portal/util/colors.dart';

// class ProfileDesignationTypeAheadWidget extends StatelessWidget {
//   final TextEditingController controller;
//   final ProfileProvider provider;
//   const ProfileDesignationTypeAheadWidget({
//     Key? key,
//     required this.controller,
//     required this.provider,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return TypeAheadFormField(
//       textFieldConfiguration: TextFieldConfiguration(
//         controller: controller,
//         style: DefaultTextStyle.of(context).style.copyWith(
//               fontSize: 16,
//               fontWeight: FontWeight.w400,
//             ),
//         textCapitalization: TextCapitalization.words,
//         decoration: InputDecoration(
//           labelText: 'Designation',
//           labelStyle: GoogleFonts.rubik(
//             color: ThemeColors.titleColor,
//           ),
//           border: const UnderlineInputBorder(
//             borderSide: BorderSide(
//               color: Color(0xFFDEE7FF),
//             ),
//           ),
//           enabledBorder: const UnderlineInputBorder(
//             borderSide: BorderSide(
//               color: Color(0xFFDEE7FF),
//             ),
//           ),
//         ),
//       ),
//       suggestionsCallback: (pattern) async {
//         return await provider.suggestionsCallback(pattern);
//       },
//       itemBuilder: (context, suggestion) {
//         return ListTile(title: Text('$suggestion'));
//       },
//       onSuggestionSelected: (suggestion) {
//         controller.text = suggestion.toString();
//       },
//     );
//   }
// }
