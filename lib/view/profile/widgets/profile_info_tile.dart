import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/material.dart';

class ProfileInfoTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final bool showDivider;
  const ProfileInfoTile({
    required this.title,
    this.subtitle,
    this.showDivider = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (subtitle != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SizedBox(height: h * 10),
          Text(
            title,
            style: tsS12w500c949494,
          ),
          Sized<PERSON><PERSON>(height: h * 3),
          Text(
            subtitle!,
            style: tsS14w500c2C2D33,
          ),
          SizedBox(height: h * 14),
          if (showDivider)
            Divider(
              color: ThemeColors.colorD9D9D9,
              thickness: 1,
              height: 3,
            ),
        ],
      );
    }
    return const SizedBox.shrink();
  }
}
