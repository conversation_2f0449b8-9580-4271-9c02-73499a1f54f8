import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class ProfilePictureAvatar extends StatelessWidget {
  final String? photo;
  final String? name;
  final Color? color;
  const ProfilePictureAvatar({this.photo, this.name, this.color, super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ProfileProvider>();
    return GestureDetector(
      onTap: () => provider.changeProfilePicture(context),
      child: Stack(
        children: [
          Container(
            width: w * 88,
            height: h * 88,
            margin: EdgeInsets.only(top: h * 13, bottom: h * 10),
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.red,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(500),
              child: Stack(
                fit: StackFit.passthrough,
                children: [
                  CachedNetworkImage(
                    width: w * 88,
                    height: h * 88,
                    fit: BoxFit.cover,
                    imageUrl: photo ?? '',
                    errorWidget: (context, url, error) {
                      return Container(
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          shape: BoxShape.circle,
                        ),
                        child: Text(
                          name != null
                              ? name!
                                  .substring(0, 1)
                                  .toUpperCase()
                                  .toUpperCase()
                              : '',
                          style: GoogleFonts.rubik(
                            fontSize: 60,
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                      );
                    },
                  ),
                  Container(
                    height: h * 30,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(top: h * 66),
                    color: const Color.fromRGBO(0, 0, 0, 0.7),
                    child: Text(
                      'Change',
                      style: tsS10w400cFFFFFF,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (color != null)
            Positioned(
              right: 0,
              bottom: 18 * h,
              child: Container(
                height: 18 * h,
                width: 18 * w,
                decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                          offset: Offset(2, 2),
                          blurRadius: 15,
                          spreadRadius: 0,
                          color: Color.fromRGBO(0, 0, 0, 0.3))
                    ],
                    shape: BoxShape.circle,
                    color: color,
                    border: Border.all(color: Colors.white, width: 1)),
              ),
            ),
        ],
      ),
    );
  }
}
