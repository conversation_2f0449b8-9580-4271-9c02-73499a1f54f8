import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/badge_model.dart';
import 'package:e8_hr_portal/provider/badge_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/badges/badges_screen.dart';
import 'package:e8_hr_portal/view/badges/utils/unlocked_badge_bottom_sheet_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class BadgesSection extends StatelessWidget {
  const BadgesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _titleWidget(context: context),
        _bageBuilder(context),
      ],
    );
  }

  Widget _titleWidget({required BuildContext context}) {
    return Consumer<BadgeProvider>(
      builder: (context, provider, _) {
        if (provider.badgeModelInProfileList.isEmpty) {
          return const SizedBox();
        }
        return Padding(
          padding: const EdgeInsets.only(left: 13),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(right: 13),
                child: Divider(height: 0, color: ThemeColors.colorD9D9D9),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Badges',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                      color: ThemeColors.color2C2D33,
                    ),
                  ),
                  TextButton(
                    onPressed: () async {
                      BadgeProvider provider =
                          Provider.of<BadgeProvider>(context, listen: false);
                      provider.badgeSelectedFilter =
                          provider.badgeFilterList.first;
                      provider.getBadgesWithFilter(context: context);
                      if (!context.mounted) return;
                      Navigator.push(
                          context,
                          CupertinoPageRoute(
                              builder: (context) => const BadgesScreen()));
                    },
                    style: TextButton.styleFrom(
                      textStyle: GoogleFonts.poppins(
                        fontWeight: FontWeight.w400,
                        fontSize: 11,
                      ),
                      foregroundColor: ThemeColors.colorF9637D,
                    ),
                    child: const Text('More...'),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _bageBuilder(BuildContext context) {
    return FutureBuilder(
      future: context.read<BadgeProvider>().getBadgesInProfile(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator.adaptive(),
          );
        }
        if (snapshot.hasError) {
          return const Center(
            child: Text('Something went wrong'),
          );
        }
        // List<BadgeModel> badgeList = snapshot.data?.sublist(1, 2) ?? [];
        List<BadgeModel> badgeList = snapshot.data ?? [];
        if (badgeList.isEmpty) {
          return const SizedBox();
        }

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0 * w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: badgeList.map((e) {
              BadgeModel badgeModel = e;
              return Expanded(
                child: Row(
                  children: [
                    _badgeAvatarWidget(
                        badgeModel: badgeModel, context: context),
                    const Spacer(),
                  ],
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  Widget _badgeAvatarWidget(
      {required BadgeModel badgeModel, required BuildContext context}) {
    String image = badgeModel.image ?? '';
    String name = badgeModel.name ?? '';
    bool isLocked = badgeModel.isLocked ?? true;
    int? achiveCount = badgeModel.achieveCount;
    // isLocked = false; //============
    // achiveCount = 2;
    return SizedBox(
      width: h * 70,
      child: GestureDetector(
        onTap: () => LocalBadgeBottomSheetUtils.showBottomSheet(
            context: context, badgeModel: badgeModel, isLocked: isLocked),
        // : () {},
        child: Stack(
          alignment: Alignment.center,
          children: [
            Column(
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Opacity(
                      opacity: isLocked ? 0.2 : 1,
                      child: Stack(
                        alignment: Alignment.topRight,
                        children: [
                          Container(
                            height: h * 64,
                            // width: h * 64,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: ThemeColors.colorE6E6E6,
                                width: 2,
                              ),
                              image: DecorationImage(
                                fit: BoxFit.cover,
                                image: CachedNetworkImageProvider(
                                  image,
                                ),
                              ),
                            ),
                          ),
                          if (achiveCount != null)
                            Positioned(
                              right: 0,
                              top: 0,
                              child: Align(
                                alignment: Alignment.topRight,
                                child: Container(
                                  // height: 30,
                                  // width: 20,
                                  padding: const EdgeInsets.all(5),
                                  alignment: Alignment.center,
                                  decoration: const BoxDecoration(
                                      color: Color(0xFF5570F1),
                                      shape: BoxShape.circle),
                                  child: Text(
                                    "$achiveCount",
                                    textAlign: TextAlign.center,
                                    style: GoogleFonts.poppins(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    if (isLocked && achiveCount == null) _lockWidget(),
                  ],
                ),
                Opacity(
                  opacity: isLocked ? 0.2 : 1,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 5.0, horizontal: 8),
                    child: Text(
                      name,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      style: GoogleFonts.poppins(
                        fontSize: 10 * f,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _lockWidget() => Container(
        height: 64 * h,
        padding: const EdgeInsets.only(left: 12, right: 12),
        alignment: Alignment.center,
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset('assets/icons/lock.svg'),
            Text(
              "Locked",
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 10 * f,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
}
