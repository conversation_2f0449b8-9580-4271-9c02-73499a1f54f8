import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/responsibilities/responsibilities_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';

class MyResponsibilitiesTile extends StatelessWidget {
  const MyResponsibilitiesTile({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onMyResponsibilitiesPressed(context),
      child: Container(
        width: w * 343,
        margin: EdgeInsets.only(top: h * 15, bottom: h * 15),
        padding: EdgeInsets.fromLTRB(
          w * 13,
          h * 15,
          w * 13,
          h * 15,
        ),
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: ThemeColors.colorFFFFFF,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "My Responsibilities",
              style: tsS14NormalBlack,
            ),
            const Icon(Icons.arrow_circle_right_outlined)
          ],
        ),
      ),
    );
  }

  onMyResponsibilitiesPressed(BuildContext context) async {
    final provider = context.read<ProfileProvider>();
    EasyLoading.show();
    if (!provider.isEmpResponsibilityLoading) {
      provider.isEmpResponsibilityLoading = true;
      bool isGranded = await provider.getEmpResponsilbility(context: context);
      if (isGranded && context.mounted) {
        PageNavigator.push(
          context: context,
          route: const ResponsibilitiesScreen(),
        );
      }
      EasyLoading.dismiss();
    }
  }
}
