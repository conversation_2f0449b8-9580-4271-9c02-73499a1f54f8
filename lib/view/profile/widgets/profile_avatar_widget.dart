import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:provider/provider.dart';

class ProfileAvatarWidget extends StatelessWidget {
  const ProfileAvatarWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Consumer<ProfileProvider>(
        builder: (context, provider, child) {
          final data = provider.userDetailesModel;
          return GestureDetector(
            onTap: () {
              provider.changeProfilePicture(context);
            },
            child: SizedBox(
              width: 156,
              height: 150,
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: CachedNetworkImage(
                      width: 156,
                      height: 156,
                      fit: BoxFit.cover,
                      imageUrl: "${data?.profileDetails?.profilePic}",
                      errorWidget: (context, url, error) {
                        return Container(
                          // width: 156,
                          // height: 150,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            data?.firstName != null
                                ? data!.firstName!.substring(0, 1).toUpperCase()
                                : "",
                            style: GoogleFonts.rubik(
                                fontSize: 60,
                                fontWeight: FontWeight.w700,
                                color: Colors.white),
                          ),
                        );
                      },
                    ),
                  ),
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                      decoration: BoxDecoration(
                        color: ThemeColors.secondaryColor,
                        borderRadius: const BorderRadius.vertical(
                          bottom: Radius.circular(10),
                        ),
                      ),
                      width: double.infinity,
                      child: const Text(
                        'Edit',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
