import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/colors.dart';

class ProfileTextFieldWidget extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final TextInputType? keyboardType;
  final void Function()? onTap;
  final bool readOnly;
  final TextCapitalization textCapitalization;
  final String? Function(String?)? validator;
  final int? maxlength;
  final Widget? label;
  const ProfileTextFieldWidget({
    super.key,
    this.controller,
    this.labelText,
    this.keyboardType,
    this.onTap,
    this.readOnly = false,
    this.textCapitalization = TextCapitalization.none,
    this.validator,
    this.maxlength,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      textCapitalization: textCapitalization,
      onTap: onTap,
      readOnly: readOnly,
      maxLength: maxlength,
      decoration: InputDecoration(
          labelText: labelText,
          label: label,
          labelStyle: GoogleFonts.rubik(
            color: ThemeColors.titleColor,
          ),
          border: const UnderlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xFFDEE7FF),
            ),
          ),
          enabledBorder: const UnderlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xFFDEE7FF),
            ),
          ),
          focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(
            color: Colors.grey,
          ))),
      validator: validator,
    );
  }
}
