import 'package:e8_hr_portal/model/theme_model.dart';
import 'package:e8_hr_portal/provider/theme_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/theme_type.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

List<ThemeModel> themesList = [
  ThemeModel(title: 'Default Theme', type: ThemeType.yellowTheme),
  ThemeModel(title: 'Purple Blue Theme', type: ThemeType.purpleBlueTheme),
];

class ChangeThemeBottomSheet extends StatelessWidget {
  const ChangeThemeBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 14),
          height: 5,
          width: 135,
          decoration: BoxDecoration(color: ThemeColors.colorD9D9D9),
        ),
        SizedBox(
          height: h * 15,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 29),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              "Change your current theme",
              style: tsS14w4009F9F9F,
            ),
          ),
        ),
        ListView.separated(
          shrinkWrap: true,
          padding: const EdgeInsets.symmetric(horizontal: 29, vertical: 20),
          itemBuilder: (context, index) {
            final provider = context.watch<ThemeProvider>();
            bool selected = themesList[index].type == provider.selectedTheme;
            TextStyle textStyle = tsS14w500Black;
            if (selected) {
              textStyle = tsS14w500c03AD9E;
            }
            return InkWell(
              onTap: () async {
                SharedPreferences prefs = await SharedPreferences.getInstance();
                provider.selectedTheme = themesList[index].type;
                prefs.setInt('theme', provider.selectedTheme.index);
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      themesList[index].title,
                      style: textStyle,
                    ),
                  ),
                  if (selected)
                    ImageIcon(
                      const AssetImage(
                        "assets/icons/tick.png",
                      ),
                      color: ThemeColors.colorF9637D,
                    )
                ],
              ),
            );
          },
          separatorBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(top: 8.0, bottom: 8),
              child: Divider(
                thickness: 1,
                color: ThemeColors.colorD9D9D9,
              ),
            );
          },
          itemCount: themesList.length,
        ),
        const SafeArea(child: SizedBox()),
      ],
    );
  }
}
