import 'package:e8_hr_portal/provider/log_time_provider.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../helper/button_widget.dart';
import '../../helper/textfield_widget.dart';
import '../../util/colors.dart';
import '../widgets/hisense_drop_down_tile.dart';
import '../widgets/type_ahead_form_field_widget.dart';

class AddNewTaskScreen extends StatelessWidget {
  static const route = 'add_new_task/';
  AddNewTaskScreen({super.key});
  final TextEditingController _projectController = TextEditingController();
  final TextEditingController _taskController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: 'Add New Task',
      body: Form(
        key: _formKey,
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(overscroll: false),
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: w * 16),
            children: [
              _taskTypeDropDownTile(),
              SizedBox(height: h * 15),
              _projectDropDownTile(),
              _taskDropDownTile(),
              _stageDropDownTile(),
              SizedBox(height: h * 15),
              _textFormField(
                  hintText: 'Describe Task',
                  controller: _descriptionController,
                  title: 'Task Description',
                  showTitle: true,
                  maxLines: 10,
                  validator: Validator.text),
              SizedBox(height: h * 15),
              if (MediaQuery.of(context).viewInsets.bottom > 0)
                Container(
                    padding: EdgeInsets.fromLTRB(w * 0, 0, w * 0, h * 28),
                    color: ThemeColors.colorF4F5FA,
                    child: Consumer<LogTimeProvider>(
                      builder: (context, provider, child) {
                        return ButtonWidget(
                          title: 'Start Task',
                          textStyle: tsS16w600cFFFFFF,
                          color: Theme.of(context).primaryColor,
                          onPressed: () async {
                            if (_formKey.currentState!.validate()) {
                              provider.createTask(
                                  context: context,
                                  description: _descriptionController.text,
                                  task: _taskController.text);
                            }
                          },
                        );
                      },
                    )),
            ],
          ),
        ),
      ),
      bottomNavigationBar: MediaQuery.of(context).viewInsets.bottom == 0
          ? BottomAppBar(
              color: Colors.transparent,
              elevation: 0,
              child: Container(
                  padding: EdgeInsets.fromLTRB(w * 16, 0, w * 16, h * 28),
                  color: ThemeColors.colorF4F5FA,
                  child: Consumer<LogTimeProvider>(
                    builder: (context, provider, child) {
                      return ButtonWidget(
                        title: 'Start Task',
                        textStyle: tsS16w600cFFFFFF,
                        color: Theme.of(context).primaryColor,
                        onPressed: () async {
                          if (_formKey.currentState!.validate()) {
                            provider.createTask(
                                context: context,
                                description: _descriptionController.text,
                                task: _taskController.text);
                          }
                        },
                      );
                    },
                  )),
            )
          : null,
    );
  }

  Widget _projectDropDownTile() {
    return Consumer<LogTimeProvider>(
      builder: (context, provider, _) {
        if (provider.selectedTaskType == '2') {
          return const SizedBox();
        }
        return Column(
          children: [
            Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      'Project',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFF30292F),
                        fontSize: 12 * f,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Text(
                      '*',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFFFA0000),
                        fontSize: 12 * f,
                        fontWeight: FontWeight.w400,
                      ),
                    )
                  ],
                ),
                SizedBox(height: h * 4),
              ],
            ),
            TypeAheadFormFieldWidget(
              controller: _projectController,
              hintText: 'Select Project',
              suffixIcon: provider.selectedProject != null
                  ? IconButton(
                      onPressed: () {
                        _projectController.clear();
                        provider.selectedProject = null;
                        provider.selectedTask = null;
                        provider.selectedStage = null;
                        _taskController.clear();
                        provider.stageList.clear();
                      },
                      icon: Icon(
                        Icons.close,
                        color: ThemeColors.color000000,
                      ),
                    )
                  : null,
              itemBuilder: (context, suggestion) {
                return ListTile(
                  title: Text(
                    suggestion.title ?? '',
                    style: tsS14w400454444,
                  ),
                );
              },
              labelStyle: tsS14w400454444,
              validator: (value) {
                return value == null || value.trim().isEmpty
                    ? 'Select a project'
                    : null;
              },
              onSelected: (suggestion) {
                _projectController.text = suggestion.title ?? '';
                provider.selectedProject = suggestion;
                provider.selectedTask = null;
                provider.selectedStage = null;
                _taskController.clear();
                provider.getExistingTasks();
                provider.getStages();
              },
              suggestionsCallback: (pattern) {
                return provider.projectList
                    .where((item) => item.title!
                        .toLowerCase()
                        .startsWith(pattern.toLowerCase()))
                    .toList();
              },
            ),
          ],
        );
      },
    );
  }

  Widget _taskTypeDropDownTile() {
    return Consumer<LogTimeProvider>(
      builder: (context, provider, _) {
        if (provider.taskTypes == null) {
          return const SizedBox();
        }
        return Column(
          children: [
            Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      'Task Type',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFF30292F),
                        fontSize: 12 * f,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Text(
                      '*',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFFFA0000),
                        fontSize: 12 * f,
                        fontWeight: FontWeight.w400,
                      ),
                    )
                  ],
                ),
                SizedBox(height: h * 4),
              ],
            ),
            HisenseDropdownTile(
                title: null,
                titleStyle: GoogleFonts.poppins(
                  color: const Color(0xFF30292F),
                  fontSize: 12 * f,
                  fontWeight: FontWeight.w400,
                ),
                hintText: 'Select Task Type',
                hintStyle: GoogleFonts.poppins(
                  fontSize: 12 * f,
                  fontWeight: FontWeight.w400,
                ),
                style: tsS14w400454444,
                errorStyle: tsS11w400cerrorColor,
                value: provider.selectedTaskType,
                contentPadding:
                    EdgeInsets.symmetric(vertical: h * 12, horizontal: w * 11),
                validator: (value) {
                  if (value == null) {
                    return 'Select a task type';
                  }
                  return null;
                },
                onChanged: (String? value) {
                  if (value != null) {
                    provider.selectedTaskType = value;
                  }
                  provider.getProjectTypes();
                  provider.selectedTask = null;
                  _taskController.clear();
                  if (value == '2') {
                    provider.getExistingTasks();
                  }
                },
                items: [
                  DropdownMenuItem(
                    value: '1',
                    child: Text(
                      provider.taskTypes?['1'],
                      style: tsS14w400454444,
                    ),
                  ),
                  DropdownMenuItem(
                    value: '2',
                    child: Text(
                      provider.taskTypes?['2'],
                      style: tsS14w400454444,
                    ),
                  )
                ]),
          ],
        );
      },
    );
  }

  Widget _textFormField({
    required TextEditingController controller,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    bool? enabled,
    String? hintText,
    String? errorText,
    bool showTitle = false,
    bool isMandatory = false,
    String? title,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (showTitle)
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    title ?? '',
                    style: GoogleFonts.poppins(
                      color: const Color(0xFF30292F),
                      fontSize: 12 * f,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  if (isMandatory)
                    Text(
                      '*',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFFFA0000),
                        fontSize: 12 * f,
                        fontWeight: FontWeight.w400,
                      ),
                    )
                ],
              ),
              SizedBox(height: h * 4),
            ],
          ),
        TextFieldWidget(
          enabled: enabled ?? true,
          controller: controller,
          textStyle: tsS14w400c30292F,
          hintStyle: GoogleFonts.poppins(
            fontSize: 12 * f,
            fontWeight: FontWeight.w400,
          ),
          hintText: hintText,
          error: errorText,
          errorStyle: tsS11w400cerrorColor,
          keyboardType: keyboardType,
          borderColor: ThemeColors.colorE3E3E3,

          contentPadding:
              EdgeInsets.symmetric(vertical: h * 14, horizontal: w * 11),
          border: OutlineInputBorder(
            borderSide: BorderSide(
              width: 1,
              color: ThemeColors.colorE3E3E3,
            ),
            borderRadius: BorderRadius.circular(5),
          ),
          // error: provider.loginErrorEmail,
          validator: validator,
          maxLines: maxLines,
        ),
      ],
    );
  }

  Widget _taskDropDownTile() {
    return Consumer<LogTimeProvider>(
      builder: (context, provider, _) {
        return Column(
          children: [
            Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      'Task',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFF30292F),
                        fontSize: 12 * f,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Text(
                      '*',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFFFA0000),
                        fontSize: 12 * f,
                        fontWeight: FontWeight.w400,
                      ),
                    )
                  ],
                ),
                SizedBox(height: h * 4),
              ],
            ),
            TypeAheadFormFieldWidget(
              controller: _taskController,
              hintText: 'Select or Enter Task',
              suffixIcon: provider.selectedTask != null
                  ? IconButton(
                      onPressed: () {
                        _taskController.clear();
                        provider.selectedTask = null;
                        provider.selectedStage = null;
                        provider.getStages();
                      },
                      icon: Icon(
                        Icons.close,
                        color: ThemeColors.color000000,
                      ),
                    )
                  : null,
              itemBuilder: (context, suggestion) {
                return ListTile(
                  title: Text(
                    suggestion.title ?? '',
                    style: tsS14w400454444,
                  ),
                );
              },
              labelStyle: tsS14w400454444,
              validator: (value) {
                return value == null || value.trim().isEmpty
                    ? 'Select a task'
                    : null;
              },
              onSelected: (suggestion) {
                _taskController.text = suggestion.title ?? '';
                provider.selectedTask = suggestion;
                provider.selectedStage = null;
                provider.getStages();
              },
              suggestionsCallback: (pattern) {
                return provider.taskList
                    .where((item) => item.title!
                        .toLowerCase()
                        .startsWith(pattern.toLowerCase()))
                    .toList();
              },
            ),
          ],
        );
      },
    );
  }

  Widget _stageDropDownTile() {
    return Consumer<LogTimeProvider>(
      builder: (context, provider, _) {
        if (provider.selectedTaskType == '2') {
          return const SizedBox();
        }
        return Column(
          children: [
            Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      'Stage',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFF30292F),
                        fontSize: 12 * f,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Text(
                      '*',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFFFA0000),
                        fontSize: 12 * f,
                        fontWeight: FontWeight.w400,
                      ),
                    )
                  ],
                ),
                SizedBox(height: h * 4),
              ],
            ),
            HisenseDropdownTile(
              title: null,
              titleStyle: GoogleFonts.poppins(
                color: const Color(0xFF30292F),
                fontSize: 12 * f,
                fontWeight: FontWeight.w400,
              ),
              hintText: 'Select Stage',
              hintStyle: GoogleFonts.poppins(
                fontSize: 12 * f,
                fontWeight: FontWeight.w400,
              ),
              style: tsS14w400454444,
              value: provider.selectedStage,
              errorStyle: tsS11w400cerrorColor,
              contentPadding:
                  EdgeInsets.symmetric(vertical: h * 12, horizontal: w * 11),
              validator: (value) {
                if (value == null) {
                  return 'Select a stage';
                }
                return null;
              },
              onChanged: (value) {
                if (value != null) {
                  provider.selectedStage = value;
                }
              },
              items: provider.stageList.map((item) {
                return DropdownMenuItem(
                  value: item,
                  child: Text(
                    item.title?.trimLeft() ?? '',
                    style: tsS14w400454444,
                  ),
                );
              }).toList(),
            ),
          ],
        );
      },
    );
  }
}
