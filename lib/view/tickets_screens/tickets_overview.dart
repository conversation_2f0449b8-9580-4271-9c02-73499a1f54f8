import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/model/tickets_overview.dart';
import 'package:e8_hr_portal/provider/tickets_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/other_screens/overview/widgets/cancel_button.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:provider/provider.dart';
import '../../util/page_navigator.dart';
import 'create_tickets.dart';

class TicketsOverview extends StatelessWidget {
  const TicketsOverview({super.key});
  @override
  Widget build(BuildContext context) {
    return Consumer<TicketsProvider>(
      builder: (context, provider, child) {
        TicketOverviewModel? ticketOverview = provider.ticketOverview;
        String? ticketsID = ticketOverview?.ticketId;
        String? date = ticketOverview?.createdAt;
        String? subject = ticketOverview?.subject;
        String? description = ticketOverview?.description;
        int? ticketId = ticketOverview?.id;
        String? status = ticketOverview?.status;
        String? comment = ticketOverview?.comment;
        String? receiver = ticketOverview?.receiver;
        String? image = ticketOverview?.image;
        String? replyDate = ticketOverview?.replyDate;
        final Divider divider = Divider(
          color: ThemeColors.colorD9D9D9,
          thickness: 1,
        );

        return CustomScaffold(
            screenTitle: "IT Help Desk",
            actions: [
              if (status == "pending")
                _editButton(
                    provider: provider,
                    context: context,
                    description: description ?? "",
                    subject: subject ?? "",
                    ticketId: ticketId ?? 0,
                    createdAt: date ?? "",
                    ticketsID: ticketsID ?? "",
                    image: image)
            ],
            body: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          // height: h * 254,
                          width: w * 343,
                          margin: EdgeInsets.only(top: h * 36),
                          padding: EdgeInsets.fromLTRB(
                              w * 10, h * 20, w * 10, h * 16),
                          decoration: BoxDecoration(
                            color: ThemeColors.colorFFFFFF,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                // mainAxisAlignment: MainAxisAlignment.,
                                // crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    height: h * 48,
                                    width: w * 48,
                                    alignment: Alignment.center,
                                    padding: const EdgeInsets.all(14.15),
                                    margin: EdgeInsets.only(right: w * 10),
                                    decoration: const BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Color.fromRGBO(3, 173, 158, 0.1),
                                    ),
                                    child: Image.asset(
                                      "assets/icons/tickets.png",
                                      // height: h * 19.71,
                                      // width: w * 17.88,
                                    ),
                                  ),
                                  Expanded(
                                    child: Text(
                                      subject ?? "",
                                      style: tsS14w500c2C2D33,
                                    ),
                                  ),
                                  Align(
                                      alignment: Alignment.topRight,
                                      heightFactor: 2.9,
                                      child:
                                          _status(status: status.toString())),
                                ],
                              ),
                              SizedBox(height: h * 15),
                              _detailsTail(
                                  title: "Date", desciption: date ?? ""),
                              divider,
                              _detailsTail(
                                  title: "Ticket ID",
                                  desciption: ticketsID ?? ""),
                              divider,
                              _detailsTail(
                                  title: "Subject",
                                  desciption: description ?? ""),
                              if (image != null) divider,
                              if (image != null)
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    _detailsTail(
                                        title: "Document",
                                        desciption: "Document"),
                                    InkWell(
                                      onTap: () {
                                        Navigator.of(context).push(
                                            MaterialPageRoute(
                                                builder: (context) =>
                                                    ViewTicketImageUrl(
                                                      image: image,
                                                    )));
                                      },
                                      child: Row(
                                        children: [
                                          Text(
                                            "View",
                                            style: GoogleFonts.poppins(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w500,
                                                color: ThemeColors.primaryColor,
                                                decoration:
                                                    TextDecoration.underline),
                                          ),
                                          SizedBox(width: w * 2),
                                          Image.asset(
                                            "assets/icons/download_icon.png",
                                            height: h * 11.67,
                                            width: w * 11.67,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),
                        if (status != null &&
                            (status.toLowerCase() == "replied" ||
                                status.toLowerCase() == "closed") &&
                            comment != "")
                          _ticketApprovedRejectedCard(
                              comment: comment ?? "",
                              rejectedBy: receiver ?? "",
                              status: status,
                              replyDate: replyDate ?? ""),
                        // const Spacer(),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: h * 100),
              ],
            ),
            floatingActionButton:
                status != null && status.toLowerCase() != "closed"
                    ? _cancelButton(context: context, ticketsID: ticketId!)
                    : null);
      },
    );
  }

  Widget _detailsTail({required String title, required String desciption}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: tsS12w400c949494,
        ),
        SizedBox(height: h * 3),
        Text(
          desciption,
          style: tsS14w500c2C2D33,
        ),
        SizedBox(height: h * 8),
      ],
    );
  }

  Widget _editButton(
      {required BuildContext context,
      required int ticketId,
      required TicketsProvider provider,
      required String ticketsID,
      required String description,
      required String createdAt,
      required String? image,
      required String subject}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () {
          provider.alreadySelectedImage = image;
          provider.isImageRemoved = false;
          provider.imageFile = null;
          PageNavigator.push(
            context: context,
            route: CreateTickets(
                isForEdit: true,
                ticketId: ticketId,
                ticketsID: ticketsID,
                description: description,
                date: createdAt,
                subject: subject),
          );
        },
        child: Align(
          alignment: Alignment.center,
          child: Padding(
            padding: EdgeInsets.only(left: w * 20, right: w * 20, top: h * 5),
            child: Text(
              "Edit",
              style: tsS14w500cFFFFFF,
            ),
          ),
        ),
      ),
    );
  }
}

Widget _status({required String status}) {
  Color backGround = ThemeColors.color32936F.withOpacity(0.16);
  TextStyle textStyle = tsS12w600c949494;
  String statusText = "Pending";
  switch (status.toLowerCase().trim()) {
    case "closed":
      backGround = ThemeColors.color32936F.withOpacity(0.16);
      textStyle = tsS12w600c949494;
      statusText = "Closed";
      break;
    case "pending":
      backGround = const Color(0xffFFF2E2);
      textStyle = tsS12w600E5B900;
      statusText = "Pending";
      break;
    case 'replied':
      backGround = ThemeColors.color570DAB.withOpacity(0.15);
      textStyle = tsS12w5570F1;
      statusText = "Replied";
  }
  // ignore: prefer_interpolation_to_compose_strings

  return Container(
    height: h * 23,
    alignment: Alignment.center,
    padding: EdgeInsets.symmetric(horizontal: w * 11),
    decoration: BoxDecoration(
      color: backGround,
      borderRadius: BorderRadius.circular(8),
    ),
    child: Text(statusText, style: textStyle),
  );
}

Widget _cancelButton({required BuildContext context, required int ticketsID}) {
  return Padding(
      padding: const EdgeInsets.fromLTRB(1, 1, 1, 1),
      child: CancelButton(
        height: h * 56,
        width: w * 343,
        title: 'Delete Ticket',
        textStyle: tsS18w600cFFFFFF,
        onPressed: () async {
          showModalBottomSheet(
            context: context,
            isDismissible: true,
            backgroundColor: ThemeColors.colorFFFFFF,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            builder: (context) {
              return _showModalBottomSheet(
                  context: context, ticketsID: ticketsID);
            },
          );
        },
      )
      // : const SizedBox(),
      );
}

Widget _showModalBottomSheet(
    {required BuildContext context, required int ticketsID}) {
  return Container(
    height: h * 249,
    decoration: BoxDecoration(
      // color: ThemeColors.colorFFFFFF,
      borderRadius: BorderRadius.circular(12),
    ),
    child: Column(
      children: [
        SizedBox(height: h * 14),
        Container(
          height: h * 5,
          width: w * 134,
          decoration: BoxDecoration(
            color: ThemeColors.colorEAEBED,
            borderRadius: BorderRadius.circular(100),
          ),
        ),
        SizedBox(height: h * 44),
        Text("Delete ticket", style: tsS26w500cFFFFFF),
        SizedBox(height: h * 2),
        Text("Are you sure ? ", style: tsS16w500c9F9F9F),
        SizedBox(height: h * 33),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: w * 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GeneralButton(
                height: h * 50,
                width: w * 164,
                isDisabledColor: true,
                title: "No",
                textStyle: tsS18w600cFFFFFF,
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              GeneralButton(
                height: h * 50,
                width: w * 164,
                title: "Yes",
                textStyle: tsS18w600cFFFFFF,
                // color: ThemeColors.secondaryColor,
                onPressed: () async {
                  EasyLoading.show();
                  final navigator = Navigator.of(context);

                  final provider =
                      Provider.of<TicketsProvider>(context, listen: false);
                  await provider.deleteTicket(ticketID: ticketsID);
                  // if (isPop) {
                  navigator.pop();
                  navigator.pop();
                  // }
                  EasyLoading.dismiss();
                },
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

Widget _ticketApprovedRejectedCard(
    {required String rejectedBy,
    required String comment,
    required String status,
    required String replyDate}) {
  return Column(
    children: [
      const SizedBox(height: 10),
      Container(
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(12)),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 24,
                  width: 24,
                  margin: const EdgeInsets.only(right: 10),
                  padding: const EdgeInsets.all(5),
                  decoration: BoxDecoration(
                      color: status == "replied"
                          ? ThemeColors.colorFCD2D0
                          : ThemeColors.color32936F.withOpacity(0.16),
                      borderRadius: BorderRadius.circular(6)),
                  child: Container(
                    decoration: BoxDecoration(
                        color: status == "replied"
                            ? ThemeColors.colorF64D44
                            : ThemeColors.color519C66,
                        shape: BoxShape.circle),
                    child: const Center(
                      child: Icon(
                        Icons.close_rounded,
                        color: Colors.white,
                        size: 10,
                      ),
                    ),
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        "Ticket Comment",
                        style: status == "replied"
                            ? tsS12w4c5570F1
                            : tsS12w400c519C66,
                      ),
                    ),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        rejectedBy,
                        style: tsS10w400c646363,
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  width: 39 * w,
                ),
                Align(
                  alignment: Alignment.topCenter,
                  child: Text(
                    replyDate,
                    style: tsS10w400c646363,
                  ),
                ),
              ],
            ),
            Divider(
              thickness: 1,
              color: ThemeColors.colorD9D9D9,
            ),
            const SizedBox(height: 12),
            Align(
              alignment: Alignment.topLeft,
              child: Text(
                comment,
                style: tsS14w400979797,
              ),
            )
          ],
        ),
      ),
    ],
  );
}

class ViewTicketImageUrl extends StatelessWidget {
  final String image;
  const ViewTicketImageUrl({super.key, required this.image});

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
        screenTitle: "",
        body: Center(
          child: Image.network(image),
        ));
  }
}
