// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';

import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/tickets_screens/create_tickets.dart';
import 'package:e8_hr_portal/view/tickets_screens/tickets_overview.dart';

import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';

import '../../model/user_tickets_list.dart';
import '../../provider/tickets_provider.dart';
import '../../util/dailoge.dart';

class Tickets extends StatefulWidget {
  const Tickets({super.key});

  @override
  State<Tickets> createState() => _TicketsState();
}

class _TicketsState extends State<Tickets> {
  @override
  void initState() {
    Provider.of<TicketsProvider>(context, listen: false).init();
    super.initState();
  }

  @override
  void dispose() {
    Provider.of<TicketsProvider>(context, listen: false)
        .pagingController
        .dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: "IT Help Desk",
      actions: [_createTicketButton(context: context)],
      onTap: () {},
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16 * w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: h * 32),
            Text(
              "My IT Support Requests",
              style: tsS18w500c161616,
            ),
            Expanded(
              child: Consumer<TicketsProvider>(
                builder: (context, provider, _) {
                  // if (provider.ticketList == null ||
                  //     provider.ticketList!.isEmpty) {

                  // }
                  return PagedListView.separated(
                    padding: EdgeInsets.only(top: 25 * h, bottom: 45 * h),
                    pagingController: provider.pagingController,
                    builderDelegate: PagedChildBuilderDelegate<UserTicketsList>(
                      noItemsFoundIndicatorBuilder: (context) {
                        return const Center(
                          child: Text("No Data Found"),
                        );
                      },
                      newPageProgressIndicatorBuilder: (_) {
                        return Center(
                          child: CircularProgressIndicator(
                              color: ThemeColors.color06AA37),
                        );
                      },
                      firstPageProgressIndicatorBuilder: (_) {
                        return Center(
                          child: CircularProgressIndicator(
                              color: ThemeColors.primaryColor),
                        );
                      },
                      itemBuilder: (context, item, index) {
                        // String status = index == 0 ? "Approved" : "Rejected";
                        // String assignee = "HR Manager";
                        // String date = "23 Dec 2022";
                        // String requestType = "Sick Leave Request";
                        // String time = "1 min";
                        return _requesTile(
                          provider: provider,
                          data: item,
                          context: context,
                        );
                      },
                    ),
                    separatorBuilder: (context, index) {
                      return SizedBox(height: h * 10);
                    },
                  );

                  // ListView.separated(
                  //   shrinkWrap: true,
                  //   physics: const BouncingScrollPhysics(),
                  //   padding: EdgeInsets.only(top: h * 15, bottom: h * 90),
                  //   itemCount: provider.ticketList!.length,
                  //   itemBuilder: (context, index) {
                  //     return Consumer<TicketsProvider>(
                  //       builder: (context, provider, _) {
                  //         return _requesTile(
                  //           provider: provider,
                  //           data: provider.ticketList![index],
                  //           context: context,
                  //         );
                  //       },
                  //     );
                  //   },
                  //   separatorBuilder: (context, index) =>
                  //       SizedBox(height: h * 10),
                  // );
                },
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _createTicketButton({required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () async {
          TicketsProvider provider =
              Provider.of<TicketsProvider>(context, listen: false);
          provider.imageFile = null;
          provider.alreadySelectedImage = null;

          bool isGo = await provider.generateTicketsID();

          if (isGo && !provider.clicked) {
            provider.clicked = true;
            PageNavigator.pushSlideRight(
              context: context,
              route: const CreateTickets(),
            );
          } else {
            showToastText("Couldn't create token now");
          }
        },
        child: Align(
          alignment: Alignment.center,
          child: Padding(
            padding: EdgeInsets.only(left: w * 10, right: w * 10, top: h * 5),
            child: Text(
              "Create Request",
              style: tsS14w500cFFFFFF,
            ),
          ),
        ),
      ),
    );
  }

  Widget _requesTile({
    required TicketsProvider provider,
    required BuildContext context,
    required UserTicketsList data,
  }) {
    int? tickID = data.id;
    String? date = data.createdAt;
    String? ticketId = data.ticketId;
    String? title = data.subject;
    String? status = data.status;

    return GestureDetector(
      onTap: () async {
        TicketsProvider provider =
            Provider.of<TicketsProvider>(context, listen: false);
        if (tickID != null) {
          await provider.getTicketOverview(ticketID: tickID);
          PageNavigator.pushSlideRight(
            context: context,
            route: const TicketsOverview(),
          );
        }
      },
      child: Container(
        key: UniqueKey(),
        // height: h * 80,
        width: w * 343,
        padding: EdgeInsets.fromLTRB(w * 10, h * 10, w * 10, h * 10),
        decoration: BoxDecoration(
          color: ThemeColors.colorFFFFFF,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Slidable(
          closeOnScroll: true,
          endActionPane: status?.toLowerCase() == "closed"
              ? null
              : ActionPane(
                  motion: const ScrollMotion(),
                  children: [
                    InkWell(
                      onTap: () {
                        provider.deleteTicket(ticketID: tickID!);
                      },
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 30),
                        alignment: Alignment.center,
                        height: 40 * h,
                        width: 40 * w,
                        decoration: const BoxDecoration(
                            shape: BoxShape.circle, color: Colors.red),
                        child: const Center(
                            child: Icon(
                          Icons.delete_outline_rounded,
                          color: Colors.white,
                        )),
                      ),
                    ),
                  ],
                ),
          child: Row(
            children: [
              Container(
                height: h * 48,
                width: w * 48,
                alignment: Alignment.center,
                padding: const EdgeInsets.all(14.15),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: ThemeColors.colorF8F8F8,
                ),
                child: Image.asset(
                  "assets/icons/tickets.png",
                  color: ThemeColors.primaryColor,
                  // height: h * 19.71,
                  // width: w * 17.88,
                ),
              ),
              SizedBox(width: w * 11),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title ?? "",
                      style: tsS14w500c161616,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: h * 2),
                    Text(
                      "Ticket Id : $ticketId • ${date ?? ""}",
                      style: tsS12w400c979797,
                    )
                  ],
                ),
              ),
              Align(
                alignment: Alignment.topRight,
                heightFactor: 2.9,
                child: _status(status: status!),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Widget _status({required String status}) {
  Color backGround = ThemeColors.color32936F.withOpacity(0.16);
  TextStyle textStyle = tsS12w600c949494;
  String statusText = "Pending";
  switch (status.toLowerCase().trim()) {
    case "closed":
      backGround = ThemeColors.color32936F.withOpacity(0.16);
      textStyle = tsS12w600c949494;
      statusText = "Closed";
      break;
    case "pending":
      backGround = const Color(0xffFFF2E2);
      textStyle = tsS12w600E5B900;
      statusText = "Pending";
      break;
    case 'replied':
      backGround = ThemeColors.color570DAB.withOpacity(0.15);
      textStyle = tsS12w5570F1;
      statusText = "Replied";
      break;
  }

  return Container(
    height: h * 23,
    alignment: Alignment.center,
    padding: EdgeInsets.symmetric(horizontal: w * 11),
    decoration: BoxDecoration(
      color: backGround,
      borderRadius: BorderRadius.circular(8),
    ),
    child: Text(statusText, style: textStyle),
  );
}
