import 'dart:io';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/provider/tickets_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/tickets_screens/tickets_overview.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:provider/provider.dart';

class CreateTickets extends StatefulWidget {
  final bool isForEdit;
  final int? ticketId;
  final String? ticketsID;
  final String? subject;
  final String? description;
  final String? date;
  const CreateTickets(
      {this.ticketId,
      this.ticketsID,
      this.isForEdit = false,
      this.description,
      this.subject,
      this.date,
      super.key});

  @override
  State<CreateTickets> createState() => _CreateTicketsState();
}

class _CreateTicketsState extends State<CreateTickets> {
  final TextEditingController _ticketSubjectController =
      TextEditingController();
  final TextEditingController _ticketDescriptionController =
      TextEditingController();
  final _ticketFormKey = GlobalKey<FormState>();
  String? ticketsId;
  String? date;
  @override
  void initState() {
    init();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final TicketsProvider ticketPro = Provider.of<TicketsProvider>(context);
    return HisenseScaffold(
      screenTitle: widget.isForEdit ? "Edit Request" : "Create Request",
      onTap: () {
        ticketPro.clicked = false;
      },
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16 * w),
        child: Form(
          key: _ticketFormKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: h * 32),
                      Text(
                        widget.isForEdit ? "Edit Request" : "Create Request",
                        style: tsS18w500c161616,
                      ),
                      SizedBox(height: h * 15),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _disabledTextField(
                              title: "Request Id", subject: ticketsId ?? ""),
                          _disabledTextField(
                              title: "Date", subject: date ?? ""),
                        ],
                      ),
                      _textFormFields(
                          controller: _ticketSubjectController,
                          title: "Request Subject",
                          maxLines: null),
                      _textFormFields(
                          controller: _ticketDescriptionController,
                          title: "Description",
                          maxLines: 5,
                          labelText: "Write something..."),
                      SizedBox(height: h * 15),
                      Text(
                        "Upload Documents",
                        style: tsS14w400c30292F,
                      ),
                      SizedBox(height: h * 5),
                      _uploadYourDocumentWidget(context: context),
                    ],
                  ),
                ),
              ),
              const SizedBox(
                height: 15,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _cancelButton(),
                  GeneralButton(
                      title: "Submit",
                      onPressed: () async {
                        final navigator = Navigator.of(context);
                        if (_ticketFormKey.currentState!.validate()) {
                          if (!widget.isForEdit) {
                            TicketsProvider provider =
                                Provider.of<TicketsProvider>(context,
                                    listen: false);

                            EasyLoading.show();
                            provider.clicked = false;
                            bool isPop = await provider.createTicket(
                                date: date ?? "",
                                description: _ticketDescriptionController.text,
                                subject: _ticketSubjectController.text);
                            EasyLoading.dismiss();
                            if (isPop) {
                              navigator.pop();
                            }
                          } else if (widget.isForEdit &&
                              widget.ticketId != null) {
                            //editAndupdateTicket
                            TicketsProvider provider =
                                Provider.of<TicketsProvider>(context,
                                    listen: false);
                            EasyLoading.show();
                            bool isPop = await provider.editAndupdateTicket(
                                date: date ?? "",
                                ticketId: widget.ticketId!,
                                description: _ticketDescriptionController.text,
                                subject: _ticketSubjectController.text);
                            EasyLoading.dismiss();
                            if (isPop) {
                              navigator.pop();
                              navigator.pop();
                            }
                          }
                        }
                      },
                      isDisabled: false,
                      textStyle: tsS18w600cFFFFFF,
                      height: h * 50,
                      width: w * 164),
                ],
              ),
              SizedBox(height: h * 50),
            ],
          ),
        ),
      ),
    );
  }

  Widget _cancelButton() {
    return ElevatedButton(
      onPressed: () {
        Provider.of<TicketsProvider>(context, listen: false).clicked = false;
        Navigator.pop(context);
      },
      style: ElevatedButton.styleFrom(
        elevation: 0,
        backgroundColor: ThemeColors.disabledButtonColor,
        disabledBackgroundColor: Colors.transparent,
        shadowColor: Colors.transparent,
        minimumSize: Size(w * 164, h * 50),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(50),
        ),
      ),
      child: Text(
        "Cancel",
        style: tsS18w600cFFFFFF,
      ),
    );
  }

  Widget _disabledTextField({required String title, required String subject}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: tsS14w400c30292F),
        SizedBox(height: h * 4),
        Container(
          height: h * 45,
          width: w * 164,
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.only(left: w * 10),
          decoration: BoxDecoration(
            color: ThemeColors.colorE3E1E1,
            borderRadius: BorderRadius.circular(5),
            border: Border.all(color: ThemeColors.colorE3E3E3, width: 1),
          ),
          child: Text(
            subject,
            style: tsS14w400454444,
          ),
        ),
      ],
    );
  }

  Widget _textFormFields(
      {required String title,
      String? labelText,
      int? maxLines,
      required TextEditingController controller}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: h * 15),
        Text(
          title,
          style: tsS14w400c30292F,
        ),
        SizedBox(height: h * 4),
        TextFieldWidget(
          controller: controller,
          hintStyle: tsS12w400c9F9F9F,
          textStyle: tsS14w400c30292F,
          keyboardType: TextInputType.text,
          maxLines: maxLines,
          validator: Validator.text,
          hintText: labelText,
          contentPadding:
              EdgeInsets.symmetric(vertical: h * 12, horizontal: w * 10),
          textCapitalization: TextCapitalization.sentences,
        ),
      ],
    );
  }

  void init() {
    TicketsProvider provider =
        Provider.of<TicketsProvider>(context, listen: false);
    ticketsId = provider.ticketsId;
    var dateTime = DateTime.now();

    date = formatDateFromDate(dateTime: dateTime, format: "dd MMM yyyy");
    if (widget.isForEdit) {
      if (widget.description != null || widget.description!.isNotEmpty) {
        _ticketDescriptionController.text = widget.description!;
      }
      if (widget.subject != null || widget.subject!.isNotEmpty) {
        _ticketSubjectController.text = widget.subject!;
      }
      if (widget.date != null || widget.date!.isNotEmpty) {
        date = widget.date!;
      }
      if (widget.ticketId != null) {
        ticketsId = widget.ticketsID.toString();
      }
    }
  }
}

Widget _uploadYourDocumentWidget({required BuildContext context}) {
  return Consumer<TicketsProvider>(
    builder: (context, provider, child) {
      return DottedBorder(
        dashPattern: [w * 6, w * 7],
        color: ThemeColors.colorD9D9D9,

        borderType: BorderType.RRect,
        radius: const Radius.circular(3),
        // padding: EdgeInsets.all(6),
        child: GestureDetector(
            onTap: () {
              if (provider.imageFile == null &&
                  provider.alreadySelectedImage == null) {
                showDialog(
                  context: context,
                  builder: (context) => TicketDocUploadDialog(ctx: context),
                );
              }
            },
            child: ClipRRect(
              borderRadius: const BorderRadius.all(
                Radius.circular(3),
              ),
              child: Container(
                width: w * 343,
                color: ThemeColors.colorFFFFFF,
                alignment: Alignment.center,
                padding: EdgeInsets.only(top: h * 30, bottom: h * 22),
                child: Column(
                  children: [
                    if (provider.imageFile == null &&
                        provider.alreadySelectedImage == null)
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Image.asset("assets/icons/upload.png",
                              height: h * 24, width: w * 29.34),
                          SizedBox(height: h * 8),
                          Text(
                            "Click to upload your document",
                            style: tsS14w400c30292F,
                          ),
                          SizedBox(height: h * 3),
                          Text(
                            "Supports :JPG, JPEG, PNG with max size of 10 MB",
                            style: tsS12w400979797,
                          ),
                          // Text(
                          //   "Supports : JPEG, PNG",
                          //   style: tsS12w400979797,
                          // ),
                        ],
                      )
                    else if (provider.imageFile != null)
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (context) => const ViewTicketImage()));
                        },
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                              border: Border.all(color: Colors.black),
                              borderRadius: BorderRadius.circular(8)),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Text(
                                  "${provider.imageFile?.name}",
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              InkWell(
                                onTap: () async {
                                  provider.imageFile = null;
                                },
                                child: const Icon(
                                  Icons.delete_outline_sharp,
                                  color: Colors.black,
                                ),
                              )
                            ],
                          ),
                        ),
                      )
                    else if (provider.alreadySelectedImage != null)
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (context) => ViewTicketImageUrl(
                                  image: provider.alreadySelectedImage!)));
                        },
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                              border: Border.all(color: Colors.black),
                              borderRadius: BorderRadius.circular(8)),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Text(
                                  "${provider.alreadySelectedImage}",
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              InkWell(
                                onTap: () async {
                                  provider.alreadySelectedImage = null;
                                },
                                child: const Icon(
                                  Icons.delete_outline_sharp,
                                  color: Colors.black,
                                ),
                              )
                            ],
                          ),
                        ),
                      )
                    //   },
                    // ),
                  ],
                ),
              ),
            )),
      );
    },
  );
}

class TicketDocUploadDialog extends StatelessWidget {
  final BuildContext ctx;
  const TicketDocUploadDialog({super.key, required this.ctx});

  @override
  Widget build(BuildContext context) {
    TicketsProvider provider =
        Provider.of<TicketsProvider>(context, listen: false);
    return AlertDialog(
      title: const Text("Options"),
      content: SingleChildScrollView(
        child: ListBody(
          children: [
            GestureDetector(
              child: const Text("Capture image from camera"),
              onTap: () async {
                await provider.openCamera(context);
              },
            ),
            const Padding(
              padding: EdgeInsets.all(15),
            ),
            GestureDetector(
              child: const Text("Upload an image from gallery"),
              onTap: () async {
                Navigator.pop(context);
                await provider.openGallery();
              },
            ),
            const Padding(
              padding: EdgeInsets.all(10),
            ),
          ],
        ),
      ),
    );
  }
}

class ViewTicketImage extends StatelessWidget {
  const ViewTicketImage({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "",
      body: Consumer<TicketsProvider>(
        builder: (context, provider, _) {
          return provider.imageFile != null
              ? Center(
                  child: Image.file(
                    File(provider.imageFile!.path),
                  ),
                )
              : const SizedBox();
        },
      ),
    );
  }
}
