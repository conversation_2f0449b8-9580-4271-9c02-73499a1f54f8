import 'package:flutter/material.dart';

class CertificatesAndLetters extends StatelessWidget {
  const CertificatesAndLetters({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: InkWell(
          onTap: () => Navigator.pop(context),
          child: const Icon(
            Icons.arrow_back_ios,
            color: Colors.white,
          ),
        ),
        title: const Text("Certificates & Letters"),
      ),
      body: const Center(child: Text("coming soon")),
    );
  }
}
