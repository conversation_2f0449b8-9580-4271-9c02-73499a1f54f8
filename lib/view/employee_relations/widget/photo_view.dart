import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/provider/medical_insurance_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:photo_view/photo_view.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:provider/provider.dart';
// import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';

class PhotoViewScreen extends StatefulWidget {
  final String? image;
  final String? extension;
  const PhotoViewScreen(
      {super.key, required this.image, required this.extension});
  @override
  State<PhotoViewScreen> createState() => _PhotoViewScreenState();
}

class _PhotoViewScreenState extends State<PhotoViewScreen> {
  final Completer<PDFViewController> _controller =
      Completer<PDFViewController>();
  int? pages = 0;
  int? currentPage = 0;
  String errorMessage = '';
  String remotePDFpath = "";

  @override
  void initState() {
    var provider =
        Provider.of<MedicalInsuranceProvider>(context, listen: false);
    if (widget.extension == "pdf") {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
        await provider.createFileOfPdfUrl(widget.image).then((f) {
          setState(() {
            remotePDFpath = f.path;
          });
        });
      });
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "",
      horizontalPadding: 0,
      body: Column(
        children: [
          if ((widget.extension == "png" ||
                  widget.extension == "jpg" ||
                  widget.extension == "jpeg") &&
              widget.image != null &&
              widget.image != "")
            Expanded(
              child: PhotoView(
                imageProvider: CachedNetworkImageProvider(
                  widget.image.toString(),
                ),
              ),
            ),
          if (widget.extension == "pdf" && remotePDFpath != "")
            Expanded(
              child: Consumer<MedicalInsuranceProvider>(
                builder: (context, provider, _) {
                  return PDFView(
                    filePath: remotePDFpath,
                    enableSwipe: true,
                    nightMode: true,
                    swipeHorizontal: true,
                    autoSpacing: false,
                    pageFling: true,
                    pageSnap: true,
                    defaultPage: provider.currentPage,
                    fitPolicy: FitPolicy.BOTH,
                    preventLinkNavigation: true,
                    onRender: (pages) {
                      if (pages != null) {
                        provider.pages = pages;
                      }
                    },
                    onPageChanged: (int? page, int? total) {
                      if (page != null) {
                        provider.currentPage = page;
                      }
                    },
                    onError: (error) {
                      provider.errorMessage = error.toString();
                    },
                    onPageError: (page, error) {
                      provider.errorMessage = '$page: ${error.toString()}';
                    },
                    onViewCreated: (PDFViewController pdfViewController) {
                      _controller.complete(pdfViewController);
                    },
                    onLinkHandler: (String? uri) {},
                  );
                },
              ),
            ),
          if (remotePDFpath == "" && widget.image == null)
            LinearProgressIndicator(
              color: ThemeColors.colorF64D44.withOpacity(.75),
            ),
        ],
      ),
    );
  }
}
