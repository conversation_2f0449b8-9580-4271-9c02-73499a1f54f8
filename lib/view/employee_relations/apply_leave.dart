// import 'package:flutter/material.dart';

// class ApplyLeaveScreen extends StatelessWidget {
//   const ApplyLeaveScreen({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         leading: InkWell(
//           onTap: () => Navigator.pop(context),
//           child: const Icon(
//             Icons.arrow_back_ios,
//             color: Colors.white,
//           ),
//         ),
//         title: const Text("Apply Leave"),
//       ),
//       body: const Center(child: Text("coming soon")),
//     );
//   }
// }
