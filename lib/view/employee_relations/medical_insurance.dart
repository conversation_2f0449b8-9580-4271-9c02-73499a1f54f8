import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/model/download_url_model.dart';
import 'package:e8_hr_portal/provider/medical_insurance_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:provider/provider.dart';

import '../../provider/general_provider.dart';
import '../widgets/download_dialog.dart';

class MedicalInsuranceScreen extends StatelessWidget {
  const MedicalInsuranceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final MedicalInsuranceProvider insuranceProvider =
        Provider.of<MedicalInsuranceProvider>(context);
    return CustomScaffold(
      screenTitle: "Medical Insurance",
      body: insuranceProvider.medicalInsuranceCardModel?.data?.id == null
          ? const Center(
              child: Text("No data"),
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              // mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (insuranceProvider.medicalInsuranceCardModel?.data != null)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: h * 32),
                      Text("Insurance card", style: tsS18w500c181818),
                      SizedBox(height: h * 16),
                      Container(
                        height: h * 219,
                        width: w * 343,
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          // boxShadow: const [
                          //   BoxShadow(
                          //       offset: Offset(2, 2),
                          //       blurRadius: 10,
                          //       spreadRadius: 0.2,
                          //       color: Color.fromRGBO(0, 0, 0, 0.16))
                          // ],
                        ),
                        // child: Column(
                        //   children: [
                        //     // DottedBorder(
                        //     //     dashPattern: const [4, 4, 4, 4],
                        //     //     borderType: BorderType.RRect,
                        //     //     color: Colors.black,
                        //     //     radius: const Radius.circular(12),
                        //     //     padding: const EdgeInsets.symmetric(
                        //     //       vertical: 10,
                        //     //     ),
                        //     //     child: Column(
                        //     //       children: [
                        //     //         Padding(
                        //     //           padding: const EdgeInsets.all(8.0),
                        //     //           child: Row(
                        //     //             children: [
                        //     //               Row(
                        //     //                 children: [
                        //     //                   Container(
                        //     //                     height: 100,
                        //     //                     width: 100,
                        //     //                     decoration: BoxDecoration(
                        //     //                       color: ThemeColors.primaryColor,
                        //     //                       shape: BoxShape.circle,
                        //     //                     ),
                        //     //                     child: Image.asset(
                        //     //                       "assets/icons/Hisense-Logo.png",
                        //     //                       color: Colors.white,
                        //     //                       scale: 1.6,
                        //     //                     ),
                        //     //                   ),

                        //     //                   Align(
                        //     //                     alignment: Alignment.centerLeft,
                        //     //                     child: Text("FROM"),
                        //     //                   )
                        //     //                 ],
                        //     //               )

                        //     //             ],
                        //     //           ),
                        //     //         )
                        //     //       ],
                        //     //     )),
                        //     /////////////////////

                        //     // Image.asset("assets/images/insurance.png"),
                        //   ],
                        // ),

                        child: GestureDetector(
                          onTap: () async {
                            String file = insuranceProvider
                                .medicalInsuranceCardModel!.data!.file
                                .toString();
                            GeneralProvider generalProvider =
                                Provider.of<GeneralProvider>(context,
                                    listen: false);
                            if (Platform.isIOS) {
                              await generalProvider.launchUniversalLinkIos(
                                  url: file);
                            } else if (Platform.isAndroid) {
                              await generalProvider.launchInBrowser(url: file);
                            }
                          },
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              insuranceProvider
                                  .medicalInsuranceCardModel!.data!.file
                                  .toString(),
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                if (insuranceProvider.medicalInsuranceCardModel?.data != null)
                  SizedBox(height: h * 20),
                if (insuranceProvider.medicalInsuranceCardModel?.data == null)
                  SizedBox(height: h * 32),
                if (insuranceProvider.downloadUrlModel?.data != null)
                  Text("Documents", style: tsS18w500c181818),
                if (insuranceProvider.downloadUrlModel?.data != null)
                  Expanded(
                    child: ListView.separated(
                      padding: EdgeInsets.only(top: h * 16, bottom: h * 90),
                      physics: const ClampingScrollPhysics(),
                      shrinkWrap: true,
                      itemCount:
                          insuranceProvider.downloadUrlModel!.data!.length,
                      itemBuilder: (context, index) {
                        return _docTile(
                            element: insuranceProvider
                                .downloadUrlModel!.data![index],
                            context: context);
                      },
                      separatorBuilder: (context, index) {
                        return SizedBox(height: h * 10);
                      },
                    ),
                  ),
              ],
            ),
    );
  }

  Widget _docTile({required Data element, required BuildContext context}) {
    return Container(
      padding: EdgeInsets.fromLTRB(w * 10, h * 10, w * 4, h * 10),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: ThemeColors.colorFFFFFF),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                height: h * 30,
                width: w * 30,
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: ThemeColors.colorFCC400.withOpacity(0.1),
                    shape: BoxShape.circle),
                child: Image.asset(
                  "assets/icons/documents.png",
                ),
              ),
              SizedBox(width: w * 10),
              Container(
                width: 200 * w,
                alignment: Alignment.centerLeft,
                child: Text(
                  element.name ?? "",
                  overflow: TextOverflow.ellipsis,
                  style: tsS14w500c181818,
                  softWrap: false,
                ),
              ),
            ],
          ),
          TextButton(
            onPressed: () async {
              if (element.id != null) {
                if (Platform.isAndroid) {
                  showDialog(
                    context: context,
                    builder: (context) => DownloadingDialog(
                        fileName: "Elment8 Medical Insurance ${element.name}",
                        extention: ".${element.extension!}",
                        url: element.file!),
                  );
                }
                if (Platform.isIOS) {
                  GeneralProvider generalProvider =
                      Provider.of<GeneralProvider>(context, listen: false);
                  await generalProvider.launchUniversalLinkIos(
                      url: element.file!);
                }
              }
              // PageNavigator.push(
              //   context: context,
              //   route: PhotoViewScreen(
              //     image: element.file,
              //     extension: element.extension,
              //   ),
              // );
            },
            child: Row(
              children: [
                Text(
                  "Download",
                  style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: ThemeColors.primaryColor,
                      decoration: TextDecoration.underline),
                ),
                SizedBox(width: w * 2),
                Image.asset(
                  "assets/icons/download_icon.png",
                  height: h * 11.67,
                  width: w * 11.67,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
