// ignore_for_file: use_build_context_synchronously

import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/flight_tickets/widget/flight_ticket_upload-dialoge.dart';
import 'package:e8_hr_portal/view/flight_tickets/widget/reject_reason_dialoge.dart';
import 'package:e8_hr_portal/view/other_screens/overview/widgets/cancel_button.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/provider/flight_ticket_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:e8_hr_portal/view/widgets/hisense_drop_down_tile.dart';
import 'package:provider/provider.dart';
import '../../util/colors.dart';
import '../../util/styles.dart';
import '../widgets/hisense_text_form_field.dart';

class FlighticketsNewRequest2 extends StatefulWidget {
  final bool isFromEdit;
  final bool isFromLevels;
  final bool isRequestNew;
  final String cityOfDeparture;
  final String countryOfDeparture;
  final String countryOfArrival;
  final String cityOfArrival;
  final String status;
  final String? remarks;
  final String? airline;
  final String? departureDate;
  final String? returnDate;
  final String? ticketPermission;
  final int? ticketId;
  final bool isFromUpload;
  final bool isButtonAction;

  const FlighticketsNewRequest2({
    required this.cityOfArrival,
    required this.countryOfArrival,
    required this.cityOfDeparture,
    required this.countryOfDeparture,
    required this.isFromEdit,
    required this.status,
    required this.ticketPermission,
    this.isRequestNew = false,
    this.remarks,
    this.airline,
    this.isFromLevels = false,
    this.departureDate,
    this.returnDate,
    this.ticketId,
    this.isFromUpload = false,
    this.isButtonAction = false,
    super.key,
  });

  @override
  State<FlighticketsNewRequest2> createState() =>
      _FlighticketsNewRequest2State();
}

class _FlighticketsNewRequest2State extends State<FlighticketsNewRequest2> {
  final _flightTicketNew2FormKey = GlobalKey<FormState>();
  final _uploadQutationFormKey = GlobalKey<FormState>();

  TextEditingController flightRemarksController = TextEditingController();
  TextEditingController fileNameController = TextEditingController();
  TextEditingController flightDaysController2 = TextEditingController();
  @override
  void initState() {
    var flightTicketProvider = context.read<FlightTicketProvider>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      flightTicketProvider.uploadFileImage = null;
      flightTicketProvider.pickedFile = null;
    });

    if (widget.isFromLevels || widget.isFromEdit) {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        flightTicketProvider.selectedAirline = null;
        if (widget.airline != null) {
          flightTicketProvider.selectedAirline = widget.airline ?? "";
        }

        flightTicketProvider.selectedFromDate =
            DateTime.parse(widget.departureDate ?? "");
        flightTicketProvider.selectedToDate =
            DateTime.parse(widget.returnDate ?? "");
      });
      int daysBetween = 0;
      int daysBetweenPlus = 0;
      var retrnDate = DateTime.parse(widget.returnDate.toString());
      var departureDAte = DateTime.parse(widget.departureDate.toString());
      final duration = retrnDate.difference(departureDAte);
      daysBetween = duration.inDays;
      daysBetweenPlus = daysBetween + 1;
      flightDaysController2.text = daysBetweenPlus.toString();
    } else {
      flightDaysController2.text = "1";
    }
    FlightTicketProvider provider =
        Provider.of<FlightTicketProvider>(context, listen: false);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      flightRemarksController.text = widget.remarks ?? "";

      // provider.selectedFromDate = DateTime.now();
      // provider.selectedToDate = DateTime.now();
      // provider.formattedFromDate =
      //     formatDateFromDate(dateTime: DateTime.now(), format: "dd MMM yyyy");
      // provider.formattedToDate = formatDateFromDate(
      //     dateTime: provider.selectedFromDate!, format: "dd MMM yyyy");

      provider.daysBetweenPlus = 0;
      provider.daysBetween = 0;

      setState(() {});
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      screenTitle: "Flight Tickets",
      body: Form(
        key: _flightTicketNew2FormKey,
        child: ListView(
          children: [
            SizedBox(height: h * 25),
            Text(
              "Company & Personal Info",
              style: tsS18w500c181818,
            ),
            SizedBox(height: h * 15),
            _subTitile(subtitle: "Airline"),
            _selectAirlineDropdown(isForEdit: widget.isFromEdit),
            SizedBox(height: h * 15),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Consumer<FlightTicketProvider>(
                  builder: (context, provider, _) {
                    return IgnorePointer(
                      ignoring: (widget.isFromLevels ||
                          (provider.flightTicketOverviewModel?.data?.status
                                      ?.toLowerCase() !=
                                  "pending" &&
                              widget.isFromEdit)),
                      child: _calenderWidget(
                        isMandatory: true,
                        subtitle: "Departure Date",
                        date: formatDateFromDate(
                            dateTime: provider.selectedFromDate,
                            format: "dd MMM yyyy"),
                        onTap: () async {
                          // if (Platform.isAndroid) {
                          await provider.selectFromDate(context: context);

                          // } else if (Platform.isIOS) {
                          //   await provider.selectFromDateIOS(ctx: context);
                          // }
                        },
                      ),
                    );
                  },
                ),
                Consumer<FlightTicketProvider>(
                  builder: (context, provider, _) {
                    return IgnorePointer(
                      ignoring: (widget.isFromLevels ||
                          (provider.flightTicketOverviewModel?.data?.status
                                      ?.toLowerCase() !=
                                  "pending" &&
                              widget.isFromEdit)),
                      child: _calenderWidget(
                        isMandatory: true,
                        subtitle: "Return Date",
                        date: formatDateFromDate(
                            dateTime: provider.selectedToDate,
                            format: "dd MMM yyyy"),
                        onTap: () async {
                          // if (Platform.isAndroid) {

                          await provider.selectToDate(context: context);

                          // } else if (Platform.isIOS) {
                          //   await provider.selectToDateIOS(ctx: context);
                          // }
                        },
                      ),
                    );
                  },
                ),
              ],
            ),
            SizedBox(height: h * 15),
            _subTitile(subtitle: "No of days"),
            HisenseTextFormField(
              controller: flightDaysController2,
              hintText: "Number of days",
              hintStyle: tsS14w400454444,
              textStyle: tsS14w400454444,
              // keyboardType: TextInputType.text,
              // validator: Validator.text,
              enabled: false,
              contentPadding:
                  EdgeInsets.symmetric(horizontal: w * 11, vertical: h * 12),
            ),
            SizedBox(height: h * 15),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  "*",
                  style: tsS14w400cFA0000,
                ),
                Text(
                  "*",
                  style: tsS14w400cFA0000,
                ),
                SizedBox(
                  width: 05 * w,
                ),
                const Expanded(
                  child: Column(
                    children: [
                      Text(
                        "Please select departure and return dates that match the requested leave period.",
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: h * 15),
            _subTitile(subtitle: "Remarks"),
            Consumer<FlightTicketProvider>(builder: (context, provider, _) {
              return IgnorePointer(
                ignoring: (widget.isFromLevels ||
                    (provider.flightTicketOverviewModel?.data?.status
                                ?.toLowerCase() !=
                            "pending" &&
                        widget.isFromEdit)),
                child: HisenseTextFormField(
                  controller: flightRemarksController,
                  hintText: "Remarks",
                  hintStyle: tsS14w400454444,
                  textStyle: tsS14w400454444,
                  // keyboardType: TextInputType.text,
                  validator: Validator.text,
                  maxLines: 5,
                  enabled: true,
                  contentPadding: EdgeInsets.symmetric(
                      horizontal: w * 11, vertical: h * 12),
                ),
              );
            }),
            SizedBox(
              height: 10 * h,
            ),
            if (!widget.isRequestNew)
              Consumer<FlightTicketProvider>(builder: (context, flight, _) {
                if (flight.flightTicketOverviewModel?.data?.apprvReject?.first
                            .userId ==
                        LoginModel.uid &&
                    (flight.flightTicketOverviewModel?.data?.status
                            ?.toLowerCase() !=
                        "pending")) {
                  return const SizedBox();
                }

                if (flight.flightTicketOverviewModel?.data?.apprvReject?.first
                            .userId ==
                        LoginModel.uid ||
                    (flight.flightTicketOverviewModel?.data?.status
                                ?.toLowerCase() ==
                            "approved" &&
                        LoginModel.isAdmin == true &&
                        widget.isFromUpload)) {
                  return Column(
                    children: [
                      _subTitile(
                        subtitle: widget.isFromUpload
                            ? "Upload Tickets"
                            : "Upload Quotations",
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: 250 * w,
                            child: Form(
                              key: _uploadQutationFormKey,
                              child: HisenseTextFormField(
                                controller: fileNameController,
                                hintText: widget.isFromUpload
                                    ? "Ticket Name"
                                    : "Quotation Name",
                                hintStyle: tsS14w400454444,
                                textStyle: tsS14w400454444,
                                // keyboardType: TextInputType.text,
                                validator: Validator.text,
                                enabled: true,
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: w * 11,
                                  vertical: h * 12,
                                ),
                              ),
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              showDialog(
                                context: context,
                                builder: (context) =>
                                    FlightTicketDocUploadDialog(ctx: context),
                              );
                            },
                            child: Container(
                              height: 45 * h,
                              margin: EdgeInsets.only(left: 10 * h),
                              padding: EdgeInsets.all(8 * h),
                              decoration: BoxDecoration(
                                  color: Colors.blueGrey[100],
                                  borderRadius: BorderRadius.circular(5)),
                              child: const Center(
                                child: Text(
                                  "Choose file",
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                } else {
                  return const SizedBox();
                }
              }),
            const SizedBox(
              height: 10,
            ),
            if (!widget.isRequestNew)
              Consumer<FlightTicketProvider>(builder: (context, flight, _) {
                if (flight.uploadFileImage?.name == null &&
                    flight.pickedFile?.name == null) {
                  return const SizedBox();
                }
                return Row(
                  children: [
                    Container(
                      height: 40 * h,
                      width: 250 * w,
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: ThemeColors.colorE6E6E6)),
                      child: Text(
                        flight.uploadFileImage?.name ??
                            flight.pickedFile?.name ??
                            "unknown",
                        style: tsS14BN,
                      ),
                    ),
                    InkWell(
                      onTap: () async {
                        if (_uploadQutationFormKey.currentState!.validate()) {
                          // if (fileNameController.text.isNotEmpty) {
                          if (flight.flightTicketOverviewModel?.data
                                      ?.apprvReject?.first.userId ==
                                  LoginModel.uid &&
                              !widget.isFromUpload) {
                            await flight.getFlightTicketUploadQuotation(
                              title: fileNameController.text,
                              ticketId: widget.ticketId ?? 1,
                            );
                            fileNameController.clear();
                          } else {
                            if (flight.flightTicketOverviewModel?.data?.status
                                        ?.toLowerCase() ==
                                    "approved" &&
                                LoginModel.isAdmin == true) {
                              await flight.getFlightTicketUploadTicket(
                                title: fileNameController.text,
                                ticketId: widget.ticketId ?? 1,
                              );
                              fileNameController.clear();
                            }
                          }

                          // flight.uploadFileImage = null;
                          // flight.pickedFile = null;
                          // } else {
                          //   showToastText("Please enter file titile");
                          // }
                        }
                      },
                      child: Container(
                        height: 40,
                        width: 40,
                        margin: EdgeInsets.only(left: 7 * h),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: ThemeColors.colorE6E6E6),
                        ),
                        child: Icon(
                          Icons.add,
                          color: ThemeColors.colorFD5E5A,
                        ),
                      ),
                    ),
                  ],
                );
              }),
            const SizedBox(
              height: 10,
            ),
            // if (widget.isFromUpload)
            Consumer<FlightTicketProvider>(builder: (context, flight, _) {
              if (LoginModel.isAdmin == false &&
                  !widget.isFromLevels &&
                  !widget.isFromEdit) {
                return const SizedBox();
              }

              if (widget.isFromLevels && !widget.isFromUpload) {
                return const SizedBox();
              }

              if ((flight.flightTicketOverviewModel?.data == null &&
                      flight.flightTicketOverviewModel?.data?.tktUploadData ==
                          null) ||
                  flight.flightTicketOverviewModel!.data!.tktUploadData!
                      .isEmpty) {
                return const SizedBox();
              }

              return Column(
                children: [
                  _subTitile(subtitle: "Uploaded Tickets"),
                  ListView.separated(
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: flight
                        .flightTicketOverviewModel!.data!.tktUploadData!.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return Row(
                        children: [
                          Container(
                            height: 40 * h,
                            width: 250 * w,
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(5),
                                border:
                                    Border.all(color: ThemeColors.colorE6E6E6)),
                            child: Text(
                              flight.flightTicketOverviewModel!.data!
                                      .tktUploadData![index].title ??
                                  "",
                              style: tsS14BN,
                            ),
                          ),
                          if (widget.isFromUpload)
                            InkWell(
                              onTap: () {
                                flight.flightTicketQuatationDelete(
                                    ticketId: widget.ticketId ?? 1,
                                    fileType: flight.flightTicketOverviewModel!
                                        .data!.tktUploadData![index].fileType
                                        .toString(),
                                    attachmentId: flight
                                        .flightTicketOverviewModel!
                                        .data!
                                        .tktUploadData![index]
                                        .id
                                        .toString());
                              },
                              child: Container(
                                height: 40,
                                width: 40,
                                margin: EdgeInsets.only(left: 7 * h),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(5),
                                  border: Border.all(
                                      color: ThemeColors.colorE6E6E6),
                                ),
                                child: Padding(
                                  padding: EdgeInsets.only(bottom: 18.0 * h),
                                  child: Icon(
                                    Icons.minimize_sharp,
                                    color: ThemeColors.colorFD5E5A,
                                  ),
                                ),
                              ),
                            ),
                          InkWell(
                            onTap: () {
                              flight.downloadDocument(
                                  url: flight.flightTicketOverviewModel!.data!
                                      .tktUploadData![index].file
                                      .toString(),
                                  fileName: flight.flightTicketOverviewModel!
                                      .data!.tktUploadData![index].title
                                      .toString());
                            },
                            child: Container(
                              height: 40,
                              width: 40,
                              margin: EdgeInsets.only(left: 7 * h),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(5),
                                border:
                                    Border.all(color: ThemeColors.colorE6E6E6),
                              ),
                              child: Icon(
                                Icons.file_download_outlined,
                                color: ThemeColors.primaryColor,
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                    separatorBuilder: (context, index) => const SizedBox(
                      height: 10,
                    ),
                  ),
                  SizedBox(
                    height: 10 * h,
                  )
                ],
              );
            }),

            if (!widget.isRequestNew)
              Consumer<FlightTicketProvider>(builder: (context, flight, _) {
                // if (widget.isFromLevels ||
                //     (flight.flightTicketOverviewModel?.data?.status
                //                 ?.toLowerCase() !=
                //             "pending" &&
                //         widget.isFromEdit)) {
                //   return const SizedBox();
                // }
                if (!widget.isFromLevels) {
                  return const SizedBox();
                }
                // if (widget.isFromLevels &&
                //     flight.flightTicketOverviewModel?.data?.apprvReject?.first
                //             .userId !=
                //         LoginModel.uid) {
                //   return const SizedBox();
                // }
                if ((flight.flightTicketOverviewModel?.data == null &&
                        flight.flightTicketOverviewModel?.data?.tktFile ==
                            null) ||
                    flight.flightTicketOverviewModel!.data!.tktFile!.isEmpty) {
                  return const SizedBox();
                }
                return Column(
                  children: [
                    _subTitile(subtitle: "Uploaded Quotations"),
                    ListView.separated(
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: flight
                          .flightTicketOverviewModel!.data!.tktFile!.length,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        return Row(
                          children: [
                            Container(
                              height: 40 * h,
                              width: 250 * w,
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(5),
                                  border: Border.all(
                                      color: ThemeColors.colorE6E6E6)),
                              child: Text(
                                flight.flightTicketOverviewModel!.data!
                                        .tktFile![index].title ??
                                    "",
                                style: tsS14BN,
                              ),
                            ),
                            if (flight.flightTicketOverviewModel?.data
                                        ?.apprvReject?.first.userId ==
                                    LoginModel.uid &&
                                (flight.flightTicketOverviewModel?.data?.status
                                        ?.toLowerCase() ==
                                    "pending"))
                              InkWell(
                                onTap: () {
                                  flight.flightTicketQuatationDelete(
                                      ticketId: widget.ticketId ?? 1,
                                      fileType: flight
                                          .flightTicketOverviewModel!
                                          .data!
                                          .tktFile![index]
                                          .fileType
                                          .toString(),
                                      attachmentId: flight
                                          .flightTicketOverviewModel!
                                          .data!
                                          .tktFile![index]
                                          .id
                                          .toString());
                                },
                                child: Container(
                                  height: 40,
                                  width: 40,
                                  margin: EdgeInsets.only(left: 7 * h),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(5),
                                    border: Border.all(
                                        color: ThemeColors.colorE6E6E6),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.only(bottom: 18.0 * h),
                                    child: Icon(
                                      Icons.minimize_sharp,
                                      color: ThemeColors.colorFD5E5A,
                                    ),
                                  ),
                                ),
                              ),
                            InkWell(
                              onTap: () {
                                flight.downloadDocument(
                                    url: flight.flightTicketOverviewModel!.data!
                                        .tktFile![index].file
                                        .toString(),
                                    fileName: flight.flightTicketOverviewModel!
                                        .data!.tktFile![index].title
                                        .toString());
                              },
                              child: Container(
                                height: 40,
                                width: 40,
                                margin: EdgeInsets.only(left: 7 * h),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(5),
                                  border: Border.all(
                                      color: ThemeColors.colorE6E6E6),
                                ),
                                child: Icon(
                                  Icons.file_download_outlined,
                                  color: ThemeColors.primaryColor,
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                      separatorBuilder: (context, index) => const SizedBox(
                        height: 10,
                      ),
                    ),
                  ],
                );
              }),
            SizedBox(
              height: 10 * h,
            ),
            if (!widget.isRequestNew)
              Consumer<FlightTicketProvider>(builder: (context, flight, _) {
                var approveRejectComments =
                    flight.flightTicketOverviewModel?.data?.apprvReject;

                if (approveRejectComments == null ||
                    approveRejectComments.isEmpty) {
                  return const SizedBox();
                }

                return Column(
                  children: [
                    // _subTitile(subtitle: "Comments"),
                    ListView.separated(
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        physics: const BouncingScrollPhysics(),
                        itemBuilder: (context, index) {
                          if (approveRejectComments[index].remark == null) {
                            return const SizedBox();
                          }
                          return leaveRejectedCard(
                              rejectedBy:
                                  approveRejectComments[index].userName ?? "",
                              comment:
                                  approveRejectComments[index].remark ?? "",
                              rejectedDate:
                                  approveRejectComments[index].requestedDate ??
                                      "",
                              appOrReject:
                                  (approveRejectComments[index].levelStatus ??
                                              "")
                                          .toLowerCase() ==
                                      "approved");
                        },
                        separatorBuilder: (context, index) {
                          return const SizedBox(
                            height: 5,
                          );
                        },
                        itemCount: approveRejectComments.length),
                  ],
                );
              }),
            SizedBox(height: h * 32),
            // const Spacer(),
            if ((widget.remarks != null &&
                    widget.status.toLowerCase() == "pending" &&
                    widget.isFromEdit) &&
                !widget.isFromLevels)
              GeneralButton(
                height: h * 56,
                width: w * 343,
                onPressed: () async {
                  if (_flightTicketNew2FormKey.currentState!.validate()) {
                    FlightTicketProvider prov =
                        Provider.of<FlightTicketProvider>(context,
                            listen: false);

                    bool isPop = await prov.flightTicketEdit(
                      ticketId: widget.ticketId.toString(),
                      context: context,
                      airline: prov.selectedAirline!,
                      cityOfArrival: widget.cityOfArrival,
                      cityOfDeparture: widget.cityOfDeparture,
                      countryOfArrival: widget.countryOfArrival,
                      countryOfDeparture: widget.countryOfDeparture,
                      remarks: flightRemarksController.text,
                      departureDate: formatDateFromDate(
                          dateTime: prov.selectedFromDate,
                          format: "yyyy-MM-dd"),
                      returnDate: formatDateFromDate(
                          dateTime: prov.selectedToDate, format: "yyyy-MM-dd"),
                      type: prov.selectedWayText,
                    );
                    if (isPop && mounted) {
                      Navigator.of(context).pop();
                      Navigator.of(context).pop();
                    }
                  }
                },
                title: "Update",
                textStyle: tsS18w600cFFFFFF,
              )
            else if (((widget.isFromLevels &&
                    widget.ticketPermission?.toLowerCase() == "allow") &&
                (widget.status.toLowerCase() == "pending" ||
                    widget.status.toLowerCase() == "in progress") &&
                (!widget.isButtonAction)))
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  CancelButton2(
                    title: widget.isFromLevels ? "Reject" : "Cancel",
                    height: 56 * h,
                    width: 162,
                    textStyle: tsS18w600cFFFFFF,
                    onPressed: () async {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return FlightRejectionReasonDialog(
                            ticektId: widget.ticketId ?? 0,
                          );
                        },
                      );
                    },
                  ),
                  Consumer<FlightTicketProvider>(builder: (context, flight, _) {
                    return GeneralButton(
                      height: h * 56,
                      width: w * 160,
                      onPressed: () async {
                        if (_flightTicketNew2FormKey.currentState!.validate()) {
                          if (flight.flightTicketOverviewModel?.data
                                      ?.apprvReject?.first.userId ==
                                  LoginModel.uid &&
                              !widget.isFromUpload) {
                            if ((flight.flightTicketOverviewModel?.data ==
                                        null &&
                                    flight.flightTicketOverviewModel?.data
                                            ?.tktFile ==
                                        null) ||
                                flight.flightTicketOverviewModel!.data!.tktFile!
                                    .isEmpty) {
                              showToastText("Please upload quotations");

                              return;
                            }
                          }
                          showDialog(
                            context: context,
                            builder: (context) {
                              return FlightRejectionReasonDialog(
                                ticektId: widget.ticketId ?? 0,
                                isAccept: true,
                              );
                            },
                          );
                        }
                      },
                      title: "Approve",
                      textStyle: tsS18w600cFFFFFF,
                    );
                  }),
                ],
              )
            else if (widget.status.toLowerCase() == "cancelled")
              const SizedBox()
            else if (widget.status.toLowerCase() == "approved")
              const SizedBox()
            else if (widget.status.toLowerCase() == "rejected")
              const SizedBox()
            else if (widget.status.toLowerCase() == "in progress" &&
                widget.isButtonAction)
              const SizedBox()
            else if (widget.isRequestNew)
              GeneralButton(
                height: h * 56,
                width: w * 343,
                onPressed: () async {
                  if (_flightTicketNew2FormKey.currentState!.validate()) {
                    FlightTicketProvider prov =
                        Provider.of<FlightTicketProvider>(context,
                            listen: false);

                    bool isPop = await prov.flightTicketCreate(
                        context: context,
                        airline: prov.selectedAirline!,
                        cityOfArrival: widget.cityOfArrival,
                        cityOfDeparture: widget.cityOfDeparture,
                        countryOfArrival: widget.countryOfArrival,
                        countryOfDeparture: widget.countryOfDeparture,
                        remarks: flightRemarksController.text,
                        departureDate: formatDateFromDate(
                            dateTime: prov.selectedFromDate,
                            format: "yyyy-MM-dd"),
                        returnDate: formatDateFromDate(
                            dateTime: prov.selectedToDate,
                            format: "yyyy-MM-dd"),
                        type: prov.selectedWayText);
                    if (isPop && mounted) {
                      Navigator.of(context).pop();
                      Navigator.of(context).pop();
                    }
                  }
                },
                title: "Submit",
                textStyle: tsS18w600cFFFFFF,
              ),
            SizedBox(height: h * 50),
          ],
        ),
      ),
    );
  }

  Widget _subTitile({required String subtitle, bool isMandatory = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: Row(
            children: [
              Text(
                subtitle,
                style: tsS14w400c30292F,
              ),
              if (isMandatory)
                Text(
                  "*",
                  style: tsS14w400cFA0000,
                ),
            ],
          ),
        ),
        SizedBox(height: h * 4),
      ],
    );
  }

  Widget _calenderWidget({
    required String subtitle,
    required bool isMandatory,
    required String date,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _subTitile(subtitle: subtitle, isMandatory: isMandatory),
        Align(
          alignment: Alignment.centerLeft,
          child: GestureDetector(
            onTap: onTap,
            child: Container(
              height: h * 45,
              width: w * 164,
              padding: EdgeInsets.symmetric(horizontal: w * 11),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: ThemeColors.colorFFFFFF,
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: ThemeColors.colorE3E3E3, width: 1),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    date,
                    style: tsS14w400454444,
                  ),
                  Image.asset(
                    "assets/icons/calendar_black.png",
                    height: h * 18,
                    width: w * 18,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Widget _radioButton({required String title, required int selectedWay}) {
  //   return Container(
  //     width: w * 150,
  //     alignment: Alignment.centerLeft,
  //     child: Consumer<FlightTicketProvider>(
  //       builder: (context, provider, _) {
  //         return RadioListTile(
  //           dense: true,
  //           contentPadding: EdgeInsets.zero,
  //           title: Text(
  //             title,
  //             style: selectedWay == provider.selectedWay
  //                 ? tsS14w400c45464E
  //                 : tsS14w400c949494,
  //             maxLines: 1,
  //           ),
  //           value: true,
  //           groupValue: true,
  //           activeColor: ThemeColors.colorFCC400,
  //           onChanged: (bool? value) {
  //             // if (value != null) {
  //             //   provider.selectedWay = value;
  //             //   if (value == 1) {
  //             //     provider.selectedWayText = "One Way";
  //             //   } else if (value == 2) {
  //             //     provider.selectedWayText = "Two Way";
  //             //   }
  //             // }
  //           },
  //         );
  //       },
  //     ),
  //   );
  // }

  Widget _selectAirlineDropdown({required bool isForEdit}) {
    return Consumer<FlightTicketProvider>(
      builder: (context, provider, _) {
        return IgnorePointer(
          ignoring: (widget.isFromLevels ||
              (provider.flightTicketOverviewModel?.data?.status
                          ?.toLowerCase() !=
                      "pending" &&
                  isForEdit)),
          child: HisenseDropdownTile(
            title: "",
            hintText: "Select Airline",
            hintStyle: tsS14w4009F9F9F,
            style: tsS14w400454444,
            value: provider.selectedAirline,
            validator: (value) {
              if (value == null) {
                return "Select Your Desired Airline";
              }
              return null;
            },
            // contentPadding: EdgeInsets.fromLTRB(12 * w, 20 * h, 12 * w, 12 * h),
            onChanged: (String? value) {
              if (value != null) {
                WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
                  provider.selectedAirline = value;
                });
              }
            },
            items: provider.airlineList.map((item) {
              return DropdownMenuItem(
                value: item.name,
                child: Text(
                  item.name ?? "",
                  style: tsS14w400454444,
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  Widget leaveRejectedCard(
      {required String rejectedBy,
      required String comment,
      required String? rejectedDate,
      required bool? appOrReject}) {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                height: 24,
                width: 24,
                margin: const EdgeInsets.only(right: 10),
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                    color: appOrReject == false
                        ? ThemeColors.colorFCD2D0
                        : ThemeColors.color06AA37.withOpacity(.2),
                    borderRadius: BorderRadius.circular(6)),
                child: Container(
                  decoration: BoxDecoration(
                      color: appOrReject == false
                          ? ThemeColors.colorF64D44
                          : ThemeColors.color06AA37,
                      shape: BoxShape.circle),
                  child: Center(
                    child: Icon(
                      appOrReject == false
                          ? Icons.close_rounded
                          : Icons.done_rounded,
                      color: Colors.white,
                      size: 10,
                    ),
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      appOrReject == false
                          ? "Flight Ticket Rejected"
                          : "Flight Ticket Approved",
                      style: appOrReject == false
                          ? tsS12w4cF64D44
                          : tsS12w4c06AA37,
                    ),
                  ),
                  SizedBox(
                    height: 15,
                    width: 280 * w,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: Text(
                            rejectedBy,
                            style: tsS10w400c646363,
                          ),
                        ),
                        SizedBox(
                          width: 10 * w,
                        ),
                        Expanded(
                          child: Text(
                            rejectedDate.toString(),
                            style: tsS10w400c646363,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          Divider(
            thickness: 1,
            color: ThemeColors.colorD9D9D9,
          ),
          const SizedBox(height: 12),
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              comment,
              style: tsS14w400979797,
            ),
          )
        ],
      ),
    );
  }

  // String _calculateDays() {
  //   FlightTicketProvider provider =
  //       Provider.of<FlightTicketProvider>(context, listen: false);
  //   num days = (provider.daysBetweenPlus);

  //   if (days != 0) {
  //     flightDaysController.text = "$days Days";
  //   } else if (days == 0) {
  //     flightDaysController.text = "${days + 1} Day";
  //   } else {
  //     flightDaysController.clear();
  //   }
  //   return days.toString();
  // }
}
