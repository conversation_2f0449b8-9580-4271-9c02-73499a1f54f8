import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart' as dot;
import 'package:e8_hr_portal/model/flight_ticket_count_model.dart';
import 'package:e8_hr_portal/model/flight_ticket_overview_model.dart';
import 'package:e8_hr_portal/model/flight_ticket_personal_info.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/provider/flight_ticket_provider.dart';
import 'package:e8_hr_portal/util/capitalize.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/flight_tickets/flight_ticket_previous_request.dart';
import 'package:e8_hr_portal/view/flight_tickets/widget/flight_ticket_upload-dialoge.dart';
import 'package:e8_hr_portal/view/flight_tickets/widget/reject_reason_dialoge.dart';
import 'package:e8_hr_portal/view/other_screens/overview/widgets/cancel_button.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:e8_hr_portal/view/widgets/general_dialog_box.dart';
import 'package:e8_hr_portal/view/widgets/hisense_text_form_field.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:easy_stepper/easy_stepper.dart';
import 'package:provider/provider.dart';

class FlightTicketDetailsViewNew extends StatefulWidget {
  final bool isForEdit;
  final bool isFromLevels;
  final FlightTicketOverviewModel? flightTicketOverviewModel;
  const FlightTicketDetailsViewNew({
    super.key,
    required this.flightTicketOverviewModel,
    this.isForEdit = false,
    this.isFromLevels = false,
  });

  @override
  State<FlightTicketDetailsViewNew> createState() =>
      _FlightTicketDetailsViewNewState();
}

class _FlightTicketDetailsViewNewState
    extends State<FlightTicketDetailsViewNew> {
  final _uploadQutationFormKey = GlobalKey<FormState>();
  TextEditingController flightRemarksController = TextEditingController();
  TextEditingController fileNameController = TextEditingController();

  @override
  void initState() {
    var flightTicketProvider = context.read<FlightTicketProvider>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      flightTicketProvider.uploadFileImage = null;
      flightTicketProvider.pickedFile = null;
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var provider = context.read<FlightTicketProvider>();
    var personalInfo = provider.flightTicketPersonalInfo;
    var flightTicketCountModel = provider.flightTicketCountModel;
    int index = 0;
    return HisenseScaffold(
      screenTitle: "Flight Tickets",
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: h * 10),
            Text(
              "Request Details",
              style: tsS18w500c181818,
            ),
            SizedBox(
              height: 10 * h,
            ),
            _steperWidget(index: index),
            SizedBox(
              height: 10 * h,
            ),
            _personalDetails(
              personalInfo: personalInfo,
              flightTicketCountModel: flightTicketCountModel,
            ),
            SizedBox(
              height: 10 * h,
            ),
            _ticketDetails(),
            SizedBox(
              height: 10 * h,
            ),
            // if (widget
            //     .flightTicketOverviewModel!.data!.tktFile!.isNotEmpty) ...[
            _qutationDetails(),
            SizedBox(
              height: 10 * h,
            ),
            // ],
            _remarks(),
            SizedBox(
              height: 10 * h,
            ),
            _comments(),
            SizedBox(
              height: 10 * h,
            ),
            _previousRequest(),
            SizedBox(
              height: 30 * h,
            ),
            _buttons(),
            SizedBox(
              height: 30 * h,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buttons() {
    var item = widget.flightTicketOverviewModel?.data;
    return Column(
      children: [
        if ((item?.remark != null &&
                item?.status?.toLowerCase() == "pending" &&
                widget.isForEdit) &&
            !widget.isFromLevels)
          GeneralButton(
            height: h * 50,
            width: w * 343,
            onPressed: () async {
              // if (_flightTicketNew2FormKey.currentState!.validate()) {
              FlightTicketProvider prov =
                  Provider.of<FlightTicketProvider>(context, listen: false);

              bool isPop = await prov.flightTicketEdit(
                ticketId: item?.id.toString() ?? '',
                context: context,
                airline: prov.selectedAirline!,
                cityOfArrival: item?.cityOfArrival ?? "",
                cityOfDeparture: item?.cityOfDeperature ?? "",
                countryOfArrival: item?.countryOfArrival ?? "",
                countryOfDeparture: item?.countryOfDeparture ?? "",
                remarks: flightRemarksController.text,
                departureDate: formatDateFromDate(
                    dateTime: prov.selectedFromDate, format: "yyyy-MM-dd"),
                returnDate: formatDateFromDate(
                    dateTime: prov.selectedToDate, format: "yyyy-MM-dd"),
                type: prov.selectedWayText,
              );
              if (isPop && mounted) {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              }
              // }
            },
            title: "Update",
            textStyle: tsS18w600cFFFFFF,
          )
        else if (((widget.isFromLevels &&
                    item?.tktPermission?.ticketPermission?.toLowerCase() ==
                        "allow") &&
                (item?.status?.toLowerCase() == "pending" ||
                    item?.status?.toLowerCase() == "in progress")
            //     &&
            // (!widget.isButtonAction)
            ))
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              CancelButton2(
                title: widget.isFromLevels ? "Reject" : "Cancel",
                height: 50 * h,
                width: 162 * w,
                textStyle: tsS18w600cFFFFFF,
                onPressed: () async {
                  showDialog(
                    context: context,
                    builder: (context) {
                      return FlightRejectionReasonDialog(
                        ticektId: item?.id ?? 0,
                      );
                    },
                  );
                },
              ),
              Consumer<FlightTicketProvider>(builder: (context, flight, _) {
                return GeneralButton(
                  height: h * 50,
                  width: w * 160,
                  onPressed: () async {
                    // if (_flightTicketNew2FormKey.currentState!.validate()) {
                    if (flight.flightTicketOverviewModel?.data?.apprvReject
                                ?.first.userId ==
                            LoginModel.uid
                        //      &&
                        // !widget.isFromUpload
                        ) {
                      if ((flight.flightTicketOverviewModel?.data == null &&
                              flight.flightTicketOverviewModel?.data?.tktFile ==
                                  null) ||
                          flight.flightTicketOverviewModel!.data!.tktFile!
                              .isEmpty) {
                        showToastText("Please upload quotations");

                        return;
                      }
                    }
                    showDialog(
                      context: context,
                      builder: (context) {
                        return FlightRejectionReasonDialog(
                          ticektId: item?.id ?? 0,
                          isAccept: true,
                        );
                      },
                    );
                    // }
                  },
                  title: "Approve",
                  textStyle: tsS18w600cFFFFFF,
                );
              }),
            ],
          )
        else if (item?.status?.toLowerCase() == "cancelled")
          const SizedBox()
        else if (item?.status?.toLowerCase() == "approved")
          const SizedBox()
        else if (item?.status?.toLowerCase() == "rejected")
          const SizedBox()
        else if (item?.status?.toLowerCase() == "in progress"
            // &&
            //     widget.isButtonAction
            )
          const SizedBox()
        // else if (widget.isRequestNew)
        //   GeneralButton(
        //     height: h * 56,
        //     width: w * 343,
        //     onPressed: () async {
        //       if (_flightTicketNew2FormKey.currentState!.validate()) {
        //         FlightTicketProvider prov =
        //             Provider.of<FlightTicketProvider>(context, listen: false);

        //         bool isPop = await prov.flightTicketCreate(
        //             context: context,
        //             airline: prov.selectedAirline!,
        //             cityOfArrival: widget.cityOfArrival,
        //             cityOfDeparture: widget.cityOfDeparture,
        //             countryOfArrival: widget.countryOfArrival,
        //             countryOfDeparture: widget.countryOfDeparture,
        //             remarks: flightRemarksController.text,
        //             departureDate: formatDateFromDate(
        //                 dateTime: prov.selectedFromDate, format: "yyyy-MM-dd"),
        //             returnDate: formatDateFromDate(
        //                 dateTime: prov.selectedToDate, format: "yyyy-MM-dd"),
        //             type: prov.selectedWayText);
        //         if (isPop && mounted) {
        //           Navigator.of(context).pop();
        //           Navigator.of(context).pop();
        //         }
        //       }
        //     },
        //     title: "Submit",
        //     textStyle: tsS18w600cFFFFFF,
        //   ),
      ],
    );
  }

  Widget _previousRequest() {
    return InkWell(
      onTap: () {
        if (isRedundentClick(DateTime.now())) {
          return;
        }
        PageNavigator.push(
            context: context,
            route: PreviousRequestFlightTicketRequest(
              userId: widget.flightTicketOverviewModel?.data?.userId ?? 0,
            ));
        // var pro = context.read<FlightTicketProvider>();
        // pro.getPerivousRequest(page: ,
        //     userId: widget.flightTicketOverviewModel?.data?.userId ?? 0);
      },
      child: Container(
        padding: const EdgeInsets.all(10),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Align(
              alignment: Alignment.topLeft,
              child: Text(
                "Previous Requests",
                style: tsS14w500c161616,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _remarks() {
    return Container(
      padding: const EdgeInsets.all(10),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              "Remarks",
              style: tsS14w500c161616,
            ),
          ),
          SizedBox(
            height: 10 * h,
          ),
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              widget.flightTicketOverviewModel?.data?.remark.toString() ?? "",
            ),
          ),
          SizedBox(
            height: 10 * h,
          ),
        ],
      ),
    );
  }

  Widget _comments() {
    bool isComments = widget.flightTicketOverviewModel!.data!.apprvReject!
        .any((e) => e.remark != null);

    List<ApproveRejectData>? comments = [];
    for (var data in widget.flightTicketOverviewModel!.data!.apprvReject!) {
      if (data.remark != null) {
        comments.add(data);
      }
    }

    if (isComments) {
      return Container(
        padding: const EdgeInsets.all(10),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Align(
              alignment: Alignment.topLeft,
              child: Text(
                "Comments",
                style: tsS14w500c161616,
              ),
            ),
            SizedBox(
              height: 10 * h,
            ),
            ListView.separated(
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                        color: comments[index].levelStatus == "approved"
                            ? ThemeColors.color06AA37.withOpacity(0.1)
                            : Colors.red.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8)),
                    child: Column(
                      children: [
                        Align(
                          alignment: Alignment.topLeft,
                          child: Text(
                            comments[index].remark ?? "",
                          ),
                        ),
                        Align(
                          alignment: Alignment.bottomRight,
                          child: Text(
                            "${comments[index].userName} ${comments[index].requestedDate}",
                            style: tsS12w500c979797,
                          ),
                        ),
                      ],
                    ),
                  );
                },
                separatorBuilder: (context, index) {
                  return SizedBox(
                    height: 10 * h,
                  );
                },
                itemCount: comments.length),
            // Align(
            //   alignment: Alignment.topLeft,
            //   child: Text(
            //     widget.flightTicketOverviewModel?.data?.remark.toString() ?? "",
            //   ),
            // ),
            SizedBox(
              height: 10 * h,
            ),
          ],
        ),
      );
    } else {
      return const SizedBox();
    }
  }

  Widget _qutationDetails() {
    return Container(
      padding: const EdgeInsets.all(10),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              "Quotations",
              style: tsS14w500c161616,
            ),
          ),
          SizedBox(
            height: 10 * h,
          ),
          Consumer<FlightTicketProvider>(builder: (context, flight, _) {
            if (flight.flightTicketOverviewModel?.data?.apprvReject?.first
                        .userId ==
                    LoginModel.uid &&
                (flight.flightTicketOverviewModel?.data?.status
                        ?.toLowerCase() !=
                    "pending")) {
              return const SizedBox();
            }

            if (flight.flightTicketOverviewModel?.data?.apprvReject?.first
                        .userId ==
                    LoginModel.uid ||
                (flight.flightTicketOverviewModel?.data?.status
                            ?.toLowerCase() ==
                        "approved" &&
                    LoginModel.isAdmin == true
                // &&
                // widget.isFromUpload

                )) {
              return Column(
                children: [
                  _subTitile(
                    subtitle:
                        // widget.isFromUpload
                        //     ? "Upload Tickets"
                        "Quotation Name",
                    isMandatory: true,
                  ),
                  Form(
                    key: _uploadQutationFormKey,
                    child: HisenseTextFormField(
                      controller: fileNameController,
                      hintText:
                          //  widget.isFromUpload
                          // ? "Ticket Name"
                          "Quotation Name",
                      hintStyle: tsS14w400454444,
                      textStyle: tsS14w400454444,
                      // keyboardType: TextInputType.text,
                      validator: Validator.text,
                      enabled: true,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: w * 11,
                        vertical: h * 12,
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  _subTitile(
                    subtitle:
                        // widget.isFromUpload
                        //     ? "Upload Tickets"
                        "Choose File",
                    isMandatory: true,
                  ),
                  _uploadYourDocumentWidget(),
                  SizedBox(
                    height: 10 * h,
                  ),
                  Consumer<FlightTicketProvider>(builder: (context, flight, _) {
                    if (flight.uploadFileImage?.name == null &&
                        flight.pickedFile?.name == null) {
                      return const SizedBox();
                    }
                    return Row(
                      children: [
                        Container(
                          height: 40 * h,
                          width: 250 * w,
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(5),
                              border:
                                  Border.all(color: ThemeColors.colorE6E6E6)),
                          child: Text(
                            flight.uploadFileImage?.name ??
                                flight.pickedFile?.name ??
                                "unknown",
                            style: tsS14BN,
                          ),
                        ),
                        InkWell(
                          onTap: () async {
                            if (isRedundentClick(DateTime.now())) {
                              return;
                            }

                            if (_uploadQutationFormKey.currentState!
                                .validate()) {
                              // if (fileNameController.text.isNotEmpty) {
                              if (flight.flightTicketOverviewModel?.data
                                      ?.apprvReject?.first.userId ==
                                  LoginModel.uid) {
                                await flight.getFlightTicketUploadQuotation(
                                  title: fileNameController.text,
                                  ticketId: widget.flightTicketOverviewModel
                                          ?.data?.id ??
                                      1,
                                );
                                fileNameController.clear();
                              } else {
                                if (flight.flightTicketOverviewModel?.data
                                            ?.status
                                            ?.toLowerCase() ==
                                        "approved" &&
                                    LoginModel.isAdmin == true) {
                                  await flight.getFlightTicketUploadTicket(
                                    title: fileNameController.text,
                                    ticketId: widget.flightTicketOverviewModel
                                            ?.data?.id ??
                                        1,
                                  );
                                  fileNameController.clear();
                                }
                              }

                              // flight.uploadFileImage = null;
                              // flight.pickedFile = null;
                              // } else {
                              //   showToastText("Please enter file titile");
                              // }
                            }
                          },
                          child: Container(
                            height: 40,
                            width: 40,
                            margin: EdgeInsets.only(left: 7 * h),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(5),
                              border:
                                  Border.all(color: ThemeColors.colorE6E6E6),
                            ),
                            child: Icon(
                              Icons.add,
                              color: ThemeColors.colorFD5E5A,
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
                ],
              );
            } else {
              return const SizedBox();
            }
          }),
          SizedBox(
            height: 10 * h,
          ),
          Consumer<FlightTicketProvider>(
            builder: (context, flight, _) {
              // if (widget.isFromLevels ||
              //     (flight.flightTicketOverviewModel?.data?.status
              //                 ?.toLowerCase() !=
              //             "pending" &&
              //         widget.isFromEdit)) {
              //   return const SizedBox();
              // }
              if (!widget.isFromLevels) {
                return const SizedBox();
              }
              // if (widget.isFromLevels &&
              //     flight.flightTicketOverviewModel?.data?.apprvReject?.first
              //             .userId !=
              //         LoginModel.uid) {
              //   return const SizedBox();
              // }
              if ((flight.flightTicketOverviewModel?.data == null &&
                      flight.flightTicketOverviewModel?.data?.tktFile ==
                          null) ||
                  flight.flightTicketOverviewModel!.data!.tktFile!.isEmpty) {
                return const SizedBox();
              }
              return Column(
                children: [
                  ListView.separated(
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount:
                        flight.flightTicketOverviewModel!.data!.tktFile!.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                            color: ThemeColors.color7E7E7E.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: ThemeColors.colorE6E6E6)),
                        child: Row(
                          children: [
                            Text(
                              flight.flightTicketOverviewModel!.data!
                                      .tktFile![index].title ??
                                  "",
                              style: tsS14BN,
                            ),
                            const Spacer(),
                            InkWell(
                              onTap: () {
                                if (isRedundentClick(DateTime.now())) {
                                  return;
                                }
                                flight.downloadDocument(
                                    url: flight.flightTicketOverviewModel!.data!
                                        .tktFile![index].file
                                        .toString(),
                                    fileName: flight.flightTicketOverviewModel!
                                        .data!.tktFile![index].title
                                        .toString());
                              },
                              child: Icon(
                                Icons.file_download_outlined,
                                color: ThemeColors.primaryColor,
                              ),
                            ),
                            if (flight.flightTicketOverviewModel?.data
                                        ?.apprvReject?.first.userId ==
                                    LoginModel.uid &&
                                (flight.flightTicketOverviewModel?.data?.status
                                        ?.toLowerCase() ==
                                    "pending"))
                              InkWell(
                                onTap: () {
                                  if (isRedundentClick(DateTime.now())) {
                                    return;
                                  }
                                  showDialog(
                                    context: context,
                                    builder: (context) {
                                      return GeneralConfirmationDialog(
                                        onYesStyle: tsS14w500c475366,
                                        onNoStyle: tsS14w500c475366,
                                        onNoPressed: () {
                                          Navigator.pop(context);
                                        },
                                        onYesPressed: () async {
                                          if (isRedundentClick(
                                              DateTime.now())) {
                                            return;
                                          }
                                          final navigator =
                                              Navigator.of(context);
                                          flight.flightTicketQuatationDelete(
                                              ticketId: widget
                                                      .flightTicketOverviewModel
                                                      ?.data
                                                      ?.id ??
                                                  1,
                                              fileType: flight
                                                  .flightTicketOverviewModel!
                                                  .data!
                                                  .tktFile![index]
                                                  .fileType
                                                  .toString(),
                                              attachmentId: flight
                                                  .flightTicketOverviewModel!
                                                  .data!
                                                  .tktFile![index]
                                                  .id
                                                  .toString());
                                          navigator.pop();
                                        },
                                        title: "Confirmation",
                                        content:
                                            "Are you sure do you want to delete this file ?",
                                        acceptLabel: "Yes",
                                        cancelLabel: "No",
                                      );
                                    },
                                  );
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  height: 20,
                                  width: 20,
                                  margin: EdgeInsets.only(left: 7 * h),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: ThemeColors.colorE6E6E6,
                                      width: 2,
                                    ),
                                  ),
                                  child: Icon(
                                    Icons.close,
                                    size: 15,
                                    color: ThemeColors.colorC2C2C2,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      );
                    },
                    separatorBuilder: (context, index) => const SizedBox(
                      height: 10,
                    ),
                  ),
                ],
              );
            },
          ),
          SizedBox(
            height: 10 * h,
          )
        ],
      ),
    );
  }

  Widget _uploadYourDocumentWidget() {
    return dot.DottedBorder(
      dashPattern: [w * 6, w * 7],
      color: ThemeColors.colorD9D9D9,

      borderType: dot.BorderType.RRect,
      radius: const Radius.circular(3),
      // padding: EdgeInsets.all(6),
      child: GestureDetector(
        onTap: () {
          if (isRedundentClick(DateTime.now())) {
            return;
          }
          showDialog(
            context: context,
            builder: (context) => FlightTicketDocUploadDialog(ctx: context),
          );
        },
        child: ClipRRect(
          borderRadius: const BorderRadius.all(
            Radius.circular(3),
          ),
          child: Consumer<FlightTicketProvider>(
            builder: (context, provider, _) {
              return Container(
                color: ThemeColors.colorFFFFFF,
                alignment: Alignment.center,
                padding: EdgeInsets.only(top: h * 30, bottom: h * 22),
                child: Column(
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset("assets/icons/upload.png",
                            height: h * 24, width: w * 29.34),
                        SizedBox(height: h * 8),
                        Text(
                          "Click to upload your document",
                          style: tsS14w400c30292F,
                        ),
                        SizedBox(height: h * 3),
                        Text(
                          "Supports : JPEG, PNG, PDF",
                          style: tsS12w400979797,
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _subTitile({required String subtitle, bool isMandatory = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: Row(
            children: [
              Text(
                subtitle,
                style: tsS14w400c30292F,
              ),
              if (isMandatory)
                Text(
                  "*",
                  style: tsS14w400cFA0000,
                ),
            ],
          ),
        ),
        SizedBox(height: h * 4),
      ],
    );
  }

  Widget _ticketDetails() {
    var item = widget.flightTicketOverviewModel?.data;
    return Container(
      padding: const EdgeInsets.all(10),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              "Ticket Details",
              style: tsS14w500c161616,
            ),
          ),
          SizedBox(
            height: 10 * h,
          ),
          Row(children: [
            _detailsWidget(
              title: "Airline",
              subTitle: item?.airline.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
            _detailsWidget(
              title: "Departure Date",
              subTitle: item?.deperatureDate.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
          ]),
          SizedBox(
            height: 10 * h,
          ),
          Row(children: [
            _detailsWidget(
              title: "Return Date",
              subTitle: item?.returnDate.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
            _detailsWidget(
              title: "No of Days",
              subTitle: item?.noOfDays.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
          ]),
          SizedBox(
            height: 10 * h,
          ),
          Row(children: [
            _detailsWidget(
              title: "Country of Departure",
              subTitle: item?.countryOfDeparture.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
            _detailsWidget(
              title: "City of Departure",
              subTitle: item?.cityOfDeperature.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
          ]),
          SizedBox(
            height: 10 * h,
          ),
          Row(children: [
            _detailsWidget(
              title: "Country of Arrival",
              subTitle: item?.countryOfArrival.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
            _detailsWidget(
              title: "City of Arrival",
              subTitle: item?.cityOfArrival.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
          ]),
          SizedBox(
            height: 10 * h,
          ),
          Row(children: [
            _detailsWidget(
              title: "Ticket Count",
              subTitle: item?.extraTicket.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
          ]),
        ],
      ),
    );
  }

  Widget _personalDetails(
      {required FlightTicketPersonalInfo? personalInfo,
      required FlightTicketCountModel? flightTicketCountModel}) {
    var item = widget.flightTicketOverviewModel?.data;
    return Container(
      padding: const EdgeInsets.all(10),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              "Personal Details",
              style: tsS14w500c161616,
            ),
          ),
          SizedBox(
            height: 10 * h,
          ),
          Row(
            children: [
              Container(
                margin: EdgeInsets.only(right: 10 * w),
                height: 42 * h,
                width: 42 * w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: ThemeColors.primaryColor,
                  image: DecorationImage(
                      image: CachedNetworkImageProvider(
                        item?.profilePic ?? "",
                      ),
                      fit: BoxFit.cover),
                ),
                child: item?.profilePic == null
                    ? Center(
                        child: Text(
                          item?.name.toString().substring(0, 1).toUpperCase() ??
                              "A",
                          style: tsS14FFFFF,
                        ),
                      )
                    : null,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "${item?.name}",
                    style: tsS14w500c2C2D33,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    "${item?.email}",
                    style: tsS12w400c949494,
                  ),
                  Text(
                    "EMP ID : ${item?.empId}",
                    style: tsS12w400c949494,
                  ),
                ],
              ),
              // _detailsWidget(date: name ?? "", title: "Name"),
            ],
          ),
          SizedBox(
            height: 10 * h,
          ),
          Divider(
            height: 1,
            color: ThemeColors.colorE8E8E8,
            endIndent: 2,
            indent: 2,
            thickness: 2,
          ),
          SizedBox(
            height: 10 * h,
          ),
          Row(
            children: [
              _detailsWidget(
                title: "Department",
                subTitle: personalInfo?.department.toString(),
                flex: 1,
                message: "Not added",
                expired: false,
              ),
              _detailsWidget(
                title: "Designation",
                subTitle: personalInfo?.designation.toString(),
                flex: 2,
                message: "Not added",
                expired: false,
              ),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          Row(children: [
            _detailsWidget(
              title: "Contact Number",
              subTitle: personalInfo?.phoneNo,
              flex: 1,
              message: "Not added",
              expired: false,
            ),
            _detailsWidget(
              title: "Dob",
              subTitle: personalInfo?.dob.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
            _detailsWidget(
              title: "Date of Joining",
              subTitle: personalInfo?.doj.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
          ]),
          SizedBox(
            height: 10 * h,
          ),
          Divider(
            height: 1,
            color: ThemeColors.colorE8E8E8,
            endIndent: 2,
            indent: 2,
            thickness: 2,
          ),
          SizedBox(
            height: 10 * h,
          ),
          Row(children: [
            _detailsWidget(
              title: "Nationality",
              subTitle: personalInfo?.nationality.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
            _detailsWidget(
              title: "Passport No",
              subTitle: personalInfo?.passport?.passportNumber.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
          ]),
          const SizedBox(
            height: 10,
          ),
          Row(children: [
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Passport Exp",
                    style: tsS12w400c949494,
                  ),
                  SizedBox(height: h * 3),
                  Row(
                    children: [
                      Text(
                        personalInfo?.passport?.status ?? "",
                        style: tsS12w500c4CD964,
                      ),
                      if (personalInfo?.passport?.status
                                  ?.toLowerCase()
                                  .trim() ==
                              "active" &&
                          personalInfo?.passport?.passportExpiry != "")
                        Text(
                          "(${personalInfo?.passport?.passportExpiry})",
                          style: tsS10w500c2C2D33,
                        ),
                    ],
                  ),
                  // SizedBox(height: h * 15),
                ],
              ),
            ),
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Visa Status",
                    style: tsS12w400c949494,
                  ),
                  SizedBox(height: h * 3),
                  Row(
                    children: [
                      Text(
                        personalInfo?.visa?.status ?? "",
                        style: tsS12w500c4CD964,
                      ),
                      if (personalInfo?.visa?.status?.toLowerCase().trim() ==
                              "active" &&
                          personalInfo?.visa?.expiry != "")
                        Text(
                          "(${personalInfo?.visa?.expiry})",
                          style: tsS10w500c2C2D33,
                        ),
                    ],
                  ),
                  // SizedBox(height: h * 15),
                ],
              ),
            ),
          ]),
          const SizedBox(
            height: 10,
          ),
          Row(children: [
            _detailsWidget(
              title: "EID Number",
              subTitle: personalInfo?.department.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
            _detailsWidget(
              title: "EID Expiry Date",
              subTitle: personalInfo?.designation.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
          ]),
          SizedBox(
            height: 10 * h,
          ),
          Divider(
            height: 1,
            color: ThemeColors.colorE8E8E8,
            endIndent: 2,
            indent: 2,
            thickness: 2,
          ),
          SizedBox(
            height: 10 * h,
          ),
          Row(children: [
            _detailsWidget(
              title: "Leave Req. Date",
              subTitle:
                  "${item?.leaveData?.startDate} to ${item?.leaveData?.endDate}",
              flex: 1,
              message: "Not added",
              expired: false,
            ),
            _detailsWidget(
              title: "Leave Type",
              subTitle: item?.leaveData?.leaveType.toString(),
              flex: 1,
              message: "Not added",
              expired: false,
            ),
          ]),
          SizedBox(
            height: 10 * h,
          ),
          Row(children: [
            _detailsWidget(
              title: "Ticket Eligible",
              subTitle:
                  flightTicketCountModel?.data?.eligibleTicket.toString() ??
                      '0',
              flex: 1,
              message: "Not added",
              expired: false,
            ),
            _detailsWidget(
              title: "Ticket Availed",
              subTitle:
                  flightTicketCountModel?.data?.takenTicket.toString() ?? '0',
              flex: 1,
              message: "Not added",
              expired: false,
            ),
            _detailsWidget(
              title: "Ticket Balance",
              subTitle:
                  flightTicketCountModel?.data?.balanceTicket.toString() ?? '0',
              flex: 1,
              message: "Not added",
              expired: false,
            ),
          ]),
          SizedBox(
            height: 15 * h,
          )
        ],
      ),
    );
  }

  Widget _detailsWidget({
    required String? title,
    required String? subTitle,
    required String? message,
    bool? expired,
    int flex = 1,
  }) {
    return Expanded(
      flex: flex,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (expired == true || subTitle == null)
            Tooltip(
              message: message,
              child: Row(
                children: [
                  Text(
                    "$title",
                    style: tsS12w400c949494,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Icon(
                    Icons.info_outline,
                    size: 15,
                    color: ThemeColors.colorF64D44,
                  )
                ],
              ),
            )
          else
            Text(
              title ?? "",
              style: tsS12w400c949494,
              overflow: TextOverflow.ellipsis,
            ),
          SizedBox(height: h * 3),
          SizedBox(
            width: 150 * w,
            child: Text(
              subTitle ?? "",
              style: tsS12w500c2C2D33,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          // SizedBox(height: h * 15),
        ],
      ),
    );
  }

  Widget _steperWidget({required int index}) {
    var item = widget.flightTicketOverviewModel?.data;
    List<int> activeIndex = [];
    for (var data in item!.apprvReject!) {
      if (data.levelStatus?.toLowerCase() == "approved" ||
          data.levelStatus?.toLowerCase() == "rejected") {
        activeIndex.add(data.id!);
      }
    }
    bool activeColor = false;
    activeColor = item.apprvReject!
        .any((e) => e.levelStatus?.toLowerCase() == "rejected");
    return Container(
      height: 100 * h,
      padding: const EdgeInsets.all(8),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: EasyStepper(
        alignment: Alignment.center,
        activeStepBorderType: BorderType.normal,
        unreachedStepBorderType: BorderType.normal,
        activeStepBorderColor: ThemeColors.colorECECEC,
        unreachedStepBorderColor: ThemeColors.colorECECEC,
        // stepBorderRadius: 1,
        activeStep: activeIndex.length - 1,
        lineStyle: LineStyle(
          activeLineColor: ThemeColors.colorECECEC,
          // progressColor: Colors.red,
          defaultLineColor: ThemeColors.colorECECEC,
          lineLength: 35 * w,
          lineType: LineType.normal,
          lineThickness: 2,
          lineSpace: 0,
          lineWidth: 0,
          unreachedLineType: LineType.normal,
        ),
        stepShape: StepShape.circle,
        // stepBorderRadius: ,
        borderThickness: 5,
        internalPadding: 2,
        padding: const EdgeInsetsDirectional.symmetric(
          horizontal: 30,
          vertical: 20,
        ),
        activeStepBackgroundColor:
            !activeColor ? ThemeColors.primaryColor : Colors.red,
        unreachedStepBackgroundColor: Colors.white,
        stepRadius: 15 * h,

        showLoadingAnimation: false,
        steps: item.apprvReject!.map((e) {
          // var ind = item?.
          return EasyStep(
            customStep: Text(
              e.levelType.toString().substring(1, 2),
            ),
            customTitle: Text(
              e.levelStatus.toString().capitalize(),
              textAlign: TextAlign.center,
              style: tsS10w400c979797,
            ),
          );
        }).toList(),

        // [
        //   EasyStep(
        //     customStep: Text("$index"),
        //     customTitle: Text(
        //       'Pending',
        //       textAlign: TextAlign.center,
        //       style: tsS10w400c979797,
        //     ),
        //   ),
        //   EasyStep(
        //     customStep: Text("$index"),
        //     customTitle: Text(
        //       'Pending',
        //       textAlign: TextAlign.center,
        //       style: tsS10w400c979797,
        //     ),
        //   ),
        //   EasyStep(
        //     customStep: Text("$index"),
        //     customTitle: Text(
        //       'Pending',
        //       textAlign: TextAlign.center,
        //       style: tsS10w400c979797,
        //     ),
        //   ),
        //   EasyStep(
        //     customStep: Text("$index"),
        //     customTitle: Text(
        //       'Pending',
        //       textAlign: TextAlign.center,
        //       style: tsS10w400c979797,
        //     ),
        //   ),
        //   EasyStep(
        //     customStep: Text("$index"),
        //     customTitle: Text(
        //       'Pending',
        //       textAlign: TextAlign.center,
        //       style: tsS10w400c979797,
        //     ),
        //   ),
        // ],
        onStepReached: (index1) => setState(() => index = index1),
      ),
    );
  }
}
