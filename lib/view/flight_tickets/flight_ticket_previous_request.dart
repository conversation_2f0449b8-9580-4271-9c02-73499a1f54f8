import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/flight_ticket_request_model.dart';
import 'package:e8_hr_portal/provider/flight_ticket_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/notification/no_item_widget.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';

class PreviousRequestFlightTicketRequest extends StatefulWidget {
  final int userId;
  const PreviousRequestFlightTicketRequest({super.key, required this.userId});

  @override
  State<PreviousRequestFlightTicketRequest> createState() =>
      _PreviousRequestFlightTicketRequestState();
}

class _PreviousRequestFlightTicketRequestState
    extends State<PreviousRequestFlightTicketRequest> {
  @override
  void initState() {
    dataLoding();
    super.initState();
  }

  @override
  void dispose() {
    var provider = Provider.of<FlightTicketProvider>(context, listen: false);
    provider.flightTicketPreviousRequestPagingController?.dispose();
    super.dispose();
  }

  void dataLoding() async {
    // EasyLoading.show();
    var provider = Provider.of<FlightTicketProvider>(context, listen: false);
    await provider.initPreviousRequest(userId: widget.userId);

    // EasyLoading.dismiss();
  }

  @override
  Widget build(BuildContext context) {
    FlightTicketProvider provider = Provider.of<FlightTicketProvider>(context);
    return HisenseScaffold(
      screenTitle: "Flight Tickets",
      onTap: () {
        provider.uniqueIds.clear();
      },
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: h * 10),
            Text(
              "Request List",
              style: tsS18w500c181818,
            ),
            SizedBox(height: h * 10),
            Expanded(
              child: PagedListView.separated(
                padding: const EdgeInsets.only(top: 10),
                pagingController:
                    provider.flightTicketPreviousRequestPagingController!,
                builderDelegate:
                    PagedChildBuilderDelegate<FlightTicketRequstListModel>(
                  noItemsFoundIndicatorBuilder: (context) {
                    return const NoItemWidget(
                      iconImage: "assets/images/recent_empty.png",
                      text1: "No Flight ticket",
                      text2: "It seems no activity happened yet.",
                      text3: "",
                    );
                  },
                  newPageProgressIndicatorBuilder: (_) {
                    return Center(
                      child: CircularProgressIndicator(
                          color: ThemeColors.color06AA37),
                    );
                  },
                  firstPageProgressIndicatorBuilder: (_) {
                    return Center(
                      child: CircularProgressIndicator(
                          color: ThemeColors.primaryColor),
                    );
                  },
                  itemBuilder: (context, item, index) {
                    return GestureDetector(
                      // onTap: () async {
                      //   if (isRedundentClick(DateTime.now())) {
                      //     return;
                      //   }

                      //   await provider.getFlightTicketOverview(
                      //       ticketId: item.id.toString());
                      //   bool isGo = await provider.getFlightTicketPersonalInfo(
                      //       userId: provider
                      //           .flightTicketOverviewModel?.data?.userId);

                      //   await provider.getFlightTicketLeveList();
                      //   await provider.getFlightTicketCount(
                      //       userId: provider
                      //           .flightTicketOverviewModel?.data?.userId);

                      //   if (isGo) {
                      //     if (!context.mounted) return;
                      //     PageNavigator.push(
                      //       context: context,
                      //       route: FlightTicketDetailsViewNew(
                      //         isForEdit: false,
                      //         isFromLevels: true,
                      //         flightTicketOverviewModel:
                      //             provider.flightTicketOverviewModel,
                      //       ),
                      //     );
                      //     // PageNavigator.push(
                      //     //   context: context,
                      //     //   route: FlightTicketNewRequest(
                      //     //     isForEdit: true,
                      //     //     isFromLevels: true,
                      //     //     flightTicketOverviewModel:
                      //     //         provider.flightTicketOverviewModel,
                      //     //   ),
                      //     // );
                      //   }
                      // },
                      child: _requestTile(
                        context: context,
                        isStateSeeMore: provider.isStateSeeMore,
                        item: item,
                      ),
                    );
                  },
                ),
                separatorBuilder: (context, index) {
                  return SizedBox(height: h * 10);
                },
              ),
            ),
            SizedBox(
              height: 20 * h,
            )
          ],
        ),
      ),
    );
  }

  Widget _detailsWidget({required String title, required String date}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: tsS12w400c949494,
        ),
        SizedBox(height: h * 3),
        Text(
          date,
          style: tsS14w500c2C2D33,
          overflow: TextOverflow.ellipsis,
        )
      ],
    );
  }

  Widget _status({required String status}) {
    TextStyle? style;
    Color? color;
    switch (status.toLowerCase()) {
      case "approved":
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
        break;
      case "rejected":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case "expired":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;

      case "pending":
        {
          style = tsS12w600cE5B900;
          color = ThemeColors.colorFFF2E2;
        }
        break;
      case "In progress":
        {
          style = tsS12W683B4FF;
          color = ThemeColors.color83B4FF.withOpacity(0.3);
        }
        break;
      case "cancelled":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      default:
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
    }
    return Container(
        height: h * 23,
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: w * 11),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          status,
          style: style,
        ));
  }

  Widget _requestTile({
    required BuildContext context,
    required bool isStateSeeMore,
    required FlightTicketRequstListModel item,
  }) {
    return Consumer<FlightTicketProvider>(
      builder: (context, provider, _) {
        FlightTicketRequstListModel element = item;
        String? name = element.sender;
        String? requestedDate = element.requestedDate;
        // String? approvedDate = element.approvedOrRejectedDate;
        // String? approvedBy = element.approvedBy;
        // String? depDate = element.deperatureDate;
        // String? retrnDate = element.returnDate;
        // String? noDays = element.noOfDays;
        // String? airline = element.airline;
        // String? cityOfDep = element.cityOfDeperature;
        // String? arrivalCountry = element.countryOfArrival;
        // String? arrivalCity = element.cityOfArrival;
        // int? uniId = element.id;
        String? email = element.email;
        String? empId = element.empId;

        // String? status = element.status;
        String? status = element.status;

        return Container(
          // height: h * 106,
          width: w * 343,
          padding: EdgeInsets.fromLTRB(w * 10, h * 10, w * 10, h * 13),
          decoration: BoxDecoration(
            color: ThemeColors.colorFFFFFF,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    margin: EdgeInsets.only(right: 10 * w),
                    height: 42 * h,
                    width: 42 * w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: ThemeColors.primaryColor,
                      image: DecorationImage(
                          image: CachedNetworkImageProvider(
                            element.profilePic ?? "",
                          ),
                          fit: BoxFit.cover),
                    ),
                    child: element.profilePic == null
                        ? Center(
                            child: Text(
                              name.toString().substring(0, 1).toUpperCase(),
                              style: tsS14FFFFF,
                            ),
                          )
                        : null,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name.toString(),
                        style: tsS14w500c2C2D33,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        "$email",
                        style: tsS12w400c949494,
                      ),
                      Text(
                        "EMP ID : $empId",
                        style: tsS12w400c949494,
                      ),
                    ],
                  ),
                  // _detailsWidget(date: name ?? "", title: "Name"),
                  const Spacer(),
                  const Spacer(flex: 2),
                  _status(
                    status:
                        "${status?[0].toUpperCase()}${status?.substring(1).toLowerCase()}",
                  ),
                ],
              ),
              SizedBox(height: h * 11),
              Divider(
                color: ThemeColors.colorE8E8E8,
                thickness: 1,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _detailsWidget(
                      date: requestedDate ?? "", title: "Requested Date"),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Align(
                        alignment: Alignment.topRight,
                        child: Text(
                          "Approved/Rejected By",
                          style: tsS12w400c949494,
                          textAlign: TextAlign.right,
                        ),
                      ),
                      SizedBox(height: h * 3),
                      SizedBox(
                        height: 30 * h,
                        child: ListView.separated(
                          shrinkWrap: true,
                          scrollDirection: Axis.horizontal,
                          itemCount: element.apprvRejData?.length ?? 2,
                          itemBuilder: (context, index) {
                            return Stack(
                              children: [
                                Container(
                                  margin: EdgeInsets.only(right: 2 * w),
                                  height: 30 * h,
                                  width: 30 * w,
                                  decoration: BoxDecoration(
                                    color: ThemeColors.primaryColor,
                                    shape: BoxShape.circle,
                                    image: DecorationImage(
                                        colorFilter: element
                                                    .apprvRejData?[index]
                                                    .levelStatus
                                                    ?.toLowerCase() ==
                                                "approved"
                                            ? null
                                            : const ColorFilter.mode(
                                                Colors.green,
                                                BlendMode.srcOver),
                                        image: CachedNetworkImageProvider(
                                          element.apprvRejData?[index]
                                                  .profilePic ??
                                              "",
                                        ),
                                        fit: BoxFit.cover),
                                  ),
                                  child:
                                      element.apprvRejData?[index].profilePic ==
                                              null
                                          ? Center(
                                              child: Text(
                                                element.apprvRejData?[index]
                                                        .userName
                                                        .toString()
                                                        .substring(0, 1)
                                                        .toUpperCase() ??
                                                    "A",
                                                style: tsS14FFFFF,
                                              ),
                                            )
                                          : null,
                                ),
                                Positioned(
                                  right: 0,
                                  bottom: 0,
                                  child: element
                                              .apprvRejData?[index].levelStatus
                                              ?.toLowerCase() !=
                                          "pending"
                                      ? Image.asset(
                                          element.apprvRejData?[index]
                                                      .levelStatus
                                                      ?.toLowerCase() ==
                                                  "approved"
                                              ? "assets/icons/tick.png"
                                              : "assets/icons/close_red.png",
                                          scale: element.apprvRejData?[index]
                                                      .levelStatus
                                                      ?.toLowerCase() ==
                                                  "approved"
                                              ? 2.5
                                              : 1.9,
                                        )
                                      : const SizedBox(),
                                ),
                              ],
                            );
                          },
                          separatorBuilder: (context, index) {
                            return SizedBox(
                              width: 2 * w,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
