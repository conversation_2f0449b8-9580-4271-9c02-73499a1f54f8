import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/helper/drop_down_widget.dart';
import 'package:e8_hr_portal/helper/search_textfield.dart';
import 'package:e8_hr_portal/model/flight_ticket_approved_list.dart';
import 'package:e8_hr_portal/provider/flight_ticket_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/flight_tickets/flight_ticket_upload_new_screen.dart';
import 'package:e8_hr_portal/view/notification/no_item_widget.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';

class FlightTicketApprovedListScreen extends StatefulWidget {
  const FlightTicketApprovedListScreen({super.key});

  @override
  State<FlightTicketApprovedListScreen> createState() =>
      _FlightTicketApprovedListScreenState();
}

class _FlightTicketApprovedListScreenState
    extends State<FlightTicketApprovedListScreen> {
  final TextEditingController searchController = TextEditingController();
  @override
  void initState() {
    dataLoding();

    super.initState();
  }

  @override
  void dispose() {
    var provider = Provider.of<FlightTicketProvider>(context, listen: false);
    provider.flightTicketApprovedPagingController?.dispose();
    super.dispose();
  }

  void dataLoding() async {
    // EasyLoading.show();
    var provider = Provider.of<FlightTicketProvider>(context, listen: false);
    await provider.initApproved();
    await provider.getAllCountries();

    // EasyLoading.dismiss();
  }

  @override
  Widget build(BuildContext context) {
    FlightTicketProvider provider = Provider.of<FlightTicketProvider>(context);
    return HisenseScaffold(
      screenTitle: 'Flight Tickets Uploads',
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: h * 15),
            Text(
              'Approved Requests',
              style: tsS18w500c181818,
            ),
            SizedBox(
              height: 10 * h,
            ),
            Consumer<FlightTicketProvider>(builder: (context, flight, _) {
              return SearchTextFieldWidget(
                onChanged: (value) {
                  flight.searchFlightTicketKeyword = value;
                  flight.currentPageFlightTicketApproved = 0;
                  flight.flightTicketApprovedPagingController?.refresh();
                },
                controller: searchController,
                prefixIcon: IconButton(
                  onPressed: () {},
                  icon: const ImageIcon(
                    AssetImage(
                      'assets/icons/search.png',
                    ),
                  ),
                ),
                hintText: 'Search ...',
              );
            }),
            SizedBox(
              height: 10 * h,
            ),
            Consumer<FlightTicketProvider>(builder: (context, flight, _) {
              return Row(
                children: [
                  Expanded(
                    child: DropdownWidgetFlightTicket(
                      hintText: 'Select department',
                      selectedValue: flight.selectedDepartment,
                      items: flight.departmentModel?.map((e) {
                        return DropdownMenuItem(
                          value: e,
                          child: Text(
                            e.name.toString(),
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        flight.selectedDepartment = value;
                        flight.currentPageFlightTicketApproved = 0;
                        flight.flightTicketApprovedPagingController?.refresh();
                      },
                    ),
                  ),
                  SizedBox(
                    width: 10 * h,
                  ),
                  Expanded(
                    child: DropdownWidgetFlightTicket(
                      hintText: 'Select designation',
                      selectedValue: flight.selectedDesignation,
                      items: flight.designationsModel?.map((e) {
                        return DropdownMenuItem(
                          value: e,
                          child: Text(
                            e.name.toString(),
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        flight.selectedDesignation = value;
                        flight.currentPageFlightTicketApproved = 0;
                        flight.flightTicketApprovedPagingController?.refresh();
                      },
                    ),
                  ),
                ],
              );
            }),
            Expanded(
              child: PagedListView.separated(
                padding: const EdgeInsets.only(top: 10),
                pagingController:
                    provider.flightTicketApprovedPagingController!,
                builderDelegate:
                    PagedChildBuilderDelegate<FlightTicketApprovedListModel>(
                  noItemsFoundIndicatorBuilder: (context) {
                    return const NoItemWidget(
                      iconImage: 'assets/images/recent_empty.png',
                      text1: 'No Flight ticket',
                      text2: 'It seems no activity happened yet.',
                      text3: '',
                    );
                  },
                  newPageProgressIndicatorBuilder: (_) {
                    return Center(
                      child: CircularProgressIndicator(
                          color: ThemeColors.color06AA37),
                    );
                  },
                  firstPageProgressIndicatorBuilder: (_) {
                    return Center(
                      child: CircularProgressIndicator(
                          color: ThemeColors.primaryColor),
                    );
                  },
                  itemBuilder: (context, item, index) {
                    return GestureDetector(
                      onTap: () async {
                        if (isRedundentClick(DateTime.now())) {
                          return;
                        }
                        await provider.getFlightTicketOverview(
                            ticketId: item.ticketRequestId.toString());
                        await provider.getFlightTicketLeveList();
                        if (!context.mounted) return;
                        PageNavigator.push(
                          context: context,
                          route: FLightTicketUploadNewScreen(
                              flightTicketOverviewModel:
                                  provider.flightTicketOverviewModel),
                        );
                      },
                      child: _requestTile(
                          context: context,
                          isStateSeeMore: provider.isStateSeeMore,
                          item: item,
                          index: index),
                    );
                  },
                ),
                separatorBuilder: (context, index) {
                  return SizedBox(height: h * 10);
                },
              ),
            ),
            SizedBox(
              height: 75 * h,
            )
          ],
        ),
      ),
    );
  }

  Widget _requestTile(
      {required BuildContext context,
      required bool isStateSeeMore,
      required FlightTicketApprovedListModel item,
      required int index}) {
    return Consumer<FlightTicketProvider>(
      builder: (context, provider, _) {
        FlightTicketApprovedListModel element = item;

        String? name = element.sender;
        String? requestedDate = element.requestedDate;
        // String? approvedDate = element.approvedOrRejectedDate;
        // String? approvedBy = element.approvedBy;
        String? depDate = element.deperatureDate;
        String? retrnDate = element.returnDate;
        String? noDays = element.noOfDays.toString();
        String? airline = element.airline;
        String? cityOfDep = element.cityOfDeperature;
        String? arrivalCountry = element.countryOfArrival;
        String? arrivalCity = element.cityOfArrival;
        int? uniId = element.id;

        // String? status = element.status;
        String? status = element.status;

        return Container(
          // height: h * 106,
          width: w * 343,
          padding: EdgeInsets.fromLTRB(w * 10, h * 10, w * 10, h * 13),
          decoration: BoxDecoration(
            color: ThemeColors.colorFFFFFF,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                // crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _detailsWidget(date: name ?? '', title: 'Name'),
                  const Spacer(),
                  _detailsWidget(
                      date: requestedDate ?? '', title: 'Requested Date'),
                  // if (status?.toLowerCase().trim() != 'pending')
                  //   _detailsWidget(
                  //     date: approvedDate ?? '',
                  //     title: status?.toLowerCase().trim() == 'approved'
                  //         ? 'Approved Date'
                  //         : 'Rejected Date',
                  //   ),
                  const Spacer(flex: 2),
                  _status(
                    status:
                        '${status?[0].toUpperCase()}${status?.substring(1).toLowerCase()}',
                  ),
                ],
              ),
              SizedBox(height: h * 11),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // if (approvedBy == null) const SizedBox(),
                  // if (approvedBy != null &&
                  //     status?.toLowerCase().trim() != 'pending')
                  // _detailsWidget(
                  //   date: approvedBy 'asdsad',
                  //   title: status?.toLowerCase().trim() != 'approved'
                  //       ? 'Rejected By'
                  //       : 'Approved By',
                  // ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Approved/Rejected By',
                        style: tsS12w400c949494,
                      ),
                      SizedBox(height: h * 3),
                      SizedBox(
                        width: 150 * h,
                        height: 30 * h,
                        child: ListView.separated(
                          shrinkWrap: true,
                          scrollDirection: Axis.horizontal,
                          itemCount: element.apprvRejData?.length ?? 2,
                          itemBuilder: (context, index) {
                            return Stack(
                              children: [
                                Container(
                                  margin: EdgeInsets.only(right: 4 * h),
                                  height: 30 * h,
                                  width: 30 * w,
                                  decoration: BoxDecoration(
                                    color: ThemeColors.primaryColor,
                                    shape: BoxShape.circle,
                                    image: DecorationImage(
                                      image: CachedNetworkImageProvider(
                                        element.apprvRejData?[index]
                                                .profilePic ??
                                            '',
                                      ),
                                    ),
                                  ),
                                  child:
                                      element.apprvRejData?[index].profilePic ==
                                              null
                                          ? Center(
                                              child: Text(
                                                element.apprvRejData?[index]
                                                        .userName
                                                        .toString()
                                                        .substring(0, 1)
                                                        .toUpperCase() ??
                                                    'A',
                                                style: tsS14FFFFF,
                                              ),
                                            )
                                          : null,
                                ),
                                Positioned(
                                  right: 0,
                                  bottom: 0,
                                  child: element
                                              .apprvRejData?[index].levelStatus
                                              ?.toLowerCase() !=
                                          'pending'
                                      ? Image.asset(
                                          element.apprvRejData?[index]
                                                      .levelStatus
                                                      ?.toLowerCase() ==
                                                  'approved'
                                              ? 'assets/icons/tick.png'
                                              : 'assets/icons/close_red.png',
                                          scale: 2.5,
                                        )
                                      : const SizedBox(),
                                ),
                              ],
                            );
                          },
                          separatorBuilder: (context, index) {
                            return SizedBox(
                              width: 2 * w,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  if (element.tktUploadData
                          ?.any((element) => element.fileType == 'Ticket') ??
                      false)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          'Ticket Uploaded',
                          style: tsS12w400c949494,
                        ),
                        SizedBox(
                          height: 10 * h,
                        ),
                        Image.asset(
                          'assets/icons/tick-circle.png',
                          scale: 2,
                        )
                      ],
                    ),

                  if (!provider.uniqueIds.contains(uniId!))
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                        onPressed: () {
                          provider.uniqueIds.add(uniId);
                          provider.isStateSeeMore = true;

                          isStateSeeMore = true;
                        },
                        child: Text(
                          'See more',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: ThemeColors.colorFCC400,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              if (provider.uniqueIds.contains(uniId))
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: h * 11),
                    ///////reason for rejection row
                    if (status?.trim().toLowerCase() == 'rejected' &&
                        element.rejectedComment != null)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Reason for rejection',
                            style: tsS12w400c949494,
                          ),
                          SizedBox(height: h * 3),
                          Text(
                            element.rejectedComment!,
                            // 'On the other hand, we denounce with righteous ndignation and dislike men who are so beguiled and demoralized by the charms of pleasure.',
                            style: tsS14w500c2C2D33,
                          ),
                          SizedBox(height: h * 11),
                        ],
                      ),
                    Row(
                      children: [
                        Expanded(
                          child: _detailsWidget(
                            title: 'Dep. Date',
                            date: depDate ?? '',
                          ),
                        ),
                        Expanded(
                          child: _detailsWidget(
                            title: 'Rtn. Date',
                            date: retrnDate ?? '',
                          ),
                        ),
                        Expanded(
                          child: _detailsWidget(
                            title: 'No. Days',
                            date: '$noDays ${noDays == '1' ? 'Day' : 'Days'}',
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: h * 20),
                    Row(
                      children: [
                        Expanded(
                          child: _detailsWidget(
                            title: 'Airline',
                            date: airline ?? '',
                          ),
                        ),
                        Expanded(
                          child: _detailsWidget(
                            title: 'City of Dep.',
                            date: cityOfDep ?? '',
                          ),
                        ),
                        Expanded(
                          child: _detailsWidget(
                            title: 'Arrival Country',
                            date: arrivalCountry ?? '',
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: h * 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        _detailsWidget(
                            title: 'Arrival City', date: arrivalCity ?? ''),
                        TextButton(
                          onPressed: () {
                            provider.uniqueIds.remove(uniId);
                            provider.isStateSeeMore = false;
                            isStateSeeMore = false;
                          },
                          child: Text(
                            'See less',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: ThemeColors.colorFCC400,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                )
            ],
          ),
        );
      },
    );
  }

  // Widget _popUpMenuItem({required BuildContext context}) {
  Widget _detailsWidget({required String title, required String date}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: tsS12w400c949494,
        ),
        SizedBox(height: h * 3),
        Text(
          date,
          style: tsS14w500c2C2D33,
          overflow: TextOverflow.ellipsis,
        )
      ],
    );
  }

  Widget _status({required String status}) {
    TextStyle? style;
    Color? color;
    switch (status) {
      case 'Approved':
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
        break;
      case 'Rejected':
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case 'Expired':
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;

      case 'Pending':
        {
          style = tsS12w600cE5B900;
          color = ThemeColors.colorFFF2E2;
        }
        break;
      case 'In progress':
        {
          style = tsS12W683B4FF;
          color = ThemeColors.color83B4FF.withOpacity(0.3);
        }
        break;
      case 'Cancelled':
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      default:
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
    }
    return Container(
        height: h * 23,
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: w * 11),
        decoration: BoxDecoration(
          // color: status.toLowerCase().trim() == 'approved'
          //     ? ThemeColors.color32936F.withOpacity(0.16)
          //     : status.toLowerCase().trim() == 'rejected'
          //         ? ThemeColors.colorF64D44.withOpacity(0.15)
          //         : ThemeColors.colorFFF2E2,
          color: color,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          status,
          style: style,
          // style: status.toLowerCase().trim() == 'approved'
          //     ? tsS12w600c519C66
          //     : status.toLowerCase().trim() == 'rejected'
          //         ? tsS12w600cF64D44
          //         : tsS12w600cE5B900),
        ));
  }
}
