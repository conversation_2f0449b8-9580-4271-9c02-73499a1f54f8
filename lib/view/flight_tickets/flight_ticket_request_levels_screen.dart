import 'package:cached_network_image/cached_network_image.dart';
import 'package:e8_hr_portal/model/flight_ticket_request_model.dart';
import 'package:e8_hr_portal/provider/flight_ticket_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/flight_tickets/flight_ticket_new_request.dart';
import 'package:e8_hr_portal/view/notification/no_item_widget.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';

class FlightTicketRequestLevelScreen extends StatefulWidget {
  const FlightTicketRequestLevelScreen({super.key});

  @override
  State<FlightTicketRequestLevelScreen> createState() =>
      _FlightTicketRequestLevelScreenState();
}

class _FlightTicketRequestLevelScreenState
    extends State<FlightTicketRequestLevelScreen> {
  @override
  void initState() {
    dataLoding();

    super.initState();
  }

  @override
  void dispose() {
    var provider = Provider.of<FlightTicketProvider>(context, listen: false);
    provider.flightTicketRequestPagingController?.dispose();
    super.dispose();
  }

  void dataLoding() async {
    // EasyLoading.show();
    var provider = Provider.of<FlightTicketProvider>(context, listen: false);
    await provider.initRequest();

    await provider.getAllCountries();

    // EasyLoading.dismiss();
  }

  @override
  Widget build(BuildContext context) {
    FlightTicketProvider provider = Provider.of<FlightTicketProvider>(context);
    return HisenseScaffold(
      screenTitle: "Flight Ticket Requests",
      onTap: () {
        provider.uniqueIds.clear();
      },

      // actions: [_requiestNewButton(context: context)],
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: h * 15),
            Text(
              "Flight Ticket Requests",
              style: tsS18w500c181818,
            ),
            Expanded(
              child: PagedListView.separated(
                padding: const EdgeInsets.only(top: 10),
                pagingController: provider.flightTicketRequestPagingController!,
                builderDelegate:
                    PagedChildBuilderDelegate<FlightTicketRequstListModel>(
                  noItemsFoundIndicatorBuilder: (context) {
                    return const NoItemWidget(
                      iconImage: "assets/images/recent_empty.png",
                      text1: "No Flight ticket",
                      text2: "It seems no activity happened yet.",
                      text3: "",
                    );
                  },
                  newPageProgressIndicatorBuilder: (_) {
                    return Center(
                      child: CircularProgressIndicator(
                          color: ThemeColors.color06AA37),
                    );
                  },
                  firstPageProgressIndicatorBuilder: (_) {
                    return Center(
                      child: CircularProgressIndicator(
                          color: ThemeColors.primaryColor),
                    );
                  },
                  itemBuilder: (context, item, index) {
                    return GestureDetector(
                      onTap: () async {
                        if (isRedundentClick(DateTime.now())) {
                          return;
                        }
                        // provider.getFlightTicketOverview();
                        await provider.getAirlineList();
                        await provider.getFlightTicketOverview(
                            ticketId: item.id.toString());
                        bool isGo = await provider.getFlightTicketPersonalInfo(
                            userId: provider
                                .flightTicketOverviewModel?.data?.userId);

                        await provider.getFlightTicketLeveList();
                        await provider.getFlightTicketCount(
                            userId: provider
                                .flightTicketOverviewModel?.data?.userId);
                        // provider.selectedFlightTicketLeaveModel = "21"
                        if (isGo) {
                          if (!context.mounted) return;
                          PageNavigator.push(
                            context: context,
                            route: FlightTicketNewRequest(
                              isForEdit: true,
                              isFromLevels: true,
                              flightTicketOverviewModel:
                                  provider.flightTicketOverviewModel,
                            ),
                          );
                        }
                      },
                      child: _requestTile(
                        context: context,
                        isStateSeeMore: provider.isStateSeeMore,
                        item: item,
                      ),
                    );
                  },
                ),
                separatorBuilder: (context, index) {
                  return SizedBox(height: h * 10);
                },
              ),
            ),
            SizedBox(
              height: 20 * h,
            )
          ],
        ),
      ),
    );
  }

  // Widget _requiestNewButton({required BuildContext context}) {
  //   return Padding(
  //     padding: EdgeInsets.symmetric(vertical: 0, horizontal: w * 10),
  //     child: TextButton(
  //       onPressed: () async {
  //         FlightTicketProvider provider =
  //             Provider.of<FlightTicketProvider>(context, listen: false);
  //         EasyLoading.show();
  //         await provider.getAirlineList();
  //         bool isGo = await provider.getFlightTicketPersonalInfo();
  //         if (isGo) {
  //           // ignore: use_build_context_synchronously
  //           PageNavigator.push(
  //             context: context,
  //             route: const FlightTicketNewRequest(
  //               isFromLevels: true,
  //             ),
  //           );
  //         }
  //         EasyLoading.dismiss();
  //       },
  //       style: ElevatedButton.styleFrom(
  //         backgroundColor: ThemeColors.colorTransparent,
  //         minimumSize: const Size(10, 36),
  //       ),
  //       child: Text(
  //         "Request New",
  //         style: tsS14w500cFFFFFF,
  //       ),
  //     ),
  //   );
  // }

  // Widget _popUpMenuItem({required BuildContext context}) {
  Widget _detailsWidget({required String title, required String date}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: tsS12w400c949494,
        ),
        SizedBox(height: h * 3),
        Text(
          date,
          style: tsS14w500c2C2D33,
          overflow: TextOverflow.ellipsis,
        )
      ],
    );
  }

  Widget _status({required String status}) {
    TextStyle? style;
    Color? color;
    switch (status.toLowerCase()) {
      case "approved":
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
        break;
      case "rejected":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      case "expired":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;

      case "pending":
        {
          style = tsS12w600cE5B900;
          color = ThemeColors.colorFFF2E2;
        }
        break;
      case "In progress":
        {
          style = tsS12W683B4FF;
          color = ThemeColors.color83B4FF.withOpacity(0.3);
        }
        break;
      case "cancelled":
        {
          style = tsS12w600cF64D44;
          color = ThemeColors.colorF64D44.withOpacity(0.15);
        }
        break;
      default:
        {
          style = tsS12w600c519C66;
          color = ThemeColors.color32936F.withOpacity(0.16);
        }
    }
    return Container(
        height: h * 23,
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: w * 11),
        decoration: BoxDecoration(
          // color: status.toLowerCase().trim() == "approved"
          //     ? ThemeColors.color32936F.withOpacity(0.16)
          //     : status.toLowerCase().trim() == "rejected"
          //         ? ThemeColors.colorF64D44.withOpacity(0.15)
          //         : ThemeColors.colorFFF2E2,
          color: color,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          status,
          style: style,
          // style: status.toLowerCase().trim() == "approved"
          //     ? tsS12w600c519C66
          //     : status.toLowerCase().trim() == "rejected"
          //         ? tsS12w600cF64D44
          //         : tsS12w600cE5B900),
        ));
  }

  Widget _requestTile({
    required BuildContext context,
    required bool isStateSeeMore,
    required FlightTicketRequstListModel item,
  }) {
    return Consumer<FlightTicketProvider>(
      builder: (context, provider, _) {
        FlightTicketRequstListModel element = item;
        String? name = element.sender;
        String? requestedDate = element.requestedDate;
        // String? approvedDate = element.approvedOrRejectedDate;
        // String? approvedBy = element.approvedBy;
        String? depDate = element.deperatureDate;
        String? retrnDate = element.returnDate;
        String? noDays = element.noOfDays;
        String? airline = element.airline;
        String? cityOfDep = element.cityOfDeperature;
        String? arrivalCountry = element.countryOfArrival;
        String? arrivalCity = element.cityOfArrival;
        int? uniId = element.id;

        // String? status = element.status;
        String? status = element.status;

        return Container(
          // height: h * 106,
          width: w * 343,
          padding: EdgeInsets.fromLTRB(w * 10, h * 10, w * 10, h * 13),
          decoration: BoxDecoration(
            color: ThemeColors.colorFFFFFF,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                // crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _detailsWidget(date: name ?? "", title: "Name"),
                  const Spacer(),
                  _detailsWidget(
                      date: requestedDate ?? "", title: "Requested Date"),
                  // if (status?.toLowerCase() != "pending")
                  //   _detailsWidget(
                  //     date: approvedDate ?? "",
                  //     title: status?.toLowerCase().trim() == "approved"
                  //         ? "Approved Date"
                  //         : "Rejected Date",
                  //   ),
                  const Spacer(flex: 2),
                  _status(
                    status:
                        "${status?[0].toUpperCase()}${status?.substring(1).toLowerCase()}",
                  ),
                ],
              ),
              SizedBox(height: h * 11),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // if (approvedBy == null) const SizedBox(),
                  // if (approvedBy != null &&
                  //     status?.toLowerCase().trim() != "pending")
                  // _detailsWidget(
                  //   date: approvedBy "asdsad",
                  //   title: status?.toLowerCase().trim() != "approved"
                  //       ? "Rejected By"
                  //       : "Approved By",
                  // ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Approved/Rejected By",
                        style: tsS12w400c949494,
                      ),
                      SizedBox(height: h * 3),
                      SizedBox(
                        width: 150 * h,
                        height: 30 * h,
                        child: ListView.separated(
                          shrinkWrap: true,
                          scrollDirection: Axis.horizontal,
                          itemCount: element.apprvRejData?.length ?? 2,
                          itemBuilder: (context, index) {
                            return Stack(
                              children: [
                                Container(
                                  margin: EdgeInsets.only(right: 4 * h),
                                  height: 30 * h,
                                  width: 30 * w,
                                  decoration: BoxDecoration(
                                    color: ThemeColors.primaryColor,
                                    shape: BoxShape.circle,
                                    image: DecorationImage(
                                        image: CachedNetworkImageProvider(
                                          element.apprvRejData?[index]
                                                  .profilePic ??
                                              "",
                                        ),
                                        fit: BoxFit.cover),
                                  ),
                                  child:
                                      element.apprvRejData?[index].profilePic ==
                                              null
                                          ? Center(
                                              child: Text(
                                                element.apprvRejData?[index]
                                                        .userName
                                                        .toString()
                                                        .substring(0, 1)
                                                        .toUpperCase() ??
                                                    "A",
                                                style: tsS14FFFFF,
                                              ),
                                            )
                                          : null,
                                ),
                                Positioned(
                                  right: 0,
                                  bottom: 0,
                                  child: element
                                              .apprvRejData?[index].levelStatus
                                              ?.toLowerCase() !=
                                          "pending"
                                      ? Image.asset(
                                          element.apprvRejData?[index]
                                                      .levelStatus
                                                      ?.toLowerCase() ==
                                                  "approved"
                                              ? "assets/icons/tick.png"
                                              : "assets/icons/close_red.png",
                                          scale: element.apprvRejData?[index]
                                                      .levelStatus
                                                      ?.toLowerCase() ==
                                                  "approved"
                                              ? 2.5
                                              : 1.9,
                                        )
                                      : const SizedBox(),
                                ),
                              ],
                            );
                          },
                          separatorBuilder: (context, index) {
                            return SizedBox(
                              width: 2 * w,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  if (element.tktUploadData
                          ?.any((element) => element.fileType == "Ticket") ??
                      false)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Ticket Uploaded",
                          style: tsS12w400c949494,
                        ),
                        SizedBox(
                          height: 10 * h,
                        ),
                        Image.asset(
                          "assets/icons/tick-circle.png",
                          scale: 2,
                        )
                      ],
                    ),

                  if (!provider.uniqueIds.contains(uniId!))
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                        onPressed: () {
                          provider.uniqueIds.add(uniId);
                          provider.isStateSeeMore = true;

                          isStateSeeMore = true;
                        },
                        child: Text(
                          "See more",
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: ThemeColors.colorFCC400,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              if (provider.uniqueIds.contains(uniId))
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: h * 11),
                    ///////reason for rejection row
                    if (status?.trim().toLowerCase() == "rejected" &&
                        element.rejectedComment != null)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Reason for rejection",
                            style: tsS12w400c949494,
                          ),
                          SizedBox(height: h * 3),
                          Text(
                            element.rejectedComment!,
                            // "On the other hand, we denounce with righteous ndignation and dislike men who are so beguiled and demoralized by the charms of pleasure.",
                            style: tsS14w500c2C2D33,
                          ),
                          SizedBox(height: h * 11),
                        ],
                      ),
                    Row(
                      children: [
                        Expanded(
                          child: _detailsWidget(
                            title: "Dep. Date",
                            date: depDate ?? "",
                          ),
                        ),
                        Expanded(
                          child: _detailsWidget(
                            title: "Rtn. Date",
                            date: retrnDate ?? "",
                          ),
                        ),
                        Expanded(
                          child: _detailsWidget(
                            title: "No. Days",
                            date:
                                "${noDays ?? ""} ${noDays == "1" ? "Day" : "Days"}",
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: h * 20),
                    Row(
                      children: [
                        Expanded(
                          child: _detailsWidget(
                            title: "Airline",
                            date: airline ?? "",
                          ),
                        ),
                        Expanded(
                          child: _detailsWidget(
                            title: "City of Dep.",
                            date: cityOfDep ?? "",
                          ),
                        ),
                        Expanded(
                          child: _detailsWidget(
                            title: "Arrival Country",
                            date: arrivalCountry ?? "",
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: h * 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        _detailsWidget(
                            title: "Arrival City", date: arrivalCity ?? ""),
                        TextButton(
                          onPressed: () {
                            provider.uniqueIds.remove(uniId);
                            provider.isStateSeeMore = false;
                            isStateSeeMore = false;
                          },
                          child: Text(
                            "See less",
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: ThemeColors.colorFCC400,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                )
            ],
          ),
        );
      },
    );
  }
}
