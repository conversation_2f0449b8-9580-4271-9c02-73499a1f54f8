import 'package:dotted_border/dotted_border.dart' as dot;
import 'package:e8_hr_portal/model/flight_ticket_overview_model.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/provider/flight_ticket_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/flight_tickets/widget/flight_ticket_upload-dialoge.dart';
import 'package:e8_hr_portal/view/widgets/general_dialog_box.dart';
import 'package:e8_hr_portal/view/widgets/hisense_text_form_field.dart';
import 'package:e8_hr_portal/widgets/hisense_scaffold.dart';
import 'package:easy_stepper/easy_stepper.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class FLightTicketUploadNewScreen extends StatefulWidget {
  final FlightTicketOverviewModel? flightTicketOverviewModel;
  const FLightTicketUploadNewScreen(
      {super.key, required this.flightTicketOverviewModel});

  @override
  State<FLightTicketUploadNewScreen> createState() =>
      _FLightTicketUploadNewScreenState();
}

class _FLightTicketUploadNewScreenState
    extends State<FLightTicketUploadNewScreen> {
  final _uploadQutationFormKey = GlobalKey<FormState>();
  TextEditingController flightRemarksController = TextEditingController();
  TextEditingController fileNameController = TextEditingController();

  @override
  void initState() {
    var flightTicketProvider = context.read<FlightTicketProvider>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      flightTicketProvider.uploadFileImage = null;
      flightTicketProvider.pickedFile = null;
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return HisenseScaffold(
      screenTitle: "Flight Tickets",
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: GestureDetector(
          onTap: () {
            hideKeyboard(context);
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: h * 10),
              Text("Request Details", style: tsS18w500c181818),
              SizedBox(height: 10 * h),
              _steperWidget(index: 1),
              SizedBox(height: 10 * h),
              _qutationDetails(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _qutationDetails() {
    return Container(
      padding: const EdgeInsets.all(10),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text("Upload Ticket", style: tsS14w500c161616),
          ),
          SizedBox(height: 10 * h),
          Consumer<FlightTicketProvider>(
            builder: (context, flight, _) {
              // if (flight.flightTicketOverviewModel?.data?.apprvReject?.first
              //             .userId ==
              //         LoginModel.uid &&
              //     (flight.flightTicketOverviewModel?.data?.status
              //             ?.toLowerCase() !=
              //         "pending")) {
              //   return const SizedBox();
              // }

              if (flight.flightTicketOverviewModel?.data?.apprvReject?.first
                          .userId ==
                      LoginModel.uid ||
                  (flight.flightTicketOverviewModel?.data?.status
                              ?.toLowerCase() ==
                          "approved" &&
                      LoginModel.isAdmin == true
                  // &&
                  // widget.isFromUpload

                  )) {
                return Column(
                  children: [
                    // _subTitile(
                    //   subtitle: "Upload Tickets",
                    //   isMandatory: true,
                    // ),
                    Form(
                      key: _uploadQutationFormKey,
                      child: HisenseTextFormField(
                        controller: fileNameController,
                        hintText: "Ticket Name",
                        hintStyle: tsS14w400454444,
                        textStyle: tsS14w400454444,
                        // keyboardType: TextInputType.text,
                        validator: Validator.text,
                        enabled: true,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: w * 11,
                          vertical: h * 12,
                        ),
                      ),
                    ),
                    const SizedBox(height: 10),
                    _uploadYourDocumentWidget(),
                  ],
                );
              } else {
                return const SizedBox();
              }
            },
          ),
          SizedBox(height: 10 * h),
          Consumer<FlightTicketProvider>(
            builder: (context, flight, _) {
              if (flight.uploadFileImage?.name == null &&
                  flight.pickedFile?.name == null) {
                return const SizedBox();
              }
              return Row(
                children: [
                  Container(
                    height: 40 * h,
                    width: 250 * w,
                    padding: const EdgeInsets.all(8),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(5),
                        border: Border.all(color: ThemeColors.colorE6E6E6)),
                    child: Text(
                      flight.uploadFileImage?.name ??
                          flight.pickedFile?.name ??
                          "unknown",
                      style: tsS14BN,
                    ),
                  ),
                  InkWell(
                    onTap: () async {
                      if (isRedundentClick(DateTime.now())) {
                        return;
                      }

                      if (_uploadQutationFormKey.currentState!.validate()) {
                        // if (fileNameController.text.isNotEmpty) {
                        // if (flight.flightTicketOverviewModel?.data?.apprvReject
                        //         ?.first.userId ==
                        //     LoginModel.uid) {
                        //   await flight.getFlightTicketUploadQuotation(
                        //     title: fileNameController.text,
                        //     ticketId:
                        //         widget.flightTicketOverviewModel?.data?.id ?? 1,
                        //   );
                        //   fileNameController.clear();
                        // } else {
                        if (flight.flightTicketOverviewModel?.data?.status
                                    ?.toLowerCase() ==
                                "approved" &&
                            LoginModel.isAdmin == true) {
                          await flight.getFlightTicketUploadTicket(
                            title: fileNameController.text,
                            ticketId:
                                widget.flightTicketOverviewModel?.data?.id ?? 1,
                          );
                          fileNameController.clear();
                          // }
                        }

                        // flight.uploadFileImage = null;
                        // flight.pickedFile = null;
                        // } else {
                        //   showToastText("Please enter file titile");
                        // }
                      }
                    },
                    child: Container(
                      height: 40,
                      width: 40,
                      margin: EdgeInsets.only(left: 7 * h),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(5),
                        border: Border.all(color: ThemeColors.colorE6E6E6),
                      ),
                      child: Icon(
                        Icons.add,
                        color: ThemeColors.colorFD5E5A,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
          SizedBox(height: 10 * h),
          Consumer<FlightTicketProvider>(
            builder: (context, flight, _) {
              if ((flight.flightTicketOverviewModel?.data == null &&
                      flight.flightTicketOverviewModel?.data?.tktUploadData ==
                          null) ||
                  flight.flightTicketOverviewModel!.data!.tktUploadData!
                      .isEmpty) {
                return const SizedBox();
              }
              return Column(
                children: [
                  ListView.separated(
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: flight
                        .flightTicketOverviewModel!.data!.tktUploadData!.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                            color: ThemeColors.color7E7E7E.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: ThemeColors.colorE6E6E6)),
                        child: Row(
                          children: [
                            Text(
                                flight.flightTicketOverviewModel!.data!
                                        .tktUploadData![index].title ??
                                    "",
                                style: tsS14BN),
                            const Spacer(),
                            InkWell(
                              onTap: () {
                                if (isRedundentClick(DateTime.now())) {
                                  return;
                                }
                                flight.downloadDocument(
                                    url: flight.flightTicketOverviewModel!.data!
                                        .tktUploadData![index].file
                                        .toString(),
                                    fileName: flight.flightTicketOverviewModel!
                                        .data!.tktUploadData![index].title
                                        .toString());
                              },
                              child: Icon(
                                Icons.file_download_outlined,
                                color: ThemeColors.primaryColor,
                              ),
                            ),
                            // if (flight.flightTicketOverviewModel?.data
                            //             ?.apprvReject?.first.userId ==
                            //         LoginModel.uid &&
                            //     (flight.flightTicketOverviewModel?.data
                            //             ?.status
                            //             ?.toLowerCase() ==
                            //         "pending"))
                            InkWell(
                              onTap: () {
                                if (isRedundentClick(DateTime.now())) {
                                  return;
                                }
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    return GeneralConfirmationDialog(
                                      onYesStyle: tsS14w500c475366,
                                      onNoStyle: tsS14w500c475366,
                                      onNoPressed: () {
                                        Navigator.pop(context);
                                      },
                                      onYesPressed: () async {
                                        if (isRedundentClick(DateTime.now())) {
                                          return;
                                        }

                                        final navigator = Navigator.of(context);
                                        flight.flightTicketQuatationDelete(
                                            ticketId: widget
                                                    .flightTicketOverviewModel
                                                    ?.data
                                                    ?.id ??
                                                1,
                                            fileType: flight
                                                .flightTicketOverviewModel!
                                                .data!
                                                .tktUploadData![index]
                                                .fileType
                                                .toString(),
                                            attachmentId: flight
                                                .flightTicketOverviewModel!
                                                .data!
                                                .tktUploadData![index]
                                                .id
                                                .toString());

                                        navigator.pop();
                                      },
                                      title: "Confirmation",
                                      content:
                                          "Are you sure do you want to delete this file ?",
                                      acceptLabel: "Yes",
                                      cancelLabel: "No",
                                    );
                                  },
                                );
                              },
                              child: Container(
                                alignment: Alignment.center,
                                height: 20,
                                width: 20,
                                margin: EdgeInsets.only(left: 7 * h),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: ThemeColors.colorE6E6E6,
                                    width: 2,
                                  ),
                                ),
                                child: Icon(
                                  Icons.close,
                                  size: 15,
                                  color: ThemeColors.colorC2C2C2,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                    separatorBuilder: (context, index) => const SizedBox(
                      height: 10,
                    ),
                  ),
                  //    Consumer<FlightTicketProvider>(builder: (context, flight, _) {
                  // if (flight.uploadFileImage?.name == null &&
                  //     flight.pickedFile?.name == null) {
                  //   return const SizedBox();
                  // }
                  // return Row(
                  //   children: [
                  //     Container(
                  //       height: 40 * h,
                  //       width: 250 * w,
                  //       padding: const EdgeInsets.all(8),
                  //       decoration: BoxDecoration(
                  //           color: Colors.white,
                  //           borderRadius: BorderRadius.circular(5),
                  //           border: Border.all(color: ThemeColors.colorE6E6E6)),
                  //       child: Text(
                  //         flight.uploadFileImage?.name ??
                  //             flight.pickedFile?.name ??
                  //             "unknown",
                  //         style: tsS14BN,
                  //       ),
                  //     ),
                  //     InkWell(
                  //       onTap: () async {
                  //         if (_uploadQutationFormKey.currentState!.validate()) {
                  //           // if (fileNameController.text.isNotEmpty) {
                  //           if (flight.flightTicketOverviewModel?.data
                  //                       ?.apprvReject?.first.userId ==
                  //                   LoginModel.uid &&
                  //               !widget.isFromUpload) {
                  //             await flight.getFlightTicketUploadQuotation(
                  //               title: fileNameController.text,
                  //               ticketId: widget.ticketId ?? 1,
                  //             );
                  //             fileNameController.clear();
                  //           } else {
                  //             if (flight.flightTicketOverviewModel?.data?.status
                  //                         ?.toLowerCase() ==
                  //                     "approved" &&
                  //                 LoginModel.isAdmin == true) {
                  //               await flight.getFlightTicketUploadTicket(
                  //                 title: fileNameController.text,
                  //                 ticketId: widget.ticketId ?? 1,
                  //               );
                  //               fileNameController.clear();
                  //             }
                  //           }

                  //           // flight.uploadFileImage = null;
                  //           // flight.pickedFile = null;
                  //           // } else {
                  //           //   showToastText("Please enter file titile");
                  //           // }
                  //         }
                  //       },
                  //       child: Container(
                  //         height: 40,
                  //         width: 40,
                  //         margin: EdgeInsets.only(left: 7 * h),
                  //         decoration: BoxDecoration(
                  //           color: Colors.white,
                  //           borderRadius: BorderRadius.circular(5),
                  //           border: Border.all(color: ThemeColors.colorE6E6E6),
                  //         ),
                  //         child: Icon(
                  //           Icons.add,
                  //           color: ThemeColors.colorFD5E5A,
                  //         ),
                  //       ),
                  //     ),
                  //   ],
                  // );
                  // }),
                ],
              );
            },
          ),
          SizedBox(height: 10 * h)
        ],
      ),
    );
  }

  Widget _uploadYourDocumentWidget() {
    return dot.DottedBorder(
      dashPattern: [w * 6, w * 7],
      color: ThemeColors.colorD9D9D9,

      borderType: dot.BorderType.RRect,
      radius: const Radius.circular(3),
      // padding: EdgeInsets.all(6),
      child: GestureDetector(
        onTap: () {
          if (isRedundentClick(DateTime.now())) {
            return;
          }
          showDialog(
            context: context,
            builder: (context) => FlightTicketDocUploadDialog(ctx: context),
          );
        },
        child: ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(3)),
          child: Consumer<FlightTicketProvider>(
            builder: (context, provider, _) {
              return Container(
                color: ThemeColors.colorFFFFFF,
                alignment: Alignment.center,
                padding: EdgeInsets.only(top: h * 30, bottom: h * 22),
                child: Column(
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset("assets/icons/upload.png",
                            height: h * 24, width: w * 29.34),
                        SizedBox(height: h * 8),
                        Text("Click to upload your document",
                            style: tsS14w400c30292F),
                        SizedBox(height: h * 3),
                        Text("Supports : JPEG, PNG, PDF",
                            style: tsS12w400979797),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _steperWidget({required int index}) {
    var item = widget.flightTicketOverviewModel?.data;
    List<int> activeIndex = [];
    for (var data in item!.apprvReject!) {
      if (data.levelStatus?.toLowerCase() == "approved" ||
          data.levelStatus?.toLowerCase() == "rejected") {
        activeIndex.add(data.id!);
      }
    }
    bool activeColor = false;
    activeColor = item.apprvReject!
        .any((e) => e.levelStatus?.toLowerCase() == "rejected");
    return Container(
      height: 100 * h,
      padding: const EdgeInsets.all(8),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: EasyStepper(
        alignment: Alignment.center,
        activeStepBorderType: BorderType.normal,
        unreachedStepBorderType: BorderType.normal,
        // activeStepBorderColor: ThemeColors.colorECECEC,
        unreachedStepBorderColor: ThemeColors.colorECECEC,
        // stepBorderRadius: 1,
        activeStep: activeIndex.length - 1,
        lineStyle: LineStyle(
          activeLineColor: ThemeColors.colorECECEC,
          // progressColor: Colors.red,
          defaultLineColor: ThemeColors.colorECECEC,
          lineLength: 35 * w,
          lineType: LineType.normal,
          lineThickness: 2,
          lineSpace: 0,
          lineWidth: 0,
          unreachedLineType: LineType.normal,
        ),
        stepShape: StepShape.circle,
        // stepBorderRadius: ,
        borderThickness: 5,
        internalPadding: 2,
        padding: const EdgeInsetsDirectional.symmetric(
          horizontal: 30,
          vertical: 20,
        ),

        activeStepBackgroundColor:
            !activeColor ? ThemeColors.primaryColor : Colors.red,
        unreachedStepBackgroundColor: Colors.white,
        stepRadius: 15 * h,

        showLoadingAnimation: false,
        steps: item.apprvReject!.map((e) {
          // var ind = item?.
          return EasyStep(
            customStep: Text(e.levelType.toString().substring(1, 2)),
            customTitle: Text(
              '${e.levelStatus}',
              textAlign: TextAlign.center,
              style: tsS10w400c979797,
            ),
          );
        }).toList(),

        onStepReached: (index1) => setState(() => index = index1),
      ),
    );
  }

  // Widget _subTitile({required String subtitle, bool isMandatory = false}) {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       Align(
  //         alignment: Alignment.topLeft,
  //         child: Row(
  //           children: [
  //             Text(
  //               subtitle,
  //               style: tsS14w400c30292F,
  //             ),
  //             if (isMandatory)
  //               Text(
  //                 "*",
  //                 style: tsS14w400cFA0000,
  //               ),
  //           ],
  //         ),
  //       ),
  //       SizedBox(height: h * 4),
  //     ],
  //   );
  // }
}
