import 'package:e8_hr_portal/provider/flight_ticket_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class FlightTicketDocUploadDialog extends StatelessWidget {
  final BuildContext ctx;
  const FlightTicketDocUploadDialog({super.key, required this.ctx});

  @override
  Widget build(BuildContext context) {
    FlightTicketProvider provider =
        Provider.of<FlightTicketProvider>(context, listen: false);
    return AlertDialog(
      title: const Text('Options'),
      content: SingleChildScrollView(
        child: ListBody(
          children: [
            GestureDetector(
              child: const Text('Capture image from camera'),
              onTap: () async {
                await provider.openCamera(context);
              },
            ),
            const Padding(
              padding: EdgeInsets.all(10),
            ),
            GestureDetector(
              child: const Text('Upload an image from gallery'),
              onTap: () async {
                await provider.openGallery(context);
              },
            ),
            const Padding(
              padding: EdgeInsets.all(10),
            ),
            GestureDetector(
              child: const Text('Pick file from memory'),
              onTap: () async {
                await provider.pickFile(context: context);
              },
            ),
          ],
        ),
      ),
    );
  }
}
