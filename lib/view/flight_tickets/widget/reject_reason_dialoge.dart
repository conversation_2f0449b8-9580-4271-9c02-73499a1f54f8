import 'package:e8_hr_portal/provider/flight_ticket_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:e8_hr_portal/view/widgets/hisense_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';

class FlightRejectionReasonDialog extends StatefulWidget {
  final int ticektId;
  final bool isAccept;
  const FlightRejectionReasonDialog(
      {required this.ticektId, this.isAccept = false, super.key});

  @override
  State<FlightRejectionReasonDialog> createState() =>
      _FlightRejectionReasonDialogState();
}

class _FlightRejectionReasonDialogState
    extends State<FlightRejectionReasonDialog> {
  final TextEditingController _leaveRejectionReasonController =
      TextEditingController();
  final _leaveRejectionReasonFormKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: w * 16),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(26),
      ),
      elevation: 0.0,
      backgroundColor: ThemeColors.colorFFFFFF,
      child: Padding(
        padding: EdgeInsets.fromLTRB(w * 20, h * 5, w * 5, h * 25),
        child: Form(
          key: _leaveRejectionReasonFormKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.centerRight,
                child: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Icon(
                    Icons.close,
                    color: ThemeColors.colorD6D6D6,
                    size: h * 20,
                  ),
                ),
              ),
              SizedBox(height: h * 10),
              if (!widget.isAccept)
                Text(
                  "Reason For Rejection",
                  style: tsS14w500c6E7079,
                )
              else
                Text(
                  "Reason For Approval",
                  style: tsS14w500c6E7079,
                ),
              SizedBox(height: h * 6),
              Padding(
                padding: EdgeInsets.only(right: w * 15),
                child: HisenseTextFormField(
                  controller: _leaveRejectionReasonController,
                  hintText: "Enter the reason",
                  hintStyle: tsS12w400c475366,
                  maxLines: 3,
                  validator: Validator.reason,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp("[a-z A-Z-0-9]"))
                  ],
                ),
              ),
              SizedBox(height: h * 25),
              Padding(
                padding: EdgeInsets.only(left: w * 7, right: w * 21),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _cancelButton(
                        onPressed: _onCancelled,
                        width: w * 140,
                        height: h * 40,
                        title: "Cancel",
                        textStyle: tsS14w500c475366),
                    GeneralButton(
                      title: widget.isAccept ? "Approve" : "Reject",
                      height: h * 40,
                      textStyle: tsS14w500cFFFFFF,
                      width: w * 140,
                      onPressed: () {
                        _onSubmitted(
                            isAccept: widget.isAccept,
                            ticektId: widget.ticektId,
                            reason: _leaveRejectionReasonController.text);
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onCancelled() {
    Navigator.pop(context);
  }

  void _onSubmitted({
    required int ticektId,
    required String reason,
    required bool isAccept,
  }) async {
    if (_leaveRejectionReasonFormKey.currentState!.validate()) {
      EasyLoading.show();
      final navigator = Navigator.of(context);
      final flightTicketProvider =
          Provider.of<FlightTicketProvider>(context, listen: false);
      if (!isAccept) {
        await flightTicketProvider.flightTciketApproveReject(
            action: "rejected", comment: reason, ticketId: ticektId);
      } else {
        await flightTicketProvider.flightTciketApproveReject(
            action: "approved", comment: reason, ticketId: ticektId);
      }

      // navigator.pop();
      navigator.pop();
      navigator.pop();
      EasyLoading.dismiss();
    }
  }

  Widget _cancelButton(
      {required VoidCallback onPressed,
      required double width,
      required double height,
      required String title,
      required TextStyle textStyle}) {
    return Container(
      decoration: BoxDecoration(
        color: ThemeColors.colorF3F3F9,
        borderRadius: BorderRadius.circular(50),
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50),
          ),
          minimumSize: Size(width, height),
          // fixedSize: Size(size.width * 0.872, size.height * 0.0689),
        ),
        child: Text(
          title,
          style: textStyle,
        ),
      ),
    );
  }
}
