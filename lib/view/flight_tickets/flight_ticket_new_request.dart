// ignore_for_file: use_build_context_synchronously
import 'package:e8_hr_portal/helper/drop_down_widget.dart';
import 'package:e8_hr_portal/model/flight_ticket_count_model.dart';
import 'package:e8_hr_portal/model/flight_ticket_leve_model.dart';
import 'package:e8_hr_portal/model/flight_ticket_overview_model.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/view/flight_tickets/widget/reject_reason_dialoge.dart';
import 'package:e8_hr_portal/view/other_screens/overview/widgets/cancel_button.dart';
import 'package:e8_hr_portal/view/widgets/hisense_drop_down_tile.dart';
import 'package:e8_hr_portal/view/widgets/hisense_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/model/flight_ticket_personal_info.dart';
import 'package:e8_hr_portal/provider/flight_ticket_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:e8_hr_portal/view/widgets/custom_scaffold.dart';
import 'package:e8_hr_portal/view/widgets/general_button.dart';
import 'package:provider/provider.dart';
import '../../util/dailoge.dart';
import '../../util/styles.dart';

TextEditingController flightDaysController = TextEditingController();

class FlightTicketNewRequest extends StatefulWidget {
  final bool isFromUpload;
  final bool isForEdit;
  final bool isFromLevels;
  final FlightTicketOverviewModel? flightTicketOverviewModel;
  final bool isRequestNew;
  const FlightTicketNewRequest({
    super.key,
    this.isForEdit = false,
    this.isFromLevels = false,
    this.isFromUpload = false,
    this.isRequestNew = false,
    this.flightTicketOverviewModel,
  });

  @override
  State<FlightTicketNewRequest> createState() => _FlightTicketNewRequestState();
}

class _FlightTicketNewRequestState extends State<FlightTicketNewRequest> {
  TextEditingController country = TextEditingController();
  TextEditingController state = TextEditingController();
  // TextEditingController city = TextEditingController();
  TextEditingController flightRemarksController = TextEditingController();

  final TextEditingController _departureController = TextEditingController();
  final TextEditingController _countryDepartureController =
      TextEditingController();
  final TextEditingController _countryArrivelController =
      TextEditingController();
  final TextEditingController _arrivalController = TextEditingController();
  final _flightTicketNewFormKey = GlobalKey<FormState>();

  bool isButtonAction = false;

  @override
  void initState() {
    FlightTicketProvider ticketProvider =
        Provider.of<FlightTicketProvider>(context, listen: false);
    if (!widget.isRequestNew) {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        _countryArrivelController.text =
            widget.flightTicketOverviewModel?.data?.countryOfArrival ?? "";
        _countryDepartureController.text =
            widget.flightTicketOverviewModel?.data?.countryOfDeparture ?? "";
        _arrivalController.text =
            widget.flightTicketOverviewModel?.data?.cityOfArrival ?? "";
        _departureController.text =
            widget.flightTicketOverviewModel?.data?.cityOfDeperature ?? "";

        if (widget.flightTicketOverviewModel?.data?.leaveData != null) {
          ticketProvider.selectedFlightTicketLeaveModel = null;
          if (!ticketProvider.flightTicketLeaveModel
              .contains(widget.flightTicketOverviewModel!.data!.leaveData!)) {
            ticketProvider.flightTicketLeaveModel
                .add(widget.flightTicketOverviewModel!.data!.leaveData!);
          }
          ticketProvider.selectedFlightTicketLeaveModel =
              widget.flightTicketOverviewModel?.data?.leaveData;

          ticketProvider.selectedNumberOfCount =
              widget.flightTicketOverviewModel?.data?.extraTicket ?? 1;
        }
      });

      // _countryArrivelController.text =
      //     widget.flightTicketOverviewModel?.data?.countryOfArrival ?? "";

      // --- screen two -- codes

      WidgetsBinding.instance.addPostFrameCallback((_) {
        ticketProvider.uploadFileImage = null;
        ticketProvider.pickedFile = null;
      });

      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        ticketProvider.selectedAirline = null;
        if (widget.flightTicketOverviewModel?.data?.airline != null) {
          ticketProvider.selectedAirline =
              widget.flightTicketOverviewModel?.data?.airline ?? "";
        }

        ticketProvider.selectedFromDate = DateTime.parse(
            widget.flightTicketOverviewModel?.data?.deperatureDate ?? "");
        ticketProvider.selectedToDate = DateTime.parse(
            widget.flightTicketOverviewModel?.data?.returnDate ?? "");
      });
      int daysBetween = 0;
      int daysBetweenPlus = 0;
      var retrnDate = DateTime.parse(
          (widget.flightTicketOverviewModel?.data?.returnDate ?? "")
              .toString());
      var departureDAte = DateTime.parse(
          (widget.flightTicketOverviewModel?.data?.deperatureDate ?? "")
              .toString());
      final duration = retrnDate.difference(departureDAte);
      daysBetween = duration.inDays;
      daysBetweenPlus = daysBetween + 1;
      flightDaysController.text = daysBetweenPlus.toString();

      FlightTicketProvider provider =
          Provider.of<FlightTicketProvider>(context, listen: false);
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        flightRemarksController.text =
            widget.flightTicketOverviewModel?.data?.remark ?? "";

        // provider.selectedFromDate = DateTime.now();
        // provider.selectedToDate = DateTime.now();
        // provider.formattedFromDate =
        //     formatDateFromDate(dateTime: DateTime.now(), format: "dd MMM yyyy");
        // provider.formattedToDate = formatDateFromDate(
        //     dateTime: provider.selectedFromDate!, format: "dd MMM yyyy");

        provider.daysBetweenPlus = 0;
        provider.daysBetween = 0;

        setState(() {});
      });
    } else {
      if (ticketProvider.selectedFlightTicketLeaveModel != null) {
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          ticketProvider.selectedFlightTicketLeaveModel = null;
        });
      }
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        ticketProvider.selectedAirline = null;
        // _ticketProvider.pickedFile = null;
      });
      flightDaysController.text = "1";
    }

    isButtonAction = widget.flightTicketOverviewModel?.data?.apprvReject?.any(
            (element) =>
                element.userId == LoginModel.uid &&
                (element.levelStatus?.toLowerCase() == "approved" ||
                    element.levelStatus?.toLowerCase() == "rejected")) ??
        false;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // suggestionBoxController.close();
      },
      child: CustomScaffold(
        screenTitle: "Flight Tickets",
        body: SingleChildScrollView(
          child: Form(
            // autovalidateMode: AutovalidateMode.onUserInteraction,
            key: _flightTicketNewFormKey,
            child: Consumer<FlightTicketProvider>(
                builder: (context, ticketProvider, _) {
              FlightTicketPersonalInfo? flightTicketPersonalInfo =
                  ticketProvider.flightTicketPersonalInfo;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: h * 25),
                  Text(
                    "Company & Personal Info",
                    style: tsS18w500c181818,
                  ),
                  SizedBox(height: h * 15),
                  _companyAndPersonalInfoWidget(
                    flightTicketPersonalInfo: flightTicketPersonalInfo!,
                    flightTicketCountModel:
                        ticketProvider.flightTicketCountModel,
                    flighticketLeaveModel:
                        ticketProvider.flightTicketLeaveModel,
                    numberOfTickets: ticketProvider.numberOfTicketCount,
                    ticketProvider: ticketProvider,
                    isFromLevels: widget.isFromLevels,
                  ),
                  SizedBox(height: h * 30),
                  Text(
                    "Air Ticket Details",
                    style: tsS18w500c181818,
                  ),
                  SizedBox(height: h * 8),
                  Container(
                    width: w * 343,
                    padding:
                        EdgeInsets.fromLTRB(w * 10, h * 10, w * 10, h * 28),
                    decoration: BoxDecoration(
                      color: ThemeColors.colorFFFFFF,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        _subTitile(subtitle: "Airline", isMandatory: true),
                        _selectAirlineDropdown(isForEdit: widget.isForEdit),
                        SizedBox(
                          height: 15 * h,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Consumer<FlightTicketProvider>(
                                builder: (context, provider, _) {
                                  return IgnorePointer(
                                    ignoring: (widget.isFromLevels ||
                                        (provider.flightTicketOverviewModel
                                                    ?.data?.status
                                                    ?.toLowerCase() !=
                                                "pending" &&
                                            widget.isForEdit)),
                                    child: _calenderWidget(
                                      isMandatory: true,
                                      subtitle: "Departure Date",
                                      date: formatDateFromDate(
                                          dateTime: provider.selectedFromDate,
                                          format: "dd MMM yyyy"),
                                      onTap: () async {
                                        // if (Platform.isAndroid) {
                                        await provider.selectFromDate(
                                            context: context);

                                        // } else if (Platform.isIOS) {
                                        //   await provider.selectFromDateIOS(ctx: context);
                                        // }
                                      },
                                    ),
                                  );
                                },
                              ),
                            ),
                            SizedBox(
                              width: 15 * h,
                            ),
                            Expanded(
                              child: Consumer<FlightTicketProvider>(
                                builder: (context, provider, _) {
                                  return IgnorePointer(
                                    ignoring: (widget.isFromLevels ||
                                        (provider.flightTicketOverviewModel
                                                    ?.data?.status
                                                    ?.toLowerCase() !=
                                                "pending" &&
                                            widget.isForEdit)),
                                    child: _calenderWidget(
                                      isMandatory: true,
                                      subtitle: "Return Date",
                                      date: formatDateFromDate(
                                          dateTime: provider.selectedToDate,
                                          format: "dd MMM yyyy"),
                                      onTap: () async {
                                        // if (Platform.isAndroid) {

                                        await provider.selectToDate(
                                            context: context);

                                        // } else if (Platform.isIOS) {
                                        //   await provider.selectToDateIOS(ctx: context);
                                        // }
                                      },
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: h * 15),
                        _subTitile(subtitle: "No of days"),
                        HisenseTextFormField(
                          controller: flightDaysController,
                          hintText: "Number of days",
                          hintStyle: tsS14w400454444,
                          textStyle: tsS14w400454444,
                          // keyboardType: TextInputType.text,
                          // validator: Validator.text,
                          enabled: false,
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: w * 11, vertical: h * 12),
                        ),
                        SizedBox(height: h * 15),
                        IgnorePointer(
                          ignoring: (widget.isFromLevels ||
                              (ticketProvider.flightTicketOverviewModel?.data
                                          ?.status
                                          ?.toLowerCase() !=
                                      "pending" &&
                                  widget.isForEdit)),
                          child: TypeAheadFieldWidget(
                            typeAheadController: _countryDepartureController,
                            cities: ticketProvider.allCountries,
                            title: "Country of Departure",
                          ),
                        ),
                        SizedBox(height: h * 8),
                        _textFormFieldWidget(
                            controller: _departureController,
                            title: "City of Departure",
                            validator: Validator.text,
                            hintText: "City",
                            keyboardType: TextInputType.text,
                            flightTicketProvider: ticketProvider),
                        SizedBox(height: h * 8),
                        IgnorePointer(
                          ignoring: (widget.isFromLevels ||
                              (ticketProvider.flightTicketOverviewModel?.data
                                          ?.status
                                          ?.toLowerCase() !=
                                      "pending" &&
                                  widget.isForEdit)),
                          child: TypeAheadFieldWidget(
                            typeAheadController: _countryArrivelController,
                            cities: ticketProvider.allCountries,
                            title: "Country of Arrival",
                          ),
                        ),
                        _textFormFieldWidget(
                            controller: _arrivalController,
                            title: "City of Arrival",
                            validator: Validator.text,
                            hintText: "City",
                            keyboardType: TextInputType.text,
                            flightTicketProvider: ticketProvider),
                        SizedBox(height: h * 15),
                        _subTitile(subtitle: "Remarks"),
                        Consumer<FlightTicketProvider>(
                            builder: (context, provider, _) {
                          return IgnorePointer(
                            ignoring: (widget.isFromLevels ||
                                (provider.flightTicketOverviewModel?.data
                                            ?.status
                                            ?.toLowerCase() !=
                                        "pending" &&
                                    widget.isForEdit)),
                            child: HisenseTextFormField(
                              controller: flightRemarksController,
                              hintText: "Remarks",
                              hintStyle: tsS14w400454444,
                              textStyle: tsS14w400454444,
                              // keyboardType: TextInputType.text,
                              validator: Validator.text,
                              maxLines: 5,
                              enabled: true,
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: w * 11, vertical: h * 12),
                            ),
                          );
                        }),
                        // ------------------------------------------ quattion and ticjet----------------------------
                        SizedBox(height: h * 15),
                        if (!widget.isRequestNew)
                          Consumer<FlightTicketProvider>(
                              builder: (context, flight, _) {
                            if (LoginModel.isAdmin == false &&
                                !widget.isFromLevels &&
                                !widget.isForEdit) {
                              return const SizedBox();
                            }

                            if (widget.isFromLevels && !widget.isFromUpload) {
                              return const SizedBox();
                            }

                            if ((flight.flightTicketOverviewModel?.data ==
                                        null &&
                                    flight.flightTicketOverviewModel?.data
                                            ?.tktUploadData ==
                                        null) ||
                                flight.flightTicketOverviewModel!.data!
                                    .tktUploadData!.isEmpty) {
                              return const SizedBox();
                            }

                            return Column(
                              children: [
                                _subTitile(subtitle: "Uploaded Tickets"),
                                ListView.separated(
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: flight.flightTicketOverviewModel!
                                      .data!.tktUploadData!.length,
                                  shrinkWrap: true,
                                  itemBuilder: (context, index) {
                                    return Row(
                                      children: [
                                        Expanded(
                                          child: Container(
                                            height: 40 * h,
                                            // width: 250 * w,
                                            padding: const EdgeInsets.all(8),
                                            decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                border: Border.all(
                                                    color: ThemeColors
                                                        .colorE6E6E6)),
                                            child: Align(
                                              alignment: Alignment.centerLeft,
                                              child: Text(
                                                flight
                                                        .flightTicketOverviewModel!
                                                        .data!
                                                        .tktUploadData![index]
                                                        .title ??
                                                    "",
                                                style: tsS14BN,
                                              ),
                                            ),
                                          ),
                                        ),
                                        if (widget.isFromUpload)
                                          InkWell(
                                            onTap: () {
                                              flight.flightTicketQuatationDelete(
                                                  ticketId: widget
                                                          .flightTicketOverviewModel
                                                          ?.data
                                                          ?.id ??
                                                      1,
                                                  fileType: flight
                                                      .flightTicketOverviewModel!
                                                      .data!
                                                      .tktUploadData![index]
                                                      .fileType
                                                      .toString(),
                                                  attachmentId: flight
                                                      .flightTicketOverviewModel!
                                                      .data!
                                                      .tktUploadData![index]
                                                      .id
                                                      .toString());
                                            },
                                            child: Container(
                                              height: 40,
                                              width: 40,
                                              margin:
                                                  EdgeInsets.only(left: 7 * h),
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                border: Border.all(
                                                    color: ThemeColors
                                                        .colorE6E6E6),
                                              ),
                                              child: Padding(
                                                padding: EdgeInsets.only(
                                                    bottom: 18.0 * h),
                                                child: Icon(
                                                  Icons.minimize_sharp,
                                                  color:
                                                      ThemeColors.colorFD5E5A,
                                                ),
                                              ),
                                            ),
                                          ),
                                        InkWell(
                                          onTap: () {
                                            flight.downloadDocument(
                                                url: flight
                                                    .flightTicketOverviewModel!
                                                    .data!
                                                    .tktUploadData![index]
                                                    .file
                                                    .toString(),
                                                fileName: flight
                                                    .flightTicketOverviewModel!
                                                    .data!
                                                    .tktUploadData![index]
                                                    .title
                                                    .toString());
                                          },
                                          child: Container(
                                            height: 40,
                                            width: 40,
                                            margin:
                                                EdgeInsets.only(left: 7 * h),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(5),
                                              border: Border.all(
                                                  color:
                                                      ThemeColors.colorE6E6E6),
                                            ),
                                            child: Icon(
                                              Icons.file_download_outlined,
                                              color: ThemeColors.primaryColor,
                                            ),
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                  separatorBuilder: (context, index) =>
                                      const SizedBox(
                                    height: 10,
                                  ),
                                ),
                                SizedBox(
                                  height: 10 * h,
                                )
                              ],
                            );
                          }),

                        // ===========================================================================================
                      ],
                    ),
                  ),
                  SizedBox(height: h * 18),
                  // Row(
                  //   crossAxisAlignment: CrossAxisAlignment.start,
                  //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //   children: [
                  //     if ((widget.isForEdit || widget.isFromLevels) &&
                  //         (widget.flightTicketOverviewModel?.data?.status
                  //                 ?.toLowerCase() ==
                  //             "pending") &&
                  //         !isButtonAction!)
                  //       Expanded(
                  //         child: Padding(
                  //           padding: const EdgeInsets.only(right: 8.0),
                  //           child: CancelButton2(
                  //             title: widget.isFromLevels ? "Reject" : "Cancel",
                  //             height: 56 * h,
                  //             width: 162,
                  //             textStyle: tsS18w600cFFFFFF,
                  //             onPressed: () async {
                  //               FlightTicketProvider ticketProvider =
                  //                   Provider.of<FlightTicketProvider>(context,
                  //                       listen: false);
                  //               if (widget.isFromLevels) {
                  //                 showDialog(
                  //                   context: context,
                  //                   builder: (context) {
                  //                     return FlightRejectionReasonDialog(
                  //                       ticektId: widget
                  //                               .flightTicketOverviewModel
                  //                               ?.data
                  //                               ?.id ??
                  //                           0,
                  //                     );
                  //                   },
                  //                 );
                  //               } else {
                  //                 ticketProvider.cancelRequest(
                  //                   context: context,
                  //                   ticketId: widget
                  //                           .flightTicketOverviewModel?.data?.id
                  //                           .toString() ??
                  //                       "",
                  //                 );
                  //               }
                  //             },
                  //           ),
                  //         ),
                  //       ),
                  //     Expanded(
                  //       child: GeneralButton(
                  //         height: h * 56,
                  //         width: widget.isForEdit &&
                  //                 widget.flightTicketOverviewModel?.data?.status
                  //                         ?.toLowerCase() !=
                  //                     "approved"
                  //             ? w * 162
                  //             : w * 343,
                  //         onPressed: () async {
                  //           FocusScope.of(context).unfocus();
                  //           if (flightTicketPersonalInfo
                  //                   .isProfileCompleted?.isCompleted ==
                  //               true) {
                  //             if (_flightTicketNewFormKey.currentState!
                  //                 .validate()) {
                  //               FlightTicketProvider ticketProvider =
                  //                   Provider.of<FlightTicketProvider>(context,
                  //                       listen: false);
                  //               ticketProvider.selectedAirline = null;
                  //               // ticketProvider.formattedToDate = formatDateFromDate(
                  //               //     dateTime: DateTime.now(), format: "dd MMM yyyy");
                  //               // ticketProvider.formattedFromDate = formatDateFromDate(
                  //               //     dateTime: DateTime.now(), format: "dd MMM yyyy");
                  //               ticketProvider.selectedToDate = DateTime(
                  //                 DateTime.now().year,
                  //                 DateTime.now().month,
                  //                 DateTime.now().day,
                  //               );
                  //               ticketProvider.selectedFromDate = DateTime(
                  //                 DateTime.now().year,
                  //                 DateTime.now().month,
                  //                 DateTime.now().day,
                  //               );
                  //               EasyLoading.show();
                  //               bool isGo =
                  //                   await ticketProvider.getAirlineList();

                  //               if (_arrivalController.text ==
                  //                       _departureController.text &&
                  //                   _countryArrivelController.text ==
                  //                       _countryDepartureController.text) {
                  //                 showToastText(
                  //                     "City of departure and City of Arrival are same");
                  //                 EasyLoading.dismiss();
                  //                 return;
                  //               }

                  //               if (isGo && mounted) {
                  //                 PageNavigator.push(
                  //                   context: context,
                  //                   route: FlighticketsNewRequest2(
                  //                     isRequestNew: widget.isRequestNew,
                  //                     isFromEdit: widget.isForEdit,
                  //                     isFromLevels: widget.isFromLevels,
                  //                     cityOfArrival: city.text,
                  //                     cityOfDeparture:
                  //                         _departureController.text,
                  //                     status: widget.flightTicketOverviewModel
                  //                             ?.data?.status ??
                  //                         "",
                  //                     countryOfArrival:
                  //                         _countryArrivelController.text,
                  //                     countryOfDeparture:
                  //                         _countryDepartureController.text,
                  //                     remarks: widget.isForEdit
                  //                         ? widget.flightTicketOverviewModel
                  //                             ?.data?.remark
                  //                         : null,
                  //                     airline: widget.isForEdit
                  //                         ? widget.flightTicketOverviewModel
                  //                             ?.data?.airline
                  //                         : null,
                  //                     departureDate: widget.isForEdit
                  //                         ? widget.flightTicketOverviewModel
                  //                             ?.data?.deperatureDate
                  //                         : null,
                  //                     returnDate: widget.isForEdit
                  //                         ? widget.flightTicketOverviewModel
                  //                             ?.data?.returnDate
                  //                         : null,
                  //                     ticketId: widget.isForEdit
                  //                         ? widget.flightTicketOverviewModel
                  //                             ?.data?.id
                  //                         : null,
                  //                     ticketPermission: widget
                  //                             .flightTicketOverviewModel
                  //                             ?.data
                  //                             ?.tktPermission
                  //                             ?.ticketPermission ??
                  //                         "",
                  //                     isFromUpload: widget.isFromUpload,
                  //                     isButtonAction: isButtonAction ?? false,
                  //                   ),
                  //                 );
                  //               }
                  //               EasyLoading.dismiss();
                  //             }
                  //           } else {
                  //             showToastText(flightTicketPersonalInfo
                  //                     .isProfileCompleted?.message ??
                  //                 "Please update your basic details by contacting HR / Admin.");
                  //           }
                  //         },
                  //         title: "Next",
                  //         textStyle: tsS18w600cFFFFFF,
                  //       ),
                  //     ),
                  //   ],
                  // ),

                  if ((widget.flightTicketOverviewModel?.data?.remark != null &&
                          widget.flightTicketOverviewModel?.data?.status
                                  ?.toLowerCase() ==
                              "pending" &&
                          widget.isForEdit) &&
                      !widget.isFromLevels)
                    Row(
                      children: [
                        // if ((widget.isForEdit || widget.isFromLevels) &&
                        //     (widget.flightTicketOverviewModel?.data?.status
                        //             ?.toLowerCase() ==
                        //         "pending") &&
                        //     !isButtonAction!)
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: CancelButton2(
                              title: "Cancel",
                              height: 56 * h,
                              width: 162,
                              textStyle: tsS18w600cFFFFFF,
                              onPressed: () async {
                                if (isRedundentClick(DateTime.now())) {
                                  return;
                                }
                                FlightTicketProvider ticketProvider =
                                    Provider.of<FlightTicketProvider>(context,
                                        listen: false);

                                ticketProvider.cancelRequest(
                                  context: context,
                                  ticketId: widget
                                          .flightTicketOverviewModel?.data?.id
                                          .toString() ??
                                      "",
                                );
                              },
                            ),
                          ),
                        ),
                        Expanded(
                          child: GeneralButton(
                            height: h * 56,
                            width: w * 343,
                            onPressed: () async {
                              if (_flightTicketNewFormKey.currentState!
                                  .validate()) {
                                FlightTicketProvider prov =
                                    Provider.of<FlightTicketProvider>(context,
                                        listen: false);

                                bool isPop = await prov.flightTicketEdit(
                                  ticketId: widget
                                          .flightTicketOverviewModel?.data?.id
                                          .toString() ??
                                      '',
                                  context: context,
                                  airline: prov.selectedAirline!,
                                  cityOfArrival: _arrivalController.text,
                                  cityOfDeparture: _departureController.text,
                                  countryOfArrival:
                                      _countryArrivelController.text,
                                  countryOfDeparture:
                                      _countryDepartureController.text,
                                  remarks: flightRemarksController.text,
                                  departureDate: formatDateFromDate(
                                      dateTime: prov.selectedFromDate,
                                      format: "yyyy-MM-dd"),
                                  returnDate: formatDateFromDate(
                                      dateTime: prov.selectedToDate,
                                      format: "yyyy-MM-dd"),
                                  type: prov.selectedWayText,
                                );
                                if (isPop && mounted) {
                                  Navigator.of(context).pop();
                                  // Navigator.of(context).pop();
                                }
                              }
                            },
                            title: "Update",
                            textStyle: tsS18w600cFFFFFF,
                          ),
                        ),
                      ],
                    )
                  else if (((widget.isFromLevels &&
                          widget.flightTicketOverviewModel?.data?.tktPermission
                                  ?.ticketPermission
                                  ?.toLowerCase() ==
                              "allow") &&
                      (widget.flightTicketOverviewModel?.data?.status
                                  ?.toLowerCase() ==
                              "pending" ||
                          widget.flightTicketOverviewModel?.data?.status
                                  ?.toLowerCase() ==
                              "in progress") &&
                      (!isButtonAction)))
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        CancelButton2(
                          title: widget.isFromLevels ? "Reject" : "Cancel",
                          height: 56 * h,
                          width: 162,
                          textStyle: tsS18w600cFFFFFF,
                          onPressed: () async {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return FlightRejectionReasonDialog(
                                  ticektId: widget.flightTicketOverviewModel
                                          ?.data?.id ??
                                      0,
                                );
                              },
                            );
                          },
                        ),
                        Consumer<FlightTicketProvider>(
                            builder: (context, flight, _) {
                          return GeneralButton(
                            height: h * 56,
                            width: w * 160,
                            onPressed: () async {
                              if (_flightTicketNewFormKey.currentState!
                                  .validate()) {
                                if (flight.flightTicketOverviewModel?.data
                                            ?.apprvReject?.first.userId ==
                                        LoginModel.uid &&
                                    !widget.isFromUpload) {
                                  if ((flight.flightTicketOverviewModel?.data ==
                                              null &&
                                          flight.flightTicketOverviewModel?.data
                                                  ?.tktFile ==
                                              null) ||
                                      flight.flightTicketOverviewModel!.data!
                                          .tktFile!.isEmpty) {
                                    showToastText("Please upload quotations");

                                    return;
                                  }
                                }
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    return FlightRejectionReasonDialog(
                                      ticektId: widget.flightTicketOverviewModel
                                              ?.data?.id ??
                                          0,
                                      isAccept: true,
                                    );
                                  },
                                );
                              }
                            },
                            title: "Approve",
                            textStyle: tsS18w600cFFFFFF,
                          );
                        }),
                      ],
                    )
                  else if (widget.flightTicketOverviewModel?.data?.status
                          ?.toLowerCase() ==
                      "cancelled")
                    const SizedBox()
                  else if (widget.flightTicketOverviewModel?.data?.status
                          ?.toLowerCase() ==
                      "approved")
                    const SizedBox()
                  else if (widget.flightTicketOverviewModel?.data?.status
                          ?.toLowerCase() ==
                      "rejected")
                    const SizedBox()
                  else if (widget.flightTicketOverviewModel?.data?.status
                              ?.toLowerCase() ==
                          "in progress" &&
                      isButtonAction)
                    const SizedBox()
                  else if (widget.isRequestNew)
                    GeneralButton(
                      height: h * 56,
                      width: w * 343,
                      onPressed: () async {
                        if (_flightTicketNewFormKey.currentState!.validate()) {
                          FlightTicketProvider prov =
                              Provider.of<FlightTicketProvider>(context,
                                  listen: false);

                          bool isPop = await prov.flightTicketCreate(
                              context: context,
                              airline: prov.selectedAirline!,
                              cityOfArrival: _arrivalController.text,
                              cityOfDeparture: _departureController.text,
                              countryOfArrival: _countryArrivelController.text,
                              countryOfDeparture:
                                  _countryDepartureController.text,
                              remarks: flightRemarksController.text,
                              departureDate: formatDateFromDate(
                                  dateTime: prov.selectedFromDate,
                                  format: "yyyy-MM-dd"),
                              returnDate: formatDateFromDate(
                                  dateTime: prov.selectedToDate,
                                  format: "yyyy-MM-dd"),
                              type: prov.selectedWayText);
                          if (isPop && mounted) {
                            // Navigator.of(context).pop();
                            Navigator.of(context).pop();
                          }
                        }
                      },
                      title: "Submit",
                      textStyle: tsS18w600cFFFFFF,
                    ),

                  SizedBox(height: h * 50),
                ],
              );
            }),
          ),
        ),
      ),
    );
  }

  Widget _detailsWidget({
    required String? title,
    required String? subTitle,
    required String? message,
    bool? expired,
    int flex = 1,
  }) {
    return Expanded(
      flex: flex,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (expired == true || subTitle == null)
            Tooltip(
              message: message,
              child: Row(
                children: [
                  Text(
                    "$title",
                    style: tsS12w400c949494,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Icon(
                    Icons.info_outline,
                    size: 15,
                    color: ThemeColors.colorF64D44,
                  )
                ],
              ),
            )
          else
            Text(
              title ?? "",
              style: tsS12w400c949494,
              overflow: TextOverflow.ellipsis,
            ),
          SizedBox(height: h * 3),
          Text(
            subTitle ?? "",
            style: tsS14w500c2C2D33,
            overflow: TextOverflow.ellipsis,
          ),
          // SizedBox(height: h * 15),
        ],
      ),
    );
  }

  Widget _companyAndPersonalInfoWidget({
    required FlightTicketPersonalInfo flightTicketPersonalInfo,
    FlightTicketCountModel? flightTicketCountModel,
    List<FlightTicketLeaveModel>? flighticketLeaveModel,
    List<int>? numberOfTickets,
    required FlightTicketProvider ticketProvider,
    required bool isFromLevels,
  }) {
    FlightTicketPersonalInfo? info = flightTicketPersonalInfo;

    // String? status = info.s;
    String? fullName = info.name;
    String? empId = info.employeeId;
    String? department = info.department;
    String? designation = info.designation;
    String? emailAddress = info.email;
    String? phoneNumber = info.phoneNo;
    String? infoDob = info.dob;
    String? nationality = info.nationality;
    String? passportNumber = info.passport?.passportNumber;
    String? infoPassportExpiry = info.passport?.passportExpiry;
    String? visaStatus = info.visa?.status;
    String? emiratesId = info.emiratesId;
    String? visaExpiry = info.visa?.expiry;
    String? dateOfJoinig = info.doj;
    String? dateOfJoin;
    String? dob;
    String? passportExpiry;

    debugPrint(
        "FlighTTIcket status == ${ticketProvider.flightTicketOverviewModel?.data?.status?.toLowerCase()}");
    if (infoDob != null) {
      dob = formatDateFromString(infoDob, "MMMM dd yyyy", "dd MMM yyyy");
    }
    if (infoPassportExpiry != null) {
      passportExpiry = infoPassportExpiry;
    }
    if (dateOfJoinig != null) {
      dateOfJoin =
          formatDateFromString(dateOfJoinig, "yyyy-MM-dd", "dd MMM yyyy");
    }

    return Container(
      width: w * 343,
      padding: EdgeInsets.fromLTRB(w * 10, h * 10, w * 10, h * 28),
      decoration: BoxDecoration(
        color: ThemeColors.colorFFFFFF,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // ----
          if (isFromLevels) ...[
            Row(
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _detailsWidget(
                  title: "Full Name",
                  subTitle: fullName,
                  message: "Not added",
                ),
                _detailsWidget(
                  title: "Emp ID",
                  subTitle: empId,
                  message: "Not added",
                ),
                _detailsWidget(
                  title: "Department",
                  subTitle: department,
                  message: "Not added",
                ),
              ],
            ),
            SizedBox(height: h * 11),
            Row(
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _detailsWidget(
                  title: "Designation",
                  subTitle: designation,
                  message: "Not added",
                ),
                _detailsWidget(
                  flex: 2,
                  title: "Email Address",
                  subTitle: emailAddress,
                  message: "Not added",
                ),
              ],
            ),
            SizedBox(height: h * 20),
          ],
          Row(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _detailsWidget(
                  title: "Phone no.",
                  subTitle: phoneNumber,
                  flex: 2,
                  message: "Not added"),
              SizedBox(
                width: 12 * w,
              ),
              _detailsWidget(
                  title: "Dob", subTitle: dob, flex: 2, message: "Not added"),
              // const Spacer(flex: 1),
              _detailsWidget(
                title: "Nationality",
                subTitle: nationality,
                flex: 2,
                message: "Not added",
              ),
            ],
          ),
          SizedBox(height: h * 20),
          Row(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _detailsWidget(
                  title: "Passport No",
                  subTitle: passportNumber,
                  expired: info.passport?.isExpired,
                  message: "Passport Number Expired",
                  flex: 0),
              const Spacer(
                flex: 1,
              ),
              // SizedBox(
              //   width: w * 30,
              // ),
              _detailsWidget(
                title: "Passport Exp",
                subTitle: passportExpiry,
                flex: 0,
                message: passportExpiry == null ? "Not added" : "Expired",
                expired: info.passport?.isExpired,
              ),
              const Spacer(flex: 1),
              // SizedBox(
              //   width: w * 30,
              // ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Visa Status",
                    style: tsS12w400c949494,
                  ),
                  SizedBox(height: h * 3),
                  Row(
                    children: [
                      Text(
                        visaStatus ?? "",
                        style: tsS12w500c4CD964,
                      ),
                      if (visaStatus?.toLowerCase().trim() == "active" &&
                          visaExpiry != "")
                        Text(
                          "($visaExpiry)",
                          style: tsS10w500c2C2D33,
                        ),
                    ],
                  ),
                  // SizedBox(height: h * 15),
                ],
              ),
              // const Spacer(flex: 2),
            ],
          ),
          SizedBox(height: h * 24),
          Row(
            children: [
              _detailsWidget(
                title: "Emirates ID",
                subTitle: emiratesId,
                flex: 1,
                message: emiratesId == null ? "Not added" : "Expired",
                expired: info.isEmiratesIdExpired,
              ),
              _detailsWidget(
                title: "Date of Joining",
                subTitle: dateOfJoin,
                flex: 1,
                message: "Not added",
                expired: info.isEmiratesIdExpired,
              ),
              // SizedBox(
              //   width:  * w,
              // ),
            ],
          ),

          // if (!isFromLevels) ...[
          SizedBox(
            height: 20 * h,
          ),
          Divider(
            height: 1,
            color: ThemeColors.colorE8E8E8,
            endIndent: 2,
            indent: 2,
            thickness: 2,
          ),
          SizedBox(
            height: 15 * h,
          ),
          Row(
            children: [
              _detailsWidget(
                title: "Ticket Eligible",
                subTitle:
                    flightTicketCountModel?.data?.eligibleTicket.toString() ??
                        '0',
                flex: 1,
                message: "Not added",
                expired: false,
              ),
              _detailsWidget(
                title: "Ticket Availed",
                subTitle:
                    flightTicketCountModel?.data?.takenTicket.toString() ?? '0',
                flex: 1,
                message: "Not added",
                expired: false,
              ),
              _detailsWidget(
                title: "Ticket Balance",
                subTitle:
                    flightTicketCountModel?.data?.balanceTicket.toString() ??
                        '0',
                flex: 1,
                message: "Not added",
                expired: false,
              ),
            ],
          ),
          // ],
          SizedBox(
            height: 15 * h,
          ),

          Divider(
            height: 1,
            color: ThemeColors.colorE8E8E8,
            endIndent: 2,
            indent: 2,
            thickness: 2,
          ),
          SizedBox(
            height: 15 * h,
          ),

          Align(
            alignment: Alignment.topLeft,
            child: Text("Approved leave req.", style: tsS12w400c949494),
          ),
          SizedBox(height: 5 * h),
          IgnorePointer(
            ignoring: (widget.isFromLevels ||
                (ticketProvider.flightTicketOverviewModel?.data?.status
                            ?.toLowerCase() !=
                        "pending" &&
                    widget.isForEdit)),
            // ignoring: ticketProvider.flightTicketOverviewModel?.data?.status !=
            //         null &&
            //     ticketProvider.flightTicketOverviewModel?.data?.status
            //             ?.toLowerCase() !=
            //         "pending" &&
            //     widget.isFromLevels,
            child: DropdownWidgetFlightTicket(
              hintText: "Select Approved Leave",
              selectedValue: ticketProvider.selectedFlightTicketLeaveModel,
              validator: (p0) {
                if (p0 == null) {
                  return "Please enter approved leave";
                }
                return null;
              },
              items: flighticketLeaveModel?.map((e) {
                return DropdownMenuItem(
                  value: e,
                  enabled: true,
                  child:
                      Text('${e.startDate} to ${e.endDate} (${e.leaveType})'),
                );
              }).toList(),
              onChanged: (value) {
                ticketProvider.selectedFlightTicketLeaveModel = value;
              },
            ),
          ),
          SizedBox(
            height: 5 * h,
          ),
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              "No. of tickets",
              style: tsS12w400c949494,
            ),
          ),
          SizedBox(
            height: 5 * h,
          ),
          IgnorePointer(
            ignoring: (ticketProvider.flightTicketOverviewModel?.data?.status
                        .toString()
                        .toLowerCase() !=
                    "pending" &&
                widget.isForEdit),
            child: DropdownWidgetFlightTicket(
              hintText: "1",
              selectedValue: ticketProvider.selectedNumberOfCount,
              items: numberOfTickets?.map((e) {
                return DropdownMenuItem(
                    value: e, enabled: true, child: Text(e.toString()));
              }).toList(),
              onChanged: (value) {
                ticketProvider.selectedNumberOfCount = value;
              },
            ),
          )
          // Column(
          //   crossAxisAlignment: CrossAxisAlignment.start,
          //   children: [
          //     Text(
          //       "Emirates ID",
          //       style: tsS12w400c949494,
          //     ),
          //     SizedBox(height: h * 3),
          //     Text(
          //       emiratesId ?? "",
          //       style: tsS14w500c2C2D33,
          //     ),
          //     // SizedBox(height: h * 15),
          //   ],
          // ),
        ],
      ),
    );
  }

  Widget _textFormFieldWidget({
    required String title,
    String? hintText,
    required TextEditingController controller,
    required var validator,
    required TextInputType keyboardType,
    required FlightTicketProvider flightTicketProvider,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: tsS14w400c30292F),
        SizedBox(height: h * 4),
        IgnorePointer(
          ignoring: (widget.isFromLevels ||
              (flightTicketProvider.flightTicketOverviewModel?.data?.status
                          ?.toLowerCase() !=
                      "pending" &&
                  widget.isForEdit)),
          child: TextFieldWidget(
            controller: controller,
            hintText: hintText,
            hintStyle: tsS14w400c9F9F9F,
            textStyle: tsS14w400c30292F,
            keyboardType: keyboardType,
            validator: validator,
            contentPadding:
                EdgeInsets.symmetric(vertical: h * 11, horizontal: w * 11),
          ),
        ),
        SizedBox(height: h * 15),
      ],
    );
  }

  Widget _subTitile({required String subtitle, bool isMandatory = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: Row(
            children: [
              Text(
                subtitle,
                style: tsS14w400c30292F,
              ),
              if (isMandatory)
                Text(
                  "*",
                  style: tsS14w400cFA0000,
                ),
            ],
          ),
        ),
        SizedBox(height: h * 4),
      ],
    );
  }

  Widget _calenderWidget({
    required String subtitle,
    required bool isMandatory,
    required String date,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _subTitile(subtitle: subtitle, isMandatory: isMandatory),
        Align(
          alignment: Alignment.centerLeft,
          child: GestureDetector(
            onTap: onTap,
            child: Container(
              height: h * 45,
              width: w * 164,
              padding: EdgeInsets.symmetric(horizontal: w * 11),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: ThemeColors.colorFFFFFF,
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: ThemeColors.colorE3E3E3, width: 1),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    date,
                    style: tsS14w400454444,
                  ),
                  Image.asset(
                    "assets/icons/calendar_black.png",
                    height: h * 18,
                    width: w * 18,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _selectAirlineDropdown({required bool isForEdit}) {
    return Consumer<FlightTicketProvider>(
      builder: (context, provider, _) {
        return IgnorePointer(
          ignoring: (widget.isFromLevels ||
              (provider.flightTicketOverviewModel?.data?.status
                          ?.toLowerCase() !=
                      "pending" &&
                  isForEdit)),
          child: HisenseDropdownTile(
            title: "",
            hintText: "Select Airline",
            hintStyle: tsS14w4009F9F9F,
            style: tsS14w400454444,
            value: provider.selectedAirline,
            validator: (value) {
              if (value == null) {
                return "Select Your Desired Airline";
              }
              return null;
            },
            // contentPadding: EdgeInsets.fromLTRB(12 * w, 20 * h, 12 * w, 12 * h),
            onChanged: (String? value) {
              if (value != null) {
                WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
                  provider.selectedAirline = value;
                });
              }
            },
            items: provider.airlineList.map((item) {
              return DropdownMenuItem(
                value: item.name,
                child: Text(
                  item.name ?? "",
                  style: tsS14w400454444,
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }
}

class TypeAheadFieldWidget extends StatelessWidget {
  final String title;
  final TextEditingController _typeAheadController;
  final List<String> cities;

  const TypeAheadFieldWidget({
    super.key,
    required TextEditingController typeAheadController,
    required this.cities,
    required this.title,
  }) : _typeAheadController = typeAheadController;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: tsS14w400c30292F),
        SizedBox(height: h * 4),
        TypeAheadField<String>(
          controller: _typeAheadController,
          suggestionsCallback: (pattern) {
            return cities
                .where((item) =>
                    item.toLowerCase().startsWith(pattern.toLowerCase()))
                .toList();
          },
          itemBuilder: (context, String suggestion) {
            return ListTile(
              title: Text(suggestion),
            );
          },
          onSelected: (String suggestion) {
            _typeAheadController.text = suggestion;
          },
          builder: (context, controller, focusNode) {
            return TextField(
              controller: controller,
              focusNode: focusNode,
              decoration: InputDecoration(
                contentPadding:
                    EdgeInsets.symmetric(vertical: h * 11, horizontal: w * 11),
                labelStyle: GoogleFonts.rubik(
                  color: const Color(0xFF8391B5),
                ),
                fillColor: Colors.white,
                filled: true,
                hintText: title,
                hintStyle: tsS14w400c9F9F9F,
                floatingLabelStyle: tsS14w400c30292F,
                enabledBorder: OutlineInputBorder(
                  borderSide: const BorderSide(
                    width: 1,
                    color: Color(0xFFDEE7FF),
                  ),
                  borderRadius: BorderRadius.circular(5),
                ),
                border: OutlineInputBorder(
                  borderSide: const BorderSide(
                    width: 1,
                    color: Color(0xFFDEE7FF),
                  ),
                  borderRadius: BorderRadius.circular(5),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: const BorderSide(
                    width: 1,
                    color: Color(0xFFDEE7FF),
                  ),
                  borderRadius: BorderRadius.circular(5),
                ),
              ),
            );
          },
        ),
        SizedBox(height: h * 15),
      ],
    );
  }
}
