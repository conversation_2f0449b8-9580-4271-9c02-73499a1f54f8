import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/provider/team_members_provider.dart';
import 'package:e8_hr_portal/provider/user_status_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/view/work_updates.dart/work_status_bottomsheet.dart';
import 'package:provider/provider.dart';

class WorkUpdatesScreen extends StatelessWidget {
  const WorkUpdatesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 20, left: 15, right: 15),
      decoration: BoxDecoration(
        color: ThemeColors.colorF4F5FA,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Align(
              alignment: Alignment.topLeft,
              child: Text(
                "My Status",
                style: tsS18w500,
              ),
            ),
            Container(
              margin: const EdgeInsets.symmetric(vertical: 18),
              padding: const EdgeInsets.all(11),
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(8)),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Consumer2<UserStatusProvider, ProfileProvider>(
                    builder: (context, provider, profile, child) {
                      if (profile.userDetailesModel == null) {
                        return const SizedBox();
                      }
                      return SizedBox(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            ClipRRect(
                                borderRadius: BorderRadius.circular(5.0),
                                child: ClipOval(
                                  child: CachedNetworkImage(
                                    imageUrl: profile.userDetailesModel!
                                        .profileDetails!.profilePic
                                        .toString(),
                                    width: 50,
                                    height: 50,
                                    fit: BoxFit.cover,
                                    errorWidget: (context, url, error) {
                                      return Container(
                                        height: 50,
                                        width: 50,
                                        decoration: BoxDecoration(
                                          color: ThemeColors.primaryColor,
                                          shape: BoxShape.circle,
                                        ),
                                        alignment: Alignment.center,
                                        child: Text(
                                          "A",
                                          // name?.substring(0, 1).toUpperCase() ?? "",
                                          style: GoogleFonts.rubik(
                                              fontSize: 22,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.white),
                                        ),
                                      );
                                    },
                                  ),
                                )),
                            const SizedBox(
                              width: 10,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    "Status",
                                    style: tsS12w400979797,
                                  ),
                                ),
                                const SizedBox(
                                  height: 5,
                                ),
                                Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    profile.userDetailesModel!.profileDetails
                                            ?.empStatus?.name
                                            .toString() ??
                                        "",
                                    style: tsS14BN,
                                  ),
                                )
                              ],
                            )
                          ],
                        ),
                      );
                    },
                  ),
                  InkWell(
                    onTap: () {
                      showModalBottomSheet<void>(
                        useSafeArea: false,
                        context: context,
                        isDismissible: true,
                        isScrollControlled: true,
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(35.0),
                          ),
                        ),
                        builder: (BuildContext context) {
                          return const WorkStatusUpdateBottomSheet();
                        },
                      );
                    },
                    child: Container(
                      height: 30,
                      width: 98,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(50),
                        gradient: const LinearGradient(
                            // stops: [96.13, 83.07],
                            begin: Alignment.centerLeft,
                            colors: [Color(0xff03AD9E), Color(0xff12DFCC)]),
                      ),
                      child: Center(
                        child: Text(
                          "Change",
                          style: tsS14FFFFF,
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
            Align(
              alignment: Alignment.topLeft,
              child: Text(
                "Other Status",
                style: tsS18w500,
              ),
            ),
            Consumer<TeamMembersProvider>(builder: (context, provider, child) {
              final data = provider.teamMembersList;
              if (data.isEmpty) {
                return const SizedBox();
              }
              return Container(
                margin: const EdgeInsets.only(top: 18),
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Expanded(
                  child: ListView.separated(
                      physics: const BouncingScrollPhysics(),
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        return SizedBox(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              ClipRRect(
                                  borderRadius: BorderRadius.circular(5.0),
                                  child: ClipOval(
                                    child: CachedNetworkImage(
                                      imageUrl:
                                          data[index].profilePic.toString(),
                                      width: 50,
                                      height: 50,
                                      fit: BoxFit.cover,
                                      errorWidget: (context, url, error) {
                                        return Container(
                                          height: 50,
                                          width: 50,
                                          decoration: BoxDecoration(
                                            color: ThemeColors.primaryColor,
                                            shape: BoxShape.circle,
                                          ),
                                          alignment: Alignment.center,
                                          child: Text(
                                            data[index]
                                                .name
                                                .toString()
                                                .substring(0, 1)
                                                .toUpperCase(),
                                            style: GoogleFonts.rubik(
                                                fontSize: 22,
                                                fontWeight: FontWeight.w500,
                                                color: Colors.white),
                                          ),
                                        );
                                      },
                                    ),
                                  )),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        data[index].name.toString(),
                                        style: tsS14BN,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 2,
                                    ),
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        data[index]
                                            .designation!
                                            .first
                                            .name
                                            .toString(),
                                        overflow: TextOverflow.ellipsis,
                                        style: tsS12w400979797,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                decoration: BoxDecoration(
                                    color: data[index].userStatus?.first.name ==
                                            "Active"
                                        ? ThemeColors.color32936F
                                            .withOpacity(0.15)
                                        : data[index].userStatus?.first.name ==
                                                "At Work"
                                            ? ThemeColors.color5570F1
                                                .withOpacity(0.15)
                                            : ThemeColors.colorF64D44
                                                .withOpacity(0.15),
                                    borderRadius: BorderRadius.circular(6)),
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                      left: 10, right: 10, top: 4, bottom: 4),
                                  child: FittedBox(
                                    child: Text(
                                      '${data[index].userStatus?.first.name}',
                                      style: GoogleFonts.poppins(
                                          color: data[index]
                                                      .userStatus
                                                      ?.first
                                                      .name ==
                                                  "Active"
                                              ? ThemeColors.color32936F
                                              : data[index]
                                                          .userStatus
                                                          ?.first
                                                          .name ==
                                                      "At Work"
                                                  ? ThemeColors.color5570F1
                                                  : ThemeColors.colorF64D44,
                                          fontSize: 10 * f,
                                          fontWeight: FontWeight.w600),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      separatorBuilder: (context, index) {
                        return Divider(
                          thickness: 1,
                          color: ThemeColors.colorF3F3F3,
                        );
                      },
                      itemCount: data.length),
                ),
              );
            })
          ],
        ),
      ),
    );
  }
}
