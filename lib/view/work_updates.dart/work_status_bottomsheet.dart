// ignore_for_file: use_build_context_synchronously

import 'package:e8_hr_portal/model/emp_action_status_model.dart';
import 'package:e8_hr_portal/model/user_detailes_model.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/provider/user_status_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:provider/provider.dart';

import '../../util/size_config.dart';

class WorkStatusUpdateBottomSheet extends StatelessWidget {
  const WorkStatusUpdateBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<UserStatusProvider>();
    List<EmpActionStatusModel> userStatuses = provider.userStatus;
    userStatuses.map((e) => e.isShowActions = false).toList();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 14),
          height: 5,
          width: 135,
          decoration: BoxDecoration(color: ThemeColors.colorD9D9D9),
        ),
        SizedBox(
          height: h * 15,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 29),
          child: Align(
              alignment: Alignment.centerLeft,
              child:
                  Text("Change your current status", style: tsS14w4009F9F9F)),
        ),
        Consumer2<UserStatusProvider, ProfileProvider>(
            builder: (context, provider, profile, _) {
          List<EmpActionStatusModel> userStatuses = provider.userStatus;
          ProfileDetails? profileDetails =
              profile.userDetailesModel?.profileDetails;
          EmpStatus? empStatus = profileDetails?.empStatus;

          return ListView.separated(
              padding: const EdgeInsets.only(
                  left: 29, right: 29, top: 20, bottom: 70),
              shrinkWrap: true,
              itemBuilder: (context, index) {
                EmpActionStatusModel item = userStatuses[index];
                List<SubAction> actions = item.subAction ?? [];
                bool isShowActions = item.isShowActions ?? false;
                bool isMainSelected = item.id == empStatus?.id;
                return Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    InkWell(
                      onTap: () async {
                        if (actions.isEmpty) {
                          await provider.editUserStatus(id: item.id.toString());
                          await profile.getProfileData(context: context);
                          Navigator.pop(context);
                        } else {
                          provider.onShowActions(
                            selectedEmpSubSction: empStatus?.subAction ?? '',
                            item: item,
                            items: userStatuses,
                            selectedEmpStatusName: empStatus?.name ?? '',
                          );
                        }
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            item.name ?? '',
                            style: isMainSelected
                                ? tsS14w500c03AD9E
                                : tsS14w500Black,
                          ),
                          if (isMainSelected)
                            ImageIcon(
                              const AssetImage("assets/icons/tick.png"),
                              color: ThemeColors.colorF9637D,
                            )
                        ],
                      ),
                    ),
                    if (isShowActions) ...[
                      SizedBox(height: 10),
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Wrap(
                          spacing: 12,
                          runSpacing: 8,
                          children: actions.map(
                            (e) {
                              String title = e.value ?? '';
                              bool isActive = e.isActive ?? false;
                              return InkWell(
                                borderRadius: BorderRadius.circular(30),
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 6),
                                  decoration: BoxDecoration(
                                      color: isActive
                                          ? ThemeColors.colorF9637D
                                              .withOpacity(0.1)
                                          : ThemeColors.colorFCC500
                                              .withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(30),
                                      border: Border.all(
                                          color: isActive
                                              ? ThemeColors.colorF9637D
                                              : ThemeColors.colorFCC500)),
                                  child: Text(title),
                                ),
                                onTap: () async {
                                  provider.onSubActionChanged(
                                      item: e, items: actions);
                                  if (e.value != null) {
                                    await provider.editUserStatus(
                                        id: item.id.toString(),
                                        subAction:
                                            (e.value ?? '').toLowerCase());
                                    await profile.getProfileData(
                                        context: context);
                                  } else {
                                    await provider.editUserStatus(
                                        id: item.id.toString());
                                    await profile.getProfileData(
                                        context: context);
                                  }
                                  Navigator.pop(context);
                                },
                              );
                            },
                          ).toList(),
                        ),
                      ),
                    ],
                  ],
                );
              },
              separatorBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0, bottom: 8),
                  child: Divider(
                    thickness: 1,
                    color: ThemeColors.colorD9D9D9,
                  ),
                );
              },
              itemCount: userStatuses.length);
        })
      ],
    );
  }
}
