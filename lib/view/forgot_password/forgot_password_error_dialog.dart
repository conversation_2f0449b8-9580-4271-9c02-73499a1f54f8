import 'package:flutter/material.dart';

class ForgotPasswordErrorDialog extends StatelessWidget {
  const ForgotPasswordErrorDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(
        'Failed!',
      ),
      content: const Text(
          'There is no user record corresponding to this identifier. The user may have been deleted.'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Okay'),
        ),
      ],
    );
  }
}
