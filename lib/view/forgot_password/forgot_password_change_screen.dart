import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/provider/forgot_password_provider.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:provider/provider.dart';

class ForgotPasswordChangePassScreen extends StatefulWidget {
  const ForgotPasswordChangePassScreen({super.key});

  @override
  State<ForgotPasswordChangePassScreen> createState() =>
      _ForgotPasswordChangePassScreenState();
}

class _ForgotPasswordChangePassScreenState
    extends State<ForgotPasswordChangePassScreen> {
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  final _formKey = GlobalKey<FormState>();

  bool _hidePassword = true;
  bool _hidePassword2 = true;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: const Icon(Icons.arrow_back_rounded),
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(15, 22, 15, 15),
            child: Form(
              key: _formKey,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              child: Consumer<ForgotPasswordProvider>(
                  builder: (context, provider, _) {
                return Column(children: [
                  Align(
                    alignment: Alignment.topLeft,
                    child: Text(
                      "New Password",
                      style: GoogleFonts.poppins(
                          fontSize: 36,
                          fontWeight: FontWeight.w600,
                          fontStyle: FontStyle.normal,
                          color: const Color(0xff23262D)),
                    ),
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  Align(
                    alignment: Alignment.topLeft,
                    child: Text(
                      "Set a new password to continue.",
                      style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontStyle: FontStyle.normal,
                          color: const Color(0xff9F9F9F)),
                    ),
                  ),
                  const SizedBox(
                    height: 35,
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  Align(
                    alignment: Alignment.topLeft,
                    child: Text(
                      "Enter New Password",
                      style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          fontStyle: FontStyle.normal,
                          color: const Color(0xff30292F)),
                    ),
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  TextFieldWidget(
                    controller: _passwordController,
                    hintText: 'Enter New Password',

                    obscureText: _hidePassword,
                    prefixIcon: const Padding(
                      padding: EdgeInsets.all(13.0),
                      child: ImageIcon(
                        AssetImage('assets/icons/password.png'),
                        color: Color(0xff292D32),
                      ),
                    ),

                    error: provider.passError,
                    suffixIcon: InkWell(
                      onTap: () =>
                          setState(() => _hidePassword = !_hidePassword),
                      child: Padding(
                        padding: const EdgeInsets.all(13.0),
                        child: ImageIcon(
                          _hidePassword
                              ? const AssetImage('assets/icons/hidden.png')
                              : const AssetImage('assets/icons/show.png'),
                        ),
                      ),
                    ),
                    validator: (value) =>
                        Validator.textSpecific(value!, 'password'),
                    textCapitalization: TextCapitalization.none,
                    // obscureText: _hidePassword,
                    // focusNode: _passwordFocusNode,
                  ),
                  const SizedBox(height: 15),
                  Align(
                    alignment: Alignment.topLeft,
                    child: Text(
                      "Re-enter Password",
                      style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          fontStyle: FontStyle.normal,
                          color: const Color(0xff30292F)),
                    ),
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  TextFieldWidget(
                    controller: _confirmPasswordController,
                    hintText: 'Re-enter Password',
                    obscureText: _hidePassword2,
                    prefixIcon: const Padding(
                      padding: EdgeInsets.all(13.0),
                      child: ImageIcon(
                        AssetImage('assets/icons/password.png'),
                        color: Color(0xff292D32),
                      ),
                    ),
                    error: provider.confirmPassError,
                    suffixIcon: InkWell(
                      onTap: () =>
                          setState(() => _hidePassword2 = !_hidePassword2),
                      child: Padding(
                        padding: const EdgeInsets.all(13.0),
                        child: ImageIcon(
                          _hidePassword
                              ? const AssetImage('assets/icons/hidden.png')
                              : const AssetImage('assets/icons/show.png'),
                        ),
                      ),
                    ),
                    validator: (value) =>
                        Validator.textSpecific(value!, 'password'),
                    textCapitalization: TextCapitalization.none,
                    // obscureText: _hidePassword,
                    // focusNode: _passwordFocusNode,
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                  Consumer<ForgotPasswordProvider>(
                    builder: (context, provider, child) {
                      return ButtonWidget(
                          title: 'Save Password',
                          textStyle: tsS161E2138w,
                          color: Theme.of(context).primaryColor,
                          onPressed: () {
                            if (_formKey.currentState!.validate()) {
                              provider.forgotPasswordChangeURL(
                                  password: _passwordController.text,
                                  confirmPassword:
                                      _confirmPasswordController.text,
                                  context: context);
                            }
                          });
                    },
                  ),
                ]);
              }),
            ),
          ),
        ));
  }
}
