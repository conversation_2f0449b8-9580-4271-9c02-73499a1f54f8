import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/sign_in/login_with_email_screen.dart';

class ForgotPasswordSuccessDialog extends StatelessWidget {
  const ForgotPasswordSuccessDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(
        'Success!',
      ),
      content: const Text(
        'An email has just been sent to you, Click the link provided to reset password',
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            PageNavigator.pushAndRemoveUntil(
              context: context,
              route: const LoginWithEmailScreen(),
            );
          },
          child: const Text('Back to Login'),
        ),
      ],
    );
  }
}
