// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/helper/textfield_widget.dart';
import 'package:e8_hr_portal/provider/forgot_password_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/validator.dart';
import 'package:provider/provider.dart';

class ForgotPassword extends StatelessWidget {
  ForgotPassword({super.key});

  final TextEditingController _emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFF),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: const Icon(Icons.arrow_back_rounded),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(15, 22, 15, 15),
          child: Column(
            children: [
              Align(
                alignment: Alignment.topLeft,
                child: Text(
                  "Forgot Password?",
                  style: GoogleFonts.poppins(
                      fontSize: 36,
                      fontWeight: FontWeight.w600,
                      fontStyle: FontStyle.normal,
                      color: const Color(0xff23262D)),
                ),
              ),
              const SizedBox(
                height: 4,
              ),
              Align(
                alignment: Alignment.topLeft,
                child: Text(
                  "We will send a 4 digit code to your registered Email Address to reset your password.",
                  style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      fontStyle: FontStyle.normal,
                      color: const Color(0xff9F9F9F)),
                ),
              ),
              const SizedBox(height: 80),
              Align(
                alignment: Alignment.topLeft,
                child: Text(
                  "Email Address",
                  style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      fontStyle: FontStyle.normal,
                      color: const Color(0xff30292F)),
                ),
              ),
              const SizedBox(
                height: 4,
              ),
              Form(
                key: _formKey,
                child: TextFieldWidget(
                  controller: _emailController,
                  hintText: 'Email',
                  keyboardType: TextInputType.emailAddress,
                  prefixIcon: const Padding(
                    padding: EdgeInsets.all(13.0),
                    child: ImageIcon(
                      AssetImage('assets/icons/mail.png'),
                    ),
                  ),
                  validator: Validator.email,
                  textCapitalization: TextCapitalization.none,
                ),
              ),
              const SizedBox(height: 30),
              Consumer<ForgotPasswordProvider>(
                builder: (context, provider, child) {
                  return ButtonWidget(
                      title: 'Send',
                      textStyle: tsS161E2138w,
                      color: Theme.of(context).primaryColor,
                      onPressed: () =>
                          _sendPasswordResetEmail(context, provider));
                },
              ),
              const SizedBox(height: 30),
              Align(
                alignment: Alignment.center,
                child: InkWell(
                  onTap: () => Navigator.pop(context),
                  child: Text(
                    "Back to login",
                    style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        fontStyle: FontStyle.normal,
                        color: ThemeColors.colorF9637D),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _sendPasswordResetEmail(
    BuildContext context,
    ForgotPasswordProvider provider,
  ) async {
    if (_formKey.currentState!.validate()) {
      await hideKeyboard(context);
      await provider.sendPasswordResetEmail(
        context: context,
        email: _emailController.text.toLowerCase(),
      );
    }
  }
}
