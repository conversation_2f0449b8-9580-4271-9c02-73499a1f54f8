import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/helper/button_widget.dart';
import 'package:e8_hr_portal/provider/forgot_password_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:provider/provider.dart';

class ForgotOtpScreen extends StatefulWidget {
  final String? email;
  const ForgotOtpScreen({super.key, required this.email});

  @override
  State<ForgotOtpScreen> createState() => _ForgotOtpScreenState();
}

class _ForgotOtpScreenState extends State<ForgotOtpScreen> {
  final TextEditingController _otpController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  // final ValueNotifier<bool> _buttonDisabled = ValueNotifier(true);
  // final bool _buttonDisabled = false;
  @override
  Widget build(BuildContext context) {
    var height = MediaQuery.of(context).size.height;
    var width = MediaQuery.of(context).size.width;

    final ForgotPasswordProvider forgotPasswordProvider =
        Provider.of<ForgotPasswordProvider>(context);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: const Icon(Icons.arrow_back_rounded),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(15, 22, 15, 15),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Align(
                alignment: Alignment.topLeft,
                child: Text(
                  "Forgot Password?",
                  style: GoogleFonts.poppins(
                      fontSize: 36,
                      fontWeight: FontWeight.w600,
                      fontStyle: FontStyle.normal,
                      color: const Color(0xff23262D)),
                ),
              ),
              const SizedBox(
                height: 4,
              ),
              Align(
                alignment: Alignment.topLeft,
                child: RichText(
                  text: TextSpan(children: [
                    TextSpan(
                      text:
                          "Enter The 4 digit code sent to your Email Address to verify your account.",
                      style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontStyle: FontStyle.normal,
                          color: const Color(0xff9F9F9F)),
                    ),
                    TextSpan(
                      text: widget.email,
                      style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          fontStyle: FontStyle.normal,
                          color: const Color(0xff131515)),
                    ),
                  ]),
                ),
              ),
              const SizedBox(
                height: 35,
              ),
              Align(
                alignment: Alignment.topLeft,
                child: Text(
                  "Enter Verification Code",
                  style: tsS12w400Black,
                ),
              ),
              const SizedBox(
                height: 15,
              ),
              PinCodeTextField(
                showCursor: true,
                appContext: context,
                controller: _otpController,
                length: 4,
                boxShadows: const [
                  BoxShadow(
                      offset: Offset(2, 2),
                      color: Color.fromRGBO(0, 0, 0, 0.16),
                      blurRadius: 5)
                ],
                validator: (value) {
                  return value!.isEmpty ? "Enter 4 digit otp" : null;
                },
                animationType: AnimationType.none,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                textStyle: const TextStyle(color: Colors.black),
                onChanged: (value) {
                  // if (value.length == 4) {
                  //   _buttonDisabled = true;
                  // } else {
                  //   _buttonDisabled = false;
                  // }
                },
                keyboardType: TextInputType.number,
                inputFormatters: <TextInputFormatter>[
                  FilteringTextInputFormatter.digitsOnly,
                ],
                autoFocus: true,
                enableActiveFill: true,
                cursorColor: Colors.grey,
                pinTheme: PinTheme(
                  shape: PinCodeFieldShape.box,
                  activeFillColor: Colors.white,
                  inactiveColor: const Color(0xffE3E3E3),
                  inactiveFillColor: Colors.white,
                  selectedColor: Colors.white,
                  selectedFillColor: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  activeColor: ThemeColors.primaryColor,
                  // errorBorderColor: Colors.grey,
                  borderWidth: 1,
                  fieldWidth: width * 0.2,
                  fieldHeight: height * 0.061,
                ),
              ),
              const SizedBox(height: 30),
              Consumer<ForgotPasswordProvider>(
                builder: (context, provider, child) {
                  return ButtonWidget(
                      title: 'Verify',
                      textStyle: tsS161E2138w,
                      color: Theme.of(context).primaryColor,
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          provider.otpVerification(
                              otp: _otpController.text, context: context);
                        }
                      });
                },
              ),
              const SizedBox(height: 30),
              Align(
                alignment: Alignment.center,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Didn’t receive code? ",
                      style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          fontStyle: FontStyle.normal,
                          color: Colors.black),
                    ),
                    InkWell(
                      onTap: () {
                        // Navigator.pop(context);
                        _otpController.clear();
                        forgotPasswordProvider.sendPasswordResetEmail(
                          context: context,
                          email: widget.email.toString(),
                        );
                      },
                      child: Text(
                        "Resend",
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          fontStyle: FontStyle.normal,
                          color: ThemeColors.colorF9637D,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
