import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:http/http.dart' as http;
import 'package:e8_hr_portal/model/logged_in_user.dart';

const _serverKey =
    "AAAAnqP8srw:APA91bG_2eBM4SDF6bG0th2IY6_HsloBeoB3OZUQBupmQbLYOl2mLnTYYSyIPKwab1htISJqIvv9jimaWXFbvcsi6zmUBhWEWCnjJp0Y3MRaUDKrs1OiKFTsjVLEcFRQ22q52c-2pKDC";

final _uri = Uri.parse('https://fcm.googleapis.com/fcm/send');

const _headers = {
  'authorization': 'key=$_serverKey',
  'content-type': 'application/json'
};

sendMessagesToSpecificDevices({
  required String deviceToken,
  required String body,
  required String title,
  required String screen,
}) async {
  Map json = {
    'to': deviceToken,
    'priority': 'high',
    'notification': {'body': body, 'title': title},
    'data': {'screen': screen, 'click_action': 'FLUTTER_NOTIFICATION_CLICK'}
  };
  await http.post(
    _uri,
    headers: _headers,
    body: jsonEncode(json),
  );
}

sendMessagesToAllDevices({
  required String body,
  required String title,
  required String screen,
}) async {
  FirebaseMessaging.instance.unsubscribeFromTopic('all');

  Map json = {
    'to': '/topics/all',
    'priority': 'high',
    'notification': {'body': body, 'title': title},
    'data': {'screen': screen, 'click_action': 'FLUTTER_NOTIFICATION_CLICK'},
  };
  await http
      .post(
    _uri,
    headers: _headers,
    body: jsonEncode(json),
  )
      .then((value) {
    FirebaseMessaging.instance.subscribeToTopic('all');
  });
}

sendMessagesToAllAdmins({
  required String body,
  required String title,
  required String screen,
}) async {
  FirebaseMessaging.instance.unsubscribeFromTopic('all');

  Map json = {
    'to': '/topics/admins',
    'priority': 'high',
    'notification': {'body': body, 'title': title},
    'data': {'screen': screen, 'click_action': 'FLUTTER_NOTIFICATION_CLICK'},
  };
  await http
      .post(
    _uri,
    headers: _headers,
    body: jsonEncode(json),
  )
      .then((value) {
    FirebaseMessaging.instance.subscribeToTopic('all');
  });
}

updateFCMToken(String uid) async {
  String? token = await FirebaseMessaging.instance.getToken();
  FirebaseFirestore.instance.collection('users').doc(uid).update({
    'fcmToken': token,
  });
  FirebaseMessaging.instance.subscribeToTopic('all');
  if (LoggedInUser.isAdmin) {
    FirebaseMessaging.instance.subscribeToTopic('admins');
  }
}
