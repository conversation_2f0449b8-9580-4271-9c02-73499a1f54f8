import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/experience_certificate_request.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

import '../util/dailoge.dart';

class ExperienceCertifcateProvider extends ChangeNotifier {
  bool _isLoading = false;
  String? _errorMsg;
  List<ExperienceCertifcateRequest> _requestList = [];

  bool get isLoading => _isLoading;
  String? get errorMsg => _errorMsg;
  List<ExperienceCertifcateRequest> get requestList => _requestList;

  set isLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  set requestList(List<ExperienceCertifcateRequest> value) {
    _requestList = value;
    notifyListeners();
  }

  Future<bool> fetchPreviousRequests() async {
    if (requestList.isEmpty) {
      EasyLoading.show();
    }
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    Uri uri = Uri.parse(
        '${baseUrl}certificates-list/?action=experience_certificates');
    var response =
        await http.get(uri, headers: {"Authorization": "Bearer $auth"});

    if (response.statusCode == 200) {
      var json = jsonDecode(response.body);
      if (json['result'] == 'success') {
        List records = json['records'];
        requestList = records
            .map((e) => ExperienceCertifcateRequest.fromJson(e))
            .toList();
      }
      EasyLoading.dismiss();
      notifyListeners();
      return true;
    }
    EasyLoading.dismiss();
    notifyListeners();
    return false;
  }

  Future<bool> submitRequest(
      {String? to, String? bankName, String? branchName}) async {
    try {
      isLoading = true;
      _errorMsg = null;
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");
      Uri uri = Uri.parse('${baseUrl}experience-certificates-create/');
      var response = await http.post(
        uri,
        headers: {"Authorization": "Bearer $auth"},
      );
      debugPrint(response.request.toString());
      debugPrint(response.statusCode.toString());
      debugPrint(response.body);
      if (response.statusCode == 200) {
        var json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          isLoading = false;
          return true;
        }
      } else if (response.statusCode == 400) {
        var json = jsonDecode(response.body);
        if (json['result'] == 'failure') {
          Map<String, dynamic> errors = json['erros'];
          if (errors.containsKey('request')) {
            _errorMsg = errors['request'];
          }
          if (errors.containsKey('profile')) {
            _errorMsg = errors['profile'];
          }
        }
      }
      isLoading = false;
      return false;
    } catch (e) {
      isLoading = false;
      debugPrint(e.toString());
      return false;
    }
  }

  String? _experienceCertificate;
  String? get experienceCertificate => _experienceCertificate;
  Future<bool> getExperienceCertificate(
      {required String action, required int actionId}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    try {
      Uri experienceCertificateURL = Uri.parse(
          "${baseUrl}certificates-view/?action=$action&action_id=$actionId");
      var response = await http.get(experienceCertificateURL,
          headers: {"Authorization": "Bearer $auth"});

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json["result"] == "success") {
          if (json.containsKey("records")) {
            Map<String, dynamic> records = json["records"];
            if (records.containsKey("file")) {
              _experienceCertificate = null;
              _experienceCertificate = records["file"];
            }
          }
          return true;
        }
      }
      if (response.statusCode == 400) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json["result"] == "failure") {
          _experienceCertificate = null;
          if (json.containsKey("errors")) {
            showToastText(json["errors"]);
          }
          return false;
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return false;
  }

  Future<bool> experienceCommentCreate({
    required BuildContext context,
    required String action,
    required int actionId,
    required String comment,
  }) async {
    final navigator = Navigator.of(context);
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");

    Map<String, String>? body = {
      "action": action,
      "action_id": actionId.toString(),
      "comment": comment,
    };
    var response = await http.post(commentsCreateURL,
        body: body, headers: {"Authorization": "Bearer $auth"});

    if (response.statusCode == 200) {
      Map<String?, dynamic> json = jsonDecode(response.body);

      if (json["result"] == "success") {
        if (json.containsKey("message")) {
          showToastText(json["message"]);
          navigator.pop();
          navigator.pop();
        }
        return true;
      }
    }
    if (response.statusCode == 400) {
      Map<String?, dynamic> json = jsonDecode(response.body);
      if (json["result"] == "failure") {
        if (json.containsKey("erros")) {
          Map<String, dynamic> erros = json["erros"];
          if (erros.containsKey("request")) {
            showToastText(erros["request"]);
          }
        }
        return false;
      }
    }
    return false;
  }
}
