import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/ai_chat_models.dart';
import '../services/ai_chatbot_service.dart';
import '../services/hr_action_executor.dart';

/// Enhanced Chat Provider that integrates AI chatbot with HR actions
class EnhancedChatProvider extends ChangeNotifier {
  static const String _logPrefix = '[ENHANCED_CHAT_PROVIDER]';

  final AIChatbotService _aiService = AIChatbotService();
  final HRActionExecutor _actionExecutor = HRActionExecutor();

  final List<EnhancedChatMessage> _messages = [];
  bool _isLoading = false;
  ConversationContext? _context;
  String? _currentSessionId;

  // Getters
  List<EnhancedChatMessage> get messages => _messages;
  bool get isLoading => _isLoading;
  ConversationContext? get context => _context;

  /// Initialize chat session
  Future<void> initializeChat() async {
    try {
      _currentSessionId = DateTime.now().millisecondsSinceEpoch.toString();
      _context = ConversationContext(
        userId: await _getCurrentUserId(),
        messages: [],
      );

      // Check if greeting message has already been shown today
      bool shouldShowGreeting = await _shouldShowGreetingMessage();

      if (shouldShowGreeting) {
        // Add welcome message
        _addMessage(EnhancedChatMessage(
          content:
              "Hello! I'm your HR assistant. I can help you with leave applications, work from home requests, expense tracking, and answer questions about company policies. How can I help you today?",
          isUser: false,
          messageType: ChatMessageType.welcome,
        ));

        // Mark greeting as shown for today
        await _markGreetingAsShown();
      }

      if (kDebugMode) {
        log('$_logPrefix Chat session initialized: $_currentSessionId');
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error initializing chat: $e');
      }
    }
  }

  /// Send message and get AI response with potential actions
  Future<void> sendMessage(String message) async {
    if (message.trim().isEmpty || _isLoading) return;

    try {
      _isLoading = true;
      notifyListeners();

      // Add user message
      EnhancedChatMessage userMessage = EnhancedChatMessage(
        content: message,
        isUser: true,
        messageType: ChatMessageType.user,
      );
      _addMessage(userMessage);

      // Add loading message
      EnhancedChatMessage loadingMessage = EnhancedChatMessage(
        content: "Thinking...",
        isUser: false,
        messageType: ChatMessageType.loading,
      );
      _addMessage(loadingMessage);

      // Get AI response
      AIChatResponse aiResponse = await _aiService.sendMessage(
        message: message,
        conversationHistory: _context?.messages,
        context: _buildContextString(),
      );

      // Remove loading message
      _removeMessage(loadingMessage);

      // Process AI response
      await _processAIResponse(aiResponse);
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error sending message: $e');
      }

      // Remove loading message if it exists
      _messages
          .removeWhere((msg) => msg.messageType == ChatMessageType.loading);

      // Add error message
      _addMessage(EnhancedChatMessage(
        content:
            "I apologize, but I'm having trouble processing your request right now. Please try again.",
        isUser: false,
        messageType: ChatMessageType.error,
      ));
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Process AI response and execute actions if needed
  Future<void> _processAIResponse(AIChatResponse aiResponse) async {
    try {
      if (aiResponse.requiresAction) {
        // Add confirmation message before executing action
        _addMessage(EnhancedChatMessage(
          content: aiResponse.aiResponse,
          isUser: false,
          messageType: ChatMessageType.confirmation,
          aiResponse: aiResponse,
        ));

        // Show action execution message
        EnhancedChatMessage actionMessage = EnhancedChatMessage(
          content: "Processing your request...",
          isUser: false,
          messageType: ChatMessageType.processing,
        );
        _addMessage(actionMessage);

        // Add original message to parameters for better context
        Map<String, dynamic> enhancedParameters =
            Map<String, dynamic>.from(aiResponse.parameters);
        enhancedParameters['original_message'] = aiResponse.originalMessage;

        // Create enhanced AI response with original message context
        AIChatResponse enhancedAiResponse = AIChatResponse(
          originalMessage: aiResponse.originalMessage,
          aiResponse: aiResponse.aiResponse,
          intent: aiResponse.intent,
          confidence: aiResponse.confidence,
          requiresAction: aiResponse.requiresAction,
          parameters: enhancedParameters,
        );

        // Execute the action
        ActionResult result =
            await _actionExecutor.executeAction(enhancedAiResponse);

        // Remove processing message
        try {
          _removeMessage(actionMessage);
        } catch (e) {
          if (kDebugMode) {
            log('$_logPrefix Error removing processing message: $e');
          }
        }

        // Add result message
        try {
          _addMessage(EnhancedChatMessage(
            content: result.message,
            isUser: false,
            messageType: result.success
                ? ChatMessageType.success
                : ChatMessageType.error,
            actionResult: result,
          ));
        } catch (e) {
          if (kDebugMode) {
            log('$_logPrefix Error adding result message: $e');
          }
        }

        // Update conversation context
        try {
          _updateContext(aiResponse, result);
        } catch (e) {
          if (kDebugMode) {
            log('$_logPrefix Error updating context: $e');
          }
        }
      } else {
        // Just add the AI response
        _addMessage(EnhancedChatMessage(
          content: aiResponse.aiResponse,
          isUser: false,
          messageType: ChatMessageType.response,
          aiResponse: aiResponse,
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error processing AI response: $e');
      }

      _addMessage(EnhancedChatMessage(
        content:
            "I encountered an error while processing your request. Please try again.",
        isUser: false,
        messageType: ChatMessageType.error,
      ));
    }
  }

  /// Add message to the conversation
  void _addMessage(EnhancedChatMessage message) {
    _messages.add(message);

    // Update context
    if (_context != null) {
      _context!.messages.add(AIChatMessage(
        content: message.content,
        isUser: message.isUser,
        intent: message.aiResponse?.intent,
        requiresAction: message.aiResponse?.requiresAction,
      ));
    }

    notifyListeners();
  }

  /// Remove message from the conversation
  void _removeMessage(EnhancedChatMessage message) {
    _messages.remove(message);
    notifyListeners();
  }

  /// Update conversation context
  void _updateContext(AIChatResponse aiResponse, ActionResult result) {
    if (_context != null) {
      // Create a new mutable map from the existing sessionData
      Map<String, dynamic> newSessionData =
          Map<String, dynamic>.from(_context!.sessionData);
      newSessionData['lastAction'] = aiResponse.intent.toString();
      newSessionData['lastActionResult'] = result.success;
      newSessionData['lastActionTime'] = DateTime.now().toIso8601String();

      // Update the context with the new session data
      _context = ConversationContext(
        userId: _context!.userId,
        messages: _context!.messages,
        sessionData: newSessionData,
      );
    }
  }

  /// Build context string for AI
  String _buildContextString() {
    if (_context == null) return '';

    StringBuffer contextBuffer = StringBuffer();

    // Add recent conversation summary
    if (_context!.messages.isNotEmpty) {
      contextBuffer.writeln('Recent conversation:');
      var recentMessages = _context!.messages.take(5);
      for (var msg in recentMessages) {
        contextBuffer
            .writeln('${msg.isUser ? "User" : "Assistant"}: ${msg.content}');
      }
    }

    // Add session data
    if (_context!.sessionData.isNotEmpty) {
      contextBuffer
          .writeln('Session context: ${jsonEncode(_context!.sessionData)}');
    }

    return contextBuffer.toString();
  }

  /// Get current user ID
  Future<String> _getCurrentUserId() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString('user_id') ?? 'unknown_user';
    } catch (e) {
      return 'unknown_user';
    }
  }

  /// Check if greeting message should be shown
  /// Returns true if greeting hasn't been shown today or if it's a new user session
  Future<bool> _shouldShowGreetingMessage() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String userId = await _getCurrentUserId();
      String today =
          DateTime.now().toIso8601String().split('T')[0]; // Get date part only

      // Check if greeting was shown today for this user
      String? lastGreetingDate = prefs.getString('last_greeting_date_$userId');

      // Show greeting if it's a new day or first time for this user
      return lastGreetingDate != today;
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error checking greeting status: $e');
      }
      // Default to showing greeting if there's an error
      return true;
    }
  }

  /// Mark greeting message as shown for today
  Future<void> _markGreetingAsShown() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String userId = await _getCurrentUserId();
      String today =
          DateTime.now().toIso8601String().split('T')[0]; // Get date part only

      await prefs.setString('last_greeting_date_$userId', today);

      if (kDebugMode) {
        log('$_logPrefix Greeting marked as shown for user $userId on $today');
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error marking greeting as shown: $e');
      }
    }
  }

  /// Clear chat history
  void clearChat({bool resetGreeting = false}) {
    _messages.clear();
    _context = null;
    _currentSessionId = null;

    // Optionally reset greeting status
    if (resetGreeting) {
      _resetGreetingStatus();
    }

    notifyListeners();
  }

  /// Reset greeting status (for testing or when user wants to see greeting again)
  Future<void> _resetGreetingStatus() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String userId = await _getCurrentUserId();
      await prefs.remove('last_greeting_date_$userId');

      if (kDebugMode) {
        log('$_logPrefix Greeting status reset for user $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error resetting greeting status: $e');
      }
    }
  }

  /// Save conversation to local storage
  Future<void> saveConversation() async {
    try {
      if (_context == null || _currentSessionId == null) return;

      SharedPreferences prefs = await SharedPreferences.getInstance();
      String conversationJson = jsonEncode(_context!.toJson());
      await prefs.setString(
          'chat_conversation_$_currentSessionId', conversationJson);

      if (kDebugMode) {
        log('$_logPrefix Conversation saved: $_currentSessionId');
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error saving conversation: $e');
      }
    }
  }

  /// Load conversation from local storage
  Future<void> loadConversation(String sessionId) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? conversationJson =
          prefs.getString('chat_conversation_$sessionId');

      if (conversationJson != null) {
        Map<String, dynamic> data = jsonDecode(conversationJson);
        _context = ConversationContext.fromJson(data);
        _currentSessionId = sessionId;

        // Rebuild messages from context
        _messages.clear();
        for (var contextMessage in _context!.messages) {
          _messages.add(EnhancedChatMessage(
            content: contextMessage.content,
            isUser: contextMessage.isUser,
            messageType: contextMessage.isUser
                ? ChatMessageType.user
                : ChatMessageType.response,
            timestamp: contextMessage.timestamp,
          ));
        }

        notifyListeners();

        if (kDebugMode) {
          log('$_logPrefix Conversation loaded: $sessionId');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error loading conversation: $e');
      }
    }
  }

  /// Get quick action suggestions
  List<String> getQuickActions() {
    return [
      "Apply for sick leave tomorrow",
      "Request work from home for next week",
      "Check my leave balance",
      "Show upcoming holidays",
      "Book a meeting room for 2 PM",
      "Submit expense for client dinner",
    ];
  }

  /// Handle quick action selection
  Future<void> selectQuickAction(String action) async {
    await sendMessage(action);
  }
}

/// Enhanced chat message model
class EnhancedChatMessage {
  final String content;
  final bool isUser;
  final ChatMessageType messageType;
  final DateTime timestamp;
  final AIChatResponse? aiResponse;
  final ActionResult? actionResult;

  EnhancedChatMessage({
    required this.content,
    required this.isUser,
    required this.messageType,
    DateTime? timestamp,
    this.aiResponse,
    this.actionResult,
  }) : timestamp = timestamp ?? DateTime.now();
}

/// Enum for different types of chat messages
enum ChatMessageType {
  user,
  response,
  welcome,
  confirmation,
  processing,
  success,
  error,
  loading,
}
