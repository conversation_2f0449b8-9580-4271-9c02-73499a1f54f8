// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'dart:io';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FAQProvider extends ChangeNotifier {
  //image section
  List<File> selectedImages = [];
  final imagePicker = ImagePicker();
  Future<void> pickImageFromCamera({required BuildContext context}) async {
    XFile? pickedImage = await imagePicker.pickImage(
      source: ImageSource.camera,
      imageQuality: 75,
    );

    if (pickedImage != null) {
      File file = File(pickedImage.path);
      int sizeInBytes = file.lengthSync();
      double sizeInMb = sizeInBytes / (1024 * 1024);
      if (sizeInMb > 4) {
        showSnackBarMessage(
            context: context, msg: 'Please select image less than 4 MB');
      } else {
        selectedImages.add(File(pickedImage.path));
        notifyListeners();
      }
    }
  }

  Future<void> pickImageFromGallery({required BuildContext context}) async {
    List<XFile> pickedImagesList =
        await imagePicker.pickMultiImage(imageQuality: 75);

    if (pickedImagesList.isNotEmpty) {
      selectedImages.addAll(pickedImagesList.map((e) => File(e.path)).toList());
      notifyListeners();
    }
  }

  Future<void> removeImage(int index) async {
    selectedImages.removeAt(index);
    notifyListeners();
  }

  //
  bool _isAnonymous = false;
  bool get isAnonymous => _isAnonymous;
  set isAnonymous(bool value) {
    _isAnonymous = value;
    notifyListeners();
  }

  bool _isLoading = false;
  bool get isLoading => _isLoading;
  set isLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  Future<void> submitFeedback(
      {required String title,
      required String description,
      required context}) async {
    isLoading = true;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    MultipartRequest request = MultipartRequest('POST', feedbackURL);
    request.headers['Authorization'] = 'Bearer $auth';
    request.fields['name'] = LoginModel.name ?? '';
    request.fields['title'] = title;
    request.fields['description'] = description;
    request.fields['is_anonymous'] = isAnonymous.toString();
    if (selectedImages.isNotEmpty) {
      for (var image in selectedImages) {
        request.files.add(await MultipartFile.fromPath('images', image.path));
      }
    }
    StreamedResponse response = await request.send();
    if (response.statusCode == 200) {
      isLoading = false;
      Map<String, dynamic> data =
          json.decode(await response.stream.bytesToString());
      showSnackBarMessage(context: context, msg: data['message']);
      Navigator.pop(context);
    } else if (response.statusCode == 400) {
      isLoading = false;
      Map<String, dynamic> data =
          json.decode(await response.stream.bytesToString());
      showSnackBarMessage(
          context: context,
          msg: (data['images']['0'] as List).first.toString());
    } else {
      isLoading = false;
      showSnackBarMessage(context: context, msg: 'Something went wrong!!!');
    }
    isLoading = false;
  }
}
