import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

import '../util/dailoge.dart';

class GeneralProvider extends ChangeNotifier {
  String _progressing = "0";
  String get progressing => _progressing;
  set progressing(String value) {
    _progressing = value;
    notifyListeners();
  }

  PermissionStatus? _status;
  PermissionStatus? get status => _status;
  set status(PermissionStatus? value) {
    _status = value;
    notifyListeners();
  }

  Future<void> pdfDownload(
      {required String fileurl,
      required String fileName,
      required String extension,
      required BuildContext context}) async {
    final navigator = Navigator.of(context);
    Map<Permission, PermissionStatus> statuses = await [
      Permission.storage,
      Permission.accessMediaLocation,
      Permission.manageExternalStorage,
      Permission.photos,
      //add more permission to request here.
    ].request();
    if (statuses[Permission.photos]!.isGranted) {
      {
        String directory = "/storage/emulated/0/Download/";
        bool dirDownloadExists = await Directory(directory).exists();
        if (dirDownloadExists) {
          directory = "/storage/emulated/0/Download/";
        } else {
          directory = "/storage/emulated/0/Downloads/";
          // Create a new directory, recursively creating non-existent directories.
          if (!await Directory(directory).exists()) {
            File(directory).createSync(recursive: true);
          }
        }
        final path = directory;

        String savename = fileName;
        String savePath = "$path$savename";

        //output:  /storage/emulated/0/Download/banner.png

        try {
          await Dio().download(fileurl, savePath,
              onReceiveProgress: (received, total) {
            if (total != -1) {
              progressing = "${(received / total * 100).toStringAsFixed(0)}%";
              //you can build progressbar feature too
              if ((received / total * 100) == 100) {
                navigator.pop();
                showToastText("Downloaded");
                SnackBar snackBar = SnackBar(
                  content: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text("Document Downloaded"),
                      OutlinedButton(
                        onPressed: () async {
                          await launchInBrowser(url: fileurl);
                          // PageNavigator.push(
                          //   context: context,
                          //   route: PhotoViewScreen(
                          //     image: fileurl,
                          //     extension: "pdf",
                          //   ),
                          // );
                        },
                        child: const Text("View"),
                      ),
                    ],
                  ),
                  behavior: SnackBarBehavior.floating,
                );
                ScaffoldMessenger.of(context).showSnackBar(snackBar);
              }
            }
          });
        } on DioException catch (e) {
          navigator.pop();
          showToastText("Document not found");
          debugPrint(e.message.toString());
        }
      }
      notifyListeners();
    } else {
      await getStatus(context: context);
    }
  }

  Future<void> launchInBrowser({required String url}) async {
    Uri uri = Uri.parse(url);
    if (!await launchUrl(
      uri,
      mode: LaunchMode.externalApplication,
    )) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> launchUniversalLinkIos({required String url}) async {
    Uri uri = Uri.parse(url);
    final bool nativeAppLaunchSucceeded = await launchUrl(
      uri,
      mode: LaunchMode.externalNonBrowserApplication,
    );
    if (!nativeAppLaunchSucceeded) {
      await launchUrl(
        uri,
        mode: LaunchMode.inAppWebView,
      );
    }
  }

  Future<void> getStatus({required BuildContext context}) async {
    status = await Permission.photos.status;

    // ignore: use_build_context_synchronously
    await requestPermission(context: context);
    notifyListeners();
  }

  Future<void> requestPermission({required BuildContext context}) async {
    if (status!.isDenied) {
      final navigator = Navigator.of(context);
      navigator.pop();

      await Permission.photos.request();

      openAppSettings();
    } else if (status!.isPermanentlyDenied) {
      await openAppSettings();
    }
    notifyListeners();
  }
}
