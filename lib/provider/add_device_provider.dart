// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:e8_hr_portal/model/ble_model.dart';
import 'package:e8_hr_portal/util/api_headers.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';

import 'ble_attendance_provider.dart';

class AddDeviceProvider extends ChangeNotifier {
  bool _isScanning = false;
  bool get isScanning => _isScanning;
  set isScanning(bool status) {
    _isScanning = status;
    notifyListeners();
  }

  StreamSubscription<BluetoothAdapterState>? _blueSubscription;
  Future<void> _checkBluetoothAvailability() async {
    debugPrint('Check bluetooth isAvailable or notwww');
    bool isSupported = await FlutterBluePlus.isSupported;
    if (!isSupported) {
      debugPrint("Bluetooth not supported by this device");
      return;
    }

    if (Platform.isAndroid) {
      _blueSubscription = FlutterBluePlus.adapterState.listen((event) async {
        if (event == BluetoothAdapterState.off) {
          await FlutterBluePlus.turnOn();
        } else {
          _blueSubscription?.cancel();
        }
      });
    }
  }

  List<BLEModel> bleList = [];

  scanNearbyDevices() async {
    try {
      bleList.clear();
      await _checkBluetoothAvailability();
      FlutterBluePlus.scanResults.listen(checkResult);
      FlutterBluePlus.isScanning.listen(handleScanningStatus);
      await FlutterBluePlus.startScan(
        timeout: const Duration(seconds: 5),
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  stopScanning() async {
    await FlutterBluePlus.stopScan();
  }

  checkResult(List<ScanResult> results) {
    for (ScanResult r in results) {
      String deviceName = r.advertisementData.advName;
      Map<Guid, List<int>> serviceData = r.advertisementData.serviceData;
      bool isSeen = bleList
          .where((e) => e.id == r.device.remoteId.str)
          .toList()
          .isNotEmpty;
      if (!isSeen && deviceName.isNotEmpty && serviceData.isNotEmpty) {
        bleList.add(
          BLEModel(
            id: r.device.remoteId.str,
            name: deviceName,
          ),
        );
        notifyListeners();
      }
    }
  }

  handleScanningStatus(bool scanning) {
    if (!scanning) {
      isScanning = false;
    } else {
      isScanning = true;
    }
  }

  bool _isDeviceAddingLoading = false;
  bool get isDeviceAddingLoading => _isDeviceAddingLoading;
  set isDeviceAddingLoading(bool value) {
    _isDeviceAddingLoading = value;
    notifyListeners();
  }

  Future<bool> addBlEDevice(
      String deviceId, String name, BuildContext context) async {
    isDeviceAddingLoading = true;
    try {
      Uri uri = Uri.parse('${baseUrl}add-ble-device/');
      String platform = 'Android';
      if (Platform.isIOS) {
        platform = 'IOS';
      }
      Map<String, dynamic> body = {
        'device_id': deviceId,
        'name': name,
        'platform': platform,
      };
      final response = await http.post(
        uri,
        body: body,
        headers: await APIHeaders.getApiHeader(),
      );
      if (response.statusCode == 200) {
        isDeviceAddingLoading = false;
        var json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          BLEAttendanceProvider provider =
              Provider.of<BLEAttendanceProvider>(context, listen: false);
          provider.getBLEDeviceList();
          return true;
        } else {
          return false;
        }
      } else if (response.statusCode == 400) {
        Map<String, dynamic> error = jsonDecode(response.body);
        isDeviceAddingLoading = false;
        if (error.containsKey('error')) {
          Map<String, dynamic> error1 = error['error'];
          if (error1.containsKey('error')) {
            showToastText(error1['error']);
          }
        }
        return false;
      } else {
        isDeviceAddingLoading = false;

        showToastText('Something went wrong !!');
        return false;
      }
    } catch (e) {
      isDeviceAddingLoading = false;
      debugPrint(e.toString());
      return false;
    }
  }
}
