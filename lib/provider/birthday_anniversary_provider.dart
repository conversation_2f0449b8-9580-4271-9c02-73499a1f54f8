// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'package:e8_hr_portal/provider/sign_in_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/birthday_anniversary_model.dart' as d;
import 'package:e8_hr_portal/model/birthday_model.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:http/http.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../util/urls.dart';

class BirthdayAnniversaryProvider with ChangeNotifier {
  List<BirthdayModel> _birthdayList = [];
  List<BirthdayModel> get birthdayList => _birthdayList;
  set birthdayList(List<BirthdayModel> value) {
    _birthdayList = value;
    notifyListeners();
  }

  bool _click = false;
  bool get click => _click;
  set click(bool value) {
    _click = value;
    notifyListeners();
  }

  List<d.Data> todayList = [];
  List<d.Data> upcomingList = [];
  d.BirthdayAnniversaryModel? birthdayAnniversaryList;
  Future<bool> getBirthdayAnniversary(
      {required bool master, required BuildContext context}) async {
    if (!master) {
      if (birthdayList.isEmpty) {
        EasyLoading.show();
      }
    }
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? token = shared.getString('access_token');
    try {
      Response response = await get(
          Uri.parse("${baseUrl}upcoming_birthday_anniversary/"),
          headers: {"Authorization": "Bearer $token"});

      if (response.statusCode == 200) {
        todayList.clear();
        upcomingList.clear();
        Map<String, dynamic> data = jsonDecode(response.body);
        List temp = data['data'];
        birthdayList = temp.map((e) => BirthdayModel.fromJson(e)).toList();
        birthdayAnniversaryList = d.BirthdayAnniversaryModel.fromJson(data);
        DateTime now = DateTime.now();
        String today = formatDateFromDate(dateTime: now, format: "MM-dd");
        birthdayAnniversaryList?.data?.forEach((element) {
          if (element.model == "Birthday") {
            String? dob = formatDateFromString(
                element.dateOfBirth.toString(), 'yyyy-MM-dd', 'MM-dd');

            if (dob == today) {
              todayList.add(element);
            } else {
              upcomingList.add(element);
            }
          } else {
            String? doj = formatDateFromString(
                element.dateOfJoining.toString(), 'yyyy-MM-dd', 'MM-dd');
            if (doj == today) {
              todayList.add(element);
            } else {
              upcomingList.add(element);
            }
          }
        });
        notifyListeners();
        EasyLoading.dismiss();
        return true;
      } else if (response.statusCode == 401) {
        context.read<SignInProvider>().updateToken(context: context);
        // PageNavigator.pushAndRemoveUntil(
        //   context: context,
        //   route: const LoginWithEmailScreen(),
        // );
        notifyListeners();
        EasyLoading.dismiss();
        return false;
      } else {
        birthdayAnniversaryList = null;
        notifyListeners();
        EasyLoading.dismiss();
        return false;
      }
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint(e.toString());
    }
    return false;
  }
}
