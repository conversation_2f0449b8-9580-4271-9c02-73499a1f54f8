import 'dart:convert';
import 'dart:developer';
import 'package:e8_hr_portal/model/team_member_model.dart';
import 'package:e8_hr_portal/provider/sign_in_provider.dart';
import 'package:e8_hr_portal/util/api_headers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/country_model.dart';
import 'package:e8_hr_portal/model/designation_model.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:http/http.dart';
import 'package:e8_hr_portal/model/leave_employee_model.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TeamMembersProvider extends ChangeNotifier {
  TeamMemberModel? recentSearchEmpDetails;
  bool _isRecentSearchShow = true;
  bool get isRecentSearchShow => _isRecentSearchShow;
  set isRecentSearchShow(bool value) {
    _isRecentSearchShow = value;
    notifyListeners();
  }

  TeamMemberModel? _selectedTeamMemberModel;
  TeamMemberModel? get selectedTeamMemberModel => _selectedTeamMemberModel;
  set selectedTeamMemberModel(TeamMemberModel? value) {
    _selectedTeamMemberModel = value;
    notifyListeners();
  }

  ////////////branch section
  List<int> selectedBranchList = [];
  void addtoSelectedBranchList(int? id) {
    if (!selectedBranchList.contains(id)) {
      selectedBranchList.add(id!);
      notifyListeners();
    }
  }

  void removefromBranchList(int? id) {
    selectedBranchList.remove(id);
    notifyListeners();
  }

/////////////////designation section
  List<int> selectedDesignation = [];
  void addtoSelectedDesignaion(int? id) {
    if (!selectedDesignation.contains(id)) {
      selectedDesignation.add(id!);
      notifyListeners();
    }
  }

  void removeFromSelectedDesignation(int? id) {
    selectedDesignation.remove(id);
    notifyListeners();
  }

//////////////////work status
  List<int> selectedWorkstatus = [];
  void addtoSelectedWorkStatus(int? id) {
    if (!selectedWorkstatus.contains(id)) {
      selectedWorkstatus.add(id!);
      notifyListeners();
    }
  }

  void removeFromSelectedWorkStatus(int? id) {
    selectedWorkstatus.remove(id);
    notifyListeners();
  }

//////////////////////////////////////
  void clearSelectedFilter() {
    selectedBranchList.clear();
    selectedDesignation.clear();
    selectedWorkstatus.clear();
    notifyListeners();
  }

  Future<void> getRecentEmpDetails({required String id}) async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? token = shared.getString('access_token');
    try {
      var response = await get(Uri.parse("${baseUrl}user_details/$id/"),
          headers: {"Authorization": "Bearer $token"});
      debugPrint("fhd fhd${response.request}");
      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);
        recentSearchEmpDetails = TeamMemberModel.fromJson(data["data"]);
      } else {
        recentSearchEmpDetails = null;
      }
      notifyListeners();
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint(e.toString());
    }
  }
  //badge

  //

  List<TeamMemberModel> searchResultList = [];

  bool isMembersLoading = false;

  final List<LeaveEmployeeModel> _leaveEmployees = [];

  List<LeaveEmployeeModel> get leaveEmployees => _leaveEmployees;

  List<TeamMemberModel> teamMembersList = [];
  // String? userStatus;
  // String? userProfile;
  String? designationMy;
  int? newMembersCount;
  int? totalCount;
  Future<void> getTeamMembers({
    required bool isFilter,
    required bool master,
    required BuildContext context,
  }) async {
    if (!master) {
      if (teamMembersList.isEmpty) {
        EasyLoading.show();
      }
    }
    isMembersLoading = true;
    Response response;
    SharedPreferences shared = await SharedPreferences.getInstance();
    designationMy = shared.getString('designation');

    if (isFilter == false) {
      response =
          await get(userlistURL, headers: await APIHeaders.getApiHeader());
    } else {
      String branch = selectedBranchList.join(',');
      String designation = selectedDesignation.join(',');
      String workStatus = selectedWorkstatus.join(',');
      StringBuffer urlBuffer = StringBuffer(baseUrl);
      urlBuffer.write('user_filter/?');
      urlBuffer.write('branch=$branch');
      urlBuffer.write('&designation=$designation');
      urlBuffer.write('&user_status=$workStatus');
      Uri uri = Uri.parse(urlBuffer.toString());
      response = await get(uri, headers: await APIHeaders.getApiHeader());
    }
    log('getTeamMembers --> ${(response.body)}');
    if (response.statusCode == 200) {
      isMembersLoading = false;
      var json = jsonDecode(response.body);
      if (json['result'] == 'success') {
        List data = json['data'] ?? [];
        teamMembersList = data.map((e) => TeamMemberModel.fromJson(e)).toList();
        teamMembersList.sort(((a, b) => a.name!.compareTo(b.name!)));
        newMembersCount = json['new_members_count'];
        totalCount = json['total_count'];
      }
    } else if (response.statusCode == 401 && context.mounted) {
      final signInProvider = context.read<SignInProvider>();
      signInProvider.updateToken(context: context);
    } else {
      teamMembersList.clear();
    }
    EasyLoading.dismiss();
    notifyListeners();
  }

  Future<void> getSearchResult(String keyword) async {
    // EasyLoading.show();
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? token = shared.getString('access_token');
      Response response = await get(
          Uri.parse("${baseUrl}user_search/?search_text=$keyword"),
          headers: {"Authorization": "Bearer $token"});
      if (response.statusCode == 200) {
        var json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          List data = json['data'] ?? [];
          searchResultList =
              data.map((e) => TeamMemberModel.fromJson(e)).toList();
        }
      } else {
        searchResultList.clear();
      }
      notifyListeners();
      EasyLoading.dismiss();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  DesignationModel? designationList;
  Future<void> getDesignation({required bool master}) async {
    if (!master) {
      if (designationList == null || designationList!.data!.isEmpty) {
        EasyLoading.show();
      }
    }
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? token = shared.getString('access_token');
    Response response = await get(Uri.parse("${baseUrl}designation_list/"),
        headers: {"Authorization": "Bearer $token"});
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      designationList = DesignationModel.fromJson(data);
    } else {
      designationList = null;
    }
    notifyListeners();

    EasyLoading.dismiss();
  }

  CountryModel? countryList;
  Future<void> getBranchList({required bool master}) async {
    if (!master) {
      if (countryList == null || countryList!.data!.isEmpty) {
        EasyLoading.show();
      }
    }

    SharedPreferences shared = await SharedPreferences.getInstance();
    String? token = shared.getString('access_token');
    Response response = await get(Uri.parse("${baseUrl}branch_list/"),
        headers: {"Authorization": "Bearer $token"});

    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      countryList = CountryModel.fromJson(data);
    } else {
      countryList = null;
    }
    notifyListeners();
    EasyLoading.dismiss();
  }

  // Future<void> filterEmployee() async {
  //   SharedPreferences shared = await SharedPreferences.getInstance();
  //   String? token = shared.getString('access_token');
  //   try {
  //     Response response = await get(Uri.parse("${baseUrlHisense}/branch_list/"),
  //         headers: {"Authorization": "Bearer $token"});
  //   } catch (e) {
  //     print(e.toString());
  //   }
  // }
}
