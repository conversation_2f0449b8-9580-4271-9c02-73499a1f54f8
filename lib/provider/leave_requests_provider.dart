import 'package:flutter/material.dart';

class LeaveRequestsProvider extends ChangeNotifier {
//   int _selectedRequestItemID = 1;
//   int get selectedRequestItemID => _selectedRequestItemID;
//   set selectedRequestItemID(int value) {
//     _selectedRequestItemID = value;
//     notifyListeners();
//   }

//   List<LeaveRequestFilterModel> leaveFilterModel = [
//     LeaveRequestFilterModel("All", 1),
//     LeaveRequestFilterModel("Today", 2),
//     LeaveRequestFilterModel("Weekly", 3),
//     LeaveRequestFilterModel("Monthly", 4),
//   ];
// }

// class LeaveRequestFilterModel {
//   final String text;
//   final int id;
//   LeaveRequestFilterModel(this.text, this.id);
}
