// ignore_for_file: use_build_context_synchronously
import 'dart:convert';
import 'package:e8_hr_portal/model/anniversary_model.dart';
import 'package:e8_hr_portal/model/get_birthday_model.dart';
import 'package:e8_hr_portal/model/holidays_model.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/model/recent_activity_model.dart';
import 'package:e8_hr_portal/model/user_policies_model.dart';
import 'package:e8_hr_portal/provider/sign_in_provider.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:http/http.dart';

import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:e8_hr_portal/model/event_detailes_model.dart' as data;
import '../util/general_functions.dart';

class CompanyActivitiesProvider extends ChangeNotifier {
  List<int> deletedImageId = [];

  List<data.EventImg> imageListForEditEvent = [];
  void addImage(List<data.EventImg> list) {
    imageListForEditEvent = list;
    notifyListeners();
  }

  void removeImage(int index, int imageId) {
    imageListForEditEvent.removeAt(index);
    deletedImageId.add(imageId);
    notifyListeners();
  }

  bool isAnniversayLoading = false;
  AnniversaryModel? anniversaryList;
  bool isBirthdayLoading = false;
  GetBirthdayModel? birthdayList;
  List<HolidaysModel> indianHolidaysList = [];
  List<HolidaysModel> upcomingHolidaysList = [];
  HolidaysModel? uaeHolidaysList;
  bool isIndianLoading = false;
  bool isUaeLoading = false;

  // List<HolidaysModel> holidaysUae = [];
  // Future<HolidaysModel?> getHolidaysUae() async {
  //   await FirebaseFirestore.instance
  //       .collection("holiday_calendar")
  //       .where("country", isEqualTo: "UAE")
  //       .orderBy("date")
  //       .get()
  //       .then((value) {
  //     holidaysUae =
  //         value.docs.map((e) => HolidaysModel.fromJson(e.data())).toList();

  //     notifyListeners();
  //   });
  //   return null;
  // }

  Future<void> getUpcomingHolidays() async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      var response = await get(upcomingHolidaysURL, headers: {
        'Authorization': 'Bearer ${shared.getString('access_token')}',
      });

      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);
        upcomingHolidaysList = (data['data'] as List)
            .map((e) => HolidaysModel.fromJson(e))
            .toList();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  UserPoliciesModel? _selectedPoliciesType;
  UserPoliciesModel? get selectedPolicesType => _selectedPoliciesType;
  set selectedPolicesType(UserPoliciesModel? value) {
    _selectedPoliciesType = value;
    notifyListeners();
  }

  // String get selectedPolice => _selectedPoliciesType;
  // set selectedPolice(String value) {
  //   _selectedPoliciesType = value;
  //   notifyListeners();
  // }

  List<UserPoliciesModel> holidaysPolicies = [];
  Future<void> getUserPoliciesHolidays() async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      var response = await get(userPoliciesURL, headers: {
        'Authorization': 'Bearer ${shared.getString('access_token')}',
      });

      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);
        holidaysPolicies = (data["data"] as List)
            .map((e) => UserPoliciesModel.fromJson(e))
            .toList();

        _selectedPoliciesType = holidaysPolicies.firstWhere(
          (e) => e.id == (LoginModel.policyId ?? 2),
          orElse: () => UserPoliciesModel(),
        );
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  String? upcomingHoliday;
  List<DateTime?> temp = [];
  List<DateTime?> holidayDateList = [];
  List holiDates = [];
  Future<void> getHolidays() async {
    try {
      EasyLoading.show();
      SharedPreferences shared = await SharedPreferences.getInstance();
      var response = await get(
          Uri.parse("$holidaysURL?policy=${_selectedPoliciesType?.id ?? 2}"),
          headers: {
            'Authorization': 'Bearer ${shared.getString('access_token')}',
          });

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json.containsKey("data")) {
          List data = json["data"];
          indianHolidaysList.clear();
          indianHolidaysList =
              data.map((e) => HolidaysModel.fromJson(e)).toList();
          // _selectedPoliciesType =
          //           holidaysPolicies.where((e) => e.id == 2) as UserPoliciesModel;

          // if (indianHolidaysList.isNotEmpty) {
          //   temp.clear();
          //   indianHolidaysList
          //       .map((e) => temp.add(stringToDateTime(
          //           date: e.startDate ?? '', format: 'yyyy-MM-ddThh:mm:ssZ')))
          //       .toList();
          // }
          if (indianHolidaysList.isNotEmpty) {
            var upcoming = indianHolidaysList.firstWhere((e) {
              var date = stringToDateTime(
                  date: e.startDate ?? '', format: 'yyyy-MM-ddThh:mm:ssZ');

              if (date != null && date.isAfter(DateTime.now())) {
                return true;
              }
              return false;
            }, orElse: () => indianHolidaysList.first);

            // upcomingHolidaysList = indianHolidaysList
            //     .where((e) {
            //       var date = stringToDateTime(
            //           date: e.startDate ?? '', format: 'yyyy-MM-ddThh:mm:ssZ');

            //       return date!.isAfter(DateTime.now());
            //     })
            //     .take(3)
            //     .toList();
            String? day = formatDateFromString(
                upcoming.startDate ?? '', 'yyyy-MM-ddThh:mm:ssZ', 'dd MMM');
            upcomingHoliday = '$day - ${upcoming.occassion}';

            for (var date in indianHolidaysList) {
              holiDates.add(formatDateFromString(date.startDate!,
                  "yyyy-MM-ddThh:mm:ssZ", "yyyy-MM-dd 00:00:00.000"));

              if (date.startDate != date.endDate) {
                var days = getDaysInBetween(DateTime.parse(date.startDate!),
                    DateTime.parse(date.endDate!));

                holiDates.add(formatDateFromString(date.endDate!,
                    "yyyy-MM-ddThh:mm:ssZ", "yyyy-MM-dd 00:00:00.000"));
                var day1 = formatDateFromString(date.startDate!,
                    "yyyy-MM-ddThh:mm:ssZ", "yyyy-MM-dd 00:00:00.000");
                var day2 = formatDateFromString(date.endDate!,
                    "yyyy-MM-ddThh:mm:ssZ", "yyyy-MM-dd 00:00:00.000");

                List<String> stringDays = days
                    .where((element) => element != day1 && element != day2)
                    .toList();

                if (stringDays.isNotEmpty) {
                  var difDays = formatDateFromString(stringDays.first,
                      "yyyy-MM-dd 00:00:00.000", "yyyy-MM-dd 00:00:00.000");

                  holiDates.add(formatDateFromString(difDays,
                      "yyyy-MM-dd 00:00:00.000", "yyyy-MM-dd 00:00:00.000"));
                }
              }
              // formatDateFromString(date.startDate!, yyyy-MM-ddThh:mm:ssZ, "dd MM yyyy")
            }

            notifyListeners();
          }
        } else {
          indianHolidaysList.clear();
        }
      }
      EasyLoading.dismiss();
    } catch (e) {
      debugPrint("$e");
    }
    EasyLoading.dismiss();
    notifyListeners();
  }

  List<String> getDaysInBetween(DateTime startDate, DateTime endDate) {
    List<String> days = [];
    for (int i = 0; i <= endDate.difference(startDate).inDays; i++) {
      days.add(formatDateFromString(startDate.add(Duration(days: i)).toString(),
          "yyyy-MM-dd 00:00:00.000", "yyyy-MM-dd 00:00:00.000"));
    }
    return days;
  }

  Future<void> getBirthday() async {
    isBirthdayLoading = true;
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      var response = await get(upcomigBirthdayURL, headers: {
        'Authorization': 'Bearer ${shared.getString('access_token')}',
      });

      if (response.statusCode == 200) {
        isBirthdayLoading = false;
        var data = jsonDecode(response.body);
        birthdayList = GetBirthdayModel.fromJson(data);
      }
      notifyListeners();
    } catch (e) {
      isBirthdayLoading = false;
      notifyListeners();
    }
  }

  Future<void> getAnniversary() async {
    try {
      isAnniversayLoading = true;
      SharedPreferences shared = await SharedPreferences.getInstance();
      var response = await get(workAnniversaryURL, headers: {
        'Authorization': 'Bearer ${shared.getString('access_token')}',
      });
      if (response.statusCode == 200) {
        isAnniversayLoading = false;
        var data = jsonDecode(response.body);
        anniversaryList = AnniversaryModel.fromJson(data);
      }
      notifyListeners();
    } catch (e) {
      isAnniversayLoading = false;
      notifyListeners();
      debugPrint(e.toString());
    }
  }

////////////////////recent activities with pagination//////////////////////////
  int currentPage = 0;
  late PagingController<int, RecentActivityModel> pagingController;
  init() {
    currentPage = 0;
    pagingController = PagingController(firstPageKey: 1);
    pagingController.addPageRequestListener((pageKey) {
      getRecentActivities(pageKey);
    });
  }

  Future<void> getRecentActivities(int page) async {
    if (currentPage != page) {
      currentPage = page;

      SharedPreferences shared = await SharedPreferences.getInstance();
      String? token = shared.getString('access_token');
      // try {
      Response response = await get(
          Uri.parse("${baseUrl}recent_activity/?limit=20&page=$page"),
          headers: {"Authorization": "Bearer $token"});

      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);
        List<RecentActivityModel> temp = [];
        temp = (data["data"] as List)
            .map((e) => RecentActivityModel.fromJson(e))
            .toList();

        if (data["has_next"] == true) {
          int nextPage = page + 1;
          pagingController.appendPage(temp, nextPage);
        } else {
          pagingController.appendLastPage(temp);
        }
      } else {
        // List<RecentActivityModel> temp = [];

        int nextPage = 0;
        pagingController.appendPage([], nextPage);
      }

      notifyListeners();
      // } catch (e) {
      //   debugPrint(e.toString());
      // }
    }
  }

////////////////clear all recent activities/////////////////////
  bool _showClearButton = true;
  bool get showClearButton => _showClearButton;
  set showClearButton(bool value) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _showClearButton = value;
      notifyListeners();
    });
  }

  bool _isClearing = false;
  bool get isClearing => _isClearing;
  set isClearing(bool value) {
    _isClearing = value;
    notifyListeners();
  }

  Future<void> clearRecentActivity({required BuildContext context}) async {
    isClearing = true;
    EasyLoading.show();

    SharedPreferences shared = await SharedPreferences.getInstance();
    Response response =
        await put(Uri.parse("${baseUrl}recent_activity/"), headers: {
      'Authorization': 'Bearer ${shared.getString('access_token')}',
    });
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      await showSnackBarMessage(context: context, msg: data["message"]);

      currentPage = 0;

      pagingController.refresh();
      pagingController.notifyListeners();
      recentActivity.clear();
    }
    EasyLoading.dismiss();
    isClearing = false;
  }

  /////////////////////////////////////
  List<RecentActivityModel> recentActivity = [];
  Future<void> getRecentActivity(
      {required bool master, required BuildContext context}) async {
    if (!master) {
      if (recentActivity.isEmpty) {
        EasyLoading.show();
      }
    }

    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? token = shared.getString('access_token');
      Response response = await get(
          Uri.parse("${baseUrl}recent_activity/?limit=10&page=1"),
          headers: {"Authorization": "Bearer $token"});

      recentActivity.clear();

      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);
        recentActivity = (data["data"] as List)
            .map((e) => RecentActivityModel.fromJson(e))
            .toList();

        notifyListeners();
      } else if (response.statusCode == 401) {
        context.read<SignInProvider>().updateToken(context: context);
        // EasyLoading.dismiss();
        // PageNavigator.pushAndRemoveUntil(
        //   context: context,
        //   route: const LoginWithEmailScreen(),
        // );
      }
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint(e.toString());
    }
  }
}
