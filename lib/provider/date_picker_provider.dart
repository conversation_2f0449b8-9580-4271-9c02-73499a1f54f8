import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:month_picker_dialog/month_picker_dialog.dart';
import '../util/size_config.dart';
import '../util/styles.dart';

class DatePickerProvider extends ChangeNotifier {
  Future<DateTime?> pickDate(
      {required BuildContext context,
      DateTime? lastDate,
      DateTime? firstDate}) async {
    final now = DateTime.now();
    DateTime? picked = await showDatePicker(
        context: context,
        initialDate: now,
        firstDate: firstDate ?? DateTime(now.year - 50),
        lastDate: lastDate ?? DateTime(now.year + 50));
    return picked;
  }

  DateTime? _selectedDate;
  DateTime? get selectedDate => _selectedDate;
  set selectedDate(value) {
    _selectedDate = value;
    notifyListeners();
  }

  Future<DateTime?> yearPicker(
      {required BuildContext context,
      DateTime? lastDate,
      DateTime? firstDate}) async {
    final now = DateTime.now();
    selectedDate = now;

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Select Year", style: tsS18w500c161616),
          content: SizedBox(
            width: 300,
            height: 200,
            child: YearPicker(
              firstDate: firstDate ?? DateTime(now.year - 50),
              lastDate: lastDate ?? DateTime(now.year + 50),
              initialDate: DateTime.now(),
              selectedDate: selectedDate,
              currentDate: selectedDate,
              onChanged: (DateTime dateTime) {
                selectedDate = dateTime;
                Navigator.pop(context);
              },
            ),
          ),
        );
      },
    );
    return selectedDate;
  }

  Future<DateTime?> monthAndYearPicker(
      {required BuildContext context,
      DateTime? lastDate,
      DateTime? firstDate}) async {
    final now = DateTime.now();
    // selectedDate = null;
    // return selectedDate = await showMonthPicker(

    //   context: context,
    //   initialDate: now,
    // );
    log('message -------- ${selectedDate}');
    final date = await showMonthPicker(
      monthPickerDialogSettings: MonthPickerDialogSettings(
        dialogSettings: PickerDialogSettings(
            dismissible: true,
            customWidth: w * 600,
            customHeight: h * 235,
            dialogRoundedCornersRadius: 10),
        buttonsSettings: const PickerButtonsSettings(
            unselectedMonthsTextColor: Colors.black),
      ),
      context: context,
      initialDate: selectedDate ?? now,
      firstDate: firstDate ?? DateTime(now.year - 50, now.month, now.day),
      lastDate: lastDate ?? DateTime(now.year + 50, now.month, now.day),
    );
    if (date != null) {
      return selectedDate = date;
    }
    return selectedDate;
  }
}
