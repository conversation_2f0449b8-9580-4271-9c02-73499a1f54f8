// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/model/project_model.dart';
import 'package:e8_hr_portal/model/total_working_hours_model.dart';
import 'package:e8_hr_portal/model/user_current_tasks.dart';
import 'package:e8_hr_portal/util/constants.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:http/http.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LogTimeProvider extends ChangeNotifier {
  //completed tasks section
  DateTime _selectedDate = DateTime.now();
  DateTime get selectedDate => _selectedDate;
  set selectedDate(DateTime date) {
    _selectedDate = date;
    notifyListeners();
  }

  List<UserCurrentTasks> completedTasksList = [];
  Future<void> getCompletedTasks() async {
    var response =
        await post(Uri.parse('$workflowURL/user-timelog-listing/'), headers: {
      'client-id': workflowClientID,
      'email': LoginModel.email.toString(),
    }, body: {
      'action': 'date',
      'task_date':
          formatDateFromDate(dateTime: selectedDate, format: 'yyyy-MM-dd')
    });

    log(response.body);

    Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
    if (response.statusCode == 200 && data['result'] == 'success') {
      completedTasksList = (data['records'] as List)
          .map((e) => UserCurrentTasks.fromJson(e))
          .toList();
    } else {
      completedTasksList.clear();
    }
    notifyListeners();
  }

  //
//Task type section
  String? _selectedTaskType;
  String? get selectedTaskType => _selectedTaskType;
  set selectedTaskType(String? type) {
    _selectedTaskType = type;
    notifyListeners();
  }

  Map<String, dynamic>? taskTypes;
  Future<void> getTaskType() async {
    EasyLoading.show();
    Uri uri = Uri.parse('$workflowURL/user-timelog-task-type/');
    Response response = await post(uri);
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      taskTypes = data['records'];
      notifyListeners();
    }
    EasyLoading.dismiss();
  }

//
//Project section
  ProjectModel? _selectedProject;
  ProjectModel? get selectedProject => _selectedProject;
  set selectedProject(ProjectModel? value) {
    _selectedProject = value;
    notifyListeners();
  }

  List<ProjectModel> projectList = [];
  Future<void> getProjectTypes() async {
    EasyLoading.show();
    Uri uri = Uri.parse('$workflowURL/user-timelog-task-project/');
    Response response = await post(uri, headers: {
      "client-id": workflowClientID,
      "email": LoginModel.email.toString(),
    });
    // log('getProjectTypes == ${response.statusCode} - ${response.request} - ${response.headers} - ${response.body}');
    log('getProjectTypes ==  ${response.headers} ');
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      projectList = (data['records'] as List)
          .map((e) => ProjectModel.fromJson(e))
          .toList();
    }
    notifyListeners();
    EasyLoading.dismiss();
  }

//
//Tasks section
  ProjectModel? _selectedTask;
  ProjectModel? get selectedTask => _selectedTask;
  set selectedTask(ProjectModel? value) {
    _selectedTask = value;
    notifyListeners();
  }

  List<ProjectModel> taskList = [];
  Future<void> getExistingTasks() async {
    Map<String, dynamic> body = {};
    if (selectedTaskType == '1') {
      body['project_id'] = selectedProject?.id.toString();
    }
    body['task_type'] = selectedTaskType;
    Uri uri = Uri.parse('$workflowURL/user-timelog-task-exist/');
    Response response = await post(uri, body: body);
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      taskList = (data['records'] as List)
          .map((e) => ProjectModel.fromJson(e))
          .toList();
    }
    notifyListeners();
  }

//
//stage section
  ProjectModel? _selectedStage;
  ProjectModel? get selectedStage => _selectedStage;
  set selectedStage(ProjectModel? value) {
    _selectedStage = value;
    notifyListeners();
  }

  List<ProjectModel> stageList = [];
  Future<void> getStages() async {
    Uri uri = Uri.parse('$workflowURL/user-timelog-task-stage/');
    Map<String, dynamic> body = {};
    if (selectedProject != null) {
      body['project_id'] = selectedProject?.id.toString();
    }
    if (selectedTask != null) {
      body['task_id'] = selectedTask?.id.toString();
    }
    Response response = await post(uri, body: body, headers: {
      'client-id': workflowClientID,
      'email': LoginModel.email.toString(),
    });
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      stageList = (data['records'] as List)
          .map((e) => ProjectModel.fromJson(e))
          .toList();
    }
    notifyListeners();
  }

//
//create task section
  bool _isTaskCreating = false;
  bool get isTaskCreating => _isTaskCreating;
  set isTaskCreating(bool value) {
    _isTaskCreating = value;
    notifyListeners();
  }

  Future<void> createTask(
      {required BuildContext context,
      required String description,
      required String task}) async {
    EasyLoading.show();
    isTaskCreating = true;
    Uri uri = Uri.parse('$workflowURL/user-timelog-task-create/');
    Map<String, dynamic> body = {
      'task_type': selectedTaskType,
      'project_id':
          selectedTaskType == '2' ? '0' : selectedProject?.id.toString(),
      'task': selectedTask == null ? task : selectedTask?.title,
      'stage_id': selectedTaskType == '2' ? '0' : selectedStage?.id.toString(),
    };
    if (description.isNotEmpty) {
      body['description'] = description;
    }
    Response response = await post(uri, body: body, headers: {
      'client-id': workflowClientID,
      'email': LoginModel.email.toString(),
    });
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      if (data['result'] == 'success') {
        getUserTimeLogList(master: false);
        Navigator.pop(context);
      } else if (data['result'] == 'failure') {
        if (data.containsKey('errors')) {
          Map<String, dynamic> error = data['errors'];
          if (error.containsKey('project_id')) {
            showSnackBarMessage(context: context, msg: error['project_id']);
          }
        }
      }
    }
    EasyLoading.dismiss();
    isTaskCreating = false;
  }

//
  bool isLoading = false;
  bool _isTimerPaused = false;
  bool get isTimerPaused => _isTimerPaused;
  set isTimerPaused(bool value) {
    _isTimerPaused = value;
    notifyListeners();
  }

  bool isTimerStopped = false;
  // bool get isTimerStopped => _isTimerStopped;
  // set isTimerStopped(bool value) {
  //   _isTimerStopped = value;
  //   notifyListeners();
  // }

  //----------------------------------------------------------------------------------------------------------------------------------------
  List<String> typeOfHoursList = [
    'Today’s Hours',
    'This Week’s Hours',
    'Last Week’s Hours',
    'This Month’s Hours',
    'Last Month’s Hours',
  ];

  String selectedTypeOfHour = 'Today’s Hours';
  String? hourType;
  selectTypeOfHours(
      {required int index,
      required TotalWorkingHoursModel totalWorkingHoursModel}) {
// This fuction is used to change the type of hours //
    selectedTypeOfHour = typeOfHoursList[index];

    switch (selectedTypeOfHour) {
      case 'Today’s Hours':
        hourType = totalWorkingHoursModel.todayHours;
        break;
      case 'This Week’s Hours':
        hourType = totalWorkingHoursModel.past7DaysHours;
        break;
      case 'Last Week’s Hours':
        hourType = totalWorkingHoursModel.lastWeekHours;
        break;
      case 'This Month’s Hours':
        hourType = totalWorkingHoursModel.thisMonthHours;
        break;
      case 'Last Month’s Hours':
        hourType = totalWorkingHoursModel.lastMonthHours;
        break;
      default:
        hourType = totalWorkingHoursModel.todayHours;
    }
    notifyListeners();
  }

  Map<String, int> secondsToHMS(int seconds) {
    int hours = seconds ~/ 3600;
    int remainingSeconds = seconds % 3600;
    int minutes = remainingSeconds ~/ 60;
    int remainingSecondsFinal = remainingSeconds % 60;

    Map<String, int> timeMap = {
      'hours': hours,
      'minutes': minutes,
      'seconds': remainingSecondsFinal,
    };

    return timeMap;
  }

  int timeToSeconds(String time) {
    List<String> parts = time.split(':');
    if (parts.length != 2) {
      throw FormatException("Invalid time format: $time");
    }

    int hours = int.parse(parts[0]);
    int minutes = int.parse(parts[1]);

    if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
      throw FormatException("Invalid time values: $time");
    }

    int totalSeconds = hours * 3600 + minutes * 60;
    return totalSeconds;
  }

  //------------------------------------------------------------APIs----------------------------------------------------------------------------

  TotalWorkingHoursModel? totalWorkingHoursModel;
  Future<bool> getTotalWorkingHours() async {
    // try {
    isLoading = true;
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    Response response;
    String currentTime =
        formatDateFromDate(dateTime: DateTime.now(), format: 'yyyy-MM-dd');
    Map<String, dynamic> body = {};
    body['action'] = 'email';
    body['email'] = LoginModel.email;
    body['date'] = currentTime;
    response = await post(
      Uri.parse("$workflowURL/user-timelog-report-get/"),
      headers: {"Authorization": "Bearer $auth"},
      body: body,
    );
    if (response.statusCode == 200) {
      Map<String, dynamic> json = jsonDecode(response.body);
      EasyLoading.dismiss();
      if (json['result'] == 'success') {
        if (json.containsKey('records')) {
          Map<String, dynamic> records = json['records'];
          totalWorkingHoursModel = TotalWorkingHoursModel.fromJson(records);
          hourType = totalWorkingHoursModel?.todayHours;
          selectedTypeOfHour = typeOfHoursList.first;
        }
      }
      notifyListeners();
      isLoading = false;
      return true;
    } else {}
    EasyLoading.dismiss();
    // } catch (e) {
    //   debugPrint(e.toString());
    //   isLoading = false;
    //   return false;
    // }
    isLoading = false;
    return false;
  }

  formattedTime({required int timeInSecond}) {
    Duration seconds = Duration(seconds: timeInSecond);
    // int sec = timeInSecond % 60;
    // int min = ((timeInSecond % 3600) / 60).floor();
    // int hour = (timeInSecond / 3600).floor();
    // final int min = ((timeInSecond / 3600 - hour) * 60).floor();
    // final int sec = ((((timeInSecond / 3600 - hour) * 60) - min) * 60).floor();
    int hour = seconds.inHours.remainder(24);
    int min = seconds.inMinutes.remainder(60);
    int sec = seconds.inSeconds.remainder(60);
    String hours = hour.toString().padLeft(2, '0');
    String minute = min.toString().padLeft(2, '0');
    String second = sec.toString().padLeft(2, '0');

    return "$hours:$minute:$second";
  }

  Timer? _timer;
  Timer? get timer => _timer;
  set timer(Timer? value) {
    _timer = value;
    notifyListeners();
  }

  Duration? _myDuration;
  Duration? get myDuration => _myDuration;
  set myDuration(Duration? value) {
    _myDuration = value;
    notifyListeners();
  }

  Future<void> startTimer() async {
    _timer = Timer.periodic(const Duration(seconds: 1), (_) => setCountDown());

    notifyListeners();
  }

  Future<void> stopTimer() async {
    _timer?.cancel();
    notifyListeners();
  }

  Future<void> resetTimer() async {
    _hours = 0;
    _minutes = 0;
    _seconds = 0;
    stopTimer();
    _myDuration = const Duration(seconds: 1);
  }

  Future<void> resumeTimer() async {
    _timer = Timer.periodic(const Duration(seconds: 1), (_) => setCountDown());
    notifyListeners();
  }

  Future<void> setCountDown() async {
    int addSecondsBy = 1;
    final seconds = _myDuration!.inSeconds + addSecondsBy;
    _myDuration = Duration(seconds: seconds);
    init();
    notifyListeners();
  }

  void init() {
    _hours = _myDuration!.inHours.remainder(24);
    _minutes = _myDuration!.inMinutes.remainder(60);
    _seconds = _myDuration!.inSeconds.remainder(60);

    notifyListeners();
  }

  int? _hours = 0;
  int? get hours => _hours;

  set hours(int? value) {
    _hours = value;
    notifyListeners();
  }

  int? _minutes = 0;
  int? get minutes => _minutes;

  set minutes(int? value) {
    _minutes = value;
    notifyListeners();
  }

  int? _duration = 0;
  int? get duration => _duration;

  set duration(int? value) {
    _duration = value;
    notifyListeners();
  }

  int? _seconds = 0;
  int? get seconds => _seconds;

  set seconds(int? value) {
    _seconds = value;
    notifyListeners();
  }

  int timeInSecond = 0;
  List<UserCurrentTasks> userCurrentTaskList = [];
  UserCurrentTasks runningTask = UserCurrentTasks();
  Future<void> getUserTimeLogList({
    required bool master,
  }) async {
    if (!master) {
      EasyLoading.show();
    }

    try {
      var response =
          await post(Uri.parse("$workflowURL/user-timelog-listing/"), headers: {
        "client-id": workflowClientID,
        "email": LoginModel.email.toString(),
      }, body: {
        'action': 'active'
      });
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));

      if (response.statusCode == 200 && data["result"] == "success") {
        List records = data['records'];
        if (records.isEmpty) {
          isTimerStopped = true;
        }
        userCurrentTaskList =
            records.map((e) => UserCurrentTasks.fromJson(e)).toList();
        runningTask = userCurrentTaskList.firstWhere(
          (e) => e.status == 1,
          orElse: () => UserCurrentTasks(),
        );
        if (userCurrentTaskList.isNotEmpty) {
          userCurrentTaskList
              .sort(((a, b) => (a.status ?? 0).compareTo(b.status ?? 0)));
        }
        if (runningTask.status == 1) {
          isTimerPaused = true;
          isTimerStopped = false;
        } else if (runningTask.status == 2) {
          isTimerPaused = false;
          isTimerStopped = false;
        } else if (runningTask.status == 3) {
          isTimerStopped = true;
        } else {
          isTimerStopped = false;
        }
        if (runningTask.id != null) {
          _isTimerPaused = false;
          isTimerStopped = false;
          timeInSecond = runningTask.timing ?? 0;
        } else {
          _isTimerPaused = true;
          isTimerStopped = true;
        }

        _myDuration = Duration(seconds: timeInSecond);
        if (runningTask.id != null) {
          _timer?.cancel();
          startTimer();
        }
      }
      EasyLoading.dismiss();
      notifyListeners();
    } catch (e) {
      debugPrint("getUserTimeList Api ---- $e");
    }
  }

  Future<void> timerPlayPauseStope({
    required int taskId,
    required int statusId,
    String? comment,
    int? seconds,
    bool isPopNeeded = true,
    required BuildContext context,
  }) async {
    await EasyLoading.show();
    var response =
        await post(Uri.parse("$workflowURL/user-timelog-status/"), headers: {
      "client-id": workflowClientID,
      "email": LoginModel.email.toString()
    }, body: {
      "status_id": statusId.toString(),
      "task_id": taskId.toString(),
      if (comment != null) "comment": comment
    });
    var data = jsonDecode(utf8.decode(response.bodyBytes));
    if (response.statusCode == 200 && data["result"] == "success") {
      if (statusId == 1) {
        resetTimer();
        isTimerPaused = false;
      } else if (statusId == 2) {
        resetTimer();
        isTimerPaused = true;
        if (isPopNeeded) {
          Navigator.pop(context);
        }
      } else {
        getCompletedTasks();
        resetTimer();
      }
      getUserTimeLogList(master: false);
      getTotalWorkingHours();
    } else {
      showToastText(data["errors"]["task_id"]);
    }

    await EasyLoading.dismiss();
    notifyListeners();
  }

  Future<void> pauseThroughWatch(
      {required int taskId,
      required int statusId,
      required BuildContext context}) async {
    await EasyLoading.show();

    stopTimer();
    if (context.mounted) {
      await timerPlayPauseStope(
        taskId: taskId,
        statusId: statusId,
        comment: 'Stopped by using watch',
        context: context,
        isPopNeeded: false,
      );
    }
    await EasyLoading.dismiss();
  }

  Future<void> userDailyTaskSummery() async {
    try {
      var response = await post(
        userdailytasksummaryURL,
        headers: {
          "client-id": workflowClientID,
          "email": LoginModel.email.toString()
        },
      );

      var data = jsonDecode(utf8.decode(response.bodyBytes));
      log("workflow----- summery = ${data}");
      log("status code = ${response.body}");
      if (response.statusCode == 200 && data["result"] == "success") {}
    } catch (e, stack) {
      debugPrint(e.toString());
      debugPrint(stack.toString());
    }
  }

  Future<void> userAllTaskStop() async {
    try {
      var response = await post(
        usertimelogstopAll,
        headers: {
          "client-id": workflowClientID,
          "email": LoginModel.email.toString()
        },
      );

      log("workflow----- userStopTAskALl {} = ${response.body}");
      var data = jsonDecode(utf8.decode(response.bodyBytes));

      log("status code = ${response.body}");
      if (response.statusCode == 200 && data["result"] == "success") {}
    } catch (e, stack) {
      debugPrint(e.toString());
      debugPrint(stack.toString());
    }
  }

  Future<void> userTimeLogReportSend() async {
    try {
      var now = DateTime.now();
      var formattedDate =
          formatDateFromDate(dateTime: now, format: 'yyyy-MM-dd');
      var emailList =
          [LoginModel.email?.toString()].whereType<String>().toList();
      var body = {
        "action": "email",
        "date": formattedDate,
        "email": emailList,
      };

      var response = await post(usertimelogreportsendURL,
          headers: {
            "Content-Type": "application/json",
            // "client-id": workflowClientID,
          },
          body: jsonEncode(body));

      log("status code = ${response.body}");
      var data = jsonDecode(utf8.decode(response.bodyBytes));
      log("workflow----- userTimeLogReportSend = ${data}");
      if (response.statusCode == 200 && data["result"] == "success") {}
    } catch (e, stack) {
      debugPrint(e.toString());
      debugPrint(stack.toString());
    }
  }
}
