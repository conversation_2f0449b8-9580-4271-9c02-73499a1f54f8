import 'dart:developer';
import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';

import '../main.dart';

class CameraProvider extends ChangeNotifier {
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;
  set isInitialized(bool value) {
    _isInitialized = value;
    notifyListeners();
  }

  int selectedCameraIndex = 0;
  CameraController? controller;

  Future<void> initializeCamera() async {
    if (cameras.isEmpty) return;

    controller = CameraController(
      cameras[selectedCameraIndex],
      ResolutionPreset.low,
      enableAudio: false,
    );

    try {
      await controller!.initialize();
      isInitialized = true;
    } catch (e) {
      debugPrint('Error initializing camera: $e');
    }
  }

  File? image;
  Future<File?> takePicture() async {
    if (controller == null || !controller!.value.isInitialized) return null;
    try {
      final XFile picture = await controller!.takePicture();
      image = File(picture.path);
      notifyListeners();
      log('image size - ${image?.lengthSync()}');
      return image;
    } catch (e) {
      debugPrint('Error taking picture: $e');
      return null;
    }
  }

  Future<void> switchCamera() async {
    if (cameras.length < 2 || controller == null) return;

    isInitialized = false;
    selectedCameraIndex = (selectedCameraIndex + 1) % cameras.length;

    await controller!.dispose();
    controller = CameraController(
      cameras[selectedCameraIndex],
      ResolutionPreset.low,
      enableAudio: false,
    );

    try {
      await initializeCamera();
      isInitialized = true;
    } catch (e) {
      debugPrint('Error switching camera: $e');
    }
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
    controller?.dispose();
  }
}
