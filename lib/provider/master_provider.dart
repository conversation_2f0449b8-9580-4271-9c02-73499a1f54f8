import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:e8_hr_portal/util/fcm.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/logged_in_user.dart';
import 'package:e8_hr_portal/model/splash_model.dart';
import 'package:e8_hr_portal/view/certificate_requests/new_salary_request/new_salary_request.dart';
import 'package:e8_hr_portal/view/certificate_requests/new_salary_request/new_salary_transfer_request.dart';
import 'package:e8_hr_portal/view/certificate_requests/non_objection_certificates/new_noc_request.dart';
import 'package:http/http.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../util/urls.dart';

class MasterProvider extends ChangeNotifier {
  bool isLastReached = false;

  int _currentIndex = 0;

  int get currentIndex => _currentIndex;

  set currentIndex(int value) {
    _currentIndex = value;
    notifyListeners();
  }

  int _teamMembersCount = 0;

  int get teamMembersCount => _teamMembersCount;

  set teamMembersCount(int value) {
    _teamMembersCount = value;
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      notifyListeners();
    });
  }

  int _meetingRoomCount = 0;

  int get meetingRoomCount => _meetingRoomCount;

  set meetingRoomCount(int value) {
    _meetingRoomCount = value;
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      notifyListeners();
    });
  }

  int _meetingCount = 0;

  int get meetingCount => _meetingCount;

  set meetingCount(int value) {
    _meetingCount = value;
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      notifyListeners();
    });
  }

  Future<void> getTeamMembersCount() async {
    QuerySnapshot<Map<String, dynamic>> querySnapshot =
        await FirebaseFirestore.instance.collection('users').get();
    teamMembersCount = querySnapshot.size;
  }

  Future<void> getMeetingRoomsCount() async {
    QuerySnapshot<Map<String, dynamic>> querySnapshot =
        await FirebaseFirestore.instance.collection('meeting_rooms').get();
    meetingRoomCount = querySnapshot.size;
  }

  Future<void> getMeetingBookingCount() async {
    DateTime now = DateTime.now();
    QuerySnapshot<Map<String, dynamic>> querySnapshot =
        await FirebaseFirestore.instance
            .collection('room_booking')
            .where('booking_date',
                isEqualTo: DateTime(
                  now.year,
                  now.month,
                  now.day,
                ))
            .get();
    meetingCount = querySnapshot.size;
  }

  Future<void> getScheduledMeetings() async {
    await FirebaseFirestore.instance
        .collection('room_booking')
        .where('uid', isEqualTo: LoggedInUser.uid)
        .get();
  }

  init() {
    getTeamMembersCount();
    getMeetingRoomsCount();
    getMeetingBookingCount();
  }

  int _getStartedSliderIndex = 0;
  int get getStartedSliderIndex => _getStartedSliderIndex;
  set getStartedSliderIndex(int value) {
    _getStartedSliderIndex = value;
    notifyListeners();
  }

  handleClick(
      {required String value,
      required BuildContext context,
      required String route}) {
    switch (value) {
      case 'Request New':
        if (route == "newsalaryrequest") {
          Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => const NewSalaryRequest()));
        } else if (route == "newsalarytransferrequest") {
          Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => const NewSalaryTransferRequest()));
        } else if (route == "new_experience") {
          Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => const NewSalaryTransferRequest()));
        } else if (route == "noc") {
          Navigator.of(context).push(
              MaterialPageRoute(builder: (context) => const NewNocRequest()));
        }
        break;
      case 'Comment Changes':
        if (route == "experience_comments") {
          // Navigator.of(context).push(MaterialPageRoute(
          //     builder: (context) => const ExperienceCertificateComments())
          // );
          //ExperienceCertificateComments
          //DrivingLicenseComments
        } else if (route == "driving_license_comments") {
          // Navigator.of(context).push(MaterialPageRoute(
          //     builder: (context) => const DrivingLicenseComments()));
        }
        break;
    }
  }

  Set<String> pop = {'Request New', 'Comment Changes'};
  Set<String> flightTicketPop = {'Request New'};

  List<SplashModel> splashList = [];
  Future<void> getSplashScreen() async {
    EasyLoading.show();
    try {
      Response response = await get(Uri.parse("${baseUrl}splash_screen_list/"));
      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);
        splashList =
            (data["data"] as List).map((e) => SplashModel.fromJson(e)).toList();

        notifyListeners();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    EasyLoading.dismiss();
  }

  Future<void> tokenUpdate() async {
    var fcmToken = await FCM.generateToken();

    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.setString("fcmToken", fcmToken.toString());

    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    var body = {};
    body["token"] = fcmToken;
    if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      body['primary'] = iosInfo.identifierForVendor;
      body['manufacturer'] = 'apple';
      body['model'] = iosInfo.model;
      body['platform'] = 'IOS';
    } else {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      body['primary'] = androidInfo.id;
      body['manufacturer'] = androidInfo.manufacturer;
      body['model'] = androidInfo.model;
      body['platform'] = 'Android';
    }
    debugPrint("${preferences.getString('access_token')}");
    var response = await post(tokenUpdateURL, body: body, headers: {
      'Authorization': 'Bearer ${preferences.getString('access_token')}',
    });

    if (response.statusCode == 200) {}
  }
}
