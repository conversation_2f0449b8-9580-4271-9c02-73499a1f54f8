import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/salary_certificate_designation.dart';
import 'package:e8_hr_portal/model/salary_certificate_model.dart';
import 'package:e8_hr_portal/model/salary_certificate_personal_info.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../util/dailoge.dart';

class SalaryCertificateProvider extends ChangeNotifier {
  final RefreshController refreshController =
      RefreshController(initialRefresh: false);

  void onRefresh() async {
    await getSalaryCertificate();
    refreshController.refreshCompleted();
  }

  final List<SalaryCertificateDesignation> designationList = [
    SalaryCertificateDesignation("HR Admin", 1),
  ];
  String? _selectedDesignation;
  String? get selectedDesignation => _selectedDesignation;
  set selectedDesignation(String? value) {
    _selectedDesignation = value;
    notifyListeners();
  }

  final List<String> monthList = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  ];
  String? _selectedMonth;
  String? get selectedMonth => _selectedMonth;
  set selectedMonth(String? value) {
    _selectedMonth = value;
    notifyListeners();
  }

  List<String>? _availableMonths = [];
  List<String>? get availableMonths => _availableMonths;
  set availableMonths(List<String>? value) {
    _availableMonths = value;
    notifyListeners();
  }

// Function to update available months based on selected year
  void updateAvailableMonths() {
    if (selectedYear == DateTime.now().year &&
        DateTime.now().month != DateTime.december) {
      if (selectedYear == DateTime.now().year) {
        // If selected year is the current year, only show months that have already passed
        int currentMonth = DateTime.now().month;
        List<String> availableMonths = monthList.sublist(0, currentMonth);
        selectedMonth = availableMonths.last;
        // Update the month dropdown with available months
        // ...
      }
      // If selected year is the current year and the current month is not December,
      // show only the current and previous months
      int currentMonth = DateTime.now().month;
      availableMonths = monthList.sublist(0, currentMonth);
    } else {
      // If selected year is not the current year, or the current month is December,
      // show all twelve months
      availableMonths = List.from(monthList);
    }
    // Update the month dropdown with available months
    // ...
  }

  // Event listener for the year dropdown
  void onYearSelected(int year) {
    selectedYear = year;
    updateAvailableMonths();
    notifyListeners();
  }

// Event listener for the month dropdown
  void onMonthSelected(String month) {
    selectedMonth = month;
    notifyListeners();
  }

  final List<SalaryCertificateMonth> yourDesignationList = [
    SalaryCertificateMonth("Employee", 1),
  ];
  String? _selectedyourDesignation;
  String? get selectedyourDesignation => _selectedyourDesignation;
  set selectedyourDesignation(String? value) {
    _selectedyourDesignation = value;
    notifyListeners();
  }

  List<int>? _yearList;
  List<int>? get yearList => _yearList;
  set yearList(List<int>? value) {
    _yearList = value;
    notifyListeners();
  }

  int? _selectedYear;
  int? get selectedYear => _selectedYear;
  set selectedYear(int? value) {
    _selectedYear = value;
    notifyListeners();
  }

  void getYear() {
    _yearList = null;
    int currentYear = DateTime.now().year;
    int startingYear = (DateTime.now().year) - 3;
    _yearList = List.generate(
        (currentYear - startingYear) + 1, (index) => startingYear + index);
  }

  SalaryCertificatePersonalInfo? _salaryCertificatePersonalInfo;
  SalaryCertificatePersonalInfo? get salaryCertificatePersonalInfo =>
      _salaryCertificatePersonalInfo;
  Future<bool> getFlightTicketPersonalInfo() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    try {
      var response = await http.get(salaryCertificatePersonalInfoURL,
          headers: {"Authorization": "Bearer $auth"});

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json["result"] == "success") {
          if (json.containsKey("records")) {
            _salaryCertificatePersonalInfo =
                SalaryCertificatePersonalInfo.fromJson(json["records"]);
            return true;
          }
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return false;
  }

  Future<bool> salaryCertificateCreate({
    required BuildContext context,
    required String monthAndYear,
    required String to,
  }) async {
    final navigator = Navigator.of(context);
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");

    var body = {"to": to, "month": monthAndYear};
    var response = await http.post(salaryCertificateCreateURL,
        body: body, headers: {"Authorization": "Bearer $auth"});

    if (response.statusCode == 200) {
      Map<String?, dynamic> json = jsonDecode(response.body);

      if (json["result"] == "success") {
        if (json.containsKey("message")) {
          showToastText(json["message"]);
          navigator.pop();
          navigator.pop();
        }
        return true;
      }
    }
    if (response.statusCode == 400) {
      Map<String?, dynamic> json = jsonDecode(response.body);
      if (json["result"] == "failure") {
        if (json.containsKey("erros")) {
          Map<String, dynamic> erros = json["erros"];
          if (erros.containsKey("request")) {
            showToastText(erros["request"]);
          }
          if (erros.containsKey("profile")) {
            showToastText(erros["profile"]);
          }
        }
        notifyListeners();
        return false;
      }
    }
    notifyListeners();

    return false;
  }

  List<SalaryCertificateModel> _salaryList = [];
  List<SalaryCertificateModel> get salaryList => _salaryList;
  set salaryList(List<SalaryCertificateModel> values) {
    _salaryList = values;
    notifyListeners();
  }

  bool _isCertificatesLoading = false;
  bool get isCertificatesLoading => _isCertificatesLoading;
  set isCertificatesLoading(bool value) {
    _isCertificatesLoading = value;
    notifyListeners();
  }

  Future<bool> getSalaryCertificate() async {
    if (salaryList.isEmpty) {
      EasyLoading.show();
    }
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    // isCertificatesLoading = true;
    try {
      http.Response response = await http.get(
          Uri.parse("${baseUrl}certificates-list/?action=salary_certifcates"),
          headers: {"Authorization": "Bearer $auth"});

      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);
        salaryList = (data["records"] as List)
            .map((e) => SalaryCertificateModel.fromJson(e))
            .toList();
        EasyLoading.dismiss();
        return true;
      }

      // isCertificatesLoading = false;
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();

      // isCertificatesLoading = false;
      debugPrint(e.toString());
    }
    return false;
  }

  String? salaryPdf;
  Future<void> getSalaryCertificatePdf({required String certificateId}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    http.Response response = await http.get(
        Uri.parse(
            "${baseUrl}certificates-view/?action=salary_certifcates&action_id=$certificateId"),
        headers: {"Authorization": "Bearer $auth"});

    salaryPdf = null;
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      salaryPdf = data["records"]["file"];
      notifyListeners();
    }
  }

  Future<bool> salaryCertificateCommentCreate({
    required BuildContext context,
    required String action,
    required int actionId,
    required String comment,
  }) async {
    final navigator = Navigator.of(context);
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");

    Map<String, String>? body = {
      "action": action,
      "action_id": actionId.toString(),
      "comment": comment,
    };
    var response = await http.post(commentsCreateURL,
        body: body, headers: {"Authorization": "Bearer $auth"});

    if (response.statusCode == 200) {
      Map<String?, dynamic> json = jsonDecode(response.body);

      if (json["result"] == "success") {
        if (json.containsKey("message")) {
          showToastText(json["message"]);
          navigator.pop();
          navigator.pop();
        }
        return true;
      }
    }
    if (response.statusCode == 400) {
      Map<String?, dynamic> json = jsonDecode(response.body);
      if (json["result"] == "failure") {
        if (json.containsKey("erros")) {
          Map<String, dynamic> erros = json["erros"];
          if (erros.containsKey("request")) {
            showToastText(erros["request"]);
          }
        }
        return false;
      }
    }
    return false;
  }
}
