// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class RequestChatProvider extends ChangeNotifier {
  Future<void> submitRequest({
    required String subject,
    required String description,
    required String createdAt,
    required BuildContext context,
  }) async {
    var body = {
      "subject": subject,
      "description": description,
      "created_at": createdAt
    };
    SharedPreferences shared = await SharedPreferences.getInstance();
    var response = await http.post(chatRequestAdminURL,
        headers: {
          'Authorization': 'Bearer ${shared.getString('access_token')}',
        },
        body: body);

    if (response.statusCode == 200) {
      var data = jsonDecode(response.body);
      await showToastText(data["message"]);
      Navigator.pop(context);
      Navigator.pop(context);
    }
  }
}
