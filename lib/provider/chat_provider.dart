import 'dart:convert';
import 'package:e8_hr_portal/model/chat_model.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ChatProvider extends ChangeNotifier {
  List<ChatModel> chatList = [];
  List<String> sendChatBotMsg = [];
  cleatChat() {
    chatList.clear();
    sendChatBotMsg.clear();
  }

  bool _isLoading = false;
  bool get isLoading => _isLoading;
  set isLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  ChatModel loadingModel = ChatModel(answer: 'Loading');

  Future<void> chatbotMsg({required String message}) async {
    try {
      isLoading = true;
      sendChatBotMsg.add(message);
      if (chatList.isNotEmpty) {
        chatList.insert(chatList.length, loadingModel);

        sendChatBotMsg.insert(sendChatBotMsg.length, "Loading");
      }
      SharedPreferences shared = await SharedPreferences.getInstance();
      var response =
          await get(Uri.parse("$chatBotURL?questions=$message"), headers: {
        'Authorization': 'Bearer ${shared.getString('access_token')}',
      });
      debugPrint("chatbotMSG = ${response.request}");
      debugPrint("chatbotMSG = ${response.body}");
      if (response.statusCode == 200) {
        // var data = jsonDecode(response.body);
        var data = jsonDecode(utf8.decode(response.bodyBytes));
        chatList.remove(loadingModel);
        sendChatBotMsg.remove("Loading");
        chatList.add(ChatModel.fromJson(data));
      }

      isLoading = false;
      // await EasyLoading.dismiss();
      notifyListeners();
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
