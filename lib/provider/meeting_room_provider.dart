// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:e8_hr_portal/model/amenity_model.dart';
import 'package:e8_hr_portal/model/meeting_room_details_model.dart';
import 'package:e8_hr_portal/model/meeting_room_models/meeting_room_model.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:http/http.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/user_policies_model.dart';
import '../util/urls.dart';

class MeetingRoomProvider with ChangeNotifier {
  //check if admin or not
  // bool isAdmin = false;
  // isAdminCheck() async {
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   isAdmin = prefs.getBool('is_admin') ?? false;
  // }

  //
  //image for creating meeting room
  List<File> selectedMeetingRoomImages = [];
  final imagePicker = ImagePicker();
  Future<void> pickImageFromCamera({required BuildContext context}) async {
    XFile? pickedImage = await imagePicker.pickImage(
      source: ImageSource.camera,
      imageQuality: 75,
    );
    Navigator.of(context).pop();
    if (pickedImage != null) {
      File file = File(pickedImage.path);
      int sizeInBytes = file.lengthSync();
      double sizeInMb = sizeInBytes / (1024 * 1024);
      if (sizeInMb > 4) {
        showSnackBarMessage(
            context: context, msg: 'Please select image less than 4 MB');
      } else {
        selectedMeetingRoomImages.add(File(pickedImage.path));
        notifyListeners();
      }
    }
  }

  Future<void> pickImageFromGallery({required BuildContext context}) async {
    List<XFile> pickedImagesList =
        await imagePicker.pickMultiImage(imageQuality: 75);
    Navigator.of(context).pop();
    if (pickedImagesList.isNotEmpty) {
      selectedMeetingRoomImages
          .addAll(pickedImagesList.map((e) => File(e.path)).toList());
      notifyListeners();
    }
  }

  Future<void> removeImage(int index) async {
    selectedMeetingRoomImages.removeAt(index);
    notifyListeners();
  }

  //
  //amenity section
  AmenityModel? _selectedAmenity;
  AmenityModel? get selectedAmenity => _selectedAmenity;
  set selectedAmenity(AmenityModel? amenity) {
    _selectedAmenity = amenity;
    notifyListeners();
  }

  List<AmenityModel> selectedAmenitiesList = [];
  addAmenity(AmenityModel amenity) {
    selectedAmenitiesList.add(amenity);
    notifyListeners();
  }

  removeAmenity(int index) {
    selectedAmenitiesList.removeAt(index);
    if (selectedAmenitiesList.isEmpty) {
      selectedAmenity = null;
    }
    notifyListeners();
  }

  List<AmenityModel> amenitiesList = [];
  Future<void> getAmenities() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response response =
        await get(amenitiesListURL, headers: {'Authorization': 'Bearer $auth'});
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      amenitiesList =
          (data['data'] as List).map((e) => AmenityModel.fromJson(e)).toList();
      notifyListeners();
    }
  }

  //
  //policy section
  UserPoliciesModel? _selectedUserPolicy;
  UserPoliciesModel? get selectedUserPolicy => _selectedUserPolicy;
  set selectedUserPolicy(UserPoliciesModel? policy) {
    _selectedUserPolicy = policy;
    notifyListeners();
  }

  List<UserPoliciesModel> policiesList = [];
  Future<void> getPolicies() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response response =
        await get(userPoliciesURL, headers: {'Authorization': 'Bearer $auth'});
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      policiesList = (data['data'] as List)
          .map((e) => UserPoliciesModel.fromJson(e))
          .toList();
      notifyListeners();
    }
  }

  //create meeting room
  bool _creatingMeetingRoom = false;
  bool get creatingMeetingRoom => _creatingMeetingRoom;
  set creatingMeetingRoom(bool value) {
    _creatingMeetingRoom = value;
    notifyListeners();
  }

  Future<void> createMeetingRoom(
      {required String roomName,
      required String seatCount,
      required BuildContext context}) async {
    creatingMeetingRoom = true;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    MultipartRequest request = MultipartRequest('POST', createMeetingRoomURL);
    request.headers['Authorization'] = 'Bearer $auth';
    request.fields['name'] = roomName;
    request.fields['seat_count'] = seatCount;
    request.fields['policy'] = selectedUserPolicy!.id.toString();
    request.fields['amenities'] =
        selectedAmenitiesList.map((e) => e.id).toList().join(',');
    for (var image in selectedMeetingRoomImages) {
      var multipartFile = await getMultipartFile(image.path);
      request.files.add(multipartFile);
    }
    await request.send().then((value) async {
      var responseData = await value.stream.toBytes();
      var responseString = String.fromCharCodes(responseData);
      if (value.statusCode == 200) {
        Map<String, dynamic> response = jsonDecode(responseString);
        if (response['result'] == 'success') {
          showSnackBarMessage(
              context: context, msg: response['message'].toString());
          getMeetingRooms();
          Navigator.pop(context);
        } else if (response['result'] == 'failure') {
          Map<String, dynamic> errors = response['errors'];
          if (errors.containsKey('non_field_errors')) {
            showSnackBarMessage(
                context: context, msg: errors['non_field_errors'].toString());
          } else if (errors.containsKey('images')) {
            showSnackBarMessage(
                context: context,
                msg: (errors['images'] as List).first.toString());
          } else if (errors.containsKey('error')) {
            showSnackBarMessage(
                context: context, msg: errors['error'].toString());
          } else if (errors.containsKey('image_size')) {
            showSnackBarMessage(
                context: context, msg: errors['image_size'].toString());
          }
        }
      } else {
        Map<String, dynamic> response = jsonDecode(responseString);
        if (response.containsKey('message')) {
          showSnackBarMessage(
              context: context, msg: (response['message'].toString()));
        } else {
          showSnackBarMessage(context: context, msg: 'Something went wrong');
        }
      }
    });
    creatingMeetingRoom = false;
  }

  Future<MultipartFile> getMultipartFile(String path) async {
    return await MultipartFile.fromPath('images', path);
  }

  //
  // meeting room list section
  bool _isMeetingRoomLoading = false;
  bool get isMeetingRoomLoading => _isMeetingRoomLoading;
  set isMeetingRoomLoading(bool value) {
    _isMeetingRoomLoading = value;
    notifyListeners();
  }

  List<MeetingRoomModel> meetingRoomsList = [];
  Future<void> getMeetingRooms() async {
    isMeetingRoomLoading = true;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response response = await get(meetingRoomListURL,
        headers: {'Authorization': 'Bearer $auth'});
    isMeetingRoomLoading = false;
    meetingRoomsList.clear();
    log('getMeetingRooms --> ${response.body}');
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      meetingRoomsList = (data['data'] as List)
          .map((e) => MeetingRoomModel.fromJson(e))
          .toList();
    } else {}

    notifyListeners();
    isMeetingRoomLoading = false;
  }

  //
  //delete meeting room section
  Future<void> deleteMeetingRoom(
      {required BuildContext context, required String roomID}) async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response response = await post(deleteMeetingRoomURL,
        headers: {'Authorization': 'Bearer $auth'},
        body: {'meeting_room_id': roomID});
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      showSnackBarMessage(context: context, msg: data['message']);
      getMeetingRooms();
    } else if (response.statusCode == 400) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      if (data.containsKey('error')) {
        Map<String, dynamic> error = data['error'];
        if (error.containsKey('room_id')) {
          showSnackBarMessage(
              context: context, msg: error['room_id'].toString());
        }
      }
    } else {
      showSnackBarMessage(context: context, msg: 'Something went wrong');
    }
    EasyLoading.dismiss();
  }
  //

  //edit meeting room section
  List<RoomImgs> _alreadySelectedImagesList = [];
  List<int> removedImagesIdList = [];
  List<RoomImgs> get alreadySelectedImagesList => _alreadySelectedImagesList;
  set alreadySelectedImagesList(List<RoomImgs> imageList) {
    _alreadySelectedImagesList = imageList;
    notifyListeners();
  }

  Future<void> removeAlreadySelectedImage(int index) async {
    removedImagesIdList.add(alreadySelectedImagesList[index].id!);
    alreadySelectedImagesList.removeAt(index);
    notifyListeners();
  }

  Future<void> editMeetingRoom(
      {required String meetingRoomId,
      required String roomName,
      required String seatCount,
      required BuildContext context}) async {
    creatingMeetingRoom = true;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    MultipartRequest request = MultipartRequest(
        'POST', Uri.parse('$editMeetingRoomURL$meetingRoomId'));
    request.headers['Authorization'] = 'Bearer $auth';
    request.fields['name'] = roomName;
    request.fields['seat_count'] = seatCount;
    request.fields['policy'] = selectedUserPolicy!.id.toString();
    if (removedImagesIdList.isNotEmpty) {
      request.fields['deleted_img'] = removedImagesIdList.join(',');
    }
    request.fields['amenities'] =
        selectedAmenitiesList.map((e) => e.id).toList().join(',');
    for (var image in selectedMeetingRoomImages) {
      var multipartFile = await getMultipartFile(image.path);
      request.files.add(multipartFile);
    }
    await request.send().then((value) async {
      var responseData = await value.stream.toBytes();
      var responseString = String.fromCharCodes(responseData);
      if (value.statusCode == 200) {
        Map<String, dynamic> response = jsonDecode(responseString);
        showSnackBarMessage(context: context, msg: response['message']);
        getMeetingRooms();
        Navigator.pop(context);
      } else if (value.statusCode == 400) {
        // showSnackBarMessage(context: context, msg: 'Something went wrong');
        Map<String, dynamic> response = jsonDecode(responseString);
        if (response.containsKey('error')) {
          Map<String, dynamic> error = response['error'];
          if (error.containsKey('image_size')) {
            showSnackBarMessage(context: context, msg: error['image_size']);
          } else if (error.containsKey('images')) {
            showSnackBarMessage(context: context, msg: error['images']);
          } else if (error.containsKey('error')) {
            showSnackBarMessage(
                context: context, msg: error['error'].toString());
          } else if (error.containsKey('non_field_errors')) {
            showSnackBarMessage(
                context: context, msg: error['non_field_errors'].toString());
          }
        }
      }
    });
    creatingMeetingRoom = false;
  }
// else if (response['result'] == 'failure') {
//           Map<String, dynamic> errors = response['errors'];
//           if (errors.containsKey('non_field_errors')) {
//             showSnackBarMessage(
//                 context: context, msg: errors['non_field_errors'].toString());
//           } else if (errors.containsKey('images')) {
//             showSnackBarMessage(
//                 context: context,
//                 msg: (errors['images'] as List).first.toString());
//           } else if (errors.containsKey('error')) {
//             showSnackBarMessage(
//                 context: context, msg: errors['error'].toString());
//           } else if (errors.containsKey('image_size')) {
//             showSnackBarMessage(
//                 context: context, msg: errors['image_size'].toString());
//           }
//         }

  //
  //meeting room details section
  bool _isMeetingRoomDetailsLoading = false;
  bool get isMeetingRoomDetailsLoading => _isMeetingRoomDetailsLoading;
  set isMeetingRoomDetailsLoading(bool value) {
    _isMeetingRoomDetailsLoading = value;
    notifyListeners();
  }

  MeetingRoomDetailsModel? meetingRoomDetails;
  Future<void> getMeetingRoomDetails({required String meetingRoomID}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    isMeetingRoomDetailsLoading = true;
    Response? response = await get(
      Uri.parse('$meetingRoomDetailsURL$meetingRoomID'),
      headers: {'Authorization': 'Bearer $auth'},
    );
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      meetingRoomDetails = MeetingRoomDetailsModel.fromJson(data['data']);
    }
    isMeetingRoomDetailsLoading = false;
    notifyListeners();
  }
  //
}
