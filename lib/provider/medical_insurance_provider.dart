import 'dart:convert';
import 'dart:io';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/download_url_model.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../model/medical_insurance_model.dart';
import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

class MedicalInsuranceProvider extends ChangeNotifier {
  int _pages = 0;
  int get pages => _pages;
  set pages(int value) {
    _pages = value;
    notifyListeners();
  }

  int _currentPage = 0;
  int get currentPage => _currentPage;
  set currentPage(int value) {
    _currentPage = value;
    notifyListeners();
  }

  bool _isReady = false;
  bool get isReady => _isReady;
  set isReady(bool value) {
    _isReady = value;
    notifyListeners();
  }

  String _errorMessage = "";
  String get errorMessage => _errorMessage;
  set errorMessage(String value) {
    _errorMessage = value;
    notifyListeners();
  }

  MedicalInsuranceCardModel? medicalInsuranceCardModel;
  Future<void> medicalInsurance() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");

    var response = await http.get(medicalInsuranceCardURL,
        headers: {"Authorization": "Bearer $auth"});

    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      medicalInsuranceCardModel = MedicalInsuranceCardModel.fromJson(data);
    }
    notifyListeners();
  }

  DownloadUrlModel? downloadUrlModel;
  Future<void> medicalInsuranceDownload() async {
    SharedPreferences shared = await SharedPreferences.getInstance();

    String? auth = shared.getString("access_token");

    var response = await http.get(medicalInsuranceDownloadURL,
        headers: {"Authorization": "Bearer $auth"});

    if (response.statusCode == 200) {
      var data = jsonDecode(response.body);
      if (data["result"] == "success") {
        downloadUrlModel = DownloadUrlModel.fromJson(data);
      }
    }
    notifyListeners();
  }

  Future<File> createFileOfPdfUrl(String? pdf) async {
    Completer<File> completer = Completer();

    await EasyLoading.show();
    try {
      final String url = pdf!;
      final filename = url.substring(url.lastIndexOf("/") + 1);
      var request = await HttpClient().getUrl(Uri.parse(url));
      var response = await request.close();
      var bytes = await consolidateHttpClientResponseBytes(response);
      var dir = await getApplicationDocumentsDirectory();
      File file = File("${dir.path}/$filename");
      await file.writeAsBytes(bytes, flush: true);
      completer.complete(file);
    } catch (e) {
      throw Exception('Error parsing asset file!');
    }
    await EasyLoading.dismiss();

    return completer.future;
  }
}
