import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/user_detailes_model.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:e8_hr_portal/services/api_service.dart';

import '../model/emp_action_status_model.dart';

class UserStatusProvider extends ChangeNotifier {
  EmpStatus? _selectedUserStatus;
  EmpStatus? get selectedUserStatus => _selectedUserStatus;

  set selectedUserStatus(EmpStatus? value) {
    _selectedUserStatus = value;
    notifyListeners();
  }

  bool? _isLoading = false;
  bool? get isLoading => _isLoading;
  set isLoading(bool? value) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _isLoading = value;
      notifyListeners();
    });
  }

// At the time of taping on the status tile , need to reset the sub status and set appropriate sub status
  onShowActions({
    required EmpActionStatusModel item,
    required List<EmpActionStatusModel> items,
    required String selectedEmpSubSction,
    required String selectedEmpStatusName,
  }) {
    bool isShowActions = item.isShowActions ?? false;
    items.map((e) {
      e.isShowActions = false;
      List<SubAction> subAction = e.subAction ?? [];

      if (subAction.isNotEmpty) {
        subAction.map((v) => v.isActive = false).toList();
        SubAction? selectedSubAction = subAction.firstWhere(
            (element) =>
                element.value?.toLowerCase() ==
                selectedEmpSubSction.toLowerCase(),
            orElse: () => SubAction());
        if (selectedSubAction.value != null) {
          if (selectedEmpStatusName.toLowerCase() == item.name?.toLowerCase()) {
            selectedSubAction.isActive = true;
          }
        } else {
          selectedSubAction.isActive = false;
        }
      }
    }).toList();
    item.isShowActions = !isShowActions;
    notifyListeners();
  }

  onSubActionChanged(
      {required List<SubAction> items, required SubAction item}) {
    items.map((e) => e.isActive = false).toList();
    item.isActive = true;
    notifyListeners();
  }

  List<EmpActionStatusModel> userStatus = [];
  Future<void> getUserStatus({required bool master}) async {
    if (!master) {
      if (userStatus.isEmpty) {
        EasyLoading.show();
      }
    }

    isLoading = true;

    try {
      // SECURITY FIX: Use secure ApiService instead of direct HTTP calls
      var response = await ApiService.get(employeeStatusURL);
      log('getUserStatus -- ${response.body}');

      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);
        if (data["result"] == "success") {
          userStatus = (data['data'] as List)
              .map((e) => EmpActionStatusModel.fromJson(e))
              .toList();
        }
        userStatus.map((e) => e.isShowActions = false).toList();
      }
    } catch (e) {
      log('getUserStatus -- Error: $e');
      // Clear userStatus on error
      userStatus.clear();
    }

    isLoading = false;
    notifyListeners();
    EasyLoading.dismiss();
  }

  Future<void> editUserStatus({
    required String? id,
    String? subAction,
  }) async {
    isLoading = true;
    EasyLoading.show();

    try {
      Map<String, dynamic> body = {};
      body['user_status'] = id;
      if (subAction != null) {
        body['sub_action'] = subAction.toLowerCase();
      }

      // SECURITY FIX: Use secure ApiService instead of direct HTTP calls
      var response = await ApiService.put(
        employeeStatusUpdateURL,
        body: jsonEncode(body),
      );

      Map<String, dynamic> data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        if (data["result"] == "success") {
          log('editUserStatus -- Status updated successfully');
        }
      }
    } catch (e) {
      log('editUserStatus -- Error: $e');
    }

    isLoading = false;
    EasyLoading.dismiss();
    notifyListeners();
  }

  clear() {
    selectedUserStatus = null;
  }
}
