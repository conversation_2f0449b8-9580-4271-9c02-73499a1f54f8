import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/driving_license_list_model.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

import '../util/dailoge.dart';

class DrivingLicenseProvider extends ChangeNotifier {
  final RefreshController refreshController =
      RefreshController(initialRefresh: false);

  void onRefresh() async {
    await getDrivingLicenseList();
    refreshController.refreshCompleted();
  }

  String? _errorMsg;
  String? get errorMsg => _errorMsg;
  List<DrivingLicenseListModel> _drivingLicenseList = [];
  List<DrivingLicenseListModel> get drivingLicenseList => _drivingLicenseList;
  set drivingLicenseList(List<DrivingLicenseListModel> value) {
    _drivingLicenseList = value;
    notifyListeners();
  }

  Future<bool> getDrivingLicenseList() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    try {
      EasyLoading.show();
      var response = await http.get(drivingLicenseListURL,
          headers: {"Authorization": "Bearer $auth"});

      if (response.statusCode == 200) {
        EasyLoading.dismiss();
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json["result"] == "success") {
          if (json.containsKey("records")) {
            List records = json["records"];
            if (records.isEmpty) {
              showToastText("No Data Found");
              return false;
            }
            drivingLicenseList = records
                .map((e) => DrivingLicenseListModel.fromJson(e))
                .toList();
            notifyListeners();
            return true;
          }
        }
      }
      notifyListeners();
      EasyLoading.dismiss();
    } catch (e) {
      debugPrint(e.toString());
      EasyLoading.dismiss();
    }
    return false;
  }

  Future<bool> submitRequest(
      {String? to, String? bankName, String? branchName}) async {
    try {
      _errorMsg = null;
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");
      Uri uri = Uri.parse('${baseUrl}driving-licnese-permission-create/');
      var response = await http.post(
        uri,
        headers: {"Authorization": "Bearer $auth"},
      );
      debugPrint(response.request.toString());
      debugPrint(response.statusCode.toString());

      if (response.statusCode == 200) {
        var json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          await getDrivingLicenseList();
          return true;
        }
      } else if (response.statusCode == 400) {
        var json = jsonDecode(response.body);
        if (json['result'] == 'failure') {
          Map<String, dynamic> errors = json['erros'];
          if (errors.containsKey('request')) {
            _errorMsg = errors['request'];
          } else if (errors.containsKey("profile")) {
            _errorMsg = errors['profile'];
          }
        }
      }
      return false;
    } catch (e) {
      debugPrint(e.toString());
      return false;
    }
  }

  String? _drivingLicensePDFUrl;
  String? get drivingLicensePDFUrl => _drivingLicensePDFUrl;
  Future<bool> getDrivingLicensePDf(
      {required String action, required int actionId}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    try {
      Uri nocCertificateURL = Uri.parse(
          "${baseUrl}certificates-view/?action=$action&action_id=$actionId");
      var response = await http
          .get(nocCertificateURL, headers: {"Authorization": "Bearer $auth"});
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json["result"] == "success") {
          if (json.containsKey("records")) {
            Map<String, dynamic> records = json["records"];
            if (records.containsKey("file")) {
              _drivingLicensePDFUrl = null;
              _drivingLicensePDFUrl = records["file"];
            }
          }
          return true;
        }
      }
      if (response.statusCode == 400) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json["result"] == "failure") {
          _drivingLicensePDFUrl = null;
          if (json.containsKey("errors")) {
            showToastText(json["errors"]);
          }
          return false;
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return false;
  }

  Future<bool> drivingLicenseCommentCreate({
    required BuildContext context,
    required String action,
    required int actionId,
    required String comment,
  }) async {
    final navigator = Navigator.of(context);
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");

    Map<String, String>? body = {
      "action": action,
      "action_id": actionId.toString(),
      "comment": comment,
    };
    var response = await http.post(commentsCreateURL,
        body: body, headers: {"Authorization": "Bearer $auth"});

    if (response.statusCode == 200) {
      Map<String?, dynamic> json = jsonDecode(response.body);

      if (json["result"] == "success") {
        if (json.containsKey("message")) {
          showToastText(json["message"]);
          navigator.pop();
          navigator.pop();
        }
        return true;
      }
    }
    if (response.statusCode == 400) {
      Map<String?, dynamic> json = jsonDecode(response.body);
      if (json["result"] == "failure") {
        if (json.containsKey("erros")) {
          Map<String, dynamic> erros = json["erros"];
          if (erros.containsKey("request")) {
            showToastText(erros["request"]);
          }
        }
        return false;
      }
    }
    return false;
  }

  // double _progress = 0.0;
  // double get progress => _progress;
  // set progress(double value) {
  //   _progress = value;
  //   notifyListeners();
  // }

  // void startDownloading(
  //     {required String url, required BuildContext context}) async {
  //   // String url = widget.url;
  //   Dio dio = Dio();
  //   const String fileName = "drivingLicense.pdf";
  //   String path = await _getFilePath("hisense/$fileName");
  //   debugPrint("path ----------------------- $path");
  //   await dio.download(
  //     url,
  //     path,
  //     onReceiveProgress: (recivedBytes, totalBytes) {
  //       progress = recivedBytes / totalBytes;
  //     },
  //     deleteOnError: true,
  //   ).then((_) {
  //     Navigator.pop(context);
  //     final scaffoldMessenger = ScaffoldMessenger.of(context);
  //     Widget text = Row(
  //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //       children: [
  //         const Text('Downloaded successfully.'),
  //         OutlinedButton(
  //           onPressed: () async {
  //             await viewFile();
  //           },
  //           style: ButtonStyle(
  //             side: MaterialStateProperty.all(
  //               const BorderSide(width: 1, color: Colors.white),
  //             ),
  //           ),
  //           child: const Text("View"),
  //         ),
  //       ],
  //     );
  //     SnackBar snackBar = SnackBar(content: text);
  //     scaffoldMessenger.showSnackBar(snackBar);
  //   });
  // }

  // Future<String> _getFilePath(String filename) async {
  //   final dir = await getApplicationDocumentsDirectory();

  //   return "${dir.path}/$filename";
  // }

  // Future<void> viewFile() async {
  //   bool dirDownloadExists = true;
  //   var directory;
  //   if (Platform.isIOS) {
  //     directory = await getDownloadsDirectory();

  //   } else {
  //     directory = "/storage/emulated/0/Download/";

  //     dirDownloadExists = await Directory(directory).exists();
  //     if (dirDownloadExists) {
  //       directory = "/storage/emulated/0/Download/";
  //     } else {
  //       directory = "/storage/emulated/0/Downloads/";
  //     }
  //   }
  // }

//   String _message = '';
//   Future<void> downloadPdf(
//       {required String url, required BuildContext context}) async {
//     _message = 'Downloading PDF...';
//     final scaffoldMessenger = ScaffoldMessenger.of(context);
//     final directory = await getExternalStorageDirectory();
//     final savedDir = '${directory!.path}/Download';

//     final taskId = await FlutterDownloader.enqueue(
//       url: url,
//       savedDir: savedDir,
//       fileName: 'DrivingLicense.pdf',
//       showNotification: true,
//       openFileFromNotification: true,
//       saveInPublicStorage: true,
//       allowCellular: true,
//       requiresStorageNotLow: true,
//     );
//     EasyLoading.dismiss();

//     await FlutterDownloader.registerCallback((id, status, progress) async {
//       if (status == DownloadTaskStatus.complete) {
//         _message = 'PDF downloaded successfully!';
//         Text text = Text(_message);
//         SnackBar snackBar = SnackBar(content: text);
//         scaffoldMessenger.showSnackBar(snackBar);
//         if (taskId != null) {
//           await FlutterDownloader.open(taskId: taskId);
//         }
//       } else if (status == DownloadTaskStatus.failed) {
//         _message = 'PDF download failed. Task ID: $taskId';
//         Text text = Text(_message);
//         SnackBar snackBar = SnackBar(content: text);
//         scaffoldMessenger.showSnackBar(snackBar);
//       }
//     });
//     notifyListeners();
//   }

//   void unbindBackgroundIsolate() {
//     IsolateNameServer.removePortNameMapping('downloader_send_port');
//   }
// }
}


//https://hisense-hrportal.e8demo.com/media/driving_license_permission_certificates/drivinglicense_permission_certificate-33.pdf