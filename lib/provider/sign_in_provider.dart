// ignore_for_file: use_build_context_synchronously
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/view/sign_in/login_with_email_screen.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/model/siginin_model.dart';
import 'package:e8_hr_portal/util/fcm.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:e8_hr_portal/view/sign_in/otp_screen.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:http/http.dart' as http;
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/view/master/master_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:encrypt/encrypt.dart' as aes;
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:e8_hr_portal/services/api_service.dart';
import 'package:e8_hr_portal/services/secure_key_manager.dart';
import 'package:e8_hr_portal/services/forensic_logger.dart';

class SignInProvider extends ChangeNotifier {
  bool _isLoading = false;
  bool _isLoadingGoogle = false;
  bool _hidePassword = true;
  String? _otpErrorMsg;

  String? get otpErrorMsg => _otpErrorMsg;
  bool get isLoading => _isLoading;
  bool get isLoadingGoogle => _isLoadingGoogle;
  bool get hidePassword => _hidePassword;

  set isLoading(bool status) {
    _isLoading = status;
    notifyListeners();
  }

  set isLoadingGoogle(bool status) {
    _isLoadingGoogle = status;
    notifyListeners();
  }

  set hidePassword(bool status) {
    _hidePassword = status;
    notifyListeners();
  }

  set otpErrorMsg(String? msg) {
    _otpErrorMsg = msg;
    notifyListeners();
  }

  String? errorMsg;
  Future<void> googleLogin(
      {required String email, required BuildContext context}) async {
    // isLoadingGoogle = true;
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      String? fcmtoken = await FCM.generateToken();
      Map<String, dynamic> body = {};
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';
      // body['email'] = '<EMAIL>';

      body['email'] = email;
      body['login_type'] = 'google_login';
      body['token'] = fcmtoken ?? '';
      if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        body['primary'] = iosInfo.identifierForVendor;
        body['manufacturer'] = 'apple';
        body['model'] = iosInfo.model;
        body['platform'] = 'IOS';
      } else {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        body['primary'] = androidInfo.id;
        body['manufacturer'] = androidInfo.manufacturer;
        body['model'] = androidInfo.model;
        body['platform'] = 'Android';
      }
      var response = await http.post(
        googleLoginURL,
        body: jsonEncode(body),
        headers: {'Content-Type': 'application/json'},
      );
      debugPrint('googleLogin ----------- ${response.body} ---- $body ');
      Map<String, dynamic> data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        if (data['result'] == 'failure') {
          Map<String, dynamic> error = data['errors'];
          if (error.containsKey('email')) {
            showToastText(error['email']);
          }
          if (error.containsKey('password')) {
            showToastText(error['passwords']);
          }
          showToastText(data['errors']['email']);
        } else if (data['result'] == 'success') {
          LoginModel.fromJson(data);
          var token = data['token']['access_token'];
          var refresh = data['token']['refresh_token'];
          var email = data['email'];
          var name = data['name'];
          var designation = data['designation'];
          await shared.setString('access_token', token);
          await shared.setString('refresh_token', refresh);
          // SECURITY FIX: Use secure storage instead of SharedPreferences
          await SecureKeyManager.storeAccessToken(token);
          await SecureKeyManager.storeRefreshToken(refresh);

          // FORENSIC: Log successful authentication
          await ForensicLogger.logAuthenticationEvent(
            'LOGIN_SUCCESS',
            email,
            true,
            {
              'email': email,
              'name': name,
              'designation': designation,
            },
          );

          shared.setBool('isLoggedIn', true);
          shared.setString('email', email!);
          shared.setString('name', name!);
          shared.setString('designation', designation);

          // SECURITY FIX: Don't log actual tokens
          log('Authentication successful for user: $email');
          //sending email to watch
          if (Platform.isIOS) {
            const channel = MethodChannel('com.amorn.watch');
            channel.invokeMethod('flutterToWatch',
                {'method': 'sendCounterToNative', 'data': email});
          }
          Navigator.of(context).pushAndRemoveUntil(
              (MaterialPageRoute(builder: (context) => const MasterScreen())),
              (route) => false);
        }
      }
      isLoadingGoogle = false;
      EasyLoading.dismiss();
      notifyListeners();
    } catch (e) {
      isLoadingGoogle = false;
      notifyListeners();
      debugPrint(e.toString());
    }
  }

  String? _loginErrorEmail;
  String? _loginErrorPassword;

  String? get loginErrorEmail => _loginErrorEmail;
  set loginErrorEmail(String? value) {
    _loginErrorEmail = value;
    notifyListeners();
  }

  String? get loginErrorPassword => _loginErrorPassword;
  set loginErrorPassword(String? value) {
    _loginErrorPassword = value;
    notifyListeners();
  }

  String? secretKey;
  Future<void> encryptionKey() async {
    try {
      var response = await http.get(
        normalLgin,
        // headers: {'Content-Type': 'application/json'},
      );
      if (response.statusCode == 200) {
        var data = jsonDecode(response.body);

        secretKey = data['hisense_secure'];
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  String? secureKey;

  Future<void> secKey() async {
    var response = await http.get(secretKeyURL, headers: {
      'Content-Type': 'application/json',
      // 'Host': 'hisense-hscp.e8demo.com',
      // 'Origin': 'https://hisense-hscp.e8demo.com',
    });
    var data = jsonDecode(response.body);
    secureKey = data['hisense_secure'];
  }

  SiginInModel? siginInModel;

  Future<void> siginInWithEmail({
    required String email,
    required String pass,
    required BuildContext context,
  }) async {
    isLoading = true;
    try {
      // FORENSIC: Log authentication attempt
      await ForensicLogger.logAuthenticationEvent(
        'LOGIN_ATTEMPT',
        null, // No user ID yet
        false, // Will update if successful
        {
          'email': email,
          'method': 'email_password',
        },
      );

      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      await encryptionKey();

      // SECURITY FIX: Use secure encryption with GCM mode instead of ECB
      final secureKey = await SecureKeyManager.getEncryptionKey();
      final key = aes.Key.fromBase64(secureKey);
      final iv = aes.IV.fromSecureRandom(16); // Use secure random IV
      final encrypter =
          aes.Encrypter(aes.AES(key, mode: aes.AESMode.gcm)); // Use GCM mode
      String? passworddAES = pass;
      final encryptedPass = encrypter.encrypt(passworddAES, iv: iv);

      //  ------------------ email And Pass Encryption-------------- End---------------------------------------------------------------
      String? fcmtoken = await FCM.generateToken();
      Map<String, dynamic> body = {};
      // body['email'] = encryptedEmail.base64;

      body['email'] = email;
      body['password'] = encryptedPass.base64;
      // body['password'] = pass;
      body['login_type'] = 'normal_login';
      body['token'] = fcmtoken ?? '';
      body['otp-login'] = true;
      body['encryption'] = 1;
      body['hisense_secure'] = secretKey.toString();
      if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        body['primary'] = iosInfo.identifierForVendor;
        body['manufacturer'] = 'apple';
        body['model'] = iosInfo.model;
        body['platform'] = 'IOS';
      } else {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        body['primary'] = androidInfo.id;
        body['manufacturer'] = androidInfo.manufacturer;
        body['model'] = androidInfo.model;
        body['platform'] = 'Android';
      }

      var response = await http.post(
        normalLgin,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(body),
      );
      SharedPreferences shared = await SharedPreferences.getInstance();

      if (response.statusCode == 200) {
        shared.setString('fcmtoken', fcmtoken.toString());

        Map<String, dynamic> json = jsonDecode(response.body);
        if (json.containsKey('OTP')) {
          showSnackBarMessage(context: context, msg: json['OTP'].toString());
        }
        if (json['result'] == 'failure') {
          Map<String, dynamic> errors = json['errors'];
          if (errors.containsKey('email')) {
            showToastText(errors['email']);
            loginErrorEmail = errors['email'];
          } else if (errors.containsKey('date_of_leaving')) {
            showToastText(errors['date_of_leaving']);
            loginErrorEmail = errors['date_of_leaving'];
          } else if (errors.containsKey('message')) {
            showToastText(errors['message']);
            loginErrorEmail = errors['message'];
          } else if (errors.containsKey('password')) {
            showToastText(errors['password']);
            loginErrorPassword = errors['password'];
          } else {
            showToastText(errors['message']);
          }
        } else {
          bool isOtpRequired = json['verification_url'] != null;
          if (isOtpRequired) {
            siginInModel = SiginInModel.fromJson(json);
            otpErrorMsg = null;
            PageNavigator.pushSlideRight(
              context: context,
              route: const OtpScreen(),
            );
          } else {
            LoginModel.fromJson(json);
            var token = json['token']['access_token'];
            var refresh = json['token']['refresh_token'];
            var email = json['email'];
            var name = json['name'];

            // SECURITY FIX: Use secure storage for tokens
            await SecureKeyManager.storeAccessToken(token);
            await SecureKeyManager.storeRefreshToken(refresh);

            // Store other user data in SharedPreferences
            shared.setBool('isLoggedIn', true);
            shared.setString('email', email!);
            shared.setString('name', name!);

            Navigator.of(context).pushAndRemoveUntil(
                (MaterialPageRoute(builder: (context) => const MasterScreen())),
                (route) => false);
          }
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    isLoading = false;
  }

  Future<void> verifyOtp(
      {required String otp, required BuildContext context}) async {
    isLoading = true;
    // otpErrorMsg = null;
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      var body = {};
      if (Platform.isIOS) {
        body['login_otp'] = otp;
        body['action'] = 'login_otp_verification';
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        body['primary'] = iosInfo.identifierForVendor;
        body['manufacturer'] = 'apple';
        body['model'] = iosInfo.model;
        body['platform'] = 'IOS';
      } else {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        body['primary'] = androidInfo.id;
        body['manufacturer'] = androidInfo.manufacturer;
        body['model'] = androidInfo.model;
        body['platform'] = 'Android';
      }

      var response = await http.post(
          Uri.parse(siginInModel!.verificationUrl.toString()),
          body: {'login_otp': otp, 'action': 'login_otp_verification'});

      Map<String, dynamic> data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        if (data['result'] == 'failed') {
          Map<String, dynamic> error = data['errors'];

          if (error.containsKey('login_otp')) {
            showToastText(error['login_otp']);
            otpErrorMsg = error['login_otp'];
          }
        } else if (data['result'] == 'failure') {
          Map<String, dynamic> error = data['errors'];

          if (error.containsKey('login_otp')) {
            showToastText(error['login_otp']);
            otpErrorMsg = error['login_otp'];
          }
        } else if (data['result'] == 'success') {
          LoginModel.fromJson(data);

          var token = data['token']['access_token'];
          var refresh = data['token']['refresh_token'];
          var email = data['email'];
          var name = data['name'];
          var designation = data['designation'];
          bool isAdmin = data['is_admin'] ?? false;

          // SECURITY FIX: Use secure storage for tokens
          await SecureKeyManager.storeAccessToken(token);
          await SecureKeyManager.storeRefreshToken(refresh);

          // Store other user data in SharedPreferences
          shared.setBool('is_admin', isAdmin);
          shared.setBool('isLoggedIn', true);
          shared.setString('email', email!);
          shared.setString('name', name!);
          shared.setString('designation', designation);

          Navigator.of(context).pushAndRemoveUntil(
              (MaterialPageRoute(builder: (context) => const MasterScreen())),
              (route) => false);
        }
      } else if (response.statusCode == 400) {
        if (data['result'] == 'failure') {
          Map<String, dynamic> error = data['errors'];

          if (error.containsKey('login_otp')) {
            showToastText(error['login_otp']);
            otpErrorMsg = error['login_otp'];
          }
          // showToastText(data['login_otp'].toString());
        }
      }

      notifyListeners();
    } catch (e) {
      debugPrint(e.toString());
    }
    isLoading = false;
  }

  Future<void> resendOtp() async {
    var response = await http.post(
      Uri.parse(
        siginInModel!.resendOtpUrl.toString(),
      ),
    );

    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);

      siginInModel = SiginInModel.fromJson(data);
      showToastText('');
    }
  }

  Future<void> logOut() async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      shared.setBool('last_huddle_dialog_date', false);

      // SECURITY FIX: Use secure API service for logout
      await ApiService.get(logoutURL);

      // Clear all stored tokens
      await SecureKeyManager.clearAllKeys();
    } catch (e) {
      debugPrint('Error during logout: $e');
      // Even if logout API fails, clear local tokens
      await SecureKeyManager.clearAllKeys();
    }
  }

  Future<void> signInWithApple(
    BuildContext context,
    // String? fcmtoken,
    // String? manufacturer,
    // String? model,
    // String? platform,
    // String? primary,
  ) async {
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    log('Google sign-in with email: ${credential.email}');

    Map<String, dynamic> body = {};
    String? fcmtoken = await FCM.generateToken();
    body['token'] = fcmtoken ?? '';

    IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
    body['primary'] = iosInfo.identifierForVendor;
    body['manufacturer'] = 'apple';
    body['model'] = iosInfo.model;
    body['platform'] = 'IOS';

    if (credential.email != null || credential.userIdentifier != null) {
      {
        if (credential.email != null) body['email'] = credential.email;
        body['identifier'] = credential.userIdentifier;
        body['login_type'] = 'apple';
      }

      var response = await http.post(socialLogin, body: body);
      Map<String, dynamic> data = jsonDecode(response.body);
      SharedPreferences shared = await SharedPreferences.getInstance();
      if (response.statusCode == 200) {
        if (data['result'] == 'failure') {
          Map<String, dynamic> error = data['errors'];
          if (error.containsKey('email')) {
            showToastText(error['email']);
          }
          if (error.containsKey('password')) {
            showToastText(error['passwords']);
          }
          showToastText(data['errors']['email']);
        } else if (data['result'] == 'success') {
          log('Google sign-in success');

          LoginModel.fromJson(data);

          var token = data['token']['access_token'];
          var refresh = data['token']['refresh_token'];
          var email = data['email'];

          // SECURITY FIX: Use secure storage for tokens
          await SecureKeyManager.storeAccessToken(token);
          await SecureKeyManager.storeRefreshToken(refresh);

          // Store other user data in SharedPreferences
          shared.setBool('isLoggedIn', true);
          shared.setString('email', email!);

          Navigator.of(context).pushAndRemoveUntil(
              (MaterialPageRoute(builder: (context) => const MasterScreen())),
              (route) => false);
        }
      }
      isLoading = false;

      notifyListeners();
    }
  }

  Future<void> updateToken({required BuildContext context}) async {
    try {
      // SECURITY FIX: Use secure token storage
      String? refreshTokenOld = await SecureKeyManager.getRefreshToken();

      if (refreshTokenOld == null) {
        // No refresh token available, redirect to login
        await ForensicLogger.logAuthenticationEvent(
          'TOKEN_REFRESH_FAILED',
          null,
          false,
          {'reason': 'No refresh token available'},
        );
        EasyLoading.dismiss();
        if (context.mounted) {
          PageNavigator.pushAndRemoveUntil(
            context: context,
            route: const LoginWithEmailScreen(),
          );
        }
        return;
      }

      Uri uri = Uri.parse('${baseUrl}refresh_token/');
      var response = await http
          .post(uri, body: {'refresh_token': refreshTokenOld.toString()});
      debugPrint('refresh token url === ${response.body}');
      debugPrint('refresh token url === ${response.statusCode}');

      if (response.statusCode == 200) {
        var data = jsonDecode(response.body);
        if (data['result'] == 'success') {
          var token = data['records']['access_token'];
          var refresh = data['records']['refresh_token'];

          // SECURITY FIX: Store tokens securely
          await SecureKeyManager.storeAccessToken(token);
          await SecureKeyManager.storeRefreshToken(refresh);

          // FORENSIC: Log successful token refresh
          await ForensicLogger.logAuthenticationEvent(
            'TOKEN_REFRESH_SUCCESS',
            null,
            true,
            {'timestamp': DateTime.now().toIso8601String()},
          );
        } else {
          // Token refresh failed, redirect to login
          await ForensicLogger.logAuthenticationEvent(
            'TOKEN_REFRESH_FAILED',
            null,
            false,
            {'reason': 'Server returned failure', 'response': data},
          );
          EasyLoading.dismiss();
          if (context.mounted) {
            PageNavigator.pushAndRemoveUntil(
              context: context,
              route: const LoginWithEmailScreen(),
            );
          }
        }
      } else {
        // HTTP error, redirect to login
        await ForensicLogger.logAuthenticationEvent(
          'TOKEN_REFRESH_FAILED',
          null,
          false,
          {'reason': 'HTTP error', 'status_code': response.statusCode},
        );
        EasyLoading.dismiss();
        if (context.mounted) {
          PageNavigator.pushAndRemoveUntil(
            context: context,
            route: const LoginWithEmailScreen(),
          );
        }
      }
      notifyListeners();
    } catch (e) {
      // FORENSIC: Log error
      await ForensicLogger.logAuthenticationEvent(
        'TOKEN_REFRESH_ERROR',
        null,
        false,
        {'error': e.toString()},
      );
      debugPrint('Token refresh error: $e');
      EasyLoading.dismiss();
      if (context.mounted) {
        PageNavigator.pushAndRemoveUntil(
          context: context,
          route: const LoginWithEmailScreen(),
        );
      }
    }
  }
}
