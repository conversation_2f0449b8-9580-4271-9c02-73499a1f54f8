import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/personal_info.dart';
import 'package:e8_hr_portal/model/salary_transfer_request.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:http/http.dart' as http;
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../util/dailoge.dart';

class SalaryTransferLetterProvider extends ChangeNotifier {
  final RefreshController refreshController =
      RefreshController(initialRefresh: false);

  void onRefresh() async {
    await fetchPreviousRequests();
    refreshController.refreshCompleted();
  }

  PersonalInfo? _personalInfo;
  bool _isLoading = false;
  String? _errorMsg;
  List<SalaryTransferRequest> _requestList = [];

  PersonalInfo? get personalInfo => _personalInfo;
  bool get isLoading => _isLoading;
  String? get errorMsg => _errorMsg;
  List<SalaryTransferRequest> get requestList => _requestList;

  set isLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  set requestList(List<SalaryTransferRequest> value) {
    _requestList = value;
    notifyListeners();
  }

  Future<bool> fetchPreviousRequests() async {
    if (requestList.isEmpty) {
      EasyLoading.show();
    }
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    Uri uri =
        Uri.parse('${baseUrl}certificates-list/?action=salary_transfer_letter');
    var response =
        await http.get(uri, headers: {"Authorization": "Bearer $auth"});

    if (response.statusCode == 200) {
      var json = jsonDecode(response.body);
      if (json['result'] == 'success') {
        List records = json['records'];
        requestList =
            records.map((e) => SalaryTransferRequest.fromJson(e)).toList();
      }
      EasyLoading.dismiss();
      notifyListeners();
      return true;
    }
    EasyLoading.dismiss();
    notifyListeners();
    return false;
  }

  Future<void> fetchPersonalInfo() async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");
      Uri uri =
          Uri.parse('${baseUrl}personal-info/?action=salary_transfer_letter');
      var response =
          await http.get(uri, headers: {"Authorization": "Bearer $auth"});

      if (response.statusCode == 200) {
        var json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          _personalInfo = PersonalInfo.fromJson(json['records']);
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<bool> submitRequest(
      {String? to, String? bankName, String? branchName}) async {
    try {
      isLoading = true;
      _errorMsg = null;
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");
      Uri uri = Uri.parse('${baseUrl}salary-transfer-letter-create/');
      var response = await http.post(
        uri,
        headers: {"Authorization": "Bearer $auth"},
        body: {
          'to': '$to',
          'bank_name': '$bankName',
          'branch_name': '$branchName',
        },
      );
      if (response.statusCode == 200) {
        var json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          isLoading = false;
          return true;
        }
      } else if (response.statusCode == 400) {
        var json = jsonDecode(response.body);
        if (json['result'] == 'failure') {
          Map<String, dynamic> errors = json['erros'];
          if (errors.containsKey('request')) {
            _errorMsg = errors['request'];
          }
        }
      }
      isLoading = false;
      return false;
    } catch (e) {
      isLoading = false;
      debugPrint(e.toString());
      return false;
    }
  }

  Future<bool> salaryTransferCommentCreate({
    required BuildContext context,
    required String action,
    required int actionId,
    required String comment,
  }) async {
    final navigator = Navigator.of(context);
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");

    Map<String, String>? body = {
      "action": action,
      "action_id": actionId.toString(),
      "comment": comment,
    };
    var response = await http.post(commentsCreateURL,
        body: body, headers: {"Authorization": "Bearer $auth"});

    if (response.statusCode == 200) {
      Map<String?, dynamic> json = jsonDecode(response.body);

      if (json["result"] == "success") {
        if (json.containsKey("message")) {
          showToastText(json["message"]);
          navigator.pop();
          navigator.pop();
        }
        return true;
      }
    }
    if (response.statusCode == 400) {
      Map<String?, dynamic> json = jsonDecode(response.body);
      if (json["result"] == "failure") {
        if (json.containsKey("erros")) {
          Map<String, dynamic> erros = json["erros"];
          if (erros.containsKey("request")) {
            showToastText(erros["request"]);
          }
        }
        return false;
      }
    }
    return false;
  }

  String? _salaryTransferPDF;
  String? get salaryTransferPDF => _salaryTransferPDF;
  Future<bool> getSalaryTransferCertificatePdf(
      {required String certificateId}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    http.Response response = await http.get(
        Uri.parse(
            "${baseUrl}certificates-view/?action=salary_transfer_letter&action_id=$certificateId"),
        headers: {"Authorization": "Bearer $auth"});

    _salaryTransferPDF = null;
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      _salaryTransferPDF = data["records"]["file"];
      return true;
    }
    return false;
  }
}
