// ignore_for_file: use_build_context_synchronously
import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart' as dio;
import 'package:e8_hr_portal/model/booked_room_status_model.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:http/http.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../util/date_formatter.dart';
import '../util/urls.dart';

class BookedMeetingRoomProvider with ChangeNotifier {
  final dio.Dio _dio = dio.Dio();
  //admin request page index
  int _currentIndex = 0;
  int get currentIndex => _currentIndex;
  set currentIndex(int index) {
    _currentIndex = index;
    notifyListeners();
  }

  //
//booked meeting room status for users
  bool _isBookedRoomsStatusLoading = false;
  bool get isBookedRoomsStatusLoading => _isBookedRoomsStatusLoading;
  set isBookedRoomsStatusLoading(bool value) {
    _isBookedRoomsStatusLoading = value;
    notifyListeners();
  }

  List<BookedRoomStatusModel> bookedRoomsStatusList = [];
  Future<void> getBookedMeetingRoomStatus() async {
    isBookedRoomsStatusLoading = true;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response? response = await get(bookedRoomsStatusURL,
        headers: {'Authorization': 'Bearer $auth'});

    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      bookedRoomsStatusList = (data['data'] as List)
          .map((e) => BookedRoomStatusModel.fromJson(e))
          .toList();
    }
    notifyListeners();
    isBookedRoomsStatusLoading = false;
  }

  //
  //cancel booked meeting room
  Future<void> cancelBookedMeetingRoom(
      {required int bookingID,
      required String reason,
      required BuildContext context}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    EasyLoading.show();
    Response response = await patch(
        Uri.parse('$cancelBookedMeetingRoomURL$bookingID'),
        body: {'reason_for_cancel': reason},
        headers: {'Authorization': 'Bearer $auth'});
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      showSnackBarMessage(context: context, msg: data['message']);
      getBookedMeetingRoomStatus();
    } else {
      showSnackBarMessage(context: context, msg: 'Something went wrong!!!');
    }
    EasyLoading.dismiss();
  }

  //
  //meeting room request for admin
  bool _isRequestLoading = false;
  bool get isRequestLoading => _isRequestLoading;
  set isRequestLoading(bool value) {
    _isRequestLoading = value;
    notifyListeners();
  }

  List<BookedRoomStatusModel> adminMeetingRoomRequestList = [];
  Future<void> getMeetingRoomRequestForAdmin({required String status}) async {
    _isRequestLoading = true;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response? response = await get(
        Uri.parse('$meetingRequestForAdminURL$status'),
        headers: {'Authorization': 'Bearer $auth'});
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      adminMeetingRoomRequestList = (data['data'] as List)
          .map((e) => BookedRoomStatusModel.fromJson(e))
          .toList();
      //removing expired meeting rooms
      if (currentIndex == 0) {
        adminMeetingRoomRequestList = adminMeetingRoomRequestList.where((e) {
          DateTime? dt =
              stringToDateTime(date: e.bookingDate!, format: 'yyyy-MM-dd');
          DateTime now = DateTime.now();
          bool? isExpired =
              dt?.isBefore(DateTime(now.year, now.month, now.day));
          return isExpired == false;
        }).toList();
      }
      //
    } else {
      adminMeetingRoomRequestList.clear();
    }
    notifyListeners();
    _isRequestLoading = false;
  }

//-------------------------------Cancel Approved request-----------------------------------------------
  Future<void> cancelApprovedMeetingRoom(
      {required int bookingID, required String reason}) async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      Map<String, dynamic> data = {};
      data['reason_for_cancel'] = reason;
      final formData = dio.FormData.fromMap(data);
      final response = await _dio.patch(
        data: formData,
        '$bookingCancelUrl?booking_id=$bookingID',
        options: dio.Options(
          headers: {'Authorization': 'Bearer $auth'},
          validateStatus: (status) => true,
        ),
      );
      log('cancelApprovedMeetingRoom -----${response.realUri}---> ${response.data}');
      if (response.statusCode == 200) {
        if (response.data['result'] == 'sucess') {
          Map<String, dynamic> data = response.data;
          BookedRoomStatusModel item = adminMeetingRoomRequestList.firstWhere(
              (element) => element.id == bookingID,
              orElse: () => BookedRoomStatusModel());
          if (item.id != null && item.id != 0) {
            int index = adminMeetingRoomRequestList.indexOf(item);
            adminMeetingRoomRequestList.removeAt(index);
          }
          BookedRoomStatusModel item2 = upcomingMeetingList.firstWhere(
              (element) => element.id == bookingID,
              orElse: () => BookedRoomStatusModel());
          if (item2.id != null && item2.id != 0) {
            log('message------->1 ${upcomingMeetingList.length}');
            int index = upcomingMeetingList.indexOf(item2);
            upcomingMeetingList.removeAt(index);
            log('message------->2 ${upcomingMeetingList.length}');
          }
          BookedRoomStatusModel item3 = todayMeetingList.firstWhere(
              (element) => element.id == bookingID,
              orElse: () => BookedRoomStatusModel());
          if (item3.id != null && item3.id != 0) {
            int index = todayMeetingList.indexOf(item3);
            todayMeetingList.removeAt(index);
          }
          BookedRoomStatusModel item4 = previousMeetingList.firstWhere(
              (element) => element.id == bookingID,
              orElse: () => BookedRoomStatusModel());
          if (item4.id != null && item4.id != 0) {
            int index = previousMeetingList.indexOf(item4);
            previousMeetingList.removeAt(index);
          }
          if (data.containsKey('message')) {
            showToastText(data['message']);
          }
        }
        //removing expired meeting rooms
      } else if (response.statusCode == 400) {
        if (response.data['result'] == 'failed') {
          Map<String, dynamic> json = response.data;
          if (json.containsKey('message')) {
            String message = json['message'] ?? '';
            showToastText(message);
          }
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      notifyListeners();
    }
  }

  //
  //meeting room approve or reject for admin section
  Future<void> approveOrReject({
    required BuildContext context,
    required String bookingID,
    required String statusID,
    required String reason,
    required BookedRoomStatusModel item,
  }) async {
    try {
      EasyLoading.show();
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      Response response;
      if (statusID == '2') {
        response = await post(approveOrRejectURL, headers: {
          'Authorization': 'Bearer $auth'
        }, body: {
          'booking_id': bookingID,
          'status': statusID,
        });
      } else {
        response = await post(approveOrRejectURL, headers: {
          'Authorization': 'Bearer $auth'
        }, body: {
          'booking_id': bookingID,
          'status': statusID,
          'reason': reason
        });
      }
      log('approveOrReject ---- > ${response.body}------${response.statusCode}');
      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
        BookedRoomStatusModel item = adminMeetingRoomRequestList.firstWhere(
            (element) => element.id == int.parse(bookingID),
            orElse: () => BookedRoomStatusModel());
        if (item.id == null && item.id == 0) return;
        int index = adminMeetingRoomRequestList.indexOf(item);
        adminMeetingRoomRequestList.removeAt(index);
        if (data.containsKey('message')) {
          showToastText(data['message']);
        }
        notifyListeners();
        // getMeetingRoomRequestForAdmin(status: '1');
      } else {
        showToastText('Something went wrong!!!');
      }
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint(e.toString());
    } finally {
      EasyLoading.dismiss();
    }
  }

  //
  //meeting invite request for members section
  List<BookedRoomStatusModel> memberMeetingInviteRequestList = [];
  Future<void> getMeetingInviteRequestForMember() async {
    _isRequestLoading = true;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response? response = await get(meetingRequestForMembersURL,
        headers: {'Authorization': 'Bearer $auth'});
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      memberMeetingInviteRequestList = (data['data'] as List)
          .map((e) => BookedRoomStatusModel.fromJson(e))
          .toList();
    } else {
      memberMeetingInviteRequestList.clear();
    }
    notifyListeners();
    _isRequestLoading = false;
  }

//approve or reject for invited memmbers section
  Future<void> approveOrRejectInviteRequest(
      {required BuildContext context,
      required String bookingID,
      required String statusID}) async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response response = await post(approveOrRejectForInviteesURL,
        headers: {'Authorization': 'Bearer $auth'},
        body: {'booking_id': bookingID, 'status': statusID});
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      showSnackBarMessage(context: context, msg: data['message']);
      getMeetingInviteRequestForMember();
    } else {
      showSnackBarMessage(context: context, msg: 'Something went wrong!!!');
    }
    EasyLoading.dismiss();
  }

  int scheduledMeetingIndex = 0;
  onChangedScheduledMeetingIndex({required int index}) {
    scheduledMeetingIndex = index;
    notifyListeners();
  }

//
  //scheduled meeting room section
  List<BookedRoomStatusModel> todayMeetingList = [];
  List<BookedRoomStatusModel> previousMeetingList = [];
  List<BookedRoomStatusModel> upcomingMeetingList = [];
  Future<void> getScheduledMeetings(
      {required String action, required BuildContext context}) async {
    try {
      if (todayMeetingList.isEmpty ||
          previousMeetingList.isEmpty ||
          upcomingMeetingList.isEmpty) {
        EasyLoading.show();
      }
      previousMeetingList.clear();
      todayMeetingList.clear();
      upcomingMeetingList.clear();
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      Response? response = await get(Uri.parse('$scheduledMeetingsURL$action'),
          headers: {'Authorization': 'Bearer $auth'});
      log('getScheduledMeetings --> ${jsonDecode(utf8.decode(response.bodyBytes))} - ${response.request}----${response.statusCode}');
      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
        if (action == 'today') {
          todayMeetingList = (data['data'] as List)
              .map((e) => BookedRoomStatusModel.fromJson(e))
              .toList();
        } else if (action == 'previous') {
          previousMeetingList = (data['data'] as List)
              .map((e) => BookedRoomStatusModel.fromJson(e))
              .toList();
        } else if (action == 'next') {
          upcomingMeetingList = (data['data'] as List)
              .map((e) => BookedRoomStatusModel.fromJson(e))
              .toList();
        }
      } else {
        switch (action) {
          case 'today':
            todayMeetingList.clear();
            break;
          case 'previous':
            previousMeetingList.clear();
            break;
          case 'next':
            upcomingMeetingList.clear();
            break;
        }
      }
      notifyListeners();
      EasyLoading.dismiss();
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      log('finally-----------------------------');

      EasyLoading.dismiss();
    }
  }

  //
  //meeting room history listing with pagination
  late PagingController<int, BookedRoomStatusModel>
      roomHistoryPaginationController;
  int currentPageHistory = 0;
  initMeetingRoomHistoryPagination({required int roomID}) {
    currentPageHistory = 0;
    roomHistoryPaginationController = PagingController(firstPageKey: 1);

    roomHistoryPaginationController.addPageRequestListener((pageKey) {
      getMeetingRoomHistory(page: pageKey, roomID: roomID);
    });
  }

  Future<void> getMeetingRoomHistory(
      {required int page, required int roomID}) async {
    if (currentPageHistory != page) {
      currentPageHistory = page;
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      Response response = await get(
          Uri.parse(
              '${baseUrl}meeting_room_history/?room_id=$roomID&page=$page&limit=10'),
          headers: {'Authorization': 'Bearer $auth'});
      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
        List<BookedRoomStatusModel> tempList = (data['data'] as List)
            .map((e) => BookedRoomStatusModel.fromJson(e))
            .toList();
        if (data['has_next']) {
          roomHistoryPaginationController.appendPage(tempList, page + 1);
        } else {
          roomHistoryPaginationController.appendLastPage(tempList);
        }
      } else {
        roomHistoryPaginationController.appendLastPage([]);
      }
    }
  }
  //
}
