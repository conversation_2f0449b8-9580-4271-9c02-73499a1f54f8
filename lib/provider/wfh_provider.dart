import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:e8_hr_portal/view/wfh/models/wfh_record_model.dart';
import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/wfh_member_model.dart';
import '../model/wfh_permission_model.dart';

class WFHProvider with ChangeNotifier {
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  set isLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  bool isFetching = false;
//selected date for filter
  DateTime _selectedDate = DateTime.now();
  DateTime get selectedDate => _selectedDate;
  set selectedDate(DateTime date) {
    _selectedDate = date;
    notifyListeners();
    getAllWFHEmployees();
  }

  List<WfhTypeModel> wfhStatusList = [
    WfhTypeModel(id: 0, name: 'All'),
    WfhTypeModel(id: 1, name: 'Pending'),
    WfhTypeModel(id: 2, name: 'In-Progress'),
    WfhTypeModel(id: 3, name: 'Approved'),
    WfhTypeModel(id: 4, name: 'Rejected'),
    WfhTypeModel(id: 5, name: 'Cancelled'),
    WfhTypeModel(id: 6, name: 'Expired'),
  ];

  WfhTypeModel selectedWFHRequestStatusType = WfhTypeModel(id: 0, name: 'All');
  onChangedWFHRequestStatusType({required WfhTypeModel item}) {
    selectedWFHRequestStatusType = item;
    notifyListeners();
  }

//
//work from home employees
  List<WFHMemberModel> wfhEmployeesList = [];
  Future<void> getAllWFHEmployees() async {
    isLoading = true;

    String date =
        formatDateFromDate(dateTime: selectedDate, format: 'yyyy-MM-dd');
    Response response = await dio.get('$wfhEmployeeUrl$date',
        options: Options(
            headers: await getHeaders(), validateStatus: (status) => true));
    if (response.statusCode == 200) {
      Map<String, dynamic> data = response.data;
      wfhEmployeesList = (data['records'] as List)
          .map((e) => WFHMemberModel.fromJson(e))
          .toList();
    } else {
      wfhEmployeesList.clear();
    }
    isLoading = false;
    notifyListeners();
  }

  //
  //search
  List<WFHPermissonModel> employeesListToShow = [];

  void filter(String keyword) {
    if (keyword.isEmpty) {
      employeesListToShow = employeesList;
      notifyListeners();
    } else {
      employeesListToShow = employeesList
          .where((user) =>
                  user.fullName!.toLowerCase().contains(keyword.toLowerCase())
              // ||
              // user.designation!.first.name!
              //     .toLowerCase()
              //     .contains(keyword.toLowerCase())
              )
          .toList();
      notifyListeners();
    }
  }

  //
  //all members

  List<int> allWFHEmployeesIDList = [];
  changePermissionStatically({required WFHPermissonModel item}) {
    // if (allWFHEmployeesIDList.contains(userID)) {
    //   allWFHEmployeesIDList.remove(userID);
    // } else {
    //   allWFHEmployeesIDList.add(userID);
    // }
    if (item.regularLeaveRequests == true) {
      item.regularLeaveRequests = false;
    }
    notifyListeners();
  }

  bool _isAllWFH = false;
  bool get isAllWFH => _isAllWFH;
  set isAllWFH(bool value) {
    _isAllWFH = value;
    notifyListeners();
  }

  List<WFHPermissonModel> employeesList = [];

  Future<void> getAllEmployees() async {
    if (employeesList.isEmpty) {
      isLoading = true;
    }

    Response response = await dio.get(allEmployeesURL.toString(),
        options: Options(
            headers: await getHeaders(), validateStatus: (status) => true));

    log('getAllEmployees --> ${response.data}');
    if (response.statusCode == 200) {
      Map<String, dynamic> data = response.data;
      employeesList = (data['data'] as List)
          .map((e) => WFHPermissonModel.fromJson(e))
          .toList();
      allWFHEmployeesIDList = employeesList
          .where((e) => e.wfhStatus == true)
          .map((e) => e.userId ?? 0)
          .toList();
      employeesListToShow = employeesList;
      if (data.containsKey('all_permission')) {
        isAllWFH = data['all_permission'];
      }
    } else {
      employeesList.clear();
    }
    isLoading = false;
    notifyListeners();
  }

  //
  //wfh permission
  Future<void> setWfhPermission({int? userID, bool? isAllWFH}) async {
    Map<String, dynamic> body = {};
    if (isAllWFH != null) {
      body['all_permission'] = isAllWFH == true ? 'True' : 'False';
    } else if (userID != null) {
      body['users_id'] = userID.toString();
    }
    Response response = await dio.post(wfhPermissionURL.toString(),
        data: body,
        options: Options(
            headers: await getHeaders(), validateStatus: (status) => true));
    log('setWfhPermission => ${response.data}');
    if (response.statusCode == 200 && isAllWFH != null) {
      getAllEmployees();
    }
  }

  //----------------------------------------------------------------------------------------------------------------------//

  List<WfhTypeModel> wfhTypes = [
    WfhTypeModel(id: 4, name: 'Temporary'),
    WfhTypeModel(id: 5, name: 'Regular'),
  ];
  WfhTypeModel selectedWFHType = WfhTypeModel(id: 4, name: 'Temporary');
  onChangedWFHType({required WfhTypeModel item}) {
    selectedWFHType = item;
    notifyListeners();
  }

  Future<DateTime?> pickDate({required BuildContext context}) async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(3100),
    );
    return picked;
  }

  //WFH request
  Dio dio = Dio();
  Future<bool> postWfhApplication(
      {required String fromDate,
      required String toDate,
      required String remark}) async {
    try {
      if (isLoading) return false;
      Map<String, dynamic> data = {};
      data['wfh_type'] = selectedWFHType.id;
      data['remark'] = remark;
      if (selectedWFHType.id == wfhTypes.first.id) {
        String tempFromDate =
            formatDateFromString(fromDate, 'dd-MM-yyyy', 'yyyy-MM-dd');
        String tempToDate =
            formatDateFromString(toDate, 'dd-MM-yyyy', 'yyyy-MM-dd');
        data['from_date'] = tempFromDate;
        data['to_date'] = tempToDate;
      }
      FormData formData = FormData.fromMap(data);
      log('postWfhApplication - $data');
      isLoading = true;
      Response response = await dio.post('$wfhRequestURL',
          data: formData,
          options: Options(
              headers: await getHeaders(), validateStatus: (status) => true));
      isLoading = false;
      log('postWfhApplication - ${response.data} - ${response.statusCode} - ${await getHeaders()} - ${response.realUri} - $data');
      if (response.statusCode == 201 || response.statusCode == 200) {
        Map<String, dynamic> data = response.data;
        if (data.containsKey('message')) {
          log('getWFHRecords();');
          showToastText(data['message']);
          // currentPage = 1;
          // whfPaginationController.refresh();
        }
        return true;
      }
      if (response.statusCode == 400) {
        Map<String, dynamic> data = response.data;
        if (data.containsKey('message')) {
          if (data['message'] is String) {
            showToastText(data['message']);
          }
          if (data['message'] is Map) {
            Map<String, dynamic> message = data['message'];
            if (message.containsKey('non_field_errors')) {
              List errors = message['non_field_errors'];
              if (errors.isNotEmpty) {
                showToastText(errors.first);
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      isLoading = false;
    }
    return false;
  }

  // List<WFHRecordsModel> wfhRecords = [];
  // int _currentPage = 1;
  // bool _hasNext = true;
  // ScrollController scrollController =
  //     ScrollController(); // for scroll pagination
  // Future<void> getWFHRecords({bool isLoadMore = false}) async {
  //   if (isFetching || (!_hasNext && isLoadMore)) return;

  //   try {
  //     isFetching = true;
  //     notifyListeners();

  //     if (!isLoadMore) {
  //       _currentPage = 1;
  //       wfhRecords.clear();
  //     }

  //     StringBuffer stringBuffer = StringBuffer(
  //       '$wfhRecordsURL?wfh_type=${selectedWFHType.id}&limit=10&page=$_currentPage',
  //     );

  //     if (selectedWFHRequestStatusType.id != 0) {
  //       stringBuffer.write('&status=${selectedWFHRequestStatusType.id}');
  //     }

  //     final response = await dio.get(
  //       stringBuffer.toString(),
  //       options: Options(headers: await getHeaders()),
  //     );

  //     if (response.statusCode == 200 && response.data['result'] == 'success') {
  //       log('json - ${response.data}');
  //       final json = WFHRecordsModel.fromJson(response.data);

  //       // wfhRecords.addAll(json.data ?? []);
  //       // _hasNext = json.hasNext ?? false;
  //       // _currentPage++; // go to next page on next scroll
  //     }
  //   } catch (e) {
  //     debugPrint(e.toString());
  //   } finally {
  //     isFetching = false;
  //     notifyListeners();
  //   }
  // }

  // int currentPageLeaveRecords = 0;
  // PagingController<int, WFHRecordsModel>? pagingControllerLeaveRecords;
  // initPageLeaveRecords() async {
  //   currentPageLeaveRecords = 0;
  //   pagingControllerLeaveRecords = PagingController(firstPageKey: 1);
  //   pagingControllerLeaveRecords?.addPageRequestListener((pageKey) {
  //     getEmployeeLeaveDetailes(page: pageKey);
  //   });
  // }

  // Future<void> getEmployeeLeaveDetailes({required int page}) async {
  //   // try {
  //   if (currentPageLeaveRecords != page) {
  //     currentPageLeaveRecords = page;

  //     String? action = '';
  //     switch (selectedWFHRequestStatusType.toLowerCase()) {
  //       case 'all':
  //         action = '';
  //         break;
  //       case 'pending':
  //         action = "1";
  //         break;
  //       case 'in-progress':
  //         action = "2";
  //         break;
  //       case 'approved':
  //         action = "3";
  //         break;
  //       case 'rejected':
  //         action = "4";
  //         break;
  //       case 'cancelled':
  //         action = "5";
  //         break;
  //       case 'expired':
  //         action = "6";
  //         break;
  //       case 'holiday':
  //         action = "7";
  //         break;
  //       default:
  //         action = '';
  //     }

  //     var response = await dio.get(
  //         "$wfhRecordsURL?filter_data=$action&limit=20&page=$page/",
  //         options: Options(
  //             headers: await getHeaders(), validateStatus: (status) => true));

  //     log("request -- ${response.data} -- ${response.realUri} - ${response.statusCode}");

  //     if (response.statusCode == 200) {
  //       // noData = jsonDecode(response.body);
  //       var data = response.data;

  //       List json = data['data'];
  //       List<WFHRecordsModel> temp = [];
  //       temp = json.map((e) => WFHRecordsModel.fromJson(e)).toList();

  //       if (data['has_next']) {
  //         int nextPage = page + 1;
  //         pagingControllerLeaveRecords?.appendPage(temp, nextPage);
  //       } else {
  //         pagingControllerLeaveRecords?.appendLastPage(temp);
  //       }

  //       // empLeaveDetailes?.data?.finalData.sort(((a, b) =>
  //       //     DateTime.parse(a.toDate.toString())
  //       //         .compareTo(DateTime.parse(b.toDate.toString()))));
  //       notifyListeners();
  //     } else {
  //       int nextPage = 0;
  //       pagingControllerLeaveRecords?.appendPage([], nextPage);

  //       notifyListeners();
  //     }
  //   }
  //   notifyListeners();
  //   EasyLoading.dismiss();
  //   // } catch (e) {
  //   //   debugPrint(e.toString());
  //   //   debugPrint(e.toString());
  //   //   (pagingControllerLeaveRecords?.error);
  //   // }
  // }

  late PagingController<int, WFHRecordsModel> whfPaginationController;
  int currentPage = 0;
  initWFHRecordsPagination() {
    currentPage = 0;
    whfPaginationController = PagingController(firstPageKey: 1);
    whfPaginationController.addPageRequestListener((pageKey) {
      getWFHRecords(page: pageKey);
    });
  }

  Future<void> getWFHRecords({required int page}) async {
    currentPage = page;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    StringBuffer stringBuffer = StringBuffer(
        '$wfhRecordsURL?wfh_type=${selectedWFHType.id}&limit=15&page=$currentPage');
    if (selectedWFHRequestStatusType.id != 0) {
      stringBuffer.write('&status=${selectedWFHRequestStatusType.id}');
    }
    final response = await dio.get(stringBuffer.toString(),
        options: Options(headers: {'Authorization': 'Bearer $auth'}));
    log('getWFHRecords - [32m${response.realUri}[0m - ${response.data}');
    if (response.statusCode == 200) {
      Map<String, dynamic> data = response.data;
      List<WFHRecordsModel> tempList = (data['data'] as List)
          .map((e) => WFHRecordsModel.fromJson(e))
          .toList();
      if (data['has_next']) {
        whfPaginationController.appendPage(tempList, page + 1);
      } else {
        whfPaginationController.appendLastPage(tempList);
      }
    } else {
      whfPaginationController.appendLastPage([]);
    }
  }
}

class WfhTypeModel {
  int id;
  String name;
  WfhTypeModel({required this.id, required this.name});
}
