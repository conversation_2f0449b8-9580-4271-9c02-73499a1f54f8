import 'dart:convert';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/tickets_overview.dart';
import 'package:e8_hr_portal/model/user_tickets_list.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../util/urls.dart';

class TicketsProvider extends ChangeNotifier {
  bool _isImageRemoved = false;
  bool get isImageRemoved => _isImageRemoved;
  set isImageRemoved(bool value) {
    _isImageRemoved = value;
    notifyListeners();
  }

  String? _alreadySelectedImage;
  String? get alreadySelectedImage => _alreadySelectedImage;
  set alreadySelectedImage(String? image) {
    _alreadySelectedImage = image;
    if (image == null) {
      isImageRemoved = true;
    }
    notifyListeners();
  }

  bool _clicked = false;
  bool get clicked => _clicked;
  set clicked(bool value) {
    _clicked = value;
    notifyListeners();
  }

  String? _ticketsId;
  String? get ticketsId => _ticketsId;
  Future<bool> generateTicketsID() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    http.Response response = await http.get(Uri.parse("$generateTicketsIDURL"),
        headers: {"Authorization": "Bearer $auth"});
    _ticketsId = null;
    if (response.statusCode == 200) {
      Map<String, dynamic> json = jsonDecode(response.body);
      _ticketsId = json["ticket_id"];
      return true;
    }
    return false;
  }

  Future<bool> createTicket(
      {required String subject,
      required String description,
      required String date}) async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");
      String createdAt =
          formatDateFromString(date, "dd MMM yyyy", "yyyy-MM-dd");
      Uri uri = Uri.parse('$createTicketURL');
      // var response = await http.post(
      //   uri,
      //   headers: {"Authorization": "Bearer $auth"},
      //   body: {
      //     'subject': subject,
      //     'description': description,
      //     'created_at': createdAt,
      //   },
      // );
      var request = http.MultipartRequest("POST", uri);
      request.headers['Authorization'] = "Bearer $auth";
      request.fields["subject"] = subject;
      request.fields["description"] = description;
      request.fields["created_at"] = createdAt;
      if (imageFile != null) {
        var image =
            await http.MultipartFile.fromPath("ticket_img", imageFile!.path);
        request.files.add(image);
      }
      var response = await request.send();
      var responseData = await response.stream.toBytes();
      Map<String, dynamic> json = jsonDecode(utf8.decode(responseData));
      if (response.statusCode == 200) {
        if (json['result'] == 'success') {
          currentPage = 0;
          pagingController.refresh();
          pagingController.notifyListeners();
          notifyListeners();
          showToastText(json["message"]);
          return true;
        } else if (json['result'] == 'failed') {
          Map<String, dynamic> errors = json['errors'];
          if (errors.containsKey('subject')) {
            showToastText(errors['subject']);
          }
          return false;
        }
      }

      return false;
    } catch (e) {
      debugPrint(e.toString());
      return false;
    }
  }
  // Future<bool> createTicket(
  //     {required String subject,
  //     required String description,
  //     required String date}) async {
  //   try {
  //     SharedPreferences shared = await SharedPreferences.getInstance();
  //     String? auth = shared.getString("access_token");
  //     String createdAt =
  //         formatDateFromString(date, "dd MMM yyyy", "yyyy-MM-dd");
  //     Uri uri = Uri.parse('$createTicketURL');
  //     var response = await http.post(
  //       uri,
  //       headers: {"Authorization": "Bearer $auth"},
  //       body: {
  //         'subject': subject,
  //         'description': description,
  //         'created_at': createdAt,
  //       },
  //     );
  //     if (response.statusCode == 200) {
  //       var json = jsonDecode(response.body);
  //       if (json['result'] == 'success') {
  //         await getTickets();
  //         notifyListeners();
  //         EasyLoading.showSuccess(json["message"]);
  //         return true;
  //       }

  //       if (json['result'] == 'failed') {
  //         Map<String, dynamic> errors = json['errors'];
  //         if (errors.containsKey('subject')) {
  //           showToastText(errors['subject']);
  //         }
  //         return false;
  //       }
  //     }

  //     return false;
  //   } catch (e) {
  //     debugPrint(e.toString());
  //     return false;
  //   }
  // }

  Future<bool> editAndupdateTicket(
      {required String subject,
      required int ticketId,
      required String description,
      required String date}) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString("access_token");
    // Map<String, dynamic> body = {
    //   "subject": subject,
    //   "description": description
    // };
    try {
      // var response = await http.patch(
      //     Uri.parse("${baseUrlHisense}edit_tickets/$ticketId/"),
      //     headers: {
      //       "Authorization": "Bearer $token",
      //     },
      //     body: body);
      var request = http.MultipartRequest(
          "PATCH", Uri.parse("${baseUrl}edit_tickets/$ticketId/"));
      request.headers['Authorization'] = "Bearer $token";
      request.fields["subject"] = subject;
      request.fields["description"] = description;
      request.fields['remove_img'] = isImageRemoved == true ? "True" : "False";

      if (imageFile != null) {
        var image =
            await http.MultipartFile.fromPath("ticket_img", imageFile!.path);
        request.files.add(image);
      }
      var response = await request.send();
      var responseData = await response.stream.toBytes();
      Map<String, dynamic> json = jsonDecode(utf8.decode(responseData));

      if (response.statusCode == 200) {
        if (json["result"] == "success") {
          if (json.containsKey("message")) {
            currentPage = 0;
            pagingController.refresh();
            pagingController.notifyListeners();
            showToastText(json["message"]);
          }
          notifyListeners();
        }
        return true;
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return false;
  }

  List<UserTicketsList>? _ticketList;
  List<UserTicketsList>? get ticketList => _ticketList;
  set ticketList(List<UserTicketsList>? value) {
    _ticketList = value;
    notifyListeners();
  }

  // Future<bool> getTickets() async {
  //   SharedPreferences shared = await SharedPreferences.getInstance();
  //   String? auth = shared.getString("access_token");
  //   http.Response response = await http.get(Uri.parse("$ticketListURL"),
  //       headers: {"Authorization": "Bearer $auth"});

  //   if (response.statusCode == 200) {
  //     Map<String, dynamic> json = jsonDecode(utf8.decode(response.bodyBytes));
  //     List data = json["data"];
  //     ticketList = data.map((e) => UserTicketsList.fromJson(e)).toList();
  //     notifyListeners();
  //     // _ticketsId = json["ticket_id"];
  //     return true;
  //   }
  //   return false;
  // }

  int currentPage = 0;
  late PagingController<int, UserTicketsList> pagingController;
  init() {
    currentPage = 0;
    pagingController = PagingController(firstPageKey: 1);
    pagingController.addPageRequestListener((pageKey) {
      getTicketsPage(pageKey);
    });
  }

  Future<void> getTicketsPage(int page) async {
    if (currentPage != page) {
      currentPage = page;

      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");
      http.Response response = await http.get(
          Uri.parse("$ticketListURL?limit=20&page=$page"),
          headers: {"Authorization": "Bearer $auth"});

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(utf8.decode(response.bodyBytes));
        // List data = json["data"];
        // ticketList = data.map((e) => UserTicketsList.fromJson(e)).toList();
        List<UserTicketsList> temp = [];
        temp = (json["data"] as List)
            .map((e) => UserTicketsList.fromJson(e))
            .toList();

        if (json["has_next"] == true) {
          int nextPage = page + 1;
          pagingController.appendPage(temp, nextPage);
        } else {
          pagingController.appendLastPage(temp);
        }

        // _ticketsId = json["ticket_id"];
        // return true;
      } else {
        // List<RecentActivityModel> temp = [];

        int nextPage = 0;
        pagingController.appendPage([], nextPage);
      }
      notifyListeners();
      // return false;
    }
  }

  TicketOverviewModel? _ticketOverview;
  TicketOverviewModel? get ticketOverview => _ticketOverview;
  set ticketOverview(TicketOverviewModel? value) {
    _ticketOverview = value;
    notifyListeners();
  }

  bool _isLoading = false;
  bool get isLoading => _isLoading;
  set isLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  Future<bool> getTicketOverview({required int ticketID}) async {
    isLoading = true;
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    http.Response response = await http.get(
        Uri.parse("$ticketOverviewURL$ticketID"),
        headers: {"Authorization": "Bearer $auth"});

    _ticketsId = null;

    if (response.statusCode == 200) {
      EasyLoading.dismiss();
      isLoading = false;
      Map<String, dynamic> json = jsonDecode(utf8.decode(response.bodyBytes));
      if (json["result"] == "success") {
        Map<String, dynamic> data = json["data"];

        ticketOverview = TicketOverviewModel.fromJson(data);
      } else {
        showToastText(json["message"]);
      }

      return true;
    } else {
      Map<String, dynamic> json = jsonDecode(utf8.decode(response.bodyBytes));
      showToastText(json["message"]);
    }
    EasyLoading.dismiss();
    isLoading = false;
    return false;
  }

  Future<void> deleteTicket({required int ticketID}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    http.Response response = await http.patch(
        Uri.parse("$ticketDeleteURL$ticketID/"),
        headers: {"Authorization": "Bearer $auth"});

    if (response.statusCode == 200) {
      var data = jsonDecode(response.body);
      if (data["result"] == "success") {
        currentPage = 0;
        pagingController.refresh();
        pagingController.notifyListeners();
        showToastText(data["message"]);
      }
    }
    notifyListeners();
  }

//////////////////

  XFile? _imageFile;
  XFile? get imageFile => _imageFile;
  set imageFile(XFile? value) {
    _imageFile = value;
    isImageRemoved = false;
    notifyListeners();
  }

  final _imgPicker = ImagePicker();
  Future<void> openCamera(BuildContext context) async {
    Navigator.pop(context);
    XFile? imgCamera = await _imgPicker.pickImage(
        source: ImageSource.camera,
        preferredCameraDevice: CameraDevice.front,
        imageQuality: 70,
        // maxHeight: 500,
        maxWidth: 1000);
    if (imgCamera != null) {
      CroppedFile? croppedImage = await cropImage(image: imgCamera);
      if (croppedImage != null) {
        imageFile = XFile(croppedImage.path);
        notifyListeners();
      }
    }
  }

  Future<void> openGallery() async {
    XFile? imgGallery = await _imgPicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 70,
        // maxHeight: 500,
        maxWidth: 1000);
    if (imgGallery != null) {
      CroppedFile? croppedImage = await cropImage(image: imgGallery);
      if (croppedImage != null) {
        final bytes = (await croppedImage.readAsBytes()).lengthInBytes;
        final kb = bytes / 1024;
        final mb = kb / 1024;

        if (mb <= 3) {
          imageFile = XFile(croppedImage.path);
        } else {
          showToastText("Please upload 2 mb file");
        }

        notifyListeners();
      }
    }
  }

  Future<CroppedFile?> cropImage({required XFile image}) async {
    return ImageCropper().cropImage(
      sourcePath: image.path,
      uiSettings: [
        AndroidUiSettings(
          statusBarColor: Colors.black,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false,
        ),
        IOSUiSettings(),
      ],
    );
  }
//////////////////////
}
