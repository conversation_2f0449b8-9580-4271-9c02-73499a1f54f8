import 'dart:convert';
import 'dart:typed_data';
import 'dart:ui';
import 'package:e8_hr_portal/model/badge_model.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:file_selector/file_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:http/http.dart' as http;
import 'package:http/http.dart';
import 'package:e8_hr_portal/services/secure_key_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BadgeProvider extends ChangeNotifier {
  bool isLoading = false;

  List<String> badgeFilterList = ['All', 'Locked', 'Unlocked'];

  String _badgeSelectedFilter = 'All';
  String get badgeSelectedFilter => _badgeSelectedFilter;
  set badgeSelectedFilter(String value) {
    _badgeSelectedFilter = value;
    notifyListeners();
  }

  XFile? _shareScreenShot;
  XFile? get shareScreenShot => _shareScreenShot;
  set shareScreenShot(XFile? value) {
    _shareScreenShot = value;
    notifyListeners();
  }

  Future<dynamic> bytesToImage(Uint8List imgBytes) async {
    var codec = await instantiateImageCodec(imgBytes);
    FrameInfo frame;
    try {
      frame = await codec.getNextFrame();
    } finally {
      codec.dispose();
    }
    return frame.image;
  }

  Future<Map<String, String>> _getApiHeader() async {
    Map<String, String> headers = {};

    // SECURITY FIX: Try secure storage first, fallback to SharedPreferences
    String? token = await SecureKeyManager.getAccessToken();

    if (token == null || token.isEmpty) {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      token = prefs.getString('access_token');
    }

    if (token != null && token.isNotEmpty) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  //This function for listing badges in profile
  List<BadgeModel> badgeModelInProfileList = [];
  Future<List<BadgeModel>> getBadgesInProfile() async {
    String apiURL = '${baseUrl}badge/?limit=4&action=all';
    Uri apiURI = Uri.parse(apiURL);
    var response = await http.get(apiURI, headers: await _getApiHeader());
    if (response.statusCode == 200) {
      var json = jsonDecode(response.body);
      if (json['result'] == 'success') {
        List records = json['records'] ?? [];
        badgeModelInProfileList =
            records.map((e) => BadgeModel.fromJson(e)).toList();
        notifyListeners();
        return records.map((e) => BadgeModel.fromJson(e)).toList();
      }
    }
    return [];
  }

  // List all badges in Bagdges screen
  List<BadgeModel> badgeModelList = [];
  Future<void> getBadgesWithFilter({required BuildContext context}) async {
    try {
      if (!isLoading) {
        isLoading = true;
        badgeModelList.clear();
        String apiURL =
            '${baseUrl}badge/?action=${badgeSelectedFilter.toLowerCase()}';
        Uri apiURI = Uri.parse(apiURL);

        var response = await http.get(apiURI, headers: await _getApiHeader());

        if (response.statusCode == 200) {
          Map<String, dynamic> json =
              jsonDecode(utf8.decode(response.bodyBytes));
          if (json['result'] == 'success') {
            List records = json['records'] ?? [];
            isLoading = false;
            badgeModelList =
                records.map((e) => BadgeModel.fromJson(e)).toList();
          }
        } else {
          EasyLoading.dismiss();
          isLoading = false;
        }
        notifyListeners();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    isLoading = false;
  }

  BadgeModel? badgeModel;
  Future<bool> getSingleBadge({required int id, required context}) async {
    try {
      EasyLoading.show();
      isLoading = true;
      String apiURL = '${baseUrl}get_badge/?badge_id=$id';
      Uri apiURI = Uri.parse(apiURL);
      Response response = await get(apiURI, headers: await _getApiHeader());
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          if (json.containsKey('records')) {
            badgeModel = BadgeModel.fromJson(json['records']);
            isLoading = false;
            EasyLoading.dismiss();
            return true;
          }
        }
      } else {
        EasyLoading.dismiss();
        isLoading = false;
        return false;
      }
      EasyLoading.dismiss();
      isLoading = false;
      return false;
    } catch (e) {
      EasyLoading.dismiss();
      isLoading = false;
      debugPrint(e.toString());
    }
    return false;
  }
}
