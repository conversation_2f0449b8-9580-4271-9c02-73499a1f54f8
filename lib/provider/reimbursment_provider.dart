// ignore_for_file: use_build_context_synchronously
import 'dart:convert';
import 'dart:developer';
import 'package:e8_hr_portal/model/department_model.dart';
import 'package:e8_hr_portal/model/reimbursment_data_model.dart';
import 'package:e8_hr_portal/model/reimbursment_details_model.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:file_picker/file_picker.dart' as file;
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:http/http.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/hr_reimbursement_model.dart';
import '../model/reimbursment_model.dart';

class ReimbursementProvider with ChangeNotifier {
  String _selectedFilter = 'All';
  String get selectedFilter => _selectedFilter;
  set selectedFilter(String value) {
    _selectedFilter = value;
    notifyListeners();
  }

  List<String> statusList = [
    'All',
    'Approved',
    'Pending',
    'Rejected',
    'In-Progress',
    'Cancelled'
  ];
  List<String> statusListForHR = ['All', 'Approved', 'Pending', 'Rejected'];
  DateTime? _startDate;
  DateTime? get startDate => _startDate;
  set startDate(DateTime? value) {
    _startDate = value;
    notifyListeners();
  }

  DateTime? _endDate;
  DateTime? get endDate => _endDate;
  set endDate(DateTime? value) {
    _endDate = value;
    notifyListeners();
  }

  ReimbursmentDropdownDataModel? _selectedTitle;
  ReimbursmentDropdownDataModel? get selectedTitle => _selectedTitle;
  set selectedTitle(ReimbursmentDropdownDataModel? value) {
    _selectedTitle = value;
    notifyListeners();
  }

  ReimbursmentDropdownDataModel? _selectedCurrency;
  ReimbursmentDropdownDataModel? get selectedCurrency => _selectedCurrency;
  set selectedCurrency(ReimbursmentDropdownDataModel? value) {
    _selectedCurrency = value;
    notifyListeners();
  }

  List<ReimbursmentDropdownDataModel> titleList = [];
  List<ReimbursmentDropdownDataModel> currencyList = [];
  Future<List<ReimbursmentDropdownDataModel>> getDropdownData(
      {required String action}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    var response = await get(
        Uri.parse('$fetchReimbursmentDropdownDataURL$action'),
        headers: {'Authorization': 'Bearer $auth'});
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      List<ReimbursmentDropdownDataModel> items = (data['records'] as List)
          .map((e) => ReimbursmentDropdownDataModel.fromJson(e))
          .toList();
      return items;
    }
    return [];
  }

  Future<void> createRequest(
      {required String description,
      required BuildContext context,
      required String amount}) async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    MultipartRequest request =
        MultipartRequest('POST', createReimbursmentRequestURL);
    request.headers['Authorization'] = 'Bearer $auth';
    request.fields['title'] = selectedTitle?.title ?? '';
    request.fields['description'] = description;
    request.fields['amount'] = amount;
    request.fields['currency'] = selectedCurrency?.id.toString() ?? '';
    request.fields['start_date'] =
        formatDateFromDate(dateTime: startDate!, format: 'yyyy-MM-dd');
    request.fields['end_date'] =
        formatDateFromDate(dateTime: endDate!, format: 'yyyy-MM-dd');
    for (var image in filesList) {
      var multipartFile = await getMultipartFile(image.path ?? '');
      request.files.add(multipartFile);
    }
    await request.send().then((value) async {
      var responseData = await value.stream.toBytes();
      var responseString = String.fromCharCodes(responseData);
      if (value.statusCode == 200) {
        Map<String, dynamic> response = jsonDecode(responseString);
        if (response['result'] == 'success') {
          showToastText(response['message']);
          fetchReimbursmentList();
        }
        Navigator.pop(context);
      }
    });
    EasyLoading.dismiss();
  }

  Future<MultipartFile> getMultipartFile(String path) async {
    return await MultipartFile.fromPath('attachment', path);
  }

  bool _showCreateRequestButton = false;
  bool get showCreateRequestButton => _showCreateRequestButton;
  set showCreateRequestButton(bool value) {
    _showCreateRequestButton = value;
    notifyListeners();
  }

  int? currency;
  List<ReimbursmentModel> reimbursmentList = [];
  Future<void> fetchReimbursmentList() async {
    EasyLoading.show(dismissOnTap: true);
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    log(auth.toString());
    // String? startDateForFilter;
    // String? endDateForFilter;
    // if (startDate != null) {
    //   startDateForFilter =
    //       formatDateFromDate(dateTime: startDate!, format: 'yyyy-MM-dd');
    // }
    // if (endDate != null) {
    //   endDateForFilter =
    //       formatDateFromDate(dateTime: endDate!, format: 'yyyy-MM-dd');
    // }
    String status = selectedFilter == 'All'
        ? ''
        : selectedFilter == 'In-Progress'
            ? 'inprogress'
            : selectedFilter.toLowerCase();
    Response response = await get(
        // Uri.parse(
        //     '$listReimbursmentUrl/?start_date=$startDateForFilter&end_date=$endDateForFilter'),

        Uri.parse('$listReimbursmentUrl/?status=$status'),
        headers: {'Authorization': 'Bearer $auth'});

    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      showCreateRequestButton = data['button_enable'];
      currency = data['user_currency'];
      reimbursmentList = (data['records'] as List)
          .map(
            (e) => ReimbursmentModel.fromJson(e),
          )
          .toList();
    } else {
      reimbursmentList.clear();
    }
    EasyLoading.dismiss();
    notifyListeners();
  }

  // removeImage({required int index, required bool isFile}) {
  //   if (isFile) {
  //     selectedImageList.removeAt(index);
  //   } else {

  //   }
  //   notifyListeners();
  // }

  ReimbursmentDetailsModel? reimbursmentDetailsModel;
  List<ReimbursementAttachements> reimbursementAttachements = [];

  Future<void> fetchReimbursmentDetails({required int? id}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    EasyLoading.show();
    Response response = await get(
      Uri.parse('$reimbursmentDetailsUrl$id/'),
      headers: {'Authorization': 'Bearer $auth'},
    );
    // print(response.request?.url.toString());
    // print(auth);
    log('fetchReimbursmentDetails - ${response.body} - ${response.request?.url.toString()} - ${auth}');
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      reimbursmentDetailsModel =
          ReimbursmentDetailsModel.fromJson((data['records']));
    } else {
      reimbursmentDetailsModel = null;
    }
    notifyListeners();
    EasyLoading.dismiss();
  }

  Future<void> cancelReimbursment() async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response response = await delete(
      Uri.parse('$reimbursmentCancelUrl${reimbursmentDetailsModel?.id}/'),
      headers: {'Authorization': 'Bearer $auth'},
    );
    if (response.statusCode == 200) {
      fetchReimbursmentDetails(id: reimbursmentDetailsModel?.id);
      fetchReimbursmentList();
    }
    EasyLoading.dismiss();
  }

  Future<void> editReimbursmentRequest(
      {required String description,
      required BuildContext context,
      required String amount}) async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    MultipartRequest request = MultipartRequest('PUT',
        Uri.parse('$reimbursmentEditUrl${reimbursmentDetailsModel?.id}/'));
    request.headers['Authorization'] = 'Bearer $auth';
    request.fields['title'] = selectedTitle?.title ?? '';
    request.fields['description'] = description;
    request.fields['amount'] = amount;
    request.fields['currency'] = selectedCurrency?.id.toString() ?? '';
    request.fields['start_date'] =
        formatDateFromDate(dateTime: startDate!, format: 'yyyy-MM-dd');
    request.fields['end_date'] =
        formatDateFromDate(dateTime: endDate!, format: 'yyyy-MM-dd');
    request.fields['deleted_images'] = deletedImagesIdList.join(',');
    for (var image in filesList) {
      var multipartFile = await getMultipartFile(image.path ?? '');
      request.files.add(multipartFile);
    }
    await request.send().then((value) async {
      var responseData = await value.stream.toBytes();
      var responseString = String.fromCharCodes(responseData);
      if (value.statusCode == 200) {
        Map<String, dynamic> response = jsonDecode(responseString);
        if (response['result'] == 'success') {
          showToastText(response['message']);
          fetchReimbursmentDetails(id: reimbursmentDetailsModel?.id);
          fetchReimbursmentList();
        }
        Navigator.pop(context);
      }
    });
    EasyLoading.dismiss();
  }

  String _searchKey = '';
  String get searchKey => _searchKey;
  set searchKey(String value) {
    _searchKey = value;
    notifyListeners();
  }

  List<ReimbursmentModel> reimbursementReqeustListForReportingPerson = [];
  Future<void> fetchReportingPersonRequestList() async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    String status = selectedFilter == 'All'
        ? ''
        : selectedFilter == 'In-Progress'
            ? 'inprogress'
            : selectedFilter.toLowerCase();
    Response response = await get(
      Uri.parse(
          '$reimbursmentRequestForReportingPersonUrl?name=$searchKey&status=$status&department=${selectedDepartment?.id ?? ''}&designation=${selectedDesignation?.id ?? ''}'),
      headers: {'Authorization': 'Bearer $auth'},
    );

    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      reimbursementReqeustListForReportingPerson = (data['records'] as List)
          .map((e) => ReimbursmentModel.fromJson(e))
          .toList();
    } else {
      reimbursementReqeustListForReportingPerson.clear();
    }

    EasyLoading.dismiss();
    notifyListeners();
  }

  Future<void> requestApproveOrReject(
      {required int id,
      required String action,
      required String comment}) async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response response = await post(
      Uri.parse('$reimbursementApproveOrReject$id'),
      body: {'action': action, 'comment': comment},
      headers: {'Authorization': 'Bearer $auth'},
    );
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      if (data['result'] == 'success') {
        showToastText(data['message']);
      }
      fetchReimbursmentDetails(id: id);
      fetchReportingPersonRequestList();
    }
    EasyLoading.dismiss();
  }

  DepartmentModel? _selectedDepartment;
  DepartmentModel? get selectedDepartment => _selectedDepartment;
  set selectedDepartment(DepartmentModel? value) {
    _selectedDepartment = value;
    notifyListeners();
  }

  List<DepartmentModel> departmentList = [];
  Future<void> fetchDepartmentList() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response response = await get(
      departmentListUrl,
      headers: {'Authorization': 'Bearer $auth'},
    );
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      departmentList = (data['records'] as List)
          .map((e) => DepartmentModel.fromJson(e))
          .toList();
    } else {
      departmentList.clear();
    }
    notifyListeners();
  }

  DepartmentModel? _selectedDesignation;
  DepartmentModel? get selectedDesignation => _selectedDesignation;
  set selectedDesignation(DepartmentModel? value) {
    _selectedDesignation = value;
    notifyListeners();
  }

  List<DepartmentModel> designationList = [];
  Future<void> fetchDesignationList() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response response = await get(
      designationListUrl,
      headers: {'Authorization': 'Bearer $auth'},
    );
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      designationList = (data['records'] as List)
          .map((e) => DepartmentModel.fromJson(e))
          .toList();
    } else {
      designationList.clear();
    }
    notifyListeners();
  }

  final _imgPicker = ImagePicker();
  Future<void> openGallery() async {
    List<XFile> pickedImage = await _imgPicker.pickMultiImage();
    if (pickedImage.isNotEmpty) {
      filesList.addAll(pickedImage
          .map(
            (e) => file.PlatformFile(name: e.name, size: 0, path: e.path),
          )
          .toList());
      notifyListeners();
    }
  }

  // List<File> selectedImageList = [];
  // Future<void> openGallery() async {
  //   List<XFile> pickedFiles = await _imgPicker.pickMultiImage();
  //   if (pickedFiles.isNotEmpty) {
  //     selectedImageList.addAll(pickedFiles.map(
  //       (e) => File(e.path),
  //     ));
  //     notifyListeners();
  //   }
  // }

  List<file.PlatformFile> filesList = [];

  List deletedImagesIdList = [];
  removeFile(int index, bool isFile) {
    if (isFile) {
      filesList.removeAt(index);
    } else {
      deletedImagesIdList.add(reimbursementAttachements[index].id);
      reimbursementAttachements.removeAt(index);
    }
    notifyListeners();
  }

  Future<void> openFile() async {
    file.FilePickerResult? result = await file.FilePicker.platform.pickFiles(
      type: file.FileType.custom,
      allowCompression: true,
      withData: true,
      allowedExtensions: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
    );
    if (result != null) {
      filesList.addAll(result.files);
      notifyListeners();
    }
  }

  List<HRReimbursementModel> reimbursementReqeustListForHR = [];
  Future<void> fetchHRRequestList() async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    String status = selectedFilter == 'All'
        ? ''
        : selectedFilter == 'In-Progress'
            ? 'inprogress'
            : selectedFilter.toLowerCase();
    Response response = await get(
      Uri.parse(
          '$reimbursementHRUrl?name=$searchKey&status=$status&department=${selectedDepartment?.id ?? ''}&designation=${selectedDesignation?.id ?? ''}'),
      headers: {'Authorization': 'Bearer $auth'},
    );
    log(response.request!.url.toString());
    log(response.body);
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      reimbursementReqeustListForHR = (data['records'] as List)
          .map(
            (e) => HRReimbursementModel.fromJson(e),
          )
          .toList();
    } else {
      reimbursementReqeustListForHR.clear();
    }
    notifyListeners();
    EasyLoading.dismiss();
  }

  Future<void> requestApproveOrRejectForHR(
      {required int id,
      required String action,
      required String comment}) async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    MultipartRequest request = MultipartRequest(
      'POST',
      Uri.parse('$reimbursementApproveOrRejectForHR$id'),
    );
    request.headers['Authorization'] = 'Bearer $auth';
    request.fields['action'] = action;
    request.fields['comment'] = comment;
    if (action == 'approved' && filesList.isNotEmpty) {
      for (var image in filesList) {
        var multipartFile = await getMultipartFile(image.path ?? '');
        request.files.add(multipartFile);
      }
    }
    print(request.fields);
    await request.send().then((value) async {
      var responseData = await value.stream.toBytes();
      var responseString = String.fromCharCodes(responseData);
      if (value.statusCode == 200) {
        Map<String, dynamic> response = jsonDecode(responseString);
        if (response['result'] == 'success') {
          showToastText(response['message']);
          fetchReimbursmentDetails(id: id);
          fetchHRRequestList();
        }
      }
    });

    EasyLoading.dismiss();
  }
}
