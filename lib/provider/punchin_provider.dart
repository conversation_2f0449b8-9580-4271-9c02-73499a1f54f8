// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:e8_hr_portal/model/location_model.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/provider/log_time_provider.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/view/ble_attendance/work_from_home_section/photo_confirmation_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../main.dart';
import '../util/urls.dart';

class PunchInProvider with ChangeNotifier {
  //location change request
  bool _isLocationRequestLoading = false;
  bool get isLocationRequestLoading => _isLocationRequestLoading;
  set isLocationRequestLoading(bool value) {
    _isLocationRequestLoading = value;
    notifyListeners();
  }

  Future<void> sentLocationChangeRequest({
    required String title,
    required String note,
    required String latitude,
    required String longitude,
    required BuildContext context,
  }) async {
    isLocationRequestLoading = true;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response response = await post(locationChangeRequestURL, headers: {
      'Authorization': 'Bearer $auth'
    }, body: {
      'latitude': latitude,
      'longitude': longitude,
      'location_name': title,
      'description': note
    });
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      showSnackBarMessage(context: context, msg: data['message']);
      getLocationRequests();
      Navigator.pop(context);
    } else if (response.statusCode == 400) {
      Map<String, dynamic> data = jsonDecode(response.body);
      if (data.containsKey('message')) {
        Map<String, dynamic> error = data['message'];
        if (error.containsKey('error')) {
          showSnackBarMessage(
              context: NavigationService.navigatorKey.currentContext ?? context,
              msg: error['error']);
        }
      }
    } else {
      showSnackBarMessage(context: context, msg: 'Something went wrong !!');
    }
    isLocationRequestLoading = false;
  }

//
//location requests list
  bool _isLocationListLoading = false;
  bool get isLocationListLoading => _isLocationListLoading;
  set isLocationListLoading(bool value) {
    _isLocationListLoading = value;
    notifyListeners();
  }

  List<LocationModel> locationRequestsList = [];
  Future<void> getLocationRequests() async {
    isLocationListLoading = true;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response response = await get(locationRequestListURL,
        headers: {'Authorization': 'Bearer $auth'});
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      if (data['result'].toString().toLowerCase() == 'success') {
        locationRequestsList = (data['data'] as List)
            .map((e) => LocationModel.fromJson(e))
            .toList();
      } else {
        locationRequestsList.clear();
      }
    } else {
      locationRequestsList.clear();
    }
    isLocationListLoading = false;
    notifyListeners();
  }

//

//location details
  bool _isLocationDetailsLoading = false;
  bool get isLocationDetailsLoading => _isLocationDetailsLoading;
  set isLocationDetailsLoading(bool value) {
    _isLocationDetailsLoading = value;
    notifyListeners();
  }

  LocationModel? locationDetail;
  Future<void> getLocationRequestDetails({required int locationID}) async {
    isLocationDetailsLoading = true;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Uri uri = Uri.parse('$locationRequestDetailsURL$locationID');
    Response response =
        await get(uri, headers: {'Authorization': 'Bearer $auth'});
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      locationDetail = LocationModel.fromJson(data['data']);
    }
    isLocationDetailsLoading = false;
    notifyListeners();
  }

//
//check location difference
  LocationStatus _isLocationMatching = LocationStatus.loading;
  LocationStatus get locationStatus => _isLocationMatching;
  set locationStatus(LocationStatus status) {
    _isLocationMatching = status;
    notifyListeners();
  }

  // checkLocationDifference
  String? errorMessage;
  Future<void> checkLocationMatching(
      {required double latitude, required double longitude}) async {
    locationStatus = LocationStatus.loading;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response response = await get(
        Uri.parse(
            '$locationCheckingURL?latitude=$latitude&longitude=$longitude'),
        headers: {'Authorization': 'Bearer $auth'});
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      if (data['result'] == 'success') {
        locationStatus = LocationStatus.success;
      } else {
        Map<String, dynamic> data = jsonDecode(response.body);
        errorMessage = data['message'];

        notifyListeners();
        locationStatus = LocationStatus.failed;
      }
    } else {
      locationStatus = LocationStatus.failed;
    }
  }

  Future<void> punchInPunchout(
      {String? action, required BuildContext context}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    try {
      Response response = await post(punchinpunchoutURL,
          headers: {'Authorization': 'Bearer $auth'}, body: {"action": action});

      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);
        if (data['result'] == 'success') {
          if (action == "punch-out") {
            log("insidei=n --punch out $action");
            var provider = context.read<LogTimeProvider>();
            log("insidei=n --punch out 2");
            await provider.userAllTaskStop();
            await provider.getUserTimeLogList(master: false);
            provider.userTimeLogReportSend();
          }
        } else {
          // Map<String, dynamic> data = jsonDecode(response.body);
        }
      } else {}

      notifyListeners();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  // checkLocationMatchingStatic() async {
  // await Future.delayed(const Duration(seconds: 2));
  double difference = Geolocator.distanceBetween(
      37.4219991, -122.0840011, 37.4219991, -122.0840012);
  // if (difference > 100) {
  //   locationStatus = LocationStatus.success;
  // } else {
  //   locationStatus = LocationStatus.failed;
  // }
  // }

//
//punch in punch out
  bool _punchinLoading = false;
  bool get punchinLoading => _punchinLoading;
  set punchinLoading(bool value) {
    _punchinLoading = value;
    notifyListeners();
  }

  Future<bool> wfhPunchinPunchout(
      {required File image,
      required BuildContext context,
      required String action}) async {
    try {
      punchinLoading = true;
      EasyLoading.show();
      Position position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.bestForNavigation);
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      MultipartRequest request = MultipartRequest('POST', wfhPunchinURL);
      request.headers['Authorization'] = 'Bearer $auth';
      request.files.add(await MultipartFile.fromPath('user_img', image.path));
      request.fields['latitude'] = position.latitude.toString();
      request.fields['longitude'] = position.longitude.toString();
      StreamedResponse response = await request.send();
      var responseData = await response.stream.toBytes();
      var responseString = String.fromCharCodes(responseData);
      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(responseString);
        if (data['result'] == 'success') {
          EasyLoading.dismiss();
          log("inside -- action $action-- ${LoginModel.email}");
          if (action == "punch-out") {
            log("inside --wfh punch out");
            var provider = context.read<LogTimeProvider>();
            await provider.userAllTaskStop();
            await provider.getUserTimeLogList(master: true);
            provider.userTimeLogReportSend();
          }
          showToastText(data['message'] ?? '');
          // showSnackBarMessage(
          //     context: NavigationService.navigatorKey.currentContext ?? context,
          //     msg: data['message']);
          return true;
        }
      } else if (response.statusCode == 400) {
        Map<String, dynamic> data = jsonDecode(responseString);
        if (data.containsKey('errors')) {
          Map<String, dynamic> error = data['errors'];
          if (error.containsKey('non_field_errors')) {
            // showSnackBarMessage(
            //     context:
            //         NavigationService.navigatorKey.currentContext ?? context,
            //     msg: error['non_field_errors']);
            showToastText(data['non_field_errors'] ?? '');
          }
        }
      }
      punchinLoading = false;
      EasyLoading.dismiss();
    } catch (e, stack) {
      EasyLoading.dismiss();
      debugPrint(e.toString());
      debugPrint(stack.toString());
    }
    return false;
  }

//
//pick image
  Future<void> pickImage(
      {required BuildContext context, required String action}) async {
    final ImagePicker picker = ImagePicker();
    XFile? pickedImage = await picker.pickImage(
        source: ImageSource.camera,
        preferredCameraDevice: CameraDevice.front,
        maxWidth: 1000,
        imageQuality: 70);
    if (pickedImage != null) {
      File image = File(pickedImage.path);

      Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => PhotoConfirmationScreen(image, action)));
    }
  }

//
}

enum LocationStatus { loading, success, failed }
