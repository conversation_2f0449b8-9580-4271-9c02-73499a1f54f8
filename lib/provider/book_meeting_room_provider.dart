// ignore_for_file: use_build_context_synchronously
import 'dart:convert';
import 'dart:developer';
import 'package:e8_hr_portal/model/employee_model.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:http/http.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/booked_room_status_model.dart';
import '../model/meeting_room_models/meeting_room_model.dart';
import '../util/urls.dart';
import '../view/meeting_room_hisence/meeting/widgets/meeting_booking_success_dialog.dart';
import 'package:dio/dio.dart' as dio;

class BookMeetingRoomProvider with ChangeNotifier {
  final dio.Dio _dio = dio.Dio();
  //checking meeting room availability
  MeetingRoomModel? _selectedRoomForCheckingAvailability;
  MeetingRoomModel? get selectedRoomForCheckingAvailability =>
      _selectedRoomForCheckingAvailability;
  set selectedRoomForCheckingAvailability(MeetingRoomModel? meetingRoom) {
    _selectedRoomForCheckingAvailability = meetingRoom;
    notifyListeners();
  }

  //
  //search employees section
  List<EmployeeModel> colleguesListToShowResult = [];
  void textFieldCleared() {
    colleguesListToShowResult = teamMembersList;
    notifyListeners();
  }

  void filter(String keyword) {
    if (keyword.isEmpty) {
      colleguesListToShowResult.clear();
      colleguesListToShowResult = teamMembersList;
      notifyListeners();
    } else {
      colleguesListToShowResult = teamMembersList
          .where(
              (user) => user.name!.toLowerCase().contains(keyword.toLowerCase())
              // ||
              // user.designation!.first.name!
              //     .toLowerCase()
              //     .contains(keyword.toLowerCase())
              )
          .toList();
      notifyListeners();
    }
  }

  //
//selected members section
  List<EmployeeModel> allSelectedMembers = [];
  addOrRemoveMembers(EmployeeModel member) {
    if (!allSelectedMembers.contains(member)) {
      allSelectedMembers.add(member);
    } else {
      allSelectedMembers.remove(member);
    }
    notifyListeners();
  }

  //
  //get all employees section
  List<EmployeeModel> teamMembersList = [];
  Future<void> getTeamMembers({required BuildContext context}) async {
    if (teamMembersList.isEmpty) {
      // EasyLoading.show();
    }
    SharedPreferences shared = await SharedPreferences.getInstance();
    Response response = await get(invitingPersonsListURL, headers: {
      'Authorization': 'Bearer ${shared.getString('access_token')}',
    });
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      teamMembersList =
          (data['data'] as List).map((e) => EmployeeModel.fromJson(e)).toList();
      teamMembersList.sort(((a, b) => a.name!.compareTo(b.name!)));
      colleguesListToShowResult = teamMembersList;
      EasyLoading.dismiss();
      notifyListeners();
    } else {
      colleguesListToShowResult.clear();
    }
    EasyLoading.dismiss();
  }

//
  //selecting date for booking section
  DateTime _selectedDate = DateTime.now();

  DateTime get selectedDate => _selectedDate;
  set selectedDate(DateTime date) {
    _selectedDate = date;
    notifyListeners();
  }

  DateTime bookingDateToDateTime(DateTime date) {
    return DateTime(date.year, date.month, date.day, selectedStartTime.hour,
        selectedStartTime.minute);
  }

  bool isValidDate(DateTime date) =>
      bookingDateToDateTime(date).isAfter(DateTime.now());
  //
  //selected time for booking section
  TimeOfDay _selectedStartTime = TimeOfDay.now();
  TimeOfDay get selectedStartTime => _selectedStartTime;
  set selectedStartTime(TimeOfDay time) {
    _selectedStartTime = time;
    notifyListeners();
  }

  TimeOfDay _selectedEndTime = TimeOfDay.now().add(hour: 1);
  TimeOfDay get selectedEndTime => _selectedEndTime;
  set selectedEndTime(TimeOfDay time) {
    _selectedEndTime = time;
    notifyListeners();
  }

  DateTime bookingTimetoDateTime(TimeOfDay time) {
    return DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
      time.hour,
      time.minute,
    );
  }

  bool isValidStartTime(TimeOfDay time) =>
      bookingTimetoDateTime(time).isAfter(DateTime.now());

  bool isValidEndTime(TimeOfDay time) => bookingTimetoDateTime(time)
      .isAfter(bookingTimetoDateTime(selectedStartTime));
  String formatTimeOfDayFormat(
      {required BuildContext context, required TimeOfDay time}) {
    final localizations = MaterialLocalizations.of(context);
    final formattedTimeOfDay = localizations.formatTimeOfDay(time);
    return formattedTimeOfDay;
  }

  Duration calculateTimeDifference(TimeOfDay startTime, TimeOfDay endTime) {
    final now = selectedDate;
    final startDateTime = DateTime(
        now.year, now.month, now.day, startTime.hour, startTime.minute);
    final endDateTime =
        DateTime(now.year, now.month, now.day, endTime.hour, endTime.minute);

    return endDateTime.difference(startDateTime);
  }

  //
  //book meeting room
  Future<void> bookMeetingRoom(
      {required String meetingRoomID,
      required String description,
      required BuildContext context}) async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    String startTime = formatTimeOfDay(selectedStartTime);
    String endTime = formatTimeOfDay(selectedEndTime);
    String bookingDate =
        formatDateFromDate(dateTime: selectedDate, format: 'yyyy-MM-dd');
    Response? response = await post(bookMeetingRoomURL, headers: {
      'Authorization': 'Bearer $auth'
    }, body: {
      'room': meetingRoomID,
      'booking_date': bookingDate,
      'start_time': startTime,
      'end_time': endTime,
      'description': description,
      'invited_users': allSelectedMembers.map((e) => e.id).toList().join(',')
    });
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      // showToastText(data['message']);
      EasyLoading.dismiss();
      await showDialog(
        context: context,
        builder: (context) {
          return MeetingBookingSuccessDialog(
            message: data['message'],
          );
        },
      );
      if (context.mounted) {
        Navigator.pop(context);
      }
    } else if (response.statusCode == 400) {
      EasyLoading.dismiss();
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      if (data.containsKey('invited_user')) {
        showToastText((data['invited_user'] as List).first.toString());
      } else if (data.containsKey('meeting_room')) {
        showToastText((data['meeting_room'] as List).first.toString());
      }
    }
    EasyLoading.dismiss();
  }

  //

  //edit booked meeting room section
  List<InvitedUser>? _alreadyInvitedMembersList;
  List<InvitedUser>? get alreadyInvitedMembersList =>
      _alreadyInvitedMembersList;
  set alreadyInvitedMembersList(List<InvitedUser>? userList) {
    _alreadyInvitedMembersList = userList;
    notifyListeners();
  }

  List<int> removedUsersIdList = [];
  removeInvitedMemeber(int userId) {
    removedUsersIdList.add(userId);
    alreadyInvitedMembersList
        ?.removeWhere((element) => element.userId == userId);

    notifyListeners();
  }

  Future<void> editBookedMeetingRoom(
      {required String bookingID,
      required String meetingRoomID,
      required String description,
      required BuildContext context}) async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    String startTime = formatTimeOfDay(selectedStartTime);
    String endTime = formatTimeOfDay(selectedEndTime);
    String bookingDate =
        formatDateFromDate(dateTime: selectedDate, format: 'yyyy-MM-dd');
    List<int?> finalList = allSelectedMembers.map((e) => e.id).toList();
    if (alreadyInvitedMembersList != null) {
      finalList
          .addAll(alreadyInvitedMembersList!.map((e) => e.userId).toList());
    }
    String invitedUsers = finalList.join(',');
    Response? response =
        await patch(Uri.parse('$editBookedMeetingRoomURL$bookingID'), headers: {
      'Authorization': 'Bearer $auth'
    }, body: {
      'room': meetingRoomID,
      'booking_date': bookingDate,
      'start_time': startTime,
      'end_time': endTime,
      'description': description,
      'invited_users': invitedUsers,
    });
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      // showToastText(data['message']);
      EasyLoading.dismiss();
      await showDialog(
        context: context,
        builder: (context) {
          return MeetingBookingSuccessDialog(
            message: data['message'],
          );
        },
      );
      if (context.mounted) {
        Navigator.pop(context);
      }
    } else if (response.statusCode == 400) {
      Map<String, dynamic> data = jsonDecode(response.body);
      if (data.containsKey('meeting_room')) {
        showToastText((data['meeting_room'] as List).first.toString());
      } else if (data.containsKey('invited_user')) {
        showToastText((data['invited_user'] as List).first.toString());
      }
    }
    EasyLoading.dismiss();
  }

  //
  //converting String time to TimeOfDay
  TimeOfDay convertStringToTimeOfDay(String timeString) {
    List<String> parts = timeString.split(' ');
    String timePart = parts[0];
    String ampmPart = parts[1];

    List<String> timeParts = timePart.split(':');
    int hours = int.parse(timeParts[0]);
    int minutes = int.parse(timeParts[1]);

    if (ampmPart == 'PM' && hours < 12) {
      hours += 12;
    } else if (ampmPart == 'AM' && hours == 12) {
      hours = 0;
    }
    return TimeOfDay(hour: hours, minute: minutes);
  }

  //
  //formating TimeOfDay to hh:mm:ss format
  String formatTimeOfDay(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    const second = '00';

    return '$hour:$minute:$second';
  }

//
//check availability of meeting room
  bool _roomIsAvailable = false;
  bool get roomIsAvailable => _roomIsAvailable;
  set roomIsAvailable(bool value) {
    _roomIsAvailable = value;
    notifyListeners();
  }

  bool _isCheckAvailabilityLoading = false;
  bool get isCheckAvailabilityLoading => _isCheckAvailabilityLoading;
  set isCheckAvailabilityLoading(bool value) {
    _isCheckAvailabilityLoading = value;
    notifyListeners();
  }

  String? _roomAvailabilityCheckResponse;
  String? get roomAvailabilityCheckResponse => _roomAvailabilityCheckResponse;
  set roomAvailabilityCheckResponse(String? value) {
    _roomAvailabilityCheckResponse = value;
    notifyListeners();
  }

  Future<void> checkAvailability({required BuildContext context}) async {
    isCheckAvailabilityLoading = true;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    int? meetingRoomId = selectedRoomForCheckingAvailability?.id;
    String startTime = formatTimeOfDay(selectedStartTime);
    String endTime = formatTimeOfDay(selectedEndTime);
    String bookingDate =
        formatDateFromDate(dateTime: selectedDate, format: 'yyyy-MM-dd');
    Response response = await get(
      Uri.parse(
          '${checkAvailabilityURL}meeting_room_id=$meetingRoomId&booking_date=$bookingDate&start_date=$startTime&end_date=$endTime'),
      headers: {'Authorization': 'Bearer $auth'},
    );
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      if (data['result'] == true) {
        roomAvailabilityCheckResponse = null;
        roomIsAvailable = true;
        showSnackBarMessage(context: context, msg: data['message']);
      } else {
        roomIsAvailable = false;
        roomAvailabilityCheckResponse = data['meeting_room'];
        showSnackBarMessage(context: context, msg: data['meeting_room']);
      }
    }
    isCheckAvailabilityLoading = false;
  }

//
//add or remove invitees of meeting room for admin section
  bool _isEditLoading = false;
  bool get isEditLoading => _isEditLoading;
  set isEditLoading(bool value) {
    _isEditLoading = value;
    notifyListeners();
  }

  Future<void> addOrRemoveInvitees(
      {required String bookingID, required BuildContext context}) async {
    isEditLoading = true;

    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Map<String, dynamic> body = {'booking_id': bookingID};
    if (allSelectedMembers.isNotEmpty) {
      body['new_invited_user_list'] =
          allSelectedMembers.map((e) => e.id).toList().join(',');
    }
    if (removedUsersIdList.isNotEmpty) {
      body['removed_invited_user_list'] = removedUsersIdList.join(',');
    }
    Response response = await patch(addOrRemoveInviteesForAdminURL,
        headers: {'Authorization': 'Bearer $auth'}, body: body);
    if (response.statusCode == 200) {
      isEditLoading = false;
      Map<String, dynamic> data = jsonDecode(response.body);
      await showSnackBarMessage(context: context, msg: data['message']);
      Navigator.pop(context);
    } else if (response.statusCode == 400) {
      isEditLoading = false;
      Map<String, dynamic> data = jsonDecode(response.body);
      if (data.containsKey('message')) {
        Map<String, dynamic> error = data['message'];
        if (error.containsKey('error')) {
          showSnackBarMessage(context: context, msg: error['error']);
        }
      }
    } else {
      showSnackBarMessage(context: context, msg: 'Something went wrong !!');
    }

    isEditLoading = false;
  }

//-------------------------------Reschedule Approved request-----------------------------------------------
  Future<bool> rescheduleApprovedMeetingRoom(
      {required int bookingID, String? reason}) async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      final bookingDate = DateFormat('yyyy-MM-dd').format(selectedDate);
      final now = DateTime.now();
      final startDateTime = DateTime(now.year, now.month, now.day,
          selectedStartTime.hour, selectedStartTime.minute);
      String formattedStartTime = DateFormat('HH:mm:ss').format(startDateTime);
      final endDateTime = DateTime(now.year, now.month, now.day,
          selectedEndTime.hour, selectedEndTime.minute);
      String formattedEndTime = DateFormat('HH:mm:ss').format(endDateTime);

      Map<String, dynamic> data = {};
      data['booking_id'] = bookingID;
      data['booking_date'] = bookingDate; //'2025-05-29';
      data['start_time'] = formattedStartTime; //11:00:00
      data['end_time'] = formattedEndTime; //15:00:00
      if (reason != null) {
        data['reason_for_reschedule'] = reason;
      }
      log('message ---- $data');
      final formData = dio.FormData.fromMap(data);
      EasyLoading.show();
      final response = await _dio.post(
        data: formData,
        '$bookingRescheduleUrl',
        options: dio.Options(
          headers: {'Authorization': 'Bearer $auth'},
          validateStatus: (status) => true,
        ),
      );
      EasyLoading.dismiss();
      log('rescheduleApprovedMeetingRoom -----${response.realUri}---> ${response.data} --- ${response.statusCode}');
      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          Map<String, dynamic> data = response.data;
          if (data.containsKey('message')) {
            showToastText(data['message']);
          }
          return true;
        }
        //removing expired meeting rooms
      } else if (response.statusCode == 400) {
        if (response.data['result'] == 'failure') {
          Map<String, dynamic> json = response.data;
          if (json.containsKey('message')) {
            if (json['message'] is String) {
              String message = json['message'] ?? '';
              showToastText(message);
            } else if (json['message'] is Map) {
              Map<String, dynamic> message = json['message'] ?? '';
              if (message.containsKey('booking_date')) {
                showToastText(message['booking_date']);
              }
              if (message.containsKey('booking_id')) {
                showToastText(message['booking_id']);
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint(e.toString());
      return false;
    } finally {
      EasyLoading.dismiss();
    }
    return false;
  }
//
}

//adding hour to timeofday datatype time
extension TimeOfDayExtension on TimeOfDay {
  TimeOfDay add({int hour = 0}) {
    return replacing(hour: this.hour + hour, minute: minute);
  }
}
//
