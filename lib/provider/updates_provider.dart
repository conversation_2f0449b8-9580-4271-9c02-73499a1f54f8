// ignore_for_file: use_build_context_synchronously
import 'dart:async';
import 'dart:convert';
import 'dart:io' as io;
import 'dart:io';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/model/view_vote_model.dart';
import 'package:e8_hr_portal/model/post_model.dart';
import 'package:e8_hr_portal/model/user_policies_model.dart';
import 'package:e8_hr_portal/provider/sign_in_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:http/http.dart';
import 'package:e8_hr_portal/services/secure_key_manager.dart';
import 'package:image_picker/image_picker.dart';
import 'package:e8_hr_portal/model/post_comments_model.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:video_player/video_player.dart';
// import '../../model/get_updates_model.dart' as d;
// import 'package:e8_hr_portal/model/post_details_model.dart' as d;

class UpdatesProvider extends ChangeNotifier {
  //check if admin or not

  //
  List<TextEditingController> textControllers = [
    TextEditingController(),
    TextEditingController()
  ];
  addTextControllers() {
    textControllers.add(TextEditingController());
    notifyListeners();
  }

  removeTextControllers(int index) {
    textControllers.removeAt(index);
    extraOptionNumber--;
    notifyListeners();
  }

  bool allowMultipleAnswers = false;
  changeMultipleAnswers() {
    allowMultipleAnswers = !allowMultipleAnswers;
    notifyListeners();
  }

  bool allowResultPublic = false;
  changeResultPublic() {
    allowResultPublic = !allowResultPublic;
    notifyListeners();
  }

  int extraOptionNumber = 0;
  addOption() {
    extraOptionNumber++;
    addTextControllers();
    notifyListeners();
  }

  String? _selectedPostType;
  String? get selectedPostType => _selectedPostType;
  set selectedPostType(String? type) {
    _selectedPostType = type;
    notifyListeners();
  }

  bool _isVideoRemoved = false;
  bool get isVideoRemoved => _isVideoRemoved;
  set isVideoRemoved(bool value) {
    _isVideoRemoved = value;
    notifyListeners();
  }

  List<int> removedImageIdList = [];
  void addRemoveImageID(int id) {
    removedImageIdList.add(id);

    notifyListeners();
  }

  List<PostFile>? _alreadyPostImageList = [];
  List<PostFile>? get alreadyPostImageList => _alreadyPostImageList;
  set alreadyPostImageList(List<PostFile>? list) {
    selectedPostsImages.clear();
    _alreadyPostImageList?.clear();
    _alreadyPostImageList = list;
    notifyListeners();
  }

  void removeAlreadySelectedImage(int index, BuildContext context) {
    addRemoveImageID(_alreadyPostImageList![index].id!.toInt());
    _alreadyPostImageList?.removeAt(index);
    notifyListeners();
    if (_alreadyPostImageList!.isEmpty) {
      Navigator.pop(context);
    }
  }

  void removeAlreadySelectedImageFromRecent(int index, BuildContext context) {
    addRemoveImageID(_alreadyPostImageListFromRecent![index].id!.toInt());
    _alreadyPostImageListFromRecent?.removeAt(index);
    notifyListeners();
    if (_alreadyPostImageListFromRecent!.isEmpty) {
      Navigator.pop(context);
    }
  }

  List<PostFile>? _alreadyPostImageListFromRecent = [];
  List<PostFile>? get alreadyPostImageListFromRecent =>
      _alreadyPostImageListFromRecent;
  set alreadyPostImageListFromRecent(List<PostFile>? list) {
    selectedPostsImages.clear();
    _alreadyPostImageListFromRecent?.clear();
    _alreadyPostImageListFromRecent = list;
    notifyListeners();
  }

  String? refreshToken;
  String? accessToken;
  bool noComments = false;
  bool commentsLoading = false;
  PostCommentsModel? commentsList;
  bool ispostUpdateLoading = false;
  bool isUpdatesLoading = false;
  List<PostModel> updatesList = [];
  bool updateInprogress = false;
  Future<void> getFromSharedPref() async {
    // SECURITY FIX: Try secure storage first, fallback to SharedPreferences
    accessToken = await SecureKeyManager.getAccessToken();
    refreshToken = await SecureKeyManager.getRefreshToken();

    if (accessToken == null || accessToken!.isEmpty) {
      SharedPreferences shared = await SharedPreferences.getInstance();
      accessToken = shared.getString("access_token");
      refreshToken = shared.getString("refresh_token");
    }

    notifyListeners();
  }

  bool postingImage = false;

  bool _isLoading = true;

  bool get isLoading => _isLoading;

  set isLoading(bool value) {
    _isLoading = value;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  int currentPage = 0;
  PagingController<int, PostModel>? pagingController;
  init(BuildContext context) {
    postLikeId.clear();
    currentPage = 0;
    pagingController = PagingController(firstPageKey: 1);
    pagingController?.addPageRequestListener((pageKey) {
      getUpdatePagination(page: pageKey, context: context, fromFun: false);
    });
  }

  Future<void> getUpdatePagination(
      {required int page,
      bool fromFun = false,
      required BuildContext context}) async {
    try {
      if (fromFun == false) {
        isUpdatesLoading = true;
      }
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');

      if (currentPage != page) {
        currentPage = page;

        var response = await get(Uri.parse("$getPostsURL?page=$page"),
            headers: {'Authorization': 'Bearer $auth'});

        if (response.statusCode == 200) {
          Map<String, dynamic> json =
              jsonDecode(utf8.decode(response.bodyBytes));
          if (json['result'] == 'success') {
            List records = json['records'] ?? [];

            scrollList.clear();
            scrollList = records.map((e) => PostModel.fromJson(e)).toList();

            for (var like in scrollList) {
              if (like.userLiked == true) {
                postLikeId.add(like.id);
              }
            }
            postLikeId.toSet().toList();

            List<PostModel> temp = (json["records"] as List)
                .map((e) => PostModel.fromJson(e))
                .toList();

            if (fromFun == false) {
              isUpdatesLoading = false;
            }
            if (json['has_next']) {
              int nextPage = page + 1;
              pagingController?.appendPage(temp, nextPage);
            } else {
              pagingController?.appendLastPage(temp);
            }
            notifyListeners();
          } else {
            int nextPage = 0;
            pagingController?.appendPage([], nextPage);
          }
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  List<PostModel> scrollList = [];
  List<PostModel> tempList = [];
  Future<void> getUpdates(
      {bool fromFun = false, required BuildContext context}) async {
    if (fromFun == false) {
      isUpdatesLoading = true;
    }
    // try {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    var response =
        await get(getPostsURL, headers: {'Authorization': 'Bearer $auth'});

    if (response.statusCode == 200) {
      Map<String, dynamic> json = jsonDecode(utf8.decode(response.bodyBytes));
      if (json['result'] == 'success') {
        List records = json['records'] ?? [];
        updatesList = records.map((e) => PostModel.fromJson(e)).toList();
        scrollList.clear();
        scrollList = updatesList.map((e) => e).toList();
        tempList.clear();
        tempList = scrollList.length > 3
            ? scrollList.sublist(0, 3).toList()
            : scrollList.toList();
        tempList = tempList.reversed.toList();
        if (fromFun == false) {
          isUpdatesLoading = false;
        }
        // for (var like in tempList) {
        //   if (like.userLiked == true) {
        //     postLikeId.add(like.id);
        //   }
        // }
        notifyListeners();
      }
    } else if (response.statusCode == 401) {
      context.read<SignInProvider>().updateToken(context: context);
    }
    if (fromFun == false) {
      isUpdatesLoading = false;
    }
    notifyListeners();
  }

  removeListScroll({required int index}) {
    tempList.removeAt(index);
    notifyListeners();
  }

  List<PostModel> postList = [];
  Future<void> getPostDetails({required int postId}) async {
    isUpdatesLoading = true;
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");
      var response = await get(Uri.parse("$getPostDetailsUrl$postId/"),
          headers: {"Authorization": "Bearer $auth"});

      // debugPrint("postDetails ${response.body}");

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(utf8.decode(response.bodyBytes));
        if (json['result'] == 'success') {
          List records = json['records'] ?? [];
          postList = records.map((e) => PostModel.fromJson(e)).toList();
          List location = [];
          if (postList.isNotEmpty) {
            location = postList.first.policy ?? [];
          }

          selectedUserPoliciesList.clear();
          selectedUserPoliciesListIds.clear();
          // getUserPolicies();

          if (userPoliciesModel != null) {
            selectedUserPoliciesList = userPoliciesModel!
                .where((e) => location.contains(e.id))
                .toList();
          }
        }
      } else {
        postList.clear();
      }
      isUpdatesLoading = false;
      notifyListeners();
    } catch (e) {
      isUpdatesLoading = false;
      notifyListeners();
    }
  }

  bool _isCreating = false;
  bool get isCreating => _isCreating;
  set isCreating(bool value) {
    _isCreating = value;
    notifyListeners();
  }

//////////////////////////////////////edit post///////////////////////////////
  Future<void> postEdit(
      {String? update, required context, required String postId}) async {
    EasyLoading.show();
    isCreating = true;

    // try {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    ispostUpdateLoading = true;
    var response =
        MultipartRequest("PUT", Uri.parse("${baseUrl}posts-update/$postId/"));
    response.headers["Authorization"] = "Bearer $auth";
    if (update != null) {
      response.fields["description"] = update;
    }
    // selectedPostsImages.forEach((element) { response.files["file"] = element.path });
    if (removedImageIdList.isNotEmpty) {
      response.fields["deleted_ids "] = removedImageIdList.join(",");
    }
    if (selectedPostsImages.isNotEmpty) {
      response.fields["file_type"] = "image";
      for (var file in selectedPostsImages) {
        var image = await MultipartFile.fromPath('file', file.path);

        response.files.add(image);
      }
    }
    if (selectedPostsVideo != null) {
      response.fields["file_type"] = "video";
      response.files
          .add(await MultipartFile.fromPath('file', selectedPostsVideo!.path));
    }
    if (LoginModel.isAdmin == true && selectedUserPoliciesList.isNotEmpty) {
      response.fields['policy'] =
          selectedUserPoliciesList.map((e) => e.id).join(',');
    }

    await response.send().then((value) async {
      var responseData = await value.stream.toBytes();
      var responseString = String.fromCharCodes(responseData);

      ispostUpdateLoading = false;

      if (value.statusCode == 200) {
        EasyLoading.dismiss();
        ispostUpdateLoading = false;
        showSnackBarMessage(
            context: context, msg: 'Post updated successfully!');
        currentPage = 0;
        pagingController?.refresh();
        pagingController?.notifyListeners();
        getUpdates(context: context);

        Navigator.pop(context);
      } else {
        EasyLoading.dismiss();
        isCreating = false;
        ispostUpdateLoading = false;
        Map<String, dynamic> data = jsonDecode(responseString);

        if (data.containsKey("erros")) {
          Map<String, dynamic> errors = data['erros'];

          if (errors.containsKey("file")) {
            showSnackBarMessage(
                context: context, msg: errors['file'].toString());
          }
        } else {
          showSnackBarMessage(context: context, msg: 'Failed');
        }
      }
    });
    EasyLoading.dismiss();
    isCreating = false;
    notifyListeners();
    // } catch (e) {
    //   ispostUpdateLoading = false;
    //   notifyListeners();
    // }
  }

  //////////////////////////////////
  Future<void> postCreate({
    String? update,
    required context,
  }) async {
    EasyLoading.show();
    isCreating = true;
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      notifyListeners();
    });

    // try {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    ispostUpdateLoading = true;
    var response = MultipartRequest("POST", postCreateURL);
    response.headers["Authorization"] = "Bearer $auth";
    response.headers["Content-Type"] = "application/json";
    if (update != null) {
      response.fields["description"] = update;
    }
    // selectedPostsImages.forEach((element) { response.files["file"] = element.path });

    if (selectedPostsImages.isNotEmpty) {
      response.fields["file_type"] = "image";
      for (var file in selectedPostsImages) {
        var image = await MultipartFile.fromPath('file', file.path);

        response.files.add(image);
      }
    }
    if (selectedPostsVideo != null) {
      response.fields["file_type"] = "video";
      response.files
          .add(await MultipartFile.fromPath('file', selectedPostsVideo!.path));
    }
    if (LoginModel.isAdmin == true && selectedUserPoliciesListIds.isNotEmpty) {
      // response.fields['policy'] = jsonEncode(selectedUserPoliciesListIds);
      response.fields['policy'] =
          selectedUserPoliciesListIds.map((e) => e).join(',');
    }

    await response.send().then((value) async {
      var responseData = await value.stream.toBytes();
      var responseString = String.fromCharCodes(responseData);

      ispostUpdateLoading = false;

      if (value.statusCode == 200) {
        EasyLoading.dismiss();
        ispostUpdateLoading = false;
        showSnackBarMessage(
            context: context, msg: 'Post updated successfully!');
        currentPage = 0;
        pagingController?.refresh();
        pagingController?.notifyListeners();
        getUpdates(context: context);
        Navigator.pop(context);
      } else {
        EasyLoading.dismiss();
        ispostUpdateLoading = false;
        Map<String, dynamic> data = jsonDecode(responseString);
        if (data.containsKey("erros")) {
          Map<String, dynamic> error = data['erros'];
          if (error.containsKey('file')) {
            showSnackBarMessage(
                context: context, msg: error['file'].toString());
          } else {
            showSnackBarMessage(context: context, msg: 'Failed');
          }
        }
      }
    });
    EasyLoading.dismiss();
    isCreating = false;
    notifyListeners();
    // } catch (e) {
    //   ispostUpdateLoading = false;
    //   notifyListeners();
    // }
  }

  List<XFile> _selectedPostsImages = [];
  List<XFile> get selectedPostsImages => _selectedPostsImages;
  set selectedPostsImages(List<XFile> value) {
    _selectedPostsImages = value;
    notifyListeners();
  }

  XFile? _selectedPostsVideo;
  XFile? get selectedPostsVideo => _selectedPostsVideo;
  set selectedPostsVideo(XFile? value) {
    _selectedPostsVideo = value;

    notifyListeners();
  }

  void removeVideo() {
    _selectedPostsVideo = null;
    notifyListeners();
  }

  void removeSelectedImage(BuildContext context, index) {
    _selectedPostsImages.removeAt(index);
    notifyListeners();
    if (_selectedPostsImages.isEmpty) {
      Navigator.pop(context);
    }
  }

  removeSelectedPosts() {
    selectedPostsImages.clear();
    selectedPostsVideo = null;
    notifyListeners();
  }

  Future<void> addImages(BuildContext context) async {
    final picker = ImagePicker();

    List<XFile>? picked = await picker.pickMultiImage(imageQuality: 100);
    if (picked.isNotEmpty) {
      if ((_selectedPostsImages.length +
              picked.length +
              alreadyPostImageList!.length) <
          6) {
        _selectedPostsImages.addAll(picked);
      } else {
        showToastText("Can't upload more than 5 images");
      }

      notifyListeners();
    }
    notifyListeners();
  }

  VideoPlayerController? _videoPlayerController;
  VideoPlayerController? get videoPlayerController => _videoPlayerController;
  set videoPlayerController(VideoPlayerController? value) {
    _videoPlayerController = value;
    notifyListeners();
  }

  Future<void> addVideos(BuildContext context) async {
    final pickerVideo = ImagePicker();
    XFile? pickedVideos =
        await pickerVideo.pickVideo(source: ImageSource.gallery);

    if (pickedVideos != null) {
      if (File(pickedVideos.path).readAsBytesSync().lengthInBytes > 12097152) {
        showToastText("The size should be less than 12 MB");
      } else {
        _selectedPostsVideo = pickedVideos;
        videoPlayerController =
            VideoPlayerController.file(io.File(_selectedPostsVideo!.path));

        videoPlayerController!.addListener(() {});
        videoPlayerController!.setLooping(true);
        videoPlayerController!.initialize();
        // videoPlayerController!.initialize().then((_) => setState(() {}));
        videoPlayerController!.pause();
        notifyListeners();
      }
    }
    notifyListeners();
  }

  // Future<CroppedFile?> cropImage(List<XFile?> sourcePath) async {
  //   return await ImageCropper().cropImage(
  //     sourcePath:
  //     aspectRatioPresets: [
  //       CropAspectRatioPreset.square,
  //       CropAspectRatioPreset.ratio3x2,
  //       CropAspectRatioPreset.original,
  //       CropAspectRatioPreset.ratio4x3,
  //       CropAspectRatioPreset.ratio16x9
  //     ],
  //   );
  // }

  Future<void> getComments({required int postId, bool frmFun = false}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    // try {
    if (frmFun == false) {
      commentsLoading = true;
    }
    var response = await get(Uri.parse("$getCommentsURL$postId/"),
        headers: {"Authorization": "Bearer $auth"});
    if (response.statusCode == 200) {
      var data = jsonDecode(utf8.decode(response.bodyBytes));
      if (data["result"] == "success") {
        noComments = false;
        commentsList = PostCommentsModel.fromJson(data);
        // commentsList?.records?.first.comments?.sort(
        //   (b, a) => DateTime.parse(a.createdAt.toString())
        //       .compareTo(DateTime.parse(b.createdAt.toString())),
        // );
        notifyListeners();
      }
      if (data["status"] == false) {
        noComments = true;
        notifyListeners();
      }
      notifyListeners();
    }
    if (frmFun == false) {
      commentsLoading = false;
    }
    notifyListeners();
    // } catch (e) {
    //   commentsLoading = false;
    //   notifyListeners();
    // }
  }

  Future<void> postComment(
      {required int postId,
      required String comment,
      required BuildContext context}) async {
    await EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString("access_token");
    var response = await post(Uri.parse("$createCommentURL$postId/"),
        headers: {"Authorization": "Bearer $auth"},
        body: {"description": comment.toString()});

    if (response.statusCode == 200) {
      await getComments(postId: postId);
      // await getPostDetails(postId: postId);
      // await getUpdates(context: context);
    }
    await EasyLoading.dismiss();
  }

  List<int?> _postLikeId = [];
  List<int?> get postLikeId => _postLikeId;
  set postLikeId(List<int?> value) {
    _postLikeId = value;
    notifyListeners();
  }

  Future<void> likePost(
      {required String postId, required context, required bool like}) async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString("access_token");
      var response = await post(Uri.parse("$likePostUrl$postId/"),
          headers: {
            "Authorization": "Bearer $auth",
            'Content-Type': 'application/json'
          },
          body: jsonEncode({"like": like}));

      if (response.statusCode == 200) {
        // if (data["records"]["count"] == 0) {
        //   _postLikeId.remove(postId);
        // }
        await getUpdates(fromFun: true, context: context);
        // await getComments(postId: int.parse(postId), frmFun: true);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  Future<void> likeComment(
      {required String commentId,
      required context,
      required bool like,
      required String postId}) async {
    // try {

    var response = await post(Uri.parse("$likePostUrl$commentId/"),
        headers: {
          "Authorization": "Bearer $accessToken",
          'Content-Type': 'application/json'
        },
        body: jsonEncode({"like": like}));

    if (response.statusCode == 200) {
      await getComments(postId: int.parse(postId));
    }
    // } catch (e) {
    //   debugPrint(e.toString());
    // }
  }

  Future<void> deleteComment(
      {required String commentId,
      required int postId,
      required context}) async {
    await EasyLoading.show();
    var response = await delete(
      Uri.parse("$commentsDeleteURL$commentId/"),
      headers: {"Authorization": "Bearer $accessToken"},
    );
    if (response.statusCode == 200) {
      EasyLoading.dismiss();
      await getComments(postId: postId);
      // await getPostDetails(postId: postId);
      // await getUpdates(context: context);
    }
    EasyLoading.dismiss();
  }

  Future<void> deleteUpdate(
      {required String? postId, required BuildContext context}) async {
    EasyLoading.show();
    var response = await delete(
      Uri.parse("$postDeleteURL$postId/"),
      headers: {"Authorization": "Bearer $accessToken"},
    );

    if (response.statusCode == 200) {
      EasyLoading.dismiss();
      Navigator.pop(context);
      showSnackBarMessage(context: context, msg: "Update deleted successfully");
      currentPage = 0;
      pagingController?.refresh();
      pagingController?.notifyListeners();
      await getUpdates(context: context);
    }
    EasyLoading.dismiss();
  }

  Future<void> deleteUpdateFromRecent(
      {required String? postId, required BuildContext context}) async {
    EasyLoading.show();
    var response = await delete(
      Uri.parse("$postDeleteURL$postId/"),
      headers: {"Authorization": "Bearer $accessToken"},
    );

    if (response.statusCode == 200) {
      EasyLoading.dismiss();
      currentPage = 0;
      pagingController?.refresh();
      pagingController?.notifyListeners();
      await getUpdates(context: context);
    }
    EasyLoading.dismiss();
  }

  int? _selectedUserPolicies;
  int? get selectedUserPolicies => _selectedUserPolicies;
  set selectedUserPolicies(int? value) {
    _selectedUserPolicies = value;
    notifyListeners();
  }

  bool _validationSub = false;
  bool get validationSub => _validationSub;
  set validationSub(bool value) {
    _validationSub = value;
    notifyListeners();
  }

  List<int>? selectedpolicies = [];
  // void selectedpolices() async {}

  List<UserPoliciesModel> selectedUserPoliciesList = [];
  List<int> selectedUserPoliciesListIds = [];
  void changeSelectedSubCategory(UserPoliciesModel model) {
    if (selectedUserPoliciesList.contains(model)) {
      selectedUserPoliciesList.remove(model);
      selectedUserPoliciesListIds.remove(model.id!);
    } else {
      selectedUserPoliciesList.add(model);
      selectedUserPoliciesListIds.add(model.id!);
    }

    if (selectedUserPoliciesList.isEmpty) {
      validationSub = true;
    } else {
      validationSub = false;
    }

    notifyListeners();
  }

  void selectPoliciesList() {
    if (selectedUserPoliciesList.length == userPoliciesModel?.length) {
      selectedUserPoliciesList.clear();
      selectedUserPoliciesListIds.clear();
    } else {
      selectedUserPoliciesList.clear();
      selectedUserPoliciesList.addAll(userPoliciesModel!);
      for (var data in selectedUserPoliciesList) {
        selectedUserPoliciesListIds.add(data.id!);
      }
    }

    if (selectedUserPoliciesList.isEmpty) {
      validationSub = true;
    } else {
      validationSub = false;
    }

    notifyListeners();
  }

  void clearids() {
    selectedUserPoliciesList.clear();
    selectedUserPoliciesListIds.clear();
    notifyListeners();
  }

  List<UserPoliciesModel>? userPoliciesModel;

  Future<void> getUserPolicies() async {
    var response = await get(
      userPoliciesURL,
      headers: {'Authorization': 'Bearer $accessToken'},
    );

    var data = jsonDecode(utf8.decode(response.bodyBytes));
    if (response.statusCode == 200) {
      userPoliciesModel = (data['data'] as List)
          .map((e) => UserPoliciesModel.fromJson(e))
          .toList();
    }
    notifyListeners();
  }

  bool _isCreatingSurveyLoading = false;
  bool get isCreatingSurveyLoading => _isCreatingSurveyLoading;
  set isCreatingSurveyLoading(bool value) {
    _isCreatingSurveyLoading = value;
    notifyListeners();
  }

  Future<void> createSurvey({
    required String qustion,
    required BuildContext context,
    required List<TextEditingController> options,
  }) async {
    isCreatingSurveyLoading = true;
    Map<String, dynamic> dynamicMap = {};
    for (int i = 0; i < options.length; i++) {
      dynamicMap['option${i + 1}'] = options[i].text;
    }
    String body = jsonEncode({
      'question': qustion,
      'options': dynamicMap,
      'allow_multiple_answer': allowMultipleAnswers,
      'allow_result_public': allowResultPublic,
      'policy': selectedUserPoliciesListIds.join(',')
    });
    Response response = await post(surveyCreateUrl,
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
        body: body);

    Map<String, dynamic> data = jsonDecode(response.body);
    if (data['result'] == 'success') {
      showSnackBarMessage(context: context, msg: data['message']);
      currentPage = 0;
      pagingController?.refresh();
      pagingController?.notifyListeners();
      getUpdates(context: context);
      Navigator.pop(context);
    } else {
      if (data.containsKey('message')) {
        showSnackBarMessage(context: context, msg: data['message']);
      }
      // showSnackBarMessage(context: context, msg: 'Something went wrong!!');
    }
    isCreatingSurveyLoading = false;
  }

  Future<void> surveyVote(
      {required String surveyID,
      required String optionID,
      required BuildContext context}) async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response? response = await post(surveyVoteURL,
        headers: {'Authorization': 'Bearer $auth'},
        body: {'survey_id': surveyID, 'option_id': optionID});

    if (response.statusCode == 200) {
      currentPage = 0;
      pagingController?.refresh();
      pagingController?.notifyListeners();
      getUpdates(context: context);
      // showSnackBarMessage(context: context, msg: data['message']);
    } else {
      // showSnackBarMessage(context: context, msg: 'Something went wrong !!!');
    }
    EasyLoading.dismiss();
  }

  bool _isViewVoteLoading = false;
  bool get isViewVoteLoading => _isViewVoteLoading;
  set isViewVoteLoading(bool value) {
    _isViewVoteLoading = value;
    notifyListeners();
  }

  ViewVoteModel? viewVote;
  Future<void> getVotedDetails({required int surveyID}) async {
    isViewVoteLoading = true;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response? response = await get(
      Uri.parse('$viewVoteURL$surveyID'),
      headers: {'Authorization': 'Bearer $auth'},
    );

    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      viewVote = ViewVoteModel.fromJson(data['records']);
    }
    notifyListeners();
    isViewVoteLoading = false;
  }

  Future<void> deleteSurvey(
      {required int surveyId, required BuildContext context}) async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response? response = await delete(Uri.parse('$deleteSurveyURL$surveyId'),
        headers: {'Authorization': 'Bearer $auth'});
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(utf8.decode(response.bodyBytes));
      showSnackBarMessage(context: context, msg: data['message']);
      currentPage = 0;
      pagingController?.refresh();
      pagingController?.notifyListeners();
      getUpdates(context: context);
    } else {
      showSnackBarMessage(context: context, msg: 'Something went wrong!!!');
    }
    EasyLoading.dismiss();
  }
}
