import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/model/user_model.dart';

class UserProvider extends ChangeNotifier {
  List<UserModel?> userModel = [];

  Future<bool> getUserDetails(String? email) async {
    late bool checkEmail;
    await FirebaseFirestore.instance
        .collection("users")
        .where("email", isEqualTo: email)
        .get()
        .then((snapshot) {
      checkEmail = snapshot.docs.isEmpty;
    });
    return checkEmail;
  }

  List<String> designationList = [];
  getDesignations() async {
    await FirebaseFirestore.instance
        .collection('designation')
        .get()
        .then((value) {
      designationList =
          value.docs.map((e) => e.get('title').toString()).toList();
      notifyListeners();
    });
  }
}
