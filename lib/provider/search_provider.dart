import 'dart:convert';
import 'package:e8_hr_portal/model/team_member_model.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SearchProvider extends ChangeNotifier {
  List<RecentSearch> _recentSearchList = [];
  List<RecentSearch> get recentSearchList => _recentSearchList;
  set recentSearchList(List<RecentSearch> list) {
    _recentSearchList = list;
    notifyListeners();
  }

  Future<void> addInRecentSearchList(TeamMemberModel? employee) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> tempRecentSearchList =
        prefs.getStringList('recent-search') ?? [];
    Map<String, dynamic> json = {};
    json['id'] = employee!.id;
    json['name'] = employee.name;
    json['image'] = employee.profilePic;

    try {
      json['designation'] = employee.designation?.first.name;
    } catch (e) {
      json['designation'] = '';
    }
    bool isExist = false;
    if (tempRecentSearchList.isNotEmpty) {
      List tempList = tempRecentSearchList
          .where((e) => jsonDecode(e)['id'] == employee.id)
          .toList();
      isExist = tempList.isNotEmpty;
    }
    if (!isExist) {
      tempRecentSearchList.add(jsonEncode(json));
      prefs.setStringList('recent-search', tempRecentSearchList);
    }

    fetchRecentSearchList();
  }

  Future<void> fetchRecentSearchList() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> tempRecentSearchList =
        prefs.getStringList('recent-search') ?? [];
    recentSearchList = tempRecentSearchList
        .map((e) => RecentSearch.fromJson(jsonDecode(e)))
        .toList();
  }

  Future<void> deleteSearch({required int? id}) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> tempRecentSearchList =
        prefs.getStringList('recent-search') ?? [];
    tempRecentSearchList.removeWhere((e) => jsonDecode(e)['id'] == id);
    prefs.setStringList('recent-search', tempRecentSearchList);
    fetchRecentSearchList();
  }

  Future<void> clearSearch() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    prefs.setStringList('recent-search', []);

    fetchRecentSearchList();
  }
}

class RecentSearch {
  int? id;
  String? name;
  String? image;
  String? designation;

  RecentSearch({this.id, this.name, this.image, this.designation});

  RecentSearch.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
    designation = json['designation'];
  }
}
