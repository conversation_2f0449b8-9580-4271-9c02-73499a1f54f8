// ignore_for_file: unused_import, unused_local_variable, use_build_context_synchronously
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:e8_hr_portal/model/emp_responsibility_model.dart' as res;
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/login_model.dart';
import 'package:e8_hr_portal/util/api_headers.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:e8_hr_portal/view/sign_in/login_with_email_screen.dart';
import 'package:http/http.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:e8_hr_portal/model/designation_model.dart';
import 'package:e8_hr_portal/model/logged_in_user.dart';
import 'package:e8_hr_portal/model/user_detailes_model.dart';
import 'package:e8_hr_portal/model/user_model.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:e8_hr_portal/provider/sign_in_provider.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/provider/user_provider.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/view/profile/confirm_profile_photo.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ProfileProvider extends ChangeNotifier {
  String? userRole;
  bool isUploadingProfilePhoto = false;
  int? userId;
  String? userName;
  UserDetailesModel? userDetailesModel;
  String? profilePic;
  String? firstName;
  String? designation;
  int? uid;
  String? employeeId;

  File? profilePhoto;
  Future<void> changeProfilePicture(BuildContext context) async {
    final picker = ImagePicker();
    final pickedFile =
        await picker.pickImage(source: ImageSource.gallery, imageQuality: 100);
    if (pickedFile != null) {
      profilePhoto = File(pickedFile.path);
      CroppedFile? tempFile = await cropImage(pickedFile.path);

      if (tempFile != null) {
        File? croppedFile = File(tempFile.path);
        profilePhoto = File(croppedFile.path);
        PageNavigator.push(
          context: context,
          route: ConfirmProfilePhoto(profilePhoto!),
        );
      }
    }
  }

  Future<CroppedFile?> cropImage(String sourcePath) async {
    return await ImageCropper().cropImage(sourcePath: sourcePath);
  }

  bool? _isLoading = false;
  bool? get isLoading => _isLoading;
  set isLoading(bool? value) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _isLoading = value;
      notifyListeners();
    });
  }

  bool _isChangeIcon = false;
  bool get isChangeIcon => _isChangeIcon;
  set isChangeIcon(bool value) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _isChangeIcon = value;
      notifyListeners();
    });
  }

  String? _userStatus;
  String? get userStatus => _userStatus;
  set userStatus(String? value) {
    _userStatus = value;
    notifyListeners();
  }

  Future<void> getProfileData({required BuildContext context}) async {
    isLoading = true;

    SharedPreferences shared = await SharedPreferences.getInstance();
    String? email = shared.getString("email");

    // try {
    var response = await get(profileView, headers: {
      'Authorization': 'Bearer ${shared.getString('access_token')}',
    });
    log('getProfileData -- ${jsonDecode(response.body)} , - ${response.request} - ${shared.getString('access_token')}');
    if (response.statusCode == 200) {
      var json = jsonDecode(response.body);
      log('getProfileData - $json');
      userDetailesModel = UserDetailesModel.fromJson(json["data"]);
      userName = userDetailesModel?.firstName;
      userId = userDetailesModel?.id;

      profilePic = userDetailesModel?.profileDetails?.profilePic;
      firstName =
          "${userDetailesModel?.firstName} ${userDetailesModel?.lastName}";
      designation = userDetailesModel?.profileDetails?.designation;
      uid = userDetailesModel?.id;
      employeeId = userDetailesModel?.profileDetails?.employeeId;
      userStatus = userDetailesModel?.profileDetails?.empStatus?.name;
      LoginModel.saveMeetingRoomPermission(
          userDetailesModel?.extraPermission?.meetingRoom);

      shared.setString('name', firstName.toString());
      isLoading = false;
      notifyListeners();
    } else if (response.statusCode == 401) {
      context.read<SignInProvider>().updateToken(context: context);
      // EasyLoading.dismiss();
      // PageNavigator.pushAndRemoveUntil(
      //   context: context,
      //   route: const LoginWithEmailScreen(),
      // );
    }

    isLoading = false;
    notifyListeners();
  }

  Future<void> editProfile(
      {required context,
      required String firstName,
      required String lastName,
      required String number,
      required String dob,
      required String email,
      required String dateOfJoin}) async {
    String? token =
        Provider.of<UpdatesProvider>(context, listen: false).accessToken;
    var body = {
      "first_name": firstName.toString(),
      "last_name": lastName,
      "contact_number": number,
      "date_of_birth": dob,
      "email": email,
      "date_of_joining": dateOfJoin,
    };

    // try {
    var response = await patch(editProfileURL,
        // Uri.parse("https://workflow-dev.e8demo.com/api/edit_profile/"),
        headers: {
          "Authorization": "Bearer $token",
        },
        body: body);
    if (response.statusCode == 200) {
      showSnackBarMessage(context: context, msg: "Successfully updated");
      await getProfileData(context: context);
      Navigator.of(context).pop();
    }
    // } catch (e) {
    //   print(e.toString());
    // }
    notifyListeners();
  }

  void updateProfilePic({required File? image, required context}) async {
    try {
      isUploadingProfilePhoto = true;
      notifyListeners();
      String? token =
          Provider.of<UpdatesProvider>(context, listen: false).accessToken;
      var response = MultipartRequest("PATCH", editProfileURL);
      response.headers["Authorization"] = "Bearer $token";
      if (image != null) {
        response.files
            .add(await MultipartFile.fromPath('profile_pic', image.path));
      }
      // response.fields['email'] = "<EMAIL>";
      response.send().then((value) async {
        var data = await value.stream.toBytes();
        var body = String.fromCharCodes(data);
        Map<String, dynamic> msg = jsonDecode(body);
        if (msg["result"] == "success") {
          isUploadingProfilePhoto = false;
          getProfileData(context: context);
          showSnackBarMessage(
              context: context, msg: "Profile picture successfully updated");
          Navigator.pop(context);
        } else {
          isUploadingProfilePhoto = false;
          if (msg.containsKey('message')) {
            Map<String, dynamic> error = msg['message'];
            if (error.containsKey("errors")) {
              Map<String, dynamic> temp = error["errors"];
              showSnackBarMessage(
                  context: context, msg: temp['non_field_errors']);
            }
          }
        }
        notifyListeners();
      });
    } catch (e) {
      isUploadingProfilePhoto = false;
      notifyListeners();
      debugPrint(e.toString());
    }
  }

  SelectResposnible _selectResposnible = SelectResposnible.daily;
  SelectResposnible get selectResposnible => _selectResposnible;
  set selectResposnible(SelectResposnible type) {
    _selectResposnible = type;
    notifyListeners();
  }

  List<res.Data>? dailyItems = [];
  List<res.Data>? weeklyItems = [];
  List<res.Data>? monthlyItems = [];
  res.EmpResponsiblityModel? empResponsiblityModel;
  bool isEmpResponsibilityLoading = false;
  Future<bool> getEmpResponsilbility({required BuildContext context}) async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      var response = await get(empResponsiblityURL, headers: {
        'Authorization': 'Bearer ${shared.getString('access_token')}',
      });
      var data = jsonDecode(utf8.decode(response.bodyBytes));
      dailyItems?.clear();
      weeklyItems?.clear();
      monthlyItems?.clear();
      if (response.statusCode == 200) {
        empResponsiblityModel = res.EmpResponsiblityModel.fromJson(data);

        for (var data in empResponsiblityModel!.data!) {
          if (data.type?.toLowerCase() == "daily") {
            dailyItems?.add(data);
          } else if (data.type?.toLowerCase() == "weekly") {
            weeklyItems?.add(data);
          } else if (data.type?.toLowerCase() == "monthly") {
            monthlyItems?.add(data);
          }
        }
        isEmpResponsibilityLoading = false;
        return true;
      } else {
        if (data.containsKey('message')) {
          if (context.mounted) {
            showSnackBarMessage(context: context, msg: data['message']);
          }
        }
        isEmpResponsibilityLoading = false;
        return false;
      }
    } catch (e) {
      debugPrint(e.toString());
      isEmpResponsibilityLoading = false;
      return false;
    }
  }
}

enum SelectResposnible { daily, weekly, monthly }
