// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:e8_hr_portal/model/department_model.dart';
import 'package:e8_hr_portal/model/designation_models.dart';
import 'package:e8_hr_portal/model/flight_ticket_approved_list.dart';
import 'package:e8_hr_portal/model/flight_ticket_count_model.dart';
import 'package:e8_hr_portal/model/flight_ticket_leve_model.dart';
import 'package:e8_hr_portal/model/flight_ticket_overview_model.dart';
import 'package:e8_hr_portal/model/flight_ticket_request_model.dart';
import 'package:e8_hr_portal/view/flight_tickets/flight_ticket_new_request.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/flight_ticket_personal_info.dart';
import 'package:e8_hr_portal/model/flight_tickets_list.dart';
import 'package:e8_hr_portal/model/listing_country_model.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../model/airline_model.dart';
import '../util/dailoge.dart';
import '../util/urls.dart';

class FlightTicketProvider extends ChangeNotifier {
  final int _selectedWay = 2;
  int get selectedWay => _selectedWay;
  // set selectedWay(int value) {
  //   _selectedWay = value;
  //   notifyListeners();
  // }

  List<int> _uniqueIds = [];
  List<int> get uniqueIds => _uniqueIds;
  set uniqueIds(List<int>? value) {
    _uniqueIds = value!;
    notifyListeners();
  }

  bool _isStateSeeMore = false;
  bool get isStateSeeMore => _isStateSeeMore;
  set isStateSeeMore(bool value) {
    _isStateSeeMore = value;
    notifyListeners();
  }

  void seeMore({required int index}) {
    notifyListeners();
  }

  final String _selectedWayText = 'Two Way';
  String get selectedWayText => _selectedWayText;
  // set selectedWayText(String value) {
  //   _selectedWayText = value;
  //   notifyListeners();
  // }

  String? _selectedAirline;
  String? get selectedAirline => _selectedAirline;
  set selectedAirline(String? value) {
    _selectedAirline = value;
    notifyListeners();
  }

  DateTime _selectedFromDate =
      DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);
  DateTime get selectedFromDate => _selectedFromDate;
  set selectedFromDate(DateTime value) {
    _selectedFromDate = value;
    notifyListeners();
  }

  DateTime _selectedToDate =
      DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);
  DateTime get selectedToDate => _selectedToDate;
  set selectedToDate(DateTime value) {
    _selectedToDate = value;
    notifyListeners();
  }

  Future<void> selectFromDate({required BuildContext context}) async {
    DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.now(),
        firstDate: DateTime.now(),
        lastDate:
            //  selectedToDate ==
            // DateTime(
            // DateTime.now().year, DateTime.now().month, DateTime.now().day)
            // ?
            DateTime(2050)
        // : selectedToDate,
        );
    if (picked != null) {
      selectedFromDate = picked;
      selectedToDate = picked;
      _calculateDaysBetween();
    }
    notifyListeners();
  }

  Future<void> selectToDate({required BuildContext context}) async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedToDate,
      firstDate: selectedFromDate,
      lastDate: DateTime(3100),
    );

    if (picked != null) {
      selectedToDate = picked;

      _calculateDaysBetween();
    }
    notifyListeners();
  }

  int _daysBetween = 0;
  int get daysBetween => _daysBetween;
  set daysBetween(int value) {
    _daysBetween = value;
    notifyListeners();
  }

  int _daysBetweenPlus = 0;
  int get daysBetweenPlus => _daysBetweenPlus;
  set daysBetweenPlus(int value) {
    _daysBetweenPlus = value;
    notifyListeners();
  }

  void _calculateDaysBetween() {
    daysBetween = 0;
    daysBetweenPlus = 0;
    final duration = selectedToDate.difference(selectedFromDate);
    daysBetween = duration.inDays;
    daysBetweenPlus = daysBetween + 1;
    flightDaysController.text = daysBetweenPlus.toString();
    notifyListeners();
  }

  FlightTicketPersonalInfo? _flightTicketPersonalInfo;
  FlightTicketPersonalInfo? get flightTicketPersonalInfo =>
      _flightTicketPersonalInfo;
  Future<bool> getFlightTicketPersonalInfo({
    int? userId,
  }) async {
    if (_flightTicketPersonalInfo == null) {
      EasyLoading.show();
    }
    // selectedFlightTicketLeaveModel = null;
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Uri url;
    if (userId != null) {
      url = Uri.parse('$flightTicketPersonalInfoURL&user_id=$userId');
    } else {
      url = flightTicketPersonalInfoURL;
    }
    try {
      var response =
          await http.get(url, headers: {'Authorization': 'Bearer $auth'});

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          if (json.containsKey('records')) {
            _flightTicketPersonalInfo =
                FlightTicketPersonalInfo.fromJson(json['records']);

            EasyLoading.dismiss();
            notifyListeners();
            return true;
          }
        }
      }

      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint(e.toString());
    }
    return false;
  }

  FlightTicketLeaveModel? _selectedFlightTicketLeaveModel;
  FlightTicketLeaveModel? get selectedFlightTicketLeaveModel =>
      _selectedFlightTicketLeaveModel;
  set selectedFlightTicketLeaveModel(FlightTicketLeaveModel? value) {
    _selectedFlightTicketLeaveModel = value;
    notifyListeners();
  }

  int _selectedNumberOfCount = 1;
  int get selectedNumberOfCount => _selectedNumberOfCount;
  set selectedNumberOfCount(int value) {
    _selectedNumberOfCount = value;
    notifyListeners();
  }

  List<FlightTicketLeaveModel> flightTicketLeaveModel = [];
  Future<void> getFlightTicketLeveList() async {
    // try {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    var response = await http.get(flightTicketLeveListURL,
        headers: {'Authorization': 'Bearer $auth'});
    log('getFlightTicketLeveList - ${response.body} - ${response.request} - ${auth}');
    if (response.statusCode == 200) {
      Map<String, dynamic> json = jsonDecode(response.body);

      if (json['result'] == 'success') {
        flightTicketLeaveModel = (json['data'] as List)
            .map((e) => FlightTicketLeaveModel.fromJson(e))
            .toList();
      }
    } else {
      flightTicketLeaveModel.clear();
    }

    EasyLoading.dismiss();
    // } catch (e) {
    //   EasyLoading.dismiss();
    //   debugPrint(e.toString());
    // }
    notifyListeners();
  }

  List<int> numberOfTicketCount = [];
  FlightTicketCountModel? flightTicketCountModel;
  Future<void> getFlightTicketCount({int? userId}) async {
    try {
      numberOfTicketCount.clear();
      selectedNumberOfCount = 1;
      EasyLoading.show();
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');

      Uri url;

      if (userId != null) {
        url = Uri.parse('$flightTicketCoutURL?user_id=$userId');
      } else {
        url = flightTicketCoutURL;
      }

      var response =
          await http.get(url, headers: {'Authorization': 'Bearer $auth'});

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);

        if (json['result'] == 'success') {
          flightTicketCountModel = FlightTicketCountModel.fromJson(json);
          if (flightTicketCountModel?.data != null) {
            for (int i = 1;
                i <= flightTicketCountModel!.data!.balanceTicket!;
                i++) {
              numberOfTicketCount.add(i);
            }
            if (flightTicketCountModel!.data!.balanceTicket! <= 1) {
              numberOfTicketCount.clear();
              numberOfTicketCount = [1, 2, 3, 4, 5, 6];
            }
            if (flightTicketCountModel!.data!.balanceTicket == 0) {
              numberOfTicketCount.clear();
              numberOfTicketCount = [1, 2, 3, 4, 5, 6];
            }
          }
        } else {
          numberOfTicketCount.clear();
        }
      }

      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  Future<bool> flightTicketCreate({
    required BuildContext context,
    required String airline,
    required String departureDate,
    required String returnDate,
    required String cityOfDeparture,
    required String countryOfArrival,
    required String countryOfDeparture,
    required String cityOfArrival,
    required String remarks,
    required String type,
  }) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');

    var body = {
      'airline': airline,
      'deperature_date': departureDate,
      'return_date': returnDate,
      'city_of_deperature': cityOfDeparture,
      'country_of_arrival': countryOfArrival,
      'country_of_departure': countryOfDeparture,
      'remark': remarks,
      'city_of_arrival': cityOfArrival,
      'type': type,
      'remaining_flight_ticket':
          ((flightTicketCountModel?.data?.balanceTicket ?? 0) -
                  selectedNumberOfCount)
              .toString(),
      'leave_request': selectedFlightTicketLeaveModel?.id.toString(),
      'extra_ticket': selectedNumberOfCount.toString(),
    };
    var response = await http.post(flightTicketCreateURL,
        body: body, headers: {'Authorization': 'Bearer $auth'});
    if (response.statusCode == 200) {
      Map<String?, dynamic> json = jsonDecode(response.body);

      if (json['result'] == 'success') {
        currentPage = 0;
        pagingController?.refresh();
        if (json.containsKey('message')) {
          showToastText(json['message']);
        }
        return true;
      } else {
        showToastText(json['erros']['error'].toString());
      }
    }
    if (response.statusCode == 400) {
      Map<String?, dynamic> json = jsonDecode(response.body);
      if (json['result'] == 'failure') {
        if (json.containsKey('erros')) {
          Map<String, dynamic> erros = json['erros'];
          if (erros.containsKey('request')) {
            showToastText(erros['request']);
          }
          if (erros.containsKey('error')) {
            showToastText(erros['error']);
          }
          if (erros.containsKey('return_date')) {
            showToastText(erros['return_date']);
          } else if (erros.containsKey('profile')) {
            showToastText(erros['profile']);
          } else if (erros.containsKey('level_perms')) {
            showToastText(erros['level_perms']);
          }
        }
        return false;
      }
    }
    return false;
  }

  Future<bool> flightTicketEdit({
    required String ticketId,
    required BuildContext context,
    required String airline,
    required String departureDate,
    required String returnDate,
    required String cityOfDeparture,
    required String countryOfArrival,
    required String countryOfDeparture,
    required String cityOfArrival,
    required String remarks,
    required String type,
  }) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');

    var body = {
      'airline': airline,
      'deperature_date': departureDate,
      'return_date': returnDate,
      'city_of_deperature': cityOfDeparture,
      'country_of_arrival': countryOfArrival,
      'country_of_departure': countryOfDeparture,
      'remark': remarks,
      'city_of_arrival': cityOfArrival,
      'type': type,
      'remaining_flight_ticket':
          ((flightTicketCountModel?.data?.balanceTicket ?? 0) -
                  selectedNumberOfCount)
              .toString(),
      'leave_request': selectedFlightTicketLeaveModel?.id.toString(),
      'extra_ticket': selectedNumberOfCount.toString(),
    };

    var response = await http.patch(
        Uri.parse('$flightTicketEditURL?ticket_id=$ticketId'),
        body: body,
        headers: {'Authorization': 'Bearer $auth'});
    if (response.statusCode == 200) {
      Map<String?, dynamic> json = jsonDecode(response.body);

      if (json['result'] == 'success') {
        currentPage = 0;
        pagingController?.refresh();
        if (json.containsKey('message')) {
          showToastText(json['message']);
        }
        return true;
      }
    }
    if (response.statusCode == 400) {
      Map<String?, dynamic> json = jsonDecode(response.body);
      if (json['result'] == 'failure') {
        if (json.containsKey('erros')) {
          Map<String, dynamic> erros = json['erros'];
          if (erros.containsKey('request')) {
            showToastText(erros['request']);
          }
          if (erros.containsKey('return_date')) {
            showToastText(erros['return_date']);
          } else if (erros.containsKey('profile')) {
            showToastText(erros['profile']);
          }
        }
        return false;
      }
    }
    return false;
  }

  List<FlightTicketsList> _flightTicketList = [];
  List<FlightTicketsList> get flightTicketList => _flightTicketList;
  set flightTicketList(List<FlightTicketsList> value) {
    _flightTicketList = value;
    notifyListeners();
  }

  List<FlightTicketRequstListModel> _flightTicketListRequest = [];
  List<FlightTicketRequstListModel> get flightTicketListRequest =>
      _flightTicketListRequest;
  set flightTicketListRequest(List<FlightTicketRequstListModel> value) {
    _flightTicketListRequest = value;
    notifyListeners();
  }

  int currentPage = 0;
  PagingController<int, FlightTicketsList>? pagingController;
  init() {
    currentPage = 0;
    pagingController = PagingController(firstPageKey: 1);
    pagingController?.addPageRequestListener((pageKey) {
      getFlightTicketList(pageKey);
    });
  }

  Future<bool> getFlightTicketList(int page) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    try {
      if (currentPage != page) {
        currentPage = page;
        var response = await http.get(flightTicketListURL,
            headers: {'Authorization': 'Bearer $auth'});

        if (response.statusCode == 200) {
          Map<String, dynamic> json = jsonDecode(response.body);
          if (json['result'] == 'success') {
            if (json.containsKey('records')) {
              List records = json['records'];
              List<FlightTicketsList> temp = [];
              flightTicketList =
                  records.map((e) => FlightTicketsList.fromJson(e)).toList();
              temp = records.map((e) => FlightTicketsList.fromJson(e)).toList();

              if (json['has_next']) {
                final nextPage = page + 1;
                pagingController?.appendPage(temp, nextPage);
              } else {
                pagingController?.appendLastPage(temp);
              }
              notifyListeners();
              return true;
            }
          }
        } else {
          int nextPage = 0;
          pagingController?.appendPage([], nextPage);
        }
      }
      notifyListeners();
    } catch (e) {
      debugPrint(e.toString());
    }
    return false;
  }

  int currentPageFlightTicketRequest = 0;
  PagingController<int, FlightTicketRequstListModel>?
      flightTicketRequestPagingController;
  initRequest() {
    currentPageFlightTicketRequest = 0;
    flightTicketRequestPagingController = PagingController(firstPageKey: 1);
    flightTicketRequestPagingController?.addPageRequestListener((pageKey) {
      getFlightTicketListRequest(pageKey);
    });
  }

  Future<bool> getFlightTicketListRequest(int page) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    try {
      if (currentPageFlightTicketRequest != page) {
        currentPageFlightTicketRequest = page;
        var response = await http.get(flightTicketRequestListURL,
            headers: {'Authorization': 'Bearer $auth'});
        if (response.statusCode == 200) {
          Map<String, dynamic> json = jsonDecode(response.body);
          if (json['result'] == 'success') {
            if (json.containsKey('entries')) {
              List records = json['entries'];
              List<FlightTicketRequstListModel> temps = [];
              flightTicketListRequest = records
                  .map((e) => FlightTicketRequstListModel.fromJson(e))
                  .toList();
              temps = records
                  .map((e) => FlightTicketRequstListModel.fromJson(e))
                  .toList();

              if (json['has_next']) {
                final nextPage = page + 1;
                flightTicketRequestPagingController?.appendPage(
                    temps, nextPage);
              } else {
                flightTicketRequestPagingController?.appendLastPage(temps);
              }
              notifyListeners();
              return true;
            }
          }
        } else {
          int nextPage = 0;
          flightTicketRequestPagingController?.appendPage([], nextPage);
        }
      }
      notifyListeners();
    } catch (e) {
      debugPrint(e.toString());
    }
    return false;
  }

  int currentPageFlightTicketApproved = 0;
  PagingController<int, FlightTicketApprovedListModel>?
      flightTicketApprovedPagingController;
  initApproved() {
    currentPageFlightTicketApproved = 0;
    flightTicketApprovedPagingController = PagingController(firstPageKey: 1);
    flightTicketApprovedPagingController?.addPageRequestListener((pageKey) {
      getFlightTicketListApproved(pageKey);
    });
  }

  String? _searchFlightTicketKeyword;
  String? get searchFlightTicketKeyword => _searchFlightTicketKeyword;
  set searchFlightTicketKeyword(String? value) {
    _searchFlightTicketKeyword = value;
    notifyListeners();
  }

  Future<bool> getFlightTicketListApproved(int page) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    try {
      if (currentPageFlightTicketApproved != page) {
        currentPageFlightTicketApproved = page;
        // Uri url = Uri();

        StringBuffer url = StringBuffer(flightTicketApprovedListURL);

        if (selectedDepartment != null && selectedDepartment?.id != 0) {
          url.write('?department=${selectedDepartment!.id}');
        }
        if (selectedDesignation != null && selectedDesignation?.id != 0) {
          url.write(selectedDepartment != null ? '&' : '?');
          url.write('designation=${selectedDesignation!.id}');
        }
        if (searchFlightTicketKeyword != null) {
          url.write(((selectedDepartment != null &&
                      selectedDepartment?.id != 0) ||
                  (selectedDesignation != null && selectedDesignation?.id != 0))
              ? '&'
              : '?');
          url.write('search=$searchFlightTicketKeyword');
        }

        var response = await http.get(Uri.parse(url.toString()),
            headers: {'Authorization': 'Bearer $auth'});

        if (response.statusCode == 200) {
          Map<String, dynamic> json = jsonDecode(response.body);
          if (json['result'] == 'success') {
            if (json.containsKey('entries')) {
              List records = json['entries'];
              List<FlightTicketApprovedListModel> temps = [];

              temps = records
                  .map((e) => FlightTicketApprovedListModel.fromJson(e))
                  .toList();

              if (json['has_next']) {
                final nextPage = page + 1;
                flightTicketApprovedPagingController?.appendPage(
                    temps, nextPage);
              } else {
                flightTicketApprovedPagingController?.appendLastPage(temps);
              }
              notifyListeners();
              return true;
            }
          }
        } else {
          int nextPage = 0;
          flightTicketApprovedPagingController?.appendPage([], nextPage);
        }
      }
      notifyListeners();
    } catch (e) {
      debugPrint(e.toString());
    }
    return false;
  }

  List<AirlineModel> _airlineList = [];
  List<AirlineModel> get airlineList => _airlineList;
  set airlineList(List<AirlineModel> value) {
    _airlineList = value;
    notifyListeners();
  }

  Future<bool> getAirlineList() async {
    try {
      var response = await http.get(
        airlineListURL,
      );

      if (response.statusCode == 200) {
        List json = jsonDecode(response.body);
        airlineList.clear();
        airlineList = json.map((e) => AirlineModel.fromJson(e)).toList();
        notifyListeners();
        return true;
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return false;
  }

  List<ListingCountryModel> temp = [];
  List<String> allCountries = [];
  Future<void> getAllCountries() async {
    // if (allCountries.isEmpty) {
    //   // EasyLoading.show();
    // }
    http.Response response =
        await http.get(Uri.parse('${baseUrl}countries_list'));
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      List records = data['data'];
      temp = records.map((e) => ListingCountryModel.fromJson(e)).toList();
      allCountries = temp.map((e) => e.name.toString()).toList();
      notifyListeners();
    }
    notifyListeners();
    // EasyLoading.dismiss();
  }

  FlightTicketOverviewModel? flightTicketOverviewModel;
  Future<void> getFlightTicketOverview({required String ticketId}) async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      var response = await http.get(
          Uri.parse('$flightTicketDetailsURL?tkt_id=$ticketId'),
          headers: {'Authorization': 'Bearer $auth'});

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          flightTicketOverviewModel = FlightTicketOverviewModel.fromJson(json);
        }
      } else {
        flightTicketOverviewModel = null;
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  Future<void> getFlightTicketEdit({required String ticketId}) async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      var response = await http.patch(
          Uri.parse('$flightTicketEditURL?ticket_id=$ticketId'),
          headers: {'Authorization': 'Bearer $auth'});
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['result'] == 'success') {}
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  Future<void> cancelRequest(
      {required String ticketId, required BuildContext context}) async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      Map<String, dynamic> body = {
        'action': 'flight_tickets',
        'action_id': ticketId,
      };
      var response = await http.put(flightTicketCancelURL,
          headers: {'Authorization': 'Bearer $auth'}, body: body);

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          showToastText(json['message']);
          currentPage = 0;
          pagingController?.refresh();
          Navigator.pop(context);
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  Future<void> flightTciketApproveReject({
    required int ticketId,
    required String action,
    required String? comment,
  }) async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      Map<String, dynamic> body = {
        'action': action,
        'comment': comment,
      };
      var response = await http.post(
        Uri.parse('$flightTicketApproveRejectURL?ticket_id=$ticketId'),
        headers: {'Authorization': 'Bearer $auth'},
        body: body,
      );

      Map<String, dynamic> json = jsonDecode(response.body);
      if (response.statusCode == 200) {
        if (json['result'] == 'success') {
          showToastText(json['message']);
          currentPageFlightTicketRequest = 0;
          flightTicketRequestPagingController?.refresh();
        }
      } else {
        showToastText(json['error']['status']);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  XFile? _uploadFileImage;
  XFile? get uploadFileImage => _uploadFileImage;
  set uploadFileImage(XFile? value) {
    _uploadFileImage = value;
    notifyListeners();
  }

  XFile? _croppedImageFile;
  XFile? get croppedImageFile => _croppedImageFile;
  set croppedImageFile(XFile? value) {
    _croppedImageFile = value;
    notifyListeners();
  }

  Future<void> getFlightTicketUploadQuotation({
    required int ticketId,
    required String title,
  }) async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      var request = http.MultipartRequest('POST',
          Uri.parse('$flightTicketUploadQuotationURL?ticket_id=$ticketId'));

      request.headers['Authorization'] = 'Bearer $auth';
      request.fields['action'] = 'upload';
      request.fields['title'] = title;
      request.fields['file_type'] = '1';
      if (uploadFileImage != null) {
        var image =
            await http.MultipartFile.fromPath('file', uploadFileImage!.path);
        request.files.add(image);
        quotationsFile.add(uploadFileImage);
      } else if (pickedFile != null) {
        var image = await http.MultipartFile.fromPath('file', pickedFile!.path);
        request.files.add(image);
        quotationsFile.add(pickedFile);
      }

      var response = await request.send();
      if (response.statusCode == 200) {
        getFlightTicketOverview(ticketId: ticketId.toString());
        uploadFileImage = null;
        pickedFile = null;
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  Future<void> getFlightTicketUploadTicket({
    required int ticketId,
    required String title,
  }) async {
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      var request = http.MultipartRequest('POST',
          Uri.parse('$flightTicketUploadTciketURL?ticket_id=$ticketId'));

      request.headers['Authorization'] = 'Bearer $auth';
      // request.fields['action'] = 'upload';
      request.fields['title'] = title;
      request.fields['file_type'] = '2';
      if (uploadFileImage != null) {
        var image =
            await http.MultipartFile.fromPath('file', uploadFileImage!.path);
        request.files.add(image);
        quotationsFile.add(uploadFileImage);
      } else if (pickedFile != null) {
        var image = await http.MultipartFile.fromPath('file', pickedFile!.path);
        request.files.add(image);
        quotationsFile.add(pickedFile);
      }

      var response = await request.send();
      var responseData = await response.stream.toBytes();
      Map<String, dynamic> json = jsonDecode(utf8.decode(responseData));

      if (response.statusCode == 200) {
        getFlightTicketOverview(ticketId: ticketId.toString());

        uploadFileImage = null;
        pickedFile = null;
      } else {
        showToastText(json['error']['error']);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  XFile? _imageFile;
  XFile? get imageFile => _imageFile;
  set imageFile(XFile? value) {
    _imageFile = value;
    notifyListeners();
  }

  List<XFile?> quotationsFile = [];
  // List<XFile?> get quotationsFile => _quotationsFile  ;
  // set quotationsFile(List<XFile?> value){
  //   _quotationsFile.add(value);
  // }

  final _imgPicker = ImagePicker();
  Future<void> openCamera(BuildContext context) async {
    Navigator.pop(context);
    var imgCamera = await _imgPicker.pickImage(
        source: ImageSource.camera,
        preferredCameraDevice: CameraDevice.front,
        imageQuality: 70,
        // maxHeight: 500,
        maxWidth: 1000);
    if (imgCamera != null) {
      final bytes = (await XFile(imgCamera.path).readAsBytes()).lengthInBytes;
      final kb = bytes / 1024;
      final mb = kb / 1024;

      if (mb <= 2) {
        croppedImageFile = null;
        imageFile = null;
        imageFile = XFile(imgCamera.path);
        await cropProfilePicture(context: context);
        imageFile = null;
      } else {
        // showToastMessage('Image should be less than 2 MB');
      }
      notifyListeners();
    }
  }

  Future<void> openGallery(BuildContext context) async {
    Navigator.pop(context);
    var imgGallery = await _imgPicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 70,
        // maxHeight: 500,
        maxWidth: 1000);
    if (imgGallery != null) {
      final bytes = (await XFile(imgGallery.path).readAsBytes()).lengthInBytes;
      final kb = bytes / 1024;
      final mb = kb / 1024;

      if (mb <= 2) {
        croppedImageFile = null;
        imageFile = null;
        imageFile = XFile(imgGallery.path);
        await cropProfilePicture(context: context);
        imageFile = null;
      } else {
        // showToastMessage('Image should be less than 2 MB');
      }
      notifyListeners();
    }
  }

  PlatformFile? _pickedPlatformFile;
  PlatformFile? get pickedPlatformFile => _pickedPlatformFile;
  set pickedPlatformFile(PlatformFile? value) {
    _pickedPlatformFile = value;
    notifyListeners();
  }

  XFile? _pickedFile;
  XFile? get pickedFile => _pickedFile;
  set pickedFile(XFile? value) {
    _pickedFile = value;
    notifyListeners();
  }

  String? _pickedFileFilePath;
  String? get pickedFileFilePath => _pickedFileFilePath;
  set pickedFileFilePath(String? value) {
    _pickedFileFilePath = value;
    notifyListeners();
  }

  Future<void> pickFile({required BuildContext context}) async {
    Navigator.pop(context);
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowCompression: true,
      withData: true,
      allowedExtensions: ['jpg', 'jpeg', 'png', 'pdf'],
    );

    if (result != null) {
      // The user picked a file.
      PlatformFile file = result.files.first;
      XFile? xFile = XFile(result.files.single.path!);
      // if (xFile != null) {
      // final bytes = (await File(xFile.path).readAsBytes()).lengthInBytes;

      if (file.size <= 2097152) {
        pickedFile = XFile(xFile.path);
      } else {
        showToastText('File should be less than 2 MB');
      }
      notifyListeners();
      // }

      if (file.size > 2097152) {
        showToastText('File size exceeded 2 MB');
      }
      if (file.size <= 2097152) {
        pickedPlatformFile = file;
        pickedFileFilePath = file.path;
      }
      // Use the file object for further processing.
    } else {
      // The user canceled the picker.
    }
  }

  cropProfilePicture({required BuildContext context}) async {
    CroppedFile? croppedFile = await ImageCropper().cropImage(
      sourcePath: imageFile!.path,
      uiSettings: [
        AndroidUiSettings(
          statusBarColor: Colors.black,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false,
        ),
        IOSUiSettings(),
      ],
    );
    if (croppedFile != null) {
      uploadFileImage = XFile(croppedFile.path);
      pickedFile = uploadFileImage;
      pickedFileFilePath = uploadFileImage?.path;
    }
  }

  final dio = Dio();

  Future<void> downloadDocument({
    required String url,
    required String fileName,
  }) async {
    try {
      if (Platform.isIOS) {
        var downloadDir = (await getApplicationDocumentsDirectory()).path;
        var path = '$downloadDir/$fileName';
        var file = File(path);
        var res = await http.get(Uri.parse(url));
        file.writeAsBytes(res.bodyBytes);
        if (res.statusCode == 200) {
          showToastText('Download success');
        } else {
          showToastText('Download failed');
        }
      }

      if (Platform.isAndroid) {
        showToastText('Downloading...');
        String path = await getPath();
        path = '$path${'E8hrportal/'}$fileName.${url.split('.').last}';

        await dio.download(url, path, onReceiveProgress: showDownloadProgress);
      }
    } catch (e) {
      debugPrint(e.toString());
      final permissionStatus = await Permission.storage.status;
      if (permissionStatus.isDenied || permissionStatus.isPermanentlyDenied) {
        showToastText('Permission denied');
        requestPermissions();
      } else {
        showToastText('Download failed');
      }
    }
  }

  Future<bool> requestPermissions() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.manageExternalStorage,
      Permission.storage,
    ].request();
    if (statuses[Permission.manageExternalStorage] ==
            PermissionStatus.granted &&
        statuses[Permission.storage] == PermissionStatus.granted) {
      // Permissions are granted
      return true;
    } else {
      // Permissions are denied or permanently denied

      await openAppSettings();
      return false;
    }
  }

  String downloadProgress = '';
  void showDownloadProgress(received, total) {
    if (total != -1) {
      downloadProgress = (received / total * 100).toStringAsFixed(0) + '%';
      debugPrint('${(received / total * 100)}');
      num progress = (received / total * 100);
      if (progress == 100) {
        showToastText('Download completed');
      }
    } else {
      downloadProgress = '';
    }
    debugPrint('downloadProgress===$downloadProgress');
    notifyListeners();
  }

  Future<String> getPath() async {
    String directory;
    if (Platform.isIOS) {
      directory = (await getDownloadsDirectory())?.path ??
          (await getTemporaryDirectory()).path;
    } else {
      directory = '/storage/emulated/0/Download/';
      var dirDownloadExists = false;
      dirDownloadExists = await Directory(directory).exists();
      if (!dirDownloadExists) {
        directory = '/storage/emulated/0/Downloads/';
        dirDownloadExists = await Directory(directory).exists();
        if (!dirDownloadExists) {
          directory = (await getTemporaryDirectory()).path;
        }
      }
    }
    return directory;
  }

  Future<void> flightTicketQuatationDelete({
    required int ticketId,
    required String fileType,
    required String attachmentId,
  }) async {
    try {
      await EasyLoading.show();
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      Map<String, dynamic> body = {
        if (fileType == 'Quotation') 'file_type': '1' else 'file_type': '2',
        'attachment_id': attachmentId,
      };
      var response = await http.post(
        flightTickeDeleteQuotationURL,
        headers: {'Authorization': 'Bearer $auth'},
        body: body,
      );
      Map<String, dynamic> json = jsonDecode(response.body);
      if (response.statusCode == 200) {
        if (json['result'] == 'success') {
          getFlightTicketOverview(
            ticketId: ticketId.toString(),
          );
        } else {
          showToastText(json['error']['ticket_id'].toString());
        }
      } else {
        if (json['result'] == 'failed') {
          showToastText(json['error']['ticket_id'].toString());
        }
      }
      await EasyLoading.dismiss();
    } catch (e) {
      await EasyLoading.dismiss();
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  DepartmentModel? _selectedDepartment;
  DepartmentModel? get selectedDepartment => _selectedDepartment;
  set selectedDepartment(DepartmentModel? value) {
    _selectedDepartment = value;
    notifyListeners();
  }

  DesignationsModel? _selectedDesignation;
  DesignationsModel? get selectedDesignation => _selectedDesignation;
  set selectedDesignation(DesignationsModel? value) {
    _selectedDesignation = value;
    notifyListeners();
  }

  List<DepartmentModel>? departmentModel = [];
  Future<void> getDepartmentList() async {
    try {
      selectedDepartment = null;
      departmentModel?.clear();
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');

      var response = await http.get(
        flightTicketDepartmentListURL,
        headers: {'Authorization': 'Bearer $auth'},
      );
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          departmentModel = (json['records'] as List)
              .map((e) => DepartmentModel.fromJson(e))
              .toList();

          departmentModel?.add(DepartmentModel(id: 0, name: 'Select all'));
          departmentModel?.sort(
            (a, b) => a.id!.compareTo(b.id!),
          );
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  List<DesignationsModel>? designationsModel = [];
  Future<void> getDesignationList() async {
    try {
      selectedDesignation = null;
      designationsModel?.clear();
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');

      var response = await http.get(
        flightTicketDesiginationListURL,
        headers: {'Authorization': 'Bearer $auth'},
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          designationsModel = (json['records'] as List)
              .map((e) => DesignationsModel.fromJson(e))
              .toList();
          designationsModel?.add(DesignationsModel(id: 0, name: 'Select all'));
          designationsModel?.sort((a, b) => a.id!.compareTo(b.id!));
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  int currentPageFlightTicketPreviousRequest = 0;
  PagingController<int, FlightTicketRequstListModel>?
      flightTicketPreviousRequestPagingController;
  initPreviousRequest({required int userId}) {
    currentPageFlightTicketPreviousRequest = 0;
    flightTicketPreviousRequestPagingController =
        PagingController(firstPageKey: 1);
    flightTicketPreviousRequestPagingController
        ?.addPageRequestListener((pageKey) {
      getPerivousRequest(userId: userId, page: pageKey);
    });
  }

  List<FlightTicketRequstListModel> previousReq = [];
  Future<void> getPerivousRequest(
      {required int userId, required int page}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    try {
      if (currentPageFlightTicketPreviousRequest != page) {
        currentPageFlightTicketPreviousRequest = page;
        var response = await http.get(
            Uri.parse('$flightTicketRequestListURL?user_id=$userId'),
            headers: {'Authorization': 'Bearer $auth'});

        if (response.statusCode == 200) {
          Map<String, dynamic> json = jsonDecode(response.body);
          if (json['result'] == 'success') {
            if (json.containsKey('entries')) {
              List records = json['entries'];
              List<FlightTicketRequstListModel> temps = [];
              previousReq = records
                  .map((e) => FlightTicketRequstListModel.fromJson(e))
                  .toList();
              temps = records
                  .map((e) => FlightTicketRequstListModel.fromJson(e))
                  .toList();

              if (json['has_next']) {
                final nextPage = page + 1;
                flightTicketPreviousRequestPagingController?.appendPage(
                    temps, nextPage);
              } else {
                flightTicketPreviousRequestPagingController
                    ?.appendLastPage(temps);
              }
              notifyListeners();
            }
          }
        }
      } else {
        int nextPage = 0;
        flightTicketPreviousRequestPagingController?.appendPage([], nextPage);
      }

      notifyListeners();
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
