// ignore_for_file: use_build_context_synchronously
import 'dart:convert';
import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/get_notification_model.dart';
import 'package:e8_hr_portal/model/logged_in_user.dart';
import 'package:e8_hr_portal/push_notification/push_notification.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import '../util/dailoge.dart';

class NotificationProvider extends ChangeNotifier {
  bool _showClearButton = true;
  bool get showClearButton => _showClearButton;
  set showClearButton(bool value) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _showClearButton = value;
      notifyListeners();
    });
  }

  bool _isLoading = false;
  bool get isLoading => _isLoading;
  set isLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  bool showDate = true;
  int currentPage = 0;
  late PagingController<int, GetNotificationModel> pagingController;
  init() {
    currentPage = 0;
    pagingController = PagingController(firstPageKey: 1);
    pagingController.addPageRequestListener((pageKey) {
      getNotification(pageKey);
    });
  }

  Future<void> getNotification(int page) async {
    if (currentPage != page) {
      currentPage = page;
      SharedPreferences shared = await SharedPreferences.getInstance();
      Uri uri = Uri.parse("${baseUrl}notification/?limit=20&page=$page");
      var response = await http.get(uri, headers: {
        'Authorization': 'Bearer ${shared.getString('access_token')}',
      });
      log(uri.toString());
      log(shared.getString('access_token').toString());
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(utf8.decode(response.bodyBytes));
        List<GetNotificationModel> temp = [];
        // notificationRead
        temp = (json['data'] as List)
            .map((e) => GetNotificationModel.fromJson(e))
            .toList();
        for (var data in temp) {
          if (data.read == true) {
            notificationRead.add(data.id);
          }
        }
        if (json['has_next']) {
          final nextPage = page + 1;
          pagingController.appendPage(temp, nextPage);
        } else {
          pagingController.appendLastPage(temp);
        }
        isLoading = false;
      }
      pagingController.notifyListeners();
      notifyListeners();
    }
  }

  bool _isClearing = false;
  bool get isClearing => _isClearing;
  set isClearing(bool value) {
    _isClearing = value;
    notifyListeners();
  }

  Future<void> clearNotification({required BuildContext context}) async {
    isClearing = true;
    EasyLoading.show();

    SharedPreferences shared = await SharedPreferences.getInstance();
    http.Response response =
        await http.patch(Uri.parse("${baseUrl}clear_notification/"), headers: {
      'Authorization': 'Bearer ${shared.getString('access_token')}',
    });

    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      await showSnackBarMessage(context: context, msg: data["message"]);
      // await EasyLoading.showSuccess(data["message"]);
      currentPage = 0;
      pagingController.refresh();
      pagingController.notifyListeners();
      getNotificationCount();
    }
    EasyLoading.dismiss();
    isClearing = false;
  }

  int _notificationCount = 0;
  int get notificationCount => _notificationCount;
  set notificationCount(int count) {
    _notificationCount = count;
    notifyListeners();
  }

  Future<void> getNotificationCount() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    http.Response response =
        await http.get(Uri.parse("${baseUrl}notification/"), headers: {
      'Authorization': 'Bearer ${shared.getString('access_token')}',
    });
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      notificationCount = data['unread_count'];
    }
  }

  final List<int?> _notificationRead = [];
  List<int?> get notificationRead => _notificationRead;
  set notificationRead(List<int?> value) {
    _notificationRead.addAll(value);
    notifyListeners();
  }

  Future<void> readNotification({required String id}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    await http.patch(Uri.parse("${baseUrl}read_notification/$id/"), headers: {
      'Authorization': 'Bearer ${shared.getString('access_token')}',
    });
    await getNotificationCount();
    // currentPage = 0;

    // pagingController.refresh();
    // pagingController.notifyListeners();
    notifyListeners();
  }

  Future<void> approveLeave({
    required String notificationId,
    required String leaveId,
    required String employeeId,
  }) async {
    isLoading = true;
    EasyLoading.show();
    DocumentReference documentReference = FirebaseFirestore.instance
        .collection('leave_applications')
        .doc(leaveId);
    await documentReference
        .collection('reporting_persons')
        .doc(LoggedInUser.uid)
        .update({'status': 'approved'});
    await FirebaseFirestore.instance
        .collection('notifications')
        .doc(notificationId)
        .update({'status': 'approved'});
    isLoading = false;
    EasyLoading.dismiss();
    showToastText('Leave approved successfully');
    sendApproveOrRejectMessage(
        employeeId: employeeId, leaveId: leaveId, isApproved: true);
  }

  Future<void> sendApproveOrRejectMessage({
    required String employeeId,
    required String leaveId,
    required bool isApproved,
  }) async {
    await FirebaseFirestore.instance.collection('notifications').add({
      'uid': employeeId,
      'leave_id': leaveId,
      'title': 'Leave ${isApproved ? 'Approved' : 'Rejected'}',
      'body':
          '${LoggedInUser.name} has ${isApproved ? 'approved' : 'rejected'} your leave.',
      'type': 'leave_approval',
      'read': false,
      'saveTime': DateTime.now(),
    });
    DocumentSnapshot<Map<String, dynamic>> documentSnapshot =
        await FirebaseFirestore.instance
            .collection('users')
            .doc(employeeId)
            .get();
    try {
      String deviceToken = documentSnapshot.get('fcmToken');
      sendMessagesToSpecificDevices(
        deviceToken: deviceToken,
        title: 'Leave ${isApproved ? 'Approved' : 'Rejected'}',
        body:
            '${LoggedInUser.name} has ${isApproved ? 'approved' : 'rejected'} your leave.',
        screen: 'notifications',
      );
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  Future<void> rejectLeave({
    required String notificationId,
    required String leaveId,
    required String employeeId,
  }) async {
    isLoading = true;
    EasyLoading.show();
    DocumentReference documentReference = FirebaseFirestore.instance
        .collection('leave_applications')
        .doc(leaveId);
    await documentReference
        .collection('reporting_persons')
        .doc(LoggedInUser.uid)
        .update({'status': 'rejected'});
    await FirebaseFirestore.instance
        .collection('notifications')
        .doc(notificationId)
        .update({'status': 'rejected'});
    isLoading = false;
    EasyLoading.dismiss();
    showToastText('Leave rejected');
    sendApproveOrRejectMessage(
      employeeId: employeeId,
      leaveId: leaveId,
      isApproved: false,
    );
  }
}
