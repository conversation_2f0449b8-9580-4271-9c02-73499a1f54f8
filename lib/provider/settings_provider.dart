import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/model/logged_in_user.dart';

class SettingsProvider extends ChangeNotifier {
  void changeWorkStatus(bool value) {
    LoggedInUser.isWorkFromHome = value;
    notifyListeners();
    _updateWorkStatus();
  }

  Future<void> _updateWorkStatus() async {
    await FirebaseFirestore.instance
        .collection('users')
        .doc(LoggedInUser.uid)
        .update({'isWorkFromHome': LoggedInUser.isWorkFromHome});
  }
}
