import 'dart:convert';
import 'dart:developer';
import 'package:e8_hr_portal/model/checklist_history_model.dart';
import 'package:e8_hr_portal/model/checklist_onsiginout_model.dart';
import 'package:e8_hr_portal/model/checklist_shecduler_model.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:e8_hr_portal/view/checklist/model/checklist_status_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:e8_hr_portal/services/api_service.dart';

class ChecklistProvider extends ChangeNotifier {
  bool _isExpanded = false;
  bool get isExpanded => _isExpanded;
  set isExpanded(value) {
    _isExpanded = value;
    notifyListeners();
  }

  bool _isChecked = false;
  bool get isChecked => _isChecked;
  set isChecked(value) {
    _isChecked = value;
    notifyListeners();
  }

  bool _radioButtonValue = false;
  bool get radioButtonValue => _radioButtonValue;
  set radioButtonValue(value) {
    _radioButtonValue = value;
    notifyListeners();
  }

  bool _radioButtonHistoryValue = true;
  bool get radioButtonHistoryValue => _radioButtonHistoryValue;
  set radioButtonHistoryValue(value) {
    _radioButtonHistoryValue = value;
    notifyListeners();
  }

  int _unReadCount = 0;
  int get unReadCount => _unReadCount;
  set unReadCount(value) {
    _unReadCount = value;
    notifyListeners();
  }

  ChecklistTypeModel? selectedChecklistStatus;
  List<ChecklistTypeModel> checklistStatusList = [
    ChecklistTypeModel(id: 2, name: 'Pending'),
    ChecklistTypeModel(id: 1, name: 'Completed'),
  ];
  onChangedSelectedStatus({required ChecklistTypeModel item}) {
    selectedChecklistStatus = item;
    if (item.id == 1) {
      filterDate = DateTime.now();
      selectedChecklistType = null;
    }
    notifyListeners();
  }

  ChecklistTypeModel? selectedChecklistType;
  onChangedSelectedType({required ChecklistTypeModel item}) {
    selectedChecklistType = item;
    notifyListeners();
  }

  DateTime? _filterDate;
  DateTime? get filterDate => _filterDate;
  set filterDate(value) {
    _filterDate = value;
    notifyListeners();
  }

  Future<void> getChecklistCount() async {
    try {
      // SECURITY FIX: Use secure ApiService instead of direct HTTP calls
      var response = await ApiService.get(checklistNotificationURL);

      log("getChecklistCount -- Status: ${response.statusCode}, Body: ${response.body}");

      if (response.statusCode == 200) {
        var data = jsonDecode(
          utf8.decode(response.bodyBytes),
        );

        if (data["result"] == "success") {
          unReadCount = data['unread_count'];
          log("getChecklistCount -- Unread count updated: $unReadCount");
        } else {
          log("getChecklistCount -- API returned failure: ${data['result']}");
        }
      } else {
        log("getChecklistCount -- HTTP error: ${response.statusCode}");
      }
    } catch (e) {
      log("getChecklistCount -- Error: $e");
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  List<ChecklistTypeModel> checklistTypeList = [];
  Future<void> getChecklistType() async {
    try {
      // checklistTypeList.clear();
      isLoading = true;

      // SECURITY FIX: Use secure ApiService instead of direct HTTP calls
      var response = await ApiService.get(checklistScheduleTypeURL);
      isLoading = false;

      log("getChecklistType -- Status: ${response.statusCode}, Body: ${response.body}");

      if (response.statusCode == 200) {
        var json = jsonDecode(utf8.decode(response.bodyBytes));

        if (json["result"] == "success") {
          List records = json['records'];
          checklistTypeList =
              records.map((e) => ChecklistTypeModel.fromJson(e)).toList();
          checklistTypeList.insert(0, ChecklistTypeModel(id: 0, name: 'All'));
          log("getChecklistType -- Successfully loaded ${checklistTypeList.length} types");
        } else {
          log("getChecklistType -- API returned failure: ${json['result']}");
        }
      } else {
        log("getChecklistType -- HTTP error: ${response.statusCode}");
      }
    } catch (e) {
      isLoading = false;
      log("getChecklistType -- Error: $e");
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  List<CheckListHistoryModel> checkListHistoryModel = [];
  List<CheckListHistoryModel> checkListHistoryModelonce = [];
  List<CheckListHistoryModel> checkListHistoryModelDialy = [];
  List<CheckListHistoryModel> checkListHistoryModelWeekly = [];
  List<CheckListHistoryModel> checkListHistoryModelMonthley = [];
  List<CheckListHistoryModel> checkListHistoryModelQuarterly = [];
  List<CheckListHistoryModel> checkListHistoryModelHalfyearly = [];
  List<CheckListHistoryModel> checkListHistoryModelyearly = [];

  Future<void> clearData() async {
    checkListHistoryModel.clear();
    checkListHistoryModelonce.clear();
    checkListHistoryModel.clear();
    checkListHistoryModelDialy.clear();
    checkListHistoryModelWeekly.clear();
    checkListHistoryModelMonthley.clear();
    checkListHistoryModelQuarterly.clear();
    checkListHistoryModelHalfyearly.clear();
    checkListHistoryModelyearly.clear();
  }

  String errorMsg = "";

  bool _isLoading = false;
  bool get isLoading => _isLoading;
  set isLoading(value) {
    _isLoading = value;
    notifyListeners();
  }

  void clearStartFromChecklist() async {
    selectedDate = DateTime.now();
    selectedMonth = null;
    selectedIndex = null;
    filterValue = null;
  }

  Future<void> getChecklistHistory() async {
    try {
      isLoading = true;
      await clearData();
      SharedPreferences shared = await SharedPreferences.getInstance();
      StringBuffer url = StringBuffer(checklistAssignedFilterURL);
      // url.write(object)
      url.write(
          "?year=${formatDateFromDate(dateTime: selectedDate, format: "yyyy")}");
      if (selectedMonth != null) {
        url.write("&month=$selectedMonth");
      }
      if (filterValue != null) {
        url.write("&filter=$filterValue");
      }

      var response = await http.get(Uri.parse(url.toString()), headers: {
        'Authorization': 'Bearer ${shared.getString('access_token')}'
      });
      EasyLoading.dismiss();
      log("response checklist history ====${response.statusCode} --${response.request},---- ${response.body}");
      if (response.statusCode == 200) {
        var data = jsonDecode(
          utf8.decode(response.bodyBytes),
        );

        if (data["result"] == "success") {
          checkListHistoryModel = (data["data"] as List)
              .map(
                (e) => CheckListHistoryModel.fromJson(e),
              )
              .toList();

          for (var data in checkListHistoryModel) {
            log("inside =======");
            if (data.scheduleId == "1") {
              checkListHistoryModelonce.add(data);
            } else if (data.scheduleId == "2") {
              checkListHistoryModelDialy.add(data);
            } else if (data.scheduleId == "3") {
              checkListHistoryModelWeekly.add(data);
            } else if (data.scheduleId == "4") {
              checkListHistoryModelMonthley.add(data);
            } else if (data.scheduleId == "5") {
              checkListHistoryModelQuarterly.add(data);
            } else if (data.scheduleId == "6") {
              checkListHistoryModelHalfyearly.add(data);
            } else if (data.scheduleId == "7") {
              checkListHistoryModelyearly.add(data);
            }
            // log("dataaaasss = $data");
          }
        } else {
          errorMsg = data["errors"];
        }
      } else {}
      isLoading = false;
    } catch (e) {
      isLoading = false;
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  List<CheckListShedulerModel> listOfsheduler = [];

  Future<void> getShedulers() async {
    try {
      isLoading = true;
      SharedPreferences shared = await SharedPreferences.getInstance();
      StringBuffer url = StringBuffer(checklistShedulersURL);
      // url.write(object)

      var response = await http.get(Uri.parse(url.toString()), headers: {
        'Authorization': 'Bearer ${shared.getString('access_token')}'
      });
      isLoading = false;
      log("response checklist sheduler ==== ${response.statusCode},---- ${response.body}");
      if (response.statusCode == 200) {
        var data = jsonDecode(utf8.decode(response.bodyBytes));
        log("response checklist sheduler ---- inside 1");

        if (data["result"] == "success") {
          log("response checklist sheduler ---- inside 2");
          listOfsheduler = (data["data"] as List)
              .map((e) => CheckListShedulerModel.fromJson(e))
              .toList();

          log("response checklist sheduler ---- inside 3");
          log("listOfsheduler  == $listOfsheduler");
        }
      }
    } catch (e) {
      isLoading = false;
      debugPrint(e.toString());
    }
    notifyListeners();
  }

  DateTime? _selectedFromDate;
  DateTime? get selectedFromDate => _selectedFromDate;
  set selectedFromDate(DateTime? value) {
    _selectedFromDate = value;
    notifyListeners();
  }

  DateTime _selectedDate = DateTime.now();
  DateTime get selectedDate => _selectedDate;
  set selectedDate(value) {
    _selectedDate = value;
    notifyListeners();
  }

  int? _selectedMonth;
  int? get selectedMonth => _selectedMonth;
  set selectedMonth(value) {
    _selectedMonth = value;
    notifyListeners();
  }

  int? _selectedIndex;
  int? get selectedIndex => _selectedIndex;
  set selectedIndex(value) {
    _selectedIndex = value;
    notifyListeners();
  }

  String? _filterValue;
  String? get filterValue => _filterValue;
  set filterValue(value) {
    _filterValue = value;
    notifyListeners();
  }

  Future<void> selectFromDate({required BuildContext context}) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Select Year", style: tsS18w500c161616),
          content: SizedBox(
            width: 300,
            height: 200,
            child: YearPicker(
              firstDate: DateTime(DateTime.now().year - 1, 1),
              lastDate: DateTime(DateTime.now().year + 7, 1),
              initialDate: DateTime.now(),
              selectedDate: selectedDate,
              currentDate: selectedDate,
              onChanged: (DateTime dateTime) {
                selectedDate = dateTime;
                getChecklistHistory();
                Navigator.pop(context);
              },
            ),
          ),
        );
      },
    );
  }

  List<CheckListHistoryModel> _checkListModel = [];
  List<CheckListHistoryModel> get checkListModel => _checkListModel;
  set checkListModel(value) {
    _checkListModel = value;
    notifyListeners();
  }

  Future<void> getChecklist(
      {String? status, String? date, String? type}) async {
    try {
      isLoading = true;
      // _checkListModel.clear();
      // isLoading = true; // Notify UI that loading has started
      SharedPreferences shared = await SharedPreferences.getInstance();
      StringBuffer url = StringBuffer('$checklistAssignedURL');
      if (selectedChecklistStatus?.name != null) {
        String status =
            (selectedChecklistStatus?.name ?? 'pending').toLowerCase();
        url.write('?status=$status');
      }
      if (selectedChecklistType?.id != 0) {
        if (selectedChecklistType?.id != null) {
          int status = selectedChecklistType?.id ?? 0;
          url.write('&type=$status');
        }
      }
      if (filterDate != null) {
        String year = formatDateFromDate(
            dateTime: filterDate ?? DateTime.now(), format: 'yyyy');
        String month = formatDateFromDate(
            dateTime: filterDate ?? DateTime.now(), format: 'MM');
        url.write('&month=$month');
        url.write('&year=$year');
      }
      log('url --------------- $url');
      var response = await http.get(Uri.parse(url.toString()), headers: {
        'Authorization': 'Bearer ${shared.getString('access_token')}'
      });
      isLoading = false;
      EasyLoading.dismiss();
      log("getChecklist ===!!= ${response.statusCode},---- ${response.body} ------ ${shared.getString('access_token')} - ${response.request}");
      if (response.statusCode == 200) {
        var data = jsonDecode(
          utf8.decode(response.bodyBytes),
        );
        if (data["result"] == "success") {
          _checkListModel = (data["data"] as List)
              .map((e) => CheckListHistoryModel.fromJson(e))
              .toList();
        } else {
          _checkListModel = [];
        }
      } else {
        _checkListModel.clear();
      }

      // catch (e) {
      //   EasyLoading.dismiss();
      //   // isLoading = false;
      //   debugPrint(e.toString());
      // }
      EasyLoading.dismiss();
      isLoading = false;
      notifyListeners();
    } catch (e) {
      EasyLoading.dismiss();
      isLoading = false;
      debugPrint(e.toString());
    }
  }

  Future<bool> submitChecklist(
      {required String assigneeID,
      String? yesOrNo,
      required String remark,
      String? checkListResponse,
      required String dueDate,
      required String details}) async {
    isLoading = true;
    try {
      SharedPreferences shared = await SharedPreferences.getInstance();
      StringBuffer url = StringBuffer(checklistAssignedUpdateURL);

      Map<String, dynamic> body = {};
      body['assignee_id'] = assigneeID;
      body['remarks'] = remark;
      body['due_date'] = dueDate;
      body['details'] = details;
      if (yesOrNo != null) {
        body['status'] = yesOrNo;
      } else {
        body['response'] = checkListResponse ?? '';
      }
      log('body - $body');
      // var body = {"status": yesOrNo, if (remark != null) "remarks": remark};

      var response = await http.post(Uri.parse(url.toString()),
          headers: {
            'Authorization': 'Bearer ${shared.getString('access_token')}'
          },
          body: body);
      EasyLoading.dismiss();
      isLoading = false;
      log("response checklist  Submit ===$body= ${response.statusCode},- ${response.request} --- ${response.body}");
      if (response.statusCode == 200) {
        var data = jsonDecode(
          utf8.decode(response.bodyBytes),
        );
        if (data["result"] == "success") {
          getChecklist();
          isLoading = false;
          showToastText("Checklist updated successfully");
          notifyListeners();
          return true;
        } else {
          isLoading = false;
          notifyListeners();
          return false;
        }
      } else {
        isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      isLoading = false;
      debugPrint(e.toString());
      notifyListeners();
      return false;
    }
  }

  List<OnsignoutChecklistModel> onSiginOutCheckListModel = [];
  Future<void> getOnSignOutChecklist() async {
    try {
      EasyLoading.show();
      SharedPreferences shared = await SharedPreferences.getInstance();
      var response = await http.get(
        onsignoutchecklistsURL,
        headers: {
          'Authorization': 'Bearer ${shared.getString('access_token')}'
        },
      );
      if (kDebugMode) {
        log("response checklist data === ${response.body}");
      }
      if (response.statusCode == 200) {
        var data = jsonDecode(
          utf8.decode(response.bodyBytes),
        );
        if (data["result"] == "success") {
          onSiginOutCheckListModel = (data["data"] as List)
              .map(
                (e) => OnsignoutChecklistModel.fromJson(e),
              )
              .toList();
        } else {}
      } else {}
      EasyLoading.dismiss();
      notifyListeners();
    } catch (e, stackTrace) {
      EasyLoading.dismiss();
      debugPrint(e.toString());
      debugPrint(stackTrace.toString());
    }
    notifyListeners();
  }

  Future<void> onSignOutChecklistUPDATE({
    String? assigneeId,
    String? remarks,
    String? status,
    String? responseString,
    String? details,
  }) async {
    try {
      EasyLoading.show();
      SharedPreferences shared = await SharedPreferences.getInstance();
      var body = {
        "assignee_id": assigneeId,
        if (remarks != null) "remarks": remarks,
        if (status != null) "status": status,
        if (responseString != null) "response": responseString,
        if (details != null) "details": details,
      };

      var response = await http.post(onsignoutchecklistsupdateURL,
          headers: {
            'Authorization': 'Bearer ${shared.getString('access_token')}'
          },
          body: body);
      // if (kDebugMode) {
      log("response checklist data update === --${response.statusCode} ${body} --- ----- -${response.body} -");
      // }
      if (response.statusCode == 200) {
        var data = jsonDecode(
          utf8.decode(response.bodyBytes),
        );
        if (data["result"] == "success") {
          showToastText("Checklist updated successfully");
          getOnSignOutChecklist();
        } else {}
      } else {}
      EasyLoading.dismiss();
      notifyListeners();
    } catch (e, stackTrace) {
      EasyLoading.dismiss();
      debugPrint(e.toString());
      debugPrint(stackTrace.toString());
    }
    notifyListeners();
  }
}
