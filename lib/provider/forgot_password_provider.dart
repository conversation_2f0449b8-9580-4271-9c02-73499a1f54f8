// ignore_for_file: use_build_context_synchronously
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/util/dailoge.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/page_navigator.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:e8_hr_portal/view/forgot_password/forgot_oto_screen.dart';
import 'package:e8_hr_portal/view/forgot_password/forgot_password_change_screen.dart';
import 'package:e8_hr_portal/view/sign_in/login_with_email_screen.dart';
import 'package:http/http.dart' as http;

class ForgotPasswordProvider extends ChangeNotifier {
  String? verificationURL;

  Future<void> sendPasswordResetEmail(
      {required BuildContext context, required String email}) async {
    // FirebaseAuth auth = FirebaseAuth.instance;
    EasyLoading.show();
    // bool emailSent = true;
    var body = {"email": email};

    var response = await http.post(forgotpassURL, body: body);

    if (response.statusCode == 200) {
      Map<String?, dynamic> data = jsonDecode(response.body);
      if (data["result"] == "success") {
        verificationURL = data["verification_url"];
        showSnackBarMessage(context: context, msg: data["OTP"].toString());

        PageNavigator.push(
            context: context,
            route: ForgotOtpScreen(
              email: email,
            ));
        // showDialog(
        //   context: context,
        //   barrierDismissible: false,
        //   builder: (context) => const ForgotPasswordSuccessDialog(),
        // );
      }
      EasyLoading.dismiss();
    } else {
      EasyLoading.dismiss();
      Map<String, dynamic> data = jsonDecode(response.body);
      Map<String, dynamic> error = data['errors'];
      if (error.containsKey("email")) {
        showToastText(error["email"]);
      } else if (error.containsKey("date_of_leaving")) {
        showToastText(error["date_of_leaving"]);
      } else if (error.containsKey("user_not_active")) {
        showToastText(error["user_not_active"]);
      }
      // showToastText(data["errors"]["email"]);
    }
    EasyLoading.dismiss();
    notifyListeners();

    // await auth
    //     .sendPasswordResetEmail(email: email)
    //     .onError((error, stackTrace) {
    //   emailSent = false;
    //   EasyLoading.dismiss();
    //   showDialog(
    //     context: context,
    //     barrierDismissible: false,
    //     builder: (context) => const ForgotPasswordErrorDialog(),
    //   );
    // }).whenComplete(() {
    //   if (emailSent) {
    //     EasyLoading.dismiss();
    //     showDialog(
    //       context: context,
    //       barrierDismissible: false,
    //       builder: (context) => const ForgotPasswordSuccessDialog(),
    //     );
    //   }
    // });
  }

  String? passwordChangeURL;

  Future<void> otpVerification(
      {required String? otp, required BuildContext context}) async {
    var body = {"otp": otp.toString()};

    var response = await http.post(Uri.parse(verificationURL!), body: body);

    if (response.statusCode == 200) {
      Map<String?, dynamic> data = jsonDecode(response.body);

      if (data["result"] == "success") {
        await showToastText(data["message"]);
        passwordChangeURL = data["password_change_url"];
        PageNavigator.push(
            context: context, route: const ForgotPasswordChangePassScreen());
      }
    } else {
      Map<String, dynamic> data = jsonDecode(response.body);
      if (data.containsKey("errors")) {
        Map<String, dynamic> error = data["errors"];
        await showToastText(error["otp"]);
      }
    }
    notifyListeners();
  }

  String? _passError;
  String? _confirmPassError;

  String? get passError => _passError;
  set passError(String? value) {
    _passError = value;
    notifyListeners();
  }

  String? get confirmPassError => _confirmPassError;
  set confirmPassError(String? value) {
    _confirmPassError = value;
    notifyListeners();
  }

  Future<void> forgotPasswordChangeURL({
    required String? password,
    required String? confirmPassword,
    required BuildContext? context,
  }) async {
    var body = {"password": password, "confirm_password": confirmPassword};

    var response = await http.post(Uri.parse(passwordChangeURL!), body: body);

    if (response.statusCode == 200) {
      var data = jsonDecode(response.body);

      if (data["result"] == "success") {
        showToastText(data["message"]);

        PageNavigator.pushAndRemoveUntil(
            context: context!, route: const LoginWithEmailScreen());
      }
    } else if (response.statusCode == 400) {
      var data = jsonDecode(response.body);

      Map<String, dynamic> errors = data['errors'];

      if (errors.containsKey('password')) {
        showToastText(errors['password']);
        _passError = errors['password'];
      } else if (errors.containsKey('confirm_password')) {
        showToastText(errors['confirm_password']);
        _confirmPassError = errors['confirm_password'];
      }
    }
    notifyListeners();
  }
}
