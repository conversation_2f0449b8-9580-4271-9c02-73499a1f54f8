// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'package:e8_hr_portal/model/attendence_view_log_model.dart';
import 'package:e8_hr_portal/provider/sign_in_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/attendance_model.dart';
import 'package:e8_hr_portal/model/attendance_details_model.dart';
import 'package:http/http.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../util/dailoge.dart';
import '../util/date_formatter.dart';
import '../util/urls.dart';

class AttendanceProvider with ChangeNotifier {
  DateTime _selectedMonth = DateTime.now();
  DateTime get selectedMonth => _selectedMonth;
  set selectedMonth(DateTime month) {
    _selectedMonth = month;
    notifyListeners();
  }

  List<AttendanceModel> attendanceList = [];
  String? onTimeArrivaldash;
  String? onTimeArrival;
  String? assignedDays;
  String? workingDays;
  Future<void> getAttendanceList(
      {required bool master, required BuildContext context}) async {
    if (!master) {
      if (attendanceList.isEmpty) {
        EasyLoading.show();
      }
    }
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    String month = formatDateFromDate(dateTime: selectedMonth, format: 'MMM');

    StringBuffer uriBuffer = StringBuffer(baseUrl);
    uriBuffer.write('attendance/?date=');
    uriBuffer.write(month);
    uriBuffer.write(',');
    uriBuffer.write(selectedMonth.year);

    Uri uri = Uri.parse(uriBuffer.toString());
    Map<String, String> headers = {'Authorization': 'Bearer $auth'};
    Response response = await get(uri, headers: headers);
    attendanceList.clear();
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      attendanceList = (data['records'] as List)
          .map((e) => AttendanceModel.fromJson(e))
          .toList();
      onTimeArrival = data['ontime_arrival'];
      if (master) {
        onTimeArrivaldash = data['ontime_arrival'];
      }
      assignedDays = data['assigned_days'];
      workingDays = data['working_days'];

      notifyListeners();
    } else if (response.statusCode == 401 && context.mounted) {
      context.read<SignInProvider>().updateToken(context: context);
      // EasyLoading.dismiss();
      // PageNavigator.pushAndRemoveUntil(
      //   context: context,
      //   route: const LoginWithEmailScreen(),
      // );
    }
    notifyListeners();
    EasyLoading.dismiss();
  }

  AttendanceDetailsModel? attendanceDetails;
  Future<void> getAttendanceDetails({required String id}) async {
    try {
      EasyLoading.show();
      SharedPreferences shared = await SharedPreferences.getInstance();
      String? auth = shared.getString('access_token');
      Response response = await get(
          Uri.parse('${baseUrl}attendance-retrieve/$id/'),
          headers: {'Authorization': 'Bearer $auth'});
      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);
        attendanceDetails = AttendanceDetailsModel.fromJson(data);
        notifyListeners();
      } else {
        attendanceDetails = null;
      }
      notifyListeners();
      EasyLoading.dismiss();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> attendanceRequest(
      {required String reason,
      required String explanation,
      required int? id,
      required String attendanceDate,
      required BuildContext context}) async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    Response response;
    if (id != null) {
      response = await post(Uri.parse('${baseUrl}attendace-request/'),
          headers: {
            'Authorization': 'Bearer $auth'
          },
          body: {
            'reason': reason,
            'explanation': explanation,
            'attendance': id.toString()
          });
    } else {
      response = await post(
          Uri.parse('${baseUrl}attendace-request/$attendanceDate/'),
          headers: {'Authorization': 'Bearer $auth'},
          body: {'reason': reason, 'explanation': explanation});
    }
    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      EasyLoading.dismiss();
      showToastText(data['message']);
      getAttendanceDetails(id: attendanceDate);
      getAttendanceList(master: false, context: context);
    } else {
      EasyLoading.dismiss();
      Map<String, dynamic> data = jsonDecode(response.body);
      if (data.containsKey('erros')) {
        Map<String, dynamic> errors = data['erros'];
        if (errors.containsKey('attendance')) {
          showToastText(errors['attendance']);
        }
      }
    }
    EasyLoading.dismiss();
  }

  AttendanceViewLogModel? attendanceViewLogModel;
  Future<void> getAttendenceViewLog({required String? date}) async {
    EasyLoading.show();
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    var response = await get(Uri.parse('$attendenceViewLogURL$date/'),
        headers: {'Authorization': 'Bearer $auth'});
    if (response.statusCode == 200) {
      var data = jsonDecode(utf8.decode(response.bodyBytes));
      attendanceViewLogModel = AttendanceViewLogModel.fromJson(data);
    }
    notifyListeners();
    EasyLoading.dismiss();
  }
}
