// ignore_for_file: deprecated_member_use, use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:e8_hr_portal/model/attendance_log_model.dart';
import 'package:e8_hr_portal/model/ble_device_model.dart';
import 'package:e8_hr_portal/provider/log_time_provider.dart';
import 'package:e8_hr_portal/util/api_headers.dart';
import 'package:e8_hr_portal/util/date_formatter.dart';
import 'package:e8_hr_portal/util/general_functions.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';

enum AttendanceStatus { none, scanning, deviceFound, requesting, completed }

class BLEAttendanceProvider extends ChangeNotifier {
  bool isLoading = false;
  DateTime _selectedMonth = DateTime.now();
  DateTime get selectedMonth => _selectedMonth;
  set selectedMonth(DateTime month) {
    _selectedMonth = month;
    notifyListeners();
  }

  AttendanceStatus _attendanceStatus = AttendanceStatus.none;
  AttendanceStatus get attendanceStatus => _attendanceStatus;
  set attendanceStatus(AttendanceStatus status) {
    _attendanceStatus = status;
    notifyListeners();
  }

  bool _isBleDetected = false;
  bool get isBleDetected => _isBleDetected;
  set isBleDetected(bool value) {
    _isBleDetected = value;
    notifyListeners();
  }

  StreamSubscription<BluetoothAdapterState>? _blueSubscription;

  Future<void> _checkBluetoothAvailability() async {
    debugPrint('Check bluetooth isAvailable or notwww');
    bool isAvailable = await FlutterBluePlus.isAvailable;
    debugPrint('Check bluetooth isAvailable or not ? $isAvailable');
    if (!isAvailable) {
      debugPrint("Bluetooth not supported by this device");
      return;
    }

    if (Platform.isAndroid) {
      _blueSubscription = FlutterBluePlus.adapterState.listen((event) async {
        if (event == BluetoothAdapterState.off) {
          await FlutterBluePlus.turnOn();
        } else {
          _blueSubscription?.cancel();
        }
      });
    }
  }

  Future<void> scanNearbyDevices() async {
    attendanceStatus = AttendanceStatus.scanning;
    await _checkBluetoothAvailability();
    // await getBLEDeviceList();
    // Setup Listener for scan results.
// device not found? see "Common Problems" in the README
    // Set<DeviceIdentifier> seen = {};
    // if (Platform.isIOS) {
    // seen =bleDeviceList.map((e) => DeviceIdentifier(e.rsId.toString())).toSet();
    // seen = {
    // const DeviceIdentifier('BF94D1A5-5D54-E648-8BDD-70A9C04D494B'),
    // const DeviceIdentifier('414F0E80-80A1-814C-B1EA-85719C4B3F5E'),
    // };
    // } else if (Platform.isAndroid) {
    //   seen = {
    //     const DeviceIdentifier('00:FA:B6:07:AA:3F'),
    //     const DeviceIdentifier('00:FA:B6:07:AA:3C'),
    //   };
    // }

    FlutterBluePlus.scanResults.listen(
      (results) {
        for (ScanResult r in results) {
          if (r.advertisementData.localName == 'Kontakt') {
            attendanceStatus = AttendanceStatus.deviceFound;
            debugPrint(
                '${r.device.remoteId}: "${r.advertisementData.localName}" found! rssi: ${r.rssi}');
            FlutterBluePlus.stopScan();
            // sendPunchInOutRequest();
          }
        }
      },
    );

// Start scanning
// Note: You should always call `scanResults.listen` before you call startScan!
    await FlutterBluePlus.startScan();
  }

  Future<void> sendPunchInOutRequest(
      {required BuildContext context, required String action}) async {
    try {
      var body = {"action": action};
      final response = await http.post(punchinpunchoutURL,
          headers: await APIHeaders.getApiHeader(), body: body);
      if (kDebugMode) {
        print(response.request);
        print(body);
        print(response.statusCode.toString());
        print(response.body);
        print('access token--------${await APIHeaders.getApiHeader()}');
      }
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(utf8.decode(response.bodyBytes));
        if (json['result'] == 'success') {
          if (action == "punch-out") {
            log("insidei=n --punch out $action");
            var provider = context.read<LogTimeProvider>();
            log("insidei=n --punch out 2");

            await provider.userAllTaskStop();
            await provider.getUserTimeLogList(master: false);
            provider.userTimeLogReportSend();
          }
          String message = json['message'];
          if (json.containsKey('message')) {
            if (!context.mounted) return;
            showSnackBarMessage(context: context, msg: message);
            EasyLoading.dismiss();
          }
        } else {
          if (!context.mounted) return;
          showSnackBarMessage(context: context, msg: 'Something went wrong');
          EasyLoading.dismiss();
        }
      } else if (response.statusCode == 400) {
        Map<String, dynamic> json = jsonDecode(utf8.decode(response.bodyBytes));
        showSnackBarMessage(context: context, msg: json['message']);
      }
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint(e.toString());
    }
  }

  void emitNumbers(bool shouldEmit) {
    int count = 0;

    void emitNextNumber() {
      if (!shouldEmit) {
        return; // Stop emitting when shouldEmit is false
      }

      // print('count------------------------------$count');

      count = (count + 1) % 4; // Wrap around to 0, 1, 2, 3
      Future.delayed(const Duration(seconds: 1),
          emitNextNumber); // Emit next number after a delay
    }

    if (shouldEmit) {
      emitNextNumber(); // Start emitting numbers
    }
  }

  List<AttendanceLogModel> attendanceLogModelList = [];
  List<String> punchinPunchoutLocalList = [];
  Future<void> getAttendanceLogs() async {
    try {
      if (!isLoading) {
        isLoading = true;
        attendanceLogModelList.clear();

        String today =
            formatDateFromDate(dateTime: DateTime.now(), format: 'yyyy-MM-dd');
        // '2023-09-19';
        String apiURL = '${baseUrl}attendance-logs/$today/';
        Uri apiURI = Uri.parse(apiURL);
        var response =
            await http.get(apiURI, headers: await APIHeaders.getApiHeader());

        log(response.body, name: "getattendanceLogs");

        if (response.statusCode == 200) {
          Map<String, dynamic> json =
              jsonDecode(utf8.decode(response.bodyBytes));
          if (json.containsKey('attendance_logs')) {
            List records = json['attendance_logs'] ?? [];
            attendanceLogModelList =
                records.map((e) => AttendanceLogModel.fromJson(e)).toList();
            punchinPunchoutLocalList.clear();
            attendanceLogModelList.map((e) {
              if (e.punchin != null) {
                punchinPunchoutLocalList.add(e.punchin!);
                if (e.punchout != null) {
                  punchinPunchoutLocalList.add(e.punchout!);
                }
              }
            }).toList();
          }
        } else {
          EasyLoading.dismiss();
          isLoading = false;
        }
        notifyListeners();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    isLoading = false;
  }

  bool _isDeviceListLoading = false;
  bool get isDeviceListLoading => _isDeviceListLoading;
  set isDeviceListLoading(bool value) {
    _isDeviceListLoading = value;
    notifyListeners();
  }

  List<BLEDeviceModel> bleDeviceList = [];

  Future<void> getBLEDeviceList() async {
    try {
      isDeviceListLoading = true;
      Uri uri = Uri.parse('${baseUrl}ble-device-list/');
      final response =
          await http.get(uri, headers: await APIHeaders.getApiHeader());
      debugPrint(response.request.toString());
      debugPrint(response.statusCode.toString());
      debugPrint(response.body);
      if (response.statusCode == 200) {
        Map<String, dynamic> data = jsonDecode(response.body);
        bleDeviceList = (data['data'] as List)
            .map((e) => BLEDeviceModel.fromJson(e))
            .toList();
      } else {
        bleDeviceList.clear();
      }
      isDeviceListLoading = false;
      notifyListeners();
    } catch (e) {
      isDeviceListLoading = false;
      debugPrint(e.toString());
    }
  }
}
