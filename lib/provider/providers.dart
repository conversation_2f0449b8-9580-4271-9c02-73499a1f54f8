import 'package:e8_hr_portal/provider/add_device_provider.dart';
import 'package:e8_hr_portal/provider/attendance_provider.dart';
import 'package:e8_hr_portal/provider/badge_provider.dart';
import 'package:e8_hr_portal/provider/birthday_anniversary_provider.dart';
import 'package:e8_hr_portal/provider/ble_attendance_provider.dart';
import 'package:e8_hr_portal/provider/chat_provider.dart';
import 'package:e8_hr_portal/provider/enhanced_chat_provider.dart';
import 'package:e8_hr_portal/provider/checklist_provider.dart';
import 'package:e8_hr_portal/provider/company_activities_provider.dart';
import 'package:e8_hr_portal/provider/driving_license_provider.dart';
import 'package:e8_hr_portal/provider/experience_certifcate_provider.dart';
import 'package:e8_hr_portal/provider/faq_provider.dart';
import 'package:e8_hr_portal/provider/flight_ticket_provider.dart';
import 'package:e8_hr_portal/provider/forgot_password_provider.dart';
import 'package:e8_hr_portal/provider/general_provider.dart';
import 'package:e8_hr_portal/provider/log_time_provider.dart';
import 'package:e8_hr_portal/provider/master_provider.dart';
import 'package:e8_hr_portal/provider/medical_insurance_provider.dart';
import 'package:e8_hr_portal/provider/book_meeting_room_provider.dart';
import 'package:e8_hr_portal/provider/booked_meeting_room_provider.dart';
import 'package:e8_hr_portal/provider/meeting_room_provider.dart';
import 'package:e8_hr_portal/provider/noc_provider.dart';
import 'package:e8_hr_portal/provider/notification_provider.dart';
import 'package:e8_hr_portal/provider/profile_provider.dart';
import 'package:e8_hr_portal/provider/punchin_provider.dart';
import 'package:e8_hr_portal/provider/reimbursment_provider.dart';
import 'package:e8_hr_portal/provider/request_chat_provider.dart';
import 'package:e8_hr_portal/provider/salary_certificate_provider.dart';
import 'package:e8_hr_portal/provider/salary_transfer_letter_provider.dart';
import 'package:e8_hr_portal/provider/search_provider.dart';
import 'package:e8_hr_portal/provider/settings_provider.dart';
import 'package:e8_hr_portal/provider/sign_in_provider.dart';
import 'package:e8_hr_portal/provider/team_members_provider.dart';
import 'package:e8_hr_portal/provider/theme_provider.dart';
import 'package:e8_hr_portal/provider/tickets_provider.dart';
import 'package:e8_hr_portal/provider/updates_provider.dart';
import 'package:e8_hr_portal/provider/user_provider.dart';
import 'package:e8_hr_portal/provider/user_status_provider.dart';
import 'package:e8_hr_portal/provider/version_provider.dart';
import 'package:e8_hr_portal/provider/wfh_provider.dart';
import 'package:e8_hr_portal/view/leave%20applications/provider/leave_apllication_provider.dart';
import 'package:e8_hr_portal/view/morning_huddle/providers/morning_huddle_provider.dart';
import 'package:e8_hr_portal/view/wfh/provider/wfh_overview_provider.dart';
import 'package:e8_hr_portal/view/wfh/provider/wfh_requests_provider.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

import 'camera_provider.dart';
import 'date_picker_provider.dart';

List<SingleChildWidget> providers = [
  ChangeNotifierProvider(create: (context) => SearchProvider()),
  ChangeNotifierProvider(create: (context) => ChatProvider()),
  ChangeNotifierProvider(create: (context) => EnhancedChatProvider()),
  ChangeNotifierProvider(create: (context) => CompanyActivitiesProvider()),
  ChangeNotifierProvider(create: (context) => FAQProvider()),
  ChangeNotifierProvider(create: (context) => ForgotPasswordProvider()),
  ChangeNotifierProvider(create: (context) => LeaveApplicationProvider()),
  ChangeNotifierProvider(create: (context) => MasterProvider()),
  ChangeNotifierProvider(create: (context) => NotificationProvider()),
  ChangeNotifierProvider(create: (context) => ProfileProvider()),
  ChangeNotifierProvider(create: (context) => SettingsProvider()),
  ChangeNotifierProvider(create: (context) => SignInProvider()),
  ChangeNotifierProvider(create: (context) => TeamMembersProvider()),
  ChangeNotifierProvider(create: (context) => UpdatesProvider()),
  ChangeNotifierProvider(create: (context) => UserProvider()),
  ChangeNotifierProvider(create: (context) => UserStatusProvider()),
  ChangeNotifierProvider(create: (context) => RequestChatProvider()),
  ChangeNotifierProvider(create: (context) => MedicalInsuranceProvider()),
  ChangeNotifierProvider(create: (context) => FlightTicketProvider()),
  ChangeNotifierProvider(create: (context) => BirthdayAnniversaryProvider()),
  ChangeNotifierProvider(create: (context) => SalaryTransferLetterProvider()),
  ChangeNotifierProvider(create: (context) => SalaryCertificateProvider()),
  ChangeNotifierProvider(create: (context) => ExperienceCertifcateProvider()),
  ChangeNotifierProvider(create: (context) => NocProvider()),
  ChangeNotifierProvider(create: (context) => DrivingLicenseProvider()),
  ChangeNotifierProvider(create: (context) => GeneralProvider()),
  ChangeNotifierProvider(create: (context) => TicketsProvider()),
  ChangeNotifierProvider(create: (context) => AttendanceProvider()),
  ChangeNotifierProvider(create: (context) => MeetingRoomProvider()),
  ChangeNotifierProvider(create: (context) => BookMeetingRoomProvider()),
  ChangeNotifierProvider(create: (context) => BookedMeetingRoomProvider()),
  ChangeNotifierProvider(create: (context) => LogTimeProvider()),
  ChangeNotifierProvider(create: (context) => BadgeProvider()),
  ChangeNotifierProvider(create: (context) => BLEAttendanceProvider()),
  ChangeNotifierProvider(create: (context) => PunchInProvider()),
  ChangeNotifierProvider(create: (context) => ThemeProvider()),
  ChangeNotifierProvider(create: (context) => AddDeviceProvider()),
  ChangeNotifierProvider(create: (context) => WFHProvider()),
  ChangeNotifierProvider(create: (context) => ReimbursementProvider()),
  ChangeNotifierProvider<ChecklistProvider>(
      create: (context) => ChecklistProvider()),
  ChangeNotifierProvider<DatePickerProvider>(
      create: (context) => DatePickerProvider()),
  ChangeNotifierProvider<CameraProvider>(create: (context) => CameraProvider()),
  ChangeNotifierProvider<VersionProvider>(
      create: (context) => VersionProvider()),
  ChangeNotifierProvider(create: (context) => WfhOverviewProvider()),
  ChangeNotifierProvider(create: (context) => WfhRequestsProvider()),
  ChangeNotifierProvider<MorningHuddleProvider>(
      create: (context) => MorningHuddleProvider()),
];
