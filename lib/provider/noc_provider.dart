import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/model/noc_certificate_list.dart';
import 'package:e8_hr_portal/model/noc_purpose_model.dart';
import 'package:e8_hr_portal/util/urls.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../util/dailoge.dart';
import '../util/date_formatter.dart';

class NocProvider extends ChangeNotifier {
  final RefreshController refreshController =
      RefreshController(initialRefresh: false);

  void onRefresh() async {
    await getNocCertificateList();
    refreshController.refreshCompleted();
  }

  bool _isSalaryIncluded = false;
  bool get isSalaryIncluded => _isSalaryIncluded;
  set isSalaryIncluded(bool value) {
    _isSalaryIncluded = value;
    notifyListeners();
  }

  final List<NocPurposeModel> nocPurposeList = [
    NocPurposeModel('business_purpose', 2, 'Business'),
    NocPurposeModel('tourism_purpose', 1, 'Tourism'),
  ];
  String? _selectedPurpose;
  String? get selectedPurpose => _selectedPurpose;
  set selectedPurpose(String? value) {
    _selectedPurpose = value;
    notifyListeners();
  }

  String? _selectedDesignationAsPer;
  String? get selectedDesignationAsPer => _selectedDesignationAsPer;
  set selectedDesignationAsPer(String? value) {
    _selectedDesignationAsPer = value;
    notifyListeners();
  }

  final List<Map<String, String>> designationAsPerList = [
    {'name': 'As per visa', 'action': 'as_per_visa'},
    {'name': 'As per profile', 'action': 'as_per_profile'}
  ];

  final List<NocNationalityModel> nocNationalityList = [
    NocNationalityModel('Bahrain', 1),
    NocNationalityModel('Kuwait', 2),
    NocNationalityModel('Oman', 3),
    NocNationalityModel('Qatar', 4),
    NocNationalityModel('Saudi Arabia', 5),
    NocNationalityModel('United Arab Emirates', 6),
  ];
  String? _selectedNationality;
  String? get selectedNationality => _selectedNationality;
  set selectedNationality(String? value) {
    _selectedNationality = value;

    notifyListeners();
  }

  final List<NocDesignationModel> nocDesignationList = [
    NocDesignationModel('Employee', 1),
    NocDesignationModel('HR Admin', 2),
    NocDesignationModel('Manager', 3),
  ];
  String? _selectedDesignation;
  String? get selectedDesignation => _selectedDesignation;
  set selectedDesignation(String? value) {
    _selectedDesignation = value;
    notifyListeners();
  }

  DateTime _selectedFromDate = DateTime.now();
  DateTime get selectedFromDate => _selectedFromDate;
  set selectedFromDate(DateTime value) {
    _selectedFromDate = value;

    notifyListeners();
  }

  DateTime _selectedToDate = DateTime.now();
  DateTime get selectedToDate => _selectedToDate;
  set selectedToDate(DateTime value) {
    _selectedToDate = value;
    notifyListeners();
  }

  DateTime _selectedReturnDate = DateTime.now();
  DateTime get selectedReturnDate => _selectedReturnDate;
  set selectedReturnDate(DateTime value) {
    _selectedReturnDate = value;
    notifyListeners();
  }

  String _formattedFromDate =
      formatDateFromDate(dateTime: DateTime.now(), format: 'dd MMM yyyy');
  String get formattedFromDate => _formattedFromDate;
  set formattedFromDate(String value) {
    _formattedFromDate = value;
    notifyListeners();
  }

  String _formattedToDate =
      formatDateFromDate(dateTime: DateTime.now(), format: 'dd MMM yyyy');
  String get formattedToDate => _formattedToDate;
  set formattedToDate(String value) {
    _formattedToDate = value;
    notifyListeners();
  }

  String _formattedReturnDate =
      formatDateFromDate(dateTime: DateTime.now(), format: 'dd MMM yyyy');
  String get formattedReturnDate => _formattedReturnDate;
  set formattedReturnDate(String value) {
    _formattedReturnDate = value;
    notifyListeners();
  }

  Future<void> selectFromDate({required BuildContext context}) async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(3100),
    );
    if (picked != null && picked != selectedFromDate) {
      selectedFromDate = picked;
      selectedToDate = picked;
      selectedReturnDate = picked;
      formattedFromDate =
          formatDateFromDate(dateTime: selectedFromDate, format: 'dd MMM yyyy');
      _calculateDaysBetween();
    }
  }

  Future<void> selectToDate({required BuildContext context}) async {
    // var now = DateTime.now();
    // var tomorrow = now.add(const Duration(days: 1));
    // if (selectedFromDate != null) {
    //   tomorrow = selectedFromDate!.add(const Duration(days: 1));
    // }
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedFromDate,
      firstDate: selectedFromDate,
      lastDate: DateTime(3100),
    );
    if (picked != null) {
      selectedToDate = picked;
      selectedReturnDate = picked;
      // formattedToDate =
      //     formatDateFromDate(dateTime: selectedToDate!, format: 'dd MMM yyyy');
      _calculateDaysBetween();
    }
  }

  Future<void> selectReturnDate({required BuildContext context}) async {
    // var tomorrow = now.add(const Duration(days: 1));
    // if (selectedFromDate != null) {
    //   tomorrow = selectedFromDate!.add(const Duration(days: 1));
    // }
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedToDate,
      firstDate: selectedToDate,
      lastDate: DateTime(3100),
    );
    if (picked != null) {
      selectedReturnDate = picked;
      // formattedReturnDate = formatDateFromDate(
      //     dateTime: selectedReturnDate!, format: 'dd MMM yyyy');
      // _calculateDaysBetween();
    }
  }

  int _daysBetween = 0;
  int get daysBetween => _daysBetween;
  set daysBetween(int value) {
    _daysBetween = value;
    notifyListeners();
  }

  int _daysBetweenPlus = 0;
  int get daysBetweenPlus => _daysBetweenPlus;
  set daysBetweenPlus(int value) {
    _daysBetweenPlus = value;
    notifyListeners();
  }

  void _calculateDaysBetween() {
    daysBetween = 0;
    daysBetweenPlus = 0;
    if (selectedFromDate.isBefore(selectedToDate)) {
      final duration = selectedToDate.difference(selectedFromDate);
      daysBetween = duration.inDays;
      daysBetweenPlus = daysBetween;
    }
  }

  Future<bool> nocCreate({
    required BuildContext context,
    required String purpose,
    required String to,
    required String placeOfTravel,
    required String entryVisa,
    required String travelCountry,
    String? fromDate,
    String? toDate,
    String? officeReturnDate,
  }) async {
    final navigator = Navigator.of(context);
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');

    Map<String, String>? body;
    if (purpose == 'tourism_purpose') {
      body = {
        'purpose': purpose,
        'to': to,
        'placeoftravel': placeOfTravel,
        'entry_visa': entryVisa,
        'travel_country': travelCountry,
        'from_date': fromDate ?? '',
        'to_date': toDate ?? '',
        'office_return_date': officeReturnDate ?? '',
        'designation': selectedDesignationAsPer.toString()
      };
    } else if (purpose == 'business_purpose') {
      body = {
        'purpose': purpose,
        'to': to,
        'placeoftravel': placeOfTravel,
        'entry_visa': entryVisa,
        'travel_country': travelCountry,
        'designation': selectedDesignationAsPer.toString()
      };
    }
    var response = await http.post(nocCreateURL,
        body: body, headers: {'Authorization': 'Bearer $auth'});

    if (response.statusCode == 200) {
      Map<String?, dynamic> json = jsonDecode(response.body);

      if (json['result'] == 'success') {
        if (json.containsKey('message')) {
          showToastText(json['message']);
          navigator.pop();
          navigator.pop();
        }
        return true;
      }
    } else if (response.statusCode == 400) {
      Map<String?, dynamic> json = jsonDecode(response.body);
      if (json['result'] == 'failure') {
        if (json.containsKey('erros')) {
          Map<String, dynamic> erros = json['erros'];
          if (erros.containsKey('request')) {
            showToastText(erros['request']);
          } else if (erros.containsKey('profile')) {
            showToastText(erros['profile']);
          }
        }
        return false;
      }
    }
    return false;
  }

  List<NocCertificateListModel> _nocCertificateList = [];
  List<NocCertificateListModel> get nocCertificateList => _nocCertificateList;
  set nocCertificateList(List<NocCertificateListModel> value) {
    _nocCertificateList = value;
    notifyListeners();
  }

  Future<bool> getNocCertificateList() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    try {
      if (nocCertificateList.isEmpty) {
        EasyLoading.show();
      }
      var response = await http.get(nocCertificateListURL,
          headers: {'Authorization': 'Bearer $auth'});

      if (response.statusCode == 200) {
        EasyLoading.dismiss();
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          if (json.containsKey('records')) {
            List records = json['records'];
            nocCertificateList = records
                .map((e) => NocCertificateListModel.fromJson(e))
                .toList();
            return true;
          }
        }
      }
      notifyListeners();
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint(e.toString());
    }
    return false;
  }

  Future<bool> nocCommentCreate({
    required BuildContext context,
    required String action,
    required int actionId,
    required String comment,
  }) async {
    final navigator = Navigator.of(context);
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');

    Map<String, String>? body = {
      'action': action,
      'action_id': actionId.toString(),
      'comment': comment,
    };
    var response = await http.post(commentsCreateURL,
        body: body, headers: {'Authorization': 'Bearer $auth'});

    if (response.statusCode == 200) {
      Map<String?, dynamic> json = jsonDecode(response.body);

      if (json['result'] == 'success') {
        if (json.containsKey('message')) {
          showToastText(json['message']);
          navigator.pop();
          navigator.pop();
        }
        return true;
      }
    }
    if (response.statusCode == 400) {
      Map<String?, dynamic> json = jsonDecode(response.body);
      if (json['result'] == 'failure') {
        if (json.containsKey('erros')) {
          Map<String, dynamic> erros = json['erros'];
          if (erros.containsKey('request')) {
            showToastText(erros['request']);
          }
        }
        return false;
      }
    }
    return false;
  }

  String? _nocCertificate;
  String? get nocCertificate => _nocCertificate;
  Future<bool> getNocCertificate(
      {required String action, required int actionId}) async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    String? auth = shared.getString('access_token');
    try {
      Uri nocCertificateURL = Uri.parse(
          '${baseUrl}certificates-view/?action=$action&action_id=$actionId');
      var response = await http
          .get(nocCertificateURL, headers: {'Authorization': 'Bearer $auth'});
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['result'] == 'success') {
          if (json.containsKey('records')) {
            Map<String, dynamic> records = json['records'];
            if (records.containsKey('file')) {
              _nocCertificate = null;
              _nocCertificate = records['file'];
            }
          }
          return true;
        }
      }
      if (response.statusCode == 400) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['result'] == 'failure') {
          _nocCertificate = null;
          if (json.containsKey('errors')) {
            showToastText(json['errors']);
          }
          return false;
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return false;
  }

  DateTime? _dateTime;
  DateTime? get dateTime => _dateTime;
  set dateTime(DateTime? value) {
    _dateTime = value;
    notifyListeners();
  }

  Future<void> selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: dateTime ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != dateTime) {
      dateTime = picked;
    }
  }
}
