import 'dart:async';
import 'package:camera/camera.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:e8_hr_portal/provider/theme_provider.dart';
import 'package:e8_hr_portal/util/custom_purple_blue_theme.dart';
import 'package:e8_hr_portal/util/custom_yellow_theme.dart';
import 'package:e8_hr_portal/util/fcm.dart';
import 'package:e8_hr_portal/util/theme_type.dart';
import 'package:e8_hr_portal/view/splash_screen/splash_screen.dart';
import 'package:e8_hr_portal/services/secure_key_manager.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:e8_hr_portal/provider/providers.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/widgets/no_network_screen.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:bot_toast/bot_toast.dart';

bool isDemo = true; //check whether it is live or demo
List<CameraDescription> cameras = [];
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  FCM.requestPermission();
  cameras = await availableCameras();

  // Test secure storage availability
  await SecureKeyManager.testSecureStorage();

  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };
  configLoading();
  runApp(
    MultiProvider(
      providers: providers,
      child: const MyApp(),
    ),
  );
}

void configLoading() {
  EasyLoading.instance
    ..loadingStyle = EasyLoadingStyle.custom
    ..backgroundColor = Colors.white
    ..maskColor = Colors.white
    ..indicatorColor = ThemeColors.primaryColor
    ..userInteractions = false
    ..dismissOnTap = false
    ..textColor = Colors.transparent
    ..contentPadding = const EdgeInsets.all(8)
    ..textPadding = EdgeInsets.zero
    ..indicatorType = EasyLoadingIndicatorType.ring
    ..indicatorSize = 23
    ..lineWidth = 2.2
    ..radius = 20
    ..boxShadow = <BoxShadow>[
      const BoxShadow(
          offset: Offset(2, 2),
          blurRadius: 10,
          color: Color.fromRGBO(0, 0, 0, .15))
    ];
}

class NavigationService {
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final ThemeData theme = ThemeData(
    fontFamily: 'SFProDisplay',
  );
  final Connectivity _connectivity = Connectivity();
  List<ConnectivityResult> _connectionStatus = [ConnectivityResult.none];
  final Connectivity _connectivitys = Connectivity();
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  bool noNetwork = false;
  @override
  void initState() {
    initConnectivity();
    initConnectivity();

    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    super.initState();
  }

  @override
  void dispose() {
    _connectivitySubscription.cancel();
    super.dispose();
  }

  Future<void> initConnectivity() async {
    late List<ConnectivityResult> result;
    try {
      result = await _connectivity.checkConnectivity();
    } on PlatformException catch (e) {
      debugPrint(e.toString());
      return;
    }
    if (!mounted) {
      return Future.value(null);
    }

    return await _updateConnectionStatus(result);
  }

  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    final isOffline = result.contains(ConnectivityResult.none);

    if (isOffline && !noNetwork) {
      NavigationService.navigatorKey.currentState?.push(
        MaterialPageRoute(
          builder: (context) => const NoConnectivityPage(),
        ),
      );
      setState(() => noNetwork = true);
    } else if (!isOffline && noNetwork) {
      NavigationService.navigatorKey.currentState!.pop();
      setState(() => noNetwork = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) => OrientationBuilder(
        builder: (context, orientation) {
          SizeConfig(
            designScreenWidth: 375,
            designScreenHeight: 812,
          ).init(constraints, orientation);
          return MaterialApp(
            title: 'HR Connect',
            debugShowCheckedModeBanner: false,
            navigatorKey: NavigationService.navigatorKey,
            builder: EasyLoading.init(builder: BotToastInit()),
            navigatorObservers: [BotToastNavigatorObserver()],
            theme: ThemeData(
              useMaterial3: false,
              appBarTheme: AppBarTheme(
                backgroundColor: ThemeColors.primaryColor,
                iconTheme: const IconThemeData(
                  color: Colors.black,
                ),
                centerTitle: false,
                titleTextStyle: GoogleFonts.rubik(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                ),
              ),
              colorScheme: ColorScheme.fromSwatch(
                accentColor: ThemeColors.secondaryColor,
                primarySwatch: ThemeColors.prime,
              ),
              fontFamily: GoogleFonts.rubik().fontFamily,
              primaryColor: ThemeColors.primaryColor,
              primarySwatch: Colors.blue,
              scaffoldBackgroundColor: ThemeColors.scaffoldBgColor,
            ),
            home: _homeWidget(),
          );
        },
      ),
    );
  }

  Widget? _homeWidget() {
    if (noNetwork) {
      return const NoConnectivityPage();
    }
    return const SplashScreen();
  }

  ThemeData getTheme() {
    final provider = context.watch<ThemeProvider>();
    switch (provider.selectedTheme) {
      case ThemeType.yellowTheme:
        return CustomYellowTheme.theme;
      case ThemeType.purpleBlueTheme:
        return CustomPurpleBlueTheme.theme;
    }
  }
}

class CustomAnimation extends EasyLoadingAnimation {
  CustomAnimation();

  @override
  Widget buildWidget(
    Widget child,
    AnimationController controller,
    AlignmentGeometry alignment,
  ) {
    return Opacity(
      opacity: controller.value,
      child: RotationTransition(
        turns: controller,
        child: child,
      ),
    );
  }
}
