class WFHMemberModel {
  String? employeeId;
  String? designationTitle;
  String? profilePic;
  String? fullName;
  String? email;
  List<AttLog>? attLog;

  WFHMemberModel(
      {this.employeeId,
      this.designationTitle,
      this.profilePic,
      this.fullName,
      this.email,
      this.attLog});

  WFHMemberModel.fromJson(Map<String, dynamic> json) {
    employeeId = json['employee_id'].toString();
    designationTitle = json['designation_title'];
    profilePic = json['profile_pic'];
    fullName = json['full_name'];
    email = json['email'];
    if (json['att_log'] != null) {
      attLog = <AttLog>[];
      json['att_log'].forEach((v) {
        attLog!.add(AttLog.fromJson(v));
      });
    }
  }
}

class AttLog {
  String? date;
  String? time;
  String? status;
  String? latitude;
  String? longitude;
  String? userImg;

  AttLog(
      {this.date,
      this.time,
      this.status,
      this.latitude,
      this.longitude,
      this.userImg});

  AttLog.fromJson(Map<String, dynamic> json) {
    date = json['date'];
    time = json['time'];
    status = json['status'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    userImg = json['user_img'];
  }
}
