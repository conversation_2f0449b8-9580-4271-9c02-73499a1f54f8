class EmpActionStatusModel {
  int? id;
  String? name;
  bool? isActive;
  String? createdAt;
  String? updatedAt;
  List<SubAction>? subAction;
  bool? isShowActions;
  EmpActionStatusModel({
    this.id,
    this.name,
    this.isActive,
    this.createdAt,
    this.updatedAt,
    this.subAction,
    this.isShowActions,
  });

  EmpActionStatusModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    if (json['sub_action'] != null) {
      subAction = <SubAction>[];
      json['sub_action'].forEach((v) {
        subAction!.add(SubAction.fromJson(v));
      });
    }
  }
}

class SubAction {
  String? key;
  String? value;
  bool? isActive;
  SubAction({this.key, this.value, this.isActive});

  SubAction.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
  }
}
