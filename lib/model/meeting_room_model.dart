// import 'package:e8_hr_portal/model/amenities_model.dart';

class MeetingRoomModel {
  String? id;
  String? name;
  int? capacity;
  List<dynamic> images = [];
  // List<AmenitiesModel> amenities = [];

  MeetingRoomModel.fromJson({
    required Map<String, dynamic> json,
    required String this.id,
  }) {
    name = json['name'];
    capacity = json['capacity'];
    images = json['images'];
    // if (json.containsKey('amenities')) {
    //   List amenityList = json['amenities'];
    //   amenities = amenityList.map((e) => AmenitiesModel.fromJson(e)).toList();
    // }
  }
}
