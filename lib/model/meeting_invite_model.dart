import 'package:cloud_firestore/cloud_firestore.dart';

class MeetingInviteModel {
  String? id;
  Timestamp? bookingDate;
  Timestamp? startTime;
  Timestamp? endTime;
  String? roomName;
  String? roomId;
  String? roomImage;
  String? status;
  String? user;
  String? uid;
  String? userImage;
  String? ownerName;
  String? ownerUid;
  String? ownerImage;
  String? inviteStatus;
  String? meetingId;
  List<dynamic> invitedColleagueList = [];
  Timestamp? saveTime;
  MeetingInviteModel.fromJson(
      {required Map<String, dynamic> json, required this.id}) {
    bookingDate = json['booking_date'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    roomName = json['room_name'];
    roomId = json['room_id'];
    roomImage = json['room_image'];
    status = json['status'];
    user = json['user'];
    uid = json['uid'];
    userImage = json['user_image'];
    ownerName = json['owner_name'];
    ownerUid = json['owner_uid'];
    ownerImage = json['owner_image'];
    inviteStatus = json['invite_status'];
    saveTime = json['save_time'];
    invitedColleagueList = json['invited_colleagues'];
  }
}
