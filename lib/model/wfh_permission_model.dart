class WFHPermissonModel {
  int? userId;
  String? fullName;
  String? designation;
  String? email;
  String? profilePic;
  bool? wfhStatus;
  bool? regularLeaveRequests;

  WFHPermissonModel({
    this.userId,
    this.fullName,
    this.designation,
    this.email,
    this.profilePic,
    this.wfhStatus,
    this.regularLeaveRequests,
  });

  WFHPermissonModel.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    fullName = json['full_name'];
    designation = json['designation'];
    email = json['email'];
    profilePic = json['profile_pic'];
    wfhStatus = json['wfh_status'];
    regularLeaveRequests = json['regular_leave_requests'];
  }
}
