class LeaveRequestViewModel {
  LeaveDetails? leaveDetails;
  UserDetails? staffIncharge;
  HrStatus? hrStatus;
  List<ReportingPersonList>? reportingPersonList;

  LeaveRequestViewModel(
      {this.leaveDetails,
      this.staffIncharge,
      this.hrStatus,
      this.reportingPersonList});

  LeaveRequestViewModel.fromJson(Map<String, dynamic> json) {
    leaveDetails = json['leave_details'] != null
        ? LeaveDetails.fromJson(json['leave_details'])
        : null;
    staffIncharge = json['staff_incharge'] != null
        ? UserDetails.fromJson(json['staff_incharge'])
        : null;
    hrStatus =
        json['hr_status'] != null ? HrStatus.fromJson(json['hr_status']) : null;
    if (json['reporting_person_list'] != null) {
      reportingPersonList = <ReportingPersonList>[];
      json['reporting_person_list'].forEach((v) {
        reportingPersonList!.add(ReportingPersonList.fromJson(v));
      });
    }
  }
}

class LeaveDetails {
  UserDetails? userDetails;
  int? id;
  String? startDate;
  String? endDate;
  String? status;
  String? dayType;
  String? reason;
  String? leaveType;
  num? dayCount;
  String? leaveDoc;
  String? docExtension;
  String? createdAt;
  String? leaveLevel;
  String? leavePermission;
  List<ReportingPersonsActions>? reportingPersonsActions;

  LeaveDetails(
      {this.userDetails,
      this.id,
      this.startDate,
      this.endDate,
      this.status,
      this.dayType,
      this.reason,
      this.leaveType,
      this.dayCount,
      this.leaveDoc,
      this.docExtension,
      this.createdAt,
      this.leaveLevel,
      this.leavePermission,
      this.reportingPersonsActions});

  LeaveDetails.fromJson(Map<String, dynamic> json) {
    userDetails = json['user_details'] != null
        ? UserDetails.fromJson(json['user_details'])
        : null;
    id = json['id'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    status = json['status'];
    dayType = json['day_type'];
    reason = json['reason'];
    leaveType = json['leave_type'];
    dayCount = json['day_count'];
    leaveDoc = json['leave_doc'];
    docExtension = json['doc_extension'];
    createdAt = json['created_at'];
    leaveLevel = json['leave_level'];
    leavePermission = json['leave_permission'];
    // if (json['reporting_persons_actions'] != null) {
    //   reportingPersonsActions = json['reporting_persons_actions'];
    // } else {
    //   reportingPersonsActions = [];
    // }
  }
}

class HrStatus {
  int? id;
  String? name;
  List<Role>? role;
  String? profilePic;
  String? isApprove;
  String? comment;
  String? createdAt;

  HrStatus(
      {this.id,
      this.name,
      this.role,
      this.profilePic,
      this.isApprove,
      this.comment,
      this.createdAt});

  HrStatus.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    if (json['role'] != null) {
      role = <Role>[];
      json['role'].forEach((v) {
        role!.add(Role.fromJson(v));
      });
    }
    profilePic = json['profile_pic'];
    isApprove = json['is_approve'];
    comment = json['comment'];
    createdAt = json['created_at'];
  }
}

class ReportingPersonsActions {
  String? name;
  bool? isApprove;
  String? profilePic;
  List<Role>? role;

  ReportingPersonsActions(
      {this.name, this.isApprove, this.profilePic, this.role});

  ReportingPersonsActions.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    isApprove = json['is_approve'];
    profilePic = json['profile_pic'];
    if (json['role'] != null) {
      role = <Role>[];
      json['role'].forEach((v) {
        role!.add(Role.fromJson(v));
      });
    }
  }
}

class UserDetails {
  int? id;
  String? name;
  String? email;
  String? designation;
  String? profilePic;

  UserDetails(
      {this.id, this.name, this.email, this.designation, this.profilePic});

  UserDetails.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    designation = json['designation'];
    profilePic = json['profile_pic'];
  }
}

class ReportingPersonList {
  int? id;
  String? name;
  String? role;
  String? profilePic;
  String? isApprove;
  String? comment;
  String? createdAt;

  ReportingPersonList(
      {this.id,
      this.name,
      this.role,
      this.profilePic,
      this.isApprove,
      this.comment,
      this.createdAt});

  ReportingPersonList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    role = json['role'];
    profilePic = json['profile_pic'];
    isApprove = json['is_approve'];
    comment = json['comment'];
    createdAt = json['created_at'];
  }
}

class Role {
  String? name;

  Role({this.name});

  Role.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    return data;
  }
}
