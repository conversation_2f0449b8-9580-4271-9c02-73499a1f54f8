class TeamMemberModel {
  int? id;
  String? employeeId;
  String? name;
  String? nickName;
  String? email;
  String? profilePic;
  String? dateOfBirth;
  String? dateOfJoining;
  List<Designation>? designation;
  List<UserStatus>? userStatus;
  String? department;
  String? branch;
  ReportingPerson? reportingPerson;
  String? emergencyContact;
  List<Badge>? badge;

  TeamMemberModel(
      {this.id,
      this.employeeId,
      this.name,
      this.nickName,
      this.email,
      this.profilePic,
      this.dateOfBirth,
      this.dateOfJoining,
      this.designation,
      this.userStatus,
      this.department,
      this.branch,
      this.reportingPerson,
      this.emergencyContact,
      this.badge});

  TeamMemberModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    employeeId = json['employee_id'];
    name = json['name'];
    nickName = json['nick_name'];
    email = json['email'];
    profilePic = json['profile_pic'];
    dateOfBirth = json['date_of_birth'];
    dateOfJoining = json['date_of_joining'];
    if (json['designation'] != null) {
      designation = <Designation>[];
      json['designation'].forEach((v) {
        designation!.add(Designation.fromJson(v));
      });
    }
    if (json['user_status'] != null) {
      userStatus = <UserStatus>[];
      json['user_status'].forEach((v) {
        userStatus!.add(UserStatus.fromJson(v));
      });
    }
    department = json['department'];
    branch = json['branch'];
    reportingPerson = json['reporting_person'] != null
        ? ReportingPerson.fromJson(json['reporting_person'])
        : null;
    emergencyContact = json['emergency_contact'];
    if (json['badge'] != null) {
      badge = <Badge>[];
      json['badge'].forEach((v) {
        badge!.add(Badge.fromJson(v));
      });
    }
  }
}

class Designation {
  int? id;
  String? name;

  Designation({this.id, this.name});

  Designation.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }
}

class UserStatus {
  int? id;
  String? name;
  bool? isActive;
  String? createdAt;
  String? updatedAt;
  String? subAction;
  UserStatus(
      {this.id,
      this.name,
      this.isActive,
      this.createdAt,
      this.updatedAt,
      this.subAction});

  UserStatus.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    subAction = json['sub_action'];
  }
}

class ReportingPerson {
  int? id;
  String? name;
  String? email;
  String? designation;
  String? profilePic;

  ReportingPerson(
      {this.id, this.name, this.email, this.designation, this.profilePic});

  ReportingPerson.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    designation = json['designation'];
    profilePic = json['profile_pic'];
  }
}

class Badge {
  int? badgeId;
  String? badgeName;
  String? badgeImg;
  int? achievedCount;

  Badge({this.badgeId, this.badgeName, this.badgeImg, this.achievedCount});

  Badge.fromJson(Map<String, dynamic> json) {
    badgeId = json['badge_id'];
    badgeName = json['badge_name'];
    badgeImg = json['badge_img'];
    achievedCount = json['achieved_count'];
  }
}
