class FlightTicketRequstListModel {
  int? id;
  String? sender;
  String? requestedDate;
  String? approvedBy;
  String? status;
  String? approvedOrRejectedDate;
  String? deperatureDate;
  String? returnDate;
  String? noOfDays;
  String? countryOfDeparture;
  String? cityOfDeperature;
  String? countryOfArrival;
  String? cityOfArrival;
  String? airline;
  bool? isSeeMore;
  String? rejectedComment;
  String? remark;
  String? empId;
  String? email;
  String? profilePic;
  List<TktData>? tktData;
  List<TktUploadData>? tktUploadData;
  List<ApprvRejData>? apprvRejData;

  FlightTicketRequstListModel(
      {this.id,
      this.sender,
      this.requestedDate,
      this.approvedBy,
      this.status,
      this.approvedOrRejectedDate,
      this.deperatureDate,
      this.returnDate,
      this.noOfDays,
      this.countryOfDeparture,
      this.cityOfDeperature,
      this.countryOfArrival,
      this.cityOfArrival,
      this.airline,
      this.isSeeMore,
      this.rejectedComment,
      this.remark,
      this.empId,
      this.email,
      this.profilePic,
      this.tktData,
      this.tktUploadData,
      this.apprvRejData});

  FlightTicketRequstListModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    sender = json['sender'];
    requestedDate = json['requested_date'];
    approvedBy = json['approved_by'];
    status = json['status'];
    approvedOrRejectedDate = json['approved_or_rejected_date'];
    deperatureDate = json['deperature_date'];
    returnDate = json['return_date'];
    noOfDays = json['no_of_days'];
    countryOfDeparture = json['country_of_departure'];
    cityOfDeperature = json['city_of_deperature'];
    countryOfArrival = json['country_of_arrival'];
    cityOfArrival = json['city_of_arrival'];
    airline = json['airline'];
    isSeeMore = json['is_see_more'];
    rejectedComment = json['rejected_comment'];
    remark = json['remark'];
    empId = json["employee_id"];
    email = json["emp_email"];
    if (json["emp_profile_pic"] != null) profilePic = json["emp_profile_pic"];
    if (json['tkt_data'] != null) {
      tktData = <TktData>[];
      json['tkt_data'].forEach((v) {
        tktData!.add(TktData.fromJson(v));
      });
    }
    if (json['tkt_upload_data'] != null) {
      tktUploadData = <TktUploadData>[];
      json['tkt_upload_data'].forEach((v) {
        tktUploadData!.add(TktUploadData.fromJson(v));
      });
    }

    if (json['apprv_rej_data'] != null) {
      apprvRejData = <ApprvRejData>[];
      json['apprv_rej_data'].forEach((v) {
        apprvRejData!.add(ApprvRejData.fromJson(v));
      });
    }
  }
}

class TktData {
  int? id;
  String? title;
  String? fileType;
  String? file;

  TktData({this.id, this.title, this.fileType, this.file});

  TktData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    fileType = json['file_type'];
    file = json['file'];
  }
}

class TktUploadData {
  int? id;
  String? title;
  String? fileType;
  String? file;

  TktUploadData({this.id, this.title, this.fileType, this.file});

  TktUploadData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    fileType = json['file_type'];
    file = json['file'];
  }
}

class ApprvRejData {
  int? id;
  int? userId;
  String? userName;
  String? levelStatus;
  String? levelType;
  String? requestedDate;
  String? remark;
  String? profilePic;

  ApprvRejData(
      {this.id,
      this.userId,
      this.userName,
      this.levelStatus,
      this.levelType,
      this.requestedDate,
      this.remark,
      this.profilePic});

  ApprvRejData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    userName = json['user_name'];
    levelStatus = json['level_status'];
    levelType = json['level_type'];
    requestedDate = json['requested_date'];
    remark = json['remark'];
    profilePic = json['profile_pic'];
  }
}
