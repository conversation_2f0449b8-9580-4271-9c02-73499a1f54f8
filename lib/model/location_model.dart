class LocationModel {
  int? id;
  String? latitude;
  String? longitude;
  String? locationName;
  String? description;
  String? requestedDate;
  String? status;
  String? reason;
  AddedBy? addedBy;

  LocationModel(
      {this.id,
      this.latitude,
      this.longitude,
      this.locationName,
      this.description,
      this.requestedDate,
      this.status,
      this.reason,
      this.addedBy});

  LocationModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    locationName = json['location_name'];
    description = json['description'];
    requestedDate = json['requested_date'];
    status = json['status'];
    reason = json['reason'];
    addedBy =
        json['added_by'] != null ? AddedBy.fromJson(json['added_by']) : null;
  }
}

class AddedBy {
  String? name;
  String? designation;
  String? profilePic;

  AddedBy({this.name, this.designation, this.profilePic});

  AddedBy.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    designation = json['designation'];
    profilePic = json['profile_pic'];
  }
}
