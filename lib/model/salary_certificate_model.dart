class SalaryCertificateModel {
  int? id;
  String? requestedDate;
  String? approvedBy;
  String? status;
  String? approvedOrRejectedDate;
  String? employeeComment;
  String? rejectedComment;

  SalaryCertificateModel(
      {this.id,
      this.requestedDate,
      this.approvedBy,
      this.status,
      this.approvedOrRejectedDate,
      this.employeeComment,
      this.rejectedComment});

  SalaryCertificateModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    requestedDate = json['requested_date'];
    approvedBy = json['approved_by'];
    status = json['status'];
    approvedOrRejectedDate = json['approved_or_rejected_date'];
    employeeComment = json['employee_comment'];
    rejectedComment = json['rejected_comment'];
  }
}
