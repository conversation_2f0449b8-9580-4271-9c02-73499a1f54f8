class HRReimbursementModel {
  int? reimbursementId;
  String? userName;
  String? userId;
  String? userEmpId;
  String? department;
  String? departmentId;
  String? designation;
  String? designationId;
  String? hrStatus;
  String? title;
  String? userProfilePic;
  List<LevelUsers>? levelUsers;

  HRReimbursementModel(
      {this.reimbursementId,
      this.userName,
      this.userId,
      this.userEmpId,
      this.department,
      this.departmentId,
      this.designation,
      this.designationId,
      this.hrStatus,
      this.levelUsers,
      this.title,
      this.userProfilePic});

  HRReimbursementModel.fromJson(Map<String, dynamic> json) {
    reimbursementId = json['reimbursement_id'];
    userName = json['user_name'];
    userId = json['user_id'];
    userEmpId = json['user_emp_id'];
    department = json['department'];
    departmentId = json['department_id'];
    designation = json['designation'];
    designationId = json['designation_id'];
    hrStatus = json['hr_status'];
    if (json['level_users'] != null) {
      levelUsers = <LevelUsers>[];
      json['level_users'].forEach((v) {
        levelUsers!.add(LevelUsers.fromJson(v));
      });
    }
    title = json['title'];
    userProfilePic = json['user_profile_pic'];
  }
}

class LevelUsers {
  int? id;
  String? approvalStatus;
  int? reimbursementLevel;
  String? addedBy;
  int? addedById;
  String? addedByProf;
  String? updatedAt;
  String? approvalStatusText;
  String? approveButton;

  LevelUsers(
      {this.id,
      this.approvalStatus,
      this.reimbursementLevel,
      this.addedBy,
      this.addedById,
      this.addedByProf,
      this.updatedAt,
      this.approvalStatusText,
      this.approveButton});

  LevelUsers.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    approvalStatus = json['approval_status'];
    reimbursementLevel = json['reimbursement_level'];
    addedBy = json['added_by'];
    addedById = json['added_by_id'];
    addedByProf = json['added_by_prof'];
    updatedAt = json['updated_at'];
    approvalStatusText = json['approval_status_text'];
    approveButton = json['approve_button'];
  }
}
