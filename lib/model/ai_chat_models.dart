/// Enhanced AI Chat Models for actionable HR conversations

/// Enum for different types of chat intents
enum ChatIntent {
  leaveApply,
  leaveCheck,
  wfhApply,
  wfhCheck,
  expenseSubmit,
  expenseTrack,
  meetingBook,
  meetingCheck,
  infoQuery,
  unknown,
}

/// Enum for leave types
enum LeaveType {
  sick,
  annual,
  emergency,
  maternity,
  paternity,
  unpaid,
}

/// Enum for WFH types
enum WFHType {
  fullDay,
  halfDay,
  multiDay,
}

/// Model for AI chat response with intent and parameters
class AIChatResponse {
  final String originalMessage;
  final String aiResponse;
  final ChatIntent intent;
  final double confidence;
  final bool requiresAction;
  final Map<String, dynamic> parameters;
  final DateTime timestamp;

  AIChatResponse({
    required this.originalMessage,
    required this.aiResponse,
    required this.intent,
    required this.confidence,
    required this.requiresAction,
    this.parameters = const {},
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'originalMessage': originalMessage,
      'aiResponse': aiResponse,
      'intent': intent.toString(),
      'confidence': confidence,
      'requiresAction': requiresAction,
      'parameters': parameters,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory AIChatResponse.fromJson(Map<String, dynamic> json) {
    return AIChatResponse(
      originalMessage: json['originalMessage'] ?? '',
      aiResponse: json['aiResponse'] ?? '',
      intent: ChatIntent.values.firstWhere(
        (e) => e.toString() == json['intent'],
        orElse: () => ChatIntent.unknown,
      ),
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      requiresAction: json['requiresAction'] ?? false,
      parameters: Map<String, dynamic>.from(json['parameters'] ?? {}),
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
    );
  }
}

/// Model for chat messages in conversation history
class AIChatMessage {
  final String content;
  final bool isUser;
  final DateTime timestamp;
  final ChatIntent? intent;
  final bool? requiresAction;

  AIChatMessage({
    required this.content,
    required this.isUser,
    DateTime? timestamp,
    this.intent,
    this.requiresAction,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'content': content,
      'isUser': isUser,
      'timestamp': timestamp.toIso8601String(),
      'intent': intent?.toString(),
      'requiresAction': requiresAction,
    };
  }

  factory AIChatMessage.fromJson(Map<String, dynamic> json) {
    return AIChatMessage(
      content: json['content'] ?? '',
      isUser: json['isUser'] ?? false,
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      intent: json['intent'] != null 
          ? ChatIntent.values.firstWhere(
              (e) => e.toString() == json['intent'],
              orElse: () => ChatIntent.unknown,
            )
          : null,
      requiresAction: json['requiresAction'],
    );
  }
}

/// Model for leave application parameters
class LeaveApplicationParams {
  final LeaveType leaveType;
  final DateTime startDate;
  final DateTime endDate;
  final String reason;
  final String? staffInCharge;
  final bool isHalfDay;

  LeaveApplicationParams({
    required this.leaveType,
    required this.startDate,
    required this.endDate,
    required this.reason,
    this.staffInCharge,
    this.isHalfDay = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'leaveType': leaveType.toString(),
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'reason': reason,
      'staffInCharge': staffInCharge,
      'isHalfDay': isHalfDay,
    };
  }

  factory LeaveApplicationParams.fromJson(Map<String, dynamic> json) {
    return LeaveApplicationParams(
      leaveType: LeaveType.values.firstWhere(
        (e) => e.toString() == json['leaveType'],
        orElse: () => LeaveType.sick,
      ),
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      reason: json['reason'] ?? '',
      staffInCharge: json['staffInCharge'],
      isHalfDay: json['isHalfDay'] ?? false,
    );
  }
}

/// Model for WFH application parameters
class WFHApplicationParams {
  final WFHType wfhType;
  final DateTime startDate;
  final DateTime endDate;
  final String reason;
  final String? location;

  WFHApplicationParams({
    required this.wfhType,
    required this.startDate,
    required this.endDate,
    required this.reason,
    this.location,
  });

  Map<String, dynamic> toJson() {
    return {
      'wfhType': wfhType.toString(),
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'reason': reason,
      'location': location,
    };
  }

  factory WFHApplicationParams.fromJson(Map<String, dynamic> json) {
    return WFHApplicationParams(
      wfhType: WFHType.values.firstWhere(
        (e) => e.toString() == json['wfhType'],
        orElse: () => WFHType.fullDay,
      ),
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      reason: json['reason'] ?? '',
      location: json['location'],
    );
  }
}

/// Model for expense submission parameters
class ExpenseSubmissionParams {
  final double amount;
  final String currency;
  final String description;
  final DateTime expenseDate;
  final String category;
  final List<String>? attachments;

  ExpenseSubmissionParams({
    required this.amount,
    required this.currency,
    required this.description,
    required this.expenseDate,
    required this.category,
    this.attachments,
  });

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'currency': currency,
      'description': description,
      'expenseDate': expenseDate.toIso8601String(),
      'category': category,
      'attachments': attachments,
    };
  }

  factory ExpenseSubmissionParams.fromJson(Map<String, dynamic> json) {
    return ExpenseSubmissionParams(
      amount: (json['amount'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'AED',
      description: json['description'] ?? '',
      expenseDate: DateTime.parse(json['expenseDate']),
      category: json['category'] ?? '',
      attachments: json['attachments'] != null 
          ? List<String>.from(json['attachments'])
          : null,
    );
  }
}

/// Model for meeting booking parameters
class MeetingBookingParams {
  final String title;
  final DateTime startTime;
  final DateTime endTime;
  final String roomId;
  final List<String> attendees;
  final String? description;

  MeetingBookingParams({
    required this.title,
    required this.startTime,
    required this.endTime,
    required this.roomId,
    required this.attendees,
    this.description,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'roomId': roomId,
      'attendees': attendees,
      'description': description,
    };
  }

  factory MeetingBookingParams.fromJson(Map<String, dynamic> json) {
    return MeetingBookingParams(
      title: json['title'] ?? '',
      startTime: DateTime.parse(json['startTime']),
      endTime: DateTime.parse(json['endTime']),
      roomId: json['roomId'] ?? '',
      attendees: List<String>.from(json['attendees'] ?? []),
      description: json['description'],
    );
  }
}

/// Model for conversation context
class ConversationContext {
  final String userId;
  final List<AIChatMessage> messages;
  final Map<String, dynamic> sessionData;
  final DateTime lastActivity;

  ConversationContext({
    required this.userId,
    required this.messages,
    this.sessionData = const {},
    DateTime? lastActivity,
  }) : lastActivity = lastActivity ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'messages': messages.map((m) => m.toJson()).toList(),
      'sessionData': sessionData,
      'lastActivity': lastActivity.toIso8601String(),
    };
  }

  factory ConversationContext.fromJson(Map<String, dynamic> json) {
    return ConversationContext(
      userId: json['userId'] ?? '',
      messages: (json['messages'] as List?)
          ?.map((m) => AIChatMessage.fromJson(m))
          .toList() ?? [],
      sessionData: Map<String, dynamic>.from(json['sessionData'] ?? {}),
      lastActivity: DateTime.parse(json['lastActivity'] ?? DateTime.now().toIso8601String()),
    );
  }
}
