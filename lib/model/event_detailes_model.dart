class EventDetailesModel {
  String? result;
  Data? data;

  EventDetailesModel({this.result, this.data});

  EventDetailesModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }
}

class Data {
  int? id;
  User? user;
  String? title;
  String? description;
  String? startTime;
  String? endTime;
  String? eventDate;
  String? eventEndDate;
  List<EventImg>? eventImg;
  String? eventVisibility;
  Data(
      {this.eventVisibility,
      this.eventEndDate,
      this.id,
      this.user,
      this.title,
      this.description,
      this.startTime,
      this.endTime,
      this.eventDate,
      this.eventImg});

  Data.fromJson(Map<String, dynamic> json) {
    eventEndDate = json['event_end_date'];
    id = json['id'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    title = json['title'];
    description = json['description'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    eventDate = json['event_date'];

    eventVisibility = json['event_visibility_name'];
    if (json['event_img'] != null) {
      eventImg = <EventImg>[];
      json['event_img'].forEach((v) {
        eventImg!.add(EventImg.fromJson(v));
      });
    }
  }
}

class User {
  int? id;
  String? name;
  String? profilePic;
  String? designation;

  User({this.id, this.name, this.profilePic, this.designation});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    profilePic = json['profile_pic'];
    designation = json['designation'];
  }
}

class EventImg {
  int? id;
  String? name;
  String? eventImage;

  EventImg({this.id, this.name, this.eventImage});

  EventImg.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    eventImage = json['event_image'];
  }
}
