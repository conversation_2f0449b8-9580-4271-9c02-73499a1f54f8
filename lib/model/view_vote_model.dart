class ViewVoteModel {
  String? question;
  List<Options>? options;
  ViewVoteModel({this.question, this.options});
  ViewVoteModel.fromJson(Map<String, dynamic> json) {
    question = json['question'];
    if (json['options'] != null) {
      options = <Options>[];
      json['options'].forEach((v) {
        options!.add(Options.fromJson(v));
      });
    }
  }
}

class Options {
  int? id;
  String? name;
  int? totalVotes;
  bool? isMaximum;
  List<Users>? users;
  Options({this.id, this.name, this.totalVotes, this.isMaximum, this.users});
  Options.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    totalVotes = json['total_votes'];
    isMaximum = json['is_maximum'];
    if (json['users'] != null) {
      users = <Users>[];
      json['users'].forEach((v) {
        users!.add(Users.fromJson(v));
      });
    }
  }
}

class Users {
  String? name;
  String? profPic;

  Users({this.name, this.profPic});

  Users.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    profPic = json['prof_pic'];
  }
}
