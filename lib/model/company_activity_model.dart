import 'package:cloud_firestore/cloud_firestore.dart';

class WorkAnniversaryModel {
  String? status;
  String? name;
  String? email;
  String? uid;
  String? appStatus;
  Timestamp? upComingWorkAnniversary;
  Timestamp? startDate;

  WorkAnniversaryModel.fromjson(Map<String, dynamic> json) {
    status = json["status"];
    name = json["name"];
    email = json["email"];
    appStatus = json["approval_status"];
    uid = json['uid'];
    upComingWorkAnniversary = json['upcomingWorkAnniversary'];
    startDate = json['start_date'];
  }
}
