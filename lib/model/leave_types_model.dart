class LeaveTypesModel {
  bool? status;
  List<Data>? data;
  String? message;

  LeaveTypesModel({this.status, this.data, this.message});

  LeaveTypesModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
    message = json['message'];
  }
}

class Data {
  int? id;
  String? title;
  bool? status;

  Data({this.id, this.title, this.status});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['name'];
    // status = json['status'];
  }
}

class DayTypesModel {
  final String text;
  final int id;
  DayTypesModel(this.text, this.id);
  // final List<DayTypesModel> dayTypeItems = [
  //   DayTypesModel("Full Day", 1),
  //   DayTypesModel("Half Day", 2),
  // ];
}
