class AnniversaryModel {
  String? message;
  List<Data>? data;

  AnniversaryModel({this.message, this.data});

  AnniversaryModel.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  String? name;
  String? dateOfJoining;
  String? profilePic;
  int? year;
  String? designation;
  bool? workAnniversaryStatus;

  Data(
      {this.name,
      this.dateOfJoining,
      this.profilePic,
      this.year,
      this.designation,
      this.workAnniversaryStatus});

  Data.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    dateOfJoining = json['date_of_joining'];
    profilePic = json['profile_pic'];
    year = json['year'];
    designation = json['designation'];
    workAnniversaryStatus = json['work_anniversary_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['date_of_joining'] = dateOfJoining;
    data['profile_pic'] = profilePic;
    data['year'] = year;
    data['designation'] = designation;
    data['work_anniversary_status'] = workAnniversaryStatus;
    return data;
  }
}
