class HolidaysModel {
  int? id;
  String? name;
  String? startDate;
  String? endDate;
  String? date;
  int? days;
  String? occassion;
  bool? restrictedHoliday;
  String? type;
  int? year;
  bool? isConfirm;
  String? holidayPolicy;

  HolidaysModel({
    this.id,
    this.name,
    this.startDate,
    this.endDate,
    this.date,
    this.days,
    this.occassion,
    this.restrictedHoliday,
    this.type,
    this.year,
    this.isConfirm,
    this.holidayPolicy,
  });

  HolidaysModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    date = json['date'];
    days = json['days'];
    occassion = json['occassion'];
    restrictedHoliday = json['restricted_holiday'];
    type = json['type'];
    year = json['year'];
    isConfirm = json['is_confirm'];
    holidayPolicy = json['holiday_policy'];
  }
}
