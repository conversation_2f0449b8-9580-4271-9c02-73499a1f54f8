class ReimbursmentModel {
  int? id;
  String? title;
  String? startDate;
  String? endDate;
  String? currency;
  num? amount;
  String? status;
  List<LevelUsers>? levelUsers;
  String? description;
  String? userName;
  String? profilePic;
  ReimbursmentModel(
      {this.id,
      this.title,
      this.startDate,
      this.endDate,
      this.currency,
      this.amount,
      this.status,
      this.levelUsers,
      this.description,
      this.userName,
      this.profilePic});

  ReimbursmentModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    currency = json['currency'];
    amount = json['amount'];
    status = json['status'];
    description = json['description'];
    if (json['level_users'] != null) {
      levelUsers = <LevelUsers>[];
      json['level_users'].forEach((v) {
        levelUsers!.add(LevelUsers.fromJson(v));
      });
    }
    userName = json['user_name'];
    profilePic = json['user_profile_pic'];
  }
}

class LevelUsers {
  int? id;
  String? approvalStatus;
  int? reimbursementLevel;
  String? addedBy;
  String? addedByProf;
  String? updatedAt;
  String? approvalStatusText;
  bool? approveButton;

  LevelUsers(
      {this.id,
      this.approvalStatus,
      this.reimbursementLevel,
      this.addedBy,
      this.addedByProf,
      this.updatedAt,
      this.approvalStatusText,
      this.approveButton});

  LevelUsers.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    approvalStatus = json['approval_status'];
    reimbursementLevel = json['reimbursement_level'];
    addedBy = json['added_by'];
    addedByProf = json['added_by_prof'];
    updatedAt = json['updated_at'];
    approvalStatusText = json['approval_status_text'];
    approveButton = json['approve_button'];
  }
}
