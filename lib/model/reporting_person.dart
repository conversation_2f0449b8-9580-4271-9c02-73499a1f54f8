class ReportingPerson {
  ReportingPerson({
    required this.id,
    required this.name,
    required this.desgination,
  });
  dynamic id;
  String? name;
  String? email;
  String? desgination;
  String? profilePhoto;
  String? fcmToken;
  ReportingPerson.fromAPI(Map<String, dynamic> json) {
    id = int.parse(json['id'].toString());
    name = json['name'];
    email = json['email'];
    desgination = json['designation'];
  }
  ReportingPerson.fromJson(Map<String, dynamic> json, this.id) {
    name = json['name'];
    email = json['email'];
    desgination = json['designation_name'];
    profilePhoto = json['profilePhoto'];
    fcmToken = json['fcmToken'];
  }
}
