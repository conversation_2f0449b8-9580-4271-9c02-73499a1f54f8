class EmployeeModel {
  int? id;
  String? employeeId;
  String? name;
  String? nickName;
  String? email;
  String? profilePic;
  String? dateOfBirth;
  String? dateOfJoining;
  List<Designation>? designation;
  List<UserStatus>? userStatus;
  String? department;
  String? branch;
  ReportingPerson? reportingPerson;
  String? emergencyContact;

  EmployeeModel(
      {this.id,
      this.employeeId,
      this.name,
      this.nickName,
      this.email,
      this.profilePic,
      this.dateOfBirth,
      this.dateOfJoining,
      this.designation,
      this.userStatus,
      this.department,
      this.branch,
      this.reportingPerson,
      this.emergencyContact});

  EmployeeModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    employeeId = json['employee_id'];
    name = json['name'];
    nickName = json['nick_name'];
    email = json['email'];
    profilePic = json['profile_pic'];
    dateOfBirth = json['date_of_birth'];
    dateOfJoining = json['date_of_joining'];
    if (json['designation'] != null) {
      designation = <Designation>[];
      json['designation'].forEach((v) {
        designation!.add(Designation.fromJson(v));
      });
    }
    if (json['user_status'] != null) {
      userStatus = <UserStatus>[];
      json['user_status'].forEach((v) {
        userStatus!.add(UserStatus.fromJson(v));
      });
    }
    department = json['department'];
    branch = json['branch'];
    reportingPerson = json['reporting_person'] != null
        ? ReportingPerson.fromJson(json['reporting_person'])
        : null;
    emergencyContact = json['emergency_contact'];
  }
}

class Designation {
  int? id;
  String? name;

  Designation({this.id, this.name});

  Designation.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }
}

class UserStatus {
  int? id;
  String? name;
  bool? isActive;
  String? createdAt;
  String? updatedAt;

  UserStatus(
      {this.id, this.name, this.isActive, this.createdAt, this.updatedAt});

  UserStatus.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}

class ReportingPerson {
  int? id;
  String? name;
  String? email;
  String? designation;
  String? profilePic;

  ReportingPerson(
      {this.id, this.name, this.email, this.designation, this.profilePic});

  ReportingPerson.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    designation = json['designation'];
    profilePic = json['profile_pic'];
  }
}
