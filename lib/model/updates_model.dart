import 'package:cloud_firestore/cloud_firestore.dart';

class UpdatesModel {
  late String id;
  late String text;
  late String? image;
  late String uid;
  late String user;
  late Timestamp dateTime;
  String? designation;
  String? userImage;
  List<dynamic> likes = [];
  UpdatesModel.fromJson(Map<String, dynamic> json, this.id) {
    text = json['description'];
    uid = json['uid'];
    user = json['user'];
    dateTime = json['dateTime'];
    image = json['image'];
    if (json.containsKey('likes')) {
      likes = json['likes'];
    }
    designation = json['designation'];
    userImage = json['user_image'];
  }
}
