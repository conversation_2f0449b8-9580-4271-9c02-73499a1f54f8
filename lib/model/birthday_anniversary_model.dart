class BirthdayAnniversaryModel {
  String? message;
  List<Data>? data;

  BirthdayAnniversaryModel({this.message, this.data});

  BirthdayAnniversaryModel.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }
}

class Data {
  String? name;
  String? dateOfBirth;
  String? dateOfJoining;
  String? profilePic;
  int? year;
  String? designation;
  bool? workAnniversaryStatus;
  String? model;

  Data(
      {this.name,
      this.dateOfBirth,
      this.dateOfJoining,
      this.profilePic,
      this.year,
      this.designation,
      this.workAnniversaryStatus,
      this.model});

  Data.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    dateOfBirth = json['date_of_birth'];
    dateOfJoining = json['date_of_joining'];
    profilePic = json['profile_pic'];
    year = json['year'];
    designation = json['designation'];
    workAnniversaryStatus = json['work_anniversary_status'];
    model = json['model'];
  }
}
