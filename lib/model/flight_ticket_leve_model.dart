import 'package:equatable/equatable.dart';

// ignore: must_be_immutable
class FlightTicketLeaveModel extends Equatable {
  int? id;
  String? startDate;
  String? endDate;
  String? leaveType;

  FlightTicketLeaveModel(
      {this.id, this.startDate, this.endDate, this.leaveType});

  FlightTicketLeaveModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    leaveType = json['leave_type'];
  }

  @override
  List<Object?> get props => [
        id,
        leaveType,
      ];
}
