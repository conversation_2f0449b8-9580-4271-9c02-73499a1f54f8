class UserDetailesModel {
  int? id;
  String? firstName;
  String? lastName;
  String? email;
  ProfileDetails? profileDetails;
  bool? isReportingPerson;
  ExtraPermission? extraPermission;
  UserDetailesModel(
      {this.id,
      this.firstName,
      this.lastName,
      this.email,
      this.profileDetails,
      this.isReportingPerson,
      this.extraPermission});

  UserDetailesModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    email = json['email'];
    profileDetails = json['profile_details'] != null
        ? ProfileDetails.fromJson(json['profile_details'])
        : null;
    isReportingPerson = json['is_reporting_person'];
    extraPermission = json['extra_permission'] != null
        ? ExtraPermission.fromJson(json['extra_permission'])
        : null;
  }
}

class ProfileDetails {
  String? employeeId;
  String? designation;
  String? dateOfBirth;
  String? profilePic;
  String? dateOfJoining;
  String? contactNumber;
  EmpStatus? empStatus;
  String? department;
  String? branch;
  bool? wfhStatus;
  ReportingPerson? reportingPerson;

  ProfileDetails(
      {this.employeeId,
      this.designation,
      this.dateOfBirth,
      this.profilePic,
      this.dateOfJoining,
      this.contactNumber,
      this.empStatus,
      this.department,
      this.branch,
      this.wfhStatus,
      this.reportingPerson});

  ProfileDetails.fromJson(Map<String, dynamic> json) {
    employeeId = json['employee_id'];
    designation = json['designation'];
    dateOfBirth = json['date_of_birth'];
    profilePic = json['profile_pic'];
    dateOfJoining = json['date_of_joining'];
    contactNumber = json['contact_number'];
    empStatus = json['emp_status'] != null
        ? EmpStatus.fromJson(json['emp_status'])
        : null;
    department = json['department'];
    branch = json['branch'];
    reportingPerson = json['reporting_person'] != null
        ? ReportingPerson.fromJson(json['reporting_person'])
        : null;
    wfhStatus = json['wfh_status'];
  }
}

class EmpStatus {
  int? id;
  String? name;
  bool? isActive;
  String? createdAt;
  String? updatedAt;
  // List<SubAction>? actions;
  bool? isShowActions;
  String? subAction;
  String? colorCode;
  EmpStatus({
    this.id,
    this.name,
    this.isActive,
    this.createdAt,
    this.updatedAt,
    // this.actions,
    this.isShowActions,
    this.subAction,
    this.colorCode,
  });

  EmpStatus.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    subAction = json['sub_action'];
    colorCode = json['color_code'];
    // if (json['actions'] != null) {
    //   actions = <SubAction>[];
    //   json['actions'].forEach((v) {
    //     actions!.add(SubAction.fromJson(v));
    //   });
    // }
  }
}

// class SubAction {
//   int? id;
//   String? name;
//   bool? isActive;

//   SubAction({this.id, this.name, this.isActive});

//   SubAction.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     isActive = json['is_active'];
//   }
// }

class ReportingPerson {
  int? id;
  String? name;
  String? email;
  String? designation;
  String? profilePic;

  ReportingPerson(
      {this.id, this.name, this.email, this.designation, this.profilePic});

  ReportingPerson.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    designation = json['designation'];
    profilePic = json['profile_pic'];
  }
}

class ExtraPermission {
  bool? meetingRoom;
  ExtraPermission({this.meetingRoom});
  ExtraPermission.fromJson(Map<String, dynamic> json) {
    meetingRoom = json['meeting_room'];
  }
}
