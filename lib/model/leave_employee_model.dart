class LeaveEmployeeModel {
  LeaveEmployeeModel({
    this.id,
    this.userId,
    this.firstname,
    this.lastname,
    this.email,
    this.startDate,
    this.endDate,
  });

  String? id;
  String? userId;
  String? firstname;
  String? lastname;
  String? email;
  DateTime? startDate;
  DateTime? endDate;

  factory LeaveEmployeeModel.fromJson(Map<String, dynamic> json) =>
      LeaveEmployeeModel(
        id: json["id"],
        userId: json["user_id"],
        firstname: json["firstname"],
        lastname: json["lastname"],
        email: json["username"],
        startDate: DateTime.parse(json["start_date"]),
        endDate: DateTime.parse(json["end_date"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "firstname": firstname,
        "lastname": lastname,
        "username": email,
        "start_date":
            "${startDate?.year.toString().padLeft(4, '0')}-${startDate?.month.toString().padLeft(2, '0')}-${startDate?.day.toString().padLeft(2, '0')}",
        "end_date":
            "${endDate?.year.toString().padLeft(4, '0')}-${endDate?.month.toString().padLeft(2, '0')}-${endDate?.day.toString().padLeft(2, '0')}",
      };
}
