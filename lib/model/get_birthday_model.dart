class GetBirthdayModel {
  String? result;
  List<Data>? data;

  GetBirthdayModel({this.result, this.data});

  GetBirthdayModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['result'] = result;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  int? uId;
  String? name;
  String? dateOfBirth;
  String? profilePic;
  bool? birthdayStatus;

  Data(
      {this.uId,
      this.name,
      this.dateOfBirth,
      this.profilePic,
      this.birthdayStatus});

  Data.fromJson(Map<String, dynamic> json) {
    uId = json['u_id'];
    name = json['name'];
    dateOfBirth = json['date_of_birth'];
    profilePic = json['profile_pic'];
    birthdayStatus = json['birthday_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['u_id'] = uId;
    data['name'] = name;
    data['date_of_birth'] = dateOfBirth;
    data['profile_pic'] = profilePic;
    data['birthday_status'] = birthdayStatus;
    return data;
  }
}
