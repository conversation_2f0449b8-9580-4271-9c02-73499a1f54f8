// class PostDetailsModel {
//   String? result;
//   List<Records>? records;
//   int? pagesCount;
//   bool? hasNext;
//   bool? hasPrevious;

//   PostDetailsModel(
//       {this.result,
//       this.records,
//       this.pagesCount,
//       this.hasNext,
//       this.hasPrevious});

//   PostDetailsModel.fromJson(Map<String, dynamic> json) {
//     result = json['result'];
//     if (json['records'] != null) {
//       records = <Records>[];
//       json['records'].forEach((v) {
//         records!.add(Records.fromJson(v));
//       });
//     }
//     pagesCount = json['pages_count'];
//     hasNext = json['has_next'];
//     hasPrevious = json['has_previous'];
//   }
// }

// class Records {
//   int? id;
//   String? createdAt;
//   String? name;
//   String? designation;
//   int? like;
//   int? comments;
//   bool? userLiked;
//   List<File>? file;
//   String? description;
//   String? descriptionString;
//   String? profilePhoto;
//   bool? isOwner;
//   String? postLink;
//   List<int>? policy;
//   int? surveyId;
//   List<Options>? options;
//   String? type;
//   String? questions;
//   bool? isAllowResultPublic;
//   bool? isAllowMultiple;

//   Records(
//       {this.id,
//       this.createdAt,
//       this.name,
//       this.designation,
//       this.like,
//       this.comments,
//       this.userLiked,
//       this.file,
//       this.description,
//       this.descriptionString,
//       this.profilePhoto,
//       this.isOwner,
//       this.postLink,
//       this.policy,
//       this.surveyId,
//       this.options,
//       this.type,
//       this.questions,
//       this.isAllowResultPublic,
//       this.isAllowMultiple});

//   Records.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     createdAt = json['created_at'];
//     name = json['name'];
//     designation = json['designation'];
//     like = json['like'];
//     comments = json['comments'];
//     userLiked = json['user_liked'];
//     if (json['file'] != null) {
//       file = <File>[];
//       json['file'].forEach((v) {
//         file!.add(File.fromJson(v));
//       });
//     }
//     description = json['description'];
//     descriptionString = json['description_string'];
//     profilePhoto = json['profile_photo'];
//     isOwner = json['is_owner'];
//     postLink = json['post_link'];
//     policy = json['policy'].cast<int>();
//     surveyId = json['survey_id'];
//     if (json['options'] != null) {
//       options = <Options>[];
//       json['options'].forEach((v) {
//         options!.add(Options.fromJson(v));
//       });
//     }
//     type = json['type'];
//     questions = json['questions'];
//     isAllowResultPublic = json['is_allow_result_public'];
//     isAllowMultiple = json['is_allow_multiple'];
//   }
// }

// class File {
//   int? id;
//   String? file;
//   String? fileType;
//   String? thumbnail;

//   File({this.id, this.file, this.fileType, this.thumbnail});

//   File.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     file = json['file'];
//     fileType = json['file_type'];
//     thumbnail = json['thumbnail'];
//   }
// }

// class Options {
//   int? id;
//   String? name;
//   int? totalVotes;
//   bool? isVoted;
//   double? votePercentage;
//   int? groupValue;
//   Options(
//       {this.id,
//       this.name,
//       this.isVoted,
//       this.totalVotes,
//       this.votePercentage,
//       this.groupValue});

//   Options.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     totalVotes = json['total_votes'];
//     isVoted = json['is_voted'];
//     votePercentage = json['vote_percentage'];
//     groupValue = json['group_value'];
//   }
// }
