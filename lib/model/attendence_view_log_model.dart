class AttendanceViewLogModel {
  List<AttendanceLog>? attendanceLog;

  AttendanceViewLogModel({this.attendanceLog});

  AttendanceViewLogModel.fromJson(Map<String, dynamic> json) {
    if (json['attendance_logs'] != null) {
      attendanceLog = <AttendanceLog>[];
      json['attendance_logs'].forEach((v) {
        attendanceLog!.add(AttendanceLog.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (attendanceLog != null) {
      data['attendance_logs'] = attendanceLog!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class AttendanceLog {
  String? punchin;
  String? punchout;

  AttendanceLog({this.punchin, this.punchout});

  AttendanceLog.fromJson(Map<String, dynamic> json) {
    punchin = json['punchin'];
    punchout = json['punchout'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['punchin'] = punchin;
    data['punchout'] = punchout;
    return data;
  }
}
