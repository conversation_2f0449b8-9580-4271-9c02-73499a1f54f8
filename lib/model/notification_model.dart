import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:e8_hr_portal/model/leave_application_model.dart';

class NotificationModel {
  String? id;
  String? title;
  String? body;
  String? type;
  String? screen;
  String? uid;
  bool? read;
  String? leaveId;
  String? status;
  Timestamp? saveTime;
  LeaveApplicationModel? leaveApplicationModel;
  NotificationModel.fromJson(Map<String, dynamic> json, this.id) {
    title = json['title'];
    body = json['body'];
    type = json['type'];
    screen = json['screen'];
    uid = json['uid'];
    read = json['read'];
    leaveId = json['leave_id'];
    status = json['status'];
    saveTime = json['saveTime'];
    if (json['leave_details'] != null) {
      leaveApplicationModel =
          LeaveApplicationModel.fromJson(json['leave_details']);
    }
  }
}
