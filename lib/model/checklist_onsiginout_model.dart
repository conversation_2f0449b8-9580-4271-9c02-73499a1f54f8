class OnsignoutChecklistModel {
  int? assigneeId;
  String? responseType;
  String? point;
  String? scheduleId;
  String? schedule;
  String? dueDate;
  String? details;
  bool? updatedStatus;
  String? status;

  OnsignoutChecklistModel(
      {this.assigneeId,
      this.responseType,
      this.point,
      this.scheduleId,
      this.schedule,
      this.dueDate,
      this.updatedStatus,
      this.status,
      this.details});

  OnsignoutChecklistModel.fromJson(Map<String, dynamic> json) {
    assigneeId = json['assignee_id'];
    responseType = json['response_type'];
    point = json['point'];
    scheduleId = json['schedule_id'];
    schedule = json['schedule'];
    dueDate = json['due_date'];
    details = json['details'];
    status = json['status'];
    updatedStatus = json['updated_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['assignee_id'] = this.assigneeId;
    data['response_type'] = this.responseType;
    data['point'] = this.point;
    data['schedule_id'] = this.scheduleId;
    data['schedule'] = this.schedule;
    data['due_date'] = this.dueDate;
    data['details'] = this.details;
    return data;
  }
}
