class RecentActivityModel {
  String? message;
  String? createdAt;
  DbData? dbData;
  String? moduleName;
  String? newDate;
  RecentActivityModel(
      {this.message, this.createdAt, this.dbData, this.moduleName});
  RecentActivityModel.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    createdAt = json['created_at'];
    dbData = json['db_data'] != null ? DbData.fromJson(json['db_data']) : null;
    moduleName = json['module_name'];
    newDate = json['new_date_time'];
  }
}

class DbData {
  int? userId;
  String? id;
  int? postId;
  DbData({this.userId, this.id, this.postId});

  DbData.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    id = json['id'].toString();
    postId = json['post_id'];
  }
}
