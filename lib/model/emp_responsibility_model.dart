class EmpResponsiblityModel {
  String? result;
  List<Data>? data;
  bool? hasNext;
  bool? hasPrevious;

  EmpResponsiblityModel(
      {this.result, this.data, this.hasNext, this.hasPrevious});

  EmpResponsiblityModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
    hasNext = json['has_next'];
    hasPrevious = json['has_previous'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['result'] = result;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['has_next'] = hasNext;
    data['has_previous'] = hasPrevious;
    return data;
  }
}

class Data {
  String? responsibility;
  String? type;
  String? addedBy;
  String? startDate;
  String? endDate;

  Data(
      {this.responsibility,
      this.type,
      this.addedBy,
      this.startDate,
      this.endDate});

  Data.fromJson(Map<String, dynamic> json) {
    responsibility = json['responsibility'];
    type = json['type'];
    addedBy = json['added_by'];
    startDate = json['start_date'];
    endDate = json['end_date'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['responsibility'] = responsibility;
    data['type'] = type;
    data['added_by'] = addedBy;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    return data;
  }
}
