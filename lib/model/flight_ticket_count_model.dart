class FlightTicketCountModel {
  String? result;
  Data? data;

  FlightTicketCountModel({this.result, this.data});

  FlightTicketCountModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }
}

class Data {
  int? eligibleTicket;
  int? takenTicket;
  int? balanceTicket;

  Data({this.eligibleTicket, this.takenTicket, this.balanceTicket});

  Data.fromJson(Map<String, dynamic> json) {
    eligibleTicket = json['eligible_ticket'];
    takenTicket = json['taken_ticket'];
    balanceTicket = json['balance_ticket'];
  }
}
