class EmployeeLeaveDetailes {
  String? result;
  List<Data>? data;
  bool? hasNext;
  bool? hasPrevious;

  EmployeeLeaveDetailes(
      {this.result, this.data, this.hasNext, this.hasPrevious});

  EmployeeLeaveDetailes.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
    hasNext = json['has_next'];
    hasPrevious = json['has_previous'];
  }
}

class Data {
  int? id;
  LeaveDetails? leaveDetails;
  HrStatus? hrStatus;
  List<ReportingPersonList>? reportingPersonList;

  Data({this.id, this.leaveDetails, this.hrStatus, this.reportingPersonList});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    leaveDetails = json['leave_details'] != null
        ? LeaveDetails.fromJson(json['leave_details'])
        : null;
    hrStatus =
        json['hr_status'] != null ? HrStatus.fromJson(json['hr_status']) : null;
    if (json['reporting_person_list'] != null) {
      reportingPersonList = <ReportingPersonList>[];
      json['reporting_person_list'].forEach((v) {
        reportingPersonList!.add(ReportingPersonList.fromJson(v));
      });
    }
  }
}

class LeaveDetails {
  int? id;
  String? startDate;
  String? endDate;
  String? status;
  String? dayType;
  String? reason;
  String? leaveType;
  num? dayCount;
  String? createdAt;
  String? leaveDoc;
  String? docExtension;
  StaffIncharge? staffIncharge;

  LeaveDetails(
      {this.id,
      this.startDate,
      this.endDate,
      this.status,
      this.dayType,
      this.reason,
      this.leaveType,
      this.dayCount,
      this.createdAt,
      this.leaveDoc,
      this.docExtension,
      this.staffIncharge});

  LeaveDetails.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    status = json['status'];
    dayType = json['day_type'];
    reason = json['reason'];
    leaveType = json['leave_type'];
    dayCount = json['day_count'];
    createdAt = json['created_at'];
    leaveDoc = json['leave_doc'];
    docExtension = json['doc_extension'];
    staffIncharge = json['staff_incharge'] != null
        ? StaffIncharge.fromJson(json['staff_incharge'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['status'] = status;
    data['day_type'] = dayType;
    data['reason'] = reason;
    data['leave_type'] = leaveType;
    data['day_count'] = dayCount;
    data['created_at'] = createdAt;
    data['leave_doc'] = leaveDoc;
    data['doc_extension'] = docExtension;
    if (staffIncharge != null) {
      data['staff_incharge'] = staffIncharge!.toJson();
    }
    return data;
  }
}

class StaffIncharge {
  int? id;
  String? name;
  String? email;
  String? designation;
  String? profilePic;

  StaffIncharge(
      {this.id, this.name, this.email, this.designation, this.profilePic});

  StaffIncharge.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    designation = json['designation'];
    profilePic = json['profile_pic'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['email'] = email;
    data['designation'] = designation;
    data['profile_pic'] = profilePic;
    return data;
  }
}

class HrStatus {
  int? id;
  String? name;
  List<Role>? role;
  String? profilePic;
  String? isApprove;
  String? comment;
  String? createdAt;

  HrStatus(
      {this.id,
      this.name,
      this.role,
      this.profilePic,
      this.isApprove,
      this.comment,
      this.createdAt});

  HrStatus.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    if (json['role'] != null) {
      role = <Role>[];
      json['role'].forEach((v) {
        role!.add(Role.fromJson(v));
      });
    }
    profilePic = json['profile_pic'];
    isApprove = json['is_approve'];
    comment = json['comment'];
    createdAt = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (role != null) {
      data['role'] = role!.map((v) => v.toJson()).toList();
    }
    data['profile_pic'] = profilePic;
    data['is_approve'] = isApprove;
    data['comment'] = comment;
    data['created_at'] = createdAt;
    return data;
  }
}

class Role {
  String? name;

  Role({this.name});

  Role.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    return data;
  }
}

class ReportingPersonList {
  int? id;
  String? name;
  String? role;
  String? profilePic;
  String? isApprove;
  String? comment;
  String? createdAt;

  ReportingPersonList(
      {this.id,
      this.name,
      this.role,
      this.profilePic,
      this.isApprove,
      this.comment,
      this.createdAt});

  ReportingPersonList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    role = json['role'];
    profilePic = json['profile_pic'];
    isApprove = json['is_approve'].toString();
    comment = json['comment'];
    createdAt = json['created_at'];
  }
}
