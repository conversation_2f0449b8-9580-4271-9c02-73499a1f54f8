class EmployeeLeaveHostory {
  int? id;
  String? startDate;
  String? endDate;
  String? status;
  String? dayType;
  String? reason;
  String? leaveType;
  num? dayCount;
  String? createdAt;
  String? leaveDoc;
  String? docExtension;
  StaffIncharge? staffIncharge;

  EmployeeLeaveHostory(
      {this.id,
      this.startDate,
      this.endDate,
      this.status,
      this.dayType,
      this.reason,
      this.leaveType,
      this.dayCount,
      this.createdAt,
      this.leaveDoc,
      this.docExtension,
      this.staffIncharge});

  EmployeeLeaveHostory.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    status = json['status'];
    dayType = json['day_type'];
    reason = json['reason'];
    leaveType = json['leave_type'];
    dayCount = json['day_count'];
    createdAt = json['created_at'];
    leaveDoc = json['leave_doc'];
    docExtension = json['doc_extension'];
    staffIncharge = json['staff_incharge'] != null
        ? StaffIncharge.fromJson(json['staff_incharge'])
        : null;
  }
}

class StaffIncharge {
  int? id;
  String? name;
  String? email;
  String? designation;
  String? profilePic;

  StaffIncharge(
      {this.id, this.name, this.email, this.designation, this.profilePic});

  StaffIncharge.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    designation = json['designation'];
    profilePic = json['profile_pic'];
  }
}
