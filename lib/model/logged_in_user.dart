import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LoggedInUser {
  static String? name;
  static String? email;
  static String? phone;
  static String? uid;
  static String? groupId;
  static String? groupName;
  static String? groupCode;
  static bool isAdmin = false;
  static String? profilePhoto;
  static String? designation;
  static String? employeeId;
  static Timestamp? dateOfBirth;
  static Timestamp? dateOfJoin;
  static List<dynamic> skills = [];
  static bool isWorkFromHome = false;
  static String? country;
  static bool enableMeetingModule = true;
  LoggedInUser(Map<String, dynamic>? json) {
    if (json != null) {
      name = json['name'];
      email = json['email'];
      phone = json['phone'];
      uid = json['uid'];
      groupId = json['groupId'];
      groupName = json['groupName'];
      groupCode = json['groupCode'];
      isAdmin = json['role'] == 'admin';
      profilePhoto = json['profilePhoto'];
      designation = json['designation_name'];
      country = json['country'];
      if (country?.toLowerCase() == 'india') {
        enableMeetingModule = false;
        debugPrint('----------------$enableMeetingModule----------------');
      }
      try {
        dateOfBirth = json['birthdate'];
      } catch (e) {
        dateOfBirth = null;
      }
      try {
        dateOfJoin = json['start_date'];
      } catch (e) {
        dateOfJoin = null;
      }
      employeeId = json['employeeId'];
      if (json.containsKey('skills')) {
        skills = json['skills'];
      }
      if (json['isWorkFromHome'] != null) {
        isWorkFromHome = json['isWorkFromHome'];
      }
    }
  }

  LoggedInUser.fromLocal(Map<String, dynamic> json) {
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
    uid = json['uid'];
    groupId = json['groupId'];
    groupName = json['groupName'];
    groupCode = json['groupCode'];
    isAdmin = json['role'] == 'admin';
    profilePhoto = json['profilePhoto'];
    designation = json['designation_name'];
    dateOfBirth = json['birthdate'];
    dateOfJoin = json['start_date'];
    employeeId = json['employeeId'];
    if (json.containsKey('skills')) {
      skills = json['skills'];
    }
    if (json['birthdate'] != null) {
      DateTime birthdate = json['birthdate'];
      Timestamp myTimeStamp = Timestamp.fromDate(birthdate);
      dateOfBirth = myTimeStamp;
    }
    if (json['start_date'] != null) {
      DateTime joinDate = json['start_date'];
      Timestamp myTimeStamp = Timestamp.fromDate(joinDate);
      dateOfJoin = myTimeStamp;
    }
  }

  setUserLocally(Map<String, dynamic> json) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String user = jsonEncode(json);
    prefs.setString('user', user);
  }
}
