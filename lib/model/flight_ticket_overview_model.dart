import 'package:e8_hr_portal/model/flight_ticket_leve_model.dart';

class FlightTicketOverviewModel {
  String? result;
  Data? data;

  FlightTicketOverviewModel({this.result, this.data});

  FlightTicketOverviewModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }
}

class Data {
  int? id;
  int? userId;
  String? name;
  String? requestedDate;
  String? approvedBy;
  String? status;
  String? approvedOrRejectedDate;
  String? deperatureDate;
  String? returnDate;
  String? noOfDays;
  String? countryOfDeparture;
  String? cityOfDeperature;
  String? countryOfArrival;
  String? cityOfArrival;
  String? airline;
  bool? isSeeMore;
  String? rejectedComment;
  String? remark;
  String? empId;
  String? email;
  String? profilePic;
  int? extraTicket;
  List<TicketData>? tktFile;
  List<TktUploadData>? tktUploadData;
  List<ApproveRejectData>? apprvReject;
  FlightTicketLeaveModel? leaveData;
  TktPermission? tktPermission;

  Data(
      {this.id,
      this.userId,
      this.requestedDate,
      this.approvedBy,
      this.status,
      this.approvedOrRejectedDate,
      this.deperatureDate,
      this.returnDate,
      this.noOfDays,
      this.countryOfDeparture,
      this.cityOfDeperature,
      this.countryOfArrival,
      this.cityOfArrival,
      this.airline,
      this.isSeeMore,
      this.rejectedComment,
      this.remark,
      this.name,
      this.empId,
      this.email,
      this.profilePic,
      this.extraTicket,
      this.tktFile,
      this.apprvReject,
      this.leaveData,
      this.tktUploadData,
      this.tktPermission});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    name = json['sender'];
    requestedDate = json['requested_date'];
    approvedBy = json['approved_by'];
    status = json['status'];
    approvedOrRejectedDate = json['approved_or_rejected_date'];
    deperatureDate = json['deperature_date'];
    returnDate = json['return_date'];
    noOfDays = json['no_of_days'];
    countryOfDeparture = json['country_of_departure'];
    cityOfDeperature = json['city_of_deperature'];
    countryOfArrival = json['country_of_arrival'];
    cityOfArrival = json['city_of_arrival'];
    airline = json['airline'];
    isSeeMore = json['is_see_more'];
    rejectedComment = json['rejected_comment'];
    remark = json['remark'];
    empId = json["employee_id"];
    email = json["emp_email"];
    profilePic = json["emp_profile_pic"];
    extraTicket = json['extra_ticket'];
    if (json['tkt_file'] != null) {
      tktFile = <TicketData>[];
      json['tkt_file'].forEach((v) {
        tktFile!.add(TicketData.fromJson(v));
      });
    }
    if (json['apprv_reject'] != null) {
      apprvReject = <ApproveRejectData>[];
      json['apprv_reject'].forEach((v) {
        apprvReject!.add(ApproveRejectData.fromJson(v));
      });
    }
    if (json['tkt_upload_file'] != null) {
      tktUploadData = <TktUploadData>[];
      json['tkt_upload_file'].forEach((v) {
        tktUploadData!.add(TktUploadData.fromJson(v));
      });
    }
    leaveData = json['leave_data'] != null
        ? FlightTicketLeaveModel.fromJson(json['leave_data'])
        : null;
    tktPermission = json['tkt_permission'] != null
        ? TktPermission.fromJson(json['tkt_permission'])
        : null;
  }
}

class LeaveData {
  int? id;
  String? startDate;
  String? endDate;
  String? leaveType;

  LeaveData({this.id, this.startDate, this.endDate, this.leaveType});

  LeaveData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    leaveType = json['leave_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['leave_type'] = leaveType;
    return data;
  }
}

class TktPermission {
  String? ticketPermission;

  TktPermission({this.ticketPermission});

  TktPermission.fromJson(Map<String, dynamic> json) {
    ticketPermission = json['ticket_permission'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ticket_permission'] = ticketPermission;
    return data;
  }
}

class TicketData {
  int? id;
  String? title;
  String? fileType;
  String? file;

  TicketData({this.id, this.title, this.fileType, this.file});

  TicketData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    fileType = json['file_type'];
    file = json['file'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['file_type'] = fileType;
    data['file'] = file;
    return data;
  }
}

class TktUploadData {
  int? id;
  String? title;
  String? fileType;
  String? file;

  TktUploadData({this.id, this.title, this.fileType, this.file});

  TktUploadData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    fileType = json['file_type'];
    file = json['file'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['file_type'] = fileType;
    data['file'] = file;
    return data;
  }
}

class ApproveRejectData {
  int? id;
  int? userId;
  String? userName;
  String? levelStatus;
  String? levelType;
  String? requestedDate;
  String? remark;
  String? profilePic;

  ApproveRejectData(
      {this.id,
      this.userId,
      this.userName,
      this.levelStatus,
      this.levelType,
      this.requestedDate,
      this.remark,
      this.profilePic});

  ApproveRejectData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    userName = json['user_name'];
    levelStatus = json['level_status'];
    levelType = json['level_type'];
    requestedDate = json['requested_date'];
    remark = json['remark'];
    profilePic = json['profile_pic'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['user_name'] = userName;
    data['level_status'] = levelStatus;
    data['level_type'] = levelType;
    data['requested_date'] = requestedDate;
    data['remark'] = remark;
    data['profile_pic'] = profilePic;
    return data;
  }
}
