class LeaveRequestModel {
  String? result;
  List<Data>? data;

  LeaveRequestModel({this.result, this.data});

  LeaveRequestModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }
}

class Data {
  int? id;
  Leave? leave;
  LeaveApprove? leaveApprove;

  Data({this.id, this.leave, this.leaveApprove});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    leave = json['leave'] != null ? Leave.fromJson(json['leave']) : null;
    leaveApprove = json['leave_approve'] != null
        ? LeaveApprove.fromJson(json['leave_approve'])
        : null;
  }
}

class Leave {
  int? id;
  String? name;
  String? profilePic;
  String? reason;
  String? fromDate;
  String? toDate;
  String? status;
  String? leaveType;

  Leave(
      {this.id,
      this.name,
      this.profilePic,
      this.reason,
      this.fromDate,
      this.toDate,
      this.status,
      this.leaveType});

  Leave.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    profilePic = json['profile_pic'];
    reason = json['reason'];
    fromDate = json['from_date'];
    toDate = json['to_date'];
    status = json['status'];
    leaveType = json['leave_type'];
  }
}

class LeaveApprove {
  int? id;
  int? teamLeadApprove;
  String? teamLeadActionDate;
  String? teamLeadComment;
  int? lineManagerApprove;
  String? lineManagerComment;
  String? lineManagerActionDate;
  int? hrManagerApprove;
  String? hrManagerComment;
  String? hrManagerActionDate;
  int? leave;

  LeaveApprove(
      {this.id,
      this.teamLeadApprove,
      this.teamLeadActionDate,
      this.teamLeadComment,
      this.lineManagerApprove,
      this.lineManagerComment,
      this.lineManagerActionDate,
      this.hrManagerApprove,
      this.hrManagerComment,
      this.hrManagerActionDate,
      this.leave});

  LeaveApprove.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    teamLeadApprove = json['team_lead_approve'];
    teamLeadActionDate = json['team_lead_action_date'];
    teamLeadComment = json['team_lead_comment'];
    lineManagerApprove = json['line_manager_approve'];
    lineManagerComment = json['line_manager_comment'];
    lineManagerActionDate = json['line_manager_action_date'];
    hrManagerApprove = json['hr_manager_approve'];
    hrManagerComment = json['hr_manager_comment'];
    hrManagerActionDate = json['hr_manager_action_date'];
    leave = json['leave'];
  }
}
