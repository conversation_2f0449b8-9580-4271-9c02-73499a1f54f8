import 'package:e8_hr_portal/util/api_headers.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LoginModel {
  static String? result;
  static String? phone;
  static Token? token;
  static int? uid;
  static String? email;
  static String? name;
  static bool? isAdmin;
  static String? profilePhoto;
  static String? employeeId;
  static String? dateOfBirth;
  static String? dateOfJoin;
  static String? designation;
  static String? country;
  static String? isWorkFromHome;
  static bool? isOtpSnd;
  static String? policy;
  static int? policyId;
  static bool? meetingRoomPermission;
  static bool? isReimbursementReportingPerson;
  LoginModel.saveMeetingRoomPermission(bool? value) {
    meetingRoomPermission = value;
    storeMeetingRoomPermissonLocally(value);
  }
  storeMeetingRoomPermissonLocally(bool? value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool('meeting_room', value ?? false);
  }

  LoginModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    phone = json['phone'];
    token = json['token'] != null ? Token.fromJson(json['token']) : null;
    uid = json['uid'];
    email = json['email'];
    name = json['name'];
    isAdmin = json['is_admin'];
    profilePhoto = json['profilePhoto'];
    employeeId = json['employeeId'];
    dateOfBirth = json['dateOfBirth'];
    dateOfJoin = json['dateOfJoin'];
    designation = json['designation'];
    country = json['country'];
    isWorkFromHome = json['isWorkFromHome'];
    isOtpSnd = json['is_otp_snd'];
    policy = json['policy'];
    policyId = json['policy_id'];
    isReimbursementReportingPerson = json['reimbursement_approval'];
    storeUserDetailsLocally(json);
  }
  storeUserDetailsLocally(Map<String, dynamic> json) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString('result', result ?? '');
    prefs.setString('phone', phone ?? '');
    prefs.setInt('uid', uid ?? 0);
    prefs.setString('email', email ?? '');
    prefs.setString('name', name ?? '');
    prefs.setBool('is_admin', isAdmin ?? false);
    prefs.setString('profilePhoto', profilePhoto ?? '');
    prefs.setString('employeeId', employeeId ?? '');
    prefs.setString('dateOfBirth', dateOfBirth ?? '');
    prefs.setString('dateOfJoin', dateOfJoin ?? '');
    prefs.setString('designation', designation ?? '');
    prefs.setString('country', country ?? '');
    prefs.setString('isWorkFromHome', isWorkFromHome ?? '');
    prefs.setBool('is_otp_snd', isOtpSnd ?? false);
    prefs.setString('policy', policy ?? "");
    prefs.setInt('policy_id', policyId ?? 2);
    prefs.setBool(
        'reimbursement_approval', isReimbursementReportingPerson ?? false);
  }

  static getUserDetails() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    result = prefs.getString('result');
    phone = prefs.getString('phone');
    uid = prefs.getInt('uid');
    email = prefs.getString('email');
    name = prefs.getString('name');
    isAdmin = prefs.getBool('is_admin');
    profilePhoto = prefs.getString('profilePhoto');
    employeeId = prefs.getString('employeeId');
    dateOfBirth = prefs.getString('dateOfBirth');
    dateOfJoin = prefs.getString('dateOfJoin');
    designation = prefs.getString('designation');
    country = prefs.getString('country');
    isWorkFromHome = prefs.getString('isWorkFromHome');
    isOtpSnd = prefs.getBool('is_otp_snd');
    policy = prefs.getString('policy');
    policyId = prefs.getInt('policy_id');
    meetingRoomPermission = prefs.getBool('meeting_room');
    isReimbursementReportingPerson = prefs.getBool('reimbursement_approval');
  }
}

class Token {
  String? accessToken;
  String? refreshToken;
  String? expiresIn;
  String? readWriteGroups;
  String? tokenType;

  Token(
      {this.accessToken,
      this.refreshToken,
      this.expiresIn,
      this.readWriteGroups,
      this.tokenType});

  Token.fromJson(Map<String, dynamic> json) {
    accessToken = json['access_token'];
    refreshToken = json['refresh_token'];
    expiresIn = json['expires_in'];
    readWriteGroups = json['read write groups'];
    tokenType = json['token_type'];
    APIHeaders.setHeaders(accessToken);
  }
}
