class UserModel {
  String? name;
  String? designation;
  String? email;
  String? phone;
  String? uid;
  String? groupId;
  String? groupName;
  String? groupCode;
  bool? isAdmin;
  String? profilePhoto;
  String? approvalStatus;
  UserModel.fromJson(Map<String, dynamic>? json) {
    if (json != null) {
      name = json['name'];
      designation = json['designation_name'];
      email = json['email'];
      phone = json['phone'];
      uid = json['uid'];
      groupId = json['groupId'];
      groupName = json['groupName'];
      groupCode = json['groupCode'];
      isAdmin = json['role'] == 'admin';
      profilePhoto = json['profilePhoto'];
      approvalStatus = json['approval_status'];
    }
  }
}
