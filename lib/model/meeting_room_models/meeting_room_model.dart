import 'package:e8_hr_portal/model/amenity_model.dart';

class MeetingRoomModel {
  int? id;
  String? name;
  bool? availability;
  int? seatCount;
  bool? isActive;
  String? createdAt;
  List<AmenityModel>? amenities;
  List<RoomImgs>? roomImgs;
  int? policy;

  MeetingRoomModel(
      {this.id,
      this.name,
      this.availability,
      this.seatCount,
      this.isActive,
      this.createdAt,
      this.amenities,
      this.roomImgs,
      this.policy});

  MeetingRoomModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    availability = json['availability'];
    seatCount = json['seat_count'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    if (json['amenities'] != null) {
      amenities = <AmenityModel>[];
      json['amenities'].forEach((v) {
        amenities!.add(AmenityModel.fromJson(v));
      });
    }
    if (json['room_imgs'] != null) {
      roomImgs = <RoomImgs>[];
      json['room_imgs'].forEach((v) {
        roomImgs!.add(RoomImgs.fromJson(v));
      });
    }
    policy = json['policy'];
  }
}

class RoomImgs {
  int? id;
  String? name;
  String? image;

  RoomImgs({this.id, this.name, this.image});

  RoomImgs.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
  }
}
