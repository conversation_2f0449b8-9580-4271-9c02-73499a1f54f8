class AirlineModel {
  String? id;
  String? name;
  String? alias;
  String? iata;
  String? icao;
  String? callsign;
  String? country;
  String? active;

  AirlineModel(
      {this.id,
      this.name,
      this.alias,
      this.iata,
      this.icao,
      this.callsign,
      this.country,
      this.active});

  AirlineModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    alias = json['alias'];
    iata = json['iata'];
    icao = json['icao'];
    callsign = json['callsign'];
    country = json['country'];
    active = json['active'];
  }
}
