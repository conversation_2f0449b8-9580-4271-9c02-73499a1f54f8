class CheckListHistoryModel {
  int? id;
  int? assigneeId;
  String? responseType;
  String? point;
  String? details;
  bool? updatedStatus;
  String? schedule;
  String? scheduleId;
  String? dueDate;
  String? status;
  String? remarks;
  String? response;
  String? updatedAt;

  CheckListHistoryModel(
      {this.id,
      this.assigneeId,
      this.responseType,
      this.point,
      this.details,
      this.updatedStatus,
      this.schedule,
      this.scheduleId,
      this.dueDate,
      this.status,
      this.remarks,
      this.response,
      this.updatedAt});

  CheckListHistoryModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    assigneeId = json['assignee_id'];
    responseType = json['response_type'];
    point = json['point'];
    details = json['details'];
    updatedStatus = json['updated_status'];
    schedule = json['schedule'];
    scheduleId = json['schedule_id'];
    dueDate = json['due_date'];
    status = json['status'];
    remarks = json['remarks'];
    response = json['response'];
    updatedAt = json['updated_at'];
  }
}
