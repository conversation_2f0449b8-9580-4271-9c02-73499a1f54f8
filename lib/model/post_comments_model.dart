class PostCommentsModel {
  String? result;
  List<Records>? records;
  int? pagesCount;
  bool? hasNext;
  bool? hasPrevious;

  PostCommentsModel(
      {this.result,
      this.records,
      this.pagesCount,
      this.hasNext,
      this.hasPrevious});

  PostCommentsModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    if (json['records'] != null) {
      records = <Records>[];
      json['records'].forEach((v) {
        records!.add(Records.fromJson(v));
      });
    }
    pagesCount = json['pages_count'];
    hasNext = json['has_next'];
    hasPrevious = json['has_previous'];
  }
}

class Records {
  int? id;
  String? createdAt;
  String? name;
  String? designation;
  int? like;
  bool? userLiked;
  String? description;
  String? profilePhoto;
  bool? isOwner;

  Records({
    this.designation,
    this.id,
    this.createdAt,
    this.name,
    this.like,
    this.userLiked,
    this.description,
    this.profilePhoto,
    this.isOwner,
  });

  Records.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createdAt = json['created_at'];
    name = json['name'];
    like = json['like'];
    userLiked = json['user_liked'];
    description = json['description'];
    profilePhoto = json['profile_photo'];
    isOwner = json['is_owner'];
    designation = json['designation'];
  }
}
