class PostModel {
  int? id;
  String? createdAt;
  String? name;
  String? designation;
  int? like;
  int? comments;
  bool? userLiked;
  List<PostFile>? file;
  String? description;
  String? descriptionString;
  String? profilePhoto;
  bool? isOwner;
  String? postLink;
  List<int>? policy;
  int? surveyId;
  List<SurveyOptions>? options;
  String? type;
  String? questions;
  bool? isAllowResultPublic;
  bool? isAllowMultiple;

  PostModel(
      {this.id,
      this.createdAt,
      this.name,
      this.designation,
      this.like,
      this.comments,
      this.userLiked,
      this.file,
      this.description,
      this.descriptionString,
      this.profilePhoto,
      this.isOwner,
      this.postLink,
      this.policy,
      this.surveyId,
      this.options,
      this.type,
      this.questions,
      this.isAllowResultPublic,
      this.isAllowMultiple});

  PostModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createdAt = json['created_at'];
    name = json['name'];
    designation = json['designation'];
    like = json['like'];
    comments = json['comments'];
    userLiked = json['user_liked'];
    if (json['file'] != null) {
      file = <PostFile>[];
      json['file'].forEach((v) {
        file!.add(PostFile.fromJson(v));
      });
    }
    description = json['description'];
    descriptionString = json['description_string'];
    profilePhoto = json['profile_photo'];
    isOwner = json['is_owner'];
    postLink = json['post_link'];
    policy = json['policy'].cast<int>();
    surveyId = json['survey_id'];
    if (json['options'] != null) {
      options = <SurveyOptions>[];
      json['options'].forEach((v) {
        options!.add(SurveyOptions.fromJson(v));
      });
    }
    type = json['type'];
    questions = json['questions'];
    isAllowResultPublic = json['is_allow_result_public'];
    isAllowMultiple = json['is_allow_multiple'];
  }
}

class PostFile {
  int? id;
  String? file;
  String? fileType;
  String? thumbnail;

  PostFile({this.id, this.file, this.fileType, this.thumbnail});

  PostFile.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    file = json['file'];
    fileType = json['file_type'];
    thumbnail = json['thumbnail'];
  }
}

class SurveyOptions {
  int? id;
  String? name;
  int? totalVotes;
  bool? isVoted;
  double? votePercentage;
  int? groupValue;
  SurveyOptions(
      {this.id,
      this.name,
      this.isVoted,
      this.totalVotes,
      this.votePercentage,
      this.groupValue});

  SurveyOptions.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    totalVotes = json['total_votes'];
    isVoted = json['is_voted'];
    votePercentage = json['vote_percentage'];
    groupValue = json['group_value'];
  }
}
