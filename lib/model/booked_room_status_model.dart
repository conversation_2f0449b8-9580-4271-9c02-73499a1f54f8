class BookedRoomStatusModel {
  int? id;
  Room? room;
  String? bookingDate;
  String? startTime;
  String? endTime;
  BookedBy? bookedBy;
  List<InvitedUser>? invitedUser;
  String? status;
  String? description;
  String? reasonForCancel;
  Info? info;
  bool? selfCreated;
  BookedRoomStatusModel({
    this.id,
    this.room,
    this.bookingDate,
    this.startTime,
    this.endTime,
    this.bookedBy,
    this.invitedUser,
    this.status,
    this.description,
    this.reasonForCancel,
    this.info,
    this.selfCreated,
  });

  BookedRoomStatusModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    room = json['room'] != null ? Room.fromJson(json['room']) : null;
    bookingDate = json['booking_date'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    bookedBy =
        json['booked_by'] != null ? BookedBy.fromJson(json['booked_by']) : null;
    if (json['invited_user'] != null) {
      invitedUser = <InvitedUser>[];
      json['invited_user'].forEach((v) {
        invitedUser!.add(InvitedUser.fromJson(v));
      });
    }
    status = json['status'];
    description = json['description'];
    reasonForCancel = json['reason_for_cancel'];
    info = json['info'] != null ? Info.fromJson(json['info']) : null;
    selfCreated = json['self_created'] ?? false;
  }
}

class Room {
  int? id;
  String? name;
  bool? availability;
  int? seatCount;
  bool? isActive;
  String? createdAt;
  List<Amenities>? amenities;
  List<RoomImgs>? roomImgs;
  int? policy;

  Room(
      {this.id,
      this.name,
      this.availability,
      this.seatCount,
      this.isActive,
      this.createdAt,
      this.amenities,
      this.roomImgs,
      this.policy});

  Room.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    availability = json['availability'];
    seatCount = json['seat_count'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    if (json['amenities'] != null) {
      amenities = <Amenities>[];
      json['amenities'].forEach((v) {
        amenities!.add(Amenities.fromJson(v));
      });
    }
    if (json['room_imgs'] != null) {
      roomImgs = <RoomImgs>[];
      json['room_imgs'].forEach((v) {
        roomImgs!.add(RoomImgs.fromJson(v));
      });
    }
    policy = json['policy'];
  }
}

class Amenities {
  int? id;
  String? name;
  String? icon;
  bool? isActive;
  String? createdAt;
  String? updatedAt;

  Amenities(
      {this.id,
      this.name,
      this.icon,
      this.isActive,
      this.createdAt,
      this.updatedAt});

  Amenities.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    icon = json['icon'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}

class RoomImgs {
  int? id;
  String? name;
  String? image;

  RoomImgs({this.id, this.name, this.image});

  RoomImgs.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
  }
}

class BookedBy {
  String? user;
  int? userId;
  String? profilePic;
  String? designation;

  BookedBy({this.user, this.userId, this.profilePic, this.designation});

  BookedBy.fromJson(Map<String, dynamic> json) {
    user = json['user'];
    userId = json['user_id'];
    profilePic = json['profile_pic'];
    designation = json['designation'];
  }
}

class InvitedUser {
  int? id;
  String? user;
  int? userId;
  String? status;
  String? profilePic;
  String? designation;

  InvitedUser(
      {this.id,
      this.user,
      this.userId,
      this.status,
      this.profilePic,
      this.designation});

  InvitedUser.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    user = json['user'];
    userId = json['user_id'];
    status = json['status'];
    profilePic = json['profile_pic'];
    designation = json['designation'];
  }
}

class Info {
  String? rejectedReason;

  Info({this.rejectedReason});

  Info.fromJson(Map<String, dynamic> json) {
    rejectedReason = json['rejected_reason'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['rejected_reason'] = rejectedReason;
    return data;
  }
}
