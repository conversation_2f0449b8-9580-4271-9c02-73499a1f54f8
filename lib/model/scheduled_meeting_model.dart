class SchduledMeetingModel {
  String? result;
  List<Data>? data;

  SchduledMeetingModel({this.result, this.data});

  SchduledMeetingModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }
}

class Data {
  int? id;
  List<Room>? room;
  String? meetingDate;
  String? startTime;
  String? endTime;
  BookedBy? bookedBy;
  List<InvitedUser>? invitedUser;
  String? status;
  String? description;
  String? reason;
  Data(
      {this.reason,
      this.id,
      this.room,
      this.bookedBy,
      this.invitedUser,
      this.status,
      this.meetingDate,
      this.startTime,
      this.endTime,
      this.description});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    if (json['room'] != null) {
      room = <Room>[];
      json['room'].forEach((v) {
        room!.add(Room.fromJson(v));
      });
    }
    bookedBy =
        json['booked_by'] != null ? BookedBy.fromJson(json['booked_by']) : null;
    if (json['invited_user'] != null) {
      invitedUser = <InvitedUser>[];
      json['invited_user'].forEach((v) {
        invitedUser!.add(InvitedUser.fromJson(v));
      });
    }
    meetingDate = json['booking_date'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    status = json['status'];
    description = json['description'];
    reason = json['reason_for_cancel'];
  }
}

class Room {
  int? id;
  String? name;
  bool? availability;
  int? seatCount;
  bool? isActive;
  String? createdAt;
  List<Amenities>? amenities;
  List<RoomImgs>? roomImgs;

  Room(
      {this.id,
      this.name,
      this.availability,
      this.seatCount,
      this.isActive,
      this.createdAt,
      this.amenities,
      this.roomImgs});

  Room.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    availability = json['availability'];
    seatCount = json['seat_count'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    if (json['amenities'] != null) {
      amenities = <Amenities>[];
      json['amenities'].forEach((v) {
        amenities!.add(Amenities.fromJson(v));
      });
    }
    if (json['room_imgs'] != null) {
      roomImgs = <RoomImgs>[];
      json['room_imgs'].forEach((v) {
        roomImgs!.add(RoomImgs.fromJson(v));
      });
    }
  }
}

class Amenities {
  int? id;
  String? name;
  String? icon;
  bool? isActive;
  String? createdAt;
  String? updatedAt;

  Amenities(
      {this.id,
      this.name,
      this.icon,
      this.isActive,
      this.createdAt,
      this.updatedAt});

  Amenities.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    icon = json['icon'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}

class RoomImgs {
  int? id;
  String? name;
  String? image;

  RoomImgs({this.id, this.name, this.image});

  RoomImgs.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
  }
}

class BookedBy {
  int? id;
  String? username;
  String? firstName;
  String? lastName;
  String? email;
  bool? isSuperuser;
  String? startDate;
  ProfileDetails? profileDetails;

  BookedBy(
      {this.id,
      this.username,
      this.firstName,
      this.lastName,
      this.email,
      this.isSuperuser,
      this.startDate,
      this.profileDetails});

  BookedBy.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    username = json['username'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    email = json['email'];
    isSuperuser = json['is_superuser'];
    startDate = json['start_date'];
    profileDetails = json['profile_details'] != null
        ? ProfileDetails.fromJson(json['profile_details'])
        : null;
  }
}

class ProfileDetails {
  String? employType;
  String? userType;
  String? maritalStatus;
  String? designation;
  String? birthDate;
  String? profilePic;
  String? userTimeZone;
  int? department;
  String? phoneNumber;
  String? gender;
  String? emiratesId;
  String? passportNumber;
  String? postcode;
  int? empUniqueId;
  String? agentId;
  String? country;

  ProfileDetails(
      {this.employType,
      this.userType,
      this.maritalStatus,
      this.designation,
      this.birthDate,
      this.profilePic,
      this.userTimeZone,
      this.department,
      this.phoneNumber,
      this.gender,
      this.emiratesId,
      this.passportNumber,
      this.postcode,
      this.empUniqueId,
      this.agentId,
      this.country});

  ProfileDetails.fromJson(Map<String, dynamic> json) {
    employType = json['employ_type'];
    userType = json['user_type'];
    maritalStatus = json['marital_status'];
    designation = json['designation'];
    birthDate = json['birth_date'];
    profilePic = json['profile_pic'];
    userTimeZone = json['user_time_zone'];
    department = json['department'];
    phoneNumber = json['phone_number'];
    gender = json['gender'];
    emiratesId = json['emirates_id'];
    passportNumber = json['passport_number'];
    postcode = json['postcode'];
    empUniqueId = json['emp_unique_id'];
    agentId = json['agent_id'];
    country = json['country'];
  }
}

class InvitedUser {
  int? id;
  String? user;
  String? profilePic;
  String? designation;
  InvitedUser({this.id, this.user, this.profilePic, this.designation});

  InvitedUser.fromJson(Map<String, dynamic> json) {
    designation = json['designation'];
    id = json['id'];
    user = json['user'];
    profilePic = json['profile_pic'];
  }
}
