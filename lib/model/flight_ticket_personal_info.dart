class FlightTicketPersonalInfo {
  String? name;
  String? employeeId;
  String? department;
  String? designation;
  String? email;
  String? phoneNo;
  String? dob;
  String? doj;
  String? nationality;
  Passport? passport;
  Visa? visa;
  String? emiratesId;
  String? salary;
  Bank? bank;
  IsProfileCompleted? isProfileCompleted;
  bool? isEmiratesIdExpired;
  FlightTicketPersonalInfo(
      {this.name,
      this.employeeId,
      this.department,
      this.designation,
      this.email,
      this.phoneNo,
      this.dob,
      this.doj,
      this.nationality,
      this.passport,
      this.visa,
      this.emiratesId,
      this.salary,
      this.bank,
      this.isProfileCompleted});

  FlightTicketPersonalInfo.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    employeeId = json['employee_id'];
    department = json['department'];
    designation = json['designation'];
    email = json['email'];
    phoneNo = json['phone_no'];
    dob = json['dob'];
    doj = json['doj'];
    nationality = json['nationality'];
    passport =
        json['passport'] != null ? Passport.fromJson(json['passport']) : null;
    visa = json['visa'] != null ? Visa.fromJson(json['visa']) : null;
    emiratesId = json['emirates_id'];
    salary = json['salary'];
    bank = json['bank'] != null ? Bank.fromJson(json['bank']) : null;
    isProfileCompleted = json['is_profile_completed'] != null
        ? IsProfileCompleted.fromJson(json['is_profile_completed'])
        : null;
    isEmiratesIdExpired = json["is_emirates_id_expired"];
  }
}

class Passport {
  String? status;
  String? passportNumber;
  String? passportExpiry;
  bool? isExpired;

  Passport({this.passportNumber, this.passportExpiry, this.isExpired});

  Passport.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    passportNumber = json['passport_number'];
    passportExpiry = json['passport_expiry'];
    isExpired = json['is_expired'];
  }
}

class Visa {
  String? status;
  String? expiry;

  Visa({this.status, this.expiry});

  Visa.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    expiry = json['expiry'];
  }
}

class Bank {
  String? accountNumber;
  String? iban;
  String? branch;

  Bank({this.accountNumber, this.iban, this.branch});

  Bank.fromJson(Map<String, dynamic> json) {
    accountNumber = json['account_number'];
    iban = json['iban'];
    branch = json['branch'];
  }
}

class IsProfileCompleted {
  bool? isCompleted;
  String? message;

  IsProfileCompleted({this.isCompleted, this.message});

  IsProfileCompleted.fromJson(Map<String, dynamic> json) {
    isCompleted = json['is_completed'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['is_completed'] = isCompleted;
    data['message'] = message;
    return data;
  }
}
