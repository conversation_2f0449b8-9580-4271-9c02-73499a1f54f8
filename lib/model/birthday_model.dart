class BirthdayModel {
  String? name;
  String? dateOfBirth;
  String? dateOfJoining;
  String? profilePic;
  int? year;
  String? designation;
  bool? workAnniversaryStatus;
  String? model;

  BirthdayModel({
    this.name,
    this.dateOfBirth,
    this.dateOfJoining,
    this.profilePic,
    this.year,
    this.designation,
    this.workAnniversaryStatus,
    this.model,
  });

  BirthdayModel.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    dateOfBirth = json['date_of_birth'];
    dateOfJoining = json['date_of_joining'];
    profilePic = json['profile_pic'];
    year = json['year'];
    designation = json['designation'];
    workAnniversaryStatus = json['work_anniversary_status'];
    model = json['model'];
  }
}
