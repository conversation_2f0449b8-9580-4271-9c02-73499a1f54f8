class FlightTicketApprovedListModel {
  int? id;
  int? ticketRequestId;
  String? sender;
  String? requestedDate;
  String? approvedBy;
  String? status;
  String? approvedOrRejectedDate;
  String? deperatureDate;
  String? returnDate;
  int? noOfDays;
  String? countryOfDeparture;
  String? cityOfDeperature;
  String? countryOfArrival;
  String? cityOfArrival;
  String? airline;
  String? rejectedComment;
  String? remark;
  TktCount? tktCount;
  int? extraTicket;
  List<TktData>? tktData;
  List<TktUploadData>? tktUploadData;
  List<ApprvRejData>? apprvRejData;

  FlightTicketApprovedListModel(
      {this.id,
      this.ticketRequestId,
      this.sender,
      this.requestedDate,
      this.approvedBy,
      this.status,
      this.approvedOrRejectedDate,
      this.deperatureDate,
      this.returnDate,
      this.noOfDays,
      this.countryOfDeparture,
      this.cityOfDeperature,
      this.countryOfArrival,
      this.cityOfArrival,
      this.airline,
      this.rejectedComment,
      this.remark,
      this.tktCount,
      this.extraTicket,
      this.tktData,
      this.tktUploadData,
      this.apprvRejData});

  FlightTicketApprovedListModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    ticketRequestId = json['ticket_request_id'];
    sender = json['sender'];
    requestedDate = json['requested_date'];
    approvedBy = json['approved_by'];
    status = json['status'];
    approvedOrRejectedDate = json['approved_or_rejected_date'];
    deperatureDate = json['deperature_date'];
    returnDate = json['return_date'];
    noOfDays = json['no_of_days'];
    countryOfDeparture = json['country_of_departure'];
    cityOfDeperature = json['city_of_deperature'];
    countryOfArrival = json['country_of_arrival'];
    cityOfArrival = json['city_of_arrival'];
    airline = json['airline'];
    rejectedComment = json['rejected_comment'];
    remark = json['remark'];
    if (json['tkt_data'] != null) {
      tktData = <TktData>[];
      json['tkt_data'].forEach((v) {
        tktData!.add(TktData.fromJson(v));
      });
    }
    extraTicket = json['extra_ticket'];
    if (json['tkt_data'] != String) {
      tktData = <TktData>[];
      json['tkt_data'].forEach((v) {
        tktData!.add(TktData.fromJson(v));
      });
    }
    if (json['tkt_upload_data'] != null) {
      tktUploadData = <TktUploadData>[];
      json['tkt_upload_data'].forEach((v) {
        tktUploadData!.add(TktUploadData.fromJson(v));
      });
    }
    if (json['apprv_rej_data'] != String) {
      apprvRejData = <ApprvRejData>[];
      json['apprv_rej_data'].forEach((v) {
        apprvRejData!.add(ApprvRejData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['requested_date'] = requestedDate;
    data['approved_by'] = approvedBy;
    data['status'] = status;
    data['approved_or_rejected_date'] = approvedOrRejectedDate;
    data['deperature_date'] = deperatureDate;
    data['return_date'] = returnDate;
    data['no_of_days'] = noOfDays;
    data['country_of_departure'] = countryOfDeparture;
    data['city_of_deperature'] = cityOfDeperature;
    data['country_of_arrival'] = countryOfArrival;
    data['city_of_arrival'] = cityOfArrival;
    data['airline'] = airline;
    data['rejected_comment'] = rejectedComment;
    data['remark'] = remark;
    data['tkt_count'] = tktCount!.toJson();
    data['extra_ticket'] = extraTicket;
    data['tkt_data'] = tktData!.map((v) => v.toJson()).toList();
    data['apprv_rej_data'] = apprvRejData!.map((v) => v.toJson()).toList();
    return data;
  }
}

class TktCount {
  int? eligibleTicket;
  int? takenTicket;
  int? balanceTicket;

  TktCount({this.eligibleTicket, this.takenTicket, this.balanceTicket});

  TktCount.fromJson(Map<String, dynamic> json) {
    eligibleTicket = json['eligible_ticket'];
    takenTicket = json['taken_ticket'];
    balanceTicket = json['balance_ticket'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['eligible_ticket'] = eligibleTicket;
    data['taken_ticket'] = takenTicket;
    data['balance_ticket'] = balanceTicket;
    return data;
  }
}

class TktData {
  int? id;
  String? title;
  String? fileType;
  String? file;

  TktData({this.id, this.title, this.fileType, this.file});

  TktData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    fileType = json['file_type'];
    file = json['file'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['file_type'] = fileType;
    data['file'] = file;
    return data;
  }
}

class TktUploadData {
  int? id;
  String? title;
  String? fileType;
  String? file;

  TktUploadData({this.id, this.title, this.fileType, this.file});

  TktUploadData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    fileType = json['file_type'];
    file = json['file'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['file_type'] = fileType;
    data['file'] = file;
    return data;
  }
}

class ApprvRejData {
  int? id;
  int? userId;
  String? userName;
  String? levelStatus;
  String? levelType;
  String? requestedDate;
  String? remark;
  String? profilePic;

  ApprvRejData(
      {this.id,
      this.userId,
      this.userName,
      this.levelStatus,
      this.levelType,
      this.requestedDate,
      this.remark,
      this.profilePic});

  ApprvRejData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    userName = json['user_name'];
    levelStatus = json['level_status'];
    levelType = json['level_type'];
    requestedDate = json['requested_date'];
    remark = json['remark'];
    profilePic = json['profile_pic'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['user_name'] = userName;
    data['level_status'] = levelStatus;
    data['level_type'] = levelType;
    data['requested_date'] = requestedDate;
    data['remark'] = remark;
    data['profile_pic'] = profilePic;
    return data;
  }
}
