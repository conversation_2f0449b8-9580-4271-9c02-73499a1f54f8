class GetNotificationModel {
  int? id;
  Info? info;
  String? message;
  bool? read;
  String? createdAt;
  String? updatedAt;
  String? newDateTime;
  UserDetails? userDetails;

  GetNotificationModel({
    this.id,
    this.info,
    this.message,
    this.read,
    this.createdAt,
    this.updatedAt,
    this.userDetails,
    this.newDateTime,
  });

  GetNotificationModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    info = json['info'] != null ? Info.fromJson(json['info']) : null;
    message = json['message'];
    read = json['read'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    newDateTime = json['new_date_time'];
    userDetails = json['user_details'] != null
        ? UserDetails.fromJson(json['user_details'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    if (info != null) {
      data['info'] = info!.toJson();
    }
    data['message'] = message;
    data['read'] = read;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    if (userDetails != null) {
      data['user_details'] = userDetails!.toJson();
    }
    return data;
  }
}

class Info {
  String? type;
  String? action;
  int? actionId;

  Info({this.type, this.action, this.actionId});

  Info.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    action = json['action'];
    if (json['action_id'] != null && json['action_id'].toString().isNotEmpty) {
      actionId = int.parse('${json['action_id']}');
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    data['action'] = action;
    data['action_id'] = actionId;
    return data;
  }
}

class UserDetails {
  int? id;
  String? name;
  String? email;
  String? designation;
  String? profilePic;

  UserDetails(
      {this.id, this.name, this.email, this.designation, this.profilePic});

  UserDetails.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    designation = json['designation'];
    profilePic = json['profile_pic'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['email'] = email;
    data['designation'] = designation;
    data['profile_pic'] = profilePic;
    return data;
  }
}
