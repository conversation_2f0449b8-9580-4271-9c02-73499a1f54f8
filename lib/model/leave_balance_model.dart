class LeaveBalanceModel {
  String? result;
  List<Data>? data;
  String? message;
  num? totalBalanceLeave;
  num? totalTakenLeaves;

  LeaveBalanceModel({
    this.result,
    this.data,
    this.message,
    this.totalBalanceLeave,
    this.totalTakenLeaves,
  });

  LeaveBalanceModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
    if (json["message"] != null) {
      message = json["message"];
    }
    if (json['total_balance_leave'] != null) {
      totalBalanceLeave = json['total_balance_leave'];
    }
    if (json["total_taken_leaves"] != null) {
      totalTakenLeaves = json['total_taken_leaves'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['result'] = result;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  LeaveType? leaveType;
  num? totalLeave;
  num? leavesTaken;
  num? availableLeave;
  LeavePolicy? leavePolicy;

  Data(
      {this.leaveType,
      this.totalLeave,
      this.leavesTaken,
      this.availableLeave,
      this.leavePolicy});

  Data.fromJson(Map<String, dynamic> json) {
    leaveType = json['leave_type'] != null
        ? LeaveType.fromJson(json['leave_type'])
        : null;
    totalLeave = json['total_leave'];
    leavesTaken = json['leaves_taken'];
    availableLeave = json['available_leave'];
    leavePolicy = json['leave_policy'] != null
        ? LeavePolicy.fromJson(json['leave_policy'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (leaveType != null) {
      data['leave_type'] = leaveType!.toJson();
    }
    data['total_leave'] = totalLeave;
    data['leaves_taken'] = leavesTaken;
    data['available_leave'] = availableLeave;
    if (leavePolicy != null) {
      data['leave_policy'] = leavePolicy!.toJson();
    }
    return data;
  }
}

class LeaveType {
  int? id;
  String? name;
  bool? active;

  LeaveType({this.id, this.name, this.active});

  LeaveType.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    active = json['active'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['active'] = active;
    return data;
  }
}

class LeavePolicy {
  int? id;
  String? name;
  String? startDate;
  String? endDate;

  LeavePolicy({this.id, this.name, this.startDate, this.endDate});

  LeavePolicy.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    startDate = json['start_date'];
    endDate = json['end_date'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    return data;
  }
}
