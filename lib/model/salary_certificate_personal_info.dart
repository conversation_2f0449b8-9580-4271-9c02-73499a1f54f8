class SalaryCertificatePersonalInfo {
  String? name;
  String? employeeId;
  String? department;
  String? designation;
  String? email;
  String? phoneNo;
  String? dob;
  String? doj;
  String? nationality;
  Passport? passport;
  String? visaStatus;
  String? emiratesId;
  String? salary;
  Bank? bank;
  IsProfileCompleted? isProfileCompleted;

  SalaryCertificatePersonalInfo({
    this.name,
    this.employeeId,
    this.department,
    this.designation,
    this.email,
    this.phoneNo,
    this.dob,
    this.doj,
    this.nationality,
    this.passport,
    this.visaStatus,
    this.emiratesId,
    this.salary,
    this.bank,
    this.isProfileCompleted,
  });

  SalaryCertificatePersonalInfo.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    employeeId = json['employee_id'];
    department = json['department'];
    designation = json['designation'];
    email = json['email'];
    phoneNo = json['phone_no'];
    dob = json['dob'];
    doj = json['doj'];
    nationality = json['nationality'];
    passport =
        json['passport'] != null ? Passport.fromJson(json['passport']) : null;
    visaStatus = json['visa_status'];
    emiratesId = json['emirates_id'];
    salary = json['salary'];
    bank = json['bank'] != null ? Bank.fromJson(json['bank']) : null;
    isProfileCompleted = json['is_profile_completed'] != null
        ? IsProfileCompleted.fromJson(json['is_profile_completed'])
        : null;
  }
}

class Passport {
  String? passportNumber;
  String? passportExpiry;
  bool? isExpired;

  Passport({this.passportNumber, this.passportExpiry, this.isExpired});

  Passport.fromJson(Map<String, dynamic> json) {
    passportNumber = json['passport_number'];
    passportExpiry = json['passport_expiry'];
    isExpired = json['is_expired'];
  }
}

class IsProfileCompleted {
  bool? isCompleted;
  String? message;

  IsProfileCompleted({this.isCompleted, this.message});

  IsProfileCompleted.fromJson(Map<String, dynamic> json) {
    isCompleted = json['is_completed'];
    message = json['message'];
  }
}

class Bank {
  String? accountNumber;
  String? iban;
  String? branch;

  Bank({this.accountNumber, this.iban, this.branch});

  Bank.fromJson(Map<String, dynamic> json) {
    accountNumber = json['account_number'];
    iban = json['iban'];
    branch = json['branch'];
  }
}
