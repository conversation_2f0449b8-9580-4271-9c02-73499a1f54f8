class UserListModel {
  String? result;
  List<Data>? data;

  UserListModel({this.result, this.data});

  UserListModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['result'] = result;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  int? id;
  String? name;
  String? email;
  String? profilePic;
  List<Designation>? designation;
  List<UserStatus>? userStatus;

  Data(
      {this.id,
      this.name,
      this.email,
      this.profilePic,
      this.designation,
      this.userStatus});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    profilePic = json['profile_pic'];
    if (json['designation'] != null) {
      designation = <Designation>[];
      json['designation'].forEach((v) {
        designation!.add(Designation.fromJson(v));
      });
    }
    if (json['user_status'] != null) {
      userStatus = <UserStatus>[];
      json['user_status'].forEach((v) {
        userStatus!.add(UserStatus.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['email'] = email;
    data['profile_pic'] = profilePic;
    if (designation != null) {
      data['designation'] = designation!.map((v) => v.toJson()).toList();
    }
    if (userStatus != null) {
      data['user_status'] = userStatus!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Designation {
  int? id;
  String? name;
  String? description;
  String? sortOrder;

  Designation({this.id, this.name, this.description, this.sortOrder});

  Designation.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    sortOrder = json['sort_order'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['sort_order'] = sortOrder;
    return data;
  }
}

class UserStatus {
  int? id;
  String? name;
  bool? isActive;
  String? createdAt;
  String? updatedAt;

  UserStatus(
      {this.id, this.name, this.isActive, this.createdAt, this.updatedAt});

  UserStatus.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['is_active'] = isActive;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
