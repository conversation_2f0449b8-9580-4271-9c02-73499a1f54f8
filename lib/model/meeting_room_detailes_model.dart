class MeetingRoomDetailesModel {
  String? result;
  Data? data;

  MeetingRoomDetailesModel({this.result, this.data});

  MeetingRoomDetailesModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }
}

class Data {
  int? id;
  String? name;
  bool? availability;
  int? seatCount;
  bool? isActive;
  String? createdAt;
  List<Amenities>? amenities;
  List<RoomImgs>? roomImgs;

  Data(
      {this.id,
      this.name,
      this.availability,
      this.seatCount,
      this.isActive,
      this.createdAt,
      this.amenities,
      this.roomImgs});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    availability = json['availability'];
    seatCount = json['seat_count'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    if (json['amenities'] != null) {
      amenities = <Amenities>[];
      json['amenities'].forEach((v) {
        amenities!.add(Amenities.fromJson(v));
      });
    }
    if (json['room_imgs'] != null) {
      roomImgs = <RoomImgs>[];
      json['room_imgs'].forEach((v) {
        roomImgs!.add(RoomImgs.fromJson(v));
      });
    }
  }
}

class Amenities {
  int? id;
  String? name;
  String? icon;
  bool? isActive;
  String? createdAt;
  String? updatedAt;

  Amenities(
      {this.id,
      this.name,
      this.icon,
      this.isActive,
      this.createdAt,
      this.updatedAt});

  Amenities.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    icon = json['icon'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}

class RoomImgs {
  int? id;
  String? name;
  String? image;

  RoomImgs({this.id, this.name, this.image});

  RoomImgs.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
  }
}
