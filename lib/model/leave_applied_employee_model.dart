class LeaveAppliedEmployeeModel {
  UserDetails? userDetails;
  int? id;
  String? startDate;
  String? endDate;
  int? status;
  String? leaveType;
  String? dayType;

  LeaveAppliedEmployeeModel(
      {this.userDetails,
      this.id,
      this.startDate,
      this.endDate,
      this.status,
      this.leaveType,
      this.dayType});

  LeaveAppliedEmployeeModel.fromJson(Map<String, dynamic> json) {
    userDetails = json['user_details'] != null
        ? UserDetails.fromJson(json['user_details'])
        : null;
    id = json['id'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    status = json['status'];
    leaveType = json['leave_type'];
    dayType = json['day_type'];
  }
}

class UserDetails {
  int? id;
  String? name;
  String? email;
  String? designation;
  String? profilePic;

  UserDetails(
      {this.id, this.name, this.email, this.designation, this.profilePic});

  UserDetails.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    designation = json['designation'];
    profilePic = json['profile_pic'];
  }
}
