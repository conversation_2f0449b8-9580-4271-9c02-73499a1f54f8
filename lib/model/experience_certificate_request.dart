class ExperienceCertifcateRequest {
  int? id;
  String? requestedDate;
  String? approvedBy;
  String? status;
  String? approvedOrRejectedDate;
  String? rejectedComment;

  ExperienceCertifcateRequest(
      {this.id,
      this.requestedDate,
      this.approvedBy,
      this.status,
      this.approvedOrRejectedDate,
      this.rejectedComment});

  ExperienceCertifcateRequest.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    requestedDate = json['requested_date'];
    approvedBy = json['approved_by'];
    status = json['status'];
    approvedOrRejectedDate = json['approved_or_rejected_date'];
    rejectedComment = json['rejected_comment'];
  }
}
