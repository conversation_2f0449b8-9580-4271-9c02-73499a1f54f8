import 'package:cloud_firestore/cloud_firestore.dart';

class LatestUpdate {
  LatestUpdate({
    required dateTime,
    required description,
    required uid,
    required user,
  });
  Timestamp? dateTime;
  String? description;
  String? uid;
  String? user;

  LatestUpdate.fromJson(Map<String, dynamic> json) {
    dateTime = json["dateTime"];
    description = json["uid"];
    uid = json["uid"];
    user = json["user"];
  }
}
