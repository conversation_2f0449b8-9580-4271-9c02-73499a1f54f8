class UserCurrentTasks {
  int? id;
  String? note;
  String? title;
  String? stage;
  String? project;
  int? timing;
  int? status;
  String? createdAt;

  UserCurrentTasks(
      {this.id,
      this.note,
      this.title,
      this.stage,
      this.project,
      this.timing,
      this.status,
      this.createdAt});

  UserCurrentTasks.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    note = json['note'];
    title = json['title'];
    stage = json['stage'];
    project = json['project'];
    timing = json['timing'];
    status = json['status'];
    createdAt = json['created_at'];
  }
}
