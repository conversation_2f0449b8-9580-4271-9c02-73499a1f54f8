class AttendanceModel {
  int? id;
  String? date;
  String? attendanceDate;
  String? name;
  String? arrival;
  String? punchin;
  String? punchout;
  String? grossHours;
  bool? data;
  Status? status;
  String? rejectedComment;
  String? rejectedBy;

  AttendanceModel({
    this.id,
    this.date,
    this.attendanceDate,
    this.name,
    this.arrival,
    this.punchin,
    this.punchout,
    this.grossHours,
    this.data,
    this.status,
    this.rejectedComment,
    this.rejectedBy,
  });

  AttendanceModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    date = json['date'];
    attendanceDate = json['attendance_date'];
    name = json['name'];
    arrival = json['arrival'];
    punchin = json['punchin'];
    punchout = json['punchout'];
    grossHours = json['gross_hours'];
    data = json['data'];
    status = json['status'] != null ? Status.fromJson(json['status']) : null;
    rejectedComment = json['rejected_comment'];
    rejectedBy = json['rejected_by'];
  }
}

class Status {
  String? content;
  String? color;
  bool? isDisabled;
  Status({this.content, this.color, this.isDisabled});
  Status.fromJson(Map<String, dynamic> json) {
    content = json['content'];
    color = json['color'];
    isDisabled = json['is_disabled'];
  }
}
