class UserTicketsList {
  int? id;
  String? ticketId;
  Sender? sender;
  String? subject;
  String? description;
  bool? isDelete;
  String? createdAt;
  String? status;
  String? image;

  UserTicketsList(
      {this.id,
      this.ticketId,
      this.sender,
      this.subject,
      this.description,
      this.isDelete,
      this.createdAt,
      this.status,
      this.image});

  UserTicketsList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    ticketId = json['ticket_id'];
    sender = json['sender'] != null ? Sender.fromJson(json['sender']) : null;
    subject = json['subject'];
    description = json['description'];
    isDelete = json['is_delete'];
    createdAt = json['created_at'];
    status = json['status'];
    image = json['ticket_img'];
  }
}

class Sender {
  int? id;
  String? name;
  String? email;
  String? designation;
  String? profilePic;

  Sender({this.id, this.name, this.email, this.designation, this.profilePic});

  Sender.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    designation = json['designation'];
    profilePic = json['profile_pic'];
  }
}
