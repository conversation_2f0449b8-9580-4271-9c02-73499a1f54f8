class AttendanceDetailsModel {
  String? result;
  Records? records;

  AttendanceDetailsModel({this.result, this.records});

  AttendanceDetailsModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    records =
        json['records'] != null ? Records.fromJson(json['records']) : null;
  }
}

class Records {
  int? id;
  String? day;
  String? attendanceDate;
  String? name;
  String? arrival;
  String? punchin;
  String? punchout;
  String? grossHours;
  bool? data;
  Status? status;
  String? rejectedComment;
  String? rejectedBy;
  bool? buttonVisibility;
  bool? isLeaveButtonShow;
  String? requestReason;
  String? requestExplanation;

  Records({
    this.id,
    this.day,
    this.attendanceDate,
    this.name,
    this.arrival,
    this.punchin,
    this.punchout,
    this.grossHours,
    this.data,
    this.status,
    this.rejectedComment,
    this.rejectedBy,
    this.buttonVisibility,
    this.isLeaveButtonShow,
    this.requestReason,
    this.requestExplanation,
  });

  Records.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    day = json['date'];
    attendanceDate = json['attendance_date'];
    name = json['name'];
    arrival = json['arrival'];
    punchin = json['punchin'];
    punchout = json['punchout'];
    grossHours = json['gross_hours'];
    data = json['data'];
    status = json['status'] != null ? Status.fromJson(json['status']) : null;
    rejectedComment = json['rejected_comment'];
    rejectedBy = json['rejected_by'];
    buttonVisibility = json['is_send_request_button'];
    isLeaveButtonShow = json['is_request_leave'];
    requestReason = json['request_reason'];
    requestExplanation = json['request_explanation'];
  }
}

class Status {
  String? content;
  String? color;

  Status({this.content, this.color});

  Status.fromJson(Map<String, dynamic> json) {
    content = json['content'];
    color = json['color'];
  }
}
