import '../util/html_util.dart';

class BadgeModel {
  int? id;
  String? name;
  String? image;
  bool? isLocked;
  String? description;
  int? achieveCount;

  BadgeModel(
      {this.id,
      this.name,
      this.image,
      this.isLocked,
      this.description,
      this.achieveCount});

  BadgeModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
    isLocked = json['is_locked'];
    description = HTMLUtil.parseHtmlString(json['description'] ?? '');
    achieveCount = json['achieve_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['image'] = image;
    data['is_locked'] = isLocked;
    data['description'] = description;
    data['achieve_count'] = achieveCount;
    return data;
  }
}
