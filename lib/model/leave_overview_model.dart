class LeaveOverviewModel {
  int? id;
  LeaveDetails? leaveDetails;
  HrStatus? hrStatus;
  List<ReportingPersonList>? reportingPersonList;

  LeaveOverviewModel(
      {this.id, this.leaveDetails, this.hrStatus, this.reportingPersonList});

  LeaveOverviewModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    leaveDetails = json['leave_details'] != null
        ? LeaveDetails.fromJson(json['leave_details'])
        : null;
    hrStatus =
        json['hr_status'] != null ? HrStatus.fromJson(json['hr_status']) : null;
    if (json['reporting_person_list'] != null) {
      reportingPersonList = <ReportingPersonList>[];
      json['reporting_person_list'].forEach((v) {
        reportingPersonList!.add(ReportingPersonList.fromJson(v));
      });
    }
  }
}

class LeaveDetails {
  int? id;
  String? startDate;
  String? endDate;
  String? status;
  String? dayType;
  String? reason;
  String? leaveType;
  num? dayCount;
  String? createdAt;
  String? leaveDoc;
  String? docExtension;
  StaffIncharge? staffIncharge;

  LeaveDetails(
      {this.id,
      this.startDate,
      this.endDate,
      this.status,
      this.dayType,
      this.reason,
      this.leaveType,
      this.dayCount,
      this.createdAt,
      this.leaveDoc,
      this.docExtension,
      this.staffIncharge});

  LeaveDetails.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    status = json['status'];
    dayType = json['day_type'];
    reason = json['reason'];
    leaveType = json['leave_type'];
    dayCount = json['day_count'];
    createdAt = json['created_at'];
    leaveDoc = json['leave_doc'];
    docExtension = json['doc_extension'];
    staffIncharge = json['staff_incharge'] != null
        ? StaffIncharge.fromJson(json['staff_incharge'])
        : null;
  }
}

class StaffIncharge {
  int? id;
  String? name;
  String? email;
  String? designation;
  String? isApprove;
  String? profilePic;

  StaffIncharge(
      {this.id, this.name, this.email, this.designation, this.profilePic});

  StaffIncharge.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    designation = json['designation'];
    isApprove = json['is_approve'].toString();
    profilePic = json['profile_pic'];
  }
}

class HrStatus {
  int? id;
  String? name;
  List<Role>? role;
  String? profilePic;
  String? isApprove;
  String? comment;
  String? createAt;

  HrStatus(
      {this.id,
      this.name,
      this.role,
      this.profilePic,
      this.isApprove,
      this.comment,
      this.createAt});

  HrStatus.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    if (json['role'] != null) {
      role = <Role>[];
      json['role'].forEach((v) {
        role!.add(Role.fromJson(v));
      });
    }
    profilePic = json['profile_pic'];
    isApprove = json['is_approve'].toString();
    comment = json['comment'];
    if (json['created_at'] != null) {
      createAt = json['created_at'];
    }
  }
}

class Role {
  String? name;

  Role({this.name});

  Role.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }
}

class ReportingPersonList {
  int? id;
  String? name;
  String? role;
  String? profilePic;
  String? isApprove;
  String? comment;
  String? createAt;

  ReportingPersonList({
    this.id,
    this.name,
    this.role,
    this.profilePic,
    this.isApprove,
    this.comment,
    this.createAt,
  });

  ReportingPersonList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    role = json['role'];
    profilePic = json['profile_pic'];
    isApprove = json['is_approve'].toString();
    comment = json['comment'];
    if (json['created_at'] != null) {
      createAt = json['created_at'];
    }
  }
}
