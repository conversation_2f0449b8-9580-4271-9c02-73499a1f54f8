class ColleguesModel {
  String? result;
  List<Data>? data;

  ColleguesModel({this.result, this.data});

  ColleguesModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }
}

/////
class Data {
  int? id;
  String? name;
  String? designation;
  String? profilePic;

  Data({this.id, this.name, this.designation});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    designation = json['designation'];
    profilePic = json['profile_pic'];
  }
}
