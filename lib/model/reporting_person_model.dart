class ReportingPersonModel {
  List<Data>? data;

  ReportingPersonModel({this.data});

  ReportingPersonModel.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }
}

class Data {
  String? firstName;
  String? lastName;
  ProfileDetails? profileDetails;
  String? level;

  Data({this.firstName, this.lastName, this.profileDetails, this.level});

  Data.fromJson(Map<String, dynamic> json) {
    firstName = json['first_name'];
    lastName = json['last_name'];
    profileDetails = json['profile_details'] != null
        ? ProfileDetails.fromJson(json['profile_details'])
        : null;
    level = json['level'];
  }
}

class ProfileDetails {
  String? employeeId;
  String? designation;
  String? profilePic;

  ProfileDetails({this.employeeId, this.designation, this.profilePic});

  ProfileDetails.fromJson(Map<String, dynamic> json) {
    employeeId = json['employee_id'];
    designation = json['designation'];
    profilePic = json['profile_pic'];
  }
}
