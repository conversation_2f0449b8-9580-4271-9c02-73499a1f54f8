class AmenityModel {
  int? id;
  String? name;
  String? icon;
  bool? isActive;
  String? createdAt;
  String? updatedAt;

  AmenityModel(
      {this.id,
      this.name,
      this.icon,
      this.isActive,
      this.createdAt,
      this.updatedAt});

  AmenityModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    icon = json['icon'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}
