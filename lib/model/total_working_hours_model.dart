class TotalWorkingHoursModel {
  String? userTask;
  String? todayHours;
  String? todayHoursTotal;
  String? todayHoursAverage;
  String? colourHighlight;
  String? past7DaysHours;
  String? past7DaysHoursTotal;
  String? past7DaysHoursAverage;
  String? lastWeekHours;
  String? lastWeekHoursTotal;
  String? lastWeekHoursAverage;
  String? thisMonthHours;
  String? thisMonthHoursTotal;
  String? thisMonthHoursAverage;
  String? lastMonthHoursTotal;
  String? lastMonthHours;
  String? lastMonthHoursAverage;

  TotalWorkingHoursModel(
      {this.userTask,
      this.todayHours,
      this.todayHoursTotal,
      this.todayHoursAverage,
      this.colourHighlight,
      this.past7DaysHours,
      this.past7DaysHoursTotal,
      this.past7DaysHoursAverage,
      this.lastWeekHours,
      this.lastWeekHoursTotal,
      this.lastWeekHoursAverage,
      this.thisMonthHours,
      this.thisMonthHoursTotal,
      this.thisMonthHoursAverage,
      this.lastMonthHoursTotal,
      this.lastMonthHours,
      this.lastMonthHoursAverage});

  TotalWorkingHoursModel.fromJson(Map<String, dynamic> json) {
    userTask = json['user_task'];
    todayHours = json['today_hours'].toString();
    todayHoursTotal = json['today_hours_total'].toString();
    todayHoursAverage = json['today_hours_average'].toString();
    colourHighlight = json['colour_highlight'].toString();
    past7DaysHours = json['past7_days_hours'].toString();
    past7DaysHoursTotal = json['past7_days_hours_total'].toString();
    past7DaysHoursAverage = json['past7_days_hours_average'].toString();
    lastWeekHours = json['last_week_hours'].toString();
    lastWeekHoursTotal = json['last_week_hours_total'].toString();
    lastWeekHoursAverage = json['last_week_hours_average'].toString();
    thisMonthHours = json['this_month_hours'].toString();
    thisMonthHoursTotal = json['this_month_hours_total'].toString();
    thisMonthHoursAverage = json['this_month_hours_average'].toString();
    lastMonthHoursTotal = json['last_month_hours_total'].toString();
    lastMonthHours = json['last_month_hours'].toString();
    lastMonthHoursAverage = json['last_month_hours_average'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user_task'] = userTask;
    data['today_hours'] = todayHours;
    data['today_hours_total'] = todayHoursTotal;
    data['today_hours_average'] = todayHoursAverage;
    data['colour_highlight'] = colourHighlight;
    data['past7_days_hours'] = past7DaysHours;
    data['past7_days_hours_total'] = past7DaysHoursTotal;
    data['past7_days_hours_average'] = past7DaysHoursAverage;
    data['last_week_hours'] = lastWeekHours;
    data['last_week_hours_total'] = lastWeekHoursTotal;
    data['last_week_hours_average'] = lastWeekHoursAverage;
    data['this_month_hours'] = thisMonthHours;
    data['this_month_hours_total'] = thisMonthHoursTotal;
    data['this_month_hours_average'] = thisMonthHoursAverage;
    data['last_month_hours_total'] = lastMonthHoursTotal;
    data['last_month_hours'] = lastMonthHours;
    data['last_month_hours_average'] = lastMonthHoursAverage;
    return data;
  }
}
