class SiginInModel {
  String? result;
  String? verificationUrl;
  String? resendOtpUrl;
  bool? notificationStat;
  int? oTP;

  SiginInModel(
      {this.result,
      this.verificationUrl,
      this.resendOtpUrl,
      this.notificationStat,
      this.oTP});

  SiginInModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    verificationUrl = json['verification_url'];
    resendOtpUrl = json['resend_otp_url'];
    notificationStat = json['notification_stat'];
    oTP = json['OTP'];
  }
}
