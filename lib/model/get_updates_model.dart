// class GetUpdatesModel {
//   String? result;
//   List<Records>? records;
//   int? pagesCount;
//   bool? hasNext;
//   bool? hasPrevious;

//   GetUpdatesModel(
//       {this.result,
//       this.records,
//       this.pagesCount,
//       this.hasNext,
//       this.hasPrevious});

//   GetUpdatesModel.fromJson(Map<String, dynamic> json) {
//     result = json['result'];
//     if (json['records'] != null) {
//       records = <Records>[];
//       json['records'].forEach((v) {
//         records!.add(Records.fromJson(v));
//       });
//     }
//     pagesCount = json['pages_count'];
//     hasNext = json['has_next'];
//     hasPrevious = json['has_previous'];
//   }
// }

// class Records {
//   int? id;
//   String? createdAt;
//   String? name;
//   String? designation;
//   int? like;
//   int? comments;
//   bool? userLiked;
//   List<File>? file;
//   String? description;
//   String? descriptionString;
//   String? profilePhoto;
//   bool? isOwner;
//   String? postLink;

//   Records({
//     this.id,
//     this.createdAt,
//     this.name,
//     this.like,
//     this.comments,
//     this.userLiked,
//     this.file,
//     this.description,
//     this.profilePhoto,
//     this.isOwner,
//     this.designation,
//     this.postLink,
//   });

//   Records.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     createdAt = json['created_at'];
//     name = json['name'];
//     like = json['like'];
//     comments = json['comments'];
//     userLiked = json['user_liked'];
//     if (json['file'] != null) {
//       file = <File>[];
//       json['file'].forEach((v) {
//         file!.add(File.fromJson(v));
//       });
//     }
//     description = json['description'];
//     descriptionString = json['description_string'];
//     profilePhoto = json['profile_photo'];
//     isOwner = json['is_owner'];
//     if (json["designation"] != null) {
//       designation = json["designation"];
//     }
//     postLink = json['post_link'];
//   }
// }

// class File {
//   String? file;
//   String? file_type;
//   int? id;
//   String? thumbnail;
//   File(String path, {this.file, this.file_type, this.id, this.thumbnail});

//   File.fromJson(Map<String, dynamic> json) {
//     file = json['file'];
//     file_type = json['file_type'];
//     id = json['id'];
//     thumbnail = json['thumbnail'];
//   }
// }
