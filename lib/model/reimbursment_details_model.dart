class ReimbursmentDetailsModel {
  int? id;
  String? title;
  String? description;
  String? startDate;
  String? endDate;
  String? currency;
  num? amount;
  String? status;
  List<LevelUsers>? levelUsers;
  List<ReimbursementAttachements>? reimbursementAttachements;
  List<Remarks>? remarks;
  String? hrRemarks;
  String? hrStatus;
  String? hrName;
  List<HrAttachment>? hrAttachment;
  ReimbursmentDetailsModel(
      {this.id,
      this.title,
      this.description,
      this.startDate,
      this.endDate,
      this.currency,
      this.amount,
      this.status,
      this.levelUsers,
      this.reimbursementAttachements,
      this.remarks,
      this.hrStatus,
      this.hrName,
      this.hrAttachment});

  ReimbursmentDetailsModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    currency = json['currency'];
    amount = json['amount'];
    status = json['status'];
    if (json['level_users'] != null) {
      levelUsers = <LevelUsers>[];
      json['level_users'].forEach((v) {
        levelUsers!.add(LevelUsers.fromJson(v));
      });
    }
    if (json['reimbursement_attachements'] != null) {
      reimbursementAttachements = <ReimbursementAttachements>[];
      json['reimbursement_attachements'].forEach((v) {
        reimbursementAttachements!.add(ReimbursementAttachements.fromJson(v));
      });
    }
    if (json['remarks'] != null) {
      remarks = <Remarks>[];
      json['remarks'].forEach((v) {
        remarks!.add(Remarks.fromJson(v));
      });
    }
    hrRemarks = json['hr_remarks'];
    hrStatus = json['hr_status'];
    hrName = json['hr_name'];
    if (json['hr_attachment'] != null) {
      hrAttachment = <HrAttachment>[];
      json['hr_attachment'].forEach((v) {
        hrAttachment!.add(HrAttachment.fromJson(v));
      });
    }
  }
}

class LevelUsers {
  int? id;
  String? approvalStatus;
  int? reimbursementLevel;
  String? addedBy;
  String? addedByProf;
  String? updatedAt;
  String? approvalStatusText;
  int? addedById;
  bool? showButton;

  LevelUsers(
      {this.id,
      this.approvalStatus,
      this.reimbursementLevel,
      this.addedBy,
      this.addedByProf,
      this.updatedAt,
      this.approvalStatusText,
      this.addedById,
      this.showButton});

  LevelUsers.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    approvalStatus = json['approval_status'];
    reimbursementLevel = json['reimbursement_level'];
    addedBy = json['added_by'];
    addedByProf = json['added_by_prof'];
    updatedAt = json['updated_at'];
    approvalStatusText = json['approval_status_text'];
    addedById = json['added_by_id'];
    showButton = json['approve_button'];
  }
}

class ReimbursementAttachements {
  int? id;
  String? attachment;

  ReimbursementAttachements({this.id, this.attachment});

  ReimbursementAttachements.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    attachment = json['attachment'];
  }
}

class Remarks {
  int? id;
  String? remarks;
  String? createdAt;
  String? approvalStatus;
  String? addedBy;
  int? addedById;

  Remarks(
      {this.id,
      this.remarks,
      this.createdAt,
      this.approvalStatus,
      this.addedBy,
      this.addedById});

  Remarks.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    remarks = json['remarks'];
    createdAt = json['created_at'];
    approvalStatus = json['approval_status'];
    addedBy = json['added_by'];
    addedById = json['added_by_id'];
  }
}

class HrAttachment {
  int? id;
  String? hrAttachment;

  HrAttachment({this.id, this.hrAttachment});

  HrAttachment.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    hrAttachment = json['hr_attachment'];
  }
}
