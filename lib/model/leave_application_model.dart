import 'package:cloud_firestore/cloud_firestore.dart';

class LeaveApplicationModel {
  String? email;
  Timestamp? fromDate;
  int? id;
  int? leaveFor;
  String? name;
  String? profilePhoto;
  String? reason;
  String? leaveType;
  Timestamp? toDate;
  int? totalDays;
  String? uid;
  LeaveApplicationModel.fromJson(Map<String, dynamic> json) {
    email = json['email'];
    fromDate = json['from_date'];
    id = json['id'];
    leaveFor = int.parse(json['leave_for'].toString());
    name = json['name'];
    profilePhoto = json['profilePhoto'];
    reason = json['reason'];
    toDate = json['to_date'];
    totalDays = int.parse(json['total_days'].toString());
    uid = json['uid'];
    leaveType = json['leave_type'];
  }
}
