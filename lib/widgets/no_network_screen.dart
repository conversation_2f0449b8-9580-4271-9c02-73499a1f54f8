import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/styles.dart';
import '../util/size_config.dart';

class NoConnectivityPage extends StatelessWidget {
  const NoConnectivityPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Center(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: h * 143,
              width: w * 133,
              decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  image: DecorationImage(
                    fit: BoxFit.cover,
                    image: AssetImage("assets/images/no_network.png"),
                  )),
            ),
            Sized<PERSON><PERSON>(
              height: h * 60,
            ),
            Text("No Internet Connection", style: tsS20w500c161616),
            Si<PERSON><PERSON><PERSON>(
              height: h * 6,
            ),
            Text(
                "Make sure Wi-Fi or Mobile data is turned\non, then try again.",
                textAlign: TextAlign.center,
                style: tsS14w4009F9F9F),
            Sized<PERSON><PERSON>(
              height: h * 50,
            ),
            // GeneralButton(
            //     title: "Try Again",
            //     height: h * 30,
            //     width: w * 164,
            //     textStyle: tsS16w600FFFFFF,
            //     onPressed: () {
            //       // initConnectivity()
            //     })
          ],
        )),
      ),
    );
  }
}
