import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';

class HisenseScaffold extends StatelessWidget {
  final String? screenTitle;
  final Widget body;
  final Widget? floatingWidget;
  final Widget? bottomNavigationBar;
  final List<Widget>? actions;
  final void Function()? onTap;
  final bool? avoidBottom;
  final bool isFromHome;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  const HisenseScaffold(
      {this.screenTitle,
      required this.body,
      this.actions,
      this.floatingWidget,
      this.onTap,
      this.avoidBottom = true,
      this.isFromHome = false,
      this.bottomNavigationBar,
      this.floatingActionButtonLocation,
      super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: 190,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.secondary,
              ],
            ),
          ),
        ),
        Scaffold(
          resizeToAvoidBottomInset: avoidBottom,
          backgroundColor: Colors.transparent,
          bottomNavigationBar: bottomNavigationBar,
          appBar: isFromHome
              ? null
              : AppBar(
                  toolbarHeight: 60 * h,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  leading: InkWell(
                    onTap: () {
                      Navigator.pop(context);
                      if (onTap != null) onTap!();
                    },
                    child: const Icon(
                      Icons.arrow_back_outlined,
                      color: Colors.white,
                      size: 25,
                    ),
                  ),
                  actions: actions,
                  iconTheme: IconThemeData(
                    color: ThemeColors.colorFFFFFF,
                  ),
                  title: screenTitle == null
                      ? null
                      : Text(
                          screenTitle!,
                          style: tsS18w500cFFFFFF,
                        ),
                ),
          body: Container(
            padding: const EdgeInsets.only(top: 20),
            decoration: BoxDecoration(
              color: ThemeColors.colorF4F5FA,
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: body,
          ),
          floatingActionButtonLocation: floatingActionButtonLocation,
          floatingActionButton: floatingWidget,
        ),
      ],
    );
  }
}
