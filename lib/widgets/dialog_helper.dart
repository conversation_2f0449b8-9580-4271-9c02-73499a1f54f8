import 'package:e8_hr_portal/util/size_config.dart';
import 'package:e8_hr_portal/util/styles.dart';
import 'package:flutter/material.dart';

import '../util/colors.dart';
import '../util/validator.dart';

class DialogHelper {
  static confirmationDialogBox({
    required BuildContext context,
    String? buttonName,
    String? title,
    required String description,
    void Function()? onConfirm,
    void Function()? onCancel,
  }) {
    return showDialog(
      context: context,
      builder: (ctxt) {
        return AlertDialog(
          title: Text(title ?? 'Confirmation Required'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(description),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: onCancel ?? () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[300],
              ),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.black),
              ),
            ),
            ElevatedButton(
              onPressed: onConfirm,
              style: ElevatedButton.styleFrom(
                backgroundColor: ThemeColors.secondaryColor,
              ),
              child: Text(buttonName ?? 'Confirm'),
            )
          ],
        );
      },
    );
  }

  static confirmDialogBoxWithTextField({
    required BuildContext context,
    String? buttonName,
    String? title,
    required TextEditingController controller,
    required String description,
    required String hintText,
    required GlobalKey<FormState> formKey,
    void Function()? onConfirm,
    void Function()? onCancel,
  }) {
    return showDialog(
      context: context,
      builder: (ctxt) {
        return Dialog(
          insetPadding: EdgeInsets.symmetric(horizontal: 16 * w),
          child: ConfirmationDialogWithTextField(
            controller: controller,
            description: description,
            hintText: hintText,
            formKey: formKey,
            buttonName: buttonName,
            onCancel: onCancel,
            onConfirm: onConfirm,
            title: title,
          ),
        );
      },
    );
  }
}

class ConfirmationDialogWithTextField extends StatelessWidget {
  final String? buttonName;
  final String? title;
  final String description;
  final String hintText;
  final TextEditingController controller;
  final void Function()? onConfirm;
  final void Function()? onCancel;
  final GlobalKey<FormState> formKey;

  const ConfirmationDialogWithTextField({
    super.key,
    this.buttonName,
    required this.description,
    required this.hintText,
    required this.controller,
    required this.formKey,
    this.onCancel,
    this.onConfirm,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              title ?? 'Confirmation Required',
              style: tsS18NormalBlack,
            ),
          ),
          SizedBox(height: 6),
          Text(description, style: tsS14NormalBlack),
          Form(
            key: formKey,
            child: TextFormField(
              decoration: InputDecoration(hintText: hintText),
              controller: controller,
              validator: (value) => Validator.text(value),
            ),
          ),
          SizedBox(height: 6),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ElevatedButton(
                onPressed: onCancel ?? () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[300],
                ),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.black),
                ),
              ),
              SizedBox(width: 6),
              ElevatedButton(
                onPressed: onConfirm,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ThemeColors.secondaryColor,
                ),
                child: Text(buttonName ?? 'Confirm'),
              )
            ],
          )
        ],
      ),
    );
  }
}
