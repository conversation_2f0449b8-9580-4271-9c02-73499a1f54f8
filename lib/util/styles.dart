import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart' as fonts;
import 'package:e8_hr_portal/util/colors.dart';

TextStyle get tsS12w400cC1C1C1 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: const Color(0xffC1C1C1));
TextStyle get tsS14w500c45464E => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w500, color: const Color(0xff45464E));
TextStyle get tsS14w500cF9637D => fonts.GoogleFonts.poppins(
      fontSize: 14,
      color: const Color(0xffF9637D),
      fontWeight: FontWeight.w500,
    );
TextStyle get tsS12w500cF9637D => fonts.GoogleFonts.poppins(
      fontSize: 12,
      color: const Color(0xffF9637D),
      fontWeight: FontWeight.w500,
    );
TextStyle get tsS16F9637D => fonts.GoogleFonts.poppins(
    fontSize: 16, fontWeight: FontWeight.bold, color: ThemeColors.colorF9637D);
TextStyle get tsS164A8FE7 => fonts.GoogleFonts.poppins(
    fontSize: 16, fontWeight: FontWeight.w500, color: ThemeColors.color4A8FE7);
TextStyle get tsS34w60023262D => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w600,
      fontSize: 34,
      color: ThemeColors.color23262D,
    );
TextStyle get tsS14w400c979797 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColors.color979797);

TextStyle get tsS14w500 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w500, color: Colors.black);
TextStyle get tsS14w500c475366 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w500, color: ThemeColors.color475366);
TextStyle get tsS14w500F03AD9E => fonts.GoogleFonts.rubik(
    fontSize: 14, fontWeight: FontWeight.w500, color: ThemeColors.colorF9637D);
TextStyle get tsS14w500c636363 => fonts.GoogleFonts.rubik(
    fontSize: 14, fontWeight: FontWeight.w500, color: ThemeColors.color636363);
TextStyle get tsS12w500c636363 => fonts.GoogleFonts.rubik(
    fontSize: 12, fontWeight: FontWeight.w500, color: ThemeColors.color636363);
TextStyle get tsS14w400c636363 => fonts.GoogleFonts.rubik(
    fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColors.color636363);
TextStyle get tsS14BN => fonts.GoogleFonts.rubik(
    fontSize: 14, fontWeight: FontWeight.normal, color: Colors.black);
TextStyle get tsS16BN => fonts.GoogleFonts.rubik(
    fontSize: 16, fontWeight: FontWeight.normal, color: Colors.black);
TextStyle get tsS16BN600 => fonts.GoogleFonts.rubik(
    fontSize: 16, fontWeight: FontWeight.w600, color: Colors.black);
TextStyle get tsS20BN => fonts.GoogleFonts.rubik(
    fontSize: 20, fontWeight: FontWeight.w500, color: Colors.black);

TextStyle get tsS161E2138 => fonts.GoogleFonts.rubik(
      fontWeight: FontWeight.w500,
      fontSize: 16,
      color: const Color(0xFF1E2138),
    );
TextStyle get tsS161E2138w => fonts.GoogleFonts.rubik(
      fontWeight: FontWeight.w500,
      fontSize: 16,
      color: Colors.white,
    );
TextStyle get tsS20w500c161616 => fonts.GoogleFonts.poppins(
    fontSize: 20, fontWeight: FontWeight.w500, color: ThemeColors.color161616);

TextStyle get tsS18w500c161616 => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w500,
      fontSize: 18,
      color: ThemeColors.color161616,
    );

TextStyle get tsS16w600cFFFFFF => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w600,
      fontSize: 16,
      color: Colors.white,
    );

TextStyle get tsS181E2138 => fonts.GoogleFonts.rubik(
      fontWeight: FontWeight.w500,
      fontSize: 18,
      color: const Color(0xFF1E2138),
    );
TextStyle get tsS20w500cFFFFFF => fonts.GoogleFonts.poppins(
    fontSize: 20, fontWeight: FontWeight.w500, color: ThemeColors.colorFFFFFF);
TextStyle get tsS19w500cFFFFFF => fonts.GoogleFonts.poppins(
    fontSize: 19, fontWeight: FontWeight.w500, color: ThemeColors.colorFFFFFF);
TextStyle get tsS22FFFFF => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.normal,
      fontSize: 22,
      color: Colors.white,
    );
TextStyle get tsS22W6FFFFF => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w600,
      fontSize: 22,
      color: Colors.white,
    );
TextStyle get tsS22W5FFFFF => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w500,
      fontSize: 22,
      color: Colors.white,
    );
TextStyle get tsS22W5161616 => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w500,
      fontSize: 22,
      color: ThemeColors.color161616,
    );
TextStyle get tsS26w500cFFFFFF => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w500,
      fontSize: 26,
      color: ThemeColors.color161616,
    );
TextStyle get ts28BoldBlack => fonts.GoogleFonts.rubik(
      fontWeight: FontWeight.bold,
      fontSize: 28,
      color: Colors.black,
    );
TextStyle get tsS14FFFFF => fonts.GoogleFonts.rubik(
    fontSize: 14, fontWeight: FontWeight.w500, color: Colors.white);
TextStyle get tsS14whiteNormal => fonts.GoogleFonts.rubik(
    fontSize: 14, fontWeight: FontWeight.normal, color: Colors.white);

TextStyle get tsS14w4009F9F9F => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w400,
      fontSize: 14,
      color: ThemeColors.color9F9F9F,
    );
TextStyle get tsS16w4009F9F9F => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w400,
      fontSize: 16,
      color: ThemeColors.color9F9F9F,
    );
TextStyle get tsS12w400979797 => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w400,
      fontSize: 12,
      color: ThemeColors.color979797,
    );
TextStyle get tsS10w400979797 => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w400,
      fontSize: 10,
      color: ThemeColors.color979797,
    );
TextStyle get tsS10w400949494 => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w400,
      fontSize: 10,
      color: ThemeColors.color949494,
    );
TextStyle get tsS10w400495057 => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w400,
      fontSize: 10,
      color: ThemeColors.color495057,
    );
TextStyle get tsS10w400c979797 => fonts.GoogleFonts.poppins(
    fontSize: 10, fontWeight: FontWeight.w400, color: ThemeColors.color979797);
TextStyle get tsS14w400979797 => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w400,
      fontSize: 14,
      color: ThemeColors.color979797,
    );
TextStyle get tsS14w400454444 => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w400,
      fontSize: 14,
      color: ThemeColors.color454444,
    );
TextStyle get tsS14w400c45464E => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w400,
      fontSize: 14,
      color: ThemeColors.color45464E,
    );
TextStyle get tsS14w400c30292F => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w400,
      fontSize: 14,
      color: ThemeColors.color30292F,
    );
TextStyle get tsS16w400c30292F => fonts.GoogleFonts.poppins(
      fontWeight: FontWeight.w400,
      fontSize: 16,
      color: ThemeColors.color30292F,
    );
TextStyle get tsS16FFFFF => fonts.GoogleFonts.rubik(
    fontSize: 16, fontWeight: FontWeight.w500, color: Colors.white);
TextStyle get tsS16w600FFFFFF => fonts.GoogleFonts.poppins(
    fontSize: 16, fontWeight: FontWeight.w600, color: Colors.white);

TextStyle get tsS12Normal => fonts.GoogleFonts.rubik(
      fontSize: 12,
      fontWeight: FontWeight.normal,
    );

TextStyle get tsS12grey => fonts.GoogleFonts.poppins(
      fontSize: 12,
      fontWeight: FontWeight.normal,
      color: const Color(0xFFB5B5B5),
    );
TextStyle get tsS13grey => fonts.GoogleFonts.rubik(
    fontSize: 13, fontWeight: FontWeight.normal, color: Colors.grey);

TextStyle get tsS14grey => fonts.GoogleFonts.rubik(
    fontSize: 14, fontWeight: FontWeight.normal, color: Colors.grey);
TextStyle get tsS18w500w => fonts.GoogleFonts.rubik(
      fontSize: 18,
      fontWeight: FontWeight.w500,
      color: Colors.white,
    );
TextStyle get tsS18w500c181818 => fonts.GoogleFonts.poppins(
    fontSize: 18, fontWeight: FontWeight.w500, color: ThemeColors.color181818);
TextStyle get tsS16w500 => fonts.GoogleFonts.poppins(
      fontSize: 16,
      fontWeight: FontWeight.w500,
      color: Colors.black,
    );
TextStyle get tsS16w500c9F9F9F => fonts.GoogleFonts.poppins(
      fontSize: 16,
      fontWeight: FontWeight.w500,
      color: ThemeColors.color9F9F9F,
    );
TextStyle get tsS16w500cCFCFCF => fonts.GoogleFonts.poppins(
      fontSize: 16,
      fontWeight: FontWeight.w500,
      color: const Color(0xffCFCFCF),
    );
TextStyle get tsS18w500 => fonts.GoogleFonts.rubik(
      fontSize: 18,
      fontWeight: FontWeight.w500,
      color: Colors.black,
    );
TextStyle get tsS18w500cFFFFFF => fonts.GoogleFonts.poppins(
    fontSize: 18, fontWeight: FontWeight.w500, color: ThemeColors.colorFFFFFF);
TextStyle get tsS18w600cFFFFFF => fonts.GoogleFonts.poppins(
    fontSize: 18, fontWeight: FontWeight.w600, color: ThemeColors.colorFFFFFF);
TextStyle get tsS14c8391B5 => fonts.GoogleFonts.rubik(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: ThemeColors.color8391B5);
TextStyle get tsS12c8391B5 => fonts.GoogleFonts.poppins(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: ThemeColors.color8391B5);
TextStyle get tsS14w500cFFFFFF => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w500, color: ThemeColors.colorFFFFFF);
TextStyle get tsS14w600cFFFFFF => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w600, color: ThemeColors.colorFFFFFF);
TextStyle get tsS14w500cFFFFFFwop60 => fonts.GoogleFonts.poppins(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: ThemeColors.colorFFFFFF.withOpacity(0.60));
TextStyle get tsS14w400c181818 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColors.color181818);
TextStyle get tsS14w400c4CD964 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColors.color4CD964);
TextStyle get tsS14w400c949494 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColors.color949494);
TextStyle get tsS14w500c161616 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w500, color: ThemeColors.color161616);
TextStyle get tsS14w500c6E7079 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w500, color: ThemeColors.color6E7079);
TextStyle get tsS16w600c6E7079 => fonts.GoogleFonts.poppins(
    fontSize: 16, fontWeight: FontWeight.w600, color: ThemeColors.color6E7079);

TextStyle get tsS14w500c181818 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w500, color: ThemeColors.color181818);
TextStyle get tsS14w400cFA0000 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColors.colorFA0000);

TextStyle get tsS13FFEFC9 => fonts.GoogleFonts.rubik(
    fontSize: 13,
    fontWeight: FontWeight.normal,
    color: ThemeColors.colorFFB400);

TextStyle get tsS13BBFFCF => fonts.GoogleFonts.rubik(
    fontSize: 13,
    fontWeight: FontWeight.normal,
    color: ThemeColors.color06AA37);

TextStyle get tsS13FFBBBB => fonts.GoogleFonts.rubik(
    fontSize: 13,
    fontWeight: FontWeight.normal,
    color: ThemeColors.colorFF0000);

TextStyle get tsS12FF9100 => fonts.GoogleFonts.rubik(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: ThemeColors.colorFF9100);
TextStyle get tsS12w400c979797 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColors.color979797);

TextStyle get tsS120C8DFF => fonts.GoogleFonts.rubik(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: ThemeColors.color0C8DFF);

TextStyle get tsS12NormalBlack => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.normal, color: Colors.black);
TextStyle get tsS12w400Black => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: Colors.black);
TextStyle get tsS12w400c656464 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: const Color(0xff656464));
TextStyle get tsS12w500Black => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w500, color: Colors.black);
TextStyle get tsS12w500FFFFF => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w500, color: Colors.white);
TextStyle get tsS12w600FFFFF => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w600, color: Colors.white);
TextStyle get tsS12w500c979797 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w500, color: ThemeColors.color979797);
TextStyle get tsS12w500c949494 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w500, color: ThemeColors.color949494);
TextStyle get tsS14w500c4CD964 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w500, color: ThemeColors.color4CD964);
TextStyle get tsS12w500c4CD964 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w500, color: ThemeColors.color4CD964);
TextStyle get tsS12w500c505050 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColors.color505050);
TextStyle get tsS12w500c5c5c5c => fonts.GoogleFonts.poppins(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: ThemeColors.color5c5c5c,
    decoration: TextDecoration.underline);
TextStyle get tsS14NormalBlack => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.normal, color: Colors.black);
TextStyle get tsS16NormalBlack => fonts.GoogleFonts.poppins(
    fontSize: 16, fontWeight: FontWeight.normal, color: Colors.black);
TextStyle get tsS18NormalBlack => fonts.GoogleFonts.poppins(
    fontSize: 18, fontWeight: FontWeight.normal, color: Colors.black);
TextStyle get tsS14w500FFFFF => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w500, color: Colors.white);
TextStyle get tsS14F9637D => fonts.GoogleFonts.poppins(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: ThemeColors.colorF9637D);
TextStyle get tsS14w400Black => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w400, color: Colors.black);
TextStyle get tsS14w4C4CD964 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w400, color: const Color(0xff4CD964));
TextStyle get tsS14w500Black => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w500, color: Colors.black);
TextStyle get tsS14Normalf66666 => fonts.GoogleFonts.poppins(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: const Color(0xff666666));
TextStyle get tsS14w4f666666 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w400, color: const Color(0xff666666));
TextStyle get tsS14w5f666666 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w500, color: const Color(0xff666666));
TextStyle get tsS12Normalf66666 => fonts.GoogleFonts.poppins(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: const Color(0xff666666));
TextStyle get tsS12w4f666666 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: const Color(0xff666666));
TextStyle get tsS12w5f666666 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w500, color: const Color(0xff666666));

TextStyle get tsS12W5F83456 => fonts.GoogleFonts.poppins(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: ThemeColors.secondaryColor);

TextStyle get tsS12second => fonts.GoogleFonts.rubik(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: ThemeColors.secondaryColor);
TextStyle get tsS14w5second => fonts.GoogleFonts.rubik(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: ThemeColors.secondaryColor);
TextStyle get tsS14w400c9F9F9F => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColors.color9F9F9F);
TextStyle get tsS12w400c30292F => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColors.color30292F);
TextStyle get tsS12w500c131515 => fonts.GoogleFonts.poppins(
      fontSize: 12,
      fontWeight: FontWeight.w500,
      color: ThemeColors.color131515,
    );
TextStyle get tsS12w400c9F9F9F => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColors.color9F9F9F);
TextStyle get tsS14second => fonts.GoogleFonts.rubik(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: ThemeColors.secondaryColor);

TextStyle get tsS12colorB8B8B8 => fonts.GoogleFonts.poppins(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: ThemeColors.colorB8B8B8);
TextStyle get tsS128391B5 => fonts.GoogleFonts.rubik(
    fontSize: 12 * f,
    fontWeight: FontWeight.normal,
    color: ThemeColors.color8391B5);

TextStyle get tsS1206AA37 => fonts.GoogleFonts.rubik(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: ThemeColors.color06AA37);

TextStyle get tsS12wRcWhite => fonts.GoogleFonts.rubik(
      fontSize: 12,
      fontWeight: FontWeight.normal,
      color: Colors.white,
    );
TextStyle get ts12second => fonts.GoogleFonts.rubik(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: ThemeColors.secondaryColor);
TextStyle get tsS12w500c161616 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w500, color: ThemeColors.color161616);
TextStyle get tsS12w500c181818 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w500, color: ThemeColors.color181818);
TextStyle get tsS12w500c03AD9E => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w500, color: ThemeColors.colorFCC400);
TextStyle get tsS12w500c2C2D33 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w500, color: ThemeColors.color2C2D33);
TextStyle get tsS10w500c2C2D33 => fonts.GoogleFonts.poppins(
    fontSize: 10, fontWeight: FontWeight.w500, color: ThemeColors.color2C2D33);
TextStyle get tsS14w500c2C2D33 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w500, color: ThemeColors.color2C2D33);

TextStyle get tsS12w500c519C66 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w500, color: ThemeColors.color519C66);
TextStyle get tsS12w400c519C66 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColors.color519C66);
TextStyle get tsS12w400c454444 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: const Color(0xff454444));
TextStyle get tsS12w600c519C66 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColors.color519C66);
TextStyle get tsS14w500c03AD9E => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w500, color: ThemeColors.colorF9637D);
TextStyle get tsS14w400c000000 => fonts.GoogleFonts.poppins(
    fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColors.color000000);
TextStyle get tsS12w400c949494 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColors.color949494);
TextStyle get tsS12w400c495057 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColors.color495057);
TextStyle get tsS12w400c475366 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColors.color475366);
TextStyle get tsS12w600c949494 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColors.color519C66);
TextStyle get tsS12w600cE5B900 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColors.colorE5B900);
TextStyle get tsS18w500cE5B900 => fonts.GoogleFonts.poppins(
    fontSize: 18, fontWeight: FontWeight.w500, color: ThemeColors.colorE5B900);

TextStyle get tsS12w600cF64D44 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColors.colorF64D44);
TextStyle get tsS12w4cF64D44 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColors.colorF64D44);
TextStyle get tsS12w4cE61212 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColors.colorE61212);
TextStyle get tsS12w4c06AA37 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColors.color06AA37);
TextStyle get tsS12w4c5570F1 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColors.color5570F1);
TextStyle get tsS12W6FE5B900 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColors.colorE5B900);
TextStyle get tsS12W683B4FF => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColors.color83B4FF);
TextStyle get tsS11w400cerrorColor => fonts.GoogleFonts.poppins(
    fontSize: 11, fontWeight: FontWeight.w400, color: ThemeColors.errorColor);
TextStyle get tsS10w400cFFFFFF => fonts.GoogleFonts.poppins(
    fontSize: 10, fontWeight: FontWeight.w400, color: ThemeColors.colorFFFFFF);
TextStyle get tsS10w400c646363 => fonts.GoogleFonts.poppins(
    fontSize: 10, fontWeight: FontWeight.w400, color: ThemeColors.color646363);
TextStyle get tsS10w400c4D4D4D => fonts.GoogleFonts.poppins(
    fontSize: 10, fontWeight: FontWeight.w400, color: ThemeColors.color4D4D4D);
TextStyle get tsS10w600cE5B900 => fonts.GoogleFonts.poppins(
    fontSize: 10, fontWeight: FontWeight.w600, color: ThemeColors.colorE5B900);
TextStyle get tsS12w600E5B900 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColors.colorE5B900);
TextStyle get tsS12w600F64D44 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColors.colorF64D44);
TextStyle get tsS12w5570F1 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColors.color5570F1);
TextStyle get tsS12w400949494 => fonts.GoogleFonts.poppins(
    fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColors.color949494);
