import 'package:shared_preferences/shared_preferences.dart';
import 'package:e8_hr_portal/services/secure_key_manager.dart';

class APIHeaders {
  static Map<String, String> authorization = {};
  static Map<String, String> json = {};
  static setHeaders(String? accessToken) {
    authorization = {
      'Authorization': 'Bearer $accessToken',
    };
    json = {
      'Authorization': 'Bearer $accessToken',
      'Content-Type': 'application/json',
      // 'TimeZone': LoggedInUser.timeZone.toString(),
    };
  }

  static Future<Map<String, String>> getApiHeader() async {
    Map<String, String> headers = {};

    // SECURITY FIX: Try secure storage first, fallback to SharedPreferences
    String? token = await SecureKeyManager.getAccessToken();

    if (token == null || token.isEmpty) {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      token = prefs.getString('access_token');
    }

    if (token != null && token.isNotEmpty) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }
}
