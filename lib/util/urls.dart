import 'package:shared_preferences/shared_preferences.dart';
import 'package:e8_hr_portal/services/secure_key_manager.dart';

import '../main.dart';

Future<Map<String, String>> getHeaders() async {
  // SECURITY FIX: Try secure storage first, fallback to SharedPreferences
  String? token = await SecureKeyManager.getAccessToken();

  if (token == null || token.isEmpty) {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    token = prefs.getString('access_token');
  }

  return {
    'Authorization': 'Bearer ${token ?? ''}',
    'Content-Type': 'application/json'
  };
}
// String baseURL = 'https://e8-hr.e8demo.com'; // liv - E8 hr portal
// String baseURL = 'https://e8-hr-dev.e8demo.com'; // demo - E8 hr portal

// String baseURL = 'https://hr-portal.element8.ae'; // new liv - E8 hr - uae
// String baseUrlE8 = '$baseURL/api/';
// String workflowURL = 'https://workflow.element8.ae'; // LIVE URL
// // String workflowURL = 'https://workflow-development.e8demo.com'; // DEMO URL
String getBaseURL() {
  if (isDemo) {
    return 'https://e8-hr-dev.e8demo.com/api/'; // demo - E8 hr portal
  }
  return 'https://hr-portal.element8.ae/api/'; // new liv - E8 hr - uae
}

String getWorkFlowURL() {
  if (isDemo) {
    // return 'https://workflow-development.e8demo.com'; // DEMO URL
    return 'https://workflow-dev.e8demo.com';
  }
  return 'https://workflow.element8.ae'; // LIVE URL
}

String baseUrl = getBaseURL();
String workflowURL = getWorkFlowURL();
Uri socialLogin = Uri.parse('${baseUrl}google_sign_in/');
Uri normalLgin = Uri.parse('${baseUrl}user_sign_in/');
Uri profileView = Uri.parse('${baseUrl}profile_view/');
Uri editProfileURL = Uri.parse('${baseUrl}edit_profile/');
Uri employeeStatusURL = Uri.parse('${baseUrl}user_status/');
Uri userlistURL = Uri.parse('${baseUrl}user_list/');
Uri employeeStatusUpdateURL = Uri.parse('${baseUrl}user_status_updates/');
Uri leaveTypesUrl = Uri.parse('${baseUrl}leave_type/');
Uri leaveForwardUrl = Uri.parse('${baseUrl}leave-forward/');
Uri requestedLeaveOverviewForReportingPerson =
    Uri.parse('${baseUrl}requested_leave_list/');
Uri reportingPersonsUrl = Uri.parse('${baseUrl}leave_reporting_person_list/');
Uri staffInChagreUrl = Uri.parse('${baseUrl}staff_in_charge/');
Uri leaveBalanceUrl = Uri.parse('${baseUrl}balance_leave/');
Uri leaveApplyUrl = Uri.parse('${baseUrl}leave_request/');
Uri adminLeaveRequestApplyUrl = Uri.parse('${baseUrl}admin_leave_request/');
Uri leaveHistoryUrl = Uri.parse('${baseUrl}leave_history_list/');
Uri leaveOverViewUrl = Uri.parse('${baseUrl}leave_overview/');
Uri requestedLeaveListURL = Uri.parse('${baseUrl}requested_leave_list/');
Uri requestedLeaveoverViewURL =
    Uri.parse('${baseUrl}requested_leave_over_view/');
Uri leaveApproveReject = Uri.parse('${baseUrl}leave_approve_reject/');
Uri remainingLeavesNext = Uri.parse('${baseUrl}remaining_leaves/');
Uri upcomigBirthdayURL = Uri.parse('${baseUrl}upcoming_birthdays/');
Uri workAnniversaryURL = Uri.parse('${baseUrl}upcoming_work_anniversary/');
Uri holidaysURL = Uri.parse('${baseUrl}holidays/');
Uri upcomingHolidaysURL = Uri.parse('${baseUrl}upcoming_holidays/');
Uri chatRequestAdminURL = Uri.parse('${baseUrl}user_admin_chat/');
Uri notificationURL = Uri.parse('${baseUrl}notification/');
Uri forgotpassURL = Uri.parse('${baseUrl}forget_password_otp/');
Uri changepassURL = Uri.parse('${baseUrl}change_password/');
Uri getPostsURL = Uri.parse('${baseUrl}posts/');
Uri getCommentsURL = Uri.parse('${baseUrl}comments/');
Uri createCommentURL = Uri.parse('${baseUrl}comments-create/');
Uri postCreateURL = Uri.parse('${baseUrl}posts-create/');
// Uri postEditURL = Uri.parse('${baseUrlHisense}posts-update/');
Uri likePostUrl = Uri.parse('${baseUrl}likes/');
Uri getPostDetailsUrl = Uri.parse('${baseUrl}posts-retrieve/');
Uri medicalInsuranceCardURL = Uri.parse('${baseUrl}medical-insuarance/');
Uri medicalInsuranceDownloadURL =
    Uri.parse('${baseUrl}medical-insuarance-documents');
Uri commentsDeleteURL = Uri.parse('${baseUrl}comments-delete/');
Uri postDeleteURL = Uri.parse('${baseUrl}posts-delete/');
Uri cancelLeaveURL = Uri.parse('${baseUrl}cancel_leave/');
Uri flightTicketPersonalInfoURL =
    Uri.parse('${baseUrl}personal-info/?action=flight_tickets');
Uri flightTicketCreateURL = Uri.parse('${baseUrl}flight-ticket-create/');
Uri flightTicketListURL =
    Uri.parse('${baseUrl}certificates-list/?action=flight_tickets');
Uri airlineListURL = Uri.parse('${baseUrl}airlines/');
Uri salaryCertificatePersonalInfoURL =
    Uri.parse('${baseUrl}personal-info/?action=salary_certificates');
Uri salaryCertificateCreateURL =
    Uri.parse('${baseUrl}salary-certificates-create/');
Uri nocCreateURL = Uri.parse('${baseUrl}non-objection-certificate-create/');
Uri nocCertificateListURL =
    Uri.parse('${baseUrl}certificates-list/?action=non_objection_certificate');
Uri drivingLicenseListURL =
    Uri.parse('${baseUrl}certificates-list/?action=driving_license_permission');
Uri commentsCreateURL = Uri.parse('${baseUrl}comments-create/');
Uri generateTicketsIDURL = Uri.parse('${baseUrl}ticket_id_generate/');
Uri createTicketURL = Uri.parse('${baseUrl}user_admin_tickets_generate/');
Uri ticketListURL = Uri.parse('${baseUrl}user_ticket_list/');
Uri ticketOverviewURL = Uri.parse('${baseUrl}ticket_details/');
Uri ticketDeleteURL = Uri.parse('${baseUrl}delete_tickets/');
Uri otpVerifyingURL = Uri.parse('${baseUrl}ip_otp_verification/');
Uri otpResendURL = Uri.parse('${baseUrl}ip_otp_resend/');
Uri logoutURL = Uri.parse('${baseUrl}user_sign_out/');
Uri secretKeyURL = Uri.parse('${baseUrl}secret_key/');
Uri entryVisaURL = Uri.parse('${baseUrl}visa_types/');
Uri tokenUpdateURL = Uri.parse('${baseUrl}fcm_token_update/');
Uri googleLoginURL = Uri.parse('${baseUrl}google_sign_in/');
Uri empResponsiblityURL = Uri.parse('${baseUrl}employee_responibility/');
Uri userPoliciesURL = Uri.parse('${baseUrl}user_policy/');
Uri surveyCreateUrl = Uri.parse('${baseUrl}survey-create/');
Uri amenitiesListURL = Uri.parse('${baseUrl}amenities_list/');
Uri createMeetingRoomURL = Uri.parse('${baseUrl}create_meeting_room/');
Uri meetingRoomListURL = Uri.parse('${baseUrl}created_meeting_rooms_list/');
Uri deleteMeetingRoomURL = Uri.parse('${baseUrl}delete_meeting_room/');
Uri editMeetingRoomURL =
    Uri.parse('${baseUrl}edit_meeting_room/?meeting_room_id=');
Uri meetingRoomDetailsURL =
    Uri.parse('${baseUrl}meeting_room_details/?meeting_room_id=');
Uri surveyVoteURL = Uri.parse('${baseUrl}survey-vote/');
Uri bookMeetingRoomURL = Uri.parse('${baseUrl}meeting_room_booking/');
Uri viewVoteURL = Uri.parse('${baseUrl}survey-view-vote/?survey_id=');
Uri deleteSurveyURL = Uri.parse('${baseUrl}survey-delete/?survey_id=');
Uri bookedRoomsStatusURL = Uri.parse('${baseUrl}list_booked_rooms/');
Uri cancelBookedMeetingRoomURL =
    Uri.parse('${baseUrl}booking_cancel/?booking_id=');
Uri editBookedMeetingRoomURL =
    Uri.parse('${baseUrl}booked_room_update/?booking_id=');
Uri checkAvailabilityURL = Uri.parse('${baseUrl}meeting_room_availability/?');
Uri meetingRequestForAdminURL =
    Uri.parse('${baseUrl}admin_booking_request_list/?status=');
Uri bookingCancelUrl = Uri.parse('${baseUrl}booking_cancel/');
Uri bookingRescheduleUrl = Uri.parse('${baseUrl}meeting_reschedule/');
Uri approveOrRejectURL = Uri.parse('${baseUrl}admin_approve_reject_meeting/');
Uri meetingRequestForMembersURL =
    Uri.parse('${baseUrl}invited_user_meeting_request_list/');
Uri scheduledMeetingsURL =
    Uri.parse('${baseUrl}scheduled_meeting_list/?action=');
Uri approveOrRejectForInviteesURL =
    Uri.parse('${baseUrl}invited_user_approve_reject_meeting/');
Uri feedbackURL = Uri.parse('${baseUrl}create_feedback_complaints/');
Uri invitingPersonsListURL = Uri.parse('${baseUrl}invited_user_list/');
Uri attendenceViewLogURL = Uri.parse('${baseUrl}attendance-logs/');
Uri refreshTokenURL = Uri.parse('${baseUrl}refresh_token/');
Uri addOrRemoveInviteesForAdminURL =
    Uri.parse('${baseUrl}admin_edit_meeting_request/');
Uri locationChangeRequestURL = Uri.parse('${baseUrl}set-wfh-location/');
Uri locationRequestListURL = Uri.parse('${baseUrl}list-user-location/');
Uri locationRequestDetailsURL =
    Uri.parse('${baseUrl}user-location-overview/?location_id=');
Uri locationCheckingURL = Uri.parse('${baseUrl}location-identification/');
Uri wfhPunchinURL = Uri.parse('${baseUrl}wfh-punch-in-out/');
Uri chatBotURL = Uri.parse('${baseUrl}chat-bot/');
Uri wfhEmployeeUrl = Uri.parse('${baseUrl}wfh-attendance-overview/?att_date=');
Uri allEmployeesURL = Uri.parse('${baseUrl}wfh_users_list/');
Uri wfhPermissionURL = Uri.parse('${baseUrl}wfh_permission_set/');
Uri flightTicketLeveListURL = Uri.parse('${baseUrl}approved-leave-list/');
Uri flightTicketCoutURL = Uri.parse('${baseUrl}ticket-count-list/');
Uri flightTicketDetailsURL = Uri.parse('${baseUrl}flight-ticket-detail/');
Uri flightTicketEditURL = Uri.parse('${baseUrl}flight-ticket-edit/');
Uri flightTicketCancelURL = Uri.parse('${baseUrl}cancel-request/');
Uri flightTicketRequestListURL =
    Uri.parse('${baseUrl}flight-ticket-level-list/');
Uri flightTicketApproveRejectURL =
    Uri.parse('${baseUrl}flight-ticket-approve-reject/');
Uri flightTicketUploadQuotationURL =
    Uri.parse('${baseUrl}flight-ticket-upload-quotation/');
Uri flightTickeDeleteQuotationURL =
    Uri.parse('${baseUrl}flight-ticket-file-delete/');
Uri flightTicketApprovedListURL =
    Uri.parse('${baseUrl}flight-ticket-approved-list/');
Uri flightTicketUploadTciketURL =
    Uri.parse('${baseUrl}flight-ticket-upload-ticket/');
Uri flightTicketDepartmentListURL =
    Uri.parse('${baseUrl}flight-ticket-department-list/');
Uri flightTicketDesiginationListURL =
    Uri.parse('${baseUrl}flight-ticket-designation-list/');
Uri fetchReimbursmentDropdownDataURL =
    Uri.parse('${baseUrl}reimbursement-listing/?action=');
Uri createReimbursmentRequestURL = Uri.parse('${baseUrl}reimbursement-create/');

Uri listReimbursmentUrl = Uri.parse('${baseUrl}reimbursement-emp-list');
Uri reimbursmentDetailsUrl =
    Uri.parse('${baseUrl}reimbursement-emp-detail-view/');
Uri reimbursmentCancelUrl = Uri.parse('${baseUrl}reimbursement-cancel/');
Uri reimbursmentEditUrl = Uri.parse('${baseUrl}reimbursement-emp-update/');
Uri reimbursmentRequestForReportingPersonUrl =
    Uri.parse('${baseUrl}reimbursement-level-list/');
Uri reimbursementApproveOrReject =
    Uri.parse('${baseUrl}reimbursement-approve-reject/?reimb_id=');
Uri reimbursementApproveOrRejectForHR =
    Uri.parse('${baseUrl}reimbursement-hr-approve-reject/?reimb_id=');

Uri departmentListUrl = Uri.parse('${baseUrl}department-list');
Uri designationListUrl = Uri.parse('${baseUrl}designation-list');
Uri reimbursementHRUrl = Uri.parse('${baseUrl}reimbursement-hr-listing/');

// =======------------_______________Checklist____________________---------------------=

Uri checklistNotificationURL = Uri.parse('${baseUrl}checklist-notifications');
Uri checklistShedulersURL = Uri.parse('${baseUrl}checklist-schedules');
Uri checklistAssignedDetailsURL =
    Uri.parse('${baseUrl}assigned-checklist-retrieve');
Uri checklistAssignedURL = Uri.parse('${baseUrl}assigned-checklists/');
Uri checklistAssignedFilterURL = Uri.parse('${baseUrl}checklists-history');
Uri checklistAssignedUpdateURL =
    Uri.parse('${baseUrl}assigned-checklist-update/');
Uri checklistScheduleTypeURL = Uri.parse('${baseUrl}checklist-schedule-type/');

Uri onsignoutchecklistsURL = Uri.parse('${baseUrl}on-signout-checklists/');
Uri onsignoutchecklistsupdateURL =
    Uri.parse('${baseUrl}on-signout-checklists-update/');
Uri usertimelogstopAll = Uri.parse("$workflowURL/user-timelog-stop-all/");
Uri userdailytasksummaryURL =
    Uri.parse("$workflowURL/user-daily-task-summary/");
Uri usertimelogreportsendURL =
    Uri.parse("$workflowURL/user-timelog-report-send/");
Uri punchinpunchoutURL = Uri.parse('${baseUrl}punchin-punchout/');
// =======------------_______________WFH____________________---------------------=
Uri wfhRequestURL = Uri.parse('${baseUrl}wfh_request/');
Uri wfhRecordsURL = Uri.parse('${baseUrl}wfh_records/');
Uri wfhRecordsOverviewURL = Uri.parse('${baseUrl}wfh_records/');
Uri wfhCancelUrl = Uri.parse('${baseUrl}cancel_wfh/');
Uri wfhUpdateUrl = Uri.parse('${baseUrl}wfh_request/');
Uri wfhRequestsListUrl = Uri.parse('${baseUrl}wfh_request_list/');
Uri wfhRequestsHrOverviewUrl = Uri.parse('${baseUrl}wfh_request/');
Uri wfhRejectUrl = Uri.parse('${baseUrl}wfh_reject/');
Uri wfhApproveUrl = Uri.parse('${baseUrl}wfh_approve/');

// =======------------_______________Morning huddle____________________---------------------=
Uri dailyTaskAllocationURL = Uri.parse('${baseUrl}daily-task-allocation/');
Uri dailyTaskAllocationListURL =
    Uri.parse('${baseUrl}daily-task-allocation-list/');
