import 'package:e8_hr_portal/util/colors.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomPurpleBlueTheme {
  static ThemeData get theme {
    ColorScheme colorScheme = const ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xFF9F2EB5),
      onPrimary: Color(0xFF9F2EB5),
      secondary: Color(0xFF2C2C93),
      onSecondary: Color(0xFF2C2C93),
      error: Colors.red,
      onError: Colors.red,
      onSurface: Colors.white,
      surface: Colors.white,
    );
    return ThemeData(
      primaryColor: const Color(0xFF9F2EB5),
      colorScheme: colorScheme,
      appBarTheme: AppBarTheme(
        backgroundColor: const Color(0xFF9F2EB5),
        iconTheme: const IconThemeData(
          color: Colors.black,
        ),
        centerTitle: false,
        titleTextStyle: GoogleFonts.rubik(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.w500,
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: const Color(0xFF4666FF),
        ),
      ),
      bottomAppBarTheme: const BottomAppBarTheme(color: Colors.white),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        selectedItemColor: Color(0xFF4666FF),
        unselectedItemColor: Color(0xFF848DB7),
      ),
      fontFamily: GoogleFonts.rubik().fontFamily,
      primarySwatch: Colors.blue,
      scaffoldBackgroundColor: ThemeColors.scaffoldBgColor,
    );
  }
}
