import 'package:flutter/material.dart';

class ThemeColors {
  static Color color131515 = const Color(0xff131515);
  static Color primaryColor = const Color(0xFFFED800);
  static Color secondaryColor = const Color(0xFFFED800);
  static Color disabledButtonColor = const Color(0xFFCBCBCB);
  static Color secondaryTextColor = const Color(0xFF979797);
  static Color titleColor = const Color(0xFF8391B5);
  static Color scaffoldBgColor = const Color(0xffF8FAFF);
  static Color color03AD9E = const Color(0xff03AD9E);
  static Color colorFFFFFF = const Color(0xFFFFFFFF);
  static Color color313541 = const Color(0xffF8FAFF);
  static Color color9F9F9F = const Color(0xff9F9F9F);
  static Color colorF9F9F9 = const Color(0xffF9F9F9);
  static Color color23262D = const Color(0xff23262D);
  static Color color8391B5 = const Color(0xff8391B5);
  static Color colorFFEFC9 = const Color(0xffFFEFC9);
  static Color colorFFB400 = const Color(0xffFFB400);
  static Color colorFCD500 = const Color(0xffFCD500);
  static Color colorDEE7FF = const Color(0xffDEE7FF);
  static Color colorFF9100 = const Color(0xffFF9100);
  static Color color0C8DFF = const Color(0xff0C8DFF);
  static Color colorFCC500 = const Color(0xffFCC500);
  static Color colorFCD900 = const Color(0xffFCD900);
  static Color colorF9637D = const Color(0xffF9637D);

  // static Color color1E2138 = const Color(0xff1E2138);
  static Color color1E2138 = const Color(0xffF8FAFF);
  static Color colorBBFFCF = const Color(0xffBBFFCF);
  static Color colorFFBBBB = const Color(0xffFFBBBB);
  static Color color06AA37 = const Color(0xff06AA37);
  static Color colorFF0000 = const Color(0xffFF0000);
  static Color color7E7E7E = const Color(0xFF7E7E7E);
  static Color colorF3F3F3 = const Color(0xFFF3F3F3);
  static Color colorF64D44 = const Color(0xFFF64D44);
  static Color colorF4F5FA = const Color(0xFFF4F5FA);
  static Color color32936F = const Color(0xFF32936F);
  static Color color5570F1 = const Color(0xFF5570F1);
  static Color colorFCC400 = const Color(0xFFFCC400);
  static Color colorFFD354 = const Color(0xffFFD354);
  static Color colorF1F1F1 = const Color(0xFFF1F1F1);
  static Color colorACACAC = const Color(0xFFACACAC);
  static Color color4B2ACE = const Color(0xff4B2ACE);
  static Color color808080 = const Color(0xFF808080);
  static Color color242A38 = const Color(0xFF242A38);
  static Color colorFFCB05 = const Color(0xFFFFCB05);
  static Color colorE4E4E4 = const Color(0xFFE4E4E4);
  static Color color7D5DFF = const Color(0xff7D5DFF);
  static Color color826AED = const Color(0xff826AED);
  static Color color2ACCC8 = const Color(0xff2ACCC8);
  static Color colorFD5E5A = const Color(0xffFD5E5A);

  static Color colorFC3636 = const Color(0xffFC3636);
  static Color colorFFE06B = const Color(0xffFFE06B);

  static Color colorF6F6F6 = const Color(0xffF6F6F6);
  static Color colorFEF3CB = const Color(0xffFEF3CB);
  static Color colorEFEFEF = const Color(0xFFEFEFEF);
  static Color colorCEE1FF = const Color(0xFFCEE1FF);
  static Color colorA7A7A7 = const Color(0xFFA7A7A7);
  static Color colorFF2A2B = const Color(0xFFFF2A2B);
  static Color color464847 = const Color(0xFF464847);
  static Color colorFF002B = const Color(0xFFFF002B);
  static Color color6950D8 = const Color(0xff6950D8);
  static Color color0AA5A1 = const Color(0xff0AA5A1);
  static Color colorA426C7 = const Color(0xffA426C7);
  static Color color8926C7 = const Color(0xff8926C7);
  static Color color7826C7 = const Color(0xff7826C7);
  static Color color7226C7 = const Color(0xff7226C7);
  static Color color7025C5 = const Color(0xff7025C5);
  static Color color47109C = const Color(0xff47109C);
  static Color color2F0483 = const Color(0xff2F0483);
  static Color color26007A = const Color(0xff26007A);
  static Color color570DAB = const Color(0xff570DAB);
  static Color color573BD4 = const Color(0xff573BD4);
  static Color color4F14A4 = const Color(0xff4F14A4);
  static Color colorD3DCFB = const Color(0xffD3DCFB);
  static Color color0048A5 = const Color(0xff0048A5);
  static Color color4A8FE7 = const Color(0xff4A8FE7);
  static Color colorFB4D3D = const Color(0xffFB4D3D);
  static Color colorCD1000 = const Color(0xffCD1000);
  static Color color9D16BE = const Color(0xff9D16BE);
  static Color colorEFABFF = const Color(0xffEFABFF);
  static Color color4D8A0B = const Color(0xff4D8A0B);
  static Color color4CD964 = const Color(0xff4CD964);
  static Color colorFFC483 = const Color(0xffFFC483);
  static Color colorFF8600 = const Color(0xffFF8600);
  static Color colorFCD800 = const Color(0xffFCD800);
  static Color color0F8E8B = const Color(0xff0F8E8B);
  static Color color90FCF9 = const Color(0xff90FCF9);
  static Color colorE5E4E3 = const Color(0xFFE5E4E3);
  static Color color161616 = const Color(0xFF161616);
  static Color color181818 = const Color(0xFF181818);
  static Color color949494 = const Color(0xFF949494);
  static Color color2C2D33 = const Color(0xFF2C2D33);
  static Color color519C66 = const Color(0xFF519C66);
  static Color color30292F = const Color(0xFF30292F);
  static Color colorE3E3E3 = const Color(0xFFE3E3E3);
  static Color colorC9C9C9 = const Color(0xFFC9C9C9);
  static Color errorColor = const Color(0xFFCB4335);
  static Color colorTransparent = const Color(0x00FFFFFF);
  static Color color979797 = const Color(0xFF979797);
  static Color color2F2F2F = const Color(0xFF2F2F2F);
  static Color colorFA0000 = const Color(0xFFFA0000);
  static Color colorF8F8F8 = const Color(0xffF8F8F8);
  static Color colorD9D9D9 = const Color(0xFFD9D9D9);
  static Color colorE3E1E1 = const Color(0xFFE3E1E1);
  static Color color454444 = const Color(0xFF454444);
  static Color colorEAEBED = const Color(0xFFEAEBED);
  static Color color505050 = const Color(0xFF505050);
  static Color color5c5c5c = const Color(0xFF5c5c5c);
  static Color colorFFF2EF = const Color(0xFFFFF2EF);
  static Color colorE5B900 = const Color(0xFFE5B900);
  static Color colorFCD2D0 = const Color(0xFFFCD2D0);
  static Color color646363 = const Color(0xFF646363);
  static Color color45464E = const Color(0xFF45464E);
  static Color color495057 = const Color(0xFF495057);
  static Color colorFFF2E2 = const Color(0xFFFFF2E2);
  static Color colorF3F3F9 = const Color(0xFFF3F3F9);
  static Color color475366 = const Color(0xFF475366);
  static Color color6E7079 = const Color(0xFF6E7079);
  static Color colorD6D6D6 = const Color(0xFFD6D6D6);
  static Color color000000 = const Color(0xff000000);
  static Color color4D4D4D = const Color(0xff4D4D4D);
  static Color colorB80000 = const Color(0xffB80000);
  static Color colorB8B8B8 = const Color(0xffB8B8B8);
  static Color color83B4FF = const Color(0xff83B4FF);
  static Color color0298C8 = const Color(0xff0298C8);
  static Color color8E2E0F = const Color(0xff8E2E0F);
  static Color colorFCC490 = const Color(0xffFCC490);
  static Color color0F8E78 = const Color(0xff0F8E78);
  static Color color90FCA1 = const Color(0xff90FCA1);
  static Color color667A81 = const Color(0xff667A81);
  static Color colorE6E6E6 = const Color(0xffE6E6E6);
  static Color color0CA726 = const Color(0xff0CA726);
  static Color colorD7D7D7 = const Color(0xffD7D7D7);
  static Color colorCBCBCB = const Color(0xffCBCBCB);
  static Color colorAD76EE = const Color(0xffAD76EE);
  static Color colorE8E8E8 = const Color(0xffE8E8E8);
  static Color colorECECEC = const Color(0xffECECEC);
  static Color colorC2C2C2 = const Color(0xffC2C2C2);
  static Color colorE61212 = const Color(0xffE61212);
  static Color colorF21F6D = const Color(0xffF21F6D);
  static Color colorFDF9FF = const Color(0xffFDF9FF);
  static Color color636363 = const Color(0xff636363);
  static Color color121417 = const Color(0xFF121417);

  //A93226

  static MaterialColor prime = const MaterialColor(
    0xFFFED800,
    <int, Color>{
      50: Color(0xFFFFFDE7),
      100: Color(0xFFFFF9C4),
      200: Color(0xFFFFF59D),
      300: Color(0xFFFFF176),
      400: Color(0xFFFFEE58),
      500: Color(0xFFFED800),
      600: Color(0xFFFDD835),
      700: Color(0xFFFBC02D),
      800: Color(0xFFF9A825),
      900: Color(0xFFF57F17),
    },
  );
  static MaterialColor chewckBox = const MaterialColor(
    0xFF6E7079,
    <int, Color>{
      50: Color(0x0ffe7079),
      100: Color(0xFF6E7099),
      200: Color(0xFF6E7079),
      300: Color(0xFF6E7079),
      400: Color(0xFF6E7079),
      500: Color(0xFF6E7079),
      600: Color(0xFF6E7079),
      700: Color(0xFF6E7079),
      800: Color(0xFF6E7079),
      900: Color(0xFF6E7079),
    },
  );
}
