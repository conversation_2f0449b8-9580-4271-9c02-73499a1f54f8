// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
// import 'package:flutter_share/flutter_share.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:html/parser.dart' as html;

showSnackBarMessage({required BuildContext context, required String msg}) {
  SnackBar snackBar = SnackBar(
    content: Text(msg),
    behavior: SnackBarBehavior.floating,
  );
  ScaffoldMessenger.of(context).showSnackBar(snackBar);
}

hideKeyboard(BuildContext context) {
  FocusScope.of(context).unfocus();
}

launchURL(String url) async {
  if (!await launchUrl(Uri.parse(url))) {
    throw 'Could not launch $url';
  }
}

openDialer(String phoneNo) async {
  await launchURL('tel://$phoneNo');
}

Map<String, dynamic> castObjectToMap(Object? object) {
  return object as Map<String, dynamic>;
}

int _count = 0;
int _seconds = 2;
exitApp(BuildContext context) {
  if (_count == 0) {
    final snackBar = SnackBar(
      content: const Text('Press back again to exit!'),
      duration: Duration(seconds: _seconds),
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
    _count++;
    Timer(Duration(seconds: _seconds), () => _count = 0);
  } else {
    exit(0);
  }
}

DateTime getUpcomingBirthday(DateTime birthdate) {
  DateTime now = DateTime.now();
  late DateTime upcomingBirthday;
  DateTime? currentYearBirthday = DateTime(
    now.year,
    birthdate.month,
    birthdate.day,
  );
  DateTime today = DateTime(now.year, now.month, now.day, 0, 0, 0);
  if (currentYearBirthday.isBefore(today)) {
    upcomingBirthday = DateTime(
      currentYearBirthday.year + 1,
      birthdate.month,
      birthdate.day,
    );
  } else {
    upcomingBirthday = currentYearBirthday;
  }
  return upcomingBirthday;
}

DateTime getUpcomingWorkAnniversary(DateTime birthdate) {
  DateTime now = DateTime.now();
  late DateTime upcomingAnniversary;
  DateTime? currentYearAnniversary = DateTime(
    now.year,
    birthdate.month,
    birthdate.day,
  );
  DateTime today = DateTime(now.year, now.month, now.day, 0, 0, 0);
  if (currentYearAnniversary.isBefore(today)) {
    upcomingAnniversary = DateTime(
      currentYearAnniversary.year + 1,
      birthdate.month,
      birthdate.day,
    );
  } else {
    upcomingAnniversary = currentYearAnniversary;
  }
  return upcomingAnniversary;
}

bool validateForm(GlobalKey<FormState> key) {
  return key.currentState!.validate();
}

Future<void> share({
  required String title,
  required String text,
  required String linkUrl,
}) async {
  final note = html.parse(title.toString());
  final String parsedTitle = html.parse(note.body!.text).documentElement!.text;
  final note1 = html.parse(text.toString());
  final String parsedText = html.parse(note1.body!.text).documentElement!.text;
  // await FlutterShare.share(
  //     title: parsedTitle,
  //     text: parsedText,
  //     linkUrl: linkUrl,
  //     chooserTitle: 'Example Chooser Title');
}

DateTime? buttonClickTime;
bool isRedundentClick(DateTime currentTime) {
  if (buttonClickTime == null) {
    buttonClickTime = currentTime;
    return false;
  }
  if (currentTime.difference(buttonClickTime!).inSeconds < 3) {
    // set this difference time in seconds
    return true;
  }

  buttonClickTime = currentTime;
  return false;
}

Color stringToColor(String colorString) {
  // Remove the '#' if it is present
  String hexColor = colorString.replaceAll("#", "");

  // If the hex color doesn't have alpha (like 'RRGGBB'), add 'FF' for full opacity.
  if (hexColor.length == 6) {
    hexColor = "FF$hexColor"; // add alpha value
  }

  // Parse the string and convert it to an integer
  return Color(int.parse(hexColor, radix: 16));
}
