import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/slide_right_route.dart';

class PageNavigator {
  PageNavigator.push({required BuildContext context, required Widget route}) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => route));
  }
  PageNavigator.pushSlideup(
      {required BuildContext context, required Widget route}) {
    Navigator.push(context, SlideUpRoute(route));
  }
  PageNavigator.pushSlideRight(
      {required BuildContext context, required Widget route}) {
    Navigator.push(
        context,
        SlideRightRoute(
          route,
        ));
  }

  PageNavigator.pushReplacement(
      {required BuildContext context, required Widget route}) {
    Navigator.pushReplacement(
        context, MaterialPageRoute(builder: (context) => route));
  }
  PageNavigator.pushAndRemoveUntil(
      {required BuildContext context, required Widget route}) {
    Navigator.pushAndRemoveUntil(context,
        MaterialPageRoute(builder: (context) => route), (route) => false);
  }
}
