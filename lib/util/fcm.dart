import 'dart:developer';

import 'package:firebase_messaging/firebase_messaging.dart';

class FCM {
  static Future<String?> generateToken() async {
    return await FirebaseMessaging.instance.getToken();
  }

  static FirebaseMessaging messaging = FirebaseMessaging.instance;
  static Future<void> requestPermission() async {
    await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    log('FCM - ${await messaging.getToken()}');
  }
}
