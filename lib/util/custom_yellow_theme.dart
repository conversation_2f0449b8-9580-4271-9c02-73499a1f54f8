import 'package:e8_hr_portal/util/colors.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomYellowTheme {
  static ThemeData get theme {
    ColorScheme colorScheme = const ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xFFFED800),
      onPrimary: Color(0xFFFED800),
      secondary: Color(0xFFFCC400),
      onSecondary: Color(0xFFFCC400),
      error: Colors.red,
      onError: Colors.red,
      background: Colors.white,
      onBackground: Colors.white,
      surface: Colors.white,
      onSurface: Colors.white,
    );
    return ThemeData(
      primaryColor: const Color(0xFFFED800),
      colorScheme: colorScheme,
      appBarTheme: AppBarTheme(
        backgroundColor: const Color(0xFFFED800),
        iconTheme: const IconThemeData(
          color: Colors.black,
        ),
        centerTitle: false,
        titleTextStyle: GoogleFonts.rubik(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.w500,
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: const Color(0xFFF9637D),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          foregroundColor: Colors.black,
        ),
      ),
      // bottomAppBarTheme: const BottomAppBarTheme(color: Colors.white),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        selectedItemColor: Color(0xFFF9637D),
        unselectedItemColor: Color(0xFF8391B5),
      ),
      fontFamily: GoogleFonts.rubik().fontFamily,
      primarySwatch: Colors.blue,
      scaffoldBackgroundColor: ThemeColors.scaffoldBgColor,
    );
  }
}
