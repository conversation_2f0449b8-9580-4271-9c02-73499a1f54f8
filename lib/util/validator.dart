class Validator {
  static String? text(String? text) {
    if (text!.trim().isEmpty) {
      return 'This field is required';
    }
    return null;
  }

  static String? reason(String? text) {
    if (text!.trim().isEmpty) {
      return 'This field is required';
    } else if (text.length < 8) {
      return '8 characters are required';
    }
    return null;
  }

  static String? textSpecific(String text, String specific) {
    if (text.isEmpty) {
      return 'Please enter your $specific';
    }
    return null;
  }

  static String? email(value) {
    if (value.isEmpty) {
      return 'Please enter your email address';
    }
    if (!RegExp(
            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  static String? url(value) {
    bool validURL = Uri.parse(value).isAbsolute;
    if (validURL) {
      return null;
    } else {
      return 'Please enter a valid URL';
    }
  }
}
