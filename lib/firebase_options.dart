// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA8sUwViIWxo5x31sFL6lI1UU07ILBfeMM',
    appId: '1:580215990334:android:91fde661a057862fc1c96f',
    messagingSenderId: '580215990334',
    projectId: 'e8-hr-portal',
    storageBucket: 'e8-hr-portal.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCe7CxAZ_TZfUujXnKLOlguFTcW8Un7a24',
    appId: '1:580215990334:ios:4ecc49b356cc9a32c1c96f',
    messagingSenderId: '580215990334',
    projectId: 'e8-hr-portal',
    storageBucket: 'e8-hr-portal.firebasestorage.app',
    androidClientId: '580215990334-12i24gmio6ne7ohp42fmh05etk4s670b.apps.googleusercontent.com',
    iosClientId: '580215990334-i05504llri1d48cp6hcv7h9qj7ickd9h.apps.googleusercontent.com',
    iosBundleId: 'ae.element8.hrportal',
  );

}