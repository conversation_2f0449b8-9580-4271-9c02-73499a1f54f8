import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/styles.dart';

class TextFieldWidget extends StatelessWidget {
  final TextEditingController? controller;
  final bool enabled;
  final String? labelText;
  final String? hintText;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final TextCapitalization textCapitalization;
  final bool obscureText;
  final Color borderColor;
  final Color fillColor;
  final InputBorder? border;
  final Widget? suffixIcon;
  final Widget? suffix;
  final Widget? prefixIcon;
  final FocusNode? focusNode;
  final String? error;
  final void Function()? onEditingComplete;
  final void Function(String)? onChanged;
  final int? maxLines;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final EdgeInsetsGeometry? contentPadding;
  final List<TextInputFormatter>? inputFormatters;
  final bool readOnly;
  final VoidCallback? onTap;
  final TextStyle? errorStyle;
  final Widget? prefix;
  const TextFieldWidget({
    super.key,
    required this.controller,
    this.labelText,
    this.hintText,
    this.enabled = true,
    this.validator,
    this.keyboardType,
    this.textCapitalization = TextCapitalization.words,
    this.obscureText = false,
    this.borderColor = const Color(0xFFDEE7FF),
    this.fillColor = Colors.white,
    this.border,
    this.prefixIcon,
    this.suffixIcon,
    this.focusNode,
    this.onEditingComplete,
    this.onChanged,
    this.maxLines,
    this.error,
    this.textStyle,
    this.hintStyle,
    this.contentPadding,
    this.inputFormatters,
    this.readOnly = false,
    this.onTap,
    this.errorStyle,
    this.suffix,
    this.prefix,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      enabled: enabled,
      keyboardType: keyboardType,
      obscureText: obscureText,
      textCapitalization: textCapitalization,
      focusNode: focusNode,
      onEditingComplete: onEditingComplete,
      onChanged: onChanged,
      inputFormatters: inputFormatters,
      readOnly: readOnly,
      onTap: onTap,
      onTapOutside: (PointerDownEvent event) {
        FocusManager.instance.primaryFocus?.unfocus();
      },

      // enableInteractiveSelection: false,
      style: textStyle ??
          GoogleFonts.rubik(
            color: const Color(0xFF8391B5),
          ),
      maxLines: obscureText ? 1 : maxLines,

      decoration: InputDecoration(
          prefix: prefix,
          fillColor: enabled ? fillColor : ThemeColors.colorE3E3E3,
          filled: true,
          labelText: labelText,
          hintText: hintText,
          hintStyle: hintStyle,
          contentPadding: contentPadding,
          labelStyle: GoogleFonts.rubik(
            color: const Color(0xFF8391B5),
          ),
          border: border ?? outlineInputBorder(),
          enabledBorder: outlineInputBorder(),
          errorBorder: OutlineInputBorder(
            borderSide: BorderSide(
              width: 1,
              color: ThemeColors.errorColor,
            ),
            borderRadius: BorderRadius.circular(5),
          ),
          focusedBorder: border ??
              OutlineInputBorder(
                borderSide: BorderSide(
                  width: 1.3,
                  color: ThemeColors.primaryColor,
                ),
                borderRadius: BorderRadius.circular(5),
              ),
          suffix: suffix,
          suffixIcon: suffixIcon,
          prefixIcon: prefixIcon,
          errorText: error,
          errorStyle: errorStyle ?? tsS12w4cF64D44,
          errorMaxLines: 3),
      validator: validator,
    );
  }

  InputBorder? outlineInputBorder() {
    return OutlineInputBorder(
      borderSide: BorderSide(
        width: 1,
        color: borderColor,
      ),
      borderRadius: BorderRadius.circular(5),
    );
  }
}
