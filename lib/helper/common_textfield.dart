import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/util/colors.dart';

class CommonTextFieldWidget extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final TextInputType? keyboardType;
  final void Function()? onTap;
  final bool readOnly;
  final TextCapitalization textCapitalization;
  final String? Function(String?)? validator;
  final TextStyle? hintStyle;
  final String? hintText;
  final int? maxLength;
  final Widget? counter;

  const CommonTextFieldWidget(
      {super.key,
      this.controller,
      this.labelText,
      this.keyboardType,
      this.onTap,
      this.readOnly = false,
      this.textCapitalization = TextCapitalization.none,
      this.validator,
      this.hintText,
      this.hintStyle,
      this.counter,
      this.maxLength});

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      textCapitalization: textCapitalization,
      onTap: onTap,
      readOnly: readOnly,
      maxLength: maxLength,
      decoration: InputDecoration(
        hintStyle: hintStyle,
        counter: counter,
        hintText: hintText,
        labelText: labelText,
        labelStyle: GoogleFonts.rubik(
          color: ThemeColors.titleColor,
        ),
        border: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: Color(0xFFDEE7FF),
          ),
        ),
        enabledBorder: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: Color(0xFFDEE7FF),
          ),
        ),
      ),
      validator: validator,
    );
  }
}
