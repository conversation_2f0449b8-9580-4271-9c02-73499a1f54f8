import 'package:e8_hr_portal/util/colors.dart';
import 'package:flutter/material.dart';
import '../util/styles.dart';

class DropdownWidget2 extends StatelessWidget {
  final String? icon;
  final String hintText;
  final List<DropdownMenuItem<Object?>>? items;
  final Object? selectedValue;
  final Function(dynamic)? onChanged;
  final Function()? onTap;
  final String? Function(Object?)? validator;
  final bool validatorColor;
  const DropdownWidget2(
      {super.key,
      required this.hintText,
      this.items,
      this.selectedValue,
      this.onChanged,
      this.icon,
      this.validator,
      this.validatorColor = false,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: DropdownButtonFormField<Object?>(
            isExpanded: true,
            focusColor: Colors.white,
            decoration: InputDecoration(
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(horizontal: 10),
              border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(
                      color: !validatorColor
                          ? ThemeColors.color6E7079
                          : Colors.red)),
              focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(
                      color: !validatorColor
                          ? ThemeColors.color6E7079
                          : Colors.red)),
              enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(
                      color: !validatorColor
                          ? ThemeColors.color6E7079
                          : Colors.red)),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
              ),
            ),
            icon: icon != null
                ? ImageIcon(
                    AssetImage(icon!),
                    size: 17,
                    color: ThemeColors.color495057,
                  )
                : Icon(
                    Icons.keyboard_arrow_down,
                    color: ThemeColors.color495057,
                  ),
            style: tsS14NormalBlack,
            validator: validator,
            hint: Text(
              hintText,
              style: tsS14NormalBlack,
              overflow: TextOverflow.ellipsis,
            ),
            value: selectedValue,
            items: items,
            onChanged: onChanged),
      ),
    );
  }
}

class DropdownWidgetFlightTicket extends StatelessWidget {
  final String? icon;
  final String hintText;
  final List<DropdownMenuItem<Object?>>? items;
  final Object? selectedValue;
  final Function(dynamic)? onChanged;
  final Function()? onTap;
  final String? Function(Object?)? validator;
  final bool validatorColor;
  const DropdownWidgetFlightTicket(
      {super.key,
      required this.hintText,
      this.items,
      this.selectedValue,
      this.onChanged,
      this.icon,
      this.validator,
      this.validatorColor = false,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: DropdownButtonFormField<Object?>(
            isExpanded: true,
            isDense: true,
            focusColor: Colors.white,
            decoration: InputDecoration(
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(horizontal: 10),
              border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                      color: !validatorColor
                          ? ThemeColors.colorE3E3E3
                          : Colors.red)),
              focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                      color: !validatorColor
                          ? ThemeColors.colorE3E3E3
                          : Colors.red)),
              enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                      color: !validatorColor
                          ? ThemeColors.colorE3E3E3
                          : Colors.red)),
              errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Colors.red)),
            ),
            icon: icon != null
                ? ImageIcon(
                    AssetImage(icon!),
                    size: 17,
                    color: ThemeColors.color495057,
                  )
                : Icon(
                    Icons.keyboard_arrow_down,
                    color: ThemeColors.color495057,
                  ),
            style: tsS12NormalBlack,
            validator: validator,
            hint: Text(
              hintText,
              style: tsS12NormalBlack,
              overflow: TextOverflow.ellipsis,
            ),
            value: selectedValue,
            items: items,
            onChanged: onChanged),
      ),
    );
  }
}
