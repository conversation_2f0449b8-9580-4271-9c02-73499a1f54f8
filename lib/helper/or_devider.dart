import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ORDevider extends StatelessWidget {
  const ORDevider({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 15),
        Text(
          'OR',
          textAlign: TextAlign.center,
          style: GoogleFonts.rubik(
            color: const Color(0xFF7E7E7E),
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 15),
      ],
    );
  }
}
