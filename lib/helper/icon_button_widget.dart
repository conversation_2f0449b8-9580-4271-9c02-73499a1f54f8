import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class IconButtonWidget extends StatelessWidget {
  final VoidCallback onPressed;
  final String label;
  final bool isLoading;
  final EdgeInsets? margin;
  final Widget icon;
  final Color color;
  const IconButtonWidget({
    super.key,
    this.color = Colors.white,
    required this.onPressed,
    required this.icon,
    required this.label,
    this.isLoading = false,
    this.margin,
  });
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: margin ?? const EdgeInsets.all(0),
      child: isLoading
          ? const CupertinoActivityIndicator()
          : ElevatedButton.icon(
              onPressed: isLoading ? null : onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                minimumSize: const Size(double.infinity, 45),
              ),
              icon: icon,
              label: Padding(
                padding: const EdgeInsets.only(left: 20),
                child: Text(
                  label,
                  style: const TextStyle(
                    color: Color(0xFF120D26),
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
    );
  }
}
