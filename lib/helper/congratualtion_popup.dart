import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CongratulationsPopUp extends StatelessWidget {
  final String? subtitle;
  const CongratulationsPopUp({required this.subtitle, super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 20),
      child: Padding(
        padding: const EdgeInsets.all(45),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              'assets/icons/congratulations_popup.png',
              height: 85,
            ),
            const SizedBox(height: 15),
            Text(
              'Congratulations!',
              textAlign: TextAlign.center,
              style: GoogleFonts.rubik(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: ThemeColors.color242A38,
              ),
            ),
            const SizedBox(height: 15),
            Text(
              // 'We have sent a reset OTP to your\nmobile number',
              subtitle.toString(),
              textAlign: TextAlign.center,
              style: GoogleFonts.rubik(
                fontSize: 13,
                color: ThemeColors.color242A38,
              ),
            ),
            const SizedBox(height: 35),
            Align(
              alignment: Alignment.center,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  minimumSize: Size(100 * w, 150 * h),
                  backgroundColor: ThemeColors.colorFFCB05,
                  elevation: 0,
                  // padding:
                  //     const EdgeInsets.symmetric(horizontal: 90, vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                child: Text(
                  'OK',
                  textDirection: TextDirection.ltr,
                  style: GoogleFonts.rubik(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
