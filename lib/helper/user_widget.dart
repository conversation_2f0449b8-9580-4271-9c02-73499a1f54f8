import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e8_hr_portal/model/user_model.dart';
// ignore: unused_import
import 'package:e8_hr_portal/util/colors.dart';

class UserWidget extends StatelessWidget {
  final String uid;
  final String userName;
  const UserWidget({
    super.key,
    required this.uid,
    required this.userName,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<DocumentSnapshot<Map<String, dynamic>>>(
      future: FirebaseFirestore.instance.collection('users').doc(uid).get(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          DocumentSnapshot<Map<String, dynamic>> doc = snapshot.data!;
          Map<String, dynamic>? json = doc.data();
          UserModel user = UserModel.fromJson(json!);
          return UserCard(
            uid: uid,
            userName: userName,
            profilePhoto: user.profilePhoto,
            designation: user.designation,
            user: user,
          );
        }
        return UserCard(uid: uid, userName: userName);
      },
    );
  }
}

class UserCard extends StatelessWidget {
  final String uid;
  final String userName;
  final String? profilePhoto;
  final String? designation;
  final UserModel? user;
  const UserCard({
    super.key,
    required this.uid,
    required this.userName,
    this.profilePhoto,
    this.designation,
    this.user,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      // onTap: () => PageNavigator.push(
      //   context: context,
      //   route: UserDetailScreen(
      //     userId: uid,
      //     userName: userName,
      //     user: user,
      //   ),
      // ),
      child: Row(
        children: [
          Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color:
                  profilePhoto != null ? null : Theme.of(context).primaryColor,
              shape: BoxShape.circle,
              image: profilePhoto != null
                  ? DecorationImage(
                      image: CachedNetworkImageProvider(profilePhoto!),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            height: 50,
            width: 50,
            child: profilePhoto != null
                ? null
                : Text(
                    userName.substring(0, 1).toUpperCase(),
                    style: GoogleFonts.rubik(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  userName,
                  style: GoogleFonts.rubik(
                    color: const Color(0xFF1E2138),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (designation != null)
                  Text(
                    '$designation',
                    style: GoogleFonts.rubik(
                      color: const Color(0xFF1E2138),
                      fontSize: 12,
                      fontWeight: FontWeight.w300,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
