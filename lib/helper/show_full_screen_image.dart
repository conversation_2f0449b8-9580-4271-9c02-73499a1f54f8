import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class ShowFullScreenImage extends StatelessWidget {
  final String image;
  final String tag;
  const ShowFullScreenImage(
      {super.key, required this.image, required this.tag});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        leading: const BackButton(
          color: Colors.white,
        ),
      ),
      body: Center(
        child: Hero(
          tag: tag,
          child: CachedNetworkImage(imageUrl: image),
        ),
      ),
    );
  }
}
