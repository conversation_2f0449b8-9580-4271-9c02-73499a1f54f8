import 'package:e8_hr_portal/util/colors.dart';
import 'package:e8_hr_portal/util/size_config.dart';
import 'package:flutter/material.dart';
import '../util/styles.dart';

class TextfieldWithFilterIcon extends StatelessWidget {
  final TextEditingController? controller;
  final bool isShowCloseButton;
  final void Function()? onButtonPressed;
  final void Function()? onTap;
  final String hintText;
  final void Function(String)? onChanged;
  final bool isShowFilterButton;
  const TextfieldWithFilterIcon(
      {super.key,
      this.onTap,
      required this.hintText,
      this.onChanged,
      this.isShowCloseButton = false,
      this.onButtonPressed,
      this.controller,
      this.isShowFilterButton = true});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: SizedBox(
            height: h * 42,
            child: TextField(
              controller: controller,
              onChanged: onChanged,
              decoration: InputDecoration(
                  hintText: hintText,
                  prefixIcon: IconButton(
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onPressed: () {},
                    icon: ImageIcon(
                      const AssetImage('assets/icons/search2.png'),
                      color: ThemeColors.color0048A5,
                    ),
                  ),
                  suffixIcon: isShowCloseButton
                      ? IconButton(
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onPressed: onButtonPressed,
                          icon: ImageIcon(
                            const AssetImage('assets/icons/close_icon.png'),
                            color: ThemeColors.color808080,
                          ),
                        )
                      : null,
                  hintStyle: tsS14NormalBlack,
                  fillColor: ThemeColors.colorACACAC.withOpacity(0.3),
                  filled: true,
                  contentPadding: EdgeInsets.symmetric(vertical: h * 10),
                  border: OutlineInputBorder(
                      borderSide: const BorderSide(color: Colors.transparent),
                      borderRadius: BorderRadius.circular(8)),
                  focusedBorder: OutlineInputBorder(
                      borderSide: const BorderSide(color: Colors.transparent),
                      borderRadius: BorderRadius.circular(8)),
                  enabledBorder: OutlineInputBorder(
                      borderSide: const BorderSide(color: Colors.transparent),
                      borderRadius: BorderRadius.circular(8))),
            ),
          ),
        ),
        if (isShowFilterButton) SizedBox(width: w * 10),
        if (isShowFilterButton)
          InkWell(
            onTap: onTap,
            child: Container(
              height: h * 42,
              width: w * 41,
              padding: const EdgeInsets.all(9),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: ThemeColors.color03AD9E),
              child: const ImageIcon(
                AssetImage('assets/icons/settings2.png'),
                color: Colors.white,
              ),
            ),
          )
      ],
    );
  }
}
