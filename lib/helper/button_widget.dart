import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:e8_hr_portal/util/colors.dart';

class ButtonWidget extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? title;
  final Widget? child;
  final Color? color;
  final bool isLoading;
  final TextStyle? textStyle;
  final EdgeInsets? margin;
  const ButtonWidget({
    super.key,
    required this.onPressed,
    this.title,
    this.child,
    this.isLoading = false,
    this.margin,
    this.color,
    this.textStyle,
  });
  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: margin ?? const EdgeInsets.all(0),
        child: Container(
          // width: width,
          // height: height,
          decoration: BoxDecoration(
            color: color ?? ThemeColors.primaryColor,
            // gradient: const LinearGradient(
            //     // stops: [96.13, 83.07],
            //     begin: Alignment.centerLeft,
            //     colors: [Color(0xff03AD9E), Color(0xff12DFCC)]),
            borderRadius: BorderRadius.circular(50),
          ),
          child: ElevatedButton(
            onPressed: isLoading ? null : onPressed,
            style: ElevatedButton.styleFrom(
              elevation: 0, foregroundColor: Colors.white70,
              // backgroundColor: Colors.transparent,
              minimumSize: const Size(double.infinity, 55),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
              ),
            ),
            child: isLoading
                ? const CupertinoActivityIndicator()
                : child ?? Text('$title', style: textStyle),
          ),
        ));
  }
}

class GoogleButtonWidget extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? title;
  final Widget? child;
  final Color? color;
  final bool isLoading;
  final TextStyle? textStyle;
  final EdgeInsets? margin;
  const GoogleButtonWidget({
    super.key,
    required this.onPressed,
    this.title,
    this.child,
    this.isLoading = false,
    this.margin,
    this.color,
    this.textStyle,
  });
  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: margin ?? const EdgeInsets.all(0),
        child: Container(
          // width: width,
          // height: height,
          decoration: BoxDecoration(
            color: color ?? ThemeColors.primaryColor,
            // gradient: const LinearGradient(
            //     // stops: [96.13, 83.07],
            //     begin: Alignment.centerLeft,
            //     colors: [Color(0xff03AD9E), Color(0xff12DFCC)]),
            borderRadius: BorderRadius.circular(50),
          ),
          child: ElevatedButton.icon(
            icon: Image.asset(
              'assets/icons/google.png',
              scale: 2,
            ),
            onPressed: isLoading ? null : onPressed,
            style: ElevatedButton.styleFrom(
              elevation: 0,
              backgroundColor: Colors.transparent,
              minimumSize: const Size(double.infinity, 55),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
              ),
            ),
            label: isLoading
                ? const CupertinoActivityIndicator()
                : child ?? Text('$title', style: textStyle),
          ),
        ));
  }
}
