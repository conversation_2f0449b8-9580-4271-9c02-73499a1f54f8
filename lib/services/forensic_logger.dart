import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:e8_hr_portal/util/urls.dart';

/// ForensicLogger provides comprehensive security and audit logging capabilities
/// for the E8 HR Portal application. It logs authentication events, security
/// incidents, and user activities for compliance and security monitoring.
class ForensicLogger {
  static const String _logPrefix = '[FORENSIC]';
  static const int _maxLocalLogs = 1000;
  static const String _localLogsKey = 'forensic_logs';

  /// Logs authentication-related events including login attempts, successes,
  /// failures, token refreshes, and logout events.
  ///
  /// [eventType] - Type of authentication event (e.g., 'LOGIN_ATTEMPT', 'LOGIN_SUCCESS')
  /// [userId] - User identifier (email or ID), can be null for failed attempts
  /// [success] - Whether the authentication event was successful
  /// [metadata] - Additional context data for the event
  static Future<void> logAuthenticationEvent(
    String eventType,
    String? userId,
    bool success,
    Map<String, dynamic> metadata,
  ) async {
    try {
      final logEntry = {
        'timestamp': DateTime.now().toIso8601String(),
        'event_type': eventType,
        'category': 'AUTHENTICATION',
        'user_id': userId,
        'success': success,
        'metadata': metadata,
        'session_id': await _getSessionId(),
        'device_info': await _getDeviceInfo(),
      };

      // Log locally for debugging
      await _logLocally(logEntry);

      // Send to server for centralized logging
      await _sendToServer(logEntry);

      // Log to console in debug mode
      if (kDebugMode) {
        log('$_logPrefix AUTH: $eventType - Success: $success - User: $userId');
      }
    } catch (e) {
      // Ensure logging failures don't break the app
      if (kDebugMode) {
        log('$_logPrefix Error logging authentication event: $e');
      }
    }
  }

  /// Logs security-related events such as suspicious activities,
  /// unauthorized access attempts, and security policy violations.
  ///
  /// [eventType] - Type of security event
  /// [severity] - Severity level ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')
  /// [description] - Human-readable description of the event
  /// [metadata] - Additional context data
  static Future<void> logSecurityEvent(
    String eventType,
    String severity,
    String description,
    Map<String, dynamic> metadata,
  ) async {
    try {
      final logEntry = {
        'timestamp': DateTime.now().toIso8601String(),
        'event_type': eventType,
        'category': 'SECURITY',
        'severity': severity,
        'description': description,
        'metadata': metadata,
        'session_id': await _getSessionId(),
        'device_info': await _getDeviceInfo(),
      };

      await _logLocally(logEntry);
      await _sendToServer(logEntry);

      if (kDebugMode) {
        log('$_logPrefix SECURITY: $eventType - Severity: $severity - $description');
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error logging security event: $e');
      }
    }
  }

  /// Logs user activity events for audit trails and compliance.
  ///
  /// [action] - The action performed by the user
  /// [resource] - The resource or feature accessed
  /// [userId] - User identifier
  /// [metadata] - Additional context data
  static Future<void> logUserActivity(
    String action,
    String resource,
    String? userId,
    Map<String, dynamic> metadata,
  ) async {
    try {
      final logEntry = {
        'timestamp': DateTime.now().toIso8601String(),
        'event_type': 'USER_ACTIVITY',
        'category': 'AUDIT',
        'action': action,
        'resource': resource,
        'user_id': userId,
        'metadata': metadata,
        'session_id': await _getSessionId(),
        'device_info': await _getDeviceInfo(),
      };

      await _logLocally(logEntry);
      await _sendToServer(logEntry);

      if (kDebugMode) {
        log('$_logPrefix ACTIVITY: $action on $resource - User: $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error logging user activity: $e');
      }
    }
  }

  /// Stores log entries locally for offline capability and backup
  static Future<void> _logLocally(Map<String, dynamic> logEntry) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final existingLogs = prefs.getStringList(_localLogsKey) ?? [];
      
      existingLogs.add(jsonEncode(logEntry));
      
      // Keep only the most recent logs to prevent storage bloat
      if (existingLogs.length > _maxLocalLogs) {
        existingLogs.removeRange(0, existingLogs.length - _maxLocalLogs);
      }
      
      await prefs.setStringList(_localLogsKey, existingLogs);
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error storing log locally: $e');
      }
    }
  }

  /// Sends log entries to the server for centralized logging and monitoring
  static Future<void> _sendToServer(Map<String, dynamic> logEntry) async {
    try {
      // Only send to server in production or when explicitly enabled
      if (kDebugMode) {
        // In debug mode, just log locally
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      final accessToken = prefs.getString('access_token');
      
      if (accessToken == null) {
        // No token available, store for later transmission
        return;
      }

      final response = await http.post(
        Uri.parse('${baseUrl}forensic-logs/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
        body: jsonEncode(logEntry),
      );

      if (response.statusCode != 200 && response.statusCode != 201) {
        if (kDebugMode) {
          log('$_logPrefix Failed to send log to server: ${response.statusCode}');
        }
      }
    } catch (e) {
      // Network errors shouldn't break the app
      if (kDebugMode) {
        log('$_logPrefix Error sending log to server: $e');
      }
    }
  }

  /// Generates a session identifier for tracking user sessions
  static Future<String> _getSessionId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? sessionId = prefs.getString('session_id');
      
      if (sessionId == null) {
        sessionId = DateTime.now().millisecondsSinceEpoch.toString();
        await prefs.setString('session_id', sessionId);
      }
      
      return sessionId;
    } catch (e) {
      return 'unknown_session';
    }
  }

  /// Collects basic device information for forensic context
  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return {
        'platform': defaultTargetPlatform.name,
        'app_version': '1.0.0', // This should come from package info
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {'error': 'Failed to collect device info'};
    }
  }

  /// Retrieves locally stored logs for debugging or offline analysis
  static Future<List<Map<String, dynamic>>> getLocalLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logStrings = prefs.getStringList(_localLogsKey) ?? [];
      
      return logStrings.map((logString) {
        try {
          return jsonDecode(logString) as Map<String, dynamic>;
        } catch (e) {
          return {'error': 'Failed to parse log entry'};
        }
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error retrieving local logs: $e');
      }
      return [];
    }
  }

  /// Clears all locally stored logs
  static Future<void> clearLocalLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_localLogsKey);
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error clearing local logs: $e');
      }
    }
  }
}
