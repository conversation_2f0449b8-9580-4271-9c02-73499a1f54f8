import 'dart:convert';
import 'dart:developer';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';
import 'package:e8_hr_portal/services/forensic_logger.dart';

/// SecureKeyManager provides secure storage and management of sensitive data
/// including authentication tokens, encryption keys, and other credentials.
/// Uses platform-specific secure storage mechanisms (Keychain on iOS, Keystore on Android).
class SecureKeyManager {
  static const String _logPrefix = '[SECURE_KEY_MANAGER]';

  // Storage keys
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _encryptionKeyKey = 'encryption_key';
  static const String _sessionIdKey = 'session_id';
  static const String _userIdKey = 'user_id';
  static const String _biometricEnabledKey = 'biometric_enabled';

  // Secure storage instance with iOS-compatible configuration
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      sharedPreferencesName: 'e8_hr_portal_secure_prefs',
      preferencesKeyPrefix: 'e8_',
    ),
    iOptions: IOSOptions(
      // Use most compatible iOS settings to avoid entitlement issues
      accessibility: KeychainAccessibility.unlocked_this_device,
      synchronizable: false,
    ),
    lOptions: LinuxOptions(),
    wOptions: WindowsOptions(),
    mOptions: MacOsOptions(
      accessibility: KeychainAccessibility.unlocked_this_device,
    ),
  );

  /// Tests if secure storage is available and working
  static Future<bool> testSecureStorage() async {
    try {
      const testKey = 'test_secure_storage';
      const testValue = 'test_value';

      // Try to write, read, and delete a test value
      await _secureStorage.write(key: testKey, value: testValue);
      final retrievedValue = await _secureStorage.read(key: testKey);
      await _secureStorage.delete(key: testKey);

      final isWorking = retrievedValue == testValue;

      await ForensicLogger.logSecurityEvent(
        'SECURE_STORAGE_TEST',
        'LOW',
        'Secure storage test completed',
        {'is_working': isWorking},
      );

      if (kDebugMode) {
        log('$_logPrefix Secure storage test: ${isWorking ? 'PASSED' : 'FAILED'}');
      }

      return isWorking;
    } catch (e) {
      await ForensicLogger.logSecurityEvent(
        'SECURE_STORAGE_TEST_FAILED',
        'MEDIUM',
        'Secure storage test failed',
        {'error': e.toString()},
      );

      if (kDebugMode) {
        log('$_logPrefix Secure storage test failed: $e');
      }

      return false;
    }
  }

  /// Stores the access token securely
  static Future<void> storeAccessToken(String token) async {
    try {
      await _secureStorage.write(key: _accessTokenKey, value: token);

      await ForensicLogger.logSecurityEvent(
        'TOKEN_STORED',
        'LOW',
        'Access token stored securely',
        {'token_length': token.length},
      );

      if (kDebugMode) {
        log('$_logPrefix Access token stored securely');
      }
    } catch (e) {
      await ForensicLogger.logSecurityEvent(
        'TOKEN_STORAGE_FAILED',
        'HIGH',
        'Failed to store access token',
        {'error': e.toString()},
      );

      if (kDebugMode) {
        log('$_logPrefix Error storing access token: $e');
      }
      rethrow;
    }
  }

  /// Retrieves the access token securely
  static Future<String?> getAccessToken() async {
    try {
      final token = await _secureStorage.read(key: _accessTokenKey);

      if (token != null) {
        await ForensicLogger.logSecurityEvent(
          'TOKEN_RETRIEVED',
          'LOW',
          'Access token retrieved',
          {'has_token': true},
        );
      }

      return token;
    } catch (e) {
      await ForensicLogger.logSecurityEvent(
        'TOKEN_RETRIEVAL_FAILED',
        'MEDIUM',
        'Failed to retrieve access token',
        {'error': e.toString()},
      );

      if (kDebugMode) {
        log('$_logPrefix Error retrieving access token: $e');
      }
      return null;
    }
  }

  /// Stores the refresh token securely
  static Future<void> storeRefreshToken(String token) async {
    try {
      await _secureStorage.write(key: _refreshTokenKey, value: token);

      await ForensicLogger.logSecurityEvent(
        'REFRESH_TOKEN_STORED',
        'LOW',
        'Refresh token stored securely',
        {'token_length': token.length},
      );

      if (kDebugMode) {
        log('$_logPrefix Refresh token stored securely');
      }
    } catch (e) {
      await ForensicLogger.logSecurityEvent(
        'REFRESH_TOKEN_STORAGE_FAILED',
        'HIGH',
        'Failed to store refresh token',
        {'error': e.toString()},
      );

      if (kDebugMode) {
        log('$_logPrefix Error storing refresh token: $e');
      }
      rethrow;
    }
  }

  /// Retrieves the refresh token securely
  static Future<String?> getRefreshToken() async {
    try {
      final token = await _secureStorage.read(key: _refreshTokenKey);

      if (token != null) {
        await ForensicLogger.logSecurityEvent(
          'REFRESH_TOKEN_RETRIEVED',
          'LOW',
          'Refresh token retrieved',
          {'has_token': true},
        );
      }

      return token;
    } catch (e) {
      await ForensicLogger.logSecurityEvent(
        'REFRESH_TOKEN_RETRIEVAL_FAILED',
        'MEDIUM',
        'Failed to retrieve refresh token',
        {'error': e.toString()},
      );

      if (kDebugMode) {
        log('$_logPrefix Error retrieving refresh token: $e');
      }
      return null;
    }
  }

  /// Generates and stores a secure encryption key
  static Future<String> getEncryptionKey() async {
    try {
      // Check if encryption key already exists
      String? existingKey = await _secureStorage.read(key: _encryptionKeyKey);

      if (existingKey != null && existingKey.isNotEmpty) {
        return existingKey;
      }

      // Generate a new 256-bit encryption key
      final key = _generateSecureKey();
      await _secureStorage.write(key: _encryptionKeyKey, value: key);

      await ForensicLogger.logSecurityEvent(
        'ENCRYPTION_KEY_GENERATED',
        'MEDIUM',
        'New encryption key generated and stored',
        {'key_length': key.length},
      );

      if (kDebugMode) {
        log('$_logPrefix New encryption key generated and stored');
      }

      return key;
    } catch (e) {
      await ForensicLogger.logSecurityEvent(
        'ENCRYPTION_KEY_GENERATION_FAILED',
        'CRITICAL',
        'Failed to generate encryption key',
        {'error': e.toString()},
      );

      if (kDebugMode) {
        log('$_logPrefix Error generating encryption key: $e');
      }
      rethrow;
    }
  }

  /// Stores user ID securely
  static Future<void> storeUserId(String userId) async {
    try {
      await _secureStorage.write(key: _userIdKey, value: userId);

      if (kDebugMode) {
        log('$_logPrefix User ID stored securely');
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error storing user ID: $e');
      }
      rethrow;
    }
  }

  /// Retrieves user ID securely
  static Future<String?> getUserId() async {
    try {
      return await _secureStorage.read(key: _userIdKey);
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error retrieving user ID: $e');
      }
      return null;
    }
  }

  /// Clears all stored keys and tokens
  static Future<void> clearAllKeys() async {
    try {
      await _secureStorage.deleteAll();

      await ForensicLogger.logSecurityEvent(
        'ALL_KEYS_CLEARED',
        'MEDIUM',
        'All secure keys and tokens cleared',
        {'timestamp': DateTime.now().toIso8601String()},
      );

      if (kDebugMode) {
        log('$_logPrefix All secure keys cleared');
      }
    } catch (e) {
      await ForensicLogger.logSecurityEvent(
        'KEY_CLEARING_FAILED',
        'HIGH',
        'Failed to clear secure keys',
        {'error': e.toString()},
      );

      if (kDebugMode) {
        log('$_logPrefix Error clearing keys: $e');
      }
      rethrow;
    }
  }

  /// Clears only authentication tokens
  static Future<void> clearAuthTokens() async {
    try {
      await _secureStorage.delete(key: _accessTokenKey);
      await _secureStorage.delete(key: _refreshTokenKey);

      await ForensicLogger.logSecurityEvent(
        'AUTH_TOKENS_CLEARED',
        'LOW',
        'Authentication tokens cleared',
        {'timestamp': DateTime.now().toIso8601String()},
      );

      if (kDebugMode) {
        log('$_logPrefix Authentication tokens cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error clearing auth tokens: $e');
      }
      rethrow;
    }
  }

  /// Generates a cryptographically secure random key
  static String _generateSecureKey() {
    final random = math.Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }

  /// Validates if a token is expired (basic JWT validation)
  static bool isTokenExpired(String? token) {
    if (token == null || token.isEmpty) return true;

    try {
      final parts = token.split('.');
      if (parts.length != 3) return true;

      final payload = parts[1];
      final normalized = base64Url.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      final Map<String, dynamic> payloadMap = jsonDecode(decoded);

      if (payloadMap.containsKey('exp')) {
        final exp = payloadMap['exp'] as int;
        final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
        return now >= exp;
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error validating token expiration: $e');
      }
      return true;
    }
  }

  /// Checks if secure storage is available on the platform
  static Future<bool> isSecureStorageAvailable() async {
    try {
      await _secureStorage.containsKey(key: 'test_key');
      return true;
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Secure storage not available: $e');
      }
      return false;
    }
  }
}
