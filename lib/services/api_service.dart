import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:e8_hr_portal/services/secure_key_manager.dart';
import 'package:e8_hr_portal/services/forensic_logger.dart';

/// ApiService provides secure HTTP client functionality with automatic
/// token management, request/response logging, and security monitoring.
class ApiService {
  static const String _logPrefix = '[API_SERVICE]';
  static const Duration _defaultTimeout = Duration(seconds: 30);

  /// Performs a secure GET request with automatic token management
  static Future<http.Response> get(
    Uri url, {
    Map<String, String>? headers,
    Duration? timeout,
  }) async {
    return _makeRequest(
      'GET',
      url,
      headers: headers,
      timeout: timeout,
    );
  }

  /// Performs a secure POST request with automatic token management
  static Future<http.Response> post(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
    Duration? timeout,
  }) async {
    return _makeRequest(
      'POST',
      url,
      headers: headers,
      body: body,
      encoding: encoding,
      timeout: timeout,
    );
  }

  /// Performs a secure PUT request with automatic token management
  static Future<http.Response> put(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
    Duration? timeout,
  }) async {
    return _makeRequest(
      'PUT',
      url,
      headers: headers,
      body: body,
      encoding: encoding,
      timeout: timeout,
    );
  }

  /// Performs a secure DELETE request with automatic token management
  static Future<http.Response> delete(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
    Duration? timeout,
  }) async {
    return _makeRequest(
      'DELETE',
      url,
      headers: headers,
      body: body,
      encoding: encoding,
      timeout: timeout,
    );
  }

  /// Internal method to handle all HTTP requests with security features
  static Future<http.Response> _makeRequest(
    String method,
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
    Duration? timeout,
  }) async {
    final requestTimeout = timeout ?? _defaultTimeout;
    final requestHeaders = await _buildSecureHeaders(headers);
    
    try {
      // Log the API request for forensic purposes
      await ForensicLogger.logUserActivity(
        'API_REQUEST',
        url.toString(),
        await _getCurrentUserId(),
        {
          'method': method,
          'url': url.toString(),
          'has_body': body != null,
        },
      );

      http.Response response;
      
      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(url, headers: requestHeaders)
              .timeout(requestTimeout);
          break;
        case 'POST':
          response = await http.post(
            url,
            headers: requestHeaders,
            body: body,
            encoding: encoding,
          ).timeout(requestTimeout);
          break;
        case 'PUT':
          response = await http.put(
            url,
            headers: requestHeaders,
            body: body,
            encoding: encoding,
          ).timeout(requestTimeout);
          break;
        case 'DELETE':
          response = await http.delete(
            url,
            headers: requestHeaders,
            body: body,
            encoding: encoding,
          ).timeout(requestTimeout);
          break;
        default:
          throw UnsupportedError('HTTP method $method is not supported');
      }

      // Log the API response for forensic purposes
      await _logApiResponse(method, url, response);

      // Handle authentication errors
      if (response.statusCode == 401) {
        await _handleUnauthorized(url);
      }

      return response;
    } catch (e) {
      // Log API errors for security monitoring
      await ForensicLogger.logSecurityEvent(
        'API_ERROR',
        'MEDIUM',
        'API request failed',
        {
          'method': method,
          'url': url.toString(),
          'error': e.toString(),
        },
      );

      if (kDebugMode) {
        log('$_logPrefix Error making $method request to $url: $e');
      }
      
      rethrow;
    }
  }

  /// Builds secure headers with authentication token
  static Future<Map<String, String>> _buildSecureHeaders(
    Map<String, String>? additionalHeaders,
  ) async {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add additional headers if provided
    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }

    // Add authentication token if available
    try {
      final accessToken = await SecureKeyManager.getAccessToken();
      if (accessToken != null && accessToken.isNotEmpty) {
        headers['Authorization'] = 'Bearer $accessToken';
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Warning: Could not retrieve access token: $e');
      }
    }

    return headers;
  }

  /// Logs API responses for forensic analysis
  static Future<void> _logApiResponse(
    String method,
    Uri url,
    http.Response response,
  ) async {
    try {
      await ForensicLogger.logUserActivity(
        'API_RESPONSE',
        url.toString(),
        await _getCurrentUserId(),
        {
          'method': method,
          'url': url.toString(),
          'status_code': response.statusCode,
          'response_size': response.body.length,
          'success': response.statusCode >= 200 && response.statusCode < 300,
        },
      );

      if (kDebugMode) {
        log('$_logPrefix $method ${url.toString()} - Status: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error logging API response: $e');
      }
    }
  }

  /// Handles unauthorized responses by attempting token refresh
  static Future<void> _handleUnauthorized(Uri url) async {
    try {
      await ForensicLogger.logSecurityEvent(
        'UNAUTHORIZED_ACCESS',
        'HIGH',
        'Received 401 Unauthorized response',
        {
          'url': url.toString(),
          'action': 'token_refresh_needed',
        },
      );

      if (kDebugMode) {
        log('$_logPrefix Unauthorized access detected for: $url');
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error handling unauthorized response: $e');
      }
    }
  }

  /// Gets the current user ID for logging purposes
  static Future<String?> _getCurrentUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('email');
    } catch (e) {
      return null;
    }
  }

  /// Creates headers for API requests with secure token management
  static Future<Map<String, String>> getSecureHeaders() async {
    return await _buildSecureHeaders(null);
  }

  /// Validates if the current session is authenticated
  static Future<bool> isAuthenticated() async {
    try {
      final accessToken = await SecureKeyManager.getAccessToken();
      return accessToken != null && accessToken.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
