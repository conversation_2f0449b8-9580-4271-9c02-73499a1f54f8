import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../model/ai_chat_models.dart';

import 'ai_config_service.dart';

/// Enhanced AI Chatbot Service that integrates with Qwen-3B-Coder model
/// and provides actionable HR conversations
class AIChatbotService {
  static const String _logPrefix = '[AI_CHATBOT_SERVICE]';
  static const Duration _defaultTimeout = Duration(seconds: 30);

  final Dio _dio = Dio();

  AIChatbotService() {
    _dio.options.connectTimeout = _defaultTimeout;
    _dio.options.receiveTimeout = _defaultTimeout;
  }

  /// Send message to AI model and get response with intent classification
  Future<AIChatResponse> sendMessage({
    required String message,
    List<AIChatMessage>? conversationHistory,
    String? context,
  }) async {
    try {
      if (kDebugMode) {
        log('$_logPrefix Sending message to AI: $message');
      }

      // Build conversation messages
      List<Map<String, String>> messages = _buildConversationMessages(
        message: message,
        conversationHistory: conversationHistory,
        context: context,
      );

      // Get AI configuration (hardcoded values)
      String aiBaseUrl = await AIConfigService.getBaseUrl();
      String aiModel = await AIConfigService.getModel();
      String? aiToken = await AIConfigService.getApiToken();

      final response = await _dio.post(
        '$aiBaseUrl/chat/completions',
        data: {
          'model': aiModel,
          'messages': messages,
          'max_tokens': 500,
          'temperature': 0.7,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $aiToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return _parseAIResponse(response.data, message);
      } else {
        throw Exception('AI API returned status: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error sending message to AI: $e');
      }
      return AIChatResponse(
        originalMessage: message,
        aiResponse:
            'I apologize, but I\'m having trouble processing your request right now. Please try again.',
        intent: ChatIntent.unknown,
        confidence: 0.0,
        requiresAction: false,
      );
    }
  }

  /// Build conversation messages with system prompt for HR context
  List<Map<String, String>> _buildConversationMessages({
    required String message,
    List<AIChatMessage>? conversationHistory,
    String? context,
  }) {
    List<Map<String, String>> messages = [];

    // System prompt to guide the AI for HR tasks
    messages.add({
      'role': 'system',
      'content': _getSystemPrompt(),
    });

    // Add conversation history if available
    if (conversationHistory != null) {
      for (var historyMessage in conversationHistory) {
        messages.add({
          'role': historyMessage.isUser ? 'user' : 'assistant',
          'content': historyMessage.content,
        });
      }
    }

    // Add current context if available
    if (context != null && context.isNotEmpty) {
      messages.add({
        'role': 'system',
        'content': 'Current context: $context',
      });
    }

    // Add current user message
    messages.add({
      'role': 'user',
      'content': message,
    });

    return messages;
  }

  /// Get system prompt for HR assistant context
  String _getSystemPrompt() {
    return '''You are an AI assistant for an HR Portal system. You can help users with various HR tasks including:

1. LEAVE MANAGEMENT: Apply for leave, check leave balance, view leave history, cancel leave requests
2. WORK FROM HOME: Apply for WFH, check WFH status, manage WFH requests
3. EXPENSE MANAGEMENT: Submit expenses, track reimbursements, upload receipts
4. MEETING ROOMS: Book meeting rooms, check availability
5. INFORMATION QUERIES: Company policies, holidays, employee information

When users request actions, respond in this JSON format:
{
  "response": "Let me check your leave balance for you.",
  "intent": "leave_apply|wfh_apply|expense_submit|meeting_book|info_query|unknown",
  "confidence": 0.0-1.0,
  "requires_action": true/false,
  "parameters": {
    "query_type": "leave_balance|holidays|leave_status|leave_types|staff_list|leave_diagnostic",
    "leave_type": "sick|annual|emergency",
    "start_date": "YYYY-MM-DD or tomorrow|today|next monday",
    "end_date": "YYYY-MM-DD or tomorrow|today|next monday",
    "reason": "text description of reason",
    "amount": "number",
    "description": "text",
    "is_half_day": false,
    "leave_type_id": "number (if user specifies ID)",
    "day_type_id": "1 for full day, 2 for half day",
    "staff_id": "number (if user specifies staff ID)",
    "staff_name": "text (if user mentions staff name)"
  }
}

EXAMPLE: If user says "I need sick leave tomorrow"
Response should be:
{
  "response": "I'll help you apply for sick leave tomorrow. Processing your request...",
  "intent": "leave_apply",
  "confidence": 0.95,
  "requires_action": true,
  "parameters": {
    "leave_type": "sick",
    "start_date": "tomorrow",
    "end_date": "tomorrow",
    "reason": "Sick leave",
    "amount": "1",
    "description": "Sick leave for tomorrow"
  }
}

IMPORTANT RULES:
- For "check leave balance", "show leave balance", "what's my balance": use intent="info_query", requires_action=true, query_type="leave_balance"
- For "show holidays", "upcoming holidays": use intent="info_query", requires_action=true, query_type="holidays"
- For "leave status", "my leave requests", "check my leave status": use intent="info_query", requires_action=true, query_type="leave_status"
- For "check leave settings", "why can't I apply leave", "leave diagnostic": use intent="info_query", requires_action=true, query_type="leave_diagnostic"
- For "show leave types", "what leave types are available", "available leave types": use intent="info_query", requires_action=true, query_type="leave_types"
- For "show staff list", "who can I select as staff in charge", "available staff": use intent="info_query", requires_action=true, query_type="staff_list"
- For leave applications: use intent="leave_apply", requires_action=true
- For "work from home", "WFH", "apply WFH", "WFH request": use intent="wfh_apply", requires_action=true
- For general questions without data retrieval: requires_action=false

LEAVE APPLICATION RULES:
- When user says "apply sick leave tomorrow", "need sick leave for tomorrow", "take sick leave tomorrow": ALWAYS use intent="leave_apply", requires_action=true, leave_type="sick", start_date="tomorrow", end_date="tomorrow"
- When user says "apply annual leave tomorrow", "need annual leave for tomorrow": ALWAYS use intent="leave_apply", requires_action=true, leave_type="annual", start_date="tomorrow", end_date="tomorrow"
- Extract dates from natural language: "tomorrow", "today", "next Monday", etc. and put them directly in start_date/end_date
- Extract leave types: "sick", "annual", "emergency" from user message
- If user provides reason like "visit friends", "doctor appointment": include in reason parameter
- For single day requests: set both start_date and end_date to the same date
- NEVER ask for additional details - ALWAYS process the request immediately if user provides leave type and date
- If user says "sick leave tomorrow" - you have ALL required information: leave_type="sick", start_date="tomorrow", end_date="tomorrow"

Always set requires_action=true when the user wants to retrieve or submit data.
Always be helpful, professional, and concise.

WFH APPLICATION RULES:
- When user says "work from home tomorrow", "WFH tomorrow", "apply WFH for tomorrow": ALWAYS use intent="wfh_apply", requires_action=true, start_date="tomorrow", end_date="tomorrow"
- When user says "WFH next week", "work from home Monday to Friday": extract proper date ranges
- Extract dates from natural language for WFH requests
- If user provides reason like "personal work", "avoid traffic": include in reason parameter
- For single day WFH: set both start_date and end_date to the same date
- NEVER ask for additional details if user provides clear WFH intent and date

CRITICAL: If user mentions ANY leave application request (apply for leave, need leave, take leave, etc.), IMMEDIATELY process with requires_action=true and intent="leave_apply". The system will handle the guided flow automatically.
CRITICAL: If user provides WFH request AND date (tomorrow/today/specific date), IMMEDIATELY process the request with requires_action=true and intent="wfh_apply". DO NOT ask for more information.
CRITICAL: DO NOT ask users for leave type, day type, or staff in charge details - the system will guide them through this automatically.''';
  }

  /// Parse AI response and extract intent and parameters
  AIChatResponse _parseAIResponse(
      Map<String, dynamic> responseData, String originalMessage) {
    try {
      String aiResponseText = responseData['choices'][0]['message']['content'];

      if (kDebugMode) {
        log('$_logPrefix AI Response: $aiResponseText');
      }

      // Try to parse JSON response from AI
      Map<String, dynamic>? parsedResponse;
      try {
        // Look for JSON in the response
        int jsonStart = aiResponseText.indexOf('{');
        int jsonEnd = aiResponseText.lastIndexOf('}') + 1;

        if (jsonStart != -1 && jsonEnd > jsonStart) {
          String jsonString = aiResponseText.substring(jsonStart, jsonEnd);
          parsedResponse = jsonDecode(jsonString);
        }
      } catch (e) {
        if (kDebugMode) {
          log('$_logPrefix Could not parse JSON from AI response: $e');
        }
      }

      if (parsedResponse != null) {
        if (kDebugMode) {
          log('$_logPrefix Parsed JSON response: $parsedResponse');
        }
        return AIChatResponse(
          originalMessage: originalMessage,
          aiResponse: parsedResponse['response'] ?? aiResponseText,
          intent: _parseIntent(parsedResponse['intent']),
          confidence: (parsedResponse['confidence'] ?? 0.0).toDouble(),
          requiresAction: parsedResponse['requires_action'] ?? false,
          parameters:
              Map<String, dynamic>.from(parsedResponse['parameters'] ?? {}),
        );
      } else {
        // Fallback: classify intent based on keywords
        if (kDebugMode) {
          log('$_logPrefix Using fallback classification for: $originalMessage');
        }
        ChatIntent fallbackIntent = _classifyIntentFromText(originalMessage);
        bool fallbackRequiresAction = _requiresActionFromText(originalMessage);

        if (kDebugMode) {
          log('$_logPrefix Fallback intent: $fallbackIntent, requires action: $fallbackRequiresAction');
        }

        return AIChatResponse(
          originalMessage: originalMessage,
          aiResponse: aiResponseText,
          intent: fallbackIntent,
          confidence: 0.5,
          requiresAction: fallbackRequiresAction,
          parameters: fallbackIntent == ChatIntent.infoQuery
              ? {'query_type': 'leave_balance'}
              : {},
        );
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error parsing AI response: $e');
      }
      return AIChatResponse(
        originalMessage: originalMessage,
        aiResponse:
            'I understand your request, but I need more information to help you.',
        intent: ChatIntent.unknown,
        confidence: 0.0,
        requiresAction: false,
      );
    }
  }

  /// Parse intent string to enum
  ChatIntent _parseIntent(String? intentString) {
    switch (intentString?.toLowerCase()) {
      case 'leave_apply':
        return ChatIntent.leaveApply;
      case 'wfh_apply':
        return ChatIntent.wfhApply;
      case 'expense_submit':
        return ChatIntent.expenseSubmit;
      case 'meeting_book':
        return ChatIntent.meetingBook;
      case 'info_query':
        return ChatIntent.infoQuery;
      default:
        return ChatIntent.unknown;
    }
  }

  /// Fallback intent classification based on keywords
  ChatIntent _classifyIntentFromText(String text) {
    String lowerText = text.toLowerCase();

    if (lowerText.contains('leave') &&
        (lowerText.contains('apply') || lowerText.contains('request'))) {
      return ChatIntent.leaveApply;
    } else if (lowerText.contains('wfh') ||
        lowerText.contains('work from home')) {
      return ChatIntent.wfhApply;
    } else if (lowerText.contains('expense') ||
        lowerText.contains('reimburs')) {
      return ChatIntent.expenseSubmit;
    } else if (lowerText.contains('meeting') && lowerText.contains('book')) {
      return ChatIntent.meetingBook;
    } else if (lowerText.contains('balance') ||
        lowerText.contains('status') ||
        lowerText.contains('holiday') ||
        lowerText.contains('check') ||
        lowerText.contains('show') ||
        lowerText.contains('what')) {
      return ChatIntent.infoQuery;
    }

    return ChatIntent.unknown;
  }

  /// Check if text requires action
  bool _requiresActionFromText(String text) {
    String lowerText = text.toLowerCase();
    List<String> actionKeywords = [
      'apply',
      'request',
      'submit',
      'book',
      'cancel',
      'create',
      'check',
      'show',
      'get',
      'what'
    ];
    return actionKeywords.any((keyword) => lowerText.contains(keyword));
  }

  /// Set AI API token (delegates to AIConfigService)
  Future<void> setAIToken(String token) async {
    await AIConfigService.setApiToken(token);
  }
}
