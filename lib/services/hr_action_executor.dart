import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/ai_chat_models.dart';
import '../util/urls.dart';
import '../util/date_formatter.dart';

/// HR Action Executor that handles API calls for chatbot actions
class HRActionExecutor {
  static const String _logPrefix = '[HR_ACTION_EXECUTOR]';
  final Dio _dio = Dio();

  /// Execute action based on AI chat response
  Future<ActionResult> executeAction(AIChatResponse aiResponse) async {
    try {
      if (kDebugMode) {
        log('$_logPrefix Executing action for intent: ${aiResponse.intent}');
        log('$_logPrefix Requires action: ${aiResponse.requiresAction}');
        log('$_logPrefix Parameters: ${aiResponse.parameters}');
      }

      if (!aiResponse.requiresAction) {
        if (kDebugMode) {
          log('$_logPrefix No action required, returning AI response');
        }
        return ActionResult(
          success: true,
          message: aiResponse.aiResponse,
          actionType: aiResponse.intent,
        );
      }

      switch (aiResponse.intent) {
        case ChatIntent.leaveApply:
          return await _executeLeaveApplication(aiResponse.parameters);
        case ChatIntent.wfhApply:
          return await _executeWFHApplication(aiResponse.parameters);
        case ChatIntent.expenseSubmit:
          return await _executeExpenseSubmission(aiResponse.parameters);
        case ChatIntent.meetingBook:
          return await _executeMeetingBooking(aiResponse.parameters);
        case ChatIntent.infoQuery:
          return await _executeInfoQuery(aiResponse.parameters);
        default:
          return ActionResult(
            success: false,
            message:
                'I understand your request, but I need more specific information to help you.',
            actionType: aiResponse.intent,
          );
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error executing action: $e');
      }
      return ActionResult(
        success: false,
        message:
            'Sorry, I encountered an error while processing your request. Please try again.',
        actionType: aiResponse.intent,
        error: e.toString(),
      );
    }
  }

  /// Diagnostic function to check leave application eligibility
  Future<ActionResult> _checkLeaveEligibility() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('access_token');
      int? userId = prefs.getInt('uid');

      if (kDebugMode) {
        log('$_logPrefix ========== LEAVE ELIGIBILITY DIAGNOSTIC ==========');
        log('$_logPrefix Access token available: ${token != null}');
        log('$_logPrefix User ID available: ${userId != null && userId != 0}');
        log('$_logPrefix User ID value: $userId');
        log('$_logPrefix ================================================');
      }

      return ActionResult(
        success: true,
        message: '🔍 **Leave Application Diagnostic**\n\n'
            '✅ Authentication: ${token != null ? "Valid" : "Missing"}\n'
            '✅ User ID: ${userId != null && userId != 0 ? "Valid ($userId)" : "Missing"}\n\n'
            '💡 **To enable leave applications:**\n'
            '• Contact your HR administrator\n'
            '• Request leave settings configuration\n'
            '• Verify your employee profile is complete',
        actionType: ChatIntent.infoQuery,
      );
    } catch (e) {
      return ActionResult(
        success: false,
        message: 'Error checking leave eligibility: ${e.toString()}',
        actionType: ChatIntent.infoQuery,
      );
    }
  }

  /// Fetch leave types from API
  Future<ActionResult> _getLeaveTypes() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('access_token');

      if (token == null) {
        return ActionResult(
          success: false,
          message: 'Authentication required. Please log in again.',
          actionType: ChatIntent.infoQuery,
        );
      }

      final response = await _dio.get(
        leaveTypesUrl.toString(),
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (kDebugMode) {
        log('$_logPrefix Leave types API response: ${response.data}');
      }

      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        if (responseData['status'] == true && responseData['data'] != null) {
          List<dynamic> leaveTypesData = responseData['data'];
          String leaveTypesText = '📋 **Available Leave Types:**\n\n';

          for (var leaveType in leaveTypesData) {
            leaveTypesText +=
                '• **${leaveType['name']}** (ID: ${leaveType['id']})\n';
          }

          return ActionResult(
            success: true,
            message: leaveTypesText,
            actionType: ChatIntent.infoQuery,
            data: responseData,
          );
        }
      }

      return ActionResult(
        success: false,
        message: 'Unable to fetch leave types.',
        actionType: ChatIntent.infoQuery,
      );
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error fetching leave types: $e');
      }
      return ActionResult(
        success: false,
        message: 'Error fetching leave types: ${e.toString()}',
        actionType: ChatIntent.infoQuery,
      );
    }
  }

  /// Fetch staff list from API
  Future<ActionResult> _getStaffList() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('access_token');

      if (token == null) {
        return ActionResult(
          success: false,
          message: 'Authentication required. Please log in again.',
          actionType: ChatIntent.infoQuery,
        );
      }

      final response = await _dio.get(
        userlistURL.toString(),
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (kDebugMode) {
        log('$_logPrefix Staff list API response: ${response.data}');
      }

      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        if (responseData['result'] == 'success' &&
            responseData['data'] != null) {
          List<dynamic> staffData = responseData['data'];
          String staffListText = '👥 **Available Staff Members:**\n\n';

          for (var staff in staffData.take(10)) {
            // Limit to first 10 for readability
            staffListText += '• **${staff['name']}** (ID: ${staff['id']})\n';
          }

          if (staffData.length > 10) {
            staffListText +=
                '\n... and ${staffData.length - 10} more staff members';
          }

          return ActionResult(
            success: true,
            message: staffListText,
            actionType: ChatIntent.infoQuery,
            data: responseData,
          );
        }
      }

      return ActionResult(
        success: false,
        message: 'Unable to fetch staff list.',
        actionType: ChatIntent.infoQuery,
      );
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error fetching staff list: $e');
      }
      return ActionResult(
        success: false,
        message: 'Error fetching staff list: ${e.toString()}',
        actionType: ChatIntent.infoQuery,
      );
    }
  }

  /// Start guided leave application flow
  Future<ActionResult> _startGuidedLeaveApplication(
      Map<String, dynamic> parameters) async {
    if (kDebugMode) {
      log('$_logPrefix Starting guided leave application flow');
      log('$_logPrefix Current parameters: $parameters');
    }

    // Extract basic information from the user's message
    String startDate = parameters['start_date'] ?? '';
    String endDate = parameters['end_date'] ?? '';
    String reason = parameters['reason'] ?? '';
    String leaveType = parameters['leave_type'] ?? '';

    // Try to extract dates from original message if AI provided placeholders
    String originalMessage =
        parameters['original_message']?.toString().toLowerCase() ?? '';
    if (startDate == 'YYYY-MM-DD' || startDate.isEmpty) {
      startDate = _extractDateFromMessage(originalMessage, 'start');
    }
    if (endDate == 'YYYY-MM-DD' || endDate.isEmpty) {
      endDate = _extractDateFromMessage(originalMessage, 'end');
    }
    if (reason == 'text' || reason.isEmpty) {
      reason = _extractReasonFromMessage(originalMessage, leaveType);
    }

    // Build the guided message with actual data from APIs
    String guidedMessage = '🎯 **Leave Application Setup**\n\n';

    if (startDate.isNotEmpty && endDate.isNotEmpty && reason.isNotEmpty) {
      guidedMessage += '✅ **Your Request Details:**\n';
      guidedMessage += '• **Dates**: $startDate to $endDate\n';
      guidedMessage += '• **Reason**: $reason\n\n';
    }

    // Fetch and display leave types
    try {
      ActionResult leaveTypesResult = await _getLeaveTypes();
      if (leaveTypesResult.success && leaveTypesResult.data != null) {
        guidedMessage += '${leaveTypesResult.message}\n\n';
      } else {
        guidedMessage +=
            '📋 **Leave Types**: Unable to fetch (please contact admin)\n\n';
      }
    } catch (e) {
      guidedMessage += '📋 **Leave Types**: Error fetching data\n\n';
    }

    // Add day types (static options)
    guidedMessage += '⏰ **Day Types:**\n\n';
    guidedMessage += '• **Full Day** (ID: 1)\n';
    guidedMessage += '• **Half Day** (ID: 2)\n\n';

    // Fetch and display staff list
    try {
      ActionResult staffResult = await _getStaffList();
      if (staffResult.success && staffResult.data != null) {
        guidedMessage += '${staffResult.message}\n\n';
      } else {
        guidedMessage +=
            '👥 **Staff List**: Unable to fetch (please contact admin)\n\n';
      }
    } catch (e) {
      guidedMessage += '👥 **Staff List**: Error fetching data\n\n';
    }

    guidedMessage += '💡 **Next Steps:**\n';
    guidedMessage +=
        '• Tell me your selections like: "Apply leave type 1, full day, staff 5"\n';
    guidedMessage +=
        '• Or say: "Sick leave, half day, John as staff in charge"\n';
    guidedMessage += '• Or ask: "How do I complete my application?"';

    return ActionResult(
      success: true,
      message: guidedMessage,
      actionType: ChatIntent.leaveApply,
      data: parameters, // Preserve the original parameters
    );
  }

  /// Execute leave application
  Future<ActionResult> _executeLeaveApplication(
      Map<String, dynamic> parameters) async {
    try {
      // Check if this is a guided leave application with all required fields
      bool hasLeaveTypeId = parameters.containsKey('leave_type_id') &&
          parameters['leave_type_id'] != null;
      bool hasDayTypeId = parameters.containsKey('day_type_id') &&
          parameters['day_type_id'] != null;
      bool hasStaffId =
          parameters.containsKey('staff_id') && parameters['staff_id'] != null;

      // If missing required IDs, start guided flow
      if (!hasLeaveTypeId || !hasDayTypeId || !hasStaffId) {
        return _startGuidedLeaveApplication(parameters);
      }

      // Validate basic required parameters
      if (!_hasRequiredLeaveParams(parameters)) {
        return ActionResult(
          success: false,
          message:
              'I need more information to apply for leave. Please provide the leave type, start date, end date, and reason.',
          actionType: ChatIntent.leaveApply,
        );
      }

      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('access_token');

      if (token == null) {
        return ActionResult(
          success: false,
          message: 'Authentication required. Please log in again.',
          actionType: ChatIntent.leaveApply,
        );
      }

      // Get current user ID for leave_emp field
      int? userId = prefs.getInt('uid');
      if (userId == null || userId == 0) {
        return ActionResult(
          success: false,
          message: 'User information not found. Please log in again.',
          actionType: ChatIntent.leaveApply,
        );
      }
      String userIdString = userId.toString();

      // Parse parameters and handle AI placeholders
      String leaveType = parameters['leave_type'] ?? 'sick';
      String startDate = parameters['start_date'] ?? '';
      String endDate = parameters['end_date'] ?? '';
      String reason = parameters['reason'] ?? '';
      bool isHalfDay = parameters['is_half_day'] ?? false;

      if (kDebugMode) {
        log('$_logPrefix ========== CHATBOT LEAVE APPLICATION DEBUG ==========');
        log('$_logPrefix Raw parameters received:');
        log('$_logPrefix leave_type: $leaveType');
        log('$_logPrefix start_date: $startDate');
        log('$_logPrefix end_date: $endDate');
        log('$_logPrefix reason: $reason');
        log('$_logPrefix is_half_day: $isHalfDay');
        log('$_logPrefix user_id (int): $userId');
        log('$_logPrefix user_id (string): $userIdString');
        log('$_logPrefix TYPE CASTING FIX: Successfully converted int to string');
        log('$_logPrefix ================================================');
      }

      // Try to extract dates from original message if AI provided placeholders
      String originalMessage =
          parameters['original_message']?.toString().toLowerCase() ?? '';
      if (startDate == 'YYYY-MM-DD' || startDate.isEmpty) {
        startDate = _extractDateFromMessage(originalMessage, 'start');
      }
      if (endDate == 'YYYY-MM-DD' || endDate.isEmpty) {
        endDate = _extractDateFromMessage(originalMessage, 'end');
      }
      if (reason == 'text' || reason.isEmpty) {
        reason = _extractReasonFromMessage(originalMessage, leaveType);
      }

      // Format dates
      String formattedStartDate = _formatDateForAPI(startDate);
      String formattedEndDate = _formatDateForAPI(endDate);

      // Calculate number of days
      DateTime startDateTime = DateTime.parse(formattedStartDate);
      DateTime endDateTime = DateTime.parse(formattedEndDate);
      int numberOfDays = endDateTime.difference(startDateTime).inDays + 1;

      // Check if dates are valid (not too far in the future)
      DateTime now = DateTime.now();
      DateTime maxFutureDate =
          now.add(const Duration(days: 365)); // 1 year ahead
      if (startDateTime.isAfter(maxFutureDate)) {
        if (kDebugMode) {
          log('$_logPrefix Warning: Leave date is more than 1 year in the future');
        }
      }

      // Get the specific IDs from parameters (for guided flow)
      String leaveTypeId = parameters['leave_type_id']?.toString() ??
          _mapLeaveType(leaveType).toString();
      String dayTypeId =
          parameters['day_type_id']?.toString() ?? (isHalfDay ? '2' : '1');
      String staffInChargeId = parameters['staff_id']?.toString() ?? '0';

      // Prepare multipart request (with all required fields)
      FormData formData = FormData.fromMap({
        'leave_type': leaveTypeId,
        'start_date': formattedStartDate,
        'end_date': formattedEndDate,
        'reason': reason,
        'number_of_days': numberOfDays.toString(),
        'day_type': dayTypeId, // 1 = full day, 2 = half day (required by API)
        'staff_incharge':
            staffInChargeId, // Staff in charge ID (required by API)
        'leave_emp': userIdString, // Current user ID (required by API)
      });

      if (kDebugMode) {
        log('$_logPrefix ========== LEAVE APPLICATION DATA ==========');
        log('$_logPrefix leave_type: $leaveTypeId (original: $leaveType)');
        log('$_logPrefix start_date: $formattedStartDate');
        log('$_logPrefix end_date: $formattedEndDate');
        log('$_logPrefix reason: $reason');
        log('$_logPrefix number_of_days: $numberOfDays');
        log('$_logPrefix day_type: $dayTypeId (half_day: $isHalfDay)');
        log('$_logPrefix staff_incharge: $staffInChargeId');
        log('$_logPrefix leave_emp: $userIdString');
        log('$_logPrefix ==========================================');
      }

      final response = await _dio.post(
        leaveApplyUrl.toString(),
        data: formData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
          },
        ),
      );

      if (kDebugMode) {
        log('$_logPrefix Leave API response status: ${response.statusCode}');
        log('$_logPrefix Leave API response data: ${response.data}');
      }

      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        if (responseData['result'] == 'success') {
          return ActionResult(
            success: true,
            message: 'Your leave application has been submitted successfully!',
            actionType: ChatIntent.leaveApply,
            data: responseData,
          );
        } else if (responseData['result'] == 'failed') {
          return ActionResult(
            success: false,
            message: responseData['message'] ??
                'Failed to submit leave application.',
            actionType: ChatIntent.leaveApply,
          );
        } else if (responseData['status'] == false) {
          return ActionResult(
            success: false,
            message: responseData['response_message'] ??
                'Failed to submit leave application.',
            actionType: ChatIntent.leaveApply,
          );
        } else {
          return ActionResult(
            success: false,
            message: 'Failed to submit leave application.',
            actionType: ChatIntent.leaveApply,
          );
        }
      } else if (response.statusCode == 400) {
        Map<String, dynamic> responseData = response.data;
        if (kDebugMode) {
          log('$_logPrefix 400 Error response: $responseData');
        }
        if (responseData['result'] == 'failed') {
          String errorMessage =
              responseData['message'] ?? 'Invalid request data.';

          // Handle specific business logic errors with user-friendly messages
          if (errorMessage.contains('cannot apply for leave') ||
              errorMessage.contains('contact admin') ||
              errorMessage.contains('leave settings')) {
            if (kDebugMode) {
              log('$_logPrefix BUSINESS LOGIC RESTRICTION: Leave application blocked by server');
              log('$_logPrefix Server message: $errorMessage');
              log('$_logPrefix This is not a technical error - user needs admin assistance');
            }

            return ActionResult(
              success: false,
              message: '❌ **Leave Application Restricted**\n\n'
                  '$errorMessage\n\n'
                  '💡 **What you can do:**\n'
                  '• Contact your HR administrator\n'
                  '• Check if your leave settings are configured\n'
                  '• Verify your leave balance and eligibility\n'
                  '• Ensure your employee profile is complete\n\n'
                  '🤖 **Try asking me instead:**\n'
                  '• "What\'s my leave balance?"\n'
                  '• "Show my leave history"\n'
                  '• "What are the upcoming holidays?"\n'
                  '• "I want to work from home tomorrow"\n\n'
                  '📞 **Need help?** Contact your HR team for leave settings configuration.',
              actionType: ChatIntent.leaveApply,
            );
          }

          return ActionResult(
            success: false,
            message: errorMessage,
            actionType: ChatIntent.leaveApply,
          );
        } else {
          return ActionResult(
            success: false,
            message: 'Invalid request. Please check your input.',
            actionType: ChatIntent.leaveApply,
          );
        }
      } else {
        return ActionResult(
          success: false,
          message: 'Failed to submit leave application. Please try again.',
          actionType: ChatIntent.leaveApply,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error in leave application: $e');
      }

      // Handle DioException to get server response details
      if (e is DioException) {
        if (kDebugMode) {
          log('$_logPrefix DioException type: ${e.type}');
          log('$_logPrefix DioException message: ${e.message}');
        }

        if (e.response != null) {
          if (kDebugMode) {
            log('$_logPrefix DioException response status: ${e.response!.statusCode}');
            log('$_logPrefix DioException response data: ${e.response!.data}');
            log('$_logPrefix DioException response headers: ${e.response!.headers}');
          }

          // Try to extract meaningful error message from server response
          try {
            Map<String, dynamic> responseData = e.response!.data;
            String errorMessage = responseData['message'] ??
                responseData['error'] ??
                responseData['response_message'] ??
                'Server validation error';

            // Handle specific business logic errors with user-friendly messages
            if (errorMessage.contains('cannot apply for leave') ||
                errorMessage.contains('contact admin') ||
                errorMessage.contains('leave settings')) {
              if (kDebugMode) {
                log('$_logPrefix BUSINESS LOGIC RESTRICTION: Leave application blocked by server');
                log('$_logPrefix Server message: $errorMessage');
                log('$_logPrefix This is not a technical error - user needs admin assistance');
              }

              return ActionResult(
                success: false,
                message: '❌ **Leave Application Restricted**\n\n'
                    '$errorMessage\n\n'
                    '💡 **What you can do:**\n'
                    '• Contact your HR administrator\n'
                    '• Check if your leave settings are configured\n'
                    '• Verify your leave balance and eligibility\n'
                    '• Ensure your employee profile is complete\n\n'
                    '🤖 **Try asking me instead:**\n'
                    '• "What\'s my leave balance?"\n'
                    '• "Show my leave history"\n'
                    '• "What are the upcoming holidays?"\n'
                    '• "I want to work from home tomorrow"\n\n'
                    '📞 **Need help?** Contact your HR team for leave settings configuration.',
                actionType: ChatIntent.leaveApply,
                error: e.toString(),
              );
            }

            return ActionResult(
              success: false,
              message: 'Leave application failed: $errorMessage',
              actionType: ChatIntent.leaveApply,
              error: e.toString(),
            );
          } catch (parseError) {
            if (kDebugMode) {
              log('$_logPrefix Error parsing server response: $parseError');
            }
          }
        } else {
          if (kDebugMode) {
            log('$_logPrefix DioException with no response - likely network error');
          }
        }
      }

      return ActionResult(
        success: false,
        message: 'Error submitting leave application: ${e.toString()}',
        actionType: ChatIntent.leaveApply,
        error: e.toString(),
      );
    }
  }

  /// Execute WFH application
  Future<ActionResult> _executeWFHApplication(
      Map<String, dynamic> parameters) async {
    try {
      if (!_hasRequiredWFHParams(parameters)) {
        return ActionResult(
          success: false,
          message:
              'I need more information for your WFH request. Please provide the start date, end date, and reason.',
          actionType: ChatIntent.wfhApply,
        );
      }

      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('access_token');

      if (token == null) {
        return ActionResult(
          success: false,
          message: 'Authentication required. Please log in again.',
          actionType: ChatIntent.wfhApply,
        );
      }

      String startDate = parameters['start_date'] ?? '';
      String endDate = parameters['end_date'] ?? '';
      String reason = parameters['reason'] ?? '';

      // Extract WFH type from parameters or default to Temporary
      String wfhType = parameters['wfh_type'] ?? 'temporary';
      int wfhTypeId = _mapWFHType(wfhType);

      if (kDebugMode) {
        log('$_logPrefix ========== WFH APPLICATION DEBUG ==========');
        log('$_logPrefix Raw parameters received:');
        log('$_logPrefix start_date: $startDate');
        log('$_logPrefix end_date: $endDate');
        log('$_logPrefix reason: $reason');
        log('$_logPrefix wfh_type: $wfhType (mapped to ID: $wfhTypeId)');
        log('$_logPrefix ==========================================');
      }

      // Prepare request data based on WFH type
      Map<String, dynamic> requestData = {
        'wfh_type': wfhTypeId,
        'remark': reason,
      };

      // Only add dates for Temporary WFH (ID: 4)
      if (wfhTypeId == 4) {
        String formattedStartDate = _formatDateForAPI(startDate);
        String formattedEndDate = _formatDateForAPI(endDate);
        requestData['from_date'] = formattedStartDate;
        requestData['to_date'] = formattedEndDate;

        if (kDebugMode) {
          log('$_logPrefix Adding dates for Temporary WFH:');
          log('$_logPrefix from_date: $formattedStartDate');
          log('$_logPrefix to_date: $formattedEndDate');
        }
      } else {
        if (kDebugMode) {
          log('$_logPrefix Regular WFH - no dates required');
        }
      }

      FormData formData = FormData.fromMap(requestData);

      final response = await _dio.post(
        wfhRequestURL.toString(), // Use wfhRequestURL instead of wfhUpdateUrl
        data: formData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
          },
        ),
      );

      if (kDebugMode) {
        log('$_logPrefix WFH API response status: ${response.statusCode}');
        log('$_logPrefix WFH API response data: ${response.data}');
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        Map<String, dynamic> responseData = response.data;
        if (responseData['result'] == 'success') {
          return ActionResult(
            success: true,
            message:
                'Your work from home request has been submitted successfully! ${responseData['message'] ?? ''}',
            actionType: ChatIntent.wfhApply,
            data: responseData,
          );
        } else {
          return ActionResult(
            success: false,
            message: responseData['message'] ?? 'Failed to submit WFH request.',
            actionType: ChatIntent.wfhApply,
          );
        }
      } else {
        return ActionResult(
          success: false,
          message: 'Failed to submit WFH request. Please try again.',
          actionType: ChatIntent.wfhApply,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error in WFH application: $e');
      }

      // Handle DioException to get server response details
      if (e is DioException) {
        if (kDebugMode) {
          log('$_logPrefix WFH DioException type: ${e.type}');
          log('$_logPrefix WFH DioException message: ${e.message}');
        }

        if (e.response != null) {
          if (kDebugMode) {
            log('$_logPrefix WFH DioException response status: ${e.response!.statusCode}');
            log('$_logPrefix WFH DioException response data: ${e.response!.data}');
          }

          // Try to extract meaningful error message from server response
          try {
            Map<String, dynamic> responseData = e.response!.data;
            String errorMessage = responseData['message'] ??
                responseData['error'] ??
                responseData['response_message'] ??
                'Server validation error';

            // Handle specific business logic errors with user-friendly messages
            if (errorMessage.contains('cannot apply') ||
                errorMessage.contains('contact admin') ||
                errorMessage.contains('wfh') ||
                errorMessage.contains('permission')) {
              return ActionResult(
                success: false,
                message: '❌ **WFH Request Restricted**\n\n'
                    '$errorMessage\n\n'
                    '💡 **What you can do:**\n'
                    '• Contact your HR administrator\n'
                    '• Check if WFH is enabled for your account\n'
                    '• Verify your WFH permissions\n\n'
                    '🤖 **Try asking me:**\n'
                    '• "What\'s my leave balance?"\n'
                    '• "Show my leave history"\n'
                    '• "What are the upcoming holidays?"',
                actionType: ChatIntent.wfhApply,
                error: e.toString(),
              );
            }

            return ActionResult(
              success: false,
              message: 'WFH request failed: $errorMessage',
              actionType: ChatIntent.wfhApply,
              error: e.toString(),
            );
          } catch (parseError) {
            if (kDebugMode) {
              log('$_logPrefix Error parsing WFH server response: $parseError');
            }
          }
        }
      }

      return ActionResult(
        success: false,
        message: 'Error submitting WFH request: ${e.toString()}',
        actionType: ChatIntent.wfhApply,
        error: e.toString(),
      );
    }
  }

  /// Execute expense submission
  Future<ActionResult> _executeExpenseSubmission(
      Map<String, dynamic> parameters) async {
    return ActionResult(
      success: false,
      message:
          'Expense submission through chat is not yet implemented. Please use the expense form in the app.',
      actionType: ChatIntent.expenseSubmit,
    );
  }

  /// Execute meeting booking
  Future<ActionResult> _executeMeetingBooking(
      Map<String, dynamic> parameters) async {
    return ActionResult(
      success: false,
      message:
          'Meeting booking through chat is not yet implemented. Please use the meeting room booking feature in the app.',
      actionType: ChatIntent.meetingBook,
    );
  }

  /// Execute information query
  Future<ActionResult> _executeInfoQuery(
      Map<String, dynamic> parameters) async {
    try {
      if (kDebugMode) {
        log('$_logPrefix Executing info query with parameters: $parameters');
      }

      String queryType = parameters['query_type'] ?? '';

      // If no query_type is provided, try to infer from the original message
      if (queryType.isEmpty) {
        // Check if this is a leave balance query based on common patterns
        queryType = _inferQueryType(parameters);
        if (kDebugMode) {
          log('$_logPrefix Inferred query type: $queryType');
        }
      } else {
        if (kDebugMode) {
          log('$_logPrefix Query type from parameters: $queryType');
        }
      }

      switch (queryType.toLowerCase()) {
        case 'leave_balance':
          return await _getLeaveBalance();
        case 'holidays':
          return await _getUpcomingHolidays();
        case 'leave_status':
          return await _getLeaveStatus();
        case 'leave_diagnostic':
        case 'check_leave_eligibility':
        case 'leave_settings':
          return await _checkLeaveEligibility();
        case 'leave_types':
        case 'show_leave_types':
        case 'available_leave_types':
          return await _getLeaveTypes();
        case 'staff_list':
        case 'show_staff':
        case 'available_staff':
          return await _getStaffList();
        default:
          // Default to leave balance for common queries
          if (queryType.isEmpty) {
            return await _getLeaveBalance();
          }
          return ActionResult(
            success: true,
            message:
                'I can help you with leave balance, upcoming holidays, or leave status. What would you like to know?',
            actionType: ChatIntent.infoQuery,
          );
      }
    } catch (e) {
      return ActionResult(
        success: false,
        message: 'Error retrieving information: ${e.toString()}',
        actionType: ChatIntent.infoQuery,
        error: e.toString(),
      );
    }
  }

  /// Infer query type from parameters or context
  String _inferQueryType(Map<String, dynamic> parameters) {
    // Check if there's any indication of what type of query this is
    String originalMessage =
        parameters['original_message']?.toString().toLowerCase() ?? '';

    if (originalMessage.contains('balance')) {
      return 'leave_balance';
    } else if (originalMessage.contains('holiday')) {
      return 'holidays';
    } else if (originalMessage.contains('status')) {
      return 'leave_status';
    }

    // Default to leave balance for check/show queries
    return 'leave_balance';
  }

  /// Get leave balance
  Future<ActionResult> _getLeaveBalance() async {
    try {
      if (kDebugMode) {
        log('$_logPrefix Getting leave balance...');
      }

      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('access_token');

      if (token == null) {
        if (kDebugMode) {
          log('$_logPrefix No access token found');
        }
        return ActionResult(
          success: false,
          message: 'Authentication required. Please log in again.',
          actionType: ChatIntent.infoQuery,
        );
      }

      if (kDebugMode) {
        log('$_logPrefix Making API call to: ${leaveBalanceUrl.toString()}');
      }

      final response = await _dio.get(
        leaveBalanceUrl.toString(),
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (kDebugMode) {
        log('$_logPrefix API Response Status: ${response.statusCode}');
        log('$_logPrefix API Response Data: ${response.data}');
      }

      if (response.statusCode == 200) {
        // Parse leave balance data and format response
        Map<String, dynamic> responseData = response.data;
        String balanceInfo = '🏖️ **Your Leave Balance:**\n\n';

        if (responseData['result'] == 'success' &&
            responseData['data'] != null) {
          List records = responseData['data'];
          if (kDebugMode) {
            log('$_logPrefix Found ${records.length} leave records');
          }

          // Add total summary first
          if (responseData['total_balance_leave'] != null &&
              responseData['total_taken_leaves'] != null) {
            double totalBalance =
                responseData['total_balance_leave'].toDouble();
            double totalTaken = responseData['total_taken_leaves'].toDouble();
            balanceInfo += '📊 **Summary:**\n';
            balanceInfo +=
                '• Total Allocated: ${totalBalance.toStringAsFixed(1)} days\n';
            balanceInfo +=
                '• Total Used: ${totalTaken.toStringAsFixed(1)} days\n';
            balanceInfo +=
                '• Total Available: ${(totalBalance - totalTaken).toStringAsFixed(1)} days\n\n';
          }

          balanceInfo += '📋 **Breakdown by Leave Type:**\n';
          for (var record in records) {
            String leaveTypeName = record['leave_type']['name'] ?? 'Unknown';
            double totalLeave = (record['total_leave'] ?? 0).toDouble();
            double leavesTaken = (record['leaves_taken'] ?? 0).toDouble();
            double availableLeave = (record['available_leave'] ?? 0).toDouble();

            // Only show leave types with allocation or usage
            if (totalLeave > 0 || leavesTaken > 0) {
              balanceInfo +=
                  '• **$leaveTypeName**: ${availableLeave.toStringAsFixed(1)} available (${totalLeave.toStringAsFixed(1)} total, ${leavesTaken.toStringAsFixed(1)} used)\n';
            }
          }
        } else {
          if (kDebugMode) {
            log('$_logPrefix No records found in response or API failed');
          }
          balanceInfo += 'No leave balance records found.';
        }

        return ActionResult(
          success: true,
          message: balanceInfo,
          actionType: ChatIntent.infoQuery,
          data: responseData,
        );
      } else {
        if (kDebugMode) {
          log('$_logPrefix API call failed with status: ${response.statusCode}');
        }
        return ActionResult(
          success: false,
          message:
              'Unable to retrieve leave balance. Status: ${response.statusCode}',
          actionType: ChatIntent.infoQuery,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error in _getLeaveBalance: $e');
      }
      return ActionResult(
        success: false,
        message: 'Error retrieving leave balance: ${e.toString()}',
        actionType: ChatIntent.infoQuery,
        error: e.toString(),
      );
    }
  }

  /// Get upcoming holidays
  Future<ActionResult> _getUpcomingHolidays() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('access_token');

      final response = await _dio.get(
        upcomingHolidaysURL.toString(),
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;

        if (kDebugMode) {
          log('$_logPrefix Holidays API response: $responseData');
        }

        // Check if response has data
        if (responseData['data'] != null && responseData['data'] is List) {
          List holidays = responseData['data'];

          if (holidays.isEmpty) {
            return ActionResult(
              success: true,
              message:
                  '🎉 **No upcoming holidays found.**\n\nEnjoy your regular work schedule!',
              actionType: ChatIntent.infoQuery,
              data: responseData,
            );
          }

          String holidayInfo = '🎉 **Upcoming Holidays:**\n\n';

          for (var holiday in holidays) {
            String holidayName =
                holiday['occassion'] ?? holiday['holiday_name'] ?? 'Holiday';
            String startDate =
                holiday['start_date'] ?? holiday['holiday_date'] ?? '';
            int days = holiday['days'] ?? 1;

            // Format the date
            String formattedDate = '';
            if (startDate.isNotEmpty) {
              try {
                DateTime date = DateTime.parse(startDate);
                formattedDate = formatDateFromDate(
                    dateTime: date, format: 'dd MMM yyyy, EEEE');
              } catch (e) {
                formattedDate = startDate;
              }
            }

            String dayText = days > 1 ? '$days days' : '1 day';
            holidayInfo += '🗓️ **$holidayName**\n';
            holidayInfo += '📅 Date: $formattedDate\n';
            holidayInfo += '⏰ Duration: $dayText\n\n';
          }

          return ActionResult(
            success: true,
            message: holidayInfo.trim(),
            actionType: ChatIntent.infoQuery,
            data: responseData,
          );
        } else {
          return ActionResult(
            success: true,
            message:
                '🎉 **No upcoming holidays found.**\n\nEnjoy your regular work schedule!',
            actionType: ChatIntent.infoQuery,
            data: responseData,
          );
        }
      } else {
        return ActionResult(
          success: false,
          message: 'Unable to retrieve holiday information at the moment.',
          actionType: ChatIntent.infoQuery,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error retrieving holidays: $e');
      }

      // Handle DioException to get server response details
      if (e is DioException) {
        if (e.response != null) {
          if (kDebugMode) {
            log('$_logPrefix Holidays DioException response status: ${e.response!.statusCode}');
            log('$_logPrefix Holidays DioException response data: ${e.response!.data}');
          }

          // Try to extract meaningful error message from server response
          try {
            Map<String, dynamic> responseData = e.response!.data;
            String errorMessage = responseData['message'] ??
                responseData['error'] ??
                responseData['response_message'] ??
                'Unable to retrieve holiday information';

            return ActionResult(
              success: false,
              message: 'Holiday information unavailable: $errorMessage',
              actionType: ChatIntent.infoQuery,
              error: e.toString(),
            );
          } catch (parseError) {
            if (kDebugMode) {
              log('$_logPrefix Error parsing holidays server response: $parseError');
            }
          }
        }
      }

      return ActionResult(
        success: false,
        message: 'Error retrieving holidays: ${e.toString()}',
        actionType: ChatIntent.infoQuery,
        error: e.toString(),
      );
    }
  }

  /// Get leave status
  Future<ActionResult> _getLeaveStatus() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('access_token');

      if (token == null) {
        return ActionResult(
          success: false,
          message: 'Authentication required. Please log in again.',
          actionType: ChatIntent.infoQuery,
        );
      }

      final response = await _dio.get(
        leaveHistoryUrl.toString(),
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (kDebugMode) {
        log('$_logPrefix Leave status API response: ${response.data}');
      }

      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;

        // Check if response has data
        if (responseData['data'] != null && responseData['data'] is List) {
          List leaveRequests = responseData['data'];

          if (leaveRequests.isEmpty) {
            return ActionResult(
              success: true,
              message:
                  '📋 **No leave requests found.**\n\nYou haven\'t submitted any leave requests yet.',
              actionType: ChatIntent.infoQuery,
              data: responseData,
            );
          }

          String statusInfo = '📋 **Your Leave Requests:**\n\n';

          // Show recent leave requests (limit to 5)
          int count = 0;
          for (var request in leaveRequests) {
            if (count >= 5) break;

            String leaveType = request['leave_type']?.toString() ?? 'Leave';
            String startDate =
                request['start_date'] ?? request['from_date'] ?? '';
            String endDate = request['end_date'] ?? request['to_date'] ?? '';
            String status = request['status']?.toString() ?? 'Unknown';
            String reason = request['reason'] ?? '';
            int days =
                request['number_of_days'] ?? request['no_of_leave_days'] ?? 1;

            // Format dates
            String dateRange = '';
            if (startDate.isNotEmpty) {
              try {
                DateTime start = DateTime.parse(startDate);
                String formattedStart =
                    formatDateFromDate(dateTime: start, format: 'dd MMM yyyy');

                if (endDate.isNotEmpty && endDate != startDate) {
                  DateTime end = DateTime.parse(endDate);
                  String formattedEnd =
                      formatDateFromDate(dateTime: end, format: 'dd MMM yyyy');
                  dateRange = '$formattedStart - $formattedEnd';
                } else {
                  dateRange = formattedStart;
                }
              } catch (e) {
                dateRange = startDate;
              }
            }

            // Status emoji
            String statusEmoji = '';
            switch (status.toLowerCase()) {
              case 'approved':
                statusEmoji = '✅';
                break;
              case 'pending':
                statusEmoji = '⏳';
                break;
              case 'rejected':
                statusEmoji = '❌';
                break;
              default:
                statusEmoji = '📄';
            }

            statusInfo +=
                '$statusEmoji **$leaveType** ($days day${days > 1 ? 's' : ''})\n';
            statusInfo += '📅 Date: $dateRange\n';
            statusInfo += '📝 Status: $status\n';
            if (reason.isNotEmpty) {
              statusInfo += '💬 Reason: $reason\n';
            }
            statusInfo += '\n';
            count++;
          }

          if (leaveRequests.length > 5) {
            statusInfo +=
                '📌 *Showing 5 most recent requests. Total: ${leaveRequests.length} requests.*';
          }

          return ActionResult(
            success: true,
            message: statusInfo.trim(),
            actionType: ChatIntent.infoQuery,
            data: responseData,
          );
        } else {
          return ActionResult(
            success: true,
            message:
                '📋 **No leave requests found.**\n\nYou haven\'t submitted any leave requests yet.',
            actionType: ChatIntent.infoQuery,
            data: responseData,
          );
        }
      } else {
        return ActionResult(
          success: false,
          message: 'Unable to retrieve leave status at the moment.',
          actionType: ChatIntent.infoQuery,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error retrieving leave status: $e');
      }

      // Handle DioException to get server response details
      if (e is DioException) {
        if (e.response != null) {
          if (kDebugMode) {
            log('$_logPrefix Leave status DioException response status: ${e.response!.statusCode}');
            log('$_logPrefix Leave status DioException response data: ${e.response!.data}');
          }

          try {
            Map<String, dynamic> responseData = e.response!.data;
            String errorMessage = responseData['message'] ??
                responseData['error'] ??
                responseData['response_message'] ??
                'Unable to retrieve leave status';

            return ActionResult(
              success: false,
              message: 'Leave status unavailable: $errorMessage',
              actionType: ChatIntent.infoQuery,
              error: e.toString(),
            );
          } catch (parseError) {
            if (kDebugMode) {
              log('$_logPrefix Error parsing leave status server response: $parseError');
            }
          }
        }
      }

      return ActionResult(
        success: false,
        message: 'Error retrieving leave status: ${e.toString()}',
        actionType: ChatIntent.infoQuery,
        error: e.toString(),
      );
    }
  }

  /// Validation helpers
  bool _hasRequiredLeaveParams(Map<String, dynamic> params) {
    return params.containsKey('leave_type') &&
        params.containsKey('start_date') &&
        params.containsKey('end_date') &&
        params.containsKey('reason') &&
        _isValidDate(params['start_date']) &&
        _isValidDate(params['end_date']) &&
        params['reason'] != 'text' && // Check for placeholder values
        params['start_date'] != 'YYYY-MM-DD' &&
        params['end_date'] != 'YYYY-MM-DD';
  }

  /// Check if a date string is valid
  bool _isValidDate(dynamic dateValue) {
    if (dateValue == null || dateValue == 'YYYY-MM-DD' || dateValue == 'text') {
      return false;
    }

    String dateString = dateValue.toString().toLowerCase().trim();

    // Check for relative dates that we can handle
    if (dateString == 'today' ||
        dateString == 'tomorrow' ||
        dateString == 'yesterday' ||
        dateString.startsWith('next ')) {
      return true;
    }

    // Try to parse as regular date
    try {
      DateTime.parse(dateValue.toString());
      return true;
    } catch (e) {
      return false;
    }
  }

  bool _hasRequiredWFHParams(Map<String, dynamic> params) {
    return params.containsKey('start_date') &&
        params.containsKey('end_date') &&
        params.containsKey('reason');
  }

  /// Helper methods
  String _formatDateForAPI(String dateString) {
    try {
      // Handle relative dates
      DateTime date;
      String lowerDate = dateString.toLowerCase().trim();

      if (lowerDate == 'today') {
        date = DateTime.now();
      } else if (lowerDate == 'tomorrow') {
        date = DateTime.now().add(const Duration(days: 1));
      } else if (lowerDate == 'yesterday') {
        date = DateTime.now().subtract(const Duration(days: 1));
      } else if (lowerDate.startsWith('next ')) {
        // Handle "next monday", "next week", etc.
        date = DateTime.now().add(const Duration(days: 7));
      } else {
        // Try to parse as regular date
        date = DateTime.parse(dateString);
      }

      return formatDateFromDate(dateTime: date, format: 'yyyy-MM-dd');
    } catch (e) {
      if (kDebugMode) {
        log('$_logPrefix Error formatting date "$dateString": $e');
      }
      return dateString;
    }
  }

  int _mapLeaveType(String leaveType) {
    switch (leaveType.toLowerCase()) {
      case 'sick':
        return 1;
      case 'annual':
        return 2;
      case 'emergency':
        return 3;
      default:
        return 1; // Default to sick leave
    }
  }

  /// Map WFH type string to API ID
  int _mapWFHType(String wfhType) {
    switch (wfhType.toLowerCase()) {
      case 'temporary':
      case 'temp':
        return 4; // Temporary WFH
      case 'regular':
      case 'permanent':
        return 5; // Regular WFH
      default:
        return 4; // Default to temporary WFH
    }
  }

  /// Extract date from natural language message
  String _extractDateFromMessage(String message, String type) {
    DateTime now = DateTime.now();

    if (message.contains('tomorrow')) {
      DateTime tomorrow = now.add(const Duration(days: 1));
      return formatDateFromDate(dateTime: tomorrow, format: 'yyyy-MM-dd');
    } else if (message.contains('today')) {
      return formatDateFromDate(dateTime: now, format: 'yyyy-MM-dd');
    } else if (message.contains('next week')) {
      DateTime nextWeek = now.add(const Duration(days: 7));
      return formatDateFromDate(dateTime: nextWeek, format: 'yyyy-MM-dd');
    } else if (message.contains('monday')) {
      DateTime nextMonday = _getNextWeekday(now, DateTime.monday);
      return formatDateFromDate(dateTime: nextMonday, format: 'yyyy-MM-dd');
    } else if (message.contains('tuesday')) {
      DateTime nextTuesday = _getNextWeekday(now, DateTime.tuesday);
      return formatDateFromDate(dateTime: nextTuesday, format: 'yyyy-MM-dd');
    } else if (message.contains('wednesday')) {
      DateTime nextWednesday = _getNextWeekday(now, DateTime.wednesday);
      return formatDateFromDate(dateTime: nextWednesday, format: 'yyyy-MM-dd');
    } else if (message.contains('thursday')) {
      DateTime nextThursday = _getNextWeekday(now, DateTime.thursday);
      return formatDateFromDate(dateTime: nextThursday, format: 'yyyy-MM-dd');
    } else if (message.contains('friday')) {
      DateTime nextFriday = _getNextWeekday(now, DateTime.friday);
      return formatDateFromDate(dateTime: nextFriday, format: 'yyyy-MM-dd');
    }

    // Default to tomorrow if no specific date found
    DateTime tomorrow = now.add(const Duration(days: 1));
    return formatDateFromDate(dateTime: tomorrow, format: 'yyyy-MM-dd');
  }

  /// Get next occurrence of a specific weekday
  DateTime _getNextWeekday(DateTime from, int weekday) {
    int daysUntilWeekday = (weekday - from.weekday) % 7;
    if (daysUntilWeekday == 0) {
      daysUntilWeekday = 7; // Next week if today is the same weekday
    }
    return from.add(Duration(days: daysUntilWeekday));
  }

  /// Extract reason from message or provide default
  String _extractReasonFromMessage(String message, String leaveType) {
    if (message.contains('sick') ||
        message.contains('ill') ||
        message.contains('medical')) {
      return 'Medical reasons';
    } else if (message.contains('emergency')) {
      return 'Emergency leave';
    } else if (message.contains('personal')) {
      return 'Personal reasons';
    } else if (message.contains('vacation') || message.contains('holiday')) {
      return 'Vacation';
    }

    // Default reasons based on leave type
    switch (leaveType.toLowerCase()) {
      case 'sick':
        return 'Medical reasons';
      case 'annual':
        return 'Annual leave';
      case 'emergency':
        return 'Emergency leave';
      default:
        return 'Personal reasons';
    }
  }
}

/// Result model for action execution
class ActionResult {
  final bool success;
  final String message;
  final ChatIntent actionType;
  final Map<String, dynamic>? data;
  final String? error;

  ActionResult({
    required this.success,
    required this.message,
    required this.actionType,
    this.data,
    this.error,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'actionType': actionType.toString(),
      'data': data,
      'error': error,
    };
  }
}
