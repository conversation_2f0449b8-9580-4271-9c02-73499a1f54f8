import 'dart:developer';
import 'package:flutter/foundation.dart';

/// Service for managing AI chatbot configuration (hardcoded)
class AIConfigService {
  static const String _logPrefix = '[AI_CONFIG_SERVICE]';

  // Hardcoded configuration - no more setup required
  static const String _hardcodedToken = 'qwen-ebdf2c476937def272d0be378ad9dd6a';
  static const String _hardcodedBaseUrl = 'https://ai.nuox.io/v1';
  static const String _hardcodedModel = 'qwen-3b-coder';

  // Keep these for backward compatibility
  static const String defaultBaseUrl = _hardcodedBaseUrl;
  static const String defaultModel = _hardcodedModel;

  /// Initialize AI configuration (now a no-op since config is hardcoded)
  static Future<void> initializeConfig({
    String? apiToken,
    String? baseUrl,
    String? model,
  }) async {
    if (kDebugMode) {
      log('$_logPrefix AI configuration is hardcoded - no initialization needed');
    }
  }

  /// Get AI API token (returns hardcoded value)
  static Future<String?> getApiToken() async {
    return _hardcodedToken;
  }

  /// Set AI API token (no-op since config is hardcoded)
  static Future<void> setApiToken(String token) async {
    if (kDebugMode) {
      log('$_logPrefix API token is hardcoded - cannot be changed');
    }
  }

  /// Get AI base URL (returns hardcoded value)
  static Future<String> getBaseUrl() async {
    return _hardcodedBaseUrl;
  }

  /// Set AI base URL (no-op since config is hardcoded)
  static Future<void> setBaseUrl(String baseUrl) async {
    if (kDebugMode) {
      log('$_logPrefix Base URL is hardcoded - cannot be changed');
    }
  }

  /// Get AI model name (returns hardcoded value)
  static Future<String> getModel() async {
    return _hardcodedModel;
  }

  /// Set AI model name (no-op since config is hardcoded)
  static Future<void> setModel(String model) async {
    if (kDebugMode) {
      log('$_logPrefix Model is hardcoded - cannot be changed');
    }
  }

  /// Check if AI configuration is complete (always true since hardcoded)
  static Future<bool> isConfigured() async {
    return true; // Always configured since values are hardcoded
  }

  /// Get complete AI configuration (returns hardcoded values)
  static Future<Map<String, String?>> getConfiguration() async {
    return {
      'token': _hardcodedToken,
      'baseUrl': _hardcodedBaseUrl,
      'model': _hardcodedModel,
    };
  }

  /// Clear all AI configuration (no-op since config is hardcoded)
  static Future<void> clearConfiguration() async {
    if (kDebugMode) {
      log('$_logPrefix AI configuration is hardcoded - cannot be cleared');
    }
  }

  /// Test AI connection with current configuration (always true since hardcoded)
  static Future<bool> testConnection() async {
    return true; // Always return true since configuration is hardcoded
  }
}
