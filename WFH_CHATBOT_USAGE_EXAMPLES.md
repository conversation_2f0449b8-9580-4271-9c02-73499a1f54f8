# Work From Home (WFH) Chatbot Usage Examples

## How to Use WFH Functionality in AI Chatbot

### 1. Quick Actions
Users can access WFH functionality through the quick actions that appear when opening the chat:
- **"Request work from home for next week"** - Pre-defined quick action

### 2. Natural Language Requests

#### Simple WFH Requests
```
User: "I want to work from home tomorrow"
AI: "I'll help you submit a work from home request for tomorrow."
→ Submits temporary WFH request for tomorrow
```

```
User: "WFH request for next Monday"
AI: "Processing your work from home request for next Monday."
→ Submits temporary WFH request for the specified date
```

#### Multi-day WFH Requests
```
User: "I need to work from home next week from Monday to Friday"
AI: "I'll submit your work from home request for next week."
→ Submits temporary WFH request for the date range
```

#### WFH with Reason
```
User: "Apply for work from home due to personal reasons"
AI: "I'll process your work from home request with the reason provided."
→ Submits WFH request with "personal reasons" as the reason
```

#### Different WFH Types
```
User: "I need regular work from home arrangement"
AI: "Processing your regular work from home request."
→ Submits regular WFH request (no specific dates required)
```

### 3. Supported Date Formats

#### Relative Dates
- **"tomorrow"** → Next day
- **"today"** → Current day
- **"next Monday"** → Following Monday
- **"next week"** → Following week

#### Specific Dates
- **"2024-01-15"** → ISO format
- **"January 15, 2024"** → Natural format
- **"15/01/2024"** → DD/MM/YYYY format

### 4. WFH Types Supported

#### Temporary WFH (Default)
- Requires start and end dates
- Used for specific date ranges
- Mapped to API ID: 4

#### Regular WFH
- No specific dates required
- For permanent arrangements
- Mapped to API ID: 5

### 5. Required Information

For successful WFH request submission, the system needs:
1. **Start Date** - When the WFH period begins
2. **End Date** - When the WFH period ends (can be same as start date)
3. **Reason** - Why you need to work from home

### 6. Error Handling

#### Missing Information
```
User: "I want to work from home"
AI: "I need more information for your WFH request. Please provide the start date, end date, and reason."
```

#### Authentication Issues
```
AI: "Authentication required. Please log in again."
```

#### Server Validation Errors
```
AI: "WFH request failed: [Specific error message from server]"
```

### 7. Success Responses

#### Successful Submission
```
AI: "Your work from home request has been submitted successfully! [Additional message from server]"
```

#### With Confirmation Details
```
AI: "✅ **WFH Request Submitted Successfully!**

📅 **Details:**
• Start Date: January 15, 2024
• End Date: January 15, 2024
• Type: Temporary WFH
• Reason: Personal work

Your request is now pending approval and will appear in your WFH dashboard."
```

### 8. Information Queries

#### WFH Status Check
```
User: "What's my WFH status?"
User: "Check my work from home requests"
User: "Show my WFH history"
```

#### WFH Policy Information
```
User: "What is the WFH policy?"
User: "How many WFH days do I have?"
User: "WFH guidelines"
```

### 9. Integration with Existing System

The chatbot WFH functionality:
- ✅ Uses same API endpoints as the manual WFH application screen
- ✅ Follows same authentication and authorization patterns
- ✅ Maintains same data validation rules
- ✅ Integrates with existing WFH approval workflow
- ✅ Appears in HR dashboard for approval/rejection

### 10. Technical Implementation

#### API Endpoint
- **URL**: `POST ${baseUrl}wfh_request/`
- **Authentication**: Bearer token via `getHeaders()`
- **Data Format**: FormData with fields:
  - `wfh_type`: Integer (4=Temporary, 5=Regular)
  - `remark`: String (reason for WFH)
  - `from_date`: String (YYYY-MM-DD format, for temporary WFH)
  - `to_date`: String (YYYY-MM-DD format, for temporary WFH)

#### Response Handling
- **Success**: `response.data.result === 'success'`
- **Error**: Detailed error messages from server validation
- **Status Codes**: 200/201 for success, others for errors

### 11. Testing

The implementation includes comprehensive tests:
- ✅ Unit tests for WFH request processing
- ✅ Integration tests for chat provider
- ✅ Authentication error handling
- ✅ Parameter validation
- ✅ Various message format handling

### 12. Troubleshooting

#### Common Issues and Solutions

**Issue**: "Authentication required"
**Solution**: User needs to log in again

**Issue**: "I need more information"
**Solution**: Provide start date, end date, and reason in the request

**Issue**: Network/API errors
**Solution**: Check internet connection and try again

**Issue**: "WFH request restricted"
**Solution**: Contact HR administrator to enable WFH permissions

The WFH chatbot functionality is now fully integrated and should work seamlessly with the existing HR Portal system.
