package ae.element8.hrportal

import android.os.Bundle
import android.util.Log
import io.flutter.embedding.android.FlutterActivity

class MainActivity: FlutterActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Suppress Google Play Services logs in debug mode
        if (BuildConfig.DEBUG) {
            suppressGooglePlayServicesLogs()
        }
    }

    private fun suppressGooglePlayServicesLogs() {
        // This helps reduce Google Play Services noise in development logs
        try {
            // Set system properties to reduce Google Services logging
            System.setProperty("firebase.crashlytics.debug", "false")
            System.setProperty("google.services.debug", "false")
            System.setProperty("firebase.analytics.debug", "false")

            // Suppress specific Google Play Services logs
            val logTags = arrayOf(
                "GoogleApiManager",
                "FlagRegistrar",
                "Firestore",
                "FirebaseApp",
                "FirebaseAuth",
                "FirebaseMessaging"
            )

            // Note: This is for development only
            // In production, you may want to keep some of these logs
            Log.i("MainActivity", "Google Play Services log suppression enabled for development")

        } catch (e: Exception) {
            // Ignore any errors in log suppression
            Log.w("MainActivity", "Could not suppress Google Play Services logs: ${e.message}")
        }
    }
}
